[{"platform": "reddit", "post_id": "reddit_1kf08o4", "title": "Why not go all in on agnc?", "content": "I'm still supposed to be in growth but keep making bad calls and am looking for something safer. I can't afford a house in my area where anything I want is over $500,000 but rent is only $700 (Reno Tahoe area).\n\nIm looking for something to do with about 100k in savings that's outside of 401k. Leaving 30k in Tesla and another 15k diversified stocks and 10k in crypto, mostly Bitcoin. \n\nWhy not put all 100k in AGNC or another high yield monthly stock? Is it just a bad call?\n\n", "author": "Notaninsidertraitor", "created_time": "2025-05-05T01:47:24", "url": "https://reddit.com/r/dividends/comments/1kf08o4/why_not_go_all_in_on_agnc/", "upvotes": 5, "comments_count": 69, "sentiment": "bullish", "engagement_score": 143.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kf21bk", "title": "Don't fool yourself, we're going into a recession", "content": "<PERSON>'s goal is to reduce or completely remove the tax on the rich. He believes he can go back to 1800s by bringing higher tariff rates and make the consumer pay the tax. But unlike the 1800s, he's also shrinking the government spending, so there won't be infrastructure investments. Though he will make a deal at the end, there's no way he's going back to where he started in the tariff war. The result will be a recession sooner or later as people cannot even afford anything right now plus there'll be many losing their jobs. \nTell me why this won't happen. I'd very much like to be wrong. ", "author": "TennisNut2008", "created_time": "2025-05-05T03:26:07", "url": "https://reddit.com/r/ValueInvesting/comments/1kf21bk/dont_fool_yourself_were_going_into_a_recession/", "upvotes": 3176, "comments_count": 698, "sentiment": "neutral", "engagement_score": 4572.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kf2gsv", "title": "Any of you that frequently use FSD, ever get feel resentful when you have to drive a vehicle manually that doesn't have FSD, especially on a road trip?", "content": "I normally drive 2 hours to get home from work. But I use FSD to get me home. Sometimes on the hour drive to work 🙃 I may speed, that'd the exception to using FSD.        \nBut today I had to do a road trip, 2 hour one way, but I have to drive a Uhaul Box Truck. The feeling I had during the drive feels so abnormal to me now, since I use FSD most of the time. It was annoying and I felt somewhat resentful.        \n       \nAny of you ever get this sensation?       \nIt made me think about the idea of Tesla potentially licensing out FSD to other non Tesla vehicles.  I know that could be a problem business-wise. But Tesla is a tech company first, so maybe there could be a benefit there for them to do that. Like for example, that new Modular Slate vehicle. What if adding licensed FSD hardware to the vehicle was a option. Tesla would get the money from the cost of that addition, and <PERSON><PERSON> get the cut of the 💰 from the rest of the vehicle.   I really would love to see FSD on non Tesla someday.", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-05-05T03:50:42", "url": "https://reddit.com/r/SelfDrivingCars/comments/1kf2gsv/any_of_you_that_frequently_use_fsd_ever_get_feel/", "upvotes": 4, "comments_count": 50, "sentiment": "neutral", "engagement_score": 104.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kf6qh7", "title": "Google chrome translates the word \"Arabic\" into Al-Qaeda", "content": "Funny and provocative by google chrome honestly. Whenever you try to translate a page from Arabic to English the word \"العربية\" translates into \"Al-Qaeda\"   \nNow if this was something against the LGBTQ community or the black community everyone would start talking about it, crazy. ", "author": "Rich_Ad_6869", "created_time": "2025-05-05T08:42:56", "url": "https://reddit.com/r/google/comments/1kf6qh7/google_chrome_translates_the_word_arabic_into/", "upvotes": 0, "comments_count": 16, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfazwm", "title": "China's BYD hits $107 billion in revenue in 2024 beating Elon Musk's Tesla for the first time", "content": "Chinese EV giant BYD surpassed Tesla in FY24 revenue, reporting $107.1 billion compared to Tesla’s $97.7 billion, marking a significant milestone by crossing the $100 billion threshold. \n\nDespite 20.5% of BYD’s revenue coming from its mobile handset business, its strong performance in electric and hybrid vehicle sales, bolstered by innovations like a fast-charging system, intensifies competition with Tesla in the global EV market.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-05T12:59:29", "url": "https://reddit.com/r/economy/comments/1kfazwm/chinas_byd_hits_107_billion_in_revenue_in_2024/", "upvotes": 1213, "comments_count": 91, "sentiment": "bullish", "engagement_score": 1395.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfb0rq", "title": "Tesla’s European Death Spiral Has No End In Sight | After a disastrous first quarter, Tesla’s sales figures in Europe tanked even further in April.", "content": "", "author": "chrisdh79", "created_time": "2025-05-05T13:00:31", "url": "https://reddit.com/r/technology/comments/1kfb0rq/teslas_european_death_spiral_has_no_end_in_sight/", "upvotes": 20434, "comments_count": 835, "sentiment": "neutral", "engagement_score": 22104.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfbfoi", "title": "May be buying a new house sooner than anticipated - most of savings in the market. What to do?", "content": "Long story short - likely buying a bigger house later this year or early next. We have a lot of money saved but most of it is in the market. Here's how it breaks out and i'm concerned that i'm exposed to much too much risk currently and may need to sell and transfer some money.\n\n* **Vanguard Brokerage Account - $182k**\n   * VMFXX - Money Market Fund - 62k (HYSA @ 4.3%)\n   * VTSAX - 75k\n   * VSGAX - 15k\n   * VTIAX - 15k\n   * TSLA - 15k\n* **Raymond James Brokerage Account - $140k**\n   * from a financial advisor that i'm trying to break away from. spread out around 10 funds. +37k since 2018.\n* **Retirement in Vanguard**\n   * Rollover IRA - 90k (former job's 401k)\n   * Roth IRA - 54k\n* **Schwab 401k - 30k (current job)**\n* **PNC Checking/Savings - 20k (emergency fund)**\n\nI've been pumping money into VTSAX over the last few years, not anticipating a major purchase for a long time. Circumstances have changed and we may need to move sooner than anticipacted.\n\nMy first point of concern is where our potential $150k down payment is coming from. My initial thought is to just completely liquidate my Raymond James account, since I want to get away from that anyway. An advisor who does nothing is making 1% quarterly on that and it pisses me off. This will come with a tax hit though of course.\n\nAlso, i'm not looking to sell my current house. I don't think I need to plus It's a nice 3 bed 2.5 bath duplex with a $1250 mortgage from a covid refi. Prob gonna keep this forever.\n\nWhat's the best play here?", "author": "ftwin", "created_time": "2025-05-05T13:19:38", "url": "https://reddit.com/r/personalfinance/comments/1kfbfoi/may_be_buying_a_new_house_sooner_than_anticipated/", "upvotes": 8, "comments_count": 15, "sentiment": "bullish", "engagement_score": 38.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfnrnr", "title": "Tesla Cybertruck inventory skyrockets to record high", "content": "", "author": "SpriteZeroY2k", "created_time": "2025-05-05T21:38:38", "url": "https://reddit.com/r/electricvehicles/comments/1kfnrnr/tesla_cybertruck_inventory_skyrockets_to_record/", "upvotes": 1185, "comments_count": 396, "sentiment": "bullish", "engagement_score": 1977.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfop3o", "title": "Need Help Architecting Low-Latency, High-Concurrency Task Execution with Cloud Run (200+ tasks in parallel)", "content": "Hi all,\n\nI’m building a system on Google Cloud Platform and would love architectural input from someone experienced in designing **high-concurrency, low-latency pipelines** with **Cloud Run + task queues**.\n\n# 🚀 The Goal:\n\nI have an **API running on Cloud Run (Service)** that receives user requests and generates tasks.\n\nEach task takes **1–2 minutes on average**, sometimes up to **30 minutes**.\n\nMy goal is that when 100–200 tasks are submitted at once, they are **picked up and processed almost instantly** (within \\~10 seconds delay at most).\n\nIn other words: high parallelism with minimal latency and operational simplicity.\n\n# 🛠️ What I’ve Tried So Far:\n\n# 1. Pub/Sub (Push mode) to Cloud Run Service\n\n* Tasks are published to a Pub/Sub topic with a push subscription to a Cloud Run Service.\n* **Problem:** Push delivery doesn’t scale up fast enough. It uses a slow-start algorithm that gradually increases load.\n* **Another issue:** Cloud Run Service in push mode is **limited to 10 min processing** (ack deadline), but I need up to 30 mins.\n* **Bottom line:** latency is too high and burst handling is weak.\n\n# 2. Pub/Sub (Pull) with Dispatcher + Cloud Run Services\n\n* I created a **dispatcher** that pulls messages from Pub/Sub and dispatches them to Cloud Run Services (via HTTP).\n* Added counters and concurrency management (semaphores, thread pools).\n* **Problem:** Complex to manage state/concurrency across tasks, plus Cloud Run Services still don’t scale fast enough for a true burst.\n* Switched dispatcher to launch **Cloud Run Jobs** instead of Services.\n   * **Result:** even more latency (\\~2 minutes cold start per task) and way more complexity to orchestrate.\n\n**3. Cloud Tasks → Cloud Run Service**\n\n* Used Cloud Tasks with aggressive settings (max\\_dispatches\\_per\\_second, max\\_concurrent\\_dispatches, etc.).\n* Despite tweaking all limits, **Cloud Tasks dispatches very slowly** in practice.\n* Again, Cloud Run doesn’t burst fast enough to handle 100+ requests in parallel without serious delay.\n\n# 🤔 What I’m Looking For:\n\n* A **simple**, scalable design that allows:\n   * Accepting user requests via API\n   * Enqueuing tasks quickly\n   * Processing tasks **at scale (100–500 concurrent)** with **minimal latency (few seconds)**\n   * Keeping **task duration support up to 30 minutes**\n* Ideally using **Cloud Run, Pub/Sub, or Cloud Tasks**, but I’m open to creative use of GKE, Workflows, Eventarc, or even hybrid models if needed — as long as the **complexity is kept low.**\n\n**❓Questions:**\n\n* Has anyone built something similar with **Cloud Run** and succeeded with near real-time scaling?\n* Is **Cloud Run Job** ever a viable option for 100+ concurrent executions with fast startup?\n* Should I abandon Cloud Run for something else if low latency at high scale is essential?\n* Any creative use of **GKE Autopilot**, **Workflows**, or **Batch** that can act as “burstable” workers?\n\nWould appreciate any architectural suggestions, war stories, or even referrals to someone who’s built something similar.\n\nThanks so much 🙏", "author": "yakirbitan", "created_time": "2025-05-05T22:18:19", "url": "https://reddit.com/r/cloudcomputing/comments/1kfop3o/need_help_architecting_lowlatency_highconcurrency/", "upvotes": 3, "comments_count": 0, "sentiment": "bearish", "engagement_score": 3.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}]