[{"platform": "reddit", "post_id": "reddit_1jyw55m", "title": "I fell for the biggest scam on the internet. And I wish someone slapped me for it.", "content": "\n\nI’ve watched 48 YouTube videos on freelancing.\n\nBut guess what?\n\nI still haven’t sent one cold email.\n\nWhy?\nBecause I’m “learning.”\n\nOr at least that’s what I tell myself when I’m just… scared to start.\n\nWe all know this phase:\n\n— Making aesthetic to-do lists\n\n— Highlighting eBooks we’ll never finish\n\n— Saving reels titled “How to make 10K/month”\n\n— Feeling productive for just watching content\n\nBut let’s be real—this is the nicest-looking scam we’ve ever sold ourselves.\n\nBecause it feels productive... even when we’re stuck.\n\nIt’s like putting on gym clothes and feeling fit, without ever hitting the gym.\n\nBut learning isn’t progress.  \nExecution is.\n\nAnd it took me months to accept that.\n\nThere’s no perfect strategy.\n\nNo right time.\n\nNo magical feeling of “I’m ready now.”\n\nThere’s only one real shift that works:\n\nStop studying the battlefield.\nStart swinging the sword.\n\nEven if your first 10 cold emails flop...\n\nThey’ll teach you more than 100 reels ever will.\n\nPS: If you’ve ghosted your own to-do list today, don’t worry.\nShe’ll take you back… if you actually text her this time.", "author": "ShubhamkWrites", "created_time": "2025-04-14T11:04:35", "url": "https://reddit.com/r/Entrepreneur/comments/1jyw55m/i_fell_for_the_biggest_scam_on_the_internet_and_i/", "upvotes": 0, "comments_count": 67, "sentiment": "neutral", "engagement_score": 134.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jywdjd", "title": "Skibdi toilet isnt brainrot", "content": "The main imagine you see is indeed from the original series. Skibdii toilet has genuine lore and a Story and the content farms like lankybox and other stuff Ruins it. Honestly the original series first epsiodes are really just shitposts but as the series continues as stupid as that sounds has genuine lore. Skibidi toilet is the living defenition of \"dont judge a book by its cover\" why do u think im defending a toilet show? Im not stupid but skibidi toilet is way more then a toilet show and has genuine lore and is interessant.loom at the first 2 minutes of the season 25 compliation u will realise that the content farms are the thing that gives <PERSON><PERSON><PERSON> its bad name.", "author": "deleted", "created_time": "2025-04-14T11:18:35", "url": "https://reddit.com/r/youtube/comments/1jywdjd/skibdi_toilet_isnt_brainrot/", "upvotes": 1, "comments_count": 84, "sentiment": "neutral", "engagement_score": 169.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz1h9a", "title": "GOOGL Call Strategy Ahead of I/O 2025: Triple Catalysts (TPU Ironwood/Waymo IPO/Auto-Scaling) + May", "content": "Bullish strategy:\n\nShort term: Buy 250417 165 call options, 1/4 position, entry price around 1.55.\n\nMedium term: Buy 250530 165 call options, 1/4 position, entry price around 8.1.\n\nCore logic\n\nFundamental support\n\nTariff exemption: US semiconductor equipment tariff exemption directly reduces the manufacturing cost of AI chips (TPU) and increases gross profit margin\n\nTechnical barriers: The seventh-generation TPU Ironwood has a 3,600-fold increase in inference performance, and cloud business competitiveness has been enhanced\n\nValuation repair: The current price-to-earnings ratio is 20.3 times, lower than the historical average of 21.5 times, and the target price implies a 27% upside\n\nTechnical breakthrough\n\nThe stock price broke through the short-term resistance level of $159.46, and the trading volume was mildly enlarged (turnover rate 0.05%), and the support level of $152.26 provided a safety margin\n\nGoogle I/O Developer Conference\n\nAs an annual technology event, Google I/O is usually held in mid-to-late May, focusing on the release of innovative products such as artificial intelligence, Android system, and hardware (such as the Pixel series). The 2025 conference may continue this time frame\n\nAI hardware iteration\n\nThe seventh-generation TPU Ironwood has been released. The next stage may focus on edge computing chips or quantum computing collaborative solutions to further consolidate cloud computing barriers\n\nAutonomous driving commercialization\n\nWaymo's valuation exceeds $45 billion. If it accelerates urban expansion or IPOs in 2025, it will be a catalyst for stock prices", "author": "Opening-Camera5485", "created_time": "2025-04-14T15:20:21", "url": "https://reddit.com/r/options/comments/1jz1h9a/googl_call_strategy_ahead_of_io_2025_triple/", "upvotes": 16, "comments_count": 2, "sentiment": "bullish", "engagement_score": 20.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz1v78", "title": "Google's Prompt Engineering PDF Breakdown with Examples - April 2025", "content": "You already know that Google dropped a 68-page guide on advanced prompt engineering\n\nSolid stuff! Highly recommend reading it\n\nBUT… if you don’t want to go through 68 pages, I have made it easy for you\n\n.. By creating this Cheat Sheet\n\nA Quick read to understand various advanced prompt techniques such as CoT, ToT, ReAct, and so on\n\nThe sheet contains all the prompt techniques from the doc, broken down into:\n\n\\-Prompt Name  \n\\- How to Use It  \n\\- Prompt Patterns (like Prof<PERSON> <PERSON>'s style)  \n\\- Prompt Examples  \n\\- Best For  \n\\- Use cases\n\nIt’s FREE. to Copy, Share & Remix\n\nGo download it. Play around. Build something cool\n\nWill add the link in the comment", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-14T15:36:13", "url": "https://reddit.com/r/Entrepreneur/comments/1jz1v78/googles_prompt_engineering_pdf_breakdown_with/", "upvotes": 1, "comments_count": 6, "sentiment": "bearish", "engagement_score": 13.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz1w1z", "title": "Google won't let cheap Android phones and tablets ship with only 16GB storage anymore", "content": "", "author": "thewhippersnapper4", "created_time": "2025-04-14T15:37:13", "url": "https://reddit.com/r/Android/comments/1jz1w1z/google_wont_let_cheap_android_phones_and_tablets/", "upvotes": 1471, "comments_count": 169, "sentiment": "neutral", "engagement_score": 1809.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz27hb", "title": "What’s one underrated tactic in digital marketing that gave you outsized results?", "content": "Everyone talks about the big stuff: SEO, paid ads, funnels, content, etc. But sometimes it’s the small, overlooked things that make a big difference.\n\nFor example, I once saw a local business double their call volume just by optimizing their Google Business Profile categories and FAQ section. No ad spend, no fancy tools, just clarity and relevance.\n\nCurious what underrated tactics, tools, or platforms you’ve used that delivered surprising results. Especially interested in things that work in specific niches or with low budgets.\n\nLet’s build a list of marketing “hidden gems” that actually move the needle.", "author": "Alive_<PERSON>er_6057", "created_time": "2025-04-14T15:50:07", "url": "https://reddit.com/r/DigitalMarketing/comments/1jz27hb/whats_one_underrated_tactic_in_digital_marketing/", "upvotes": 128, "comments_count": 59, "sentiment": "neutral", "engagement_score": 246.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz45do", "title": "Best cloud computing for LLMs (AI) and ITAR data?", "content": "I've read about Azure and Databricks but that doesn't seems exactly what I want. What I DO want is an ITAR compliant cloud service where the LLM endpoints are also ITAR compliant. \n\nAny tips or suggestions? I know both Azure and AWS offer ITAR gov cloud things, but details on AI integration aren't super specific afaik.", "author": "FlyBoi-1976", "created_time": "2025-04-14T17:09:27", "url": "https://reddit.com/r/cloudcomputing/comments/1jz45do/best_cloud_computing_for_llms_ai_and_itar_data/", "upvotes": 2, "comments_count": 1, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz5kxh", "title": "Google has started hiring for Post-AGI Research 👀", "content": "", "author": "eternviking", "created_time": "2025-04-14T18:07:27", "url": "https://reddit.com/r/singularity/comments/1jz5kxh/google_has_started_hiring_for_postagi_research/", "upvotes": 1333, "comments_count": 160, "sentiment": "neutral", "engagement_score": 1653.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz88ur", "title": "I don't care anymore. I want to live my life.", "content": "I'm 27F and it just hit me that my whole adult life up until this point has been made about saving and investing every dime. Even if that meant going without the basic things in life that make me happy. I never get my hair done, I never get my nails done, I never spend on workout classes I enjoy, I never travel, I never water my passions. Yes, there are alternatives to all of this. But at what cost? Feels like my 20s are passing me by. Here's what I've managed to do financially up until this point:\n\nHYSA: 100K (don't kill me, the economy rn scares me)\n\nRetirement: 30k\n\nOwn 3 homes (2 being rentals, 1 primary): 200k in equity\n\nI'm in the process of taking a 6-month sabbatical to travel. What's life worth if you can't truly enjoy it? I can't be alone on this.\n\nEDIT: People are asking me how and accusing me of having rich parents. Here’s my story: I did all of this honestly and one my OWN. I grew up with a single mom. Who raised me and my sister all alone. Didn’t have money to send me to college so I worked at Amazon from 17-21. Lived on my own paying $700 in rent. Managed to save 30k in those 4 years. Then I got my first corporate job at 21 making about 60k/yr as an assistant project manager and purchased my first home with no down payment. Left there at 24 with about 60k saved and purchased another home. And by 26, i double my income making 120k. Purchased my now primary. Now at 27, I’m sitting at around 150k a year salary as a project manager. I kept my expenses relatively low and saved almost every penny left over. \n\nTHANK YOU all for your valuable input here! I’ve struggled with scarcity mindset from childhood and I thought it was normal because it brought me comfort and success. I’ve realized that I’m in a much better place than I ever realized before and that it’s totally okay to take a breather.", "author": "ScreenProper3291", "created_time": "2025-04-14T19:53:26", "url": "https://reddit.com/r/Fire/comments/1jz88ur/i_dont_care_anymore_i_want_to_live_my_life/", "upvotes": 2843, "comments_count": 597, "sentiment": "neutral", "engagement_score": 4037.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jz9v23", "title": "How to do I start investing?", "content": "I am new to the stock market. I plan to start with Google Finance and invest a little but to start off (No more than $100). What companies would you recommend?", "author": "ilovecookies1029", "created_time": "2025-04-14T20:58:15", "url": "https://reddit.com/r/FinancialPlanning/comments/1jz9v23/how_to_do_i_start_investing/", "upvotes": 6, "comments_count": 9, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}]