{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-03-13", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Microsoft refusing to sort out their messy OneDrive server migration", "content": "I've spent the past week unable to access my OneDrive files. I've extensively tried Microsoft support to no avail. Every file is visible in OneDrive in the browser, but clicking on any file leads to \"500 internal server error\". I have been through an absurdly thorough troubleshooting routine. \n\nMicrosoft is apparently aware of this exact issue affecting a significant number of users due to a server migration, but is not willing to do any workarounds or provide access in a timely manner. Instead, customers without access to OneDrive are supposed to wait until \"mid-March\" for a resolution.\n\nI am angry and incredibly disappointed that Microsoft mishandled a routine server migration and that Microsoft is still unable to provide me access to my files. Cloud storage is supposed to be reliable, but apparently we need multiple cloud AND physical backups. I shouldn't have to spend a week getting fobbed off by tech support and told it's my fault, before being told this is a widespread issue and there's nothing anyone can do except wait.", "created_time": "2025-03-13T02:23:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 26.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Mapleaf42", "url": "https://reddit.com/r/microsoft/comments/1ja1p1p/microsoft_refusing_to_sort_out_their_messy/", "ticker": "MSFT", "date": "2025-03-13"}, {"title": "AWS / Microsoft level conversion", "content": "Hi all, I recently applied and interviewed for a data center technician role. I'm an L3 DCT in AWS but it's hard to gauge whether this role is equivalent or under? \n\nI tried asking one of the hiring managers what it could equate to but they didn't really have a response? \n\nSomething AWS recently started doing is only hiring L2 and then a year of trainee status before promo to L3. \n\n", "created_time": "2025-03-13T12:44:01", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Campfire-9009", "url": "https://reddit.com/r/microsoft/comments/1jab2ym/aws_microsoft_level_conversion/", "ticker": "MSFT", "date": "2025-03-13"}, {"title": "Have you used the Microsoft Zero Trust Framework Deployment tool?", "content": "I'm excited to learn more about the #microsoft #zerotrust #MAM tooling in today's latest Microsoft Security Customer Connection Program (CCP) call!  \n  \nCheck it out here: [https://microsoft.github.io/zerotrustassessment](https://microsoft.github.io/zerotrustassessment)  \n  \nIt helps walk you or your clients through the Microsoft Zero Trust Framework deployment.  \n  \nMicrosoft Security Customer Connection Program join here [www.aka.ms/JoinCCP](www.aka.ms/JoinCCP)", "created_time": "2025-03-13T15:25:47", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "CPFCoaching", "url": "https://reddit.com/r/microsoft/comments/1jaekcn/have_you_used_the_microsoft_zero_trust_framework/", "ticker": "MSFT", "date": "2025-03-13"}, {"title": "Commuting to Microsoft from Fremont", "content": "I’ll be interning at Microsoft this summer and found a decent place in Fremont and I heard there is a shuttle that makes stops near there. Would this be a reasonable commute to do every day? How long would it take? Is there any flexibility for remote work? Thanks!!", "created_time": "2025-03-13T18:48:13", "platform": "reddit", "sentiment": "bullish", "engagement_score": 8.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Independent-Ground40", "url": "https://reddit.com/r/microsoft/comments/1jajhcz/commuting_to_microsoft_from_fremont/", "ticker": "MSFT", "date": "2025-03-13"}, {"title": "is it worth moving from Amazon to MSFT", "content": "Hey everyone,\n\nI’m currently an L6 Finance Manager at Amazon HQ2 and have been with the company for three years. I recently received an offer for an L62 Senior Finance Manager role at Microsoft. The total comp ranges from $180k-$201k, which is pretty comparable to where I’m at now.\n\nHonestly, I’m feeling pretty burnt out at Amazon so a change would be welcome\n\nThe tricky part? The Microsoft role was originally supposed to be based in Reston but now they want it to be in Redmond. I’m from the East Coast, with all my family within an hour’s drive, so while I am open to moving to the West Coast I would be moving to a location where I would be completely alone.\n\nI’m open to relocating, but I’m torn between a fresh start and staying close to my support system.\n\nAnyone else made a similar move? Is the switch to Microsoft worth it, especially considering the relocation? Would love to hear your thoughts or any advice on how to approach this decision!", "created_time": "2025-03-13T19:12:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 251.0, "upvotes": 131, "num_comments": 0, "subreddit": "unknown", "author": "Novel_Procedure_119", "url": "https://reddit.com/r/microsoft/comments/1jak223/is_it_worth_moving_from_amazon_to_msft/", "ticker": "MSFT", "date": "2025-03-13"}, {"title": "One Drive (or \"Microsoft 365 Personal\") has jumped from £59.99 to £84.99 - a 42% increase, why?", "content": "Is anyone else shocked by this massive price hike? \n\nI've been using it for years to keep my documents backed up - mostly family pictures, payslips, and random other documents. \n\nI'm looking for alternatives as it seems Microsoft are charging you for AI features regardless of if you need them or not and there doesn't seem to be a normal plan. \n\nI found this site to [compare cloud storage providers](https://comparisontabl.es/cloud-storage/) \\- has anyone moved and who to?", "created_time": "2025-03-13T21:00:05", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "OrangeRackso", "url": "https://reddit.com/r/microsoft/comments/1jamm89/one_drive_or_microsoft_365_personal_has_jumped/", "ticker": "MSFT", "date": "2025-03-13"}, {"title": "10 year yield and govt shutdown", "content": "Hi! Is there reason for this hesitation to buy the 10 year? Yes it’s down today a little but even when <PERSON><PERSON> was in office at was 3.3 at the end of his presidency.. \n\nDoes the govt shutdown cause hesitancy for people to buy bonds? \n\nWith all the bad news..companies reporting slow down..it’s very clear we are on a path to recession like activity..and this isn’t even counting in the government slashing that will in turn cause more layoffs to contract workers via cancelling contracts ect..\n\nAnd FURTHER. The president, the secretary is treasury..and secretary of commerce ALL WANT THE TEN YEAR YIELD DOWN..\n\nHonestly if the ten year would go down to 3 I think all this fiasco would be done. \n\nAnyway trump still going full steam ahead to cause a recession quickly so he can have enough time to fix it. He is all but saying he is going to cause a recession. \n\nWill the 10 year fall harder after the govt shutdown is resolved? ", "created_time": "2025-03-13T22:13:05", "platform": "reddit", "sentiment": "bullish", "engagement_score": 21.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/investing_discussion/comments/1jaobaw/10_year_yield_and_govt_shutdown/", "ticker": "MSFT", "date": "2025-03-13"}, {"title": "Rejected or no?!", "content": "I interviewed for an internal transfer on the last week of February, but I still haven’t heard back from the recruiter. I’ve followed up twice via email, but there’s been no response so far. Does Microsoft typically notify candidates if they’ve been rejected?", "created_time": "2025-03-12T05:45:38", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON>-Engineer", "url": "https://reddit.com/r/microsoft/comments/1j9cja4/rejected_or_no/", "ticker": "MSFT", "date": "2025-03-12"}, {"title": "A years-long scam that began with fake Windows pop-ups ends with PayPal payments: Con artists in Cyprus took advantage of Windows users through a scam designed to create multiple transactions — all based on malware that didn't exist.", "content": "", "created_time": "2025-03-12T16:25:49", "platform": "reddit", "sentiment": "bullish", "engagement_score": 9.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "wind_of_pain", "url": "https://reddit.com/r/microsoft/comments/1j9o0qq/a_yearslong_scam_that_began_with_fake_windows/", "ticker": "MSFT", "date": "2025-03-12"}, {"title": "Can I work remotely as a new grad for Azure?", "content": "If I potentially get a ft offer from an Azure team, can I work remotely from across the country as a new grad?", "created_time": "2025-03-12T18:19:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 17.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "No-Tangelo-1857", "url": "https://reddit.com/r/microsoft/comments/1j9qtts/can_i_work_remotely_as_a_new_grad_for_azure/", "ticker": "MSFT", "date": "2025-03-12"}, {"title": "Is working at Microsoft as a software engineer really that stressful?", "content": "Hello everyone, this is not a personal experience but rather a close one. My partner, with whom I live, has been working at Microsoft as a software engineer for a little over a year. Ever since he joined, everything has changed—he’s stressed all the time, constantly works overtime, even on holidays and weekends. When he’s on call, he doesn’t sleep well or eat properly. He tells me that his teammates are not very collaborative or willing to help; they are more individualistic.\n\nI even overheard a meeting with his direct manager once, where the manager asked if they felt pressured. Everyone answered yes, and the manager simply said that’s just Microsoft’s culture, that there was nothing to do but adapt… It makes me really sad to see how my partner has changed in what we initially thought would be his dream job. The benefits are excellent, but I don’t think they make up for the physical and emotional toll it has taken on him and our relationship.\n\nI’m also a software engineer, and of course, at some point in my career, I dreamed of working at a big tech company like Microsoft. I wonder, is it really this bad for everyone? Or did we just have bad luck?\n\nThank you for your replies ", "created_time": "2025-03-12T19:59:29", "platform": "reddit", "sentiment": "neutral", "engagement_score": 282.0, "upvotes": 142, "num_comments": 0, "subreddit": "unknown", "author": "LoFiSloth", "url": "https://reddit.com/r/microsoft/comments/1j9tb15/is_working_at_microsoft_as_a_software_engineer/", "ticker": "MSFT", "date": "2025-03-12"}, {"title": "Microsoft new billing experience is bugged", "content": "We're currently experiencing a major issue with Microsoft's new billing system, which prevents us from purchasing certain new services. Despite having transitioned from the old MOSA (Microsoft Online Subscription Agreement) to the new MCA (Microsoft Customer Agreement), our account appears to be stuck in limbo between these two billing experiences.\n\nWhen attempting to buy a service that requires MCA, we're prompted to create a new billing profile—even though we've already set one up via Azure. Strangely, this billing profile doesn't appear in the Microsoft Admin Center at all.\n\nWe've had multiple sessions with Microsoft support, including several screen-sharing meetings. However, support has so far been unable to resolve the issue. The support ticket remains open, and we continue to receive daily emails indicating they're still working on a solution. Occasionally, support contacts us for further screen-sharing sessions, but unfortunately, the issue persists.\n\nEven more concerning is the possibility that the billing system may no longer be generating new invoices for our existing Microsoft Business Premium subscriptions. While being unable to purchase additional services is problematic, the risk of disruption to billing for existing subscriptions is deeply troubling.\n\nIt's genuinely frustrating that Microsoft acknowledges the issue yet seems incapable of resolving it—even after weeks of continuous effort. This isn't a criticism of the individual support representatives, who clearly lack the necessary permissions to fix the issue. Rather, the problem appears to stem from higher-level flaws within Microsoft's extraordinarily bureaucratic structure, compounded by the release of a severely bugged new billing experience.\n\nHas anyone else encountered this issue or something similar? Any advice or shared experiences would be greatly appreciated.", "created_time": "2025-03-12T20:35:28", "platform": "reddit", "sentiment": "bullish", "engagement_score": 11.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "Darius991", "url": "https://reddit.com/r/microsoft/comments/1j9u6is/microsoft_new_billing_experience_is_bugged/", "ticker": "MSFT", "date": "2025-03-12"}, {"title": "Former Microsoft exec says the first Xbox was killed early in favor of 360 because it was \"losing money left right and center,\" but luckily \"we could afford to hemorrhage cash\"", "content": "", "created_time": "2025-03-11T00:02:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 275.0, "upvotes": 261, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j8dj6r/former_microsoft_exec_says_the_first_xbox_was/", "ticker": "MSFT", "date": "2025-03-11"}, {"title": "Windows 10 22h2 or 11 23h2?", "content": "What os should I use on my probook 630 g8, intel I5 11-th gen?", "created_time": "2025-03-11T12:43:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 50.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ThinkPad1989", "url": "https://reddit.com/r/microsoft/comments/1j8pudt/windows_10_22h2_or_11_23h2/", "ticker": "MSFT", "date": "2025-03-11"}, {"title": "Xbox's hardware fightback begins with a gaming handheld planned for later this year, with full next-gen consoles targeting 2027", "content": "Microsoft's first full foray into PC gaming handhelds begins with a partner device set to launch later this year, with its own Xbox Series X|S successors now fully in production.", "created_time": "2025-03-11T16:45:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 17.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j8v9v8/xboxs_hardware_fightback_begins_with_a_gaming/", "ticker": "MSFT", "date": "2025-03-11"}, {"title": "Interviewed on 2/14 for CSAM role", "content": "Hey everyone,\n\nI recently interviewed with Microsoft for a Customer Success Manager role and got an email saying I wasn't selected for an offer at this time, but I’m still under consideration if a spot opens up or if someone declines their offer. I'm about to graduate and really hope to secure my first job in this competitive market.\n\nHas anyone been in this situation before? Did you end up getting an offer later, or is this basically a soft rejection? How long should I expect to wait before following up?\n\nAny insights would be appreciated!", "created_time": "2025-03-11T21:55:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Rude-Department838", "url": "https://reddit.com/r/microsoft/comments/1j92trh/interviewed_on_214_for_csam_role/", "ticker": "MSFT", "date": "2025-03-11"}, {"title": "I've read through many posts in this sub but I still can't make up my mind on office 365. Please guide me.", "content": "I recently purchased a surface laptop for personal use (studying and entertainment). When I say study I mean ACCA, not with a university/school so no edu email.  Now, I mostly use OneNote because that's where I have all my notes separated by subject etc. I very rarely use word and excel but i still want them there incase I randomly end up needing them. \n\nNow my question... Based on the above, should I just buy the the one time purchase of office 2024 or should I still consider 365 personal?  I see most people recommend purchasing 365 in the posts I saw but I'm not sure how it wouldn't be a waste of money in my particular case. I wish I had access to copilot but I don't think I would be using it enough in MS office to justify $99 per year.  \n\nWhat do y'all think?\n", "created_time": "2025-03-10T02:25:11", "platform": "reddit", "sentiment": "bullish", "engagement_score": 50.0, "upvotes": 32, "num_comments": 0, "subreddit": "unknown", "author": "MarioDF", "url": "https://reddit.com/r/microsoft/comments/1j7odkm/ive_read_through_many_posts_in_this_sub_but_i/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Rejected or not?", "content": "<PERSON> did microsoft loop interview for Technology Specialist Internship last week about 6 days ago.  Interview I think went well, interviewers gave me quite a good feedback after each round.  But i an starting to get worried as it has been quite long, do I reach out to recruiters or my interviewer on linkedin? Is it normal or I am getting too paranoid. Thank you very much guys", "created_time": "2025-03-10T13:11:52", "platform": "reddit", "sentiment": "bullish", "engagement_score": 88.0, "upvotes": 24, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1j7y9jm/rejected_or_not/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "I am using windows 8.1 pro WMC. If i dont want to upgrade can i still use it for my daily use?", "content": "Please an advice is needed.", "created_time": "2025-03-10T13:41:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Turbulent-Ad415", "url": "https://reddit.com/r/microsoft/comments/1j7yv0k/i_am_using_windows_81_pro_wmc_if_i_dont_want_to/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Storm-0558", "content": "Is there any way the Storm-0558 attack on Microsoft could have affected my iPhone?  Other than having a personal Hotmail account, I don’t see there’s a connection.  But the timing of the attack on Microsoft and what I saw happening on my iPhone were exactly the same, and I doubt that was a coincidence. ", "created_time": "2025-03-10T15:14:38", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "OneToughTexan2", "url": "https://reddit.com/r/microsoft/comments/1j80xqx/storm0558/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Microsoft is holding a 50th anniversary and Copilot event in April", "content": "", "created_time": "2025-03-10T19:04:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 64.0, "upvotes": 58, "num_comments": 0, "subreddit": "unknown", "author": "samiy2k", "url": "https://reddit.com/r/microsoft/comments/1j86je3/microsoft_is_holding_a_50th_anniversary_and/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Microsoft discontinuing Publisher is a shame", "content": "I know Publisher is not as powerful as InDesign. I know Publisher is not as easy as Canva. However, Publisher is a fairly robust desktop publishing app for people who need it, with a relatively gentle learning curve. \n\nI used Publisher regularly for over 20 years. I only recently started using InDesign out if necessity. But Publisher is still what I show people at work who need more flexibility than Word and more control than programs like Canva and Adobe Express. \n\nI have not used Microsoft Designer yet, but it seems to be going after Canva's market rather than the professional designer. ", "created_time": "2025-03-08T01:43:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 126.0, "upvotes": 46, "num_comments": 0, "subreddit": "unknown", "author": "movieguy95453", "url": "https://reddit.com/r/microsoft/comments/1j667ex/microsoft_discontinuing_publisher_is_a_shame/", "ticker": "MSFT", "date": "2025-03-08"}, {"title": "Nearly 1 million Windows devices targeted in advanced “malvertising” spree | Malware stole login credentials, cryptocurrency, and more from infected machines.", "content": "", "created_time": "2025-03-08T03:59:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 29.0, "upvotes": 27, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j68oqa/nearly_1_million_windows_devices_targeted_in/", "ticker": "MSFT", "date": "2025-03-08"}, {"title": "What do you guys think of Microsoft shutting down Skype in May this year?", "content": "I find it kinda sad but I get the point. I remember always calling my friends on Skype right after school to play some Roblox. I haven't used it for a few years now but was still kinda shocked that it is going.\n\nWhat do you think? I am curious what others think.\n\n[https://www.microsoft.com/en-us/microsoft-365/blog/2025/02/28/the-next-chapter-moving-from-skype-to-microsoft-teams/](https://www.microsoft.com/en-us/microsoft-365/blog/2025/02/28/the-next-chapter-moving-from-skype-to-microsoft-teams/)", "created_time": "2025-03-07T16:32:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 306.0, "upvotes": 46, "num_comments": 0, "subreddit": "unknown", "author": "RoytjePoytjeGamez", "url": "https://reddit.com/r/microsoft/comments/1j5sqs5/what_do_you_guys_think_of_microsoft_shutting_down/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "Microsoft Explores AI Partnerships Beyond OpenAI: Testing xAI, Meta, and DeepSeek Models for Copilot Innovation", "content": "", "created_time": "2025-03-07T17:39:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Common_Sleep_5777", "url": "https://reddit.com/r/microsoft/comments/1j5up1b/microsoft_explores_ai_partnerships_beyond_openai/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "New iOS OneDrive is terrible.", "content": "Just recently updated the OneDrive app on my phone. The new UI is not appealing and I’m pretty sure they got rid of the option to take photos to add to a folder. I use OneDrive for work. It allows me to share on-site photos with my managers. Before the update it was super simple. Create folder > share folder > open camera > take pics and have them immediately uploaded. It was great because it kept my work photos separate from my own personal photos. Since the update I have to take the pics using my iPhone’s camera app, then go to OD and select the pics that I just took (because I’m not allowing full access to Photos). Why does Microsoft always find a way to make things more complicated??", "created_time": "2025-03-07T18:06:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 45.0, "upvotes": 23, "num_comments": 0, "subreddit": "unknown", "author": "FY00Z", "url": "https://reddit.com/r/microsoft/comments/1j5vgl5/new_ios_onedrive_is_terrible/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "It is so nice being able to just walk away from a job", "content": "My organization is moving to 5 days in office starting at the beginning of next month.  I'm currently applying for other jobs but if I don't find anything by then, I'll simply quit. I will not pointlessly put up with a 3 hour round trip commute and office politics for 5 days a week.  It is a waste of gas, time, and will make me miserable.  WFH has tremendously boosted my quality of life.  Giving it up just because some pinhead in the ivory tower said so is stupid and I just will not do it.  That is why it is so nice to have enough money to be able to just walk away from a job when the amount of BS gets to be too much.   ", "created_time": "2025-03-07T18:16:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1373.0, "upvotes": 1147, "num_comments": 0, "subreddit": "unknown", "author": "stan<PERSON>", "url": "https://reddit.com/r/financialindependence/comments/1j5vpyf/it_is_so_nice_being_able_to_just_walk_away_from_a/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "Why is Microsoft so slow to innovate on Windows and Surface Pro?", "content": "\nHey everyone,\n\nFIRST OF ALL, it’s my own opinion. If you think that I’m wrong let me know why ! \n\nI use a Surface Pro 11, and when comparing it to iPads, I can’t help but wonder: Why doesn’t Microsoft release more impactful and innovative updates for Windows and Surface Pro?\n\nI mean, of course it is not easy to innovate everytime but isn’t Microsoft supposed to be Apple’s rival ? \n\nApple releases a new iPadOS every 2-3 years with major new features (e.g., Stage Manager, Pencil improvements, UI redesigns).\n\nMicrosoft, on the other hand, mostly pushes minor Windows 11 updates, often limited to stability fixes and small adjustments (except for Copilot recently, but that’s more AI-focused than a real UI/features revolution).\n\nEven Surface Pro devices receive very few updates that enhance the touch experience, multitasking, or UI. While Surface Pro have so much potential imo ! \n\n\n\nDoes Microsoft simply not want to push innovation on Windows and Surface like Apple does with iPadOS ?\nOr is it because Windows has to remain compatible with too many different devices?\n\nI’d like to know if other users also feel this stagnation and whether they hope Microsoft will speed up its innovation pace. What do you guys think ?\n\n\n\n", "created_time": "2025-03-07T18:51:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 106.0, "upvotes": 12, "num_comments": 0, "subreddit": "unknown", "author": "Diablo1511", "url": "https://reddit.com/r/microsoft/comments/1j5wnfu/why_is_microsoft_so_slow_to_innovate_on_windows/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "Former Xbox boss admits the company once \"encouraged\" the console wars, which he believes \"were healthy for the industry\" as \"a rising tide that lifted all ships\" | But it's not \"the old days\" anymore", "content": "", "created_time": "2025-03-07T20:52:09", "platform": "reddit", "sentiment": "neutral", "engagement_score": 267.0, "upvotes": 245, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j6026m/former_xbox_boss_admits_the_company_once/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "IC4 Level 63 salary range for NYC metro - should I push for 64?", "content": "I just got an offer for an IC4, level 63. The offer is in the mid $180k range, the position put the NYC metro range between $155k and $220k.  I have 21 years' experience, 6 and a half in Azure engineering. No manager roles, 3 years as a senior Azure engineer, and in the past 3 years as a senior sys admin. The recruiter mentioned a sign-on bonus split between first paycheck and 2nd anniversary paycheck, RSUs, stock awards, company bonus, and team bonus. I've got that on verbal and am awaiting the written offer.\n\nDo I have any leverage to push for higher? The recruiter said this was the max for level 63 and anything higher would need approval, but I'm not sure if I qualify for higher levels or what the approval looks like. \n\nIt's definitely a decent step up for me but I'm a bit worried they're lowballing. I didn't see anything close to this title or team on levels.fyi. I don't know if my background in non-tech companies is an issue or what else, so any guidance would be appreciated. ", "created_time": "2025-03-07T23:49:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 67.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "MohnJaddenPowers", "url": "https://reddit.com/r/microsoft/comments/1j63xa7/ic4_level_63_salary_range_for_nyc_metro_should_i/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "MS licensing questions for the legacy program, and some partner doubts!", "content": "Guys, my buddy at an smb has some questions about MS licensing.\n\nI did my reading and digging through MS portals to give him some ideas, then I thought you guys would love this topic since everyone loves to talk about MS licensing here so much .. ./s\n\nPlease could yall help me out a lil to make sense of these questions for us and share some \"behind the scenes\" info not mentioned in kb articles about this MS licensing stuff? Also some gotcha moments with MS licensing from your experience?\n\n**He's not really a redditor so here I am posting for him:**\n\n1. The Microsoft Legacy program is ending soon and with it some included and free Microsoft licenses to server and desktop Microsoft applications. What cost effective options are out there for a current Microsoft Legacy program partner to get the similar level of free server and desktop Microsoft licenses without the ability to get a lot of new certifications and sales\n2. If you have Microsoft Server Datacenter 2022 installed, and then you create 5 virtual server environments running Microsoft Server 2022 Standard, then do you need to purchases licenses for the virtual server environments if they are running Microsoft Server 2022 or 2019 Standard?\n3. Can you buy all three Partner Launch, Partner Success Core, and Partner Success Expanded for a single partner global account? Can you confirm that this would effectively allow the partner to have 24 Microsoft Visual Studio Professional Subscription as an example. Can you confirm by sending a link that shows this is possible\n4. Confirm that, with the Partner Success packages, the licensing allows us to use any older version of the software?\n5. Confirm that the Partner Success packages include server software?\n\n  \n**UPDATE:**\n\n  \nAfter a bit of digging we have this info so far, could anyone take a look and confirm this?\n\n**1. The Microsoft Legacy program is ending soon and with it some included and free Microsoft licenses to server and desktop Microsoft applications. What cost effective options are out there for a current Microsoft Legacy program partner to get the similar level of free server and desktop Microsoft licenses without the ability to get a lot of new certifications and sales.**\n\n* We can't find any docs which refers to this \"legacy program\" is this referring to \"Microsoft Action Pack subscription\"? This just stopped in Jan 2025.\n* So the only way is to move to one of the launch, success core, or success expanded packages?\n\n  \n**2. If you have Microsoft Server Datacenter 2022 installed, and then you create 5 virtual server environments running Microsoft Server 2022 Standard, then do you need to purchases licenses for the virtual server environments if they are running Microsoft Server 2022 or 2019 Standard?**\n\n* Licensing is based on the host not on the VMs if you have DC 2022 license you can run unlimited VMs on it the min requirements are you need to license all 16 cores per server even if you have fewer cores?\n\n**3. Can you buy all three Partner Launch, Partner Success Core, and Partner Success Expanded for a single partner global account? Can you confirm that this would effectively allow the partner to have 24 Microsoft Visual Studio Professional Subscription as an example. Can you confirm by sending a link that shows this is possible?**\n\n* Yes we can buy all 3 launch, success core, success expanded packages but **ONLY 1 OF EACH INDIVIDUALLY** \\- so we cannot buy 2 of launch/SC/SE - I can't find any doc which confirms that yes we can buy all 3 but the part about buying only 1 of each is well documented in the [FAQ on every partner benefit page](https://learn.microsoft.com/en-us/partner-center/membership/partner-launch-benefits#can-i-buy-more-than-one-partner-launch-benefits).\n* And ones we buy all 3 can we use all 24 of the visual studio professional licenses? We can't find any info which confirms if we can merge benefits like these, is this against TOS?\n\n**4. Confirm that, with the Partner Success packages, the licensing allows us to use any older version of the software?**\n\n* The answer is we can only use the version of the software until the license expires and then we are expected to upgrade, but then once we upgrade do we again buy licenses?\n* Found this in the \"[terms of participation](https://assetsprod.microsoft.com/mpn-maps-product-usage-guide.pdf)\" pdf:\n   * Each partner is responsible for tracking their own consumption and entitlement of product benefits. If a partner organization is selected for a compliance audit, that organization is responsible for presenting records regarding the active program licenses used and compliance with the terms of use. **Licenses do not provide downgrade rights or any other Software Assurance services.** For deployment, management, and other similar services, learn more about Software Assurance.\n* What type of software does this even apply to? SCCM? SCEP?\n\n**5. Confirm that the Partner Success packages include server software?**\n\n* Yes it includes server management tools like:\n   * Windows 365 Enterprise – 8 vCPU, 32 GB RAM, 512 GB Storage\n   * System Center Client Management Suite (2022)\n   * System Center Endpoint Protection (2019)\n   * Windows Server CALs (not edition-specific)\n   * Windows Server Datacenter – Per core (2022)\n   * Windows Server Standard – Per core (2022)", "created_time": "2025-03-06T01:01:18", "platform": "reddit", "sentiment": "bullish", "engagement_score": 53.0, "upvotes": 47, "num_comments": 0, "subreddit": "unknown", "author": "masterofrants", "url": "https://reddit.com/r/microsoft/comments/1j4jcbn/ms_licensing_questions_for_the_legacy_program_and/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "How Can I Get Involved in Volunteer Opportunities with Microsoft?", "content": "Hello, Microsoft community!\n\nI’m interested in gaining experience by volunteering in roles related to customer service, moderation, or support for Microsoft products and services. I’m eager to help out and contribute my time and skills without the expectation of payment—just looking to gain hands-on experience.\n\nAre there any volunteer opportunities with Microsoft, or ways to get involved in these areas remotely? Any guidance or suggestions would be greatly appreciated!\n\nThank you in advance!", "created_time": "2025-03-06T01:31:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "IdeaSprout22", "url": "https://reddit.com/r/microsoft/comments/1j4jyv2/how_can_i_get_involved_in_volunteer_opportunities/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "AA Round or not?", "content": "Hi recently i went through an interview loop. At the final round it was a super senior person with 25 years of experience in microsoft (not sure his title). Is this the as appropriate round? The interview was quite nice and relaxed, basically a try to get to know me, whats your learning process, questions about my internship, any client facing roles you have etc Thank you everyone!!!!!  ", "created_time": "2025-03-06T02:24:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 26.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1j4l1j7/aa_round_or_not/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Microsoft 365 Family without AI for $99.99 a year is still available", "content": "\\[This is for US subscriptions\\] \n\nI forgot I had OneDrive full of data and had to go back and signup for another month of Microsoft 365 Family at $12.99. I had canceled my subscription two weeks ago. I renewed for a month, went back to cancel the rebill and it offered M365 Family with AI for $99.99/year. Altogether it billed me another $107.49.\n\n  \nI went back to cancel that rebill to avoid paying $129.99/yr + taxes next year. Clicked 'Cancel Subscription' and when the page loaded it offered to switch to monthly at $12.99 but right below, highlighted in bright yellow, was the 'LOWER COST WITHOUT AI' option. M365 Family Classic $99.99/year for 6 people, and a link to 'Buy at $9.99/month'\n\nElsewhere someone said switching like that will tack a year of M365 Family, so in March 2026 I would get billed $99.99+taxes for service without AI until March 2027.", "created_time": "2025-03-06T21:26:07", "platform": "reddit", "sentiment": "bullish", "engagement_score": 40.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "brozelam", "url": "https://reddit.com/r/microsoft/comments/1j56ld1/microsoft_365_family_without_ai_for_9999_a_year/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Xbox update resetting consoles to factory settings, Insiders say", "content": "", "created_time": "2025-03-06T21:45:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 81.0, "upvotes": 59, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j5725h/xbox_update_resetting_consoles_to_factory/", "ticker": "MSFT", "date": "2025-03-06"}], "metadata": {"timestamp": "2025-07-06T21:16:25.297056", "end_date": "2025-03-13", "days_back": 7, "successful_dates": ["2025-03-13", "2025-03-12", "2025-03-11", "2025-03-10", "2025-03-08", "2025-03-07", "2025-03-06"], "failed_dates": ["2025-03-09"], "source": "local"}}}}