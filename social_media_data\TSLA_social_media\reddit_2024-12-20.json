[{"platform": "reddit", "post_id": "reddit_1hi7fcs", "title": "At what point, crime organizations start to threaten you ? ", "content": "Just to reassure you, I don’t watch that many drug-related movies 😂\n\nBut seriously, if you’re in a big city and your business starts booming, especially in an industry where there’s a lot of mafia or gang activity, they might reach out or try to interfere with your operations.\n\nWhat revenue would trigger their attention ?\n\nLet’s not talk about the restaurant example this time 😅", "author": "Only_Ad1117", "created_time": "2024-12-20T00:27:53", "url": "https://reddit.com/r/Entrepreneur/comments/1hi7fcs/at_what_point_crime_organizations_start_to/", "upvotes": 5, "comments_count": 43, "sentiment": "neutral", "engagement_score": 91.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hi7yll", "title": "Goodbye Chrome, was a fun run", "content": "I don't think I searched for another web browser so fast since the last time I installed windows on my computer and trying to download chrome from edge. \n\nhttps://preview.redd.it/x4tuve6xiw7e1.png?width=1147&format=png&auto=webp&s=469f5b59fe533d110b7c26b7624ae6aafeead8d4\n\n  \nI don't think the war between ads and adblockers will stop with uBlock, so before everything goes down, best thing is to move to another place. \n\nWas a fun run! ", "author": "partyuniverse", "created_time": "2024-12-20T00:54:33", "url": "https://reddit.com/r/chrome/comments/1hi7yll/goodbye_chrome_was_a_fun_run/", "upvotes": 219, "comments_count": 63, "sentiment": "neutral", "engagement_score": 345.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hi8kph", "title": "Tesla sales crash as drivers snub Trump supporter <PERSON><PERSON>", "content": "", "author": "BikkaZz", "created_time": "2024-12-20T01:26:11", "url": "https://reddit.com/r/economy/comments/1hi8kph/tesla_sales_crash_as_drivers_snub_trump_supporter/", "upvotes": 4475, "comments_count": 402, "sentiment": "bearish", "engagement_score": 5279.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hi8xza", "title": "[Urgent advice needed] There's a high chance I get shadowbanned for posting this qn across 2 other subs but screw it. Should one sell their tsla stocks now and buy in later, when it (probably) drops further, to earn a few extra shares?", "content": "Rn its at 428 after hours, if I sell all my shares now and buy in when its, say, 400 or lower, I'd be able to get more than I do now, instead of simply holding. Since I'm planning to inveat in tsla anyway (I'm feeling bullish for at least a few months ahead in 2025), but the shares look like they're going to keep falling in the short term, how wise would it be to sell off now and buy in later? Which price would you say it is MOST LIKELY to rebound at, or at least be close to rebounding at?\n\n\n\nPs my case is a little special bevause I don't have to worry about taxes that much, let's just take it as that", "author": "PumpkinOne6486", "created_time": "2024-12-20T01:46:00", "url": "https://reddit.com/r/investing_discussion/comments/1hi8xza/urgent_advice_needed_theres_a_high_chance_i_get/", "upvotes": 5, "comments_count": 1, "sentiment": "bearish", "engagement_score": 7.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hia79i", "title": "Should we wait even longer to buy a house?", "content": "My wife and I are in our 40s and we have one kid in kindergarten.\n\nWe are still renting.\n\nWe would like to settle down into our own home, but houses are very expensive in Seattle area. We would need to buy at least $1.2m home, probably more, to get a house we would really want to live in. We are renting a nice SFH for $3600/mo currently.\n\nI have a pretty high income (for last 4-5 years), but we are late to the earning/saving game and have had some really unfortunate \"investments\" losses (we are strictly index investors now), so our net worth is \"low\" for our area/demographic (I work in tech etc).\n\nWe currently have a combined income of \\~375k/year .  We have 300k in high-yield savings that was  earmarked for down payment, and 900k in retirement and taxable brokerage accounts, so $1.2 net worth total but growing pretty fast as we are index investing at least half of our income.\n\nOur lease is up for renewal in May and we are thinking again about buying, but every rent vs. own calculator says it will take 20+ years (or NEVER) to break even, and our mortgage payment will be double what we pay in rent currently.\n\nMy job requires on-site work, and we like the area. We don't really want to move to a LCOL area.\n\nThanks for any advice", "author": "locusofself", "created_time": "2024-12-20T02:54:51", "url": "https://reddit.com/r/personalfinance/comments/1hia79i/should_we_wait_even_longer_to_buy_a_house/", "upvotes": 1, "comments_count": 33, "sentiment": "bullish", "engagement_score": 67.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiaclg", "title": "Keep old gas car or buy an EV? The prior answers on this sub are wrong.", "content": "The last mainstream media attempt at this math was in 2022. At that time, the correct sites like Motor Trend and Ars Technica showed that if you drive a car older than about 8 years, and drive it more than around 8k mi/year you would be better off buying a new EV.\n\n  \nThe reason is that the manufacturing costs were slightly higher for EV,  but it only took a few years before you were saving emissions. Gas cars emissions are almost all from the gas itself over its lifetime. So if you keep an older car, which in almost every aspect has terrible and worsening emissions, you are pumping 10k pounds of emissions and particulates into Earth each year on average.\n\nFast forward to 2025. The top selling EV is now made at a low emission factory in Texas,. Emissions for the production of the EV have been dropping. Battery manufacturing is the top emission. It's rapidly dropping for Tesla due to dry electrode batteries made in house. They target emissions at every step of the lifecycle. Recycling batteries is now at scale too. So the embedded emissions in the vehicle as it rolls of the line are now much lower than a Gas!!!!  The efficiency of the vehicle and the cleaning of the electric grid are now 3 years further along too. The particulates from brakes are nearly zero now and the tires are specific to EV to reduce wear and particulates. There are no other sources of ongoing pollution. And the energy use is WAY more efficient than burning fossil fuels due to heat and other losses. \n\nSo it really depends on how bad your gas car emissions are, and how much you use it. There are fewer and fewer folks for which the math works. \n\nSince we are only on target to meet 2% of the 48% urgently needed emissions reductions by 2030, and the harms from the warming predicted were off by about 30 years earlier...there is no time left to debate this!", "author": "outlawbernard_yum", "created_time": "2024-12-20T03:02:49", "url": "https://reddit.com/r/sustainability/comments/1hiaclg/keep_old_gas_car_or_buy_an_ev_the_prior_answers/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hienrk", "title": "Forget Chrome—Google Starts Tracking All Your Devices In 8 Weeks", "content": "", "author": "ka<PERSON>ent", "created_time": "2024-12-20T07:29:59", "url": "https://reddit.com/r/privacy/comments/1hienrk/forget_chromegoogle_starts_tracking_all_your/", "upvotes": 782, "comments_count": 117, "sentiment": "neutral", "engagement_score": 1016.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hihnl2", "title": "Everything You Need to Know About Apple Intelligence", "content": "", "author": "ChocoMuchacho", "created_time": "2024-12-20T11:16:49", "url": "https://reddit.com/r/apple/comments/1hihnl2/everything_you_need_to_know_about_apple/", "upvotes": 0, "comments_count": 19, "sentiment": "neutral", "engagement_score": 38.0, "source_subreddit": "apple", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hihpd0", "title": "94-Year-Old Trying to Visit Every Continent with <PERSON>son Reaches Antarctica, Didn’t Get First Passport Until She Was 91", "content": "", "author": "peoplemagazine", "created_time": "2024-12-20T11:20:26", "url": "https://reddit.com/r/UpliftingNews/comments/1hihpd0/94yearold_trying_to_visit_every_continent_with/", "upvotes": 23335, "comments_count": 100, "sentiment": "neutral", "engagement_score": 23535.0, "source_subreddit": "UpliftingNews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiiigy", "title": "Scared of looking for a tech cofounder", "content": "With the advent of cursor and <PERSON> im now able to build the features that I want in my study app. Before this I was looking for a tech cofounder to build these. But at the same time im apprehensive about having one partly because of obvious fact of how rare it is to have a tech cofounder who just gels with you in many aspects. I know I would need to have someone with a technical expertise but im questioning if I should just go for a cto rather than a tech cofounder to build.\n\nEDIT: as context im not that nontechnical. Completed some online courses on programming and AI yet never coded professionally. My initial thought (but became clearer just now) is to build an mvp for 2 purposes: to get users to get feedback and to find and attract potential tech cofounders whom I can select from.", "author": "Important-Koala-3536", "created_time": "2024-12-20T12:14:17", "url": "https://reddit.com/r/startups/comments/1hiiigy/scared_of_looking_for_a_tech_cofounder/", "upvotes": 2, "comments_count": 28, "sentiment": "neutral", "engagement_score": 58.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiizlq", "title": "Tesla recalls 700,000 vehicles over tire pressure warning failure", "content": "", "author": "indig<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-20T12:43:02", "url": "https://reddit.com/r/technology/comments/1hiizlq/tesla_recalls_700000_vehicles_over_tire_pressure/", "upvotes": 30539, "comments_count": 1548, "sentiment": "bullish", "engagement_score": 33635.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hijfsg", "title": "Easiest way I found to increase revenue by 20%", "content": "Ex-Meta engineer here who spent 5+ years building their ads algorithm. In 2024, I set up server side tracking for 150+ brands this year and the gains in email marketing revenue and ads ROAS were substantial.  I founded [Aimerce](http://aimerce.ai) specifically to help Shopify brands implement clean server-side tracking, and it’s wild how often small backend changes end up driving the biggest revenue lifts.\n\nThis is best for any brand doing more than 10k/month, and leverage email marketing and paid ads (Google, Meta, etc) as their core marketing channels.\n\n**Let me explain why this matters more than ever:**\n\nWhen I was at Meta building the conversion matching system, we discovered something crucial: server-side events were getting weighted significantly higher in our models compared to client-side pixel events. This wasn't just about data reliability – it was about surviving in an increasingly privacy-focused web.\n\nSafari's Intelligent Tracking Prevention (ITP) deletes ALL client-side storage (including your precious Meta pixel data) after just 7 days of user inactivity. Even worse, if users come from Facebook (with those fbclid parameters in the URL), your client-side cookies are limited to just 24 hours. This is why we saw massive drops in performance for clients only using the basic Meta pixel.\n\n**A few critical points before I dive in:**\n\n* This does not require any changes to your creatives, campaign structures or email flows\n* The core problem that needs fixing is a data integrity problem\n* Once this is setup **correctly**, no further maintenance is needed\n* When I say \"data\", I mean server-side signals feeding Meta's algorithm\n* First-party data means both platform engagement AND server-side website data\n\n**What Actually Works: Server-Side Implementation**\n\nThe secret to maintaining accurate tracking isn't sending more data – it's sending smarter data through the right channels. Here's what actually happens behind the scenes:\n\n**1. Direct Server Communication**\n\nWhen your server talks directly to Google Ads Conversion Tracking or Meta's Conversion API (CAPI), you bypass most privacy restrictions because you're not relying on browser storage. This means longer attribution windows and better matching.\n\n2. **Progressive Identity Building**\n\nInstead of relying on a single tracking point, you want to build user identity progressively:\n\n* First visit: Capture basic server-side parameters\n* Email submission: Add hashed email identifier\n* Phone submission: Layer in additional identity data\n* Purchase: Include transaction details\n\nEach step strengthens Meta's ability to match users to ads, improving your ROAS.\n\n**Common Pitfalls I See Daily**\n\n**1. Incorrect Parameter Hashing**\n\nThe number one issue I see is improper hashing of user data. Both Google and Meta require specific hashing formats, and getting this wrong tanks your match rates.\n\n**2.** **Poor Timing Implementation**\n\nYour server needs to send events in real-time. I've seen companies batch these events and send them hours later – this destroys the temporal connection between user actions and ad interactions.\n\n**3. Missing Deduplication**\n\nIf you're running both server and client events, you need proper deduplication or you'll mess up your attribution data. This is probably the most requested topic from my last post, so let me break down deduplication properly:\n\nI've seen this go wrong in so many audits at Aimerce: duplicate events, mismatched action sources, or missing Event IDs silently killing attribution. When running both server-side and client-side tracking, you need to prevent duplicate events or you'll mess up your reporting. Here's the proven approach I've implemented across hundreds of accounts:\n\n1. Event ID Management: Generate a unique ID for each event on your server. Pass this same ID to both your client-side pixel and server-side CAPI calls. Meta will automatically deduplicate events with matching IDs.\n2. Event Sources: Always set the 'action\\_source' parameter correctly, use 'website' for client-side events, use 'system\\_generated' for server-side events. Meta uses this to determine event priority when deduplicating.\n3. Timing Window: Server events should be sent within 7 seconds of the client event. This helps Meta's system confidently match and deduplicate them. If you're sending events later, you risk double-counting or missing attributions entirely.\n\nPro Tip: When in doubt, prioritize your server-side event. It contains more reliable data and better matching parameters. If you have to choose just one (like during checkout flow issues), go with server-side.\n\n**Expected results:**\n\nOnce this is setup, you should see a step change increase in the volume of matched events on your Meta Ads. Results of 1 of many brand's who implemented this [here. ](https://ibb.co/0ZmzSMw)What you're seeing is the result of\n\n1. Larger volume of data collected\n2. This data has also been enriched for precise matching because the tracker collected more customer parameters\n3. Consistent volume of matched events on Meta (Brands without this have a lower EMQ score because the matching quality is 'patchier')\n4. Meta Ads can now leverage this information to match your ads to their users\n\n**Alternatively, look for a 1-click no-code solution:**\n\nWhen evaluating your options, the key features to look for are:\n\n1. 1-click server-side setup – this minimizes room for error\n2. Does not require Google Tag Manager – it's best if it natively leverages your site's infrastructure, eg. a Shopify native app\n3. Progressive identity building and data enrichment\n4. Proper deduplication automated", "author": "Green_Database9919", "created_time": "2024-12-20T13:09:18", "url": "https://reddit.com/r/PPC/comments/1hijfsg/easiest_way_i_found_to_increase_revenue_by_20/", "upvotes": 137, "comments_count": 80, "sentiment": "neutral", "engagement_score": 297.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hikd60", "title": "Amazon workers strike across seven facilities at peak of holiday shopping season - CNBC", "content": "", "author": "AmazonNewsBot", "created_time": "2024-12-20T14:00:04", "url": "https://reddit.com/r/amazon/comments/1hikd60/amazon_workers_strike_across_seven_facilities_at/", "upvotes": 8, "comments_count": 6, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiklgt", "title": "iPad Pro for Everything: How I Rethought My Entire Workflow Around the New 11\" iPad Pro", "content": "", "author": "deleted", "created_time": "2024-12-20T14:11:00", "url": "https://reddit.com/r/apple/comments/1hiklgt/ipad_pro_for_everything_how_i_rethought_my_entire/", "upvotes": 0, "comments_count": 20, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "apple", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hilzht", "title": "<PERSON><PERSON> Suddenly Realizes He Has No Clue How to Govern. <PERSON><PERSON> is trying to backtrack on his previous demands to shut down the government.", "content": "", "author": "indig<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-20T15:17:51", "url": "https://reddit.com/r/politics/comments/1hilzht/elon_musk_suddenly_realizes_he_has_no_clue_how_to/", "upvotes": 45980, "comments_count": 3465, "sentiment": "neutral", "engagement_score": 52910.0, "source_subreddit": "politics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1himfwc", "title": "Tesla recalling almost 700,000 vehicles due to tire pressure monitoring system issue", "content": "", "author": "discardafterusage", "created_time": "2024-12-20T15:39:06", "url": "https://reddit.com/r/news/comments/1himfwc/tesla_recalling_almost_700000_vehicles_due_to/", "upvotes": 2725, "comments_count": 376, "sentiment": "neutral", "engagement_score": 3477.0, "source_subreddit": "news", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hincvr", "title": "Tesla Recalls ~700k cars for Faulty Tire Pressure Monitoring Sensors", "content": "", "author": "<PERSON><PERSON>n", "created_time": "2024-12-20T16:20:30", "url": "https://reddit.com/r/electricvehicles/comments/1hincvr/tesla_recalls_700k_cars_for_faulty_tire_pressure/", "upvotes": 17, "comments_count": 86, "sentiment": "bullish", "engagement_score": 189.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hio7tn", "title": "Goodbye Chrome, it was fun knowing you. But your time has come.", "content": "Internet without Ublock is just unwatchable. The ads are scummy, scammy, everywhere and now that's it's no longer supported I have no reason to stay. Have a good one folks!", "author": "Turbulent-Economy198", "created_time": "2024-12-20T16:59:06", "url": "https://reddit.com/r/chrome/comments/1hio7tn/goodbye_chrome_it_was_fun_knowing_you_but_your/", "upvotes": 806, "comments_count": 179, "sentiment": "bullish", "engagement_score": 1164.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hip9ce", "title": "The data in this preview has been truncated.", "content": "https://preview.redd.it/j6cdhz78i18e1.png?width=1919&format=png&auto=webp&s=ed337ab37aadf89809392d417ae715d216903fde\n\nHi guys, have been battling with this for a week now, please how can I resolve this issues?  \nI tried with ChatGPT, everything it said I did, yet it won't work. Please help.", "author": "Beautiful_Ride_4432", "created_time": "2024-12-20T17:44:06", "url": "https://reddit.com/r/analytics/comments/1hip9ce/the_data_in_this_preview_has_been_truncated/", "upvotes": 1, "comments_count": 21, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hipg3l", "title": "Thank you to the Tesla team for continuing to push our mission forward & make Tesla successful!\n\nYour hard work in building a fully sustainable & exciting future makes all the difference", "content": "", "author": "deleted", "created_time": "2024-12-20T17:52:05", "url": "https://reddit.com/r/teslamotors/comments/1hipg3l/thank_you_to_the_tesla_team_for_continuing_to/", "upvotes": 211, "comments_count": 65, "sentiment": "neutral", "engagement_score": 341.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiq21y", "title": "Every Monday morning I invest $1,885. Here is how I divvy it up, can y'all provide feedback/opinions on my investing strategy please?", "content": "I'm an early 30s single guy with a solid income. I invest $1885.00 every week which amounts to roughly half of my take home pay, and I live off the other half.\n\nI live in a very high cost of living city and I rent an apartment. I've got a very long time horizon so I'm trying to be aggressive.\n\nHere is how I divvy my money:\n\nStock | Amount Invested | Percentage of investment\n:--|:--:|--:\nS&P 500 ETF (VOO) | $700 | 37.14%\nNASDAQ 100 ETF (QQQM) | $400 | 21.22%\nMAG 6 (no Tesla) | $180 ($30 each) | 9.55%\nSmall Caps (AVUV) | $120 | 6.37%\nBitcoin ETF (IBIT) | $100 | 5.30%\nFinancial Sector ETFs (XLF and IAI) * | $80 ($40 each) | 4.244%\nSemiconductors ETF (SMH) | $80 | 4.244%\nIndividual Stock Picks I like (15 in total, listed below) ** | $225 ($15 each) | 11.94%\n**TOTAL** | $1885 | 100%\n\n*The difference between XLF and IAI is that XLF is mostly financial services such as JP Morgan Visa, Mastercard, Bank of America, Wells Fargo etc..., whereas IAI is mostly brokers and securities dealers such as Goldman Sachs, Morgan Stanley, Schwab, S&P Global etc... There is only a 15% overlap between these two ETFs.\n\n**The 15 companies I like that I invest $15 each into are: Adobe (ADBE), AMD (AMD), Broadcom (AVGO), Chipotle (CMG), Salesforce (CRM), Door Dash (DASH), Netflix (NFLX), ServiceNow (NOW), Palo Alto Networks (PANW), Palantir (PLTR), Shopify (SHOP), Sofi Bank (SOFI), Uber (UBER)\n\n---\n\nI think at first glance, some will say that there is a little redundancy here, such as investing in the MAG 6 companies individually while also investing the majority of my money in VOO and QQQ which both contain the Mag 6 as their largest holdings each. This is a fair criticism, although I do this intentionally because I want more of a slant towards these companies so I get higher exposure by also investing in them individually, in addition to the exposure I get from the index funds.\n\nI'm open to feedback from others. I'm trying to be aggressive in this portfolio which is why I invest in the Mag 6 and the 15 companies I like, while also trying to have some degree of diversification by putting 58.36% of my money in the S&P and Nasdaq, getting small cap exposure, exposure to the financial sector so that I am not exclusively in tech, and including bitcoin which is an entirely different asset class than equities (even though technically this is a stock that tracks the price of bitcoin, it is 100% linked to bitcoin).\n\nPlease provide any thoughts or constructive criticism below. Thank you", "author": "22<PERSON><PERSON><PERSON>", "created_time": "2024-12-20T18:18:36", "url": "https://reddit.com/r/investing/comments/1hiq21y/every_monday_morning_i_invest_1885_here_is_how_i/", "upvotes": 4, "comments_count": 58, "sentiment": "bullish", "engagement_score": 120.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiq3tz", "title": "[D] OpenAI o3 87.5% High Score on ARC Prize Challenge", "content": "https://arcprize.org/blog/oai-o3-pub-breakthrough\n\n>OpenAI's new o3 system - trained on the ARC-AGI-1 Public Training set - has scored a breakthrough 75.7% on the Semi-Private Evaluation set at our stated public leaderboard $10k compute limit. A high-compute (172x) o3 configuration scored 87.5%.", "author": "currentscurrents", "created_time": "2024-12-20T18:20:47", "url": "https://reddit.com/r/MachineLearning/comments/1hiq3tz/d_openai_o3_875_high_score_on_arc_prize_challenge/", "upvotes": 278, "comments_count": 195, "sentiment": "neutral", "engagement_score": 668.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiqesy", "title": "AGI was achieved today", "content": "", "author": "LawBringer007", "created_time": "2024-12-20T18:34:23", "url": "https://reddit.com/r/singularity/comments/1hiqesy/agi_was_achieved_today/", "upvotes": 1, "comments_count": 5, "sentiment": "neutral", "engagement_score": 11.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hirdz2", "title": "Starting a web design business in 2025 ", "content": "So, roughly 10 years ago, a schoolmate told me that he and his brother were making bank creating websites for businesses. I mean, they must be rich by now, 10 years later. He told us that when everybody was partying, chilling and having fun, he was at home learning how to create websites and build his business. He also said that the only reason he went to school was because his mother wanted it. \n\nI thought at the time that it was out of my reach to learn to create webshops without school. It seemed so hard to do. At that time, I didn't know that WordPress had such a low learning curve. I thought that I needed to learn coding and things like that.\n\nI was always interested in it tough, always been a tech type of guy, but I never made that first move. Now, 10 years later, I regret not doing it. But is it still even possible to make a living making websites these days?", "author": "deleted", "created_time": "2024-12-20T19:18:26", "url": "https://reddit.com/r/business/comments/1hirdz2/starting_a_web_design_business_in_2025/", "upvotes": 0, "comments_count": 19, "sentiment": "neutral", "engagement_score": 38.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiugwh", "title": "3 penny stocks that might fck around and hit a 10x in the new year (nfa) - Stocksy's Weekly DD", "content": "Whats up everyone! Here are some notes on some of the companies that I have been paying attention to this week. Had to throw in $MMA.V since the Zambian gov finally approved their license, the company hasn’t even reported on it yet lol. $NCI.V one I have posted about in the past, it’s been really climbing recently. NICU is one I am pretty bullish on for the new year. This is all NFA, I am a random dude on reddit. Also, feel free to comment any tickers you would like me to checkout/review! Cheers\n\n  \n  \n\n\n**Midnight Sun Mining Corp. $MMA.**\n\nMarket cap: 88M\n\nCompany overview:\n\nMidnight Sun Mining is a junior exploration company focused on copper in Zambia’s copper belt, an area known for some of the world’s largest copper deposits. They hold a 506 km² property with promising targets, including the Solwezi Project, where exploration is advancing. With strong local partnerships and a strategic position in this well-established mining region, Midnight Sun is aiming to define new high-grade copper resources in a highly prospective area.\n\n\n\n**Highlights** \n\n\n\nMidnight Sun just received a looooong-awaited confirmation from the Zambian government that their exploration license for the Kazhiba target has been approved. This resolves months of uncertainty and clears the path for advancing one of their most promising oxide copper zones. With this approval, the company’s entire 506 km² Solwezi property is secured, allowing them to ramp up exploration across their four key targets: Dumbwa, Mitu, Kazhiba, and Crunch.\n\n\n\nKazhiba is especially critical because it’s part of a Cooperative Exploration Plan with First Quantum Minerals. This zone could provide near-term oxide copper feed to First Quantum’s Kansanshi Mine, located less than 10 km away. Kansanshi is Africa’s largest copper mine, and First Quantum has a pressing need for oxide copper to neutralize the sulphuric acid generated by their sulphide milling operations. High-grade results already confirmed at Kazhiba (like 14.2 meters at 5.71% Cu and 24 meters at 3.15% Cu) make this a huge opportunity.\n\n\n\n A supply deal with First Quantum could generate $40M-$60M annually for Midnight Sun, representing a massive win for a company with a market cap of just $80M. The potential is even more likely because of the strategic proximity of the assets: a direct highway connects Kazhiba to Kansanshi, meaning Midnight Sun could quickly capitalize on this opportunity.\n\n\n\nMan there is so much to unpack with this one… stay with me..\n\n\n\nAnother huge catalyst for Midnight Sun is the partnership with KoBold Metals, a cutting-edge exploration company backed by names like Bill Gates, Jack Ma, and Richard Branson. KoBold uses AI and machine learning to analyze geoscience data, making exploration faster and more efficient. Their team includes top-tier geologists like Dr. David Broughton, who led the discovery of world-class projects like Kamoa-Kakula in the Congo. KoBold signed a $15.5M earn-in agreement for the Dumbwa target, a Tier-One exploration zone that features a massive 20 km by 1 km copper-in-soil anomaly with peak values of 0.73% Cu.\n\n\n\nKoBold’s team believes Dumbwa has the potential to rival, or even exceed, Barrick’s nearby Lumwana Mine (960Mt at 0.55% Cu), a major copper operation. Under the agreement, KoBold will cover all exploration costs for Dumbwa, and Midnight Sun will retain 25% of the asset. Importantly, KoBold will also pay Midnight Sun $500,000 annually for four years, giving the company non-dilutive cash flow to explore its other high-priority targets, like Kazhiba and Mitu. This structure means Midnight Sun takes on zero financial risk while leveraging one of the best exploration teams in the industry to unlock Dumbwa’s value.\n\n\n\nWith the Zambian license now approved, I’m expecting a busy Q1 for Midnight Sun. Tons of news coming \n\n  \n  \n\n\n**NTG Clarity Networks Inc. $NYWKF $NCI.V**\n\n\n\nMarket cap: 65M (up 80% since first post)\n\n\n\nNTG Clarity Networks provides telecom and IT solutions, specializing in software development and network management. Their primary market is the Middle East, where they’ve been gaining momentum thanks to large-scale investments in digital infrastructure, particularly in Saudi Arabia. With a strong focus on enterprise clients, NTG has become a go-to partner for companies looking to modernize and optimize their operations.\n\n\n\n**Highlights**\n\n\n\nNTG Clarity Networks has been on an impressive run this year, and for good reason. Their Q3 2024 results showed $12.5M in revenue, up 109% from last year, with $2.1M in net income. That’s their eighth straight record-breaking quarter, which really speaks to how well they’ve positioned themselves in the Middle East’s booming digital transformation market. \n\n\n\nThe big story here is their ability to land massive, multi-year contracts. Their $53M deal earlier this year was a game-changer, and with over $70M in backlog right now, they’ve got a lot of work lined up. What stands out to me is how focused they are on Saudi Arabia. The Vision 2030 plan is driving a huge push for digital infrastructure in the region, and NTG has tapped into that perfectly. This isn’t just about them winning contracts, it’s about being in the right place at the right time with the right solutions.\n\n\n\nWhat I also like about NTG is their efficiency. Their offshore campus in Egypt has been key to keeping costs down while scaling up. They’ve got over 950 people working across the globe, and their ability to deliver high-quality solutions at a competitive price is why they’ve been able to keep those margins up, even as they grow.\n\n\n\nLooking forward, I think NTG is set up for a very strong 2025. They’ve got a healthy mix of new business and renewals, which shows their offerings are sticking with clients. With a backlog this size and strong execution, I wouldn’t be surprised to see more contract announcements soon. Insider ownership is also worth noting (46% insider ownership).\n\nThis is one I was talking about back in June when the stock was sitting around $0.85. No complaints about management, they have been making good progress in fixing up the balance sheet over the past few quarters and they continue to rake in solid contracts. NFA but as mentioned I think NTG will have an amazing 2025.\n\n  \n  \n  \n\n\n**Magna Mining Inc. $MGMNF $NICU.V**\n\n\n\nMarket Cap: $276M\n\n\n\nCompany Overview\n\n\n\nMagna Mining is a Canadian base metals company focused on nickel, copper, and PGM projects in the Sudbury Basin. With the advanced-stage Crean Hill project and the operating McCreedy West mine, Magna is working to build a portfolio of cash-generating assets while advancing its development pipeline.\n\n\n\n**Highlights:**\n\n\n\nMagna Mining is entering a transformative phase with its recent acquisition of multiple Sudbury assets from KGHM, including the producing McCreedy West Mine and several other properties with untapped potential. These acquisitions align with the company's vision of becoming a mid-tier producer of nickel and copper.\n\n\n\nThe Crean Hill Project remains the cornerstone of Magna’s strategy. The recently updated PEA (November 2024) outlines a 13-year mine life with an after-tax NPV of $194.1M and an ultra-quick payback period of 1.5 years. Crean Hill is already generating cash flow, with bulk sampling contributing $1.28M. This de-risks the project a ton while exploration efforts aim to expand its resource base further.\n\n\n\nOn top of that, the Crean Hill resource includes a mix of nickel, copper, and precious metals like platinum and palladium, making it a versatile asset that aligns with global decarbonization trends. It is also conveniently located near Sudbury’s established smelters, which reduces costs and timelines for processing.\n\n\n\nThe McCreedy West Mine, part of the KGHM acquisition, is another standout. With over 9M tonnes of high-grade resources (1.30% copper and 0.89% nickel), McCreedy West has been producing recently and offers immediate cash flow potential. Plans are underway to optimize production by late 2025, with improvements to grades and output expected.\n\n\n\nThe Shakespeare Project adds another layer of optionality. While development is on hold, the project is fully permitted for a 4,500-tonne-per-day operation. Recent exploration in the Southwest Copper Zone (32.4m of 1.4% copper, including 13.9m at 2.3%) showcases its long-term value and upside.\n\n\n\nMagna’s management team, many of whom have extensive experience in the Sudbury Basin, continues to demonstrate operational expertise. Their ability to secure processing agreements with majors like Vale and Glencore reduces barriers to production and underscores the company’s strategic focus.\n\n\n\nReally bullish on Magna’s drill targets and looking forward to hearing more about some of their new KGHM properties in the new year!\n\n", "author": "Stocksy1234", "created_time": "2024-12-20T21:38:56", "url": "https://reddit.com/r/pennystocks/comments/1hiugwh/3_penny_stocks_that_might_fck_around_and_hit_a/", "upvotes": 136, "comments_count": 72, "sentiment": "bullish", "engagement_score": 280.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiun03", "title": "Why only Google has a low P/E? ", "content": "i don't get it.  \n  \nWhy is google with all it's **profitability** and **exemplar capital allocation** the only tech giant that has a **low P/E**, and consistently kept it low through the years as it grew it's top line an average of 14%/y??  \n  \nAm I missing something? was the market never efficient? should we divest from Index funds?", "author": "Savings-Judge-6696", "created_time": "2024-12-20T21:46:49", "url": "https://reddit.com/r/investing/comments/1hiun03/why_only_google_has_a_low_pe/", "upvotes": 103, "comments_count": 107, "sentiment": "bullish", "engagement_score": 317.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiuoi3", "title": "Why Google is the only Mag7 with reasonable P/E?", "content": "i don't get it.  \n  \nWhy is google with all it's **profitability** and **exemplar capital allocation** the only tech giant that has a **low P/E**, and consistently kept it low through the years as it grew it's top line an average of 14%/y??  \n  \nAm I missing something? was the market never efficient? should we divest from Index funds?", "author": "Savings-Judge-6696", "created_time": "2024-12-20T21:48:43", "url": "https://reddit.com/r/stocks/comments/1hiuoi3/why_google_is_the_only_mag7_with_reasonable_pe/", "upvotes": 570, "comments_count": 298, "sentiment": "bullish", "engagement_score": 1166.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hiw8md", "title": "Google Updates are complete BS", "content": "Let's talk about Google updates and why they are complete BS. As someone who has been in the search / internet marketing industry since 2008 the money grab and the greed that we now see from Google after covid times are insane. Google keeps trying to peddle this whole \"create quality content\" and \"look at us, we are fighting spam with spam updates and HCU\", and what do we see in the end? Site owners that pour their heart into their sites get hit, while BS AI spam, useless AI overviews that are so fking inaccurate, and parasite SEO strives. \n\nu/Google, honestly, WTF are you doing? What type of morons work there? eCommerce results are complete garbage with repetitive results from brand sites, which, in most cases, don't even carry the product. Forbes is now apparently a review site, and they are so good at reviewing everything, from printers to plumbers, movers, and service-based businesses, Google absolutely loves them.  \n\n\nSh\\*t like seoinla . com has been ranking for keywords like \"SEO Los Angeles\" for over a year and passed every update, it must be great content and user experience, and they definitely fulfill user intent. Links like goo. gl/ maps/ KWrg4qPqKEN9mMer5 are now also ranking instead of the actual websites.\n\nHacked redirect spam rankings are at an all-time high, and that's probably the funniest thing to me. Imagine you create 100 good quality articles, it takes Google fking for EVER to index those. But as soon as your WP site gets hacked with Chinese redirect spam, OMG, Google gets a hard-on and will index those 1000s of pages in 24 hours. Can someone explain that to me? How stupid does your algorithm honestly have to be where it can't differentiate hacked pages vs the actual content? If my site is about T-shirts, and all of a sudden it gets hacked, and now I have 1000's of pages about p..rn and other BS, why algorithm doesn't throw a red flag and says, \"Wait a second, this site had only 100 pages yesterday about T-shirts, today they have 10,000 adult pages, something doesn't add up\", but instead, Google is like \"f\\*k yeah, let's index those 10k pages and rank them for xxx videos, that seems legit\". \n\nu/google is losing it, well actually, lost it. They turned into a BS search engine that no longer puts users first, it's now more about how much money they can squeeze out of business owners and fill their pockets. They completely obliterated content publishers which they used to gain their dominance in the first place.\n\nGoogle ads are also a complete sh\\*t where the CPC and CPL are so high now that it's not even worth running those ads anymore. \n\nSo dear u/google, go F yourself, since you are now completely useless to 90% of the publishers / small business owners.\n\nAnd yes, the only way to do SEO now is to churn and burn AI spamming and blackhat since the dumbass Google algorithm can't differentiate between quality content and garbage.\n\n  \n", "author": "az425", "created_time": "2024-12-20T23:03:38", "url": "https://reddit.com/r/SEO/comments/1hiw8md/google_updates_are_complete_bs/", "upvotes": 134, "comments_count": 68, "sentiment": "neutral", "engagement_score": 270.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "TSLA"}]