[{"platform": "reddit", "post_id": "reddit_1i0sqep", "title": "Elon Musk misrepresents data that shows Tesla is still years away from unsupervised self-driving", "content": "", "author": "walky<PERSON><PERSON><PERSON>", "created_time": "2025-01-14T00:04:03", "url": "https://reddit.com/r/SelfDrivingCars/comments/1i0sqep/elon_musk_misrepresents_data_that_shows_tesla_is/", "upvotes": 848, "comments_count": 476, "sentiment": "neutral", "engagement_score": 1800.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i13opp", "title": "I'm trying to start a software company, but Google won't let me in", "content": "Last month they closed my Google Play account with 9 apps. I think it was because I was competing with some publishers. None of my apps were deleted and I didn't get a warning. Google is really laundering money from this business. I am sure it has happened to many people here. Now we need to unite as developers. I won't stop until this is solved. What do you suggest? The best I could do was open an X post. I've listed some fair rules for the Play Store. ", "author": "bugrevealingbme", "created_time": "2025-01-14T11:04:31", "url": "https://reddit.com/r/startups/comments/1i13opp/im_trying_to_start_a_software_company_but_google/", "upvotes": 0, "comments_count": 12, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i1540d", "title": "<PERSON><PERSON><PERSON>’s a story of a loosing strategy", "content": "I’ve been saying for a while that her strategy of chasing hype is unsustainable, but my friends used to tell me that I didn’t understand anything. Now, it seems the hype has finally caught up with her. The once-promising bets are no longer delivering returns; instead, they’re piling up losses.\n\nWhile it’s unfortunate that she managed to attract so many people who ended up losing money in her fund, I believe this serves as a valuable lesson for everyone.\n\nThis lesson feels especially relevant now, as the market, in my opinion, is fairly overvalued and bears some resemblance to her portfolio—not as extreme, but still concerning.\n", "author": "ClapYourHaands", "created_time": "2025-01-14T12:37:30", "url": "https://reddit.com/r/ValueInvesting/comments/1i1540d/cathie_woods_a_story_of_a_loosing_strategy/", "upvotes": 8, "comments_count": 67, "sentiment": "bearish", "engagement_score": 142.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i173hd", "title": "15 years as a web-dev. Only just found out about this today.", "content": "", "author": "LordS<PERSON>uts", "created_time": "2025-01-14T14:21:04", "url": "https://reddit.com/r/webdev/comments/1i173hd/15_years_as_a_webdev_only_just_found_out_about/", "upvotes": 10072, "comments_count": 347, "sentiment": "neutral", "engagement_score": 10766.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i196lv", "title": "Frustrated as a Data Analyst: Are we just storytellers?", "content": "I’ve worked in five different roles in the data field, and across most companies, I’ve noticed a common trend: data analysts are primarily tasked with producing dashboards or generating figures based on very specific business requests. However, when it comes to tackling broader, more open-ended questions, things seem to get more challenging—especially in companies where Python isn’t part of the toolkit.\n\nIn my current company, for example, we’re expected to find new insights regularly, but everything is done using SQL and Tableau. While these tools are fine for certain tasks, doing deeper data exploration with them can feel tedious and limiting. We’re also not encouraged to use statistical knowledge at all, since no one on the team, including our boss, has a statistical background. It feels like there’s no understanding or value placed on applying more advanced techniques. We just need to have exceptional data storytelling skills + put up some nice figures which confirm already known intuitions.\n\nHonestly, I’m feeling a bit frustrated. I can’t help but wonder if this is common across the field or if it’s just the nature of certain industries or companies. Would things be different in a more tech-focused company or in a dedicated data science role?\n\nWhat’s your experience with this? Is this a frequent issue in your work as well, or does it vary depending on the company or team? I’d love to hear your thoughts.", "author": "Mountain_Sky_2419", "created_time": "2025-01-14T15:56:27", "url": "https://reddit.com/r/analytics/comments/1i196lv/frustrated_as_a_data_analyst_are_we_just/", "upvotes": 179, "comments_count": 96, "sentiment": "neutral", "engagement_score": 371.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i1cadi", "title": "To pay it off or to not pay it off", "content": "2022 Tesla Y bought brand new. ~$65,000 loan after taxes and down payment. $994 payment $41,500 left on loan 3.55%\n\nGoing to make about $140,000-$160,000this year, Wife makes $110,000. Have about $80,000 in cash/ HYSA/ money market/ crypto. No other debts aside from mortgage 6.125% that's $3,100ish that I pay $2,000 extra principal on per month to blast that down as fast as possible.\n\nHoping to have a kid late this year/ early next year that I'll be taking ~ a year off work to take care of and want saving to ride us through that time off. Also making a master bed+ bath that'll be ~$30,000 this spring/summer.\n\nDo I pay the car off and save that grand a month or do I save $12,000 to use as the guaranteed payments for the time I'm off? ", "author": "NordicCell", "created_time": "2025-01-14T18:07:42", "url": "https://reddit.com/r/FinancialPlanning/comments/1i1cadi/to_pay_it_off_or_to_not_pay_it_off/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i1ez2z", "title": "I made 268k at 24,  desperately need financial advice ", "content": "In my early 20s and made 268k last year (before taxes) and am in dire need of financial planning advice. \n\nI would consider myself financially illiterate and would love for you all to take me under your wing and educate me on how to plan and invest for my future, hopefully accumulating wealth over time & early retirement.\n\nBackstory:\n•extremely blessed to live at-home\n•No credit card debt\n•No student loan debt\n•has a 50k auto loan\n (APR 2%, pay off within 72 months)\n•currently in grad school \n(paying out-of-pocket through payment plans, no loan)\n\nI have yet to invest in stocks or bonds and don’t have much knowledge of what to do or how to start. Previous year before this one, I made over 100k. In the past 2 years I’ve been working, I’ve saved about half of the money (from combined income) into a high-yield savings account. \n\nAll advice is welcomed. Thank you all in advance!", "author": "WeakCommercial5618", "created_time": "2025-01-14T19:59:54", "url": "https://reddit.com/r/FinancialPlanning/comments/1i1ez2z/i_made_268k_at_24_desperately_need_financial/", "upvotes": 38, "comments_count": 58, "sentiment": "neutral", "engagement_score": 154.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}]