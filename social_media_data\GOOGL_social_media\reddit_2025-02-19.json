[{"platform": "reddit", "post_id": "reddit_1it0mxs", "title": "Google’s new policy tracks all your devices with no opt-out", "content": "", "author": "mWo12", "created_time": "2025-02-19T08:21:53", "url": "https://reddit.com/r/privacy/comments/1it0mxs/googles_new_policy_tracks_all_your_devices_with/", "upvotes": 3419, "comments_count": 343, "sentiment": "neutral", "engagement_score": 4105.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1it7jr8", "title": "uBlock Origin no longer supported by Chrome?", "content": "When I opened up Chrome this morning, I was greeted with \"These extensions were turned off because they're no longer supported.\"\n\nuBlock Origin\n\nI absolutely LOVE this plugin. And honestly, this was bound to happen bc the ad blocker was too good, even blocked YouTube ads.\n\nIs there anyway to get this extension to work again? Install in developer mode?! I'm not familiar with Chrome in that way. Was hoping the r/chrome community had some suggestions.", "author": "sco-go", "created_time": "2025-02-19T15:04:23", "url": "https://reddit.com/r/chrome/comments/1it7jr8/ublock_origin_no_longer_supported_by_chrome/", "upvotes": 218, "comments_count": 203, "sentiment": "bullish", "engagement_score": 624.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1itdfig", "title": "Google is on the Wrong Side of History", "content": "", "author": "SaveDnet-FRed0", "created_time": "2025-02-19T18:59:06", "url": "https://reddit.com/r/technology/comments/1itdfig/google_is_on_the_wrong_side_of_history/", "upvotes": 11630, "comments_count": 279, "sentiment": "neutral", "engagement_score": 12188.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1itesj8", "title": "Ublock origin was just killed off on Chrome, just Switch to another browser.", "content": "May these greedy bastard burn in hell. Everything they do has made the internet worse, to the point that the internet is unusable without an add blocker, all to promote shit I cant even afford or AI slop mobile games. If they are willing to go this far I know that they do not intend to change and at this point I don't really care.\n\nI installed firefox and took about an hour to set everything up even better than I had it on chrome. Even if i could not do that I would sooner pay for a premium addblocker than give Google/Youtube any money. ", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-19T19:52:15", "url": "https://reddit.com/r/chrome/comments/1itesj8/ublock_origin_was_just_killed_off_on_chrome_just/", "upvotes": 521, "comments_count": 234, "sentiment": "neutral", "engagement_score": 989.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}]