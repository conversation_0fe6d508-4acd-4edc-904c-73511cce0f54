[{"platform": "reddit", "post_id": "reddit_1haw3pb", "title": "Google steps in after McDonald's gets ‘review bombed’ over arrest in UnitedHealth CEO's murder", "content": "", "author": "wizardofthefuture", "created_time": "2024-12-10T07:01:22", "url": "https://reddit.com/r/technology/comments/1haw3pb/google_steps_in_after_mcdon<PERSON><PERSON>_gets_review/", "upvotes": 29906, "comments_count": 2088, "sentiment": "neutral", "engagement_score": 34082.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hay4cf", "title": "Honest Government Ad | AI", "content": "", "author": "RichyScrapDad99", "created_time": "2024-12-10T09:34:36", "url": "https://reddit.com/r/singularity/comments/1hay4cf/honest_government_ad_ai/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hb0m51", "title": "How Does Google Know Who Will Convert?", "content": "There is little doubt that Google conversion based bid strategies are good at what they say they do.  Getting conversions is what they do well, but how do they do it?\n\nRetargeting previous site visitors is an easy win. Someone who has visited your website five times is more likely to convert than someone who is on their first visit. So, the algorithm bids higher for these—that makes sense. However, what about websites that convert on their first visit?\n\nIf it's not about the number of website visits, other data must be used. If the buyers convert on the first visit, you need a high bid to win the click over competitors. This will also put the ad in a high position. But when running target impression share absolute top, the conversion rate is much lower compared to tROAS/tCPA.  This is comparing the same keywords and ads getting the same number of clicks.\n\nSo, it's not about ad position, number of site visits, or bid. None of these factors contribute to a higher conversion rate. The only other data is the users' profile, e.g. age, sex, job, location, device, audience group, plus whatever else Google knows about the user. \n\nIs it this black box of information that now makes the difference, and it's not possible to compete with this with manual campaigns?", "author": "Different-Goose-8367", "created_time": "2024-12-10T12:26:52", "url": "https://reddit.com/r/PPC/comments/1hb0m51/how_does_google_know_who_will_convert/", "upvotes": 27, "comments_count": 81, "sentiment": "bullish", "engagement_score": 189.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hb0ucc", "title": "Google Ads repeated request for verification.", "content": "Hey, all.   :-)\n\n  \nSo, back in October, I got a request to verify my Google Ads. Did it, got the confirmation email that I was verified. Done & done, right?\n\n  \nWell, I've just bumbled my way through a way more detailed verification process after receiving a mail  a handful of hours ago that I only have 20 days left to do it or my ads would be paused.\n\n  \nAny reason for this? Is this some type of an error or just a last minute change to the process. For info's sake, I'm an American who uses the English interface, but my business is in Germany.\n\n  \nThanks for any help in advance.  :-)\n\n  \n", "author": "synfoola", "created_time": "2024-12-10T12:39:52", "url": "https://reddit.com/r/adwords/comments/1hb0ucc/google_ads_repeated_request_for_verification/", "upvotes": 1, "comments_count": 4, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hb2a1o", "title": "$KULR Xero Vibe Solution Launches on NVIDIA Jetson Edge AI Platform", "content": "HOUSTON, Dec. 10, 2024 (GLOBE NEWSWIRE) -- [**KULR Technology Group, Inc.**](https://www.globenewswire.com/Tracker?data=IRoPF3LyS4j5JzNkWgxPQ0HEvBtyjfxqhmv-ytzz-lpjNuV7EYl-GK7QqN28mXtn2ft1wt2PxSsTdsevZUdoHdI-pMB6augAj6oeNmxMCJg=) (NYSE American: KULR) (the \"Company\" or \"KULR\"), a global leader in energy management and vibration reduction solutions, today announced the launch of its innovative KULR Xero Vibe™ (“KXV”) solution integrated with the [NVIDIA Jetson edge AI platform](https://www.globenewswire.com/Tracker?data=jrFjhoqibUg_JCJMJ_GTir46fZrvAmSXAASEC0MQhRurD7gWHaa6nstPAAA2maqLA-0dQ18_cUvzY6Pbp1IVro6gLGjGP3KP4KFvjhNcjmP13is6jNov3S0JzttKkgo9MwFqf8IOA8eWgwrVxxIQVc_jSZiSFjQndGOSppaGmvZFqO6TeDrmK-WTRCySeZxs). This new rollout combines superior vibration mitigation with artificial intelligence capabilities to enable high-performance, reliable operation in edge AI environments.\n\nThe NVIDIA Jetson platform, known for its powerful edge AI computing capabilities, offers unparalleled performance for edge applications such as robotics, autonomous machines, industrial IoT, and smart cities. KULR’s Xero Vibe™ solution complements the Jetson platform by addressing key operational challenges such as vibration suppression, ensuring optimal cooling system performance, reduced energy consumption, and extended mechanical lifespans.\n\nKULR CEO Michael Mo highlighted, \"The Jetson platform is NVIDIA's Industrial AI-at-the-edge solution to connect the physical world to the Omniverse through AI agents for the Industrial Revolution 4.0. It's the perfect platform for KULR to integrate our KXV technology and provide our customers a future proof AI-agent powered energy management edge device solution for data centers, renewable energy, electric mobility and industrial cooling applications. We are very excited to embark on this new era of AI-agent powered future with the NVIDIA platform.\"\n\nThe edge AI market size is [projected](https://www.globenewswire.com/Tracker?data=3hx__9Wze3zc0EK9Nht60bhZ6siILSWI8JbHXp5C_CSoMaDKEIUWz18Bv26E1IcA0cwRUsfUMsncZgQSmloFX00d_qea8ixx4RBhHxedmcs=) to grow from $24.05 billion in 2024 to $356.84 billion by 2035, representing a CAGR of 27.786% during the forecast period 2024-2035.\n\n**Key Features of the KULR Xero Vibe™ Solution:**\n\n* **Advanced Vibration Mitigation**: KULR Xero Vibe™ utilizes proprietary vibration reduction technology to minimize mechanical stress, enhancing the reliability and longevity of AI edge devices.\n* **Seamless AI Integration**: While reducing vibration to virtually zero, KULR Xero Vibe™ is enhanced by NVIDIA Jetson platform’s real-time data processing and machine learning at the edge, unlocking new possibilities for AI-driven operations.\n* **Durability in Harsh Environments**: Designed for rugged and mission-critical use cases, the KXV solution supports operations in extreme conditions, making it ideal for industrial, aerospace, and defense applications.\n\n**Applications Across Industries:**\n\nThe KULR Xero Vibe™ solution unlocks transformative opportunities across various sectors, including:\n\n* **Data Centers**: Enables data center fan cooling systems to run more efficiently and environmentally friendly which lowers operational and capex costs.\n* **Wind-Powered Turbines**: Diminished mechanical breakdown extends system lifespan leading to increased energy efficiency.\n* **Bitcoin**: Lowers energy consumption by generating less noise and reduced mechanical wear and tear in proof-of-work mining applications.\n* **Robotics**: Ensures seamless operation in precision robotics for industrial automation.\n* **Aerospace, Defense, and Electric Aviation**: Enables robust performance in mission-critical applications requiring ruggedized systems.\n\nFor more information about the KULR Xero Vibe™ solution, visit [www.kulrtechnology.com](https://www.globenewswire.com/Tracker?data=dhhecN5Dfx2hJEtQMbVSFss0FsadPHy3NSYLPT9QTcucRsHnhbxAiaxgJfIVPfFc91r6fPorOQFQIVWPdayPJQRtu9ZWfhUBoKWGrI9cy-M=).\n\n**About KULR Technology Group Inc.**  \nKULR Technology Group Inc. (NYSE American: KULR) delivers cutting edge energy storage solutions for space, aerospace, and defense by leveraging a foundation of in-house battery design expertise, comprehensive cell and battery testing suite, and battery fabrication and production capabilities. The Company’s holistic offering allows delivery of commercial-off-the-shelf and custom next generation energy storage systems in rapid timelines for a fraction of the cost compared to traditional programs. For more information, please visit [www.kulrtechnology.com](https://www.globenewswire.com/Tracker?data=dhhecN5Dfx2hJEtQMbVSFss0FsadPHy3NSYLPT9QTcsiOzWHEvFHHQ1u-BpTm4LBgv-eEdt-KHWEstP4XoX0e3qIp2QWKzypaIK357T7-58=).\n\n**About NVIDIA Jetson**  \nThe NVIDIA Jetson platform is the leading AI-at-the-edge computing platform with over a million developers. It offers high performance, energy efficiency, and scalability, enabling the development of smart devices and systems across diverse industries.\n\nThoughts ?", "author": "GodMyShield777", "created_time": "2024-12-10T13:55:39", "url": "https://reddit.com/r/pennystocks/comments/1hb2a1o/kulr_xero_vibe_solution_launches_on_nvidia_jetson/", "upvotes": 96, "comments_count": 60, "sentiment": "bullish", "engagement_score": 216.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hb39my", "title": "Google shares rise on new quantum chip breakthrough", "content": "\n\nShares of Google (NASDAQ:GOOGL) saw a 4% increase in pre-open trading Tuesday, reflecting investor confidence following the company's unveiling of its new quantum computing chip, Willow<PERSON> <PERSON><PERSON>, CEO of Google, introduced the chip on Monday, highlighting its ability to significantly reduce computational errors and its performance in benchmark tests.\n\nPichai emphasized the chip's potential in practical applications, stating, \"We see <PERSON> as an important step in our journey to build a useful quantum computer with practical applications in areas like drug discovery, fusion energy, battery design + more.\"\n\nThe new chip, according to <PERSON><PERSON><PERSON>, has achieved two major milestones. It can exponentially decrease errors with the addition of more qubits, addressing a quantum error correction challenge that has persisted for nearly three decades. In addition, <PERSON> completed a standard benchmark computation in less than five minutes, a task that would take one of the fastest supercomputers today more than 10 septillion years.\nThis development is part of Google's long-term commitment to quantum computing, a vision that began over a decade ago when <PERSON><PERSON><PERSON> founded Google Quantum (NASDAQ:QMCO) AI in 2012. \n\nThe goal is to build a quantum computer that leverages quantum mechanics to advance scientific discovery and address societal challenges.\n\nThe announcement drew a simple yet telling reaction from Tesla (NASDAQ:TSLA) CEO <PERSON><PERSON>, who expressed his astonishment with a single word, \"wow,\" on social media platform X.\n\nThe Willow chip represents a significant advancement for Google Quantum AI and positions the company closer to achieving commercially viable quantum computing applications.", "author": "Guy_PCS", "created_time": "2024-12-10T14:42:37", "url": "https://reddit.com/r/stocks/comments/1hb39my/google_shares_rise_on_new_quantum_chip/", "upvotes": 497, "comments_count": 186, "sentiment": "bullish", "engagement_score": 869.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hb3mzn", "title": "I employ 40 people, here's my top 5 pieces of advice for business owners", "content": "I employ 40 people across two different businesses. These range from mostly employees in my painting business, to about a 50% split between employees and contractors in my SAAS...\n\nAs a leader of so many with very little upper management leadership (I am the only upper management in my painting business) I rely heavily on people making sound decisions without the need for constant input from me. I host very little meetings and collaborate mostly via slack.\n\nHere's my top 5 pieces of advice for new business owners when it comes to management and leadership.\n\n1. **Create space for people to make mistakes** \\- in my painting business, there's plenty of mistakes that happen, of course, more so in my painting business than SAAS specifically because we are working people's homes.\n\nI rarely dwell on mistakes, instead, I've created a culture of solution driven crew leaders that if they call me, they're already prepared with a few solutions - at that point, my only job is to help guide them on which one makes the most sense from my perspective (even though i'm not on the job)\n\nDoing this overtime, I've probably limited the \"what do I do\" texts, calls, and messages by 70% because my team members know they're not going to lose their job if a mistake happens.\n\n2. **Stop outsourcing leaders at early stages** \\- I tried to hire in \"managers\" for certain roles, and although they have their place, for me, my experience is to give people an opportunity to manage. I know in some sectors it requires experience, but for me, I like to leverage loyalty. I think loyalty drives growth more than anything and the best way to garnish loyalty is to give opportunity to people.\n\nIn my painting business, all 4 of my crew leaders have never managed anything in their lives - however, I saw something in them when they were painters that prompted me to give them a chance at managing.\n\nFrom there, two of those crew leaders are now project managers.\n\nThis fosters a culture of growth that allows my team to see that growth is achievable. Honestly, if I were to bring someone in to manage a crew it would probably hurt morale.\n\n3. **It's Cliche, but hire for character, train for skill.**\n\nOn average, my customer success applications for my SAAS get 450 applications on LinkedIn within the first 3 days - it's literally impossible to filter these applicants with the linked in tools, so we funnel them into an internal form - the form is built entirely off of about 80% situational questions that help us see how the applicants will handle tough situations...\n\n\"A customer is threatening to cancel their account because no one answered their request for help on the intercom messenger - how do you handle this?\"\n\nSure, a little experience in customer success helps, but you can really learn a lot by letting people answer these types of questions.\n\n4. **Be accessible - get off your podium**\n\nI used to work for a bank that had about 400 employees - the CEO was this illusive ghost that only showed his face at corporate events. He wasn't a \"team guy\" he was just a guy that made high level decisions - people kind of feared him.\n\nI remember at an awards event, he had an assistant raddle off the awards, and like a puppet he would hand the awards to the winners and shake their hands. He didn't even know the people he was handing awards to. It was awkward.\n\nI vowed to never be that way. I don't care if you are QA or you are a prepper on a paint job. I want to shake your hand, or get to know you, or have a conversation with you - i know at scale this is hard, but in meetings, at the end of a meeting (especially a big one) i always say \"I'm an open book, send me a message, don't be a stranger. Even if it's just to say what's up\"\n\nI don't really know the impact because I don't ask, I just know that if i were an employee it would take the pressure down a notch.\n\n5. **Care about people**\n\nI can't teach you how to care about people. It's an innate thing. Possess empathy. I've had people in some of the most crucial times call out of work because of a life thing that happened. Dog died, family member issue, kid got in trouble at school, mental health day... idk all sorts of things.\n\nThere's no paint emergencies, and there's no SAAS emergencies.\n\nLife happens - everything can be fixed tomorrow.\n\nI always have my team's back, in some situations, i'd even step in to go paint, or fulfill a role in customer success, do a demo, or whatever is required to help the team. Again, i haven't polled people on this, but I will say that if i were an employee, i'd appreciate this\n\nTLDR: 5 things to help you lead a better organization and create a culture of happiness", "author": "Byobcoach", "created_time": "2024-12-10T14:59:28", "url": "https://reddit.com/r/Entrepreneur/comments/1hb3mzn/i_employ_40_people_heres_my_top_5_pieces_of/", "upvotes": 752, "comments_count": 93, "sentiment": "bullish", "engagement_score": 938.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hb6haw", "title": "I made my clients grow, I have good results, but I feel stagnant...", "content": "I was recently analyzing my clients' advertising accounts and checking the results and I noticed very good results. For example, clients who started with me earning less than 1k/day and now earn more than 40k, among other smaller and less eye-catching cases.\n\n\n\nBut the point is, even with all these results, my earnings are not growing. I have tried to renegotiate several times and even so, there is always resistance, especially because of the entire structure of campaigns and audiences that I have already set up, which the client can simply \"fire\" me and continue to maintain them with someone who charges less.\n\n\n\nAnd this is becoming an extremely frustrating situation. To give you an idea, one of my clients currently earns more than 6M/month including website, marketplaces and internal orders and what I receive does not even reach 20k. (These values ​​are in <PERSON><PERSON>, he is from Brazil. If you convert to dollars, this client pays me less than 4k dollars/month).\n\n\n\nI've tried to find new clients on sites like Upwork, Workana and others, but people don't even respond to me.\n\n\n\nI send screenshots of my Google Ads and Facebook Ads Dashboards, showing the results I have in real time and I feel really marginalized, because people think they are fake results.\n\n\n\nI've tried posting videos in groups and the admins don't approve the publication of the videos, even though I show the Dashboards being updated in real time on my monitor, recording directly with my cell phone.\n\n\n\nI haven't done and don't do such extraordinary work, but even so, I feel like I'm treated like a fake all the time or undervalued by the market where I work.\n\n\n\nHow do you deal with this type of situation?\n\n\n\nIn short, I have good results and only my clients grow and I don't. I try to attract new clients and I'm ignored, the same when I try to interact with others in my field.\n\n\n\n\\*P.S: I've tried boosting my Instagram posts with videos from the GA-4 dashboard, Google Ads and Facebook/Meta Ads, and I've gotten zero results. Only people who are skeptical or businesses that are too small and have no growth potential are looking for me.", "author": "Savage_M4chine", "created_time": "2024-12-10T17:02:32", "url": "https://reddit.com/r/PPC/comments/1hb6haw/i_made_my_clients_grow_i_have_good_results_but_i/", "upvotes": 558, "comments_count": 84, "sentiment": "neutral", "engagement_score": 726.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbazh6", "title": "Pixel phones now support bypass charging when set to the 80% charging limit\nThe latest Pixel update helps to keep that battery cool.", "content": "", "author": "veryfocused", "created_time": "2024-12-10T20:10:50", "url": "https://reddit.com/r/Android/comments/1hbazh6/pixel_phones_now_support_bypass_charging_when_set/", "upvotes": 782, "comments_count": 172, "sentiment": "neutral", "engagement_score": 1126.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbblg2", "title": "Help me escape from months of back-and-forth with Google Ads support?", "content": "After 8 weeks of suffering through 50+ Google Ad support emails and at least 10 Google Ad support phone calls, I am desperate for tips on how I might find \\*anyone\\* on the Google Ads team who is human and technical enough to understand why an iframe embed does **not** inherently equal \"compromised\" status. \n\nI've outlined my saga in an [excruciatingly long blog post](https://bill.harding.blog/2024/12/05/google-ads-a-2024-glimpse-into-life-as-a-google-customer/) about how our company has been suspended almost two months now in spite of no wrongdoing. We are currently suspended for what they have labeled a policy violation of \"Compromised site.\" In final section of my blog post (which I'm continuing to amend as new updates follow), [I provide a request-by-request analysis of why the page they flagged is **not** evidence of a compromised site](https://bill.harding.blog/2024/12/05/google-ads-a-2024-glimpse-into-life-as-a-google-customer/#Analyzing_whether_Quick_Start:_Commit_Activity_Browser_is_dangerous). (There is also Google's own Web Console tool, which [confirms that neither our site nor the one we link to is \"compromised\"](https://bill.harding.blog/2024/12/05/google-ads-a-2024-glimpse-into-life-as-a-google-customer#Part_II:_Suspended_for__Compromised_site__(3_weeks))) \n\nI recognize that this isn't Google Ads support, but that's sort of the point of posting here. I've been corresponding with their professional support for months now without finding anyone who can explain why they would brand us a \"compromised site\" when **there is no tool anywhere on the web that substantiates their designation of \"compromised site.\"** \n\nIf some hero on this subreddit can suggest to me a course of action that allows us to finallllllly return to posting ads on Google (as we had w/o issue for 4+ years, until this debacle began in October), I would happily purchase and allocate to them whatever reddit-swag-token-stuff $25 could buy (I am not a reddit expert, can you tell? 😜). \n\nThank you for considering the pathetic plight of an earnest small business owner. 🙏", "author": "wbharding", "created_time": "2024-12-10T20:36:49", "url": "https://reddit.com/r/adwords/comments/1hbblg2/help_me_escape_from_months_of_backandforth_with/", "upvotes": 1, "comments_count": 4, "sentiment": "bullish", "engagement_score": 9.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbdz5s", "title": "$5.. forever? 😏", "content": "👋🏼 Over the past year, I’ve been diving into software development and product management. Most of my projects have been ambitious and complex (read: nowhere near finished), so I decided to tackle something smaller to gain practical experience.\n\nRecently, I needed to organize my finances for an upcoming move. Instead of creating yet another Google Sheet, I thought, Why not build a simple tool for myself? 🙃\n\nWhat began as a quick personal project quickly escalated. In just a few days, I developed a full app, complete with a licensing system and a (barebones) marketing site. It’s been a fun learning journey, and it feels great to have something tangible out there instead of endlessly tinkering.\n\nThe app is straightforward—it’s an offline finance tool that stores data locally and helps plan finances without relying on bank integrations. While it’s not groundbreaking, it serves my needs and avoids the hassle of dealing with miscategorized transactions.\n\nHere’s where I deviated from the norm: I opted for a $5 lifetime license instead of the typical subscription model. I understand that subscriptions are standard in SaaS, and this approach likely won’t make me rich. However, I wanted to experiment with simplicity and see if a one-time price could still attract interest.\n\nSo far, a few sales have come in, boosting my confidence. But I’m curious: Does this kind of pricing make sense for small, low-maintenance tools like this? Or am I missing the mark by not adopting the subscription model?\n\nI’d love to hear your thoughts on this pricing experiment and any similar experiences you’ve had. Thanks for reading!", "author": "brody<PERSON>ie", "created_time": "2024-12-10T22:18:11", "url": "https://reddit.com/r/startups/comments/1hbdz5s/5_forever/", "upvotes": 0, "comments_count": 21, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbf8vi", "title": "Have I underestimated healthcare costs in retirement by focusing on OOP max?", "content": "Up until now, my method for calculating my healthcare costs in retirement was to basically take my premium at my planned income from [the kff calculator](https://www.kff.org/interactive/subsidy-calculator/#state=ga&zip=30328&income-type=percent&income=199&employer-coverage=0&people=1&alternate-plan-family=&adult-count=1&adults%5B0%5D%5Bage%5D=45&adults%5B0%5D%5Btobacco%5D=0&adults%5B1%5D%5Bage%5D=53&adults%5B1%5D%5Btobacco%5D=0&adults%5B2%5D%5Bage%5D=52&adults%5B2%5D%5Btobacco%5D=0&child-count=0&children%5B0%5D%5Bage%5D=19&children%5B0%5D%5Btobacco%5D=0&children%5B1%5D%5Bage%5D=19&children%5B1%5D%5Btobacco%5D=0&children%5B2%5D%5Bage%5D=17&children%5B2%5D%5Btobacco%5D=0), add in the OOP max and simply assume I'll hit that every year.  Simple right?\n\nOnly, I had a health issue earlier this year, and I've had multiple claims denied.  I'd heard that insurance companies were increasingly doing this, but I had no idea how widespread it was until recent events got everyone talking about *their* denials for things that **should** have been covered.\n\nI used to hear that 2/3rds of bankruptcies were related to medical expenses, and I used to think 'they should have had insurance'.  This was before I realized that **most actually have insurance**.\n\nHonestly, as someone with a disability, and higher than average healthcare costs, this is kind of terrifying to me.  I don't know how I'm supposed to have the confidence to FIRE when an insurance company can simply *decide not to pay* and the patient has little recourse.", "author": "alpacaMyToothbrush", "created_time": "2024-12-10T23:15:11", "url": "https://reddit.com/r/financialindependence/comments/1hbf8vi/have_i_underestimated_healthcare_costs_in/", "upvotes": 247, "comments_count": 165, "sentiment": "neutral", "engagement_score": 577.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "GOOGL"}]