[{"platform": "reddit", "post_id": "reddit_1i8kd9a", "title": "any good open source alternative to google alerts?", "content": "I have tried to use google alerts but I find them very bad, and I would like to find one that is free or better that is open source or self-hosted, do you know any?", "author": "r<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-24T01:53:48", "url": "https://reddit.com/r/analytics/comments/1i8kd9a/any_good_open_source_alternative_to_google_alerts/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i8sku7", "title": "UK to investigate Apple and Google", "content": "According to Reuters: \"Britain launched an investigation into Apple and Google's smartphone operating systems, app stores and browsers on Thursday, its second use of recently bulked up regulatory powers to scrutinise big tech companies.\"\n\nChina should ban Apple and Google. As they work with the authorities, in surveillance or manipulating data provided by mobiles. Thus Chinese or other users can be targeted for surveillance or information manipulation.\n\nAs for UK, they should consider raising tariffs on big tech hardware, to develop their own mobiles, and software for mobiles. Better they discover anticompetitive practices and government control of American computer hardware and software, from laptops, to tablets, to smartphones. And take measures to stop their sales, until the problems are solved, or UK develops its own computer platforms, including hardware and software platform.\n\nReference: https://www.reuters.com/technology/uk-investigate-apple-googles-mobile-ecosystems-2025-01-23/\n\nP.S. Downvote me if you support state surveillance and propaganda through computer devices like smartphones or the internet", "author": "fool49", "created_time": "2025-01-24T10:38:02", "url": "https://reddit.com/r/economy/comments/1i8sku7/uk_to_investigate_apple_and_google/", "upvotes": 5, "comments_count": 0, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i8th5p", "title": "Have you heard about DeepSeek, the AI model from China? It’s beating all the models from Open AI, Facebook, Google etc. And it’s open source. It also costs 97% less than the competition.", "content": "", "author": "wakeup2019", "created_time": "2025-01-24T11:40:02", "url": "https://reddit.com/r/economy/comments/1i8th5p/have_you_heard_about_deepseek_the_ai_model_from/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i8w4zo", "title": "Google DeepMind CEO <PERSON><PERSON> says AGI that is robust across all cognitive tasks and can invent its own hypotheses and conjectures about science is 3-5 years away", "content": "", "author": "katxwoods", "created_time": "2025-01-24T14:06:08", "url": "https://reddit.com/r/artificial/comments/1i8w4zo/google_deepmind_ceo_demis_hassabis_says_agi_that/", "upvotes": 86, "comments_count": 92, "sentiment": "neutral", "engagement_score": 270.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i91jjt", "title": "<PERSON> signs executive order on developing artificial intelligence ‘free from ideological bias’", "content": "", "author": "F0urLeafCl0ver", "created_time": "2025-01-24T17:57:33", "url": "https://reddit.com/r/artificial/comments/1i91jjt/trump_signs_executive_order_on_developing/", "upvotes": 770, "comments_count": 395, "sentiment": "neutral", "engagement_score": 1560.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i9920y", "title": "Marketers of SaaS Companies, What is the Biggest Tip You Learned on Succeeding with Google Ads?", "content": "Trying to navigate Google Ads for our relatively smaller SaaS company. Our budget is $5k a month which I know isn't a lot. Some things I'm already doing:\n\n  \n1. I never use Broad Match.\n\n2. I use manual CPC so I have control over how much I spend on each keyword and how well those convert, what the quality of those leads are, etc.\n\n3. My ad copy is super relevant to my ads. I've broken down ad groups into specific topics so my ads are highly relevant to the ad group they serve.\n\n4. We have a branded campaign as well to tackle competitors bidding on our company-name keywords.\n\n5. I turned off Search Partners.\n\n  \nAm I missing something?", "author": "jenna<PERSON><PERSON>", "created_time": "2025-01-24T23:19:54", "url": "https://reddit.com/r/marketing/comments/1i9920y/marketers_of_saas_companies_what_is_the_biggest/", "upvotes": 2, "comments_count": 15, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}]