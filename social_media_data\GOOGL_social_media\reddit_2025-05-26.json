[{"platform": "reddit", "post_id": "reddit_1kvnv1g", "title": "Old Google ad accounts", "content": "Any help in this regard will be appreciated a lot...", "author": "Ethan-Hunt-26", "created_time": "2025-05-26T06:50:52", "url": "https://reddit.com/r/adwords/comments/1kvnv1g/old_google_ad_accounts/", "upvotes": 0, "comments_count": 5, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kvr9r4", "title": "Why did they remove maps from google search?", "content": "This is super annoying when you google an address. You need to actually go into maps first, not just find it via google. I know this changed some time ago but still annoying A-F. \n\nAre they changing it back or is it just to live with it?", "author": "mrbluetrain", "created_time": "2025-05-26T10:42:30", "url": "https://reddit.com/r/GoogleMaps/comments/1kvr9r4/why_did_they_remove_maps_from_google_search/", "upvotes": 17, "comments_count": 13, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kvssu6", "title": "Google Is Burying the Web Alive", "content": "", "author": "Well_Socialized", "created_time": "2025-05-26T12:12:04", "url": "https://reddit.com/r/technology/comments/1kvssu6/google_is_burying_the_web_alive/", "upvotes": 24368, "comments_count": 2485, "sentiment": "neutral", "engagement_score": 29338.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kvv56r", "title": "Can't Access Google.ca Chrome Android", "content": "I've done a bunch of Googling to try to figure this out but I can't determine why Chrome keeps redirecting google.ca to google.com. it's been a couple of weeks.\n\nI've tried uninstalling and reinstalling the app, deleting data and cache, logging out of my Google account and changing my Google search settings from \"current region\" to \"Canada\". Nothing worked.\n\nI can prevent the redirect by using incognito browsing or using another browser like Firefox.\n\nCan anyone help?", "author": "asmokeandpancake", "created_time": "2025-05-26T14:03:12", "url": "https://reddit.com/r/chrome/comments/1kvv56r/cant_access_googleca_chrome_android/", "upvotes": 2, "comments_count": 10, "sentiment": "neutral", "engagement_score": 22.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kvx0sn", "title": "<PERSON>: \"Waymo's had 708,000 paid driverless rides in California in March\"", "content": "", "author": "Straight_Ad2258", "created_time": "2025-05-26T15:20:43", "url": "https://reddit.com/r/SelfDrivingCars/comments/1kvx0sn/nat_bullard_waymos_had_708000_paid_driverless/", "upvotes": 232, "comments_count": 211, "sentiment": "bullish", "engagement_score": 654.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kvxcil", "title": "Searching for startup opportunities on the Google Workspace Marketplace - I will not promote", "content": "Want to share some research I did into Google Workspace Marketplace about a year ago, mainly to prove to myself that it was worth doing the work even though the startup I launched ended up failing.\n\n**What's that?**\n\nGoogle Workspace Marketplace is kinda like the Shopify for Google productivity tools (Gmail, Sheets, Forms, etc) - you can look through a directory, and install an app that extends one/several of the tools. \n\n**Addressable market**\n\nMost users are small businesses looking for cheaper, flexible tools compared to big enterprise software (eg Mailchimp alternative, etc).\n\nGoogle last reported 5 billion installs back in 2021. It's likely way more by now. I actually spoke to a couple of founders, who are making real money here (though they preferred to stay anonymous).\n\n**Data collection/filtering**\n\nThere's 5.3k apps on the marketplace. I wrote a script to scrape them (it's easy), and have found several things to filter out for:\n\n1. apps for educational institutions - tight budgets, hard to get adoption from new crappy startup  \n2. apps that deliver little value - self-explanatory, but there are quite a few out there  \n3. apps for Google Drive / Admin - tend to be for SysAdmins -> unlikely to adopt crappy startup given security risk  \n4. install count - 1+ million installs\n\nSo that's how I filtered it down to about 200 apps.\n\n\\---\n\nThat's all I wrote so far! Obviously the next step is to cover how I did deep-dives into the 200 apps. Let me know if it's useful, or if you have any questions/feedback. ", "author": "ReditusReditai", "created_time": "2025-05-26T15:33:59", "url": "https://reddit.com/r/startups/comments/1kvxcil/searching_for_startup_opportunities_on_the_google/", "upvotes": 6, "comments_count": 5, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kvyfnc", "title": "Haven't heard anyone talk about Google IO's INSANE releases last week.", "content": "Google feels like a sleeping giant in the AI race. \n\nFor an online business or anyone doing digital marketing a lot of these releases (assuming it lives up to it's promise of course) are HUGE game changers. I'm doing a breakdown series of all the releases on [this subreddit](https://www.reddit.com/r/AIBizHub/) (there's a lot to cover), if you want to stay up to date.\n\nYes of course they are all AI related but worth noting to stay ahead of the game. \n\nThey dropped a lot of new releases from their own version of Operator/Manus to video generation now ***WITH SOUND*** and dialogue to ***virtual clothing try-ons*** and that's just the tip of the iceberg. \n\nI feel like a lot more people should be freaking out at the announcements (given it's going to ***massively*** change the way we all do business and jobs) but everyone seems to be sleeping on this news. ", "author": "Swimming_Summer5225", "created_time": "2025-05-26T16:17:43", "url": "https://reddit.com/r/DigitalMarketing/comments/1kvyfnc/havent_heard_anyone_talk_about_google_ios_insane/", "upvotes": 0, "comments_count": 6, "sentiment": "bearish", "engagement_score": 12.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kvz6dm", "title": "Google Maps/Carplay doesn't auto recalculate always", "content": "Toyota RAV4/Carplay/Google Maps/iPhone 16. All with latest software/app/map updates. Recently with the latest Google Maps update route recalculation doesn't always kick in like it used to. For instance I set my route home while in a parking lot or something. I know the route leaving out the south exit of the mall is better, but the default takes me out the north. Usually what would happen is it would figure this out itself as soon as I took the south exit and auto recalculate accordingly, but now about half the time it gets \"stuck\" and doesn't recalculate. I can drive a couple of miles away from the course it has calculated and it stays stuck. The only way I've found to get it to reset is to exit everything - reboot the head unit in the car, force quit Google Maps on the iphone, etc. Once I do that it'll work fine for a while but revert to this behavior.   \n   \nAny ideas?", "author": "barkatmoon303", "created_time": "2025-05-26T16:47:59", "url": "https://reddit.com/r/GoogleMaps/comments/1kvz6dm/google_mapscarplay_doesnt_auto_recalculate_always/", "upvotes": 20, "comments_count": 20, "sentiment": "neutral", "engagement_score": 60.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kw21ul", "title": "Building a tool to make Google analytics (GA4) somewhat easier to use", "content": "I’ve been working on a tool that lets you ask GA4 questions directly in Slack.\n\nYou just install it, connect your GA4 account, then tag it in any channel and ask things like “How many new users did we get last week?” or “Compare mobile vs desktop conversions for our spring promo.”.\n\nIt pulls the data in real time and drops back a quick summary, optionally with chart in the channel (or DM). You don't have to deal with the GA4 dashboard at all.\n\nIt can also handle more complex analysis like *“Show week‑over‑week conversion change for Instagram mobile users”* or *“Flag any sudden traffic spikes by UTM source over the past 30 days.”*\n\nWould you use something like this in your Slack workspace? Would love to hear your thoughts. Thanks!", "author": "prous5tmaker", "created_time": "2025-05-26T18:41:53", "url": "https://reddit.com/r/analytics/comments/1kw21ul/building_a_tool_to_make_google_analytics_ga4/", "upvotes": 11, "comments_count": 4, "sentiment": "bearish", "engagement_score": 19.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kw4kso", "title": "How should I expand my Google Ads account from 2 ad groups to a full $10K/month structure? Need advice.", "content": "I’m managing a Google Ads account for [AdGPT.com](http://AdGPT.com), an AI-powered tool with two main ad groups:\n\n* **AI Ad Generator**\n* **AI Social Media Post Generator**\n\nMy **daily budget is $300** (\\~$10,000/month), and I’m looking to **expand the account intelligently and profitably**. But I’m feeling a bit stuck.\n\n# Here’s what I’m trying to figure out:\n\n1. **Where do I get new ad group ideas from?**\n   * Should I mine search terms from the existing ad groups?\n   * Should I use tools like Google Keyword Planner, Ahrefs, or competitor analysis tools like SpyFu?\n   * Should I look at ChatGPT/SEO trends on forums and social media?\n2. **How should I structure the expansion?**\n   * What’s the best way to scale while maintaining strong Quality Score and CTR?\n   * Should I break campaigns out by funnel stage (awareness vs. buying intent)?\n   * Should I localize campaigns by geography, device, or time of day?\n3. **Any proven frameworks or structures I should follow?**\n   * For example: “1 campaign per intent category, each with tightly themed ad groups and SKAG or STAG-style structure” — is that still recommended?\n   * Should I be running Performance Max too?\n4. **How often should I revisit and expand my structure?**\n\nI’d appreciate hearing from anyone who’s scaled Google Ads from a couple of ad groups to a serious performance engine. Tools, case studies, templates—everything helps.\n\nThanks in advance!", "author": "Maleficent_Mud7141", "created_time": "2025-05-26T20:25:47", "url": "https://reddit.com/r/adwords/comments/1kw4kso/how_should_i_expand_my_google_ads_account_from_2/", "upvotes": 3, "comments_count": 5, "sentiment": "bullish", "engagement_score": 13.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kw86is", "title": "Thanks Google", "content": "", "author": "3d_abraham", "created_time": "2025-05-26T23:01:08", "url": "https://reddit.com/r/wallstreetbets/comments/1kw86is/thanks_google/", "upvotes": 4104, "comments_count": 95, "sentiment": "neutral", "engagement_score": 4294.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}]