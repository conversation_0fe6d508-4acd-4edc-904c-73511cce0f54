[{"platform": "reddit", "post_id": "reddit_1hi7yll", "title": "Goodbye Chrome, was a fun run", "content": "I don't think I searched for another web browser so fast since the last time I installed windows on my computer and trying to download chrome from edge. \n\nhttps://preview.redd.it/x4tuve6xiw7e1.png?width=1147&format=png&auto=webp&s=469f5b59fe533d110b7c26b7624ae6aafeead8d4\n\n  \nI don't think the war between ads and adblockers will stop with uBlock, so before everything goes down, best thing is to move to another place. \n\nWas a fun run! ", "author": "partyuniverse", "created_time": "2024-12-20T00:54:33", "url": "https://reddit.com/r/chrome/comments/1hi7yll/goodbye_chrome_was_a_fun_run/", "upvotes": 219, "comments_count": 63, "sentiment": "neutral", "engagement_score": 345.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hicdom", "title": "Why not just buy Goldman Sachs at 16x earnings? JP Morgan at 12x? GOOGL at 25x META at 28x? LVMH at 22x? AMEX at 21x?", "content": "Essentially the title. Best in class management. I personally see Goldman Sachs, JP Morgan, LVMH etc. being around for another 50-100+ years. Paying a fair price for a great compounder. I am 22 years old and think buying into some of these mega caps at these prices will position me very well.\n\nRight now market seems high, but I am finding so many good mega caps at fair prices such as GS, GOOGL, LVMHF (Louisvuitton conglomorate), META, AMEX. Seems like a good time to build a base.", "author": "Pershing_Circle", "created_time": "2024-12-20T04:59:56", "url": "https://reddit.com/r/ValueInvesting/comments/1hicdom/why_not_just_buy_goldman_sachs_at_16x_earnings_jp/", "upvotes": 199, "comments_count": 166, "sentiment": "bullish", "engagement_score": 531.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hienrk", "title": "Forget Chrome—Google Starts Tracking All Your Devices In 8 Weeks", "content": "", "author": "ka<PERSON>ent", "created_time": "2024-12-20T07:29:59", "url": "https://reddit.com/r/privacy/comments/1hienrk/forget_chromegoogle_starts_tracking_all_your/", "upvotes": 782, "comments_count": 117, "sentiment": "neutral", "engagement_score": 1016.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hii1x8", "title": "Blocking my precious extensions... well, I guess this is goodbye Google Chrome.", "content": "", "author": "0x537", "created_time": "2024-12-20T11:44:46", "url": "https://reddit.com/r/google/comments/1hii1x8/blocking_my_precious_extensions_well_i_guess_this/", "upvotes": 523, "comments_count": 174, "sentiment": "neutral", "engagement_score": 871.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hijfsg", "title": "Easiest way I found to increase revenue by 20%", "content": "Ex-Meta engineer here who spent 5+ years building their ads algorithm. In 2024, I set up server side tracking for 150+ brands this year and the gains in email marketing revenue and ads ROAS were substantial.  I founded [Aimerce](http://aimerce.ai) specifically to help Shopify brands implement clean server-side tracking, and it’s wild how often small backend changes end up driving the biggest revenue lifts.\n\nThis is best for any brand doing more than 10k/month, and leverage email marketing and paid ads (Google, Meta, etc) as their core marketing channels.\n\n**Let me explain why this matters more than ever:**\n\nWhen I was at Meta building the conversion matching system, we discovered something crucial: server-side events were getting weighted significantly higher in our models compared to client-side pixel events. This wasn't just about data reliability – it was about surviving in an increasingly privacy-focused web.\n\nSafari's Intelligent Tracking Prevention (ITP) deletes ALL client-side storage (including your precious Meta pixel data) after just 7 days of user inactivity. Even worse, if users come from Facebook (with those fbclid parameters in the URL), your client-side cookies are limited to just 24 hours. This is why we saw massive drops in performance for clients only using the basic Meta pixel.\n\n**A few critical points before I dive in:**\n\n* This does not require any changes to your creatives, campaign structures or email flows\n* The core problem that needs fixing is a data integrity problem\n* Once this is setup **correctly**, no further maintenance is needed\n* When I say \"data\", I mean server-side signals feeding Meta's algorithm\n* First-party data means both platform engagement AND server-side website data\n\n**What Actually Works: Server-Side Implementation**\n\nThe secret to maintaining accurate tracking isn't sending more data – it's sending smarter data through the right channels. Here's what actually happens behind the scenes:\n\n**1. Direct Server Communication**\n\nWhen your server talks directly to Google Ads Conversion Tracking or Meta's Conversion API (CAPI), you bypass most privacy restrictions because you're not relying on browser storage. This means longer attribution windows and better matching.\n\n2. **Progressive Identity Building**\n\nInstead of relying on a single tracking point, you want to build user identity progressively:\n\n* First visit: Capture basic server-side parameters\n* Email submission: Add hashed email identifier\n* Phone submission: Layer in additional identity data\n* Purchase: Include transaction details\n\nEach step strengthens Meta's ability to match users to ads, improving your ROAS.\n\n**Common Pitfalls I See Daily**\n\n**1. Incorrect Parameter Hashing**\n\nThe number one issue I see is improper hashing of user data. Both Google and Meta require specific hashing formats, and getting this wrong tanks your match rates.\n\n**2.** **Poor Timing Implementation**\n\nYour server needs to send events in real-time. I've seen companies batch these events and send them hours later – this destroys the temporal connection between user actions and ad interactions.\n\n**3. Missing Deduplication**\n\nIf you're running both server and client events, you need proper deduplication or you'll mess up your attribution data. This is probably the most requested topic from my last post, so let me break down deduplication properly:\n\nI've seen this go wrong in so many audits at Aimerce: duplicate events, mismatched action sources, or missing Event IDs silently killing attribution. When running both server-side and client-side tracking, you need to prevent duplicate events or you'll mess up your reporting. Here's the proven approach I've implemented across hundreds of accounts:\n\n1. Event ID Management: Generate a unique ID for each event on your server. Pass this same ID to both your client-side pixel and server-side CAPI calls. Meta will automatically deduplicate events with matching IDs.\n2. Event Sources: Always set the 'action\\_source' parameter correctly, use 'website' for client-side events, use 'system\\_generated' for server-side events. Meta uses this to determine event priority when deduplicating.\n3. Timing Window: Server events should be sent within 7 seconds of the client event. This helps Meta's system confidently match and deduplicate them. If you're sending events later, you risk double-counting or missing attributions entirely.\n\nPro Tip: When in doubt, prioritize your server-side event. It contains more reliable data and better matching parameters. If you have to choose just one (like during checkout flow issues), go with server-side.\n\n**Expected results:**\n\nOnce this is setup, you should see a step change increase in the volume of matched events on your Meta Ads. Results of 1 of many brand's who implemented this [here. ](https://ibb.co/0ZmzSMw)What you're seeing is the result of\n\n1. Larger volume of data collected\n2. This data has also been enriched for precise matching because the tracker collected more customer parameters\n3. Consistent volume of matched events on Meta (Brands without this have a lower EMQ score because the matching quality is 'patchier')\n4. Meta Ads can now leverage this information to match your ads to their users\n\n**Alternatively, look for a 1-click no-code solution:**\n\nWhen evaluating your options, the key features to look for are:\n\n1. 1-click server-side setup – this minimizes room for error\n2. Does not require Google Tag Manager – it's best if it natively leverages your site's infrastructure, eg. a Shopify native app\n3. Progressive identity building and data enrichment\n4. Proper deduplication automated", "author": "Green_Database9919", "created_time": "2024-12-20T13:09:18", "url": "https://reddit.com/r/PPC/comments/1hijfsg/easiest_way_i_found_to_increase_revenue_by_20/", "upvotes": 137, "comments_count": 80, "sentiment": "neutral", "engagement_score": 297.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hik9kw", "title": "<PERSON> cerny recent tech talk about ps5 pro and future ps6 proven that RT is not a gimmick", "content": "<PERSON> said that they are at the limits of traditional rasterization rendering with the limit now being due to the size of the GPU and he sees future growth will primarily driven by ray tracing and machine learning. This is what Nvidia have been building to all his time and nvidia have very massive headstart advantage on this", "author": "john1106", "created_time": "2024-12-20T13:54:44", "url": "https://reddit.com/r/nvidia/comments/1hik9kw/mary_cerny_recent_tech_talk_about_ps5_pro_and/", "upvotes": 0, "comments_count": 55, "sentiment": "bullish", "engagement_score": 110.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1him8su", "title": "European Cloud computing", "content": "Who is the better provider?", "author": "loopwert", "created_time": "2024-12-20T15:30:01", "url": "https://reddit.com/r/cloudcomputing/comments/1him8su/european_cloud_computing/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hio7tn", "title": "Goodbye Chrome, it was fun knowing you. But your time has come.", "content": "Internet without Ublock is just unwatchable. The ads are scummy, scammy, everywhere and now that's it's no longer supported I have no reason to stay. Have a good one folks!", "author": "Turbulent-Economy198", "created_time": "2024-12-20T16:59:06", "url": "https://reddit.com/r/chrome/comments/1hio7tn/goodbye_chrome_it_was_fun_knowing_you_but_your/", "upvotes": 806, "comments_count": 179, "sentiment": "bullish", "engagement_score": 1164.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hipa21", "title": "Google cut manager and VP roles by 10% in its efficiency push.", "content": "", "author": "early-retirement-plz", "created_time": "2024-12-20T17:44:59", "url": "https://reddit.com/r/wallstreetbets/comments/1hipa21/google_cut_manager_and_vp_roles_by_10_in_its/", "upvotes": 1985, "comments_count": 235, "sentiment": "neutral", "engagement_score": 2455.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hipbqx", "title": "The EU wants Apple to open AirDrop and AirPlay to Android and other platforms", "content": "", "author": "FragmentedChicken", "created_time": "2024-12-20T17:47:02", "url": "https://reddit.com/r/Android/comments/1hipbqx/the_eu_wants_apple_to_open_airdrop_and_airplay_to/", "upvotes": 2175, "comments_count": 423, "sentiment": "bearish", "engagement_score": 3021.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hisryn", "title": "Can I explode batteries (legally)", "content": "I live in the UK. Google won't tell me. It's gonna be in my garden", "author": "i_suck_toes_for_free", "created_time": "2024-12-20T20:20:39", "url": "https://reddit.com/r/batteries/comments/1hisryn/can_i_explode_batteries_legally/", "upvotes": 1, "comments_count": 10, "sentiment": "neutral", "engagement_score": 21.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hiugwh", "title": "3 penny stocks that might fck around and hit a 10x in the new year (nfa) - Stocksy's Weekly DD", "content": "Whats up everyone! Here are some notes on some of the companies that I have been paying attention to this week. Had to throw in $MMA.V since the Zambian gov finally approved their license, the company hasn’t even reported on it yet lol. $NCI.V one I have posted about in the past, it’s been really climbing recently. NICU is one I am pretty bullish on for the new year. This is all NFA, I am a random dude on reddit. Also, feel free to comment any tickers you would like me to checkout/review! Cheers\n\n  \n  \n\n\n**Midnight Sun Mining Corp. $MMA.**\n\nMarket cap: 88M\n\nCompany overview:\n\nMidnight Sun Mining is a junior exploration company focused on copper in Zambia’s copper belt, an area known for some of the world’s largest copper deposits. They hold a 506 km² property with promising targets, including the Solwezi Project, where exploration is advancing. With strong local partnerships and a strategic position in this well-established mining region, Midnight Sun is aiming to define new high-grade copper resources in a highly prospective area.\n\n\n\n**Highlights** \n\n\n\nMidnight Sun just received a looooong-awaited confirmation from the Zambian government that their exploration license for the Kazhiba target has been approved. This resolves months of uncertainty and clears the path for advancing one of their most promising oxide copper zones. With this approval, the company’s entire 506 km² Solwezi property is secured, allowing them to ramp up exploration across their four key targets: Dumbwa, Mitu, Kazhiba, and Crunch.\n\n\n\nKazhiba is especially critical because it’s part of a Cooperative Exploration Plan with First Quantum Minerals. This zone could provide near-term oxide copper feed to First Quantum’s Kansanshi Mine, located less than 10 km away. Kansanshi is Africa’s largest copper mine, and First Quantum has a pressing need for oxide copper to neutralize the sulphuric acid generated by their sulphide milling operations. High-grade results already confirmed at Kazhiba (like 14.2 meters at 5.71% Cu and 24 meters at 3.15% Cu) make this a huge opportunity.\n\n\n\n A supply deal with First Quantum could generate $40M-$60M annually for Midnight Sun, representing a massive win for a company with a market cap of just $80M. The potential is even more likely because of the strategic proximity of the assets: a direct highway connects Kazhiba to Kansanshi, meaning Midnight Sun could quickly capitalize on this opportunity.\n\n\n\nMan there is so much to unpack with this one… stay with me..\n\n\n\nAnother huge catalyst for Midnight Sun is the partnership with KoBold Metals, a cutting-edge exploration company backed by names like Bill Gates, Jack Ma, and Richard Branson. KoBold uses AI and machine learning to analyze geoscience data, making exploration faster and more efficient. Their team includes top-tier geologists like Dr. David Broughton, who led the discovery of world-class projects like Kamoa-Kakula in the Congo. KoBold signed a $15.5M earn-in agreement for the Dumbwa target, a Tier-One exploration zone that features a massive 20 km by 1 km copper-in-soil anomaly with peak values of 0.73% Cu.\n\n\n\nKoBold’s team believes Dumbwa has the potential to rival, or even exceed, Barrick’s nearby Lumwana Mine (960Mt at 0.55% Cu), a major copper operation. Under the agreement, KoBold will cover all exploration costs for Dumbwa, and Midnight Sun will retain 25% of the asset. Importantly, KoBold will also pay Midnight Sun $500,000 annually for four years, giving the company non-dilutive cash flow to explore its other high-priority targets, like Kazhiba and Mitu. This structure means Midnight Sun takes on zero financial risk while leveraging one of the best exploration teams in the industry to unlock Dumbwa’s value.\n\n\n\nWith the Zambian license now approved, I’m expecting a busy Q1 for Midnight Sun. Tons of news coming \n\n  \n  \n\n\n**NTG Clarity Networks Inc. $NYWKF $NCI.V**\n\n\n\nMarket cap: 65M (up 80% since first post)\n\n\n\nNTG Clarity Networks provides telecom and IT solutions, specializing in software development and network management. Their primary market is the Middle East, where they’ve been gaining momentum thanks to large-scale investments in digital infrastructure, particularly in Saudi Arabia. With a strong focus on enterprise clients, NTG has become a go-to partner for companies looking to modernize and optimize their operations.\n\n\n\n**Highlights**\n\n\n\nNTG Clarity Networks has been on an impressive run this year, and for good reason. Their Q3 2024 results showed $12.5M in revenue, up 109% from last year, with $2.1M in net income. That’s their eighth straight record-breaking quarter, which really speaks to how well they’ve positioned themselves in the Middle East’s booming digital transformation market. \n\n\n\nThe big story here is their ability to land massive, multi-year contracts. Their $53M deal earlier this year was a game-changer, and with over $70M in backlog right now, they’ve got a lot of work lined up. What stands out to me is how focused they are on Saudi Arabia. The Vision 2030 plan is driving a huge push for digital infrastructure in the region, and NTG has tapped into that perfectly. This isn’t just about them winning contracts, it’s about being in the right place at the right time with the right solutions.\n\n\n\nWhat I also like about NTG is their efficiency. Their offshore campus in Egypt has been key to keeping costs down while scaling up. They’ve got over 950 people working across the globe, and their ability to deliver high-quality solutions at a competitive price is why they’ve been able to keep those margins up, even as they grow.\n\n\n\nLooking forward, I think NTG is set up for a very strong 2025. They’ve got a healthy mix of new business and renewals, which shows their offerings are sticking with clients. With a backlog this size and strong execution, I wouldn’t be surprised to see more contract announcements soon. Insider ownership is also worth noting (46% insider ownership).\n\nThis is one I was talking about back in June when the stock was sitting around $0.85. No complaints about management, they have been making good progress in fixing up the balance sheet over the past few quarters and they continue to rake in solid contracts. NFA but as mentioned I think NTG will have an amazing 2025.\n\n  \n  \n  \n\n\n**Magna Mining Inc. $MGMNF $NICU.V**\n\n\n\nMarket Cap: $276M\n\n\n\nCompany Overview\n\n\n\nMagna Mining is a Canadian base metals company focused on nickel, copper, and PGM projects in the Sudbury Basin. With the advanced-stage Crean Hill project and the operating McCreedy West mine, Magna is working to build a portfolio of cash-generating assets while advancing its development pipeline.\n\n\n\n**Highlights:**\n\n\n\nMagna Mining is entering a transformative phase with its recent acquisition of multiple Sudbury assets from KGHM, including the producing McCreedy West Mine and several other properties with untapped potential. These acquisitions align with the company's vision of becoming a mid-tier producer of nickel and copper.\n\n\n\nThe Crean Hill Project remains the cornerstone of Magna’s strategy. The recently updated PEA (November 2024) outlines a 13-year mine life with an after-tax NPV of $194.1M and an ultra-quick payback period of 1.5 years. Crean Hill is already generating cash flow, with bulk sampling contributing $1.28M. This de-risks the project a ton while exploration efforts aim to expand its resource base further.\n\n\n\nOn top of that, the Crean Hill resource includes a mix of nickel, copper, and precious metals like platinum and palladium, making it a versatile asset that aligns with global decarbonization trends. It is also conveniently located near Sudbury’s established smelters, which reduces costs and timelines for processing.\n\n\n\nThe McCreedy West Mine, part of the KGHM acquisition, is another standout. With over 9M tonnes of high-grade resources (1.30% copper and 0.89% nickel), McCreedy West has been producing recently and offers immediate cash flow potential. Plans are underway to optimize production by late 2025, with improvements to grades and output expected.\n\n\n\nThe Shakespeare Project adds another layer of optionality. While development is on hold, the project is fully permitted for a 4,500-tonne-per-day operation. Recent exploration in the Southwest Copper Zone (32.4m of 1.4% copper, including 13.9m at 2.3%) showcases its long-term value and upside.\n\n\n\nMagna’s management team, many of whom have extensive experience in the Sudbury Basin, continues to demonstrate operational expertise. Their ability to secure processing agreements with majors like Vale and Glencore reduces barriers to production and underscores the company’s strategic focus.\n\n\n\nReally bullish on Magna’s drill targets and looking forward to hearing more about some of their new KGHM properties in the new year!\n\n", "author": "Stocksy1234", "created_time": "2024-12-20T21:38:56", "url": "https://reddit.com/r/pennystocks/comments/1hiugwh/3_penny_stocks_that_might_fck_around_and_hit_a/", "upvotes": 136, "comments_count": 72, "sentiment": "bullish", "engagement_score": 280.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hiun03", "title": "Why only Google has a low P/E? ", "content": "i don't get it.  \n  \nWhy is google with all it's **profitability** and **exemplar capital allocation** the only tech giant that has a **low P/E**, and consistently kept it low through the years as it grew it's top line an average of 14%/y??  \n  \nAm I missing something? was the market never efficient? should we divest from Index funds?", "author": "Savings-Judge-6696", "created_time": "2024-12-20T21:46:49", "url": "https://reddit.com/r/investing/comments/1hiun03/why_only_google_has_a_low_pe/", "upvotes": 103, "comments_count": 107, "sentiment": "bullish", "engagement_score": 317.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hiuoi3", "title": "Why Google is the only Mag7 with reasonable P/E?", "content": "i don't get it.  \n  \nWhy is google with all it's **profitability** and **exemplar capital allocation** the only tech giant that has a **low P/E**, and consistently kept it low through the years as it grew it's top line an average of 14%/y??  \n  \nAm I missing something? was the market never efficient? should we divest from Index funds?", "author": "Savings-Judge-6696", "created_time": "2024-12-20T21:48:43", "url": "https://reddit.com/r/stocks/comments/1hiuoi3/why_google_is_the_only_mag7_with_reasonable_pe/", "upvotes": 570, "comments_count": 298, "sentiment": "bullish", "engagement_score": 1166.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hiw8md", "title": "Google Updates are complete BS", "content": "Let's talk about Google updates and why they are complete BS. As someone who has been in the search / internet marketing industry since 2008 the money grab and the greed that we now see from Google after covid times are insane. Google keeps trying to peddle this whole \"create quality content\" and \"look at us, we are fighting spam with spam updates and HCU\", and what do we see in the end? Site owners that pour their heart into their sites get hit, while BS AI spam, useless AI overviews that are so fking inaccurate, and parasite SEO strives. \n\nu/Google, honestly, WTF are you doing? What type of morons work there? eCommerce results are complete garbage with repetitive results from brand sites, which, in most cases, don't even carry the product. Forbes is now apparently a review site, and they are so good at reviewing everything, from printers to plumbers, movers, and service-based businesses, Google absolutely loves them.  \n\n\nSh\\*t like seoinla . com has been ranking for keywords like \"SEO Los Angeles\" for over a year and passed every update, it must be great content and user experience, and they definitely fulfill user intent. Links like goo. gl/ maps/ KWrg4qPqKEN9mMer5 are now also ranking instead of the actual websites.\n\nHacked redirect spam rankings are at an all-time high, and that's probably the funniest thing to me. Imagine you create 100 good quality articles, it takes Google fking for EVER to index those. But as soon as your WP site gets hacked with Chinese redirect spam, OMG, Google gets a hard-on and will index those 1000s of pages in 24 hours. Can someone explain that to me? How stupid does your algorithm honestly have to be where it can't differentiate hacked pages vs the actual content? If my site is about T-shirts, and all of a sudden it gets hacked, and now I have 1000's of pages about p..rn and other BS, why algorithm doesn't throw a red flag and says, \"Wait a second, this site had only 100 pages yesterday about T-shirts, today they have 10,000 adult pages, something doesn't add up\", but instead, Google is like \"f\\*k yeah, let's index those 10k pages and rank them for xxx videos, that seems legit\". \n\nu/google is losing it, well actually, lost it. They turned into a BS search engine that no longer puts users first, it's now more about how much money they can squeeze out of business owners and fill their pockets. They completely obliterated content publishers which they used to gain their dominance in the first place.\n\nGoogle ads are also a complete sh\\*t where the CPC and CPL are so high now that it's not even worth running those ads anymore. \n\nSo dear u/google, go F yourself, since you are now completely useless to 90% of the publishers / small business owners.\n\nAnd yes, the only way to do SEO now is to churn and burn AI spamming and blackhat since the dumbass Google algorithm can't differentiate between quality content and garbage.\n\n  \n", "author": "az425", "created_time": "2024-12-20T23:03:38", "url": "https://reddit.com/r/SEO/comments/1hiw8md/google_updates_are_complete_bs/", "upvotes": 134, "comments_count": 68, "sentiment": "neutral", "engagement_score": 270.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}]