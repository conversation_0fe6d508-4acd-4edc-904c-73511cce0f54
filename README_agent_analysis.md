# AI对冲基金系统代理信号分析工具

这是一个用于分析和可视化AI对冲基金系统中各个代理交易信号和置信度数据的Python工具集。

## 功能特性

- 📊 **综合数据分析**: 从reasoning_logs目录提取各代理的交易信号(BUY/SELL/HOLD)和置信度数据
- 🎨 **改进版可视化**: 单一综合热力图同时显示交易信号和置信度信息
- 🌈 **智能颜色编码**: 使用颜色透明度表示置信度强度，颜色越深表示置信度越高
- 🔢 **数值标注**: 每个单元格显示具体的置信度数值，便于精确分析
- 📋 **详细图例**: 清楚说明颜色、透明度与置信度的对应关系
- 📈 **统计报告**: 自动生成详细的统计分析报告
- 🌐 **中文支持**: 完整的中文界面和字体支持
- 🔧 **灵活配置**: 支持不同股票、模型和日期范围的数据分析
- 📐 **自适应布局**: 根据数据量自动调整图表大小和字体

## 文件说明

- `analyze_agent_signals.py`: 核心分析脚本（改进版）
- `run_analysis_example.py`: 快速使用示例脚本
- `demo_improved_analysis.py`: 改进功能演示脚本
- `README_agent_analysis.md`: 使用说明文档

## 安装依赖

```bash
pip install pandas matplotlib seaborn numpy pathlib
```

## 使用方法

### 方法1: 改进功能演示脚本（推荐新用户）

```bash
python demo_improved_analysis.py
```

该脚本自动演示改进后的可视化功能，包含：
- 自动分析AAPL、MSFT、NVDA三个代表性实验
- 展示改进后的综合热力图效果
- 详细的功能说明和使用建议

### 方法2: 快速使用示例脚本

```bash
python run_analysis_example.py
```

该脚本提供交互式菜单，包含：
- 预设的分析示例（AAPL、MSFT、NVDA等不同模型）
- 自定义分析选项
- 批量分析所有实验

### 方法3: 直接使用核心脚本

```bash
# 分析AAPL的Gemini 2.0 Flash实验
python analyze_agent_signals.py --experiment_path reasoning_logs/experiment_AAPL_20250101-20250601_gemini2.0_flash

# 分析MSFT的GPT-3.5实验，指定输出目录
python analyze_agent_signals.py --experiment_path reasoning_logs/experiment_MSFT_20250101-20250601_gpt3.5 --output_dir ./charts/msft_analysis

# 分析NVDA的Grok Beta实验
python analyze_agent_signals.py --experiment_path reasoning_logs/experiment_NVDA_20250101-20250601_grok-beta
```

## 支持的实验路径格式

脚本支持以下格式的实验路径：
- `reasoning_logs/experiment_AAPL_20250101-20250601_gemini2.0_flash`
- `reasoning_logs/experiment_MSFT_20250101-20250601_gpt3.5`
- `reasoning_logs/experiment_NVDA_20250101-20250601_llama4-scout`

## 输出文件

分析完成后会生成以下文件：

### 1. 可视化图表
- **文件名**: `agent_analysis_{TICKER}_{MODEL}_{DATE_RANGE}.png`
- **内容**: 改进版综合热力图，同时显示交易信号和置信度
  - 颜色表示信号类型：绿色(BUY)、红色(SELL)、灰色(HOLD)
  - 透明度表示置信度：颜色越深表示置信度越高
  - 数值标注显示具体置信度值

### 2. 统计报告
- **文件名**: `agent_statistics_{TICKER}_{MODEL}_{DATE_RANGE}.txt`
- **内容**: 详细的统计信息，包括：
  - 每个代理的信号分布（BUY/SELL/HOLD百分比）
  - 平均置信度
  - 总信号数量

## 代理筛选规则

脚本会自动排除以下代理：
- `portfolio_manager_agent`
- `risk_management_agent`
- `reflection_analyst`

## 支持的代理类型

脚本支持分析以下代理（包含中文显示名称）：

### 投资大师代理
- Warren Buffett Agent
- Ben Graham Agent
- Peter Lynch Agent
- Bill Ackman Agent
- Cathie Wood Agent
- Charlie Munger Agent
- Phil Fisher Agent
- Michael Burry Agent
- Stanley Druckenmiller Agent
- Aswath Damodaran Agent

### 分析师代理
- 技术分析师 (Technical Analyst)
- 基本面分析师 (Fundamentals Analyst)
- 情绪分析师 (Sentiment Analyst)
- 估值分析师 (Valuation Analyst)
- 事实新闻分析师 (Factual News Analyst)
- 主观新闻分析师 (Subjective News Analyst)
- 新闻分析师 (News Analyst)
- 社交媒体分析师 (Social Media Analyst)
- 市场分析师 (Market Analyst)

## 图表说明

### 改进版综合热力图
- **颜色编码**:
  - 🟢 **绿色**: BUY信号（看涨）
  - 🔴 **红色**: SELL信号（看跌）
  - ⚪ **灰色**: HOLD信号（中性）
- **透明度编码**:
  - **深色**: 高置信度（0.7-1.0）
  - **中等**: 中等置信度（0.4-0.7）
  - **浅色**: 低置信度（0.0-0.4）
- **数值标注**: 每个单元格内的数字显示具体置信度值
- **坐标轴**:
  - **横轴**: 时间（日期）
  - **纵轴**: 代理名称（中文显示）
- **网格线**: 白色网格线提高可读性

### 使用技巧
- **关注深色区域**: 代表高置信度的交易决策
- **横向观察**: 了解特定时期所有代理的市场共识
- **纵向分析**: 比较不同代理的决策风格和一致性
- **数值参考**: 结合置信度数值进行精确分析

## 故障排除

### 常见问题

1. **"实验路径不存在"错误**
   - 检查reasoning_logs目录是否存在
   - 确认实验文件夹名称正确

2. **"未找到有效数据"错误**
   - 检查实验目录下是否有日期子目录
   - 确认JSON文件格式正确

3. **中文字体显示问题**
   - 脚本会自动尝试使用SimHei、Microsoft YaHei等中文字体
   - 如果仍有问题，请安装相应字体

4. **内存不足**
   - 对于大型数据集，可能需要增加系统内存
   - 考虑分批处理数据

### 调试模式

如需查看详细的处理过程，可以修改脚本中的日志级别或添加调试输出。

## 扩展功能

脚本设计为模块化结构，可以轻松扩展：

1. **添加新的可视化类型**
2. **支持更多数据格式**
3. **增加统计分析功能**
4. **集成更多图表库**

## 注意事项

- 确保有足够的磁盘空间存储生成的图表和报告
- 大型数据集可能需要较长的处理时间
- 建议在分析前备份原始数据

## 技术支持

如遇到问题或需要功能扩展，请检查：
1. Python版本兼容性（推荐Python 3.7+）
2. 依赖包版本
3. 数据文件格式和结构

---

**最后更新**: 2025-07-09
**版本**: 1.0.0
