[{"platform": "reddit", "post_id": "reddit_1kx4jqs", "title": "Burnout and “one more year” syndrome", "content": "I am 40, married, 1 little child, west coast HCOL.\n\nHave been in tech for the last 20 years — did everything from tech support to software engineering, and from startups to big tech (at Google as of “recent”).\n\nMy total comp has steadily increased through time and now sits at around $800k/year (crazy, I know, I still can’t believe it!), my spouse has a “normal” non tech job at $90k/year.\n\nWe have $4.5M saved up between taxable and tax advantaged accounts, no cap gain, very conservative allocation. Zero debts and no other assets (we rent). Our expenses are about $150k/year (most of it is rent + childcare).\n\nIt was a long road to get to this point, with ups and downs and starting from very humble beginnings. In the last couple of years I have hit a very rough patch at work (a string of terrible managers, mismanaged projects, layoffs) and had to deal with some health issues. I despise my current role, and ironically I keep getting more responsibilities and the highest ratings.\n\nI never thought I’d say this, but for the first time in my life I just feel extremely tired and burned out. I kept pushing as each month those sweet RSUs keep coming.\n\nWe could easily relocate to LCOL. I fantasize every day about just quitting and enjoying life, exercise, read a book, slow down. I just can’t bring myself to do it: “one more year”, “one more month”, “one more week”.\n\nI think of all the folks that would do anything for a $800k/year job and feel guilty throwing that away.\n", "author": "GOOG_FIRE-oneoff", "created_time": "2025-05-28T01:22:21", "url": "https://reddit.com/r/Fire/comments/1kx4jqs/burnout_and_one_more_year_syndrome/", "upvotes": 139, "comments_count": 92, "sentiment": "bullish", "engagement_score": 323.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kx5iyr", "title": "Google search campaign starting out", "content": "I'm running a Google ad campaign, it's one that I ran for years before Google changed it's algorithm and then eventually blocked my account. When I first ran the campaign it was very easy to get started I just did \"american airlines\" as a keyword and set up a conversion action for phone calls. Google got this done and got me tons of calls within the first few hours and my conversion action knew what it was doing. However, after the Google changes and suspension of my original account I'm trying to get this campaign up and running again with Google ads. Except, I think the issue that I'm having now is that Google doesn't know what a conversion action is and because the broad match / search intent is now king, it doesn't actually know what kind of traffic to send me. Thus, I get tons of clicks for \"american airlines\" because there is like 10,000 a day, but the ones I get are low cost and low intent clicks and I don't get any conversions.  \n  \nI've tried doing this on exact match keywords, \\[american airlines phone\\], as an example, but all those keywords inevitably lead back to the search term \"american airlines\". Which I think would be totally fine if Google had just a few conversions and knew what a conversion for me and my account was. But unfortunately, I haven't been able to get it there and it just keeps overspending my budget.  \n  \nI've tried doing portfolio bidding with a minimum bid amount that is rather high, thus I am in more competitive auctions instead of what likely would be junk traffic. I've also tried doing a high CPA and it just doesn't do anything or totally over spends everything. Nothing has really worked for this. I think the solution would be to get Google as many conversion actions as possible, but this is seeming to be impossible at this time. Not sure what other approaches to take? How can I just really push Google to get those first few conversions?", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-28T02:11:05", "url": "https://reddit.com/r/adwords/comments/1kx5iyr/google_search_campaign_starting_out/", "upvotes": 1, "comments_count": 2, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kx5y6b", "title": "BYD cuts prices to sell EV, dumps inventory to EU.  BYD Stock drops 9% today, TSLA up 8% 🤷", "content": "", "author": "Traditional_War_8229", "created_time": "2025-05-28T02:32:02", "url": "https://reddit.com/r/teslainvestorsclub/comments/1kx5y6b/byd_cuts_prices_to_sell_ev_dumps_inventory_to_eu/", "upvotes": 35, "comments_count": 135, "sentiment": "bearish", "engagement_score": 305.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kx6q9t", "title": "Crushing It with Two Simple Strategies: My 15-Min ORB & Falling Wedge Breakouts! AMA!", "content": "Just wanted to share something that's been working really well for me. I've been consistently profitable recently, and it's all thanks to focusing on just two core strategies:\n\n1. **The 15-Minute Opening Range Breakout (ORB):** This one is my bread and butter for catching those initial explosive moves right after the open.\n2. **The Falling Wedge Breakout:** Super reliable for spotting reversals and getting in on new trends.\n\nWhat's cool is that even on those notoriously tough days – you know, the big gap-ups or the ones where the market just chops around like crazy – these two strategies have kept me in the green. It's been a game-changer for my trading.\n\nI'm happy to chat about them, answer questions, or even just share my general approach.\n\n**So, hit me with your questions! Anyone else swear by these? Or got other strategies that shine on tricky days? Let's talk!**\n\nhttps://preview.redd.it/e3heqxnhwf3f1.png?width=1835&format=png&auto=webp&s=97123c146426bdad042c7fea1dc7c4dcfd502558\n\n", "author": "Hereforthebulls", "created_time": "2025-05-28T03:11:42", "url": "https://reddit.com/r/options/comments/1kx6q9t/crushing_it_with_two_simple_strategies_my_15min/", "upvotes": 6, "comments_count": 39, "sentiment": "bullish", "engagement_score": 84.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kx7ru3", "title": "What Was Your First Step Toward a More Sustainable Lifestyle?", "content": "Mine was switching to a reusable water bottle. I know it sounds small, but that one change opened my eyes to all the single-use plastic I was going through. It wasn’t about perfection—it was about starting somewhere. One swap led to another: tote bags, composting, buying secondhand. It’s not a race, it’s a journey, and I’m still learning every day. What was your first step?", "author": "su<PERSON><PERSON>_chaiya<PERSON>n", "created_time": "2025-05-28T04:08:05", "url": "https://reddit.com/r/sustainability/comments/1kx7ru3/what_was_your_first_step_toward_a_more/", "upvotes": 28, "comments_count": 46, "sentiment": "bullish", "engagement_score": 120.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kx7zt2", "title": "Are These \"Liquid Sodium\" Battery Stats Too Good to Be True?", "content": "Looking for Unbiased Expert Feedback\n\nHey r/batteries,\nI came across a comparison chart of different battery chemistries, and one entry—simply labeled \"Liquid Sodium\"—absolutely jumped out at me for its incredible stats. To keep things as unbiased as possible, I’ve **erased the brand name** from the image, since I really want to hear honest, technical feedback from the community without any marketing influence.\n\nHere are the standout specs for this \"Liquid Sodium\" battery (see attached image for the full table):\n\n- **Energy Density:** 552 Wh/kg (much higher than anything else listed)\n- **Life Cycle:** No loss in discharge (unlimited cycles?)\n- **Open Circuit Voltage:** 4.2V\n- **Operating Temp:** 120°C to 250°C\n- **Self-Discharge:** 0% per month\n- **Safety:** No thermal runaway, insulated for high temps\n- **Toxicity:** Low risk, earth-abundant materials\n\n**A few questions for the experts:**\n- Are these stats for \"liquid sodium\" batteries realistic, or is there something I should be skeptical about?\n- What are the main engineering or safety challenges for batteries that operate at such high temperatures?\n- Has anyone seen independent, peer-reviewed data or real-world deployments for this kind of cell?\n- Are there key factors left out in this chart\n- What would be the biggest technical or commercial barriers?\n\nI’m genuinely curious if this is a breakthrough or just another overhyped lab result. Would love to hear your thoughts—especially from anyone with hands-on or research experience in molten/thermal battery tech.\n\nThanks in advance!\n\n*(Image attached for reference—brand name removed for unbiased discussion)*\n\n\n\n", "author": "julian_jak<PERSON>i", "created_time": "2025-05-28T04:20:15", "url": "https://reddit.com/r/batteries/comments/1kx7zt2/are_these_liquid_sodium_battery_stats_too_good_to/", "upvotes": 143, "comments_count": 169, "sentiment": "bearish", "engagement_score": 481.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxajlh", "title": "Keeping clean while sustainable.", "content": "In terms of clothing consumption, I have done all the go to things. Years ago, I stopped buying fast fashion, I use what I own and when I need to I buy second hand. I also use those paper laundry detergent sheets. However, rewearing, washing less, and such, I feel like I smell. My house is damp, and I’m worried that everything smells, that I smell and everyone else can smell it. I don’t know what to do. \n\nMy sister keeps saying that the laundry sheets don’t work, even though I feel that they do. But she is essentially the opposite to everything I’ve just said. Consumes a lot of new clothes etc. But that to me feels dirty to the planet. ", "author": "Organic-News-8930", "created_time": "2025-05-28T06:56:55", "url": "https://reddit.com/r/sustainability/comments/1kxajlh/keeping_clean_while_sustainable/", "upvotes": 3, "comments_count": 5, "sentiment": "bullish", "engagement_score": 13.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxax4j", "title": "this emotional support kangaroo video is going viral on social media, and many people believe it’s real, but it’s actually AI", "content": "", "author": "Outside-Iron-8242", "created_time": "2025-05-28T07:21:48", "url": "https://reddit.com/r/singularity/comments/1kxax4j/this_emotional_support_kangaroo_video_is_going/", "upvotes": 7134, "comments_count": 527, "sentiment": "neutral", "engagement_score": 8188.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxe04d", "title": "How I Got My First 1,000 Users Without Running Any Ads", "content": "When I first started my small project, I had zero money for ads. Like not even enough for one boosted post. So I had to get creative.\n\nI printed out some stickers with my logo and a QR code. Then I went around local colleges, gyms, and cafes and just started sticking them everywhere.\n\nNo one asked me to. I just did it. It felt kinda crazy, but I figured, why not try?\n\nGuess what? By the end of the week, over 1,000 people had scanned the code. Some even took pics of the sticker and shared it on Instagram. That’s when I first learned about guerrilla marketing. It’s just using smart, low-cost ways to get attention in real life.\n\nIt actually worked. And it was way more fun than just pressing \"boost post\" on social.", "author": "confusedwithmoney", "created_time": "2025-05-28T10:50:44", "url": "https://reddit.com/r/marketing/comments/1kxe04d/how_i_got_my_first_1000_users_without_running_any/", "upvotes": 1, "comments_count": 38, "sentiment": "neutral", "engagement_score": 77.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxev0m", "title": "Tesla sales cut in half in Europe", "content": "", "author": "Cubezzzzz", "created_time": "2025-05-28T11:39:46", "url": "https://reddit.com/r/business/comments/1kxev0m/tesla_sales_cut_in_half_in_europe/", "upvotes": 185, "comments_count": 13, "sentiment": "neutral", "engagement_score": 211.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxfac7", "title": "Tesla Rolls Out ‘Child Left Alone Detection’ Feature in 2025.14.12 Software Update", "content": "", "author": "chrisdh79", "created_time": "2025-05-28T12:02:18", "url": "https://reddit.com/r/teslamotors/comments/1kxfac7/tesla_rolls_out_child_left_alone_detection/", "upvotes": 602, "comments_count": 152, "sentiment": "neutral", "engagement_score": 906.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxiic9", "title": "Based on 2024 revenue data… P/E ratio is 198.91 vs 24.7 as of today btw.", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-28T14:27:53", "url": "https://reddit.com/r/StockMarket/comments/1kxiic9/based_on_2024_revenue_data_pe_ratio_is_19891_vs/", "upvotes": 8046, "comments_count": 774, "sentiment": "neutral", "engagement_score": 9594.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxjbkp", "title": "Looking for Guidance to Start in Ad Management 🙏", "content": "Hello and good afternoon, fellow AdWorks members!\n\nI'm a 17-year-old who just graduated from high school, and I’m really interested in learning about ad management. The thing is, I’m not sure where to start—whether I should dive into YouTube tutorials, invest in a course, or try learning everything on my own. Fortunately, I have a lot of free time to dedicate to this.\n\nMy goal is to start managing ads for local real estate businesses—I already have some contacts in that industry—as well as for online pages. I’m very open to learning and would love to hear any advice or guidance you can offer.\n\nIt would mean a lot to me if you could recommend useful resources or share how you personally got started in this field. If anyone is open to teaching or mentoring, I’d be incredibly grateful.\n\nI’m always open to DMs and willing to listen to anyone who wants to share their experience or help. You can also reach me at [**<EMAIL>**](mailto:<EMAIL>) or on Instagram at u/sergiisuarezz if that’s more convenient.\n\nThank you very much in advance—I’m really looking forward to connecting with you and learning from this amazing community!", "author": "EconomyProduct5553", "created_time": "2025-05-28T15:00:53", "url": "https://reddit.com/r/adwords/comments/1kxjbkp/looking_for_guidance_to_start_in_ad_management/", "upvotes": 2, "comments_count": 4, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxk6w9", "title": "Google Ads had 10+ campaigns during the given time range, GA4 only shows 4", "content": "I have a rather popular problem, but I cannot find the solution that fits\n\nI'm seeing a massive discrepancy between my Google Ads clicks and Google Analytics 4 (GA4) sessions, and I'm also finding that many of my campaigns aren't even appearing in GA4. I'm hoping someone can help me pinpoint the issue.\n\nIn Google Ads, for the same time period, I see 1691 clicks. In GA4, for \"Paid Search\" (specifically Google Ads campaigns), I only see 298 sessions. This is a \\~82% drop-off.\n\nMy GA4 \"Traffic Acquisition\" report (using \"Session Google Ads campaign\" dimension) only shows 4 campaigns. However, I had over 10 active and paused campaigns running during that exact same period in Google Ads. The missing campaigns simply don't show up in GA4 at all.\n\n**What I've already checked:**\n\n* Auto-tagging is ON in my Google Ads account.\n* Google Ads and GA4 are linked.\n* The date range in GA4 matches the Google Ads date range where the campaigns were active/paused.\n\nAre there any common issues I'm missing that could cause both the huge click/session discrepancy and the completely missing campaigns?\n\nAny insights or troubleshooting steps would be greatly appreciated! Thanks in advance.", "author": "iwf_wh", "created_time": "2025-05-28T15:35:11", "url": "https://reddit.com/r/marketing/comments/1kxk6w9/google_ads_had_10_campaigns_during_the_given_time/", "upvotes": 1, "comments_count": 2, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxkf5y", "title": "Could this external framework accelerate XAI's / neuralink development path?", "content": "While exploring human-AI co-governance and adaptive architectural design, we developed a comprehensive multi-layered framework that aligns with many of XAI’s current philosophical and operational goals.\n\nNo symbolic language, no illusions – just a rigorous system architecture, prepared with AI-orchestration and structural logic in mind.\n\nThe complete Dossier (42 files, peer-reviewed format, ready for implementation or adaptation) is now publicly available:\n\n🔗 Zenodo publication link – https://zenodo.org/records/15535290\n\nIf any of you are close to the XAI development team or Neuralink structure: feel free to forward or annotate.\n", "author": "Delicious-Shock-3416", "created_time": "2025-05-28T15:44:14", "url": "https://reddit.com/r/elonmusk/comments/1kxkf5y/could_this_external_framework_accelerate_xais/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxmkpg", "title": "Google Ads Leads Dropped to Zero - No Major Changes. Advice?", "content": "I run Google Ads for a B2B manufacturing company. We were getting \\~10 leads/week until mid-May, then leads suddenly dropped to zero.\n\nNo major changes were made:\n\n* Budget is steady at $210/day\n* CPA target is $200\n* All ads rated “Excellent”\n* Same landing page, same structure\n\nWhat I’ve tried:\n\n* Testing new keywords and ad copy\n* Adjusting bids and CPA targets\n* SEMrush confirms a sharp traffic drop around May 17\n* Impression Share tanked (Top IS <10%, Lost IS >80%)\n* No automated rules in play\n\nEven our best-performing keywords have stopped converting. Could this be algorithm-related or competitor pressure?\n\nWould love any thoughts. Thanks!", "author": "Disastrous-Gold3841", "created_time": "2025-05-28T17:09:11", "url": "https://reddit.com/r/marketing/comments/1kxmkpg/google_ads_leads_dropped_to_zero_no_major_changes/", "upvotes": 2, "comments_count": 6, "sentiment": "bearish", "engagement_score": 14.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxorym", "title": "My negative keywords at the ad group level are not working as expected", "content": "Is this another example of Google's overreach into our pockets (likely) or is my understanding of how negative keywords function in need of an adjustment (also likely)?\n\n* In Ad Group 1, I am targeting the keyword \\[health ball\\] (exact match)\n* To control my messaging for health-related terms, I've applied the negative keyword \"health\" (phrase match) to Ad Group 2\n* In spite of this, I see clicks for \\[health ball\\] in my search terms report for Ad Group 2 from an exact match keyword that doesn't include the word 'health' or 'ball'\n\n...What the bloody hell?", "author": "wldsoda", "created_time": "2025-05-28T18:35:59", "url": "https://reddit.com/r/adwords/comments/1kxorym/my_negative_keywords_at_the_ad_group_level_are/", "upvotes": 1, "comments_count": 0, "sentiment": "bearish", "engagement_score": 1.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxqhcb", "title": "Best ad platform for selling products? Facebook, Google, Tiktok, Instagram?", "content": "After a year in business I think I want to try spending some money on ads.. Business can be extremely slow some days and just isn’t cutting it if I ever want to do this full time. \n\nSeems like I’m gonna have to spend quite a few $ on this stuff so i’d like to preferably spend it wisely. \n\n\nOpen to listening to all your comments and advice! ", "author": "Big-Advice-4009", "created_time": "2025-05-28T19:43:39", "url": "https://reddit.com/r/marketing/comments/1kxqhcb/best_ad_platform_for_selling_products_facebook/", "upvotes": 3, "comments_count": 9, "sentiment": "bearish", "engagement_score": 21.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxsimd", "title": "Tesla Targets June 12 Launch of Robotaxi Service in Austin", "content": "", "author": "watergoesdownhill", "created_time": "2025-05-28T21:03:59", "url": "https://reddit.com/r/SelfDrivingCars/comments/1kxsimd/tesla_targets_june_12_launch_of_robotaxi_service/", "upvotes": 134, "comments_count": 431, "sentiment": "neutral", "engagement_score": 996.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxskwh", "title": "Please fill out this investing google form for my school project", "content": "Hey guys, I'm conducting a mini research project in school on investing trends, specifically among teens (but everyone is welcome to respond). It would be great if you could fill out this super short google form so I can collect data for the project. Thank you very much.\n\n[https://docs.google.com/forms/d/e/1FAIpQLSdvFbUYOE9NlDe3DGejGsUCfhX4B2OOogZoMJeU90lI6U4f-g/viewform?usp=sharing&ouid=112884597025009281369](https://docs.google.com/forms/d/e/1FAIpQLSdvFbUYOE9NlDe3DGejGsUCfhX4B2OOogZoMJeU90lI6U4f-g/viewform?usp=sharing&ouid=112884597025009281369)\n\n", "author": "Crafty-Sprinkles4063", "created_time": "2025-05-28T21:06:30", "url": "https://reddit.com/r/dividends/comments/1kxskwh/please_fill_out_this_investing_google_form_for_my/", "upvotes": 0, "comments_count": 5, "sentiment": "bullish", "engagement_score": 10.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxu61a", "title": "Tesla investors demand Musk work 40-hour week at EV maker as 'crisis' builds", "content": "", "author": "Force_Hammer", "created_time": "2025-05-28T22:11:38", "url": "https://reddit.com/r/StockMarket/comments/1kxu61a/tesla_investors_demand_musk_work_40hour_week_at/", "upvotes": 1139, "comments_count": 143, "sentiment": "neutral", "engagement_score": 1425.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxvqtn", "title": "Can tesla really drive safely without a driver behind the wheel?", "content": "Tesla has not had any evidence of driver out success and yet they are going without a driver for testing on the roads in June.  How did we get here?  I feel that the public safety is at risk here.  Thoughts?", "author": "jetsyuan", "created_time": "2025-05-28T23:21:14", "url": "https://reddit.com/r/SelfDrivingCars/comments/1kxvqtn/can_tesla_really_drive_safely_without_a_driver/", "upvotes": 18, "comments_count": 260, "sentiment": "neutral", "engagement_score": 538.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kxwbfn", "title": "Please fill out this investing google form for my school project", "content": "Hey guys, I'm conducting a mini research project in school on investing trends, specifically among teens (but everyone is welcome to respond). It would be great if you could fill out this super short google form so I can collect data for the project. Thank you very much.\n\nhttps://docs.google.com/forms/d/e/1FAIpQLSdvFbUYOE9NlDe3DGejGsUCfhX4B2OOogZoMJeU90lI6U4f-g/viewform?usp=sharing&ouid=112884597025009281369", "author": "xykwnthrkodu", "created_time": "2025-05-28T23:47:29", "url": "https://reddit.com/r/investing_discussion/comments/1kxwbfn/please_fill_out_this_investing_google_form_for_my/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}]