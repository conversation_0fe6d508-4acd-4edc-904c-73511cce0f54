#!/usr/bin/env python3
"""
Reddit API重试脚本 - 自动处理403错误
"""

import time
import subprocess
import sys
from datetime import datetime

def retry_reddit_collection():
    """重试Reddit数据收集"""
    max_attempts = 5
    base_delay = 3600  # 1小时
    
    for attempt in range(max_attempts):
        print(f"\n=== 尝试 {attempt + 1}/{max_attempts} ===")
        print(f"时间: {datetime.now()}")
        
        try:
            # 运行Reddit收集脚本
            result = subprocess.run([
                sys.executable, "reddit_live_collector.py",
                "--start-date", "2024-12-01",
                "--end-date", "2025-02-01", 
                "--tickers", "AAPL", "MSFT", "NVDA",
                "--limit-per-subreddit", "20"
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ Reddit数据收集成功!")
                print(result.stdout)
                return True
            else:
                print("❌ Reddit数据收集失败")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ 脚本执行超时")
        except Exception as e:
            print(f"❌ 执行错误: {e}")
        
        if attempt < max_attempts - 1:
            delay = base_delay * (2 ** attempt)  # 指数退避
            print(f"等待 {delay/3600:.1f} 小时后重试...")
            time.sleep(delay)
    
    print("❌ 所有重试都失败了")
    return False

if __name__ == '__main__':
    retry_reddit_collection()
