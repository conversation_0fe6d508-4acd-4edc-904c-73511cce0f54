{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-02-04", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Windows 10 doesn't have the middle-finger emoji", "content": "I was digging around the emoji shortcut thingy, and I couldn't find the middle-finger emoji. I guess they don't want people to be offensive or smt.   \n  \nP.S: I only posted this here bc Windows 10 subreddit probably has enough people complaining about things like ads, telemetry, etc.", "created_time": "2025-02-04T04:04:10", "platform": "reddit", "sentiment": "bearish", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Great_Leg_4836", "url": "https://reddit.com/r/microsoft/comments/1ih93nt/windows_10_doesnt_have_the_middlefinger_emoji/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Interview with OG Microsoft DevDiv lead <PERSON><PERSON>", "content": "", "created_time": "2025-02-04T11:51:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "itsemdee", "url": "https://reddit.com/r/microsoft/comments/1ihfwvj/interview_with_og_microsoft_devdiv_lead_yuval/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Preferred Work Location for New Grad Role", "content": "I’m a recent graduate starting a fully remote role at Microsoft. I'm considering relocating from the East Coast (DC) to **Seattle** or **Chicago** to boost my early career development.\n\nI loved my internship in Redmond. I was drawn to Seattle’s vibrant city energy, excellent public transit, and stunning nature. The PNW spring/summer climate fits my preference for weather, and being near Microsoft HQ could enhance my learning and networking opportunities.\n\nOn the other hand, Chicago also offers a dynamic tech scene and a large Microsoft office. As a sports fan, I appreciate that both cities are major sports hubs.\n\nI’d love to hear from anyone with experience as a new grad remote worker in Seattle, Chicago, or similar cities. How have your experiences been with learning opportunities, networking, and work-life balance? How did living in your city influence your ability to make friends and build a professional network? Thanks for your insights!", "created_time": "2025-02-04T12:19:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NickoHand", "url": "https://reddit.com/r/microsoft/comments/1ihgd9d/preferred_work_location_for_new_grad_role/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Microsoft Data Engineer Interview", "content": "I had my first interview round on January 28th. The recruiter asked me to be available for the entire day as they planned to conduct all interviews on the same day. \nHowever, it's been almost a week with no updates, despite my follow-ups.\n\nThe interview went well—I was able to answer 90% of the questions confidently.\n\n", "created_time": "2025-02-04T14:33:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Business_Art173", "url": "https://reddit.com/r/microsoft/comments/1ihizm9/microsoft_data_engineer_interview/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Windows on ARM is actually good!", "content": "Hey, \nI recently got a new Copilot+ PC because it was the only good laptop in the price range (Go figure). It has the snapdragon X plus 42-100 in it, 32GB of RAM and 1TB of storage. I got it for $1248 AUD. Anyway, multiple apps I went to install said no arm compatibility and it won't work. I click install and it works flawlessly! Honestly, I was using Linux before getting my laptop and now this is pulling me back to Windows! The battery life is awesome and the fans haven't kicked in once.\nThank you Microsoft and thank you Len<PERSON>!\n\nThe only issue is <PERSON><PERSON><PERSON> doesn't work :(", "created_time": "2025-02-03T07:15:18", "platform": "reddit", "sentiment": "neutral", "engagement_score": 182.0, "upvotes": 98, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1igjk2g/windows_on_arm_is_actually_good/", "ticker": "MSFT", "date": "2025-02-03"}, {"title": "Starting with Engage -  what are your best tips ?", "content": "Hi redittors, just like many other companies, we're switching from Facebook Workplace to Microsoft Engage. I'm rather happy about it because we'll have a single tool for everything.   \nI had specific questions and would also like to have your opinion and best tips about using Engage ?\n\n\\- image size to post on communities: are there ideal size to use? I could'nt find information online yet\n\n\\- discussion VS praise: how do you use praise? how do your communities react to them?\n\nThanks, i'll be happy to discuss it with you !", "created_time": "2025-02-03T11:45:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "helag<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1ign8ou/starting_with_engage_what_are_your_best_tips/", "ticker": "MSFT", "date": "2025-02-03"}, {"title": "Question about Company Store", "content": "My big brother works for MS, so I'm able to access the company store via the Friends ane Family system, right?\nI was wondering if the company store has a \"funds\" system similar to that of Steam, where you can add money into your account to get games with instead of directly using your credit card. Is this system also in the normal MS store included in Windows?", "created_time": "2025-02-03T17:24:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Puzzleheaded_Hat2452", "url": "https://reddit.com/r/microsoft/comments/1igughm/question_about_company_store/", "ticker": "MSFT", "date": "2025-02-03"}, {"title": "If I Had Only Knew.. Before Joining MSFT", "content": "Let me first say Microsoft is a great company. But before accepting the offer there are some things I wish I did a better job of before saying I accept.\n\n* Researched the role a lot more on sites like Reddit and Glassdoor - Titles can be misleading.\n* Knew the level I was coming in at - Not just the number and salary but where you are on the ladder and compare it to where you are now. Be comfortable if you do decide to jump because it might be a while before you move up. Just to note, there are \"firewalls\" between certain levels like 62-63. The criteria to get through the firewalls are not just your average \"I do a good job so promote me.\" So you might want to fight that battle before joining to make sure you come in where you should be based on your experience. A lot of people come in under where they were previously. I've seen VPs come in at Sr. level. Some people just say if the pay is equivalent or better then I'm good, like I did. Microsoft pays more than average and I wouldn't do that again.\n* Knew that what org you come into matters - Don't just think because you are in the building it's easy to change rooms. There are internal biases between rooms and even teams.\n* (As you probably can tell by the recent layoffs) Knew performance matters - I knew this. Duh. Like everywhere performance matters. BUT to others, if you are not a self motivated individual then don't waste your time. Just to note, it's stressful working here. So if you don't do stress well then stay where you are at.\n\nAgain, great company, benefits, learning opportunities and you work with tons of smart people. I'm good.", "created_time": "2025-02-03T17:38:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 342.0, "upvotes": 248, "num_comments": 0, "subreddit": "unknown", "author": "Zestyclose_Depth_196", "url": "https://reddit.com/r/microsoft/comments/1igustt/if_i_had_only_knew_before_joining_msft/", "ticker": "MSFT", "date": "2025-02-03"}, {"title": "Senior Technical Specialist vs Cloud Solutions Architect", "content": "Hi everyone,\n\nHoping to get some clarity on the differences between the roles, and what potential career paths would be after doing either for a few years.\n\nI’m about to take a role as a Specialist for Azure after being a TAM at AWS, and would like to know a bit more from the folks that have been doing it for a while, what it’s like. \n\nAt AWS there are Solutions architects, but they don’t have the specialist role. I know the specialist role is more sales oriented, and I’m excited about that as I do want to get into sales more. But, I don’t quite understand the differences between it and the CSA, since to me, they’re both sales? \n\nAny information would be appreciated! Thanks!\n\nEdit: Clarifying that I’m going into the TS role specifically, didn’t know there was another specialist role that would cause confusion, but good to know that too! Thanks again everyone! ", "created_time": "2025-02-02T16:17:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 94.0, "upvotes": 54, "num_comments": 0, "subreddit": "unknown", "author": "david121131456", "url": "https://reddit.com/r/microsoft/comments/1ig0qgu/senior_technical_specialist_vs_cloud_solutions/", "ticker": "MSFT", "date": "2025-02-02"}, {"title": "Are Office 2024 and Office 365 the same downloaded program?", "content": "I understand the basic difference that 365 is a subscription, but is the actual program you download  different? I'm running it on Mac and I can't find anywhere online whether one operates better than the other or if it's the same exact program, but 365 will offer upgrades down the line. ", "created_time": "2025-02-02T18:47:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "thrillhouse4242", "url": "https://reddit.com/r/microsoft/comments/1ig4c6u/are_office_2024_and_office_365_the_same/", "ticker": "MSFT", "date": "2025-02-02"}, {"title": "Best Microsoft office for windows 7", "content": "Hi \n\nI want to know the latest Microsoft application best to use for Windows 7. \n\nI know this weird question and also i could just easily change the version windows i am using but 2 factors prevent me.\n\n1. Its a corporate computer i.e. for a school and i am not an administrator here i.e. just want to drop them a spreadsheet i built in Excel then I'm off for good.\n\n2. The computer ram is 2gb with Processor: Intel(R) Pentium CPU G3220 @ 3.00GHz, 3000 Mhz , 2 Core(s), 2 Logical Pro... \n\nSo i just prefer to install for them the best supported latest Microsoft Office.\n\nI also built this spreadsheet with Microsoft Office Professional Plus 2021 so earning it an absolute rejection to run on Windows 7.\n", "created_time": "2025-02-01T12:37:31", "platform": "reddit", "sentiment": "bearish", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "garpa<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1if5faw/best_microsoft_office_for_windows_7/", "ticker": "MSFT", "date": "2025-02-01"}, {"title": "Visa transition Delays with MSFT - Seeking Advice", "content": "I received an offer from Microsoft two months ago, but my TN visa transition is still unclear. On top of that, the I-129 petition went to an RFE(Request for evidence), which is surprising since my job and education are perfectly aligned, and I’ve previously obtained TN visas without issues. According to the lawyers, they still haven’t received the RFE details, even though it was issued nine days ago.\n\nI asked them to use a backup option and allow me to apply directly at the port of entry instead, but they have not accepted yet. Things have become very complicated, and I feel incredibly nervous and stressed. I have already notified my current employer about my last day, making the situation even more difficult. My manager at MSFT urgently needs me to start, but this is entirely out of my control.\n\nHas anyone had a similar experience with Microsoft? Is it possible to insist they allow me to apply directly at the port of entry instead of waiting several more weeks for the USCIS decision?\n\n  \nUPDATE: They just informed me that the RFE is straightforward and not complicated, and they are able to submit a response to USCIS soon. ", "created_time": "2025-01-31T05:24:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "TraditionalNet3466", "url": "https://reddit.com/r/microsoft/comments/1ie7q38/visa_transition_delays_with_msft_seeking_advice/", "ticker": "MSFT", "date": "2025-01-31"}, {"title": "Subscription due for renewal but confused about options.", "content": "I have cognitive issues so I'd appreciate this explaining to me as if I was really stupid please. \n\nI have microsoft 365 personal as it came with my laptop. Its due for renewal soonl but I can't afford the £85 they're asking for. I'm happy to downgrade to the classic for £60 (which is what I've budgeted) as I wouldn't be using the AI anyway, but I saw there was a basic option for £20.  I use Word, Excel and Outlook for various things as well as still using my hotmail address for logging into some things (I'm old school and kind of attached to the address I've had for nearly 30 years) , also I never save anything to the cloud. If I was to go for the £20 option would I loose the ability to use word etc on my computer? \n\nThanks ", "created_time": "2025-01-31T12:40:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Rhonda800", "url": "https://reddit.com/r/microsoft/comments/1iedvgs/subscription_due_for_renewal_but_confused_about/", "ticker": "MSFT", "date": "2025-01-31"}, {"title": "How can I see which model Microsoft Copilot uses?", "content": "How can I see which model Microsoft Copilot uses?", "created_time": "2025-01-31T14:59:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>_<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1iegnhf/how_can_i_see_which_model_microsoft_copilot_uses/", "ticker": "MSFT", "date": "2025-01-31"}, {"title": "Can I use microsoft designer ai images on my website?", "content": "Hi! I am not sure what counts as commercial use. I am creating a blog and wanted to use ai images as background and logo. I am a bit confused about what counts as commercial use. I do not want to use the images to sell anything, just to be aesthetic. ", "created_time": "2025-01-31T17:07:02", "platform": "reddit", "sentiment": "bearish", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "NorwegianIsopodFan", "url": "https://reddit.com/r/microsoft/comments/1iejlxb/can_i_use_microsoft_designer_ai_images_on_my/", "ticker": "MSFT", "date": "2025-01-31"}, {"title": "Registering as an individual developer through partner centre", "content": "I don't think this is a support question as I don't have any technical issues.\n\nI have written a WinUI 3 app as a companion to my own tabletop RPG. I only expect a small number of people to use it and don't want to charge for it.\n\nI've tried to register an individual developer account through Microsoft Partner Centre so that I can upload the app to the store, but it wants me to enter all my contact details to create the account, including an address. This is a small personal project, not something I'm creating a business for, and I don't want to put my home address or personal number on the app store for everyone to see.\nI've seen people questioning this online and being reassured that these requirements only exist for Company developer accounts, and not Individual ones, but it seems they are now necessary for individuals too.\n\nIs there some other way I can distribute my WinUI 3 app without putting my home address on the app store? Some of my friends who will be installing this are not technical at all, so I don't think the sideloading instructions provided by Microsoft will be appropriate for them.", "created_time": "2025-01-31T18:10:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 17.0, "upvotes": 11, "num_comments": 0, "subreddit": "unknown", "author": "LonianDave", "url": "https://reddit.com/r/microsoft/comments/1iel5j3/registering_as_an_individual_developer_through/", "ticker": "MSFT", "date": "2025-01-31"}, {"title": "How Are Microsoft’s January 2025 Layoffs Different (for the Worst)", "content": "When <PERSON><PERSON><PERSON> became the CEO of microsoft, it was believed he will be different. He himself told in interviews about the importance of empathy. Where has the empathy suddenly disappeared? \n\nhttps://deepseeks.medium.com/how-are-microsofts-january-2025-layoffs-different-for-the-worst-aa454f061315\n\nWhy is Microsoft behaving like service based companies who do not value their employees. It has labelled many good employees as low performers and then fired. How will this affect their careers?", "created_time": "2025-01-31T20:03:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 914.0, "upvotes": 394, "num_comments": 0, "subreddit": "unknown", "author": "LowerButterscotch556", "url": "https://reddit.com/r/microsoft/comments/1ienult/how_are_microsofts_january_2025_layoffs_different/", "ticker": "MSFT", "date": "2025-01-31"}, {"title": "End of support-Privacy protection (VPN) in Microsoft Defender for individuals on Windows/iOS/MacOS/Android February 28 2025.", "content": "", "created_time": "2025-01-31T21:58:31", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "NanoPolymath", "url": "https://reddit.com/r/microsoft/comments/1ieqk59/end_of_supportprivacy_protection_vpn_in_microsoft/", "ticker": "MSFT", "date": "2025-01-31"}, {"title": "Customer Success Internship: MBA", "content": "Hey everyone,\n\nA week ago, a recruiter from Microsoft reached out to me to apply for their Customer Success Internship since I had the relevant experience. I havent heard anything from the team since. I do realise it would take time but im getting a little worried now. Does anyone know how long it usually takes for them to get back to you? The post for the internship is still active so i havent lost hope.", "created_time": "2025-01-30T00:21:21", "platform": "reddit", "sentiment": "bullish", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Away_Entertainer1963", "url": "https://reddit.com/r/microsoft/comments/1id94nd/customer_success_internship_mba/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Microsoft and TicTok?", "content": "If Microsoft buys TicTok, do you see Microsoft’s stock jump? ", "created_time": "2025-01-30T00:54:02", "platform": "reddit", "sentiment": "bullish", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "TheTriplet1976", "url": "https://reddit.com/r/microsoft/comments/1id9ui3/microsoft_and_tictok/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Someone recommend a good Mail App alternative", "content": "Currently, the (NEW) outlook is just a piece of junk. it's never syncing it doesn't load on startup takes 3 business day for email to enter mailbox. Sent e-mail don't send and finish in queued box for eternity.  \ni'm tire of that bullshit. I just want the old one back use to work perfectly fine.\n\nI've found a way to open the old version, but it had to be done via the new version every single time. because it won't open the old one by default. so you had to go in settings and you find an option there in option---general----about outlook----return to windows mail and calendar. And if you send negative feedback to microsoft they'll just block that feature it'll be gone like it never existed. So yeah. I want an alternative i just can't find any right now. That's why i'm here. **(WARNING AFTER I RETURN TO OLD VERSION, ALL OF THE ATTACHED FILE SUCH AS LINKED PDF THAT I RECEIVED FROM SCAMMER AND SPAM EMAIL WERE FOUND ON THE PC BY MY ANTIVIRUS, THIS IS HIGH RISK AND I STRONGLY SUGGEST NOT TO RETURN TO OLD MAIL BECAUSE OF THAT, COULD POTENTIALLY ARM YOUR PC AND PUT YOUR FILES AT RISK)**\n\nI guess the most important parts is FREE and sync well and do what it's suppose to do. also allow to have multiple e-mail. Because outlook (new) just isn't working. Now i use my phone or just link phone to pc to write large email. it's soo annoying. i just can't rely on outlook (new) anymore. so many time it failed to do what it's suppose to do.", "created_time": "2025-01-30T00:57:17", "platform": "reddit", "sentiment": "bullish", "engagement_score": 18.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Secret_Fisherman_292", "url": "https://reddit.com/r/microsoft/comments/1id9xd9/someone_recommend_a_good_mail_app_alternative/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Microsoft's launches new Surface Laptop and Surface Pro laptops with 22 hours of battery life", "content": "", "created_time": "2025-01-30T15:16:28", "platform": "reddit", "sentiment": "neutral", "engagement_score": 176.0, "upvotes": 154, "num_comments": 0, "subreddit": "unknown", "author": "nick314", "url": "https://reddit.com/r/microsoft/comments/1idp76m/microsofts_launches_new_surface_laptop_and/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Fix the lock screen", "content": "A screen timeout has to let you be able to revert to lockscreen. It's the oldest reason for a lockscreen. In Windows 11, you need to have Bluetooth enabled, and have your phone tracked so the computer monitors you proximity and locks the screen that way.\n\nPlease add the option to just lock it after a time. You can make it sleep but the option isn't there for screen locking.", "created_time": "2025-01-30T17:42:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Zealousideal_Meat297", "url": "https://reddit.com/r/microsoft/comments/1idsnd4/fix_the_lock_screen/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Microsoft interested in buying TikTok", "content": "I guess its Name would Change into Microsoft TikTok365. Or do you have you have any Suggestion TikTok new Name? ", "created_time": "2025-01-30T22:18:39", "platform": "reddit", "sentiment": "bullish", "engagement_score": 44.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "smokinggunss", "url": "https://reddit.com/r/microsoft/comments/1idz8my/microsoft_interested_in_buying_tiktok/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Getting into Microsoft as a contractor", "content": "I may be offered a 4-month (possibly 6-month with extension) contract as a Program Manager at Microsoft through a staffing agency. What are the odds of me getting direct hire after contract lapses? Or this may be a game where the carrot is full-time employment but the contract keeps renewing or worse still canceled? ", "created_time": "2025-01-30T23:12:28", "platform": "reddit", "sentiment": "neutral", "engagement_score": 23.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Kool99123", "url": "https://reddit.com/r/microsoft/comments/1ie0hl6/getting_into_microsoft_as_a_contractor/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Is there like a Lite Version of Office?", "content": "Like i just download LibreOffice and the whole package is 300MB\n\nCompared to my Licensed Office ProPlus LTSC 2019 which is +4GB\n\nLike i litterally use ot like 3 times a month to edit a word document or make a simple presentation.\n\n\n\nEdit: if the post is not clear\n\nI am asking sbout a specific version of office or modified office, not an alternative to office suite\n\nLike is there a version called Office 2021 Basic that has like only word, powerpoint, excel, and access for under 1GB in file size, removing other functionality like OneDrive and onenote integration and offloading the smartArt database to cloud(as office 365)", "created_time": "2025-01-29T03:49:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 29.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Bebo991_Gaming", "url": "https://reddit.com/r/microsoft/comments/1icl0xg/is_there_like_a_lite_version_of_office/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Microsoft CEO <PERSON><PERSON><PERSON> touts DeepSeek's open-source AI as \"super impressive\": \"We should take the developments out of China very, very seriously\"", "content": "Microsoft's CEO says AI developments from China should be taken very seriously amid the DeepSeek AI frenzy.", "created_time": "2025-01-29T04:03:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 775.0, "upvotes": 693, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1icla6d/microsoft_ceo_satya_nadella_touts_deepseeks/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Disconnecting Microsoft Services", "content": "Hey guys, I work a lot with Microsoft Software for my Job. Outlook, OneDrive, 365 and so on and my girlfriend does too. My Computer broke some days ago and so I logged into my Microsoft Accounts on her Laptop and now since my Computer Works again I wanted to disconnect all Thors Services of mine from her Laptop. I logged off all Services but she still has all my data on her one Drive and also gets all my contacts as recommendations on her 365 which annoys her a lot. Any idea how to fully disconnect again?", "created_time": "2025-01-29T14:09:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "_Naydra_", "url": "https://reddit.com/r/microsoft/comments/1icukn5/disconnecting_microsoft_services/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Is the Segoe UI (Variable) fonts free to use?", "content": "Hey everyone\n\nI’ve been wondering if the fonts have a license or rights similar to Roboto- where they can be used without extra stairs? I’ve been using the font at my organization for the past 4 years.\n\nI <PERSON><PERSON> thought Microsoft kinda allows that unlike Apple.", "created_time": "2025-01-29T14:13:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "new-romantics89", "url": "https://reddit.com/r/microsoft/comments/1icunui/is_the_segoe_ui_variable_fonts_free_to_use/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "iPhone and Android integration can be done with the start menu of Windows 11.", "content": "", "created_time": "2025-01-29T17:52:50", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Novel_Negotiation224", "url": "https://reddit.com/r/microsoft/comments/1iczvii/iphone_and_android_integration_can_be_done_with/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Microsoft and OpenAI investigate whether DeepSeek illicitly obtained data from ChatGPT", "content": "", "created_time": "2025-01-29T18:42:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 180.0, "upvotes": 90, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1id14ac/microsoft_and_openai_investigate_whether_deepseek/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Imagine having unparalleled brand capital with the sinplest of words \"Office\" and ditching it entirely for the catchy \"Microsoft 365 copilot\"", "content": "Honestly, what are Microsoft doing?", "created_time": "2025-01-29T20:13:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 279.0, "upvotes": 193, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1id3ctn/imagine_having_unparalleled_brand_capital_with/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Microsoft Tops Global Game Sales Charts in December 2024", "content": "", "created_time": "2025-01-29T21:01:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "johanas25", "url": "https://reddit.com/r/microsoft/comments/1id4hz9/microsoft_tops_global_game_sales_charts_in/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "The Current State of Software Engineering Interview is Deeply Flawed", "content": "Someone posted this on LinkedIn:\n\n*The current state of software engineering interviews is deeply flawed. A friend of mine is considering leaving their job just to focus on studying full-time for interviews. Think about that—interviews have become so demanding and disconnected from day-to-day work that candidates feel the need to dedicate months solely to preparation.*\n\n*This isn’t just about solving complex algorithms or mastering system design; it’s about creating a process that values practical skills, creativity, and the ability to collaborate—qualities that truly define great engineers.*\n\n*We need to ask ourselves: are we testing for the right things? Or are we unintentionally gatekeeping talent by prioritizing who can memorize LeetCode problems over who can build scalable, impactful software?*\n\n[Post | Feed | LinkedIn](https://www.linkedin.com/feed/update/urn:li:activity:7288556194070200321/)\n\n  \nHaving interviewed for a SWE role and worked for other big non-tech companies. I would say the interview is deeply flawed at Microsoft. I've never seen a place that is more focused on algorithm and design pattern knowledge. Solving LeetCode problems, You can be passionate about the work, hard-working, eager to learn and growth, have a breath of knowledge, creative, able to collaborate and work with others but if you can't code a link list in C# (which is something rarely done or used) then no hire. I would like to see the SWE in Test roles brought back but it may be too late. ", "created_time": "2025-01-29T21:45:51", "platform": "reddit", "sentiment": "bullish", "engagement_score": 20.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "Zestyclose_Depth_196", "url": "https://reddit.com/r/microsoft/comments/1id5l09/the_current_state_of_software_engineering/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "is it possible to have microsoft family but only with the time limits?", "content": "Awhile back my father put microsoft family on my computer, only for the time limit though. With microsoft family you’re going to have restrictions too, is there a way to have only the time limits and no restrictions?", "created_time": "2025-01-28T00:05:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 37.0, "upvotes": 31, "num_comments": 0, "subreddit": "unknown", "author": "Kind_Lie6930", "url": "https://reddit.com/r/microsoft/comments/1ibobty/is_it_possible_to_have_microsoft_family_but_only/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Amazon Takes Office Space at New Tower in Miami's Wynwood Area - Bloomberg.com", "content": "", "created_time": "2025-01-28T02:00:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1ibqpe5/amazon_takes_office_space_at_new_tower_in_miamis/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Microsoft CEO <PERSON><PERSON><PERSON> calls himself ‘product’ of bond between India-US", "content": "", "created_time": "2025-01-28T09:15:47", "platform": "reddit", "sentiment": "bullish", "engagement_score": 367.0, "upvotes": 297, "num_comments": 0, "subreddit": "unknown", "author": "HindustanTimes", "url": "https://reddit.com/r/microsoft/comments/1ibxnzp/microsoft_ceo_satya_na<PERSON><PERSON>_calls_himself_product/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Seeking Insights on New Grad Remote Role Relocating", "content": "I’m a recent graduate starting a fully remote role at Microsoft. To boost my early career, I'm considering relocating from the East Coast (DC) to Seattle or Chicago. \n\nI loved my internship in Redmond. I was drawn to Seattle’s vibrant city life, excellent public transit, and stunning nature. The PNW spring/summer climate fits my preference for weather, and being near Microsoft HQ could enhance my learning and networking opportunities.\n\nOn the other hand, Chicago also offers a dynamic tech scene and a large Microsoft office. As a sports fan, I appreciate that both cities are major sports hubs.\n\nI’d love to hear from anyone with experience as a new grad remote worker in Seattle, Chicago, or similar cities. How have your experiences been with learning opportunities, networking, and work-life balance? How did living in your city influence your ability to make friends and build a professional network? Thanks for your insights!", "created_time": "2025-01-28T14:12:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "NickoHand", "url": "https://reddit.com/r/microsoft/comments/1ic2f8k/seeking_insights_on_new_grad_remote_role/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Client questionnaire and dependent outcome", "content": "Hi Guys. \n\nAfter some advice, please. Which programs or platforms within the Microsoft architecture (or 3rd party) should I use for the following use case.\n\nI would like to send a questionnaire out to customers who then answer a series of questions which, dependent on their answers, result in them being linked to a particular set of services unique to their answers.\n\nIs it a case of using any of these maybe?\n\n-  ‘Forms (with branching )?\n- Power automation? \n\nIf I’m honest, I don’t really understand how to use Forms even yet , so go easy on the technical side! \n\nThanks\n\n<PERSON>.", "created_time": "2025-01-28T19:47:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "DannykGolf1979", "url": "https://reddit.com/r/microsoft/comments/1icaefj/client_questionnaire_and_dependent_outcome/", "ticker": "MSFT", "date": "2025-01-28"}], "metadata": {"timestamp": "2025-07-06T22:23:25.662348", "end_date": "2025-02-04", "days_back": 7, "successful_dates": ["2025-02-04", "2025-02-03", "2025-02-02", "2025-02-01", "2025-01-31", "2025-01-30", "2025-01-29", "2025-01-28"], "failed_dates": [], "source": "local"}}}}