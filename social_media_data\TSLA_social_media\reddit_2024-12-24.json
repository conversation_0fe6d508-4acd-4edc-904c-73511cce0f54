[{"platform": "reddit", "post_id": "reddit_1hl1jxk", "title": "RUMBLE IS ABOUT TO 🚀 even more ", "content": "It went up by 82% today and $RUM havent even started the squeeze yet. 1 million additional short shares were added just in the last two weeks brining short interest to near an all time high of 16 million short shares. A reminder that insiders own 62%, dan b<PERSON><PERSON> owns 10% and institutions own 26%. That&#39;s 98% of the shares. Where the heck did short come up with 16 million shares to even short?", "author": "CryptographerKind366", "created_time": "2024-12-24T00:31:08", "url": "https://reddit.com/r/pennystocks/comments/1hl1jxk/rumble_is_about_to_even_more/", "upvotes": 0, "comments_count": 7, "sentiment": "bearish", "engagement_score": 14.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl23lb", "title": "How do you manage the post-holiday \"traffic drop\" (E-commerce)", "content": "Just as the title says, this is my first <PERSON> managing Google Ads. During December, we ran the max budget we were comfortable with and had a great month. However, traffic started to die down, and don't want my profits going to waste.\n\nWhat do you guys do during this time? Decrease budget? Turn on/off other campaigns? If someone can give me the rundown on how they handle their e-commerce campaigns during this post-holiday traffic drop, I would love to hear for some insight!", "author": "ProfessionalGoat99", "created_time": "2024-12-24T01:00:22", "url": "https://reddit.com/r/adwords/comments/1hl23lb/how_do_you_manage_the_postholiday_traffic_drop/", "upvotes": 2, "comments_count": 2, "sentiment": "bearish", "engagement_score": 6.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl2fmh", "title": "Is it realistic to make $350 a day for a whole year using 25K-30K for every trade. ", "content": "I was wondering if this was possible. This is my 3rd year trading, took big losses and took some nice small gains but they where consistent.\n\nEverytime I went for small gains. Between $200 and $500 I made out good. But when I went after the big profits between 1K and 2K that's when I lost.\n\nI was just thinking if I stuck to small gains throughout the whole year and not get greedy and have super discipline. Was this actually achievable and realistic. Obviously I know taxes and all that. \n\nNumbers don't lie. But humans discipline does get tested.\n\nI wonder what yall think.", "author": "LIONHEART369", "created_time": "2024-12-24T01:18:18", "url": "https://reddit.com/r/StockMarket/comments/1hl2fmh/is_it_realistic_to_make_350_a_day_for_a_whole/", "upvotes": 0, "comments_count": 156, "sentiment": "neutral", "engagement_score": 312.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl2vby", "title": "How much rent can I afford a month if I bring home $1200 a week after taxes? ", "content": "I decided to just say screw it and start putting in 58-60 hours a week  because 50k isn't enough to move into my own place when rent is around 1500 a month. Anything below that and well it would look like homeless people stayed in it and bugs everywhere with holes in the floor. Before I did overtime I would bring 560 one week and 800 the next week. I do have a 300 bi weekly car payment. But I feel like $1200 a week should be more than enough for a 600 a month car payment and 230 insurance and maybe 1700 rent and utilities. ", "author": "Reverseflash202", "created_time": "2024-12-24T01:41:57", "url": "https://reddit.com/r/personalfinance/comments/1hl2vby/how_much_rent_can_i_afford_a_month_if_i_bring/", "upvotes": 0, "comments_count": 76, "sentiment": "neutral", "engagement_score": 152.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl5meu", "title": "Thoughts on buying followers", "content": "# \n\nI've read on many posts that buying followers is a waste of money, since the goal is real engagement.\n\nHowever, consider the following scenario.\n\nI just started my instagram page creating content. I post about 1 video per day. While I want to increase my follower count, some people that might want to follow me might actually not because they see I have close to no followers (close to 1 right now). If I bought followers once, it may signal to users who like my content that others do as well, and will follow me. If they see a page with no followers, they won't follow my account.\n\nThoughts?", "author": "Interesting-Sir-3774", "created_time": "2024-12-24T04:20:46", "url": "https://reddit.com/r/marketing/comments/1hl5meu/thoughts_on_buying_followers/", "upvotes": 1, "comments_count": 26, "sentiment": "bullish", "engagement_score": 53.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl6u53", "title": "<PERSON> was a Penny Stock Investor", "content": "<PERSON> was a penny stock investor back in his day, and it started him on the path to becoming one of the richest men in the world. Guess what Berkshire’s market cap was back in the day? $10million inflation adjusted to $100 million today. Berkshire Hathaway was an undervalued penny stock. \n\nNowadays with algorithms and such, it’s even harder to find inefficiencies in the market for large cap stocks that have all the big institutions trading them. Trust your gut, and realize that even though <PERSON><PERSON><PERSON> might encourage you to invest in certain things because it benefits him, the best move is to invest in undervalued things that will benefit you in the long run when the market realizes it’s value. \n\nGood luck, stay hungry.", "author": "Suspicious_Demand_26", "created_time": "2024-12-24T05:36:09", "url": "https://reddit.com/r/pennystocks/comments/1hl6u53/warren_buffet_was_a_penny_stock_investor/", "upvotes": 169, "comments_count": 38, "sentiment": "bullish", "engagement_score": 245.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl78oa", "title": "AI Investment Opportunities - Big Tech Bets Big", "content": "I've been eyeballing the AI scene lately, and holy cow, the investment hype is still sky-high! They're saying tech giants are gonna drop trillions on AI in the coming years. Like, OpenAI isn't even planning to drop a new model like GPT-5 in 2024, they're just polishing what they've got to stay on top. Smart move, right? It's like they're giving us a heads-up to jump into AI investments before it's too late.\n\nThink about it - if these big dogs are ready to throw down that much cash, there's gotta be some serious moolah to be made. AI isn't some flash in the pan; it's reshaping everything from cloud computing to how we make chips, and even software. It's just getting started. IBM's been killing it with AI in business solutions, showing us how deep this can go.\n\nBut it's not just about the big names. There are these smaller companies doing cool, niche stuff that could really shake things up. Take Richtech Robotics, for example. They're blending AI with robotics in a way that's both smart and useful. \n\nThen you've got companies like NVIDIA, which is like the king of AI chips, and [C3.ai](http://C3.ai), which is doing some slick stuff with AI software for different industries. These guys, along with Richtech Robotics, are proving how AI can be the game-changer in all sorts of fields.\n\nAI's not just for tech nerds anymore; it's hitting education, healthcare, finance - you name it. It's making things smarter, faster, and more personalized. Like, in finance, AI can sniff out fraud or predict market swings, and in healthcare, it's helping with everything from diagnosis to treatment plans. NVIDIA's moves in medical AI are a clear sign of where things are heading.\n\nInvesting in AI isn't just about picking the flashiest tech; it's about spotting who can actually make that tech work for real-world stuff. The AI scene is gonna be all over the place, with tons of opportunities popping up left and right.\n\nSo, if you're into tech and looking to invest, now's the time to get into AI. Don't just focus on the big players; there's potential in these smaller, scrappier companies like Richtech Robotics, Serve Robotics, and UBTECH Robotics. Let's keep an eye on this together!", "author": "roycheung0319", "created_time": "2024-12-24T06:02:01", "url": "https://reddit.com/r/investing_discussion/comments/1hl78oa/ai_investment_opportunities_big_tech_bets_big/", "upvotes": 1, "comments_count": 2, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl7rko", "title": "I built a tool to save hours on data visualization", "content": "Hello everyone,\n\nI have been working on an AI tool which generates good visualization and gives quick insights on your CSV file. This process happens in less than a minute thus saving the process of preparing your datasets and planning the visualizations. \n\nWhat do you think about such tool? Would you like to use it?", "author": "mecharan14", "created_time": "2024-12-24T06:38:32", "url": "https://reddit.com/r/analytics/comments/1hl7rko/i_built_a_tool_to_save_hours_on_data_visualization/", "upvotes": 0, "comments_count": 17, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hl8ynk", "title": "Should I increase my 401k contribution? ", "content": "Hello, tldr and edits at bottom\n\nI'm 26 and make $77k/yr. My company offers 20% match up to the 401k limit which I definitely want to take advantage of. So every $10 I put, they put $2. All the way to the maximum amount.\n\nI currently contribute 15% and have been considering increasing it to a final resting place of 20.8% (idk if I can actually do .8, 20% is fine too), so that with company match I'm contributing ~25% of my income. \n\nMy dilemma arises from a Roth IRA that I am currently able to max out each year. But i don't think I'll be able to do so if I increase my 401k contributions by another 5%. By doing I'll be be able to contribute around $1000 less to my Roth IRA, unless I have a strict budget that doesn't have a lot of space for fun, which at my age I'm not really interested in. \n\nThat's really the only conflict I have with the situation. Should I increase my 401k contribution at the cost of not being able to max out my Roth IRA? I don't make enough to max my 401k.\n\nTLDR\nShould I increase my 401k contribution at the cost of not being able to max out my Roth IRA by about $1000?\n\nEdit: To clarify my company contributes 20% for everything I put. So $2 for every $10 I put up to the 401k limit.\n\nEdit #2: I appreciate everyone's responses! I've decided that I'll stop contributing to my Roth IRA entirely until I max out the 401k. \n\nThen dump whatever is left into my Roth IRA, until I make enough to max it out as well. Maybe tack on some Uber delivery driving to add a couple thousand to the Roth IRA without too much of a grind. I also still want to have fun in my late twenties. ", "author": "FrequentAd284", "created_time": "2024-12-24T08:02:32", "url": "https://reddit.com/r/FinancialPlanning/comments/1hl8ynk/should_i_increase_my_401k_contribution/", "upvotes": 16, "comments_count": 45, "sentiment": "bearish", "engagement_score": 106.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlc4la", "title": "Are there any people here whose fire plan is gym and cook 😂", "content": "I love this subreddit. I constantly see posts with people's fantastical fire plans and TBH they sound horrible to me which makes me doubt myself. I hate traveling and have zero desire for adrenaline spiking activities in general. I like being home, I like daily boring routine...when I think of fire I think of all the girls in my neighborhood who get to go to the gym every day, go to the grocery and choose dinner ingredients and come home to cook thought out healthy meals. I fantasize daily about my FIRE future and it looks more like gym, cook, read, hopefully spend time with future grandkids. I think my most exciting plan is to maybe learn a light craft although my ADHD laughs at me.Maybe this is a response to my stress at work which provides enough adrenaline rushes for a lifetime-I'm not sure but I'm beginning to wonder if my fire plans are going to backfire once I actually get there.\nFyi- I'm 36F with four kids and I think many people here are M and single or married without kids. \nAnyways, would love to hear the fire plans of people like me ?", "author": "lollipop984", "created_time": "2024-12-24T11:58:53", "url": "https://reddit.com/r/Fire/comments/1hlc4la/are_there_any_people_here_whose_fire_plan_is_gym/", "upvotes": 1308, "comments_count": 281, "sentiment": "neutral", "engagement_score": 1870.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlci98", "title": "Merry Christmas! Don't forget to pay your devs! lol", "content": "Photo not mine! CTTO\nHappy Holidays to everyone! 🙏🎉 ", "author": "rojo_salas", "created_time": "2024-12-24T12:24:57", "url": "https://reddit.com/r/webdev/comments/1hlci98/merry_christmas_dont_forget_to_pay_your_devs_lol/", "upvotes": 2436, "comments_count": 80, "sentiment": "neutral", "engagement_score": 2596.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hldfop", "title": "<PERSON>'s Lighthouses: The Idea That Helped Make Me A Multi-Millionaire", "content": "Going through a mental-health crisis comes with plenty of challenges, but when you’re laid off and too ill to work, lack of income can compound the problem by adding stress at the exact worst time imaginable. In the summer of 2023, after four days in a literal cave and several weeks of hospitalization, I began my recovery by walking the miles and miles of hiking trails surrounding Sewanee University at the top of Monteagle Mountain.\n\nThe countless hours of alone time and exercise was helpful, and I could feel myself making progress, but I still had no means of income, which made me feel like a complete piece of doo-doo. And while I worked to become a more rational thinker, the stock market became my world in the woods where I live-streamed CNBC, listened to podcasts, YouTube interviews, and audiobooks while I walked some 10-14 miles per day through the Tennessee hills.\n\nThe whole concept of “deep learning” and how different AI models were being fed a deluge of content in order to become better and more efficient at processing data intrigued me. I played with Chat GPT, told it to do different things, and found it absolutely fascinating when, in three seconds, the language model obeyed my command:\n\n“Write a 1,200-word, three-point essay about <PERSON>’s book, *The Intelligent Investor*.”\n\nThe AI answer was probably the most-coherent summation of “Mr. Market” that any washed-up journalist could’ve hoped for in the middle of those mountains.\n\nAnd while I hunted for wild mushrooms and walked beneath the brilliant fall foliage, I wondered what would happen if I tried a “deep-learning” experiment on myself.\n\nWould it really work?\n\nI mean, if I essentially tried to download hours of stock-market information into my mind, could the scrambled input of audio content—absorbed at chipmunk speed—produce a baseline financial acumen to better help me evaluate stocks/investments?\n\n$600k later, I knew the answer was surely, “YES!” Which made me totally rethink what I thought was the shittiest situation a person could be in—laid off and completely out of unemployment insurance, with no job prospects, and a damn mini fortune that miraculously fell into my lap after only a 6-week mental-health exercise!\n\nShit. Maybe getting laid off and losing my dream-job as the Tennessee Valley Authority's lead (environmental stewardship/energy) journalist wasn’t such a bad thing after all, I thought. And if I could make $600k in six weeks, which would have taken a damn-near decade in the real world, did it really make sense to go back into journalism?\n\nI can still remember the exact spot on the trail where I stopped to bookmark a passage from Albert Einstein’s Memoir, *Out of My Later Years*.\n\nHis point was that Charles Darwin would have never been able to make the same contribution to society if he hadn’t had time to think. And on the contrary, if he had been a full-time professor instead of a full-time researcher, teaching would have prevented him from having the time to travel the world and document the extensive findings that today still serve as the very foundation of evolutionary biology.\n\nAnd to further emphasize the point, Einstein recommended that all the world’s brilliant young people be given jobs in lighthouses, so they would have time to think while getting paid for their time.\n\nThe suggestion made perfect sense to me, because it was the very reason why I had chosen NOT to climb the corporate ladder—even when offered better pay. Because I knew, that extra $10k—or extra $30k-$50k in the case of some bullshit management job, came with a shit-ton of extra hours and around-the-clock federal bureaucracy that only a title-hungry moron would enjoy. And what the fuck for?!\n\nThe more I thought about Einstein’s suggestion, the more I wanted to implement it. Because if I truly wanted to have financial freedom, I knew I needed a lighthouse job that would give me time to think while I earned a living wage and health insurance for my family.\n\nScrew making the big bucks! All I needed was enough money to live while I invested in myself.\n\nAnd by god, I knew exactly where to find a lighthouse job in 2024. Power Plant Operator, baby!\n\nBreak out the old books from my days as an assistant unit operator in coal, upgrade to natural gas, then sit in a chair for hours on end while I did a deep-dive into the stock market and grew my net worth.\n\nAnd what do you know, the plan worked! And I made more in eight months sitting on my ass inside a powerhouse than I ever did in the 40 years of farm work, pouring concrete, rodding fly-ash hoppers, cutting lawns, splitting firewood, and writing news stories for the federal government.\n\nSo before you take that big promotion, which you know is going to add at least 20 hours to your workweek and destroy your home/work-life balance, ask yourself what shitting on any chance you have to grow life-changing wealth is truly going to cost you.\n\nIs that big, fancy title, and the prestige of having subordinates, really worth the trade?\n\nThere’s been so many folks who have told me on this blog that their career is too time consuming, and there’s no way they could ever learn all this stock stuff because of work.\n\nWell, maybe it’s time for a volunteer pay cut, a lighthouse job, and a big Fuck You to that executive-level dipshit who wants you to sell your soul to the company. And if you’re a blue-collar guy, maybe it’s time to let the phone ring, let the overtime slots pass you by, get better sleep, and spend your off days completely investing in yourself and a future with the only people you truly care about.\n\n**Reading List:**\n\n1. **The Psychology of Speculation (Henry Howard Harper)**\n2. **Rich Dad Poor Dad (Robert Kiyosaki)**\n3. **Think and Grow Rich (Napoleon Hill)**\n4. **Outliers (Malcom Gladwell)**\n5. **The Psychology of Money (Morgan Housel)**\n6. **The Snowball: Warren Buffett and the Business Life (Alice Schroeder)**\n7. **David and Goliath (Malcom Gladwell)**\n8. **Rationality (Steven Pinker)**\n9. **Moneyball (Michael Lewis)**\n10. **Poor Charlie's Almanack (Peter Kaufman)**\n11. **Seeking Wisdom: From Darwin to Munger (Peter Bevelin)**\n12. **Thinking in Bets (Annie Duke)**\n13. **The Tao of Warren Buffett (Mary Buffett)**\n14. **The Tao of Charlie Munger (David Clark)**\n15. **The Intelligent Investor (Ben Graham)**", "author": "No_Put_8503", "created_time": "2024-12-24T13:22:20", "url": "https://reddit.com/r/ValueInvesting/comments/1hldfop/einsteins_lighthouses_the_idea_that_helped_make/", "upvotes": 0, "comments_count": 44, "sentiment": "bearish", "engagement_score": 88.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hldz6m", "title": "The Walmart Effect. New research suggests that the company makes the communities it operates in poorer—even taking into account its famous low prices.", "content": "", "author": "Throwaway921845", "created_time": "2024-12-24T13:53:05", "url": "https://reddit.com/r/Economics/comments/1hldz6m/the_walmart_effect_new_research_suggests_that_the/", "upvotes": 14188, "comments_count": 304, "sentiment": "neutral", "engagement_score": 14796.0, "source_subreddit": "Economics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hleifj", "title": "Baby on the way! 31 yo, make $250k-$325k a year from my job. Are we in a good spot? Financial advice with our current setup?", "content": "As title suggest. First baby on the way!! 31 years old. Wife is 29. At a minimum, I will always make at least $250k. This year I will finish about $290k. Joined a new company in February of 2024 so my annual $50K of RSUs start kicking in 2025 (and I get a stock refresh every year - started with $200/4yr).\n\nMy wife makes about 18k a year working part time. She’s 6 months pregnant, and will not be working anymore when baby arrives. \n\nWe both have high anxiety when it comes to finances and although she doesn’t make much, it’s always a bit nerve wracking to forfeit income. We have put off having children until now since we wanted to have our lives in order first. \n\nI have been fortunate to make the money I have been making (and hasn’t always been the case. But not the most financially literate and have dedicated a lot this year to being better.\n\nHere’s our breakdown. Any advice? Things I should change/do differently? I know I’m lacking a Roth but wouldn’t I have to do a backdoor Roth? Whatever that even means??\n\n- $50k in a checking account (working on getting this to just the amount I need each month, baby steps)\n- $18K in Robinhood. Mostly VOO & TSLA\n- $10K in a HYSA emergency fund w/ SoFi\n- $55K in a target fund from prev 401K\n- $23K in 401K from current employer \n- $15K in an old 401K target fund from many years ago\n- $150K in stock from prev company \n- 5k across Crypto/Bonds/physical cash\n- 2 houses - 1 rental + home \n- Rental yields us 500 a month (+$12K in a Truist account for emergencies)\n- Home Mortgage+Taxes+Interest+Insurance= $4700 /month \n- 2 vehicles & a BX tractor 🚜 all fully paid off\n\nDebts:\n- ~19K in combined student loans \n- no CC or personal debt\n- ~350k on rental mortgage (valued at $650K)\n- ~600k on home (valued at 825K)\n\nI also am a bit worried of our spending. Monthly statements are usually anywhere from $4k-$7k (but some of these charges are work expenses I eventually recoup from submitted receipts - but doesn’t help with my sanity or make it any more clear on what’s our doing vs work).\n\nMy biggest concern is how “all over the place” our money feels right now. It’s hard to keep track of what is where and I know it’s good to be diverse, but is this too much diversity? Is there a one stop shop/app you’d recommend? \n\nAm I on track to retire in ~30 years while still being able to give my future children a leg up when I’m gone?\n\nAnyway, hope laying everything out helps. I appreciate any and all advise for this new dad to be who is certainly anxiety ridden. And let me know if there are more details I can add for more context/clarity ", "author": "jpgnewman195", "created_time": "2024-12-24T14:21:34", "url": "https://reddit.com/r/FinancialPlanning/comments/1hleifj/baby_on_the_way_31_yo_make_250k325k_a_year_from/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hleofl", "title": "The Pixel 9 Pro XL is the best phone I’ve ever owned..", "content": "I’m just a dude in my mid 40s who is very tough on his devices (travel extensively for business, use my phone A LOT for productivity) and has been huge fan of mobile phones for as long as I can remember.  For some context, I’ve owned many iPhones starting with the iPhone 3G, 4/5/6/7/8/x/12pm/14pm etc as well as many Nokia phones (N8,N9,N95,N97,N97 Mini, Lumia series etc), BlackBerries and of course Android phones (HTCs, Nexus 4,5,6P,Pixel 2XL, Pixel 7P,8P and now 9P XL).\n\nThis is just an appreciation post for the Pixel 9 Pro….  While most of my Android phones over the years have been pretty decent, they’ve always struggled in some way or other - whether it be poor battery life, overheating, janky software, poor build quality - you get the idea, but I’ve still always preferred the flexibility of Android over iOS as an operating system.  \n\nToday I can confidently say the Pixel 9 Pro is the best phone I’ve ever had - it’s absolutely the complete package.  I finally have a phone that has a great battery, stable software, incredible camera and simply just works as advertised.  Not to mention, it’s doing all of this for a great value imo.  I haven’t been this smitten with a phone since the Nokia N9 (if you know, you know)\n\nWhen I look at the shit show that is iOS 18 right now and the laughably stale iPhone 16 PM (not to mentioned ridiculously overpriced) with hacked together AI features (and tragedy of a photos app) - I feel like Google is finally prepared to make a serious dent in the mobile space.\n\nWishing you all a happy holiday season! \n\n\n\n\n\n", "author": "cipher29", "created_time": "2024-12-24T14:30:34", "url": "https://reddit.com/r/GooglePixel/comments/1hleofl/the_pixel_9_pro_xl_is_the_best_phone_ive_ever/", "upvotes": 489, "comments_count": 165, "sentiment": "bullish", "engagement_score": 819.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlf4ex", "title": "We're an all-EV family now :)", "content": "", "author": "EorEquis", "created_time": "2024-12-24T14:53:59", "url": "https://reddit.com/r/electricvehicles/comments/1hlf4ex/were_an_allev_family_now/", "upvotes": 991, "comments_count": 105, "sentiment": "neutral", "engagement_score": 1201.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlhefk", "title": "Seeking Advice on Eating Insects?", "content": "I'm interested in the idea of eating insects as a sustainable lifestyle choice, even if that makes me sound like a stereotypical evil liberal in some right-wing fantasy. I remember trying dried crickets and they were certainly edible. With seasoning/cooked in recipes, they could be delicious. My reasoning for wanting to eat insects is that I rarely consume meat and animal products, largely for sustainability's sake, and I find it is difficult to always get enough protein every day (please don't argue about how easy it is/give solutions unrelated to bugs in response to this post).\n\nI've heard insects are a sustainable source of protein, so I'm trying to evaluate whether insect consumption could actually be practically implemented in my everyday life.\n\nI have some queries though....\n\nAre crickets the best sort of insect to eat? I personally, however irrationally, would prefer to eat things that are less wriggly and worm-like, and certainly nothing slimy, so things like mealworms are probably out of the question.\n\nFirstly, as a source of protein, are crickets/insects actually that worthwhile? Do crickets/insects actually provide more protein/gram than natural plant-based sources of protein such as lentils etc. If I would have too regularly consume an ungodly ammount of crickets/insects, then I am uninterested, as part of the reason I'm seeking unconventional protein sources other than meat etc is because I want to get enough protein from a relatively normal diet without eating loads of one thing in one go. \n\nSecondly - how would I source edible insects, such as crickets, affordably and sustainably? The only insects I could find online that are sold for human consumption are being sold as a gimmick, because not enough people want to eat insects for it to be commercially viable otherwise. As a result they're prohibitively expensive for me. It seems a bit strange, and potentially unsafe, to consume insects intended for purposes such as reptile feed. Are my concerns unfounded? \n\nWhat's the feasability of farming crickets myself? \n\nAny advice or knowledge would be much appreciated! Also, I'm aware that this subject seems to make some people very angry (food actually appears to be an extremely culturally sensitive subject, interestingly) so please remember that I am not the personification of any cultural movement or view of life that you may disagree with. \n\nThanks :)\n\n\n\n\n\n\n\n", "author": "cosmicmatt15", "created_time": "2024-12-24T16:48:09", "url": "https://reddit.com/r/sustainability/comments/1hlhefk/seeking_advice_on_eating_insects/", "upvotes": 7, "comments_count": 11, "sentiment": "bullish", "engagement_score": 29.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlhkpo", "title": "How I’m Learning to Code After Selling a SaaS for 5 figures - now I need to build it 🙈", "content": "Hi all,\n\nI’m a beginner developer, and I wanted to share a wild story: I sold my SaaS *before I even knew how to code*. Now I’m learning everything I need to deliver the product.\n\nHere’s the backstory:  \nI noticed Pilates studios needed a way to track trainee progress and plan sessions.   \nI pitched the idea with mockups and signed my first client.  \n But I knew I wanted to build it myself—so here I am, learning to code with a real-world deadline.\n\nF\\*\\*king high stakes.\n\n# My Learning Process\n\nHere’s how I’ve been tackling it:\n\n* Starting with **TS/JavaScript** to get comfortable with the basics.\n* Learning **React** for building dynamic components (it seemed beginner-friendly but powerful).\n   * Ended up using Vite because I'm using ShadCN which seems to work better this way.\n* Using **Firebase** for the backend to keep things manageable as I learn.\n\n# Biggest Challenges So Far\n\n1. Balancing learning while meeting client expectations. I have deadlines coming soon 🙈\n2. Staying motivated when progress feels slow... you must know what I'm talking about.\n\n# What’s Working for Me\n\n* Focusing on building small features one at a time.\n   * Pushing small commits.\n* Joining communities like this one to ask questions and learn from others.\n* Stay focused on a simple tech-stack.\n\n\\---------------------------------------------------------------------------------------------------------\n\nAny advice you have  for someone learning while working on their first real-world project?\n\nAMA happy to answer.\n\nPS - If you’re curious about how I’m navigating this journey, let me know and I can share with you a link to videos I'm making on this journey.", "author": "oba2311", "created_time": "2024-12-24T16:56:50", "url": "https://reddit.com/r/webdev/comments/1hlhkpo/how_im_learning_to_code_after_selling_a_saas_for/", "upvotes": 0, "comments_count": 65, "sentiment": "bearish", "engagement_score": 130.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hli2za", "title": "I've created a longevity brand but I s*CK at digital marketing. ", "content": "For more than two years I've been working on my dream company. A longevity company that actually supports intense lifestyles instead of condemning them. \n\nI've spent nearly all my money on setting up a supplement company. Did three years of research in nutrition. And now I'm stuck at digital marketing. \n\nI've tried ads on Facebook. Nothing\nSEO - takes a long time\n\nIdeally I would like to focus on making supplements, doing research and innovating the market. I have more than 10 recipes ready to make. \n\nWhat would you do to market a new lifestyle to millennials and older gen z? A lifestyle called Live Fast Stay Young. Because there are so many beautiful people with preventable and curable chronic illnesses\n\n\n", "author": "Outrageous-Ad875", "created_time": "2024-12-24T17:20:59", "url": "https://reddit.com/r/DigitalMarketing/comments/1hli2za/ive_created_a_longevity_brand_but_i_sck_at/", "upvotes": 3, "comments_count": 48, "sentiment": "bullish", "engagement_score": 99.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hliq2d", "title": "AI and Data Analysts layoffs ", "content": "Hey everyone, has anyone noticed layoffs in data analyst roles due to AI advancements? Just curious if it's affecting the industry and how people are adapting. Drop your thoughts!", "author": "Resident-Ant8281", "created_time": "2024-12-24T17:51:46", "url": "https://reddit.com/r/analytics/comments/1hliq2d/ai_and_data_analysts_layoffs/", "upvotes": 60, "comments_count": 46, "sentiment": "bearish", "engagement_score": 152.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hll70k", "title": "This Pennsylvania school is saving big with solar and EV school buses", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-24T19:55:41", "url": "https://reddit.com/r/solar/comments/1hll70k/this_pennsylvania_school_is_saving_big_with_solar/", "upvotes": 149, "comments_count": 41, "sentiment": "neutral", "engagement_score": 231.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlms32", "title": "Thoughts on the current status of the US stock market", "content": "1. Most investors hesitate to buy the S&P 500 near its all-time highs. This is a great mistake because the S&P 500 spends most of its time near its all-time highs. This mindset causes investors to miss the train, thus preventing them from achieving great long-term returns.\n2. The recently elected US government has pledged to impose tariffs on some imported goods. Such a policy is likely to render inflation somewhat more persistent than previously expected. Many investors hesitate to buy the S&P 500 due to fears that high inflation will cause stocks to decline. Indeed, high inflation will probably result in higher interest rates, which will reduce the present value of future earnings and hence they may exert some pressure on the P/E ratio of stocks.\n\nHowever, investing in the S&P 500 is the greatest weapon investors have against inflation. In the event of persistent inflation, the earnings of the greatest 500 US companies are likely to increase thanks to higher prices. As a result, the S&P 500 will probably appreciate and thus it will save the real portfolio value of investors from eroding. This is exactly what has happened over the last three years. Those who remain on the sidelines are condemned to see the real value of their hard-earned cash erode in tandem with inflation.\n\nIf you find such thoughts interesting, you are likely to find this book interesting as well:\n\nAmazon.com: Investing in Stocks & Bonds: The Early Retirement Project Book 1: 9798324607845: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Apostolos: Books", "author": "Overall_Sport_7693", "created_time": "2024-12-24T21:19:52", "url": "https://reddit.com/r/ValueInvesting/comments/1hlms32/thoughts_on_the_current_status_of_the_us_stock/", "upvotes": 5, "comments_count": 38, "sentiment": "bullish", "engagement_score": 81.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlnrym", "title": "Is there such thing as a leveraged stock you can invest for the long term?", "content": "For example the s&p 500 fluctuates over the short term, but over the long term it provides good profits. Is there such thing as a stock like the s&p 500, but leveraged so more fluctuations, however you will eventually get more over the long term, then a regular index fund stock. \n\nIf so how do I know what stock to invest in?\n\nMeaning I can invest into a stock similar to index fund, and expect the exact same risk as a regular index fund, but with more volatility.", "author": "deleted", "created_time": "2024-12-24T22:14:11", "url": "https://reddit.com/r/investing/comments/1hlnrym/is_there_such_thing_as_a_leveraged_stock_you_can/", "upvotes": 0, "comments_count": 40, "sentiment": "bullish", "engagement_score": 80.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hlo9c1", "title": "I Built an AI Trading Assistant That Does All the Research For You", "content": "Hey fellow traders/investors! 👋\n\nI'm excited to share a project I've been working on for months - an AI-powered trading platform that's revolutionizing how we research stocks and analyze markets. Let me show you what it can do.\n\n[Dashboard](https://preview.redd.it/numx5udiiv8e1.png?width=3552&format=png&auto=webp&s=58c08fbd30604cf5edff4b35f1d3de4c64125044)\n\n# 📊Stock Analysis\n\nHere's where it gets really interesting - let's look at a detailed stock analysis. Taking Tesla as an example, you get a complete picture: live price movements, pre/post market data, technical analysis, company fundamentals, and a real-time news feed all in one view. No more jumping between different websites or tools. The AI aggregates and analyzes everything for you.\n\n\n\n[Stock Analysis](https://preview.redd.it/ijep105qiv8e1.png?width=3536&format=png&auto=webp&s=efd52de4bd5729722bd4aa957669e9b0776aa597)\n\n[AI Overview](https://preview.redd.it/7kih9deqiv8e1.png?width=3536&format=png&auto=webp&s=65a8c7b043ba302b1cac3ca9a3a18a9259ede77c)\n\n# 📝Reddit Analysis\n\nOne of my favorite features is the Reddit sentiment integration. The AI analyzes thousands of posts and comments across trading subreddits to gauge market sentiment. You can see here how it breaks down bullish and bearish arguments, tracks changing sentiment over time, and identifies emerging trends before they become mainstream.\n\n[Reddit Analysis](https://preview.redd.it/ylxfjybuiv8e1.png?width=3536&format=png&auto=webp&s=ba2ec5db2941b5a3a092d376bcfedbc92f81adcb)\n\nhttps://preview.redd.it/nackrluviv8e1.png?width=3536&format=png&auto=webp&s=2607adfaa89f1f59220fbcb850302502505dbea4\n\nhttps://preview.redd.it/6pugt6cwiv8e1.png?width=3536&format=png&auto=webp&s=2a7105eed8255df246dcc5e2eb9139079f4a3d43\n\n# 🌐Market overview\n\nThe market overview gives you a macro perspective, combining technical analysis, news sentiment, and sector rotations. It's like having a professional analyst working 24/7 to keep you informed of every significant market movement. The AI flags potential risks and opportunities as they develop.\n\nThe best part? This all happens automatically. No more spending hours reading through forums, missing important market moves, or struggling to piece together scattered research. Everything you need is right here in one clean, professional interface.\n\n[Market Overview](https://preview.redd.it/brb9by0yiv8e1.png?width=3536&format=png&auto=webp&s=c21b5788db30f2d5c0a71e97c811b6ed767c95b8)\n\n# 🤖AI chatbot\n\nIn standard mode, you're in complete control. Share any market data, news, or trading ideas you want analyzed - the AI breaks it all down for you. Want to know what Reddit thinks about stocks? Check out this detailed sentiment analysis showing breakdowns for NVIDIA, SPY, STLRY and others, with clear bullish/bearish signals for each position.\n\n\n\n[AI Chat](https://preview.redd.it/cibogmo1jv8e1.png?width=3536&format=png&auto=webp&s=e6a2266d389f7af731a999d2e4b4cb66af369bfd)\n\n[Chat - Normal Mode](https://preview.redd.it/igg3of33jv8e1.png?width=3536&format=png&auto=webp&s=5239935b1c979fff49badd317fdec06a24b0e167)\n\nNow here's where it gets fascinating - switch to \"Agentic Mode\" and watch the AI work autonomously. Instead of waiting for your input, it proactively uses built-in research tools to gather and analyze information. Just ask and look how it automatically pulls live market data, scans social sentiment, and compiles technical analysis into one clear response.\n\n[Chat - Agentic \\(Autonomous\\) mode](https://preview.redd.it/16qpe8j5jv8e1.png?width=3536&format=png&auto=webp&s=2c8e22d349e6ddbb178ee8cde719576388e0405c)\n\nhttps://preview.redd.it/u7gho8j5jv8e1.png?width=3536&format=png&auto=webp&s=ca45f05429f122a39433728f4c6fce5389d716ec\n\n# 🚨Upcoming Feature: Advanced Stock Alerts\n\n🔔We’re excited to announce that an **Advanced Stock Alert System** is in the works! This future feature will ensure you stay ahead of critical market moves and opportunities. You’ll be able to set fully customizable alerts for price breakouts, trend changes, news updates, and even sentiment shifts from Reddit and other sources.\n\n📲 To make it even more seamless, these alerts will be sent directly to your mobile phone, so you’ll never miss an important update, whether you’re at your desk or on the go. It’s like having a personal trading assistant in your pocket!\n\n# What do you think?\n\nI'm looking for early users who want to try this out and provide feedback. I'm particularly interested in hearing:\n\n❓How would this fit into your trading/investing strategy?  \n❓What additional features would make this invaluable for you?  \n❓What other data sources should we integrate?\n\n💡The platform will offer flexible pricing plans ranging from €50 to €200 per month, depending on the features and level of access you choose.\n\n**I’m curious to see how many traders and investors would be excited to get their hands on this tool before it officially launches. If you’re interested, drop a comment or DM me!**", "author": "OpenSource02", "created_time": "2024-12-24T22:41:03", "url": "https://reddit.com/r/investing_discussion/comments/1hlo9c1/i_built_an_ai_trading_assistant_that_does_all_the/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hloj0a", "title": "TIL that in 1926, <PERSON> predicted modern cell phones. <PERSON><PERSON> described a future \"when wireless is perfectly applied the whole earth\" in a way that allows humans \"to communicate with one another instantly irrespective of distance\" with the clarity of a face-to-face meeting using a device.", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-24T22:56:01", "url": "https://reddit.com/r/todayilearned/comments/1hloj0a/til_that_in_1926_nikola_tesla_predicted_modern/", "upvotes": 3286, "comments_count": 150, "sentiment": "neutral", "engagement_score": 3586.0, "source_subreddit": "todayilearned", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hloosd", "title": "Why is it happening so slowly?", "content": "I spent many years pondering <PERSON>'s Law, always asking, \"How is progress happening so quickly\"? How is it doubling every 18 months, like clockwork? What is responsible for that insanely fast rate of progress, and how is it so damn steady year after year?\n\nRecently, I flipped the question around. Why was progress so slow? Why didn't the increase happen every 18 weeks, 18 days, or 18 minutes? The most likely explanation for the steady rate of progress in integrated circuits was that it was progressing as fast as physically possible. Given the world as it was, the size of our brains, the size of the economy, and other factors doubling every 18 months was the fastest speed possible.\n\nOther similar situations, such as AI models, also fairly quickly saturate what's physically possible for humans to do. There are three main ingredients for something like this.\n\n1. The physical limit of the thing needs to be remote; <PERSON><PERSON><PERSON><PERSON>'s limit says we are VERY far from any ultimate limit on computation.\n2. The economic incentive to improve the thing must be immense. Build a better CPU, and the world will buy from you; build a better AI model, and the same happens.\n3. This is a consequence of 2, but you need a large, capable, diverse set of players working on the problem: people, institutions, companies, etc.\n\n2 and 3 assure that if anyone or any approach stalls out, someone else will swoop in with another solution. It's like an American Football player lateraling the ball to another runner right before they get tackled.\n\nLocally, there might be a breakthrough, or someone might \"beat the curve\" for a little, but zoom out, and it's impossible to exceed the overall rate of progress, the trend line. No one can look at a 2005 CPU and sit down and design the 2025 version. It's an evolution, and the intermediate steps are required. Wolfram's idea of computational irreducibility applies here.\n\nThoughts?", "author": "pbw", "created_time": "2024-12-24T23:04:48", "url": "https://reddit.com/r/singularity/comments/1hloosd/why_is_it_happening_so_slowly/", "upvotes": 1, "comments_count": 19, "sentiment": "bullish", "engagement_score": 39.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}]