[{"platform": "reddit", "post_id": "reddit_1k4uycq", "title": "Looking for suggestions on where to move my money, so they can manage my money", "content": "I've done a lot of googling but am a bit overwhelmed. As I'm not super knowledgeable about investing, I'm not certain as to what I should be looking for. I understand the fundamentals but not the minutiae. \n\nI've invested with Fidelity for decades but they moved me to a new managed account advisor and he's extremely dismissive. I'm going to move my money out of Fidelity. I'm retired and have an excellent pension, so I don't need to dip into my Fidelity money much. Just big ticket items.\n\nI am a completely hands off person when it comes to my investments. Looking to move about 750k. Was thinking maybe Fisher Investments but not getting good vibes from what I've read on Reddit. Can someone please make suggestions for a good company to use? TIA ", "author": "tigger880", "created_time": "2025-04-22T01:57:28", "url": "https://reddit.com/r/FinancialPlanning/comments/1k4uycq/looking_for_suggestions_on_where_to_move_my_money/", "upvotes": 7, "comments_count": 33, "sentiment": "neutral", "engagement_score": 73.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k4vnjd", "title": "Why is the browser menu designed by a moron?", "content": "Who at Google had the brilliant idea of placing the \"settings\" option, which people may use frequently, just above the \"exit\" menu option? Obviously this leads to people mis-clicking \"exit\" when they meant to select \"settings\". World class stupidity from Google.", "author": "Tone2600", "created_time": "2025-04-22T02:32:46", "url": "https://reddit.com/r/chrome/comments/1k4vnjd/why_is_the_browser_menu_designed_by_a_moron/", "upvotes": 5, "comments_count": 19, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k4x4gi", "title": "Car shopping with a little money", "content": "All right, everybody, been going through a lot of mental stuff trying to figure this out and to be honest in my life, I don’t have too many people that are in a similar situation so maybe there’s some people who can relate on here.\n\nI’m 20 years old, in college currently have no debt, and I am actively pursuing a couple businesses and small ventures on the side. During high school I founded a company and I sold it when I graduated which gave me a little bit of a cushion of finances. So I am kind of counting on a business to start making money rather soon, but if you guys are entrepreneurs, you know that’s most likely not the case.\n\nI have no current income/job, I do not have a car as I sold mine for costing nearly $1500 a month in just maintenance, but I owned the car outright. Currently I have about $150,000 in the bank, and I’m imagining $35,000 of it is kind of on a freeze which will cover the rest of my college tuition. I’m a car guy at heart and I really would like to buy a cool car but just having such a bad experience with an unreliable car I was looking into Teslas.\n\nI go back-and-forth every minute whether I will get bored of a Tesla and want to change it up, or is the financial savings of owning a Tesla that I can buy for sub $20,000 worth the long-term. Or will it really not matter buying the fun car now which should be closer to $45,000. I don’t have any active investments that I’m looking into but having the cash saved kind of nice but maybe I’m overthinking this.\n\nMy dream is by 25 to by a GT3RS which is about $250k, but I’m not sure buying the cooler car now will make that much of a difference when it comes to saving up that much and or making that much.\n", "author": "Jake1from2statefarm", "created_time": "2025-04-22T03:50:58", "url": "https://reddit.com/r/FinancialPlanning/comments/1k4x4gi/car_shopping_with_a_little_money/", "upvotes": 1, "comments_count": 1, "sentiment": "bullish", "engagement_score": 3.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k58bwc", "title": "Connecting Digital Ocean with Google Cloud Platform", "content": "Hello everyone, i am trying to connect GCP Vertex AI platform with my droplets/k8s instances on DO. \n\nI noticed that the proper way to do it is Workload Federation Identity. But DO does not support that i guess.\n\nSo what would be the best option to setup Application Default Credentials on a kubernetes cluster. Thank in advance!", "author": "code_fragger", "created_time": "2025-04-22T14:55:38", "url": "https://reddit.com/r/cloudcomputing/comments/1k58bwc/connecting_digital_ocean_with_google_cloud/", "upvotes": 1, "comments_count": 0, "sentiment": "bearish", "engagement_score": 1.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k59i24", "title": "Google, strangles or naked calls?", "content": "What are your ideas?", "author": "Original_Two9716", "created_time": "2025-04-22T15:42:31", "url": "https://reddit.com/r/options/comments/1k59i24/google_strangles_or_naked_calls/", "upvotes": 0, "comments_count": 3, "sentiment": "bullish", "engagement_score": 6.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k5dkwu", "title": "Google docs isn't working", "content": "The reason I say this is because whenever I try to make a new Google doc it hits me with \"You have to much storage\". I deleted some of the docs and what-not but then when I try again it still hits me with the same thing. Can anyone help me?", "author": "altbsanity1", "created_time": "2025-04-22T18:25:36", "url": "https://reddit.com/r/chrome/comments/1k5dkwu/google_docs_isnt_working/", "upvotes": 0, "comments_count": 15, "sentiment": "neutral", "engagement_score": 30.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k5g9yj", "title": "Tesla reports disappointing quarterly results as automotive revenue plunges 20%", "content": "Tesla reported a miss on the top and bottom lines in its first-quarter earnings report on Tuesday as automotive revenue plunged 20% from a year earlier.\n\nHere are the key numbers compared with LSEG expectations.\n\n* **Earnings per share**: 27 cents adjusted vs. 39 cents estimated\n* **Revenue**: $19.34 billion vs. $21.11 billion estimated\n\nTotal revenue slid 9% from $21.3 billion a year earlier. Automotive revenue dropped 20% to $14 billion from $17.4 billion in the same period last year.\n\nTesla said one reason for the decline was the need to update lines at its four vehicle factories to start making a refreshed version of its popular Model Y SUV. The company also pointed to lower average selling prices and sales incentives as a drag on revenue and profit.\n\nNet income plummeted 71% to $409 million, or 12 cents a share, from $1.39 billion or 41 cents a year ago.\n\nIt’s been a brutal start to the year for Tesla, with CEO <PERSON><PERSON> spending much of his time in President <PERSON>’s White House, overseeing an effort to dramatically downsize the federal government. The president’s sweeping tariffs plan has led to concerns that costs will increase for parts and materials crucial for electric vehicle production, including manufacturing equipment, automotive glass, printed circuit boards and battery cells.\n\nTesla shares are down 41% so far in 2025, and suffered their worst quarterly drop since 2022 in the period that ended in March. The stock was little changed in extended trading on Tuesday.\n\nThe company refrained from promising growth this year and said it will “revisit our 2025 guidance in our Q2 update.”\n\nIn its shareholder deck, Tesla cautioned investors that “uncertainty in the automotive and energy markets continues to increase as rapidly evolving trade policy adversely impacts the global supply chain and cost structure of Tesla and our peers.” The company said this “dynamic,” and “changing political sentiment” could have a meaningful near-term impact on demand for its products.\n\nTesla has faced widespread protests in the U.S. and Europe, where Musk has actively supported Germany’s far-right AfD party. Earlier this month, the company reported a 13% decline in first quarter deliveries from a year earlier to 336,681.\n\nTesla has been struggling to keep pace with lower-cost competitors in China, and is a laggard in the robotaxi market, which is currently dominated in the U.S. by Alphabet’s Waymo. The company has promised to launch its first driverless ride-hailing offering in Austin, Texas, in June.\n\nThe company reassured investors on Tuesday that it remains on track for a “pilot launch” in Austin by that point, and to begin building its humanoid robots on a pilot production line in Fremont, California, this year.\n\nOperating income in the quarter slid 66% to $400 million from $1.17 billion a year earlier, resulting in a 2.1% operating margin. The company cited an increase in expenses tied to artificial intelligence projects as one factor in the decline.\n\nThe company would have lost money on automotive sales without environmental regulatory credits during the quarter. Revenue from the credits, which Tesla receives for selling fully electric vehicles, increased to $595 million from $432 million in the same quarter last year.\n\nEnergy generation and storage revenue jumped 67% in the quarter to $2.73 billion from $1.64 billion a year ago. The company said growth in AI infrastructure is “creating an outsized opportunity for our Energy storage products to stabilize the grid, shift energy when it is needed most and provide additional power capacity.”\n\nTesla uses foreign suppliers for its energy business. The company said “increasing tariffs may cause market volatility and near-term impacts to supply and demand.”\n\nSource: [Tesla (TSLA) earnings report Q1 2025](https://www.cnbc.com/2025/04/22/tesla-tsla-earnings-report-q1-2025.html)", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-04-22T20:13:09", "url": "https://reddit.com/r/stocks/comments/1k5g9yj/tesla_reports_disappointing_quarterly_results_as/", "upvotes": 13953, "comments_count": 971, "sentiment": "bearish", "engagement_score": 15895.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k5ja8c", "title": "Anyone else finding this Google Lens thing completely r/assholedesign?", "content": "Keep accidentally clicking on this shitty Google Lens thing when simply trying to browse on iPhone & it's driving me absolutely nuts!!!!", "author": "Deep-Security-7359", "created_time": "2025-04-22T22:18:32", "url": "https://reddit.com/r/chrome/comments/1k5ja8c/anyone_else_finding_this_google_lens_thing/", "upvotes": 1, "comments_count": 8, "sentiment": "neutral", "engagement_score": 17.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}]