[{"platform": "reddit", "post_id": "reddit_1h8iwbv", "title": "Veteran - thinking about transitioning to data analytics but hesitant if oversaturated", "content": "Ok my fellow redditians. I is a veteran. I is a smarty (not really lol). I gots me 2 bachelors degrees, one in Pyschology (useless), and one in Information Technology. I never got really far in IT, because basically my school sucked, I had the Comptia A+ cert and let it expire because with all the studying I did I could never get a job that wasn't a call center/help desk. I can't do those jobs well bc I is also a deafy boi from big boom boom in sand land. I have some somewhat relevant SQL and Excel experience, and have reviewed a few Tubers talking about blah blah, you need excel, sql and tableau and you can get a job if you do my course and network blah blah. I am trying to see if I actually put the time into this, make my resume look shiny, don't list my crappy employment hx bc of my disabilabuddies from the military if I stand a chance after 6 months of study and maybe that google cert. I think I can be a shiny turd on paper, but looking for opinions from those that have tried, those that have failed and those that are lucky enough to have succeeded plz. Thankee. ", "author": "gritsofblasphemy", "created_time": "2024-12-07T02:53:07", "url": "https://reddit.com/r/analytics/comments/1h8iwbv/veteran_thinking_about_transitioning_to_data/", "upvotes": 0, "comments_count": 34, "sentiment": "neutral", "engagement_score": 68.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8jxv5", "title": "I need an honest opinion", "content": "Hi All,\n\nThis might be an unpopular post , but i humbly request all to kindly share your honest opinion .\n\nI want to enroll either for <PERSON><PERSON><PERSON>'s \"The Ultimate Digital Advertising Library Collection\" or <PERSON>'s <PERSON> Ads . \n\nKindly let me know which is a better option along with a few words of your own on why ?\n\nIt will be very helpful .\n\n", "author": "Connect_Tank_5022", "created_time": "2024-12-07T03:51:17", "url": "https://reddit.com/r/adwords/comments/1h8jxv5/i_need_an_honest_opinion/", "upvotes": 4, "comments_count": 4, "sentiment": "bullish", "engagement_score": 12.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8lm2n", "title": "What Project helped you land your first Analysis Job?", "content": "Hello all,\n\nI want to transition careers into Data Analytics. My background is in teaching Mathematics. I have a degree in math and have programming knowledge from college however i am rusty as i graduate 8 years ago. \n\nI’ve lurked this sub for a while now and understand that having a portfolio with projects is the best way to develop skills and showcase them. \n\nI currently am almost complete with google data analytics on coursera and i am starting to think about how to develop a well rounded project that is interesting but i feel like I don’t have enough business sense to make a complete project.\n\nI feel like my a lack of business acumen is making it difficult for me to plan out a project. I know the fundamentals of sql and excel and I can play around with the data but is there a process to follow that will guide me towards proper analysis. I am not interested in following YouTube tutorials because i learned better from diving straight into data with tasks and questions to guide me. What are common analytics task you all do at work? \n\nWith that being said, how did you all decide your beginner projects? What type of analysis did you do? How do you come up with questions that are important and interesting? How can i showcase data cleaning? \n\nIt would be cool if y’all can share a link to your projects that helped you land an entry level job. I am curious to see what original projects look like.\n\nThanks for the help!", "author": "ignorant_monky", "created_time": "2024-12-07T05:31:09", "url": "https://reddit.com/r/analytics/comments/1h8lm2n/what_project_helped_you_land_your_first_analysis/", "upvotes": 50, "comments_count": 23, "sentiment": "neutral", "engagement_score": 96.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8oycd", "title": "The Aloha Browser will soon make managing cookies much easier on Android", "content": "", "author": "Relevant_Ninja2251", "created_time": "2024-12-07T09:28:17", "url": "https://reddit.com/r/Android/comments/1h8oycd/the_aloha_browser_will_soon_make_managing_cookies/", "upvotes": 3, "comments_count": 8, "sentiment": "neutral", "engagement_score": 19.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8upfj", "title": "What techniques did Google use to create the interactive elements in their 2024 US election graphics?", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-07T15:22:54", "url": "https://reddit.com/r/webdev/comments/1h8upfj/what_techniques_did_google_use_to_create_the/", "upvotes": 461, "comments_count": 45, "sentiment": "neutral", "engagement_score": 551.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8uujf", "title": "<PERSON>’s Pixel 8 right after warranty ", "content": "This happened yesterday right after 1 year warranty. I pre ordered this for my dad.\n\nWhat to do? \n\nThis might cost a lot to repair. Anyone have an idea. Reached out to google support but my dad is in India, I live in US. ", "author": "deleted", "created_time": "2024-12-07T15:29:49", "url": "https://reddit.com/r/google/comments/1h8uujf/dads_pixel_8_right_after_warranty/", "upvotes": 353, "comments_count": 68, "sentiment": "neutral", "engagement_score": 489.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8x04x", "title": "Split Screen in Tab: Bringing Edge's Split View Feature to Chrome!\n", "content": "", "author": "Anxious_Natural_1781", "created_time": "2024-12-07T17:08:51", "url": "https://reddit.com/r/webdev/comments/1h8x04x/split_screen_in_tab_bringing_edges_split_view/", "upvotes": 12, "comments_count": 6, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}]