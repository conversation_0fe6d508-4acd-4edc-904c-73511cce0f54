{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-04-23", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Sr Product Designer Salary @ Microsoft India?", "content": "Hi, I'm in the final stage of interviewing for the role of a Senior Product Designer, based in India. The job description mentions having 8/10+ years of experience. \nWhat should be the expected salary range for this role during negotiation? Any inputs appreciated!", "created_time": "2025-04-23T16:34:55", "platform": "reddit", "sentiment": "bearish", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "PianistOk509", "url": "https://reddit.com/r/microsoft/comments/1k63okw/sr_product_designer_salary_microsoft_india/", "ticker": "MSFT", "date": "2025-04-23"}, {"title": "Dear Microsoft . . .", "content": "You give us features we didn't know we needed, that will save us life's most valuable resource -- time -- but you then you break basic features, and we spend scads of life's most valuable resource trying to fix what you've broken. Stop it!\n\nAddendum: I'm frustrated today with the New Outlook, changes to Teams, Copilot Studay, Power Apps, and Windows 11... and it's only noon.\n\nAddendum 2: It wouldn't be so bad if this happened in just one product, but when it happens in all of the user products in a constant deluge of changes, it's impossible to keep up. Not to mention the changes in Azure et al every day.", "created_time": "2025-04-23T16:47:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 278.0, "upvotes": 186, "num_comments": 0, "subreddit": "unknown", "author": "mind-meld224", "url": "https://reddit.com/r/microsoft/comments/1k63zkh/dear_microsoft/", "ticker": "MSFT", "date": "2025-04-23"}, {"title": "Data Center Technician Manager interview", "content": "Hi all! I currently work as a Technical Account Manager / Cloud Architect at AWS with Data Center experience, and I just noticed an opening for a Data Center Technician Manager role.\n\nMy questions are:\n\n* Is this a good role? I can't understand if this is pure manager role or a mix.\n* How doable is to move internally later on to, for example, a Solutions Architect role if I see that would be a better fit?\n* I remember some years ago having a conversation with a recruiter for a DC Technician role at Microsoft and the salary was not very high comparing to AWS, no stocks whatsoever, does the same applies to this manager role?\n\nMy biggest concern is if I'm taking a step back in my career by moving to this role.\n\nEdit: Also, what is the career progression for this role?  \nEdit 2: My main motivator is because I want to move for a management role.", "created_time": "2025-04-23T20:12:45", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Kratus7", "url": "https://reddit.com/r/microsoft/comments/1k693vr/data_center_technician_manager_interview/", "ticker": "MSFT", "date": "2025-04-23"}, {"title": "$AMZN has delayed some commitments around new data center leases, Wells Fargo analysts said Monday, the latest sign that economic concerns may be affecting tech companies’ spending plans.", "content": "A week ago, a Microsoft executive said the software company was slowing down or temporarily holding off on advancing early build-outs. Amazon Web Services (AWS) and Microsoft are the leading providers of cloud infrastructure, and both have ramped up their capital expenditures in recent quarters to meet the demands of the generative artificial intelligence boom.  \n  \n“Over the weekend, we heard from several industry sources that AWS has paused a portion of its leasing discussions on the colocation side (particularly international ones),” Wells Fargo analysts wrote in a note. They added that “the positioning is similar to what we’ve heard recently from MSFT,” in that both companies are reeling in some new projects but not canceling signed", "created_time": "2025-04-22T06:44:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 5.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Flat_Nose_8811", "url": "https://reddit.com/r/investing_discussion/comments/1k4zvh5/amzn_has_delayed_some_commitments_around_new_data/", "ticker": "MSFT", "date": "2025-04-22"}, {"title": "How I've been making 10–15% monthly for the past 3 years trading stocks using just one Indicator", "content": "This method is pretty straightforward and comes down to following the rules exactly, using just one indicator: the Stochastic Oscillator.\n\nFirst, open up the indicator tab and add the Stochastic Oscillator. Set it to 5 - 3 - 3 (close/close) and use the 15-minute timeframe.\n\nFor my trading software setup, I use free TradingView Premium from [r/BestTrades](https://www.reddit.com/r/BestTrades/comments/1kcc51e/sharing_free_reverseengineered_tradingview/). It’s an absolute must-have if you're doing serious analysis. They have versions for both Windows and Mac. Having access to more indicators and real-time price data has made a huge difference, and the fact that it’s free is just a bonus. **If you want to use paid version - do it. I am simply sharing what worked for me!**\n\nYou’ll see three zones on the oscillator:\n\n0 to 20 is the oversold zone, meaning the stock is considered too cheap and often signals a good time to buy.  \n80 to 100 is the overbought zone, which usually signals a good spot to sell or look for a short.  \nAnything between 20 and 80 is the neutral zone, and for this strategy we completely ignore it.\n\nNow here’s how I enter trades:\n\nBoth stochastic lines need to fully enter and then exit one of the extreme zones, either overbought or oversold.  \nUse the crosshair to mark where the red signal line crosses out of the zone.  \nWait for two candles in a row that are the same color, green for buys and red for sells.  \nThe wicks on those two candles should be smaller than their bodies. This shows clean price action with momentum.  \nIf everything lines up, I enter the trade at the open of the third candle using shares of the stock.\n\nFor exits, I usually target a 1.5 to 2.5 percent return depending on volatility and how strong the move looks. If momentum stays solid, I might hold a bit longer, but most trades are done within 30 to 60 minutes.\n\nThis works best on large-cap stocks and ETFs with good volume like AAPL, AMD, TSLA, SPY, or QQQ. I’ve used this strategy to consistently make 10 to 15 percent a month on my capital. No tricks or fancy signals, just a simple method, tested over time, and sticking to the rules.\n\nIf you’re curious or not sure, try it out on paper first. That’s how I started before trading live.", "created_time": "2025-04-22T08:30:09", "platform": "reddit", "sentiment": "bullish", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "TurbulentKings", "url": "https://reddit.com/r/investing_discussion/comments/1k51bk7/how_ive_been_making_1015_monthly_for_the_past_3/", "ticker": "MSFT", "date": "2025-04-22"}, {"title": "Amazon Follows Microsoft in Retreat From Ambitious AI Data Center Plans - Gizmodo", "content": "", "created_time": "2025-04-22T14:00:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 14, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1k570jf/amazon_follows_microsoft_in_retreat_from/", "ticker": "MSFT", "date": "2025-04-22"}, {"title": "Microsoft website is so slow which makes me want to avoid it", "content": "For many years, I dread going to [microsoft.com](http://microsoft.com) to do anything - everything is so slow. Whether it is accessing account, Xbox or anything else - things are very slow. Will one day in future it will be responsive as many other websites?", "created_time": "2025-04-22T16:13:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Special-Winner-8591", "url": "https://reddit.com/r/microsoft/comments/1k5aa55/microsoft_website_is_so_slow_which_makes_me_want/", "ticker": "MSFT", "date": "2025-04-22"}, {"title": "Microsoft targets ‘low performers’ in a sensational new memo", "content": "", "created_time": "2025-04-22T16:49:02", "platform": "reddit", "sentiment": "neutral", "engagement_score": 515.0, "upvotes": 377, "num_comments": 0, "subreddit": "unknown", "author": "76willcommenceagain", "url": "https://reddit.com/r/microsoft/comments/1k5b5j1/microsoft_targets_low_performers_in_a_sensational/", "ticker": "MSFT", "date": "2025-04-22"}, {"title": "Windows Phone", "content": "Hi everyone,\n\nOver the past few days, I discovered this group after buying a Lumia 1020. I’ve always loved Windows Phone, but I thought it was completely dead, just as Microsoft had declared. However, I now see that there’s a surprisingly large group of people who are still trying to keep this operating system alive. In one post, for example, a user created polls to estimate the timeline of Windows Phone’s downfall and people were genuinely excited, simply because it meant someone was doing something.\n\nI believe this is just the tip of the iceberg. I would love to hear your opinions about reviving the Windows Phone. Would you still buy it if certain improvements are made? If yes, what kind of improvements would you like to see?", "created_time": "2025-04-21T17:44:43", "platform": "reddit", "sentiment": "bullish", "engagement_score": 26.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "Outside-Round428", "url": "https://reddit.com/r/microsoft/comments/1k4jmig/windows_phone/", "ticker": "MSFT", "date": "2025-04-21"}, {"title": "Microsoft’s “1‑bit” AI model runs on a CPU only, while matching larger systems", "content": "", "created_time": "2025-04-21T18:00:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 39.0, "upvotes": 35, "num_comments": 0, "subreddit": "unknown", "author": "BippityBoppityWhoops", "url": "https://reddit.com/r/microsoft/comments/1k4k18u/microsofts_1bit_ai_model_runs_on_a_cpu_only_while/", "ticker": "MSFT", "date": "2025-04-21"}, {"title": "How to prepare for Microsoft with 5 YOE as a Full Stack Dev?", "content": "Hi all,\nI have 5 years of experience as a Full Stack Developer (Java, Spring Boot, Angular, REST/SOAP, MongoDB, Oracle). Currently working at Shena Electric with a 20 LPA package.\n\nI’m aiming for Microsoft and looking for help from those who’ve been in a similar phase:\n\nWhat roles should I target with my experience?\nHow should I start my prep?\nAny resources you found useful?\nAny advice would mean a lot. Thanks!", "created_time": "2025-04-19T05:24:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Fair-Huckleberry-396", "url": "https://reddit.com/r/microsoft/comments/1k2ozqd/how_to_prepare_for_microsoft_with_5_yoe_as_a_full/", "ticker": "MSFT", "date": "2025-04-19"}, {"title": "Microsoft Fall 2025 Internship Toronto", "content": "Did anyone hear back from Microsoft for the fall business operations or finance internships ? ", "created_time": "2025-04-18T05:27:30", "platform": "reddit", "sentiment": "bearish", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Mammoth-Nebula1731", "url": "https://reddit.com/r/microsoft/comments/1k1xdgz/microsoft_fall_2025_internship_toronto/", "ticker": "MSFT", "date": "2025-04-18"}, {"title": "Why do Microsoft certifications not tell you what you missed?", "content": "I've taken about a half dozen Azure and other certifications, and failed my first one last month. \n\n  \nI noticed during this process that when I did fail the exam, it tells you the sections that you did not sore well not, but does not give you an idea of what question you missed or answer you should have chosen. I realize that these are tightly guarded Fort Knox like secrets to prevent cheating, but it would be great if the exams could give you more detailed information about topics or things that you need to study to actually learn from your failure.\n\n  \nStudying practice exams and Codecademy courses all day is great, but if the material you study is only 80% related to the questions on the exam, then it would be nice to have other sources to study or learn from or at least know what you need to do to grow.", "created_time": "2025-04-18T20:58:10", "platform": "reddit", "sentiment": "neutral", "engagement_score": 120.0, "upvotes": 110, "num_comments": 0, "subreddit": "unknown", "author": "f00dl3", "url": "https://reddit.com/r/microsoft/comments/1k2fgtt/why_do_microsoft_certifications_not_tell_you_what/", "ticker": "MSFT", "date": "2025-04-18"}, {"title": "Microsoft faces growing unrest over role in Israel’s war on Gaza: ‘Close to a tipping point’ | Technology", "content": "", "created_time": "2025-04-18T22:56:50", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Low_Razzmatazz3190", "url": "https://reddit.com/r/microsoft/comments/1k2i1xh/microsoft_faces_growing_unrest_over_role_in/", "ticker": "MSFT", "date": "2025-04-18"}, {"title": "Please Return the Setting That Turns off Automatic Resizing of Adjacent Snapped Windows", "content": "I acknowledge rules 1, 2, and 7 - as well as the implied extensions. That said, I do believe this a *civil* version of my issue. Whichever dev decided to force us to resize adjacent snapped windows (by removing the setting post 22H) - PLEASE give us that back. With all relative respect, that was a psychopathic decision. Yes, you can hit Ctrl while adjusting the windows and it won't do that. But I have taken an informal poll, and not one of the \\~30 people I asked thinks the current setting makes ANY sense - let alone to remove our ability to turn off what should have been the opt-in setting. And I question the sanity of anyone who prefers that the windows auto resize.\n\nPlease give us back this small measure of sanity, and while you're at it, please invert the setting so that you have to TURN ON the auto-resizing.\n\nThank you for coming to my Ted Talk.\n\n  \nedit: spelling", "created_time": "2025-04-17T04:41:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Nuke_1568", "url": "https://reddit.com/r/microsoft/comments/1k14kcw/please_return_the_setting_that_turns_off/", "ticker": "MSFT", "date": "2025-04-17"}, {"title": "2022 Crash vs. Today: Lessons Learned", "content": "Today, I'm diving into the lessons from the 2022 stock market crash and how they apply to the current market downturn. Are we seeing history repeat itself with new opportunities emerging?\n\nMy original post: [https://deepvalueanalysis.substack.com/p/2022-crash-vs-today-lessons-learned](https://deepvalueanalysis.substack.com/p/2022-crash-vs-today-lessons-learned)\n\n# A. Lessons from the 2022 Crash\n\n# A.1. Lessons about Financials and Valuations\n\n**a. OCF and FCF are #2, and #1 respectively**\n\nTheoretical Lesson:\n\nNet income has been debunked time and time as a good measure of value in investments, but it is still being taken at face value by many investors and I believe that all value investors, including myself, ought to explain why it is not a good measurement.\n\nFirst of all, the reason OCF is much better is that you are actually measuring the real cash flow of your business. You don’t pay dividends or do stock buybacks from amortization or depreciation. You can’t change OCF whenever you want through complicated accounting methods. (Check [Enron](https://en.wikipedia.org/wiki/Enron_scandal)) - Enron is a classic case study of why you never look at NI without first checking OCF and FCF.\n\nSecond of all, OCF has an even better alternative, and that is FCF. FCF is the big test of whether OCF is “Bullsh\\*t” or “Real”. Now, you may be asking yourself what do I mean by this. What I am referring to is the classic case of heavy CapEx companies that have high OCF and low FCF. After all, OCF is only useful if you can spend it, but if a company constantly requires high CapEx, then the real measure of value is FCF. (Check Auto, Steel and Industrial).\n\nPractical Example:  \nTech which has both high OCF and high FCF recovered extremely well from the 2022 drop, whilst Auto, Steel and Industrials are lacking. Intel is a tech business that is the epitome of “Spend until you drop”\n\n**b. Valuations don’t last forever**\n\nTheoretical Lesson:\n\nAll bubbles pop, I don’t think that it is necessary to explain the concept too much, because everyone knows that nothing lasts forever, in particular stock market bubbles.\n\nPractical Example:  \n1907, 1929, 1937, 1962, 1987, 1990, 2000, 2008, 2020, 2022, 2025 (Now).\n\n**c. FFO, AFFO for REITs**\n\nTheoretical Lesson:\n\nWhen you’re dealing with REITs, traditional metrics like Net Income or even Free Cash Flow can lead you seriously astray. Why? Because of the unique accounting treatment of real estate—specifically depreciation. Imagine owning a building that gains value every year, but accounting tells you it’s losing value because of depreciation. That’s exactly what happens with REITs.\n\nThat’s where Funds From Operations (FFO) comes in. FFO adds back depreciation and amortization to net income, and removes gains on sales of property, giving a clearer picture of how much cash the REIT is actually generating from its operations. It’s like OCF, but real estate flavored.\n\nBut even FFO isn’t the full picture. Enter Adjusted Funds From Operations (AFFO)—this metric goes one step further by subtracting recurring CapEx (maintenance costs, tenant improvements, etc.). AFFO is essentially the REIT version of Free Cash Flow, showing what the REIT can actually return to shareholders after keeping the lights on.\n\nIf FFO is “cash coming in,” then AFFO is “cash you can actually use.” That’s why savvy REIT investors focus heavily on AFFO per share growth.\n\nPractical Example:\n\nTake Realty Income (O), the so-called “Monthly Dividend Company.” On a net income basis, it can look underwhelming. But when you look at its FFO and AFFO, it becomes obvious why investors prize its dividend reliability. On the other hand, watch out for REITs that trumpet high FFO but constantly issue shares or take on debt just to cover CapEx—they might look like cash cows but are actually cash traps.\n\n**d. Normalized FCF during Bubbles - A great tool**\n\nTheoretical Lesson:\n\nUsing normalized FCF during Bubbles is very helpful because you know exactly how to value the company in a situation where the bubble pops and CapEx drops significantly (because that shiny new tech/trend no longer matters to investors). A company may have almost identical FCF during and after a bubble and during the popping of a bubble, multiples contract considerably, and so this type of company will be left out to rot in the stock market. But, on the other hand, companies like GOOG that have very high temporary AI CapEx could easily cut back on this spending and have a much higher FCF in a short time, therefore counteracting the multiples contraction.\n\nPractical Example:\n\nI posted a recent article on the 2025 AI bubble where I gave a few examples of what valuations companies would deserve in a no-bubble scenario. Check it out [here](https://deepvalueanalysis.substack.com/p/beyond-the-ai-hype-analysing-market).\n\n# A.2. Lessons about the Value Investor Mindset\n\n**a. Roughly Two main types of Investments**\n\nTheoretical Lesson:\n\nThere are two main types of investments based on sound analysis and that meet the Benjamin Graham definition of an investment and not speculation:  \n1. ***Cigar Butt/Deep Value Play***\n\n2. ***Buffett Play***\n\n**The Cigar Butt/Deep Value Play** is mostly when you find an extremely undervalued company at a good MOS (>30%) and that has little to no future growth prospects. These are meant to be sold at fair value, or slightly above, usually giving a quick 50-70% profit. (In most cases they also give a big dividend so the total return is closer to 75%).\n\n**The Buffett Play** can be done at or below fair value, but it has to be a very high quality company with an impenetrable moat and good future growth prospects. These can be held “**forever**”. They are to be sold only when there is an extreme bubble (trading >2.5 times fair value), when the moat is in danger, or when there is a serious personal need for money.\n\nPractical Example:  \nCigar Butt - BTI (bought in at 29.3$ in May last year, and made +35% incl. Dividends, during the same time the S&P grew 3% incl. Dividends) - numbers given to exemplify a normal return for a cigar butt play.  \nA lot of REITS fall under Cigar Butt. My most recent REIT play was HIW (+80% in 1 year - basically the maximum realistic gain on a Cigar Butt in the current market)\n\nBuffett Play - AAPL, NFLX (2022-2025), MSFT (Post-2000 - Now), AMZN (Post-2000 - Now), AXP (1991-Now), etc.\n\n**b. Handling a >-30% drop**\n\nTheoretical Lesson:  \nYou shouldn’t let emotions control your investments, after all, it’s just numbers. Almost **ALL** of my investments have gone in the red before becoming profitable. I could start talking for hours about how to control yourself, but the truth is that some people are just not ready to stomach a >30% loss. I’ve been there, and it’s very hard. Some like me learn from mistakes and can be transformed into someone who can stand these losses, there are also some who naturally tolerate them, but there is also a subset of investors who can’t handle them. To those investors I recommend automatic debit to an index fund account and to never look at it.\n\nPractical example:\n\nBeing -50% on a stock that you did a whole investment thesis on and wanting to pull your hairs out, but you resist selling, and after some time you start gaining: -30%, -20%, -5%, +5%, +30%. It’s a slow process but it happens, and at the end you’ve come out on top as a better investor who has just managed to control his emotions. Great Job!\n\n**c. Misinterpreting drops in price**\n\nTheoretical Lesson:  \nPeople act on emotions and when they see a 20% drop in a week and a negative article on seeking alpha they believe that they made a bad investment. Trust me, if you do your DD and you understand the company, some random SA article or random drop shouldn’t scare you. I have learnt this from personal experience and the only way to pass this is to feel it for yourself several times to skip over the bullsh\\*t of Mr. Market.\n\nPractical Example:  \nNVDA end of 2022 - Great fundamentals but it was being battered by both Mr. Market and “Analysts” (most of them don’t deserve that title)\n\n**d. The “Cramer” Investors**\n\nTheoretical Lesson:  \nDon’t invest based on ANYTHING you see being told on TV. IF Cramer told people to buy, don’t—unless you’ve done significant DD. As the saying goes—even a broken clock is right twice a day.\n\nPractical Example:  \nInverse Cramer… I am joking.\n\n**e. No such thing as “It has grown in the past, so it must continue to do so.”**\n\nTheoretical Lesson:  \nAs the title says, past performance is almost never an indicator of future performance. A true investor’s indicator of future performance is an in-depth analysis.\n\nPractical Example:  \nAAPL has grown at a \\~27% CAGR in the past 20 years *so it must continue to do so*. - By that logic apple will have a higher market capitalization than all stock markets combined in 15 years.\n\n**f. When to sell - My mistakes**\n\nTheoretical Lesson:  \nThis links back to the two main types of investments. If you catch a cigar butt, the answer is simple. Sell at or slightly above fair value. But, in the case of “Buffett” Plays , they are to be sold only when there is an extreme bubble (trading >2.5 times fair value), when the moat is in danger, or when there is a serious personal need for money. In other words, they can be held “forever”.\n\nPractical Example:  \nAXP, GOOG, KO, AAPL.\n\nMy mistakes:  \nI confused the two types of plays. I have sold companies at +60-80% gain instead of holding out for Multibaggers (x3-10-100). My biggest mistakes are NVDA (I missed out on a x12 by selling at x2), CAT (x1.7 instead of x3.5), TSM (x1.4 instead of x2.1), META (x1.5 instead of x4), etc.\n\n# B. My 7 Key Plays during 2022-2025\n\n# B.1. The Plays\n\n1. GOOG\n2. NFLX\n3. META\n4. JPM\n5. TSM\n6. AXP\n7. CAT\n\n# B.2. Why?\n\nAll of them had one thing in common. They were undervalued based on multiple metrics, they were great business with solid growth prospects, their drop in stock price was due to reasons other than a true change in the day-to-day reality of their business operations. - There is LITERALLY nothing more to add. It’s pretty simple, you don’t need extremely complex formulas.\n\nS&P 2022 ; -\\~20% - This is the year that stocks went on sale\n\nDuring 2022 I was buying heavily, especially NFLX, GOOG and MSFT which dropped much more than the S&P. My key plays in 2022 gave me some very HARD lessons on losing money temporarily. These investments weren’t merely some fundamental analysis combined with analyzing management (through checking past promises and targets and seeing if they line up with reality and results), they were a test in emotion management. Because USD appreciated compared to my national currency and these stocks dropped a lot, I saw -35% one morning and I didn’t know what to do, so I just went for a 17 KM Run in a nearby managed forest (sort of like a park) and I took a long shower with a short 1 min cold bath and I stopped overthinking about whether I should or shouldn’t sell—in the following 3 months I recovered all my losses.\n\n# C. Similarities and Differences to 2022\n\n# C.1. Similarities\n\n**a. Tech Bubble**\n\nBoth in 2020-2021 and now there is a clear tech bubble, where multiples have expanded considerably, and now sit well above the historical averages. Of course, the reason (the motive for the bubble) is different. Moreover, the 2020-2021 bubble popped in 2022, and the 2024 bubble is slowly popping in 2025 (at least for now, it’s not impossible for it to reverse course).\n\n**b. Russian Aggression**\n\nThe Russo-Ukrainian war started on the 24th of February 2022 and it caused a widespread reaction throughout the world. It led to inflation, lower GDP growth in Europe, started recession fears in the US, etc.\n\nThe war is still ongoing and it is part of the Trump agenda, so it is still important, although its effects on the rest of the world have considerably died down.\n\n# C.2. Differences\n\n**a. Trump Tariffs**\n\nAlthough there were already tariffs on China, which Biden continued, they weren’t even close to the current scale. As of the time of writing, the tariffs stand at [145%](https://edition.cnn.com/2025/04/12/economy/toy-prices-us-china-tariffs/index.html). These are going to have a negative impact on inflation, the economy and the US’s status as a reliable trading partner. These are long term concerns that have immediate implications which may cause the US to go into a recession, or at least a bear market.\n\n**b. European “Trump Card”**\n\nTrump winning the election has definitely changed the trajectory of Europe and I believe that the EU is starting to wake up (although very slowly). Von der Leyen has until now [mostly delivered](https://www.politico.eu/article/ursula-von-der-leyen-first-100-days-graded-europe-nato-ukraine-war/) on her promises (first 100 days), which is much better than in the past. And all of her promises for the next 4 years give European stocks an ability to decouple from the us stock market performance (Capital markets union, defense union, deregulation, 28th regime, etc.), so you can find some interesting opportunities on the European markets as well.\n\n# D. 2025 - Value Ideas/Plays\n\n# D.1. Key Sector - Hidden Normalized FCF\n\nTech is hiding a lot of normalized FCF under its hood. I’ve already done an article on this topic and I’ve placed it 👇.\n\n# D.2. Similar Plays\n\nGOOG looks pretty interesting, although they have some problems with monopoly law, which should be kept in mind when doing DD. As for the rest of the 2025 plays, I believe we should still wait a bit more for them to drop, but in general most of the great plays are in tech, just like last time. (Don’t expect me to give you what stocks to invest in, I am not a guru)\n\n# D.3. New Boring Value (eg. BTI)\n\nBTI is my most recent cigar butt play and it demonstrates that “boring” value still exists in the market. Even in overvalued markets, you can still find value, you just have to build a keen sense of smell and have some patience. With the market dropping after Trump’s tariffs, I believe new boring value will appear, but the question is—should you choose to put your money in a quick cigar butt play or in a long term Buffett play?\n\n# E. Conclusion\n\nFocusing on true cash flow metrics and disciplined analysis is essential for investment success. Ignore market noise, understand company fundamentals, and manage emotions. Whether seeking quick value or long-term growth, patience and adaptability are the keys to strong returns.", "created_time": "2025-04-17T11:34:20", "platform": "reddit", "sentiment": "bearish", "engagement_score": 19.0, "upvotes": 13, "num_comments": 0, "subreddit": "unknown", "author": "Sufficient_Lead_3471", "url": "https://reddit.com/r/investing_discussion/comments/1k1ajc7/2022_crash_vs_today_lessons_learned/", "ticker": "MSFT", "date": "2025-04-17"}, {"title": "Does a referral really increase your chances at Microsoft?", "content": "Hi everyone,\n\nI’m planning to apply for a role at Microsoft (for technical engineer) and was wondering – how important is it to get a referral? I’ve seen mixed opinions online. Some say it’s a game changer, others say it doesn’t matter much if your resume is solid.\n\nIf you’ve been through the process or currently work at Microsoft, I’d really appreciate your thoughts. Is it worth trying to get a referral first, or should I just go ahead and apply?\n\nThanks!\n", "created_time": "2025-04-17T16:37:10", "platform": "reddit", "sentiment": "neutral", "engagement_score": 179.0, "upvotes": 101, "num_comments": 0, "subreddit": "unknown", "author": "Rare-Sky-6212", "url": "https://reddit.com/r/microsoft/comments/1k1ha5c/does_a_referral_really_increase_your_chances_at/", "ticker": "MSFT", "date": "2025-04-17"}, {"title": "I wonder how much revenue they would actually lose if they chose to make Windows open source at this point", "content": "For comparison, even in this quarter \"Windows OEM and Devices revenue increased 4%\"", "created_time": "2025-04-16T16:08:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 199.0, "upvotes": 125, "num_comments": 0, "subreddit": "unknown", "author": "yuhong", "url": "https://reddit.com/r/microsoft/comments/1k0o6ac/i_wonder_how_much_revenue_they_would_actually/", "ticker": "MSFT", "date": "2025-04-16"}], "metadata": {"timestamp": "2025-07-07T00:13:03.644662", "end_date": "2025-04-23", "days_back": 7, "successful_dates": ["2025-04-23", "2025-04-22", "2025-04-21", "2025-04-19", "2025-04-18", "2025-04-17", "2025-04-16"], "failed_dates": ["2025-04-20"], "source": "local"}}}}