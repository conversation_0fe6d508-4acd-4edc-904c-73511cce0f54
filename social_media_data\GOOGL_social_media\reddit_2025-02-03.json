[{"platform": "reddit", "post_id": "reddit_1igcgrb", "title": "Really at my BOILING POINT. Ive had it with employees.", "content": "Honestly . Ive had enuf of the stress and anguish of employees. I really had sincere motives. I wanted to hire people , respect them, start them off at $20per hour (pressure washing) then we added 401k. In the process of adding  health insurance and I was offering to pay 75percent. I explained the goal was grow the business and get everyone to 30per hour within 14 months.  But after another round of screaming in my house on a sunday afternoon.... Im tired of them stealing, doing half jobs, not listening, crashing, breaking stuff. These guys think they can do whatever they want and Im sick of it. Getting rid of 1 just seems to mean finding another 1 that will do the same thing with a different face.  Like I just cant take it anymore. Thinking about sub-contracting everything and firing them all. ", "author": "Puzzleheaded_Pay_181", "created_time": "2025-02-03T00:39:45", "url": "https://reddit.com/r/smallbusiness/comments/1igcgrb/really_at_my_boiling_point_ive_had_it_with/", "upvotes": 1040, "comments_count": 450, "sentiment": "bearish", "engagement_score": 1940.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1igd4yr", "title": "How do I get my client's review when they couldn't leave a review on google because they were banned several years ago from posting reviews by google?", "content": "Hello hard working people :)   \nI just finished my biggest project so far and I was planning to use this as an opportunity to promote my work. My client was very happy with my work and he tried to leave an awesome review but it's not appearing on my business profile. They emailed me a screenshot and said in the email \n\n>\"I hope google shows it. I was blocked several years ago from posting reviews so let me know if it doesn't show up and I could post it somewhere else.\"\n\nThis review is very important for me to show on my website so I'd hate to waste it. How can I verify their review if they can't leave a review on Google? ", "author": "Colonel<PERSON><PERSON><PERSON>", "created_time": "2025-02-03T01:12:29", "url": "https://reddit.com/r/Entrepreneur/comments/1igd4yr/how_do_i_get_my_clients_review_when_they_couldnt/", "upvotes": 1, "comments_count": 18, "sentiment": "neutral", "engagement_score": 37.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1igm2wp", "title": "Finelo app – Is it a good deal?", "content": "**Finelo app – Is it a good deal?**\n\nHi all, I’ve been using Bloom on and off for a while now, and I’ve found it pretty useful for learning the basics of investing. I also tried Udemy courses, which were decent but kind of a hit or miss depending on the topic\n\nIn youtube shorts I see a lot of Finelo and their subscription offer, and I’m wondering if anyone here has experience with it? From what I’ve seen, it looks similar to other investment learning platforms, but the yearly deal makes it pretty tempting. I’m mainly looking to build a solid foundation in investing and personal finance, and I would prefer something interactive within the app, so it's not just dull reading. So I’d love to know if the content is in-depth enough to stay valuable long-term.\n\nWould love to hear any thoughts—anyone using Finelo? Is it worth it?\n\nThanks in advance", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-03T10:24:47", "url": "https://reddit.com/r/investing_discussion/comments/1igm2wp/finelo_app_is_it_a_good_deal/", "upvotes": 290, "comments_count": 249, "sentiment": "neutral", "engagement_score": 788.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1igof1l", "title": "Has anyone else like me almost completely stopped using Google Search?", "content": "It's just not worth it anymore; of course, I still need to verify information every now and then, but over time I will certainly get to the point of not using it at all.", "author": "shobogenzo93", "created_time": "2025-02-03T12:55:20", "url": "https://reddit.com/r/singularity/comments/1igof1l/has_anyone_else_like_me_almost_completely_stopped/", "upvotes": 367, "comments_count": 195, "sentiment": "neutral", "engagement_score": 757.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1igtcq4", "title": "The Case for Solar on a Tesla Model Y: A $200K Experiment and the Results", "content": "Ok, I know... the posted photo looks ridiculous, but bear with me... that's 10-square-meters of surface area, or 2,000 watts of solar using PV solar cells, or 3,000 watts using tandem solar cells.\n\nhttps://preview.redd.it/hmtwdglluxge1.png?width=1661&format=png&auto=webp&s=4f4761b7d7a12ff6b137f185657efae28a4fe744\n\nFor blueprints and .stp files for 3D printing when they are ready please visit [dartsolar.com](http://dartsolar.com)\n\nI want to explain why I built this 2,000 watt solar expandable solar roof rack for my Tesla Model Y, and the results I am getting. I promise you it is worth the read. Over a year ago I posed another article on this Reddit channel with my 1,500 watt solar system ([here](https://www.reddit.com/r/TeslaLounge/comments/194ajsm/my_tesla_with_a_drivable_2000_to_4000_solar_array/)) -- thank you everyone that provided feedback. But after a lot of research, I know this community would enjoy this experimental 2,000 watt solar roof rack. The goal of of my project is to finalize the blueprints of this things so anyone individual could build one or 3D-print parts to repair one -- either for a Tesla, or any other EV. All parts are designed to be easily repaired and replaced.\n\n**Vision For 30-to-60 Miles Per Day With Solar**\n\nThe 2,000 watt solar roof rack (picture above) can charge 25 miles per day to my car (breakdown below). And if I could get my hands on the new commercial tandem solar cells which use Perovskites to increase solar efficiency to 30%, then I'll be able to charge my vehicle 37 miles per day. In 2024 Perovskites solar cells were successfully commercialized by Oxford PV. LONGi is also looking to release their tandem solar cells with Perovskites. I just haven't been able to get my hands on them yet. So in a few years, maybe by 2027, we should be able to charge our electric vehicles 30 to 40 miles per day by just parking them under the sun. In the picture above, the black area is 10-square-meters. With 20% efficient solar cells it generates 2,000 watts. However, using Perovskites, that same surface are would be able to generate 3,000 watts. The custom solar panels I built don't have junction boxes or anything (my diodes are elsewhere), so I can use the full surface are with 182mm solar cells.\n\nNow I live in the souther states (in Los Angeles, CA), but anywhere else in the world a 3,000 watt solar system on an electric light-duty vehicle would be able to provide enough current to charge any EV, or supply enough power for an entire apartment or small home.\n\n[Pretty thin from the side. Only one-inch from the glass top.](https://preview.redd.it/myidtwk5xxge1.png?width=2501&format=png&auto=webp&s=60d253d5622ed156ee0d02c314dbda5df7bb0fe4)\n\n**Aerodynamic Drag**\n\nBased on the different prototypes I've built, let's talk about weight and aerodynamic drag. Here is a photo of the 1,000 watt prototype I built earlier this year.\n\nhttps://i.redd.it/ccimmu4nvxge1.gif\n\nThat system weights about 90 lbs. That's about 40 lbs for the solar panels, and 50 lbs for  the mechanics. Now I get it..., 100 lbs sounds like a lot, but from a weight point of view the extra power needed to roll 100 lbs is minimal. The efficiency difference as far as the weight is concerned, is the same as if you had a kid in the passenger seat vs. not. What really affects the efficiency of the vehicle is the aerodynamic drag that the roof rack creates. That said, the prototype I have is only 1.25-inches tall. The entire structure is really close to the glass roof top (yes, I built my own roof rack). Without a wind breaker and skirt I get 270 wh/mi compared to 250 wh/mi when I drive without it.\n\nThe extra power needed due to aerodynamic loss is 20 wh/mi, or an extra 8%. That extra loss means that if you drove 100 miles without the solar roof rack, you would be able to drive 92 miles with the roof rack. That said, very few people drive 100 miles every day. The primary household vehicle in the US drives 50 miles per day, and the secondary household vehicle drives 30 miles per day. So the system is net positive.\n\n**Deployment**\n\nI know the thing does not look sexy, but remember I am still doing research. It is unsexy but it can be opened fast. The 1,000 watts version can open in 10 seconds, and the 2,000 watts in about 20 seconds -- and you only need one hand to open it (in case your other hand is holding a baby).\n\nhttps://i.redd.it/0f6dg1mmwxge1.gif\n\nThe expanding solar panels are locked in place with a lot of magnets and a mechanical lock. If you were to be driving at 100 mph and hit the brakes to decelerate to 0 mph in less than 2 seconds, the magnets are still not going to give way. So I drive without the mechanical locks -- I only use the magnetic locks. But I am including a mechanical lock in the blueprints for the people that don't trust magnets.\n\nThe solar panels I am using are custom made, the next iteration of the solar panels are going to be fiberglass coated, so that even hail can not break the solar cells in the panel. I am trying to make everything super rugged. The reason the solar panels slide so easily is because I use many custom made aluminum extruded telescopic tubes with ball-bearings, so that the whole thing flows like ice. Also, all the metal is 1/8-inch thick.\n\n**Rugged & Tests**\n\nWhen I started building this thing I wanted to make sure the entire solar roof rack could withstand a tornado. After a few tests and simulations I wanted to make sure that if people use this in the event of a disaster, that they could open the solar panels in winds up to 50 mph. So me (black in photo) and Thomas (yellow in the photo) built two prototypes and added weights on a slab of plywood to test our over-engineered telescopic tubes to see at what point do the tubes fail.\n\nhttps://preview.redd.it/8tmxka6pxxge1.png?width=2434&format=png&auto=webp&s=f5194d47fa9aec75bf477f41e2326eaebb31b479\n\nLong story short, the solar panels that ride on the telescopic tubes can sustain up to 90 lbs of downward weight before failing. That means we have engineered this thing to sustain horizontal winds of up to 80 to 110 mph. We have also added an escape where the rear of the solar panels detach when winds reach 40 mph... when the solar panels detach on one side only they stop working like sails. So the telescopic tubes will never break.\n\nKnowing that wind is no longer an issue when the solar panels are deployed, we moved to other components of the device. The entire frame of the device is build with 1.25 x 1.25 inch square tubes that are 1/8-inch thick. So in a magical way, we were able to attach 2,000 watts of solar (or 10-square-meters) without going over the weight limitation that a Tesla roof has. Overall, we have about 20 tests we do to make sure all our parts can withstand 500 lbs of compression/tension/shear.\n\n**Power From Solar to Vehicle (or any tool)**\n\nIn my previous post many people asked how does the solar current enter the battery. I have researched everything, and the safest way, and the way in which your Tesla's (or any other brand) warranty won't be voided is if use an intermediary power unit. Here I am using an EcoFlow Delta Pro, which too much gun power for this use case. The solar current basically gets stored in a temporary battery in the power uint, and laster you can use the 120 volt, or 240 volt outlet of a power unit to connect your Tesla's charge adapter. The power unit I suggest people use is about 1/3 the size of the one depicted in the screenshot below. Its pretty cool (and weird) seeing the charger cable come out of your car, and charge your car.\n\nhttps://preview.redd.it/g4t72cyzyxge1.png?width=1806&format=png&auto=webp&s=1ba2e3ddd784938391ae1bf4291a9d6119749904\n\n**Tesla Roof Weight Limit**\n\nNot sure if you knew this, but your Tesla Model 3 can only hold up to 155 lbs of weight on the roof (Model Y is 165 lbs). So I've engineered this thing so that the 1,000 watt version is about 100 lbs, and the 2,000 watt version will be about 150 lbs. Other EVs have higher roof weight capacities, so carrying 10-square-meters of solar should be fine.\n\n**Miles Charged?**\n\nMany people ask about the energy losses that occur, and how is it that I think we can charge EVs 60 miles per day with the setup above. To keep things simple let's use a 1,000 watt solar system. After do the math, we can multiple by 2x or 3x to think about a 2,000 watt or 3,000 solar system.\n\n* Start with 1000 watts of solar. The solar manufacturer will claim X watts. But they don't include the losses you get from the coating of the semi-flexible or glass cover. So remove 10%\n* Now we have 900 watts. Given the way the sun moves in Los Angeles and most places, a good estimate is to consider than an 8-hour day, will really give you 5-hours of power at 900 watts. So now you have 5 \\* 900 = 4,500 watt-hours, or 4.5 kWh.\n* Using the EcoFlow Delta Pro, when I push 4.5 kWh though it to charge my Model Y, only 3 kWh ends in my Tesla's battery. Why is that? Because the solar current comes in DC, and the Tesla can only accept power in AC, later the Tesla converts the power to DC to charge its own batteries. That double conversion causes a 25% loss.\n* Any genius would say, hey! hey! Why don't you charge the Tesla with DC? That's because even if I could, Tesla will heat the batteries and do other things to prepare its batteries to receive high-current (supercharge Level 3) levels of power. Currently EVs are not equipped to receive soft drip solar power. That was never the vision for EVs, but today that is possible as solar cell technology has advanced dramatically.\n* Tesla claims 250 watt-hours per mile. So 3,000 watt-hours / 250 gives me the miles I get -- 12 miles. I've also tested this in real life. If I could charge by Tesla's batteries directly (and void my warranty, or if Tesla allowed for this) I would be able to get 16 miles! I write 16 miles and not 18 miles because there is still a 10% loss when transferring current battery to battery.\n* So now let's 2x or 3x it:\n\n|Solar (watts)|Charing with AC (miles)|Potential with DC (miles)|\n|:-|:-|:-|\n|1000 (5 sq. meters, PV cells)|12|16|\n|2000 (10 sq. meters, PV cells)|24|32|\n|3000 (10 sq. meters, tandem cells)|36|48|\n\n* So there you have it, real numbers using solar in Los Angeles sometime in September. Some regions in the world will give you even more miles (Chile, Australia, Ecuador, etc.), other places will give you less miles. The fluctuation based on region and weather is +/- 30% (give or take).\n* The numbers above don't include the savings one gets from the shade the solar provides. It drastically reduces your \"Cabin Overheat\" AC power used -- if you have it turned on.\n\n**Researching**\n\nAs I've embarked on this project I've received A LOT of interest from non-Tesla users, specially van and truck users. Ultimately, this entire project was started to help people use solar to charge their EVs, so when  we release the blueprints to this thing, I want to make sure that it can work on any vehicle.\n\nAs such, here is my self-less plug. I need your help. Based on your questions and concerns I can break from my tunnel-vision and hear from others what they think of this project. I call this project DartSolar, and it has received some press (if you Google it you'll find it) but I want to learn more about how this project can continue. Are there needs that you feel I am not addressing. Are there questions, concerns, am I doing something useless? To end this article and $200k research endeavor, I leave you with the most artistic photo of the 1,000 watt prototype.\n\nhttps://preview.redd.it/s2amhtbn3yge1.png?width=1340&format=png&auto=webp&s=0a090cb22e24114b0056e0d757d755d173fed5d0\n\nThank you all, and I'll be online for the next few days trying to answer any comments and learn as much as possible.", "author": "somid3", "created_time": "2025-02-03T16:40:04", "url": "https://reddit.com/r/sustainability/comments/1igtcq4/the_case_for_solar_on_a_tesla_model_y_a_200k/", "upvotes": 37, "comments_count": 17, "sentiment": "bearish", "engagement_score": 71.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "GOOGL"}]