[{"platform": "reddit", "post_id": "reddit_1jkt7du", "title": "Utah governor signs online child safety law requiring Apple, Google to verify user ages", "content": "", "author": "iMacmatician", "created_time": "2025-03-27T02:03:23", "url": "https://reddit.com/r/apple/comments/1jkt7du/utah_governor_signs_online_child_safety_law/", "upvotes": 530, "comments_count": 107, "sentiment": "neutral", "engagement_score": 744.0, "source_subreddit": "apple", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jkw33c", "title": "Nvidia down 6% after drops from openai and google?", "content": "openai dropped their new image generator, google dropped gemini pro 2.5, and nvidia's stock dropped? I'm not claiming any expertise, but we saw something similar happen with the whole deepseek thing.   \n  \ni think it might be time to load up with vanquish on calls again. what do y'all think?", "author": "StocksTok", "created_time": "2025-03-27T04:41:36", "url": "https://reddit.com/r/options/comments/1jkw33c/nvidia_down_6_after_drops_from_openai_and_google/", "upvotes": 38, "comments_count": 15, "sentiment": "bearish", "engagement_score": 68.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jl6j1l", "title": "Google's latest model, Gemini 2.5 Pro is Amazing! It created this Awesome Minecraft clone!", "content": "", "author": "Realistic_Access", "created_time": "2025-03-27T15:25:40", "url": "https://reddit.com/r/singularity/comments/1jl6j1l/googles_latest_model_gemini_25_pro_is_amazing_it/", "upvotes": 819, "comments_count": 177, "sentiment": "neutral", "engagement_score": 1173.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jldh12", "title": "Commoditizing your complements: How Google, OpenAI, and China are playing different AI games", "content": "I paid $200/month for OpenAI's Deep Research in February. By March, Google offered the same capability for free. This isn't random—it's strategic.\n\nOpenAI and Google are playing different games. OpenAI monetizes directly, while Google protects its search business by making potential threats free. This follows <PERSON>'s \"commoditize your complements\" strategy: when complements get cheaper, demand for your core product rises.\n\nIt's why Square gave away card readers (to sell payment processing), why Google invests in free internet access (to gain search users), and why Netscape gave away browsers (to sell servers). For Google, AI research tools are complements to search—making them free protects their primary revenue stream.\n\nBut China is playing an entirely different game. DeepSeek surprised Western researchers with its R1 model in January. Unlike Western companies focused on monetization, DeepSeek released their model with liberal open source licensing—unthinkable for Western AI labs.\n\nThe Chinese government designated DeepSeek a \"national high-tech enterprise\" with preferential treatment and subsidies. The Bank of China committed $137 billion to strengthen their AI supply chain, while provincial governments provide computing vouchers to AI startups.\n\nThis creates three distinct approaches:\n\n* AI Startups (eg: OpenAI): Direct monetization of AI capabilities\n* Tech Giants (eg: Google): Commoditization to protect core business\n* China: National strategy for AI dominance without pressure for direct returns\n\nWhat does this mean for AI development? Can Western startups survive when features are rapidly commoditized by tech giants while China pursues a national strategy? And which approach do you think will lead to the most significant AI advancements long-term?", "author": "kaizer1c", "created_time": "2025-03-27T20:56:46", "url": "https://reddit.com/r/artificial/comments/1jldh12/commoditizing_your_complements_how_google_openai/", "upvotes": 25, "comments_count": 14, "sentiment": "bearish", "engagement_score": 53.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jlguqo", "title": "Im buying 2.7 year calls on google bc its undervalued", "content": "***Google LEAP 160 strike EXP Dec 17, 2027, 995 days till exp***\n\nWith Big Tech taking a good dip recently, I have been looking at option plays and think an ATM leap has a good shot of making money. Google is an extremely profitable company with major businesses such as Search and advertising, Cloud Computing and enterprise Services, YouTube, Google Maps, the Google Office suite, Gemini, Waymo, and I could go on.\n\n  \nGoogle is facing some headwinds with lawsuits concerning divesting Chrome, but other than that, the market continues to underestimate the strength of the business as a whole. A lot of people also think that LLMs will replace search, but with these LLMs being out for years, we haven't seen a decline in Google search revenue. At the same time, Google is diversifying its revenue to be less dependent on search.  \n\n**With all that being said, I think Google should be worth at least 210 per share and currently sits at 164**\n\nThe reason why I chose an expiration so long is because I'm a relatively conservative investor and like to have as much time as possible for my thesis to play out.  I would be interested to hear what others think about this position thanks!\n\n", "author": "aj_cohen", "created_time": "2025-03-27T23:17:13", "url": "https://reddit.com/r/options/comments/1jlguqo/im_buying_27_year_calls_on_google_bc_its/", "upvotes": 539, "comments_count": 235, "sentiment": "bullish", "engagement_score": 1009.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jlgwhd", "title": "Google ads specialist imposter syndrome", "content": "Hey everyone , i just wanted to get some insight on whether I’m missing anything major in my Google Ads strategy.\n\nHere’s what I typically do within a **month**:\n\n* Regularly check for expensive keywords and trim them out\n* Review search terms and add high-performing ones as keywords (based on 30-day performance)\n* Make sure no ad groups are overspending\n* Create new campaigns and ad groups as needed when I spot opportunities\n* Keep ad extensions fully built out (sitelinks, callouts, etc.)\n* Use negative keyword lists and scripts to maintain account hygiene\n\nI don’t currently do much **A/B testing,** am I missing out by not prioritizing that?\n\nDoes this approach sound solid overall, or are there key things I should be doing more consistently?\n\nAppreciate any feedback 🙏", "author": "adfusionlabs", "created_time": "2025-03-27T23:19:20", "url": "https://reddit.com/r/PPC/comments/1jlgwhd/google_ads_specialist_imposter_syndrome/", "upvotes": 63, "comments_count": 32, "sentiment": "bearish", "engagement_score": 127.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}]