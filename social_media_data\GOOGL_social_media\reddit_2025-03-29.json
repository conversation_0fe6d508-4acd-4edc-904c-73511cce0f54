[{"platform": "reddit", "post_id": "reddit_1jm9zod", "title": "How do feel about google under 160$", "content": "How do we feel about google under 160$\n\nLong term it feels like a no brainer, they have the cheapest p/e of the big 7 and chrome isn’t going anywhere. At most they will have to modify chrome. Let me know what you think.\n\nI already owned google but I bought more at 156 today and will continue to buy more if it goes any lower but I’m just curious if I’m missing something.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-29T00:25:50", "url": "https://reddit.com/r/investing/comments/1jm9zod/how_do_feel_about_google_under_160/", "upvotes": 278, "comments_count": 322, "sentiment": "bullish", "engagement_score": 922.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jmbobj", "title": "Google Sheets div data", "content": "Anyone have any clue as to how to import dividend data into Google Sheets? I used to have a function that worked, but it has since stopped working :-(\n", "author": "Silver_Database7755", "created_time": "2025-03-29T01:51:44", "url": "https://reddit.com/r/dividends/comments/1jmbobj/google_sheets_div_data/", "upvotes": 0, "comments_count": 3, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jmlvtl", "title": "Gemini 2.5 Pro Experimental is great at coding but average at everything else", "content": "Google finally has a model that can compete with rest of the frontier models. This time they actually released a great model as far as coding is concerned,, though their marketing is pretty bad and AI studio is buggy and unoptimal as hell, \n\nThis is the first Gemini model that got so much positive fanfare. A lot of great examples of coding. However a very few are talking about it's reasoning abilities. So, I did small test on a few coding, reasoning and math questions and compared it to Claude 3.7 Sonnet (thinking) and Grok 3 (think). I personally preferred these models.\n\nHere are some key observation:\n\n# Coding\n\nPretty much the consus at this point, this is the current state-of-the-art, better than Claude 3.7 thinking and also Grok 3. Internet is pretty much filled with anecdotes of how good the model is. And it's true. You'll find it better at most tasks than other models.\n\n# Reasoning\n\nThis is something very less talked about the model but the general reasoning in Gemini 2.5 Pro is very bad for how good it is at coding. Grok 3 in this department is the best so far, followed by Claude 3.7 Sonnet. This is also supported by ARC-AGI semi-private eval, the score is around to Deepseek r1.\n\n# Mathematics\n\nFor raw math ability it's still good, as long as it is in it's in training data. But anything beyond that requires general reasoning it fails. o1-pro has been the best in this regard.\n\n  \nIt seems Google has taken a page out of <PERSON>'s marketing and making their flagship models entirely around software development, this certainly helps in rapid adoption.\n\nSo, basically if your requirements heavily tilt towards programming, you'll love this model but for reasoning heavy tasks, it may not be the best. I liked Grok 3 (think) though very verbose. But it actually feels closer to how a human would think thank other models.\n\nFor full analysis and commentary check out this blog post: [Notes on Gemini 2.5 Pro: New Coding SOTA](https://composio.dev/blog/notes-on-gemini-2-5-pro-new-coding-sota/)\n\nWould love to know your experience with the new Gemini 2.5 Pro. ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-29T13:07:09", "url": "https://reddit.com/r/singularity/comments/1jmlvtl/gemini_25_pro_experimental_is_great_at_coding_but/", "upvotes": 3, "comments_count": 37, "sentiment": "bullish", "engagement_score": 77.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}]