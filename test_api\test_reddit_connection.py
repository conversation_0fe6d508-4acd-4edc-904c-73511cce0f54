#!/usr/bin/env python3
"""
简单的Reddit API连接测试
"""

import os
from dotenv import load_dotenv
import praw

def test_reddit_connection():
    """测试Reddit API连接"""
    print("🔍 测试Reddit API连接...")
    
    # 加载环境变量
    load_dotenv()
    
    # 获取配置
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'AI-Hedge-Fund-Bot/1.0')
    
    print(f"Client ID: {client_id[:8]}..." if client_id else "Client ID: 未设置")
    print(f"Client Secret: {'已设置' if client_secret else '未设置'}")
    print(f"User Agent: {user_agent}")
    
    if not client_id or not client_secret:
        print("❌ Reddit API配置不完整")
        return False
    
    try:
        # 尝试不同的认证方式
        username = os.getenv('REDDIT_USERNAME')
        password = os.getenv('REDDIT_PASSWORD')

        print(f"用户名: {'已设置' if username else '未设置'}")
        print(f"密码: {'已设置' if password else '未设置'}")

        # 创建Reddit实例
        if username and password:
            print("使用用户名密码认证...")
            reddit = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent,
                username=username,
                password=password
            )
        else:
            print("使用应用认证...")
            reddit = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent
            )
        
        # 测试基本连接
        print("\n测试访问r/stocks...")
        subreddit = reddit.subreddit('stocks')
        
        # 获取基本信息
        print(f"✓ 子版块名称: {subreddit.display_name}")
        print(f"✓ 订阅者数量: {subreddit.subscribers:,}")
        print(f"✓ 描述: {subreddit.public_description[:100]}...")
        
        # 测试获取帖子
        print("\n测试获取帖子...")
        posts = list(subreddit.hot(limit=3))
        print(f"✓ 成功获取 {len(posts)} 个热门帖子")
        
        for i, post in enumerate(posts, 1):
            print(f"  {i}. {post.title[:50]}...")
            print(f"     作者: {post.author}, 分数: {post.score}, 评论: {post.num_comments}")
        
        print("\n✅ Reddit API连接测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 提供具体的错误建议
        if "invalid_grant" in str(e):
            print("\n💡 建议:")
            print("1. 检查Client ID和Client Secret是否正确")
            print("2. 确认应用类型为'script'")
            print("3. 检查Reddit账户是否验证邮箱")
        elif "401" in str(e):
            print("\n💡 建议:")
            print("1. 检查API凭据是否有效")
            print("2. 确认User Agent格式正确")
        elif "403" in str(e):
            print("\n💡 建议:")
            print("1. 检查API权限设置")
            print("2. 确认应用未被暂停")
        
        return False

if __name__ == '__main__':
    success = test_reddit_connection()
    exit(0 if success else 1)
