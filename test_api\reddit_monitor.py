#!/usr/bin/env python3
"""
Reddit API状态监控脚本
定期检查Reddit API是否恢复正常
"""

import time
import praw
import prawcore
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

def check_reddit_status():
    """检查Reddit API状态"""
    try:
        reddit = praw.Reddit(
            client_id=os.getenv('REDDIT_CLIENT_ID'),
            client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
            user_agent=os.getenv('REDDIT_USER_AGENT'),
            username=os.getenv('REDDIT_USERNAME'),
            password=os.getenv('REDDIT_PASSWORD')
        )
        
        # 测试基本访问
        subreddit = reddit.subreddit('test')
        posts = list(subreddit.new(limit=1))
        
        return True, f"成功获取 {len(posts)} 个帖子"
        
    except prawcore.exceptions.ResponseException as e:
        if e.response.status_code == 403:
            return False, "403错误 - 仍然被限制"
        else:
            return False, f"其他错误: {e}"
    except Exception as e:
        return False, f"连接错误: {e}"

def monitor_reddit():
    """监控Reddit API状态"""
    print("开始监控Reddit API状态...")
    print("按Ctrl+C停止监控")
    
    check_interval = 1800  # 30分钟检查一次
    
    try:
        while True:
            print(f"\n[{datetime.now()}] 检查Reddit API状态...")
            
            success, message = check_reddit_status()
            
            if success:
                print(f"✅ Reddit API恢复正常: {message}")
                print("可以重新运行数据收集脚本!")
                break
            else:
                print(f"❌ Reddit API仍不可用: {message}")
                print(f"等待 {check_interval/60} 分钟后再次检查...")
                time.sleep(check_interval)
                
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == '__main__':
    monitor_reddit()
