{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-01-10", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Xbox Developer Direct kicks off 2025 with Doom: The Dark Ages, South of Midnight, <PERSON>, and a \"surprise\" fourth game", "content": "All coming to Game Pass \"this year\"", "created_time": "2025-01-10T03:26:31", "platform": "reddit", "sentiment": "neutral", "engagement_score": 44.0, "upvotes": 40, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1hxv9gs/xbox_developer_direct_kicks_off_2025_with_doom/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "MS900 to SC900", "content": "Hi guys, \n\nI passed the MS900 exam in 2024 and I am now looking forward to get the SC900.\n\nIs it more difficult or it’s just a completely different test? \n\nI know MS900 is for the fundamentals and SC900 is for security, but I’ve been working at an MSP selling Microsoft products for over a year now, including teaching people on how to use Copilot, and I just wonder how many off-time it takes on average to prepare for the exam? ", "created_time": "2025-01-10T04:36:28", "platform": "reddit", "sentiment": "bearish", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "420sblahsblah", "url": "https://reddit.com/r/microsoft/comments/1hxwhfp/ms900_to_sc900/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "Microsoft releases new 'Fluid Textures' desktop wallpapers", "content": "[https://microsoft.design/wallpapers/](https://microsoft.design/wallpapers/)", "created_time": "2025-01-10T06:48:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 107.0, "upvotes": 99, "num_comments": 0, "subreddit": "unknown", "author": "MrShortCircuitMan", "url": "https://reddit.com/r/microsoft/comments/1hxykwf/microsoft_releases_new_fluid_textures_desktop/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "Donations for California", "content": "I apologize if this doesn't exactly fall into a discussion type post, but question is not an available tag and this is an urgent post:\n\nWhat charities can I donate my Microsoft Rewards points to that will help with the LA fires? There is no other way for me to donate money and I desperately want to help to do literally anything possibly to provide assistance. Absolutely ANYTHING will be extremely helpful.\n\nThank you so, so much.", "created_time": "2025-01-10T13:21:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "-iggylove-", "url": "https://reddit.com/r/microsoft/comments/1hy49ev/donations_for_california/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "For future reference, how does a Microsoft Authy transfer work to another phone?", "content": "I have had it on my phone for a long time but I remember reading you have to becareful when doing it. I think it was something about the way you login that you want to choose one option over another or something to that effect. \n\nI'm not planning on getting a new phone right now but just want to ask for down the road to reference here so I know what I'm doing, I don't want to be locked out of accounts.\n\nI don't even remember if you even login to the Authy its been so long but I believe I wrote down some password or something for it. I'm just clueless at this point and want to make sure I know what I'm doing when the time comes.", "created_time": "2025-01-09T03:08:29", "platform": "reddit", "sentiment": "bullish", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "RJP_X", "url": "https://reddit.com/r/microsoft/comments/1hx34xk/for_future_reference_how_does_a_microsoft_authy/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Wall Street Expected to Shed 200,000 Jobs as AI Erodes Roles", "content": "Global banks will cut as many as 200,000 jobs in the next three to five years—a net 3% of the workforce—as AI takes on more tasks, according to a Bloomberg Intelligence survey.\n\n - Back, middle office and operations are most at risk.\n - Banks’ profits could surge due to improved productivity.", "created_time": "2025-01-09T13:05:11", "platform": "reddit", "sentiment": "bullish", "engagement_score": 3508.0, "upvotes": 3152, "num_comments": 0, "subreddit": "unknown", "author": "PrestigiousCat969", "url": "https://reddit.com/r/finance/comments/1hxcctu/wall_street_expected_to_shed_200000_jobs_as_ai/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Looking for a script to analyze Microsoft Tenant settings", "content": "Hey fellow <PERSON>min-D lacking humans :)\n\nLike the title says, have anyone bumped into or else i will start making one and share on here in the future.", "created_time": "2025-01-09T13:50:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hxd7hh/looking_for_a_script_to_analyze_microsoft_tenant/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Microsoft cuts more jobs, this time apparently based on performance\n", "content": "", "created_time": "2025-01-09T14:24:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 391.0, "upvotes": 309, "num_comments": 0, "subreddit": "unknown", "author": "Robemilak", "url": "https://reddit.com/r/microsoft/comments/1hxdwey/microsoft_cuts_more_jobs_this_time_apparently/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Microsoft Designer: Restyle Image no longer available after 2/1/25", "content": "", "created_time": "2025-01-09T14:48:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 6.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "FriendshipFun4992", "url": "https://reddit.com/r/microsoft/comments/1hxee8h/microsoft_designer_restyle_image_no_longer/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Application Timeline Question ('Transferred')", "content": "Hi, I just have a question for current employees regarding the early stage interview/application process.\n\nI've had a couple job applications be moved into the 'Transferred' section. They still show up as 'Active' on my profile, and have the info message of 'The job description is not available. Your application is still under consideration.' after they have been 'Transferred', but I am yet to hear anything from Microsoft. Am I just in the general applicant pool but not selected for the next round? I am just unsure of the timeline. I applied to 2 positions in the beginning of November and another towards the end of December. I've had other applications for positions I am definitely less qualified for be rejected within 1-2 weeks after I apply, so I am just confused as to the timeline. Would just love some clarity as to whether or not I am still in the process and should take time to interview prep for these positions, or that HR has just forgotten to formally reject me for them and that I should just get back to work.\n\nThanks!", "created_time": "2025-01-09T15:19:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "PlentyLecture5038", "url": "https://reddit.com/r/microsoft/comments/1hxf27e/application_timeline_question_transferred/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Entry Level Sales Role", "content": "I may have an opportunity in an entry level sales role for Microsoft and I’m curious if anybody has insight into what the job entails? I’m transitioning from a different career in my late 20s. Am I stupid if I don’t end up taking this opportunity? I realize Microsoft is one of the most profitable companies in the world and there’s tons of benefits working for MSFT including stock options, insurance, etc… I also know that this industry is pretty cutthroat and the bottom 50% are likely to be laid off. Anyways I just would like some opinions if anybody has good advice or input!", "created_time": "2025-01-08T04:21:16", "platform": "reddit", "sentiment": "bullish", "engagement_score": 5.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "DerfLedew94", "url": "https://reddit.com/r/microsoft/comments/1hwba40/entry_level_sales_role/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "Microsoft Office 365 not working?", "content": "Ok for context this laptop used to be from school, they \"unenrolled me\" but in a way where I simply lost restrictions, computer is still enterprise edition.   \nIt came as Windows 10 Enterprise edition, I updating to windows 11, enterprise edition is still active though.\n\nNote that this is NOT a support post im just curious about what yall think the problem is and why it is doing this.\n\nFor example, I open 365, enter product key for windows 11 enterprise, and it doesn't recognize? what makes it not recognize it, i'd like to know how that works.", "created_time": "2025-01-08T13:38:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 44.0, "upvotes": 32, "num_comments": 0, "subreddit": "unknown", "author": "yes_im_gavin", "url": "https://reddit.com/r/microsoft/comments/1hwkf5r/microsoft_office_365_not_working/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "BBR TCP Congestion Control", "content": "Does Windows support BBR TCP Congestion Control", "created_time": "2025-01-08T15:36:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "HeadState8730", "url": "https://reddit.com/r/microsoft/comments/1hwmxme/bbr_tcp_congestion_control/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "OneDrive requires you to have free space to copy to another user's OneDrive ", "content": "Appears if your personal OneDrive is full, you cannot upload non-OneDrive files to someone else’s OneDrive who has plenty of free space. Microsoft wants to sell space on both ends even though one end is not used in the copy at all. I had to clear out my OneDrive before the copy could be done.\n\nI have never had a use for OneDrive. If it was just a backup, I would be fine with it. However, removing my local files and storing only in the cloud is not want I want. I avoided it for years by not even signing into a MS account with my builds.\n\nSince we implemented it at work, I decided to enable it last year on my new gaming laptop at home. It promptly hit the space limit and the messages started about buying more space. Only way to stop that, without buying space I do not need, was to disable it. Then MS keeps turning it back on and does not even remember settings (desktop sync turned off). I had to create a local copy of all files in OneDrive (so much for saving space) and tell all my applications to not save to Documents folder anymore.\n\nI have been in IT for nearly 40 years. I owe much of my career to MS products. However, I really wish MS would sell an OS again. What they sell now is a platform to sell us more stuff and it is getting annoying. I don’t need OneDrive stealing my files. It is not a “backup”. I don’t need “news” clickbait that never gets you to the real story. I do not need your app store. I don’t even need your web browser. Sell me a base OS and let me chose what to install, and DON’T re-enable stuff I turn off with updates. If my games played reliably on Linux I would switch in heartbeat.\n\n/rant off", "created_time": "2025-01-08T16:51:38", "platform": "reddit", "sentiment": "bearish", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hworcs/onedrive_requires_you_to_have_free_space_to_copy/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "Does Microsoft give interns any off days during the summer?", "content": "From anyone at Microsoft. Do they allow interns to take any days off over the summer? Like vacation/sick days or PTO?", "created_time": "2025-01-08T20:42:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 34.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "EconomicsWest4989", "url": "https://reddit.com/r/microsoft/comments/1hwudd7/does_microsoft_give_interns_any_off_days_during/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "Anyone else a big user of Microsoft Movies and TV? I feel they need to add some sort of organization to the app.", "content": "I've been slowly building my collection of Movies and TV through microsoft ever since the Xbox 360. As it stands now I have over 100 tv shows and well over 300 movies (don't knock me, I typically buy them on sale, the sales are amazing.) Not sure if anyone else uses the app as much as I do. But I enjoy buying content this way rather than a paid subscription just for them to remove content I enjoy watching later down the road (looking at you Netflix.)\n\nWondering if anyone out there has a big collection too and if they would find it beneficial for Microsoft to implement new sorting features (genre, release date etc.) as well as custom grouping folders where we can place all relevant media into one spot (LoTR ,Hobbit).\n\nI realize it is easy to search, but there are times when media does not always take the same name to be searched (take alot of the DC animated films). I just feel it could be beneficial. I don't see any major updates to the app and app store ever it would be nice for a refresh in this direction.\n\n Anyone else feel the same? What is everyone else's thoughts and opinions?", "created_time": "2025-01-07T04:43:05", "platform": "reddit", "sentiment": "bullish", "engagement_score": 96.0, "upvotes": 72, "num_comments": 0, "subreddit": "unknown", "author": "TH3SCARFATH3R", "url": "https://reddit.com/r/microsoft/comments/1hvjm7a/anyone_else_a_big_user_of_microsoft_movies_and_tv/", "ticker": "MSFT", "date": "2025-01-07"}, {"title": "How can I use Microsoft office for free for learning purposes? ", "content": "When I used to be in college I had free access to the Microsoft office softwares. Now I graduated and I am unemployed and searching for a job. I am looking to improve my skills at the moment and learn Microsoft excel and PowerBi. I want to be able to practice with the software as I follow the guide and courses. do I have to buy it? ***(***I ***cant really afford atm).***  ", "created_time": "2025-01-07T11:55:22", "platform": "reddit", "sentiment": "bullish", "engagement_score": 29.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "TurkyySandwitch", "url": "https://reddit.com/r/microsoft/comments/1hvpssj/how_can_i_use_microsoft_office_for_free_for/", "ticker": "MSFT", "date": "2025-01-07"}, {"title": "Microsoft nixes Windows 11’s “Suggested actions” feature: a smart idea that didn’t quite work", "content": "", "created_time": "2025-01-07T13:30:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 90.0, "upvotes": 84, "num_comments": 0, "subreddit": "unknown", "author": "Robemilak", "url": "https://reddit.com/r/microsoft/comments/1hvrge8/microsoft_nixes_windows_11s_suggested_actions/", "ticker": "MSFT", "date": "2025-01-07"}, {"title": "'Extra income for everyone:' Business owners rejoice as Amazon workers return to office", "content": "", "created_time": "2025-01-07T14:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 131.0, "upvotes": 19, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1hvs0xk/extra_income_for_everyone_business_owners_rejoice/", "ticker": "MSFT", "date": "2025-01-07"}, {"title": "Benefits ", "content": "What are your favorite Microsoft benefits / perks? \n\nAlternatively are there any financial tips you have in relation to using Microsoft benefits? For example the mega back door roth, etc. \n\nThanks! ", "created_time": "2025-01-07T22:22:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Diligent-Sand-2675", "url": "https://reddit.com/r/microsoft/comments/1hw3twd/benefits/", "ticker": "MSFT", "date": "2025-01-07"}, {"title": "Thinking of switching from Google Workspace", "content": "I have a single user google workspace, thinking of switching to M365 and getting into Azure. I don’t have a lot of files or emails on Google. I use Google to login to things like GitHub and other services. \n\nI do however have a decent amount of calendar events. That I share with my wife on her free Gmail account. \n\nI can’t stand outlook \n\nAnyone make the switch from google workspace and miss it? Any upside to switching? ", "created_time": "2025-01-07T23:06:21", "platform": "reddit", "sentiment": "neutral", "engagement_score": 82.0, "upvotes": 68, "num_comments": 0, "subreddit": "unknown", "author": "HeadlineINeed", "url": "https://reddit.com/r/microsoft/comments/1hw4v03/thinking_of_switching_from_google_workspace/", "ticker": "MSFT", "date": "2025-01-07"}, {"title": "Army to Microsoft ", "content": "Hi everyone! I’m getting out of the army soon and I’d really like to get employed with Microsoft. I’m currently obtaining my net and sec+ certs and my background is 3 years as an army intelligence mission manager for a 3 letter government agency. What I’d really like to know is what jobs at Microsoft I should be looking for. From what I’ve seen entry level positions are few and far between so if anyone has any recommendations as to how I can make myself stand out and what I should be looking for that would be awesome. Thanks!", "created_time": "2025-01-06T01:10:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 59.0, "upvotes": 41, "num_comments": 0, "subreddit": "unknown", "author": "Donaldthecriminal", "url": "https://reddit.com/r/microsoft/comments/1humvtj/army_to_microsoft/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Pros and cons of SWE and PM at Microsoft?", "content": "I have to opportunity to go down either path and would love to hear about the work, WLB, promotions, career growth, and anything else that might be helpful to me making a decision. Thank you!", "created_time": "2025-01-06T06:25:44", "platform": "reddit", "sentiment": "bullish", "engagement_score": 30.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "tetracell_", "url": "https://reddit.com/r/microsoft/comments/1husrr0/pros_and_cons_of_swe_and_pm_at_microsoft/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Migrating GoDaddy Mail Services to Microsoft Outlook", "content": "I'm looking to migrate my GoDaddy mail services to Microsoft Outlook(using Microsoft for Startups credit) and could use some guidance. Does anyone have experience with this process or know the best way to go about it?\n\nThanks in advance.", "created_time": "2025-01-06T09:21:14", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Main_Helicopter9096", "url": "https://reddit.com/r/microsoft/comments/1huv5wk/migrating_godaddy_mail_services_to_microsoft/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Internal Hiring Bias", "content": "I'm an internal employee and I'm applying for roles within MSFT. I love the company and the culture but there seems to be an internal hiring bias and I'm witnessing it to be easier to leave the company and come back to the role you want to be at. I want to be wrong, am I?", "created_time": "2025-01-06T13:59:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Zestyclose_Depth_196", "url": "https://reddit.com/r/microsoft/comments/1huzmvy/internal_hiring_bias/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Microsoft Bing shows misleading Google-like page for 'Google' searches", "content": "", "created_time": "2025-01-06T14:24:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hv05hm/microsoft_bing_shows_misleading_googlelike_page/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Microsoft multilingual glossary?", "content": "I use different language packs at home and at work. Is there a reference somewhere, where I can check the specific translation Microsoft uses for important words in their terminology? (an example : what is the 'official' French equivalent for \"Windows product key\"?)", "created_time": "2025-01-06T16:42:29", "platform": "reddit", "sentiment": "bearish", "engagement_score": 11.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Anycauli", "url": "https://reddit.com/r/microsoft/comments/1hv3bq4/microsoft_multilingual_glossary/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Gifting Minecraft to a Mongolian friend from the US", "content": "\nHey y’all, I have this Mongolian friend of mine who I want to give <PERSON><PERSON> to, but from what I’ve heard literally everything related to Microsoft is region locked. Is there any way I can gift it?", "created_time": "2025-01-05T06:46:33", "platform": "reddit", "sentiment": "bullish", "engagement_score": 15.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "Msquared5816", "url": "https://reddit.com/r/microsoft/comments/1htzuk9/gifting_minecraft_to_a_mongolian_friend_from_the/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Message to Microsoft: please bring back 3d Biilder for Windows 11. ", "content": "As per the title. ", "created_time": "2025-01-05T07:58:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "BRunner--", "url": "https://reddit.com/r/microsoft/comments/1hu17z0/message_to_microsoft_please_bring_back_3d_biilder/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Microsoft president says AI is ‘the electricity of our age’ as company prepares to hit $80 billion spend", "content": "", "created_time": "2025-01-05T10:55:02", "platform": "reddit", "sentiment": "neutral", "engagement_score": 647.0, "upvotes": 497, "num_comments": 0, "subreddit": "unknown", "author": "108CA", "url": "https://reddit.com/r/microsoft/comments/1hu4ia1/microsoft_president_says_ai_is_the_electricity_of/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Are all surface Pro keyboards the same?", "content": "I've had the microsoft surface 7 pro for several years now, and I'm needing a new keyboard. Are all surface keyboards the same or will I have to find one that's specific to my model?", "created_time": "2025-01-05T21:00:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Little_beep", "url": "https://reddit.com/r/microsoft/comments/1huh6et/are_all_surface_pro_keyboards_the_same/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Dumping Memory to Bypass BitLocker on Windows 11", "content": "", "created_time": "2025-01-04T00:53:56", "platform": "reddit", "sentiment": "bearish", "engagement_score": 38.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NoInitialRamdisk", "url": "https://reddit.com/r/microsoft/comments/1ht27up/dumping_memory_to_bypass_bitlocker_on_windows_11/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "Old MacBook Air", "content": "I have a 13-year-old MacBook Air that won't update OS, it's the last intel chip version..  Using Big Sur 11.9.   I have a Microsoft 365 subscription.  Is there a version that I can install on this laptop?  My primary reason to keep using it is to use bibliography manager plug in.", "created_time": "2025-01-04T05:05:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Craigccrncen", "url": "https://reddit.com/r/microsoft/comments/1ht70uv/old_macbook_air/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "Microsoft expects to spend $80 billion on AI-enabled data centers in fiscal 2025", "content": "", "created_time": "2025-01-04T05:32:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 231.0, "upvotes": 173, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1ht7hib/microsoft_expects_to_spend_80_billion_on/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "LIQ<PERSON>DTEXT WOES ON SURFACE PRO 11- SEEKING ALTERNATIVE", "content": "Hello everyone!\n\nI’ve been a loyal user of LiquidText for my legal work, as it’s been a game-changer for managing case notes and documents. About six months ago, I decided to upgrade to a Surface Pro 11, excited about its ARM chip and overall utility for my profession. Unfortunately, that excitement quickly turned to frustration when I discovered the LiquidText app doesn’t work properly on the ARM chip.\n\nSince then, I’ve been in *constant* communication with the LiquidText team, trying to get the issue resolved. Six months later, all I have to show for my patience are lagging performance, frequent app crashes, and a series of unhelpful responses from their support team.\n\nHere’s the kicker: instead of solving the problem, the LiquidText team has essentially told me to *buy a new laptop*. Below are excerpts from their responses for your amusement:\n\n\"Hi,   We had been working on it. I am afraid the issue is with the ARM chip, this chip has been having issues with LiquidText for some time. Would you try any other device that runs with X64? I apologize for all the inconvenience.    Best wishes,\" \"We are working with the Microsoft team, and it seems like this specific surface model is having the same issues with LiquidText, a few other users have reported the issue as well. Microsoft is a bit slow at resolving issues, so the progress might come a bit late. I advise you to use any other laptop if you have any to see if the app is running better without any lags and crashes. \"\n\nEssentially, their solution to their app's failure is for *me* to shell out more money for new hardware. That’s like a tailor telling you to change your body because the suit doesn’t fit. Can anyone recommend apps similar to LiquidText that actually work seamlessly on the Surface Pro 11 with an ARM chip? I’d love to hear your thoughts and experiences because it seems like I need to jump ship.\n\nThanks in advance for your suggestions. And LiquidText, if you're reading this: maybe take a break from blaming Microsoft and focus on fixing your app.", "created_time": "2025-01-04T08:41:54", "platform": "reddit", "sentiment": "bearish", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hta93a/liquidtext_woes_on_surface_pro_11_seeking/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "Microsoft has finally upgraded notepad by replicating all features of notepad++ ", "content": "Using notepad was one of the most painful features of Microsoft OS. It did not auto save nor you had option of multiple tabs. But finally with W11 they have made it on par with notepad++\n\nBut why did microsoft take so long to do this ?", "created_time": "2025-01-04T09:25:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Infinite-Fold-1360", "url": "https://reddit.com/r/microsoft/comments/1htau73/microsoft_has_finally_upgraded_notepad_by/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": " What GUI framework does Microsoft's Phone Link app on iOS use? .NET MAUI or Native Frameworks?", "content": "I'm curious about the **Phone Link** app by Microsoft on iOS. I know that Microsoft uses various frameworks for their apps across platforms, but I can't seem to find clear information on the GUI framework used in the iOS version of Phone Link.\n\nDoes anyone know if Microsoft uses **.NET MAUI** for the iOS version of Phone Link, or do they stick to native frameworks like **UIKit** (or maybe even **SwiftUI**) for iOS development?\n\nIt would be interesting to know how they approach the UI development for such an app, especially considering the cross-platform nature of the app and the performance needs on iOS.\n\nThanks for any insights!", "created_time": "2025-01-03T13:59:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 33.0, "upvotes": 27, "num_comments": 0, "subreddit": "unknown", "author": "DazzlingPassion614", "url": "https://reddit.com/r/microsoft/comments/1hsn2ff/what_gui_framework_does_microsofts_phone_link_app/", "ticker": "MSFT", "date": "2025-01-03"}, {"title": "Amazon's Seattle campus still quiet as 5-days-in-office deadline hits", "content": "", "created_time": "2025-01-03T14:00:05", "platform": "reddit", "sentiment": "neutral", "engagement_score": 115.0, "upvotes": 59, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1hsn2it/amazons_seattle_campus_still_quiet_as/", "ticker": "MSFT", "date": "2025-01-03"}, {"title": "Does RCS for Phone Link no longer work?", "content": "I have been using phone link recently on my Windows 11 PC.\n\nMy phone is a Samsung Galaxy S24 Ultra.\n\nI saw on [Microsoft's support](https://support.microsoft.com/en-us/topic/supported-devices-for-phone-link-experiences-cb044172-87aa-9e41-d446-c4ac83ce8807) that my phone is supported for RCS on phone link.\n\nThe main issue is it says Samsung Messages has to be default but Samsung uses google messages, Samsung messages does not seem to be available anymore.\n\nDoes anyone know if this just doesn't work anymore?", "created_time": "2025-01-03T23:45:00", "platform": "reddit", "sentiment": "bullish", "engagement_score": 28.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "Captain<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1ht0plv/does_rcs_for_phone_link_no_longer_work/", "ticker": "MSFT", "date": "2025-01-03"}], "metadata": {"timestamp": "2025-07-06T19:53:34.404741", "end_date": "2025-01-10", "days_back": 7, "successful_dates": ["2025-01-10", "2025-01-09", "2025-01-08", "2025-01-07", "2025-01-06", "2025-01-05", "2025-01-04", "2025-01-03"], "failed_dates": [], "source": "local"}}}}