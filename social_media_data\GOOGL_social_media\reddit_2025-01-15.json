[{"platform": "reddit", "post_id": "reddit_1i1t7ej", "title": "Advice from a guy who has been doing this since 2003 and is somehow still solvent. You want to know how to avoid the worst traps and make money on penny stocks? It's easy. Avoid dilution scams.", "content": "Some of you might know me as a frequent poster on the shortsqueeze sub. I do occasionally read stuff here without commenting but after reading a couple of posts here lately, I feel prompted to add my two cents in.\n\nWhy do I think my opinion is valuable? Well, I've been doing this since 2003. Trading penny stocks, mid-caps and options on U.S. and Canadian listings. And somehow I'm still solvent, having been \"retired\" since 2014 so I can't be doing that badly. I'm a 43 year old with a grand total of 8 years of work experience on my resume. Which might be a concern for me except I haven't had a job interview since 2006 and probably won't have one in my life again.\n\nNow what nearly made my head explode and prompted me to make this post is the blatantly obvious contradiction in this sub's recent popular posts. Someone asked how to avoid traps. Fine. Valid question. But on the other hand, the most popular post on here is from some dude who bagged profits on XTIA and is now sharing his trading method. I mean...the stock collapsed 75% in two days. Isn't it quite obvious that the guy just got mega, mega lucky that he sold the hot potato before it turned into a rotten carcass infested with anthrax? You want to avoid traps...so why are you listening to the guy that just bought and sold the biggest one we have seen this week? It doesn't matter that he made money on it. What matters is his method didn't screen out a stock that took shareholders to the woodshed days after he traded it. What are you going to do? Put a stop loss on a stock that gaps down 75% after hours?\n\nI don't want to drag that guy through the mud. Unlike the scammers and self-proclaimed gurus on the shortsqueeze sub, he came across with humility and honesty. The issue is I just don't find his advice to be that great, sorry. Maybe the method he shared works for him, but it's certainly not a method I would ever use. For instance, I'm first and foremost a news and/or story trader. Especially in Canada where there is no pre-market so you have a chance to scour the news feeds in the morning and get in at a good price at 9:30. As opposed to the U.S. where 3 seconds after some good news hits the wire, the bots already have the stock up 100% in pre-market.\n\nYou want to know the simplest way to avoid traps? Avoid dilution scams. I have written a number of posts recently about this issue, look at my post history for more info.\n\n**What is a dilution scam?**\n\nYou all can use Google or read what I wrote elsewhere, but my summary definition is a listing where management is purposely acting in bad faith to dilute the stock and pay themselves a good salary. Their prime business isn't whatever they say it is on their website or on their Yahoo Finance bio or wherever. Their prime business is **scamming shareholders**.\n\nPenny stocks are by their very nature risky. Most start up businesses fail. A business lucky enough to be publicly listed has access to equity and therefore capital. So should it fail, it can try to raise capital and try again. This results in dilution of the stock. But the key difference is that management is acting in good faith. They are trying to find a business that will eventually make shareholders money.\n\nA stock that is down 90% from say, 2015, obviously has failed. But you can still reasonably say management has acted in good faith.\n\nAdjusting for splits:\n\nXTIA has dropped from \\~$1,200 to $6 in less than a year.\n\nCRKN has dropped from \\~$30,000 to under $0.10 in three years.\n\nMULN has dropped from \\~$30,000,000 to under $0.50 in four years.\n\nTOPS has dropped from \\~$1,000,000,000,000,000 - that's right - one quadrillion to $7 in 20 years.\n\nA loss of 90%? Sure, management might still be acting in good faith even if they suck at what they do. But there is no way a stock can drop from $30,000 to $0.10 in three years without management purposely and actively acting AGAINST the will and interests of shareholders. It's just not possible to be THAT bad at business.\n\nHow do you chart for that? Seriously, how do you use TA on a stock like XTIA when its sole purpose of listing is to scam people to the benefit of those in charge of the listing? You don't. You just get mega-lucky if you happen bag profits on it.\n\nThink HARD about what charting actually is. It was created under normal market conditions with companies behaving generally in good faith. To try to determine investor buy and sell psychology. If a company failed, it went bankrupt. TA wasn't created in a time where it was possible for a company to string people along for 20 years to the point that their split-adjusted stock price has 15 zeros behind it. There is NO TA that is remotely reliable for these type of stocks and anyone trying to tell you that is lying to themselves or selling you a bill of goods. TA is used by these companies to generate retail liquidity fodder to dilute into and pay their next round of salaries.\n\nSo you want to know how to avoid traps? Look at a stock's 1 or 5 or 10 year chart. If the stock price is going from something like $50,000 to $1, it's very likely a dilution scam. It's such a simple screener that will be 99% right. And yet so few people do it then complain after the fact that they got a rug pull.\n\nAs for a stock like LODE, it was $75 in 2012 and is $0.28 today. Yeah, that's shit, but not quite at the level where you can conclude with certainty that management is acting in bad faith. LODE has been a science project for a long time, and those are notorious money burners. Those type of stocks you need to comb through with further research because they are in a gray area. But that doesn't stop you from being able to blacklist XTIA, CRKN, MULN, EFSH and dozens of others which clearly exist only to separate retail shareholders from their money. Then you will have more bandwidth to analyze the ability of stocks at LODE's level of quality or higher once you jettison all the crap.\n\nPS: Yeah, umm, that was me...but in the shortsqueeze sub, sorry:\n\nhttps://preview.redd.it/9chl2579e4de1.png?width=906&format=png&auto=webp&s=32803ff51a403d6bd44d4d723e2fee82c6e49f5a\n\n", "author": "jsmith108", "created_time": "2025-01-15T08:42:39", "url": "https://reddit.com/r/pennystocks/comments/1i1t7ej/advice_from_a_guy_who_has_been_doing_this_since/", "upvotes": 585, "comments_count": 142, "sentiment": "bearish", "engagement_score": 869.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i224gn", "title": "Google Searches for Deleting Facebook/Instagram Accounts ($META)", "content": "While media coverage on this topic has been a bit thin, Google Search analytics paint an interesting picture. Google queries for how to permanently delete Facebook/Instagram accounts have hit peak popularity (100/100) in the past day, with a 110% increase in the particular hour I checked this metric. I won't get into the speculation or drama on what is causing users to defect from the platform by permanently deleting their accounts, but I do believe $META investors should watch this carefully to see if this trend continues.", "author": "FinTecGeek", "created_time": "2025-01-15T17:04:50", "url": "https://reddit.com/r/ValueInvesting/comments/1i224gn/google_searches_for_deleting_facebookinstagram/", "upvotes": 166, "comments_count": 39, "sentiment": "neutral", "engagement_score": 244.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i23mpv", "title": "L.A. Wildfires: YouTube and Google Donating $15 Million, Meta and Zuckerberg to Give $4 Million to Recovery Orgs", "content": "", "author": "mcfw31", "created_time": "2025-01-15T18:08:00", "url": "https://reddit.com/r/UpliftingNews/comments/1i23mpv/la_wildfires_youtube_and_google_donating_15/", "upvotes": 985, "comments_count": 174, "sentiment": "neutral", "engagement_score": 1333.0, "source_subreddit": "UpliftingNews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i23zbo", "title": "[P] How I found & fixed 4 bugs in Microsoft's Phi-4 model", "content": "Hey r/MachineLearning! Last week, Microsoft released Phi-4, a 14B open-source model that rivals OpenAI's GPT-4-o-mini. I managed to find & fix 4 bugs impacting its output quality. You might remember me previously from [fixing 8 bugs](https://www.reddit.com/r/MachineLearning/comments/1bipsqj/p_how_i_found_8_bugs_in_googles_gemma_6t_token/) in Google's Gemma model! :)\n\nI'm going to walk you through how I found & fixed the bugs. Phi-4's benchmarks were amazing, however many users reported weird or just wrong outputs. Since I maintain the open-source project called '[Unsloth](https://github.com/unslothai/unsloth)' (fine-tuning LLMs 2x faster with 70% less VRAM) with my brother, I firstly tested Phi-4 for inference and found many errors. Our GitHub repo: [https://github.com/unslothai/unsloth](https://github.com/unslothai/unsloth)\n\nThis time, the model had no implementation issues (unlike Gemma 2) but did have problems in the model card. For my first inference run, I randomly found an extra token which is obviously incorrect (2 eos tokens is never a good idea). Also during more runs, I found there was an extra assistant prompt which is once again incorrect. And, lastly, from past experience with <PERSON><PERSON><PERSON><PERSON>'s bug fixes, I already knew fine-tuning was wrong when I read the code.\n\nThese bugs caused Phi-4 to have some drop in accuracy and also broke fine-tuning runs. Our fixes are now [under review by Microsoft](https://huggingface.co/microsoft/phi-4/discussions/21) to be officially added to Hugging Face. We uploaded the fixed versions to [https://huggingface.co/unsloth/phi-4-GGUF](https://huggingface.co/unsloth/phi-4-GGUF)\n\nHere’s a breakdown of the bugs and their fixes:\n\n***1. Tokenizer bug fixes***\n\nThe Phi-4 tokenizer interestingly uses <|endoftext|> as the BOS (beginning of sentence), EOS (end of sentence) and PAD (padding) tokens. The main issue is the EOS token is wrong - it should be <|im\\_end|>. Otherwise, you will get <|im\\_end|><|endoftext|> in generations.\n\n***2. Fine-tuning bug fixes***\n\nThe padding token should be a designated pad token like in Llama (<|finetune\\_right\\_pad\\_id|>) or we can use an untrained token - for example we use <|dummy\\_87|>, fixing infinite generations and outputs.\n\n***3. Chat template issues***\n\nThe Phi-4 tokenizer always adds an assistant prompt - it should only do this if prompted by add\\_generation\\_prompt. Most LLM serving libraries expect non auto assistant additions, and this might cause issues during serving.\n\n**We dive deeper into the bugs in our blog:** [**https://unsloth.ai/blog/phi4**](https://unsloth.ai/blog/phi4)\n\n# Do our Fixes Work?\n\nYes! Our fixed Phi-4 uploads show clear performance gains, with even better scores than Microsoft's original uploads on the [Open LLM Leaderboard](https://huggingface.co/spaces/open-llm-leaderboard/open_llm_leaderboard#/?search=phi-4).\n\nhttps://preview.redd.it/d8hew26e06ce1.png?width=2366&format=png&auto=webp&s=173c23feacc625566271470839fe7a5e25eb860e\n\nSome redditors even tested our fixes to show greatly improved results in:\n\n* Example 1: [Multiple-choice tasks](https://www.reddit.com/r/LocalLLaMA/comments/1hwzmqc/comment/m665h08/)\n\nhttps://preview.redd.it/qx50pkq706ce1.png?width=1579&format=png&auto=webp&s=437da2cabdbf98ef5a8b8cbdc5592907a20e2316\n\n* Example 2: [ASCII art generation](https://www.reddit.com/r/LocalLLaMA/comments/1hwzmqc/comment/m65wr3e/)\n\nhttps://preview.redd.it/sw1o3a3yt4de1.png?width=2326&format=png&auto=webp&s=fc6bfc45d14134d45f332ba58bbd1de049f5776b\n\nWe also made a Colab notebook fine-tune Phi-4 completely for free using Google's free Tesla T4 (16GB) GPUs: [https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Phi\\_4-Conversational.ipynb](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Phi_4-Conversational.ipynb)\n\nThank you for reading this long post and hope you all found this insightful! If you have any questions, please feel free to ask! :)\n\n**How I found the bugs:**\n\n1. I first downloaded the original Phi-4 from [https://huggingface.co/microsoft/phi-4](https://huggingface.co/microsoft/phi-4), and tested inference out. Weirdly I found `<|im_start|>assistant<|im_sep|>` to be appended at the even with `add_generation_prompt = False` in Hugging Face, so I theorized there was a chat template problem. Adding assistant prompts by default can break serving libraries.\n2. And yes, [https://huggingface.co/microsoft/phi-4/blob/f957856cd926f9d681b14153374d755dd97e45ed/tokenizer\\_config.json#L774](https://huggingface.co/microsoft/phi-4/blob/f957856cd926f9d681b14153374d755dd97e45ed/tokenizer_config.json#L774) had by default added the assistant prompt - I first fixed this!\n3. I then found `<|endoftext|>` to be used for the BOS, EOS and PAD tokens, which is a common issue amongst models - I ignored the BOS, since Phi-4 did not have one anyways, but changed the PAD token to `<|dummy_87|>`. You can select any of the tokens since they're empty and not trained. This counteracts issues of infinite generations during finetuning.\n4. For Llama-fication, I used torch.allclose to confirm all tensors are in fact equivalent. I also used some fake random data to check all activations are also mostly similar bitwise. I also uploaded the model to the HF [Open LLM Leaderboard](https://huggingface.co/spaces/open-llm-leaderboard/open_llm_leaderboard#/?search=phi-4) to confirm if the original Phi-4 arch and the new Llama-fied models are equivalent.\n5. Finally I verified all finetuning runs with Unsloth in a [Colab Notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Phi_4-Conversational.ipynb) to confirm all runs were correct.\n\n", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-15T18:22:48", "url": "https://reddit.com/r/MachineLearning/comments/1i23zbo/p_how_i_found_fixed_4_bugs_in_microsofts_phi4/", "upvotes": 317, "comments_count": 28, "sentiment": "bearish", "engagement_score": 373.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i27y7m", "title": "Trying to calculate how much battery discharge I should see over a period of time and 50 miliamp draw", "content": "I have car battery that I am going insane trying to diagnose why it keeps discharging so fast....I'm narrowing it down to just the fact that it's a cheaper battery then I previously had in....it's a $109 everstart plus battery...it's on a 2004 mustang with a 50 miliamp parasitic draw....\n\nThere is no amp hour rating but there's a 80 minute reserve capacity rating....not knowing any better I googled and found that if you take RC/60 X 25 You will get amp hour rating....\n\nI came up with 33 amp hours....which sounds like it's a very low rating but maybe this does explain why I have to start my car once every 5 days when good batteries should let you go once every 2 weeks...\n\nAny help in conversion would be greatly appreciated so I can stop troubleshooting my alternator and looking for maybe an intermittent parasitic draw....if I could do some math with a battery with higher reserve capacity then I can have peace of mind knowing that it is just a battery that is great for the price if you run it every day but not good for me being my 2nd vehicle that I don't use often", "author": "daveinfl337777", "created_time": "2025-01-15T21:12:49", "url": "https://reddit.com/r/batteries/comments/1i27y7m/trying_to_calculate_how_much_battery_discharge_i/", "upvotes": 1, "comments_count": 17, "sentiment": "neutral", "engagement_score": 35.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i29d4l", "title": "Guys, did Google just crack the Alberta Plan? Continual learning during inference?", "content": "Y'all seeing this too???\n\nhttps://arxiv.org/abs/2501.00663\n\nin 2025 <PERSON> really is vindicated with all his major talking points (like search time learning and RL reward functions) being the pivotal building blocks of AGI, huh?\n", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-15T22:14:01", "url": "https://reddit.com/r/singularity/comments/1i29d4l/guys_did_google_just_crack_the_alberta_plan/", "upvotes": 1175, "comments_count": 302, "sentiment": "neutral", "engagement_score": 1779.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}]