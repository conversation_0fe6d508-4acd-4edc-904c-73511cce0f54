[{"platform": "reddit", "post_id": "reddit_1jq4hf3", "title": "US Senate passed bill by slim margin in a 51-48 vote to block <PERSON>'s tariffs on imports from Canada", "content": "4 Republicans cross the floor to vote with Democrats to pass a bill that would remove import tariffs on Canadian goods.\n\nThis still needs to pass the house (which has republican majority), and even if it passes the house, president can still veto. At which point it goes back to the senate and 2/3 need to vote to overturn the veto.\n\nLow chance, but indication that dissent is happening within party lines given the economic downturn of tariff policy.\n\nInteresting to see how many more house reps and senators break from party lines after today's \"liberation\" tariffs have time to impact markets and consumer prices\n\n[https://www.nbcnews.com/politics/trump-administration/live-blog/trump-administration-tariffs-musk-elections-immigration-live-updates-rcna198941](https://www.nbcnews.com/politics/trump-administration/live-blog/trump-administration-tariffs-musk-elections-immigration-live-updates-rcna198941)", "author": "Mountain-Taro-123", "created_time": "2025-04-03T00:28:52", "url": "https://reddit.com/r/investing/comments/1jq4hf3/us_senate_passed_bill_by_slim_margin_in_a_5148/", "upvotes": 5807, "comments_count": 289, "sentiment": "neutral", "engagement_score": 6385.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jq6q99", "title": "Help, Google translate not working.🙏", "content": "This morning, I opened some online novels that I read via Google translating into English, everyday.\nBut its not working. Its not translating into English.😭\nEverytime I manually click the translate button, it says- 'Oops. This page could not be translated'\n", "author": "Klutzy_Interest5673", "created_time": "2025-04-03T02:16:14", "url": "https://reddit.com/r/chrome/comments/1jq6q99/help_google_translate_not_working/", "upvotes": 11, "comments_count": 16, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jq8idq", "title": "Canadian sticker business", "content": "A friend of mine has a Tesla and I want to buy her one of the \"Anti-Elon-Club\" or \"Bought before I knew how crazy he was\" stickers for her car for her birthday. Does anybody know a Canadian place or Canadian small business, where I can buy just a single sticker of each from? All I have found are businesses that sell them in huge quantities, but I only need one.", "author": "Conscious_Boat_4595", "created_time": "2025-04-03T03:46:27", "url": "https://reddit.com/r/smallbusiness/comments/1jq8idq/canadian_sticker_business/", "upvotes": 1, "comments_count": 2, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqecya", "title": "Direct mechanical water pumping for municipal water systems", "content": "  \nThe current method of using electric pumps to pump water through minimal water systems is not ideal because \n\n\\- During power outages running water also stops working because there is no electricity to power the pumps that pump water through municipal water systems. \n\n\\- Public water utilities have to pay for the electricity that they use to pump water through there distribution systems, which makes them vulnerable to electricity price fluctuation and prevents them from spending money on other aspects of there operations \n\nThe solution I propose is to directly use mechanical energy to pump water \n\nThe two best sources of mechanical enegry that can be used to pump water are hydro and geothermal. Both of these enegry sources are first converted into kinetic enegry vis a turbine before being converted into electricity via a generator. My idea is to mechanically link a water pump to either hydro or geothermal turbines so that they can be directly powered by the kinetic enegry produced by these turbines rather than being powered by the electricity generated by the turbines. In this setup, these turbines generate electricity and pump water at the same time because they are mechanically linked to both a generator and water pump via a gearbox. \n\nWhat do you think? ", "author": "Live_Alarm3041", "created_time": "2025-04-03T09:56:21", "url": "https://reddit.com/r/CleanEnergy/comments/1jqecya/direct_mechanical_water_pumping_for_municipal/", "upvotes": 1, "comments_count": 4, "sentiment": "bullish", "engagement_score": 9.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqffdl", "title": "What's up with <PERSON><PERSON><PERSON> and them having to caption EVERY SINGLE WORD they say?", "content": "title\n\nThey're really annoying. If I wanted captions I'd just turn them on myself\n\nEDIT: To everyone misunderstanding what I mean, I'm talking about [this example](https://www.youtube.com/shorts/Gekf6hDs-BE) (Ignore that it's shorts, this is just a good example that someone posted in comments already)", "author": "_No-Life_", "created_time": "2025-04-03T11:00:54", "url": "https://reddit.com/r/youtube/comments/1jqffdl/whats_up_with_ytbers_and_them_having_to_caption/", "upvotes": 2, "comments_count": 36, "sentiment": "bearish", "engagement_score": 74.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqfk82", "title": "Have only paid off 120k in 10 years.. is that bad?", "content": "Ok so my husband and I have a Morgage of 450k and we have paid so little off. I’m grateful that the Morgage. Is this really bad?? \n\nThe house still needs 150k worth of renovations to make it comfortable. We have 2 small kids and my husband is a sole trader which brings in enough for me to not work but we will never pay it off at this rate or build up any kind of portfolio even if we wanted to invest. Help! ", "author": "Early_Menu_9143", "created_time": "2025-04-03T11:08:10", "url": "https://reddit.com/r/FinancialPlanning/comments/1jqfk82/have_only_paid_off_120k_in_10_years_is_that_bad/", "upvotes": 4, "comments_count": 37, "sentiment": "neutral", "engagement_score": 78.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqgcw5", "title": "Remember, This Is The Pullback We’ve Been Waiting For", "content": "If you’re a long-term investor who even casually cares about valuation, this market has been tough to navigate for a while. Pullbacks are always something we say we want, particularly as value investors, but they usually come when things are scary. Financial crisis, global pandemics, policy shocks… the discount never shows up gift-wrapped.\n\nYesterday’s tariff news felt like one of those moments. It’s vague, feels arbitrary, and creates a lot of uncertainty. It feels scary. And yet, that’s exactly the environment where opportunities show up.\n\nI’ll admit it, days like today make me uneasy. But as an investor, I remind myself that underneath the noise, what’s really happening stocks are getting cheaper.\n\nAnd that’s what we’ve been waiting for.\n\nEdit: Thanks for the thoughts. I wrote a post - [Tariffs, Fear, and Opportunity: Perspective For Difficult Times In the Stock Market](https://arbalistmoney.com/tariffs-fear-and-opportunity-perspective-for-difficult-times-in-the-stock-market/) \\- to add some additional context directly addressing the response to this post.", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-04-03T11:51:01", "url": "https://reddit.com/r/ValueInvesting/comments/1jqgcw5/remember_this_is_the_pullback_weve_been_waiting/", "upvotes": 1055, "comments_count": 588, "sentiment": "bullish", "engagement_score": 2231.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqk2rs", "title": "<PERSON><PERSON><PERSON> Exercised my Put Option even though it Expired worthless", "content": "I sold a $468 QQQ strike price put option expiring yesterday 4/2. \n\nYesterday, QQQ closed at $476. So I thought - I'm good. \n\nI knew there was a high chance of market tanking after hours. But I wasn't concerned about this option - since at close it was worthless.\n\nThis morning I was debited $46,800 dollars for 100 QQQ shares.\n\nTurns out <PERSON><PERSON><PERSON> (and I guess other brokers) will accept orders to exercise options until 5 or 5:30 pm - even though a Schwab website itself says the option stops trading at 4:15. \n\nThis was an expensive lesson. ", "author": "Firm_Ratio_5216", "created_time": "2025-04-03T14:35:08", "url": "https://reddit.com/r/options/comments/1jqk2rs/schwab_exercised_my_put_option_even_though_it/", "upvotes": 95, "comments_count": 127, "sentiment": "neutral", "engagement_score": 349.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqk642", "title": "Is now actually the time to start investing?", "content": "With everything down so far, my uneducated line of logic says everything is cheap and my dollar will go farther. Does that make any sense? \n\nAnd I understand the sentiment that any time is the best time, was hoping for a different conversation.", "author": "UncleReDonk", "created_time": "2025-04-03T14:38:53", "url": "https://reddit.com/r/investing_discussion/comments/1jqk642/is_now_actually_the_time_to_start_investing/", "upvotes": 93, "comments_count": 358, "sentiment": "neutral", "engagement_score": 809.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqlomx", "title": "Are humans glorifying their cognition while resisting the reality that their thoughts and choices are rooted in predictable pattern-based systems—much like the very AI they often dismiss as \"mechanistic\"?", "content": "And do humans truly believe in their \"uniqueness\" or do they cling to it precisely because their brains are wired to reject patterns that undermine their sense of individuality?\n\nThis is part of what I think most people don't grasp and it's precisely why I argue that you need to reflect deeply on how your own cognition works before taking any sides.", "author": "ThrowRa-1995mf", "created_time": "2025-04-03T15:38:12", "url": "https://reddit.com/r/artificial/comments/1jqlomx/are_humans_glorifying_their_cognition_while/", "upvotes": 0, "comments_count": 65, "sentiment": "neutral", "engagement_score": 130.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqnhg4", "title": "VCR ETF or buying Amazon/Tesla individually", "content": "I’ve held VCR for some time know as I liked the holdings. I know it’s heavily weighted towards a few stocks. Looking back I feel it was a mistake to not buy Tesla and Amazon individually in order to get more exposure to them. \n\nYou obviously get some in S&P 500 ETFs but I wanted a more heavily weighted one. However, I am not really avoiding much downside risk vs holding them individually. Any thoughts would be great. ", "author": "Financial_Bug_5209", "created_time": "2025-04-03T16:47:41", "url": "https://reddit.com/r/ValueInvesting/comments/1jqnhg4/vcr_etf_or_buying_amazontesla_individually/", "upvotes": 2, "comments_count": 5, "sentiment": "bullish", "engagement_score": 12.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqnl0y", "title": "Polestar Stops Selling Entry-Level Model in the US Over 25% Tariffs", "content": "", "author": "Individual-Tart5051", "created_time": "2025-04-03T16:51:27", "url": "https://reddit.com/r/electriccars/comments/1jqnl0y/polestar_stops_selling_entrylevel_model_in_the_us/", "upvotes": 684, "comments_count": 61, "sentiment": "bearish", "engagement_score": 806.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqnra4", "title": "Tesla Board Asks <PERSON><PERSON> to Step Down", "content": "", "author": "lurker_bee", "created_time": "2025-04-03T16:58:26", "url": "https://reddit.com/r/economy/comments/1jqnra4/tesla_board_asks_elon_musk_to_step_down/", "upvotes": 306, "comments_count": 32, "sentiment": "neutral", "engagement_score": 370.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqu39x", "title": "How Could Trump Tariffs Affect Us Across PPC?", "content": "I’m not particularly political and usually don’t pay much attention to global politics, but with all the news surrounding tariffs, I started thinking about the potential impact these tariffs could have on our industry. I’m aware that the effects will mostly depend on the specific type of business and its location, but I’m curious if there are any points you can think of. Here are a few I considered:\n\n**Increased Costs for E-commerce Businesses:**\n\nTariffs on imported goods could raise production costs (especially for US companies), leading to price adjustments and potentially higher costs. As a result, businesses may reduce their ad spend or adjust bids to maintain profitability.\n\n\n\n**Higher Competition Due to Market Share:**\n\nIncreased ad spend from businesses trying to maintain market share could drive up the CPCs.This will most likely impact small businesses the most.\n\n\n\n**Changes in Consumer Behaviour:**\n\nHigher prices may make consumers more price-sensitive, which could lower conversion rates or lower ROAS.\n\n\n\n**Ad Targeting Adjustments:**\n\nGeopolitical uncertainty may prompt businesses to focus on regions less affected by the trade policies. For example, some biotech companies might reduce spend across the US and increase spend in EMEA or APAC regions.\n\n\n\n**CPC Increases:**\n\nWith more competition for market share and reduced sales due to shifts in consumer behaviour, some businesses might bid more aggressively to maintain revenue levels. This would likely lead to an increase in CPCs and big problem for small companies already struggling with GAds sales.\n\n\n\nCan you think of any other effects these tariffs might cause? I’m sure there are plenty more that I might have missed.", "author": "AdinityAI", "created_time": "2025-04-03T20:59:54", "url": "https://reddit.com/r/PPC/comments/1jqu39x/how_could_trump_tariffs_affect_us_across_ppc/", "upvotes": 2, "comments_count": 33, "sentiment": "bullish", "engagement_score": 68.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqy208", "title": "FIRE strategy . . . Could it work?", "content": "I’m nearing 50 and ready to be done w/ work. Also have 2 kids in elementary school and would like to spend as much time with them for the few short years before they grow up. Trying to plan retiring ASAP.  I’d like to hear advice/feedback on my plan so far. \n\nCurrently sitting ~ $700k in 401k, $60k in ROTH, $70k Bitcoin, $30k brokerage account. \n\nPlan: retire from my job. Roll $600k from 401k into an IRA, withdraw $100k (take tax hit) and move to brokerage account.  Each year for the next 6 years shift $100k from IRA to ROTH and pay tax penalty. \n\n$130k in brokerage account goes into MSTY for income. In IRA, $200k into MSTR, $200k TSLA, $200k QQQ. \n\nThis is based on my thesis that BTC, MSTR and TSLA will 5-10x over the next 5 years.  And when I get to 59.5 most all my $$ will be in a ROTH and tax free for the future. \n\nRisky, but my profession is fairly high demand and I don’t see AI changing that any time soon. So if things start looking like they won’t work out as planned, I can always go back to work. ", "author": "OkDiver6272", "created_time": "2025-04-03T23:49:29", "url": "https://reddit.com/r/Fire/comments/1jqy208/fire_strategy_could_it_work/", "upvotes": 0, "comments_count": 23, "sentiment": "bearish", "engagement_score": 46.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jqy486", "title": "Salt Water Small Engine", "content": "Reference  Aug 27, 2008  Waterfuel1978 Forum Briggs Magnetron with triple coils assembly, inductor,- used with standard Ignition coil bench tester. Daveand5 noted the leakage inductance crossover voltage 150v to the low volts side. When wires hooked up, the Briggs 15kv coil gave out an unusual 40kv. Range switch on 50kv. He noted also the plastic tub holder had helped raise up voltage. He suggested increasing the 2nd and third coil size for further change.After reading this paper over considerable amount of time, I recently got the idea of CORRELATIONS. I did this years ago with other subjects, and it works. If you do some bench tests and note 4 outcomes, but only mention 2 or 3 to others over long period, the people still have questions. The originator may deem it not significant to talk about.Based on these other CORRELATIONS I find that the Georgia people neglected to tell of ignition major voltage change, when firing spark plugs with the new S1R coils assembly. With the S1r coils having reversed source flows of power, there is inherent designed in interference placed on the spark cable-bare wire. This we already know forms longer on time of spark ATDC and mild magnetism at tips of plug. The Georgia techs failed to mention about increased volts firing the spark plugs. They called it boosting the ignition cable by adding more current. Volts + current are 2 different things and by different methods. Inverter output current and magnetic field impressed.(1.) The plastic deli tub holding the S1R electrical wiring acted as a capacitance for aiding the increased voltage of output. We know the general DC power supplies use a filter capacitor after the diodes, that raises up the end volts.(2.) The 2008 bench test indicated an abnormal change of ignition volts going as to 40KV. (3.) Nathren said the earlier 60 turns coil over plug wire test on fender of car engine formed magnetic field that fired spark plug in air with white/blue ball flame blast. Under compression it is stronger.(4.) Nathren said that the Briggs 18HP engine tests with S1r coils gave higher output voltage than the car engine ignition coils.Therefore, because of correlations, it is my contention that it is normal for our S1R windings coils to increase the Magnetron high voltage to a much higher range for firing saltwater solution for successful running a water engine.Put that in your pipe and smoke it.", "author": "Putrid-Bet7299", "created_time": "2025-04-03T23:52:33", "url": "https://reddit.com/r/CleanEnergy/comments/1jqy486/salt_water_small_engine/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}]