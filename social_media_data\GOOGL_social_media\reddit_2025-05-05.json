[{"platform": "reddit", "post_id": "reddit_1kf6qh7", "title": "Google chrome translates the word \"Arabic\" into Al-Qaeda", "content": "Funny and provocative by google chrome honestly. Whenever you try to translate a page from Arabic to English the word \"العربية\" translates into \"Al-Qaeda\"   \nNow if this was something against the LGBTQ community or the black community everyone would start talking about it, crazy. ", "author": "Rich_Ad_6869", "created_time": "2025-05-05T08:42:56", "url": "https://reddit.com/r/google/comments/1kf6qh7/google_chrome_translates_the_word_arabic_into/", "upvotes": 0, "comments_count": 16, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kfav8d", "title": "Google Ads Class Action Lawsuit Legit?", "content": "Received an email this morning notifying me I'm eligible for a settlement. Can anyone confirm of the below website is legitimate? \n\nhttps://www.adwordsclicksclassaction.com/\n\n*fully aware it will likely be peanuts, but want to make sure it isn't an outright scam.", "author": "AirPurifierQs", "created_time": "2025-05-05T12:53:07", "url": "https://reddit.com/r/marketing/comments/1kfav8d/google_ads_class_action_lawsuit_legit/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kfdgkc", "title": "Is This Google Ads Class Action Lawsuit Legit?", "content": "Received an email this morning notifying me I'm eligible for a settlement. Can anyone confirm of the below website is legitimate?\n\nhttps://www.adwordsclicksclassaction.com/\n\n*fully aware it will likely be peanuts, but want to make sure it isn't an outright scam.", "author": "AirPurifierQs", "created_time": "2025-05-05T14:48:22", "url": "https://reddit.com/r/adwords/comments/1kfdgkc/is_this_google_ads_class_action_lawsuit_legit/", "upvotes": 7, "comments_count": 43, "sentiment": "neutral", "engagement_score": 93.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kfoob0", "title": "So apparently gold is made in space… and now I kinda want to buy some?", "content": "Not trying to shill anything, but I went down a rabbit hole on gold recently and it kinda blew my mind.\n\nLike… did you know gold is literally formed in supernova explosions? Check this YouTube[ video](https://youtu.be/hWEl2_S6Mws?si=B1natIXBTn47O44c)!\n\nCurious what you all think! is anyone here actually holding gold? Or is it just boomers and doomsday preppers?", "author": "Intelligent_Hunt3392", "created_time": "2025-05-05T22:17:19", "url": "https://reddit.com/r/investing_discussion/comments/1kfoob0/so_apparently_gold_is_made_in_space_and_now_i/", "upvotes": 0, "comments_count": 32, "sentiment": "bullish", "engagement_score": 64.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kfop3o", "title": "Need Help Architecting Low-Latency, High-Concurrency Task Execution with Cloud Run (200+ tasks in parallel)", "content": "Hi all,\n\nI’m building a system on Google Cloud Platform and would love architectural input from someone experienced in designing **high-concurrency, low-latency pipelines** with **Cloud Run + task queues**.\n\n# 🚀 The Goal:\n\nI have an **API running on Cloud Run (Service)** that receives user requests and generates tasks.\n\nEach task takes **1–2 minutes on average**, sometimes up to **30 minutes**.\n\nMy goal is that when 100–200 tasks are submitted at once, they are **picked up and processed almost instantly** (within \\~10 seconds delay at most).\n\nIn other words: high parallelism with minimal latency and operational simplicity.\n\n# 🛠️ What I’ve Tried So Far:\n\n# 1. Pub/Sub (Push mode) to Cloud Run Service\n\n* Tasks are published to a Pub/Sub topic with a push subscription to a Cloud Run Service.\n* **Problem:** Push delivery doesn’t scale up fast enough. It uses a slow-start algorithm that gradually increases load.\n* **Another issue:** Cloud Run Service in push mode is **limited to 10 min processing** (ack deadline), but I need up to 30 mins.\n* **Bottom line:** latency is too high and burst handling is weak.\n\n# 2. Pub/Sub (Pull) with Dispatcher + Cloud Run Services\n\n* I created a **dispatcher** that pulls messages from Pub/Sub and dispatches them to Cloud Run Services (via HTTP).\n* Added counters and concurrency management (semaphores, thread pools).\n* **Problem:** Complex to manage state/concurrency across tasks, plus Cloud Run Services still don’t scale fast enough for a true burst.\n* Switched dispatcher to launch **Cloud Run Jobs** instead of Services.\n   * **Result:** even more latency (\\~2 minutes cold start per task) and way more complexity to orchestrate.\n\n**3. Cloud Tasks → Cloud Run Service**\n\n* Used Cloud Tasks with aggressive settings (max\\_dispatches\\_per\\_second, max\\_concurrent\\_dispatches, etc.).\n* Despite tweaking all limits, **Cloud Tasks dispatches very slowly** in practice.\n* Again, Cloud Run doesn’t burst fast enough to handle 100+ requests in parallel without serious delay.\n\n# 🤔 What I’m Looking For:\n\n* A **simple**, scalable design that allows:\n   * Accepting user requests via API\n   * Enqueuing tasks quickly\n   * Processing tasks **at scale (100–500 concurrent)** with **minimal latency (few seconds)**\n   * Keeping **task duration support up to 30 minutes**\n* Ideally using **Cloud Run, Pub/Sub, or Cloud Tasks**, but I’m open to creative use of GKE, Workflows, Eventarc, or even hybrid models if needed — as long as the **complexity is kept low.**\n\n**❓Questions:**\n\n* Has anyone built something similar with **Cloud Run** and succeeded with near real-time scaling?\n* Is **Cloud Run Job** ever a viable option for 100+ concurrent executions with fast startup?\n* Should I abandon Cloud Run for something else if low latency at high scale is essential?\n* Any creative use of **GKE Autopilot**, **Workflows**, or **Batch** that can act as “burstable” workers?\n\nWould appreciate any architectural suggestions, war stories, or even referrals to someone who’s built something similar.\n\nThanks so much 🙏", "author": "yakirbitan", "created_time": "2025-05-05T22:18:19", "url": "https://reddit.com/r/cloudcomputing/comments/1kfop3o/need_help_architecting_lowlatency_highconcurrency/", "upvotes": 3, "comments_count": 0, "sentiment": "bearish", "engagement_score": 3.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}]