#!/usr/bin/env python3
"""
Reddit API认证诊断工具
用于诊断401认证错误的具体原因
"""

import os
import praw
import prawcore
from dotenv import load_dotenv
import requests
import json

# 加载环境变量
load_dotenv()

def test_reddit_credentials():
    """测试Reddit API凭据"""
    print("=== Reddit API认证诊断 ===\n")

    # 获取配置
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')

    print("1. 检查环境变量配置:")
    print(f"   REDDIT_CLIENT_ID: {'✓' if client_id else '✗'} ({client_id[:10] + '...' if client_id else 'None'})")
    print(f"   REDDIT_CLIENT_SECRET: {'✓' if client_secret else '✗'} ({client_secret[:10] + '...' if client_secret else 'None'})")
    print(f"   REDDIT_USER_AGENT: {user_agent}")
    print(f"   REDDIT_USERNAME: {username}")
    print(f"   REDDIT_PASSWORD: {'✓' if password else '✗'}")
    print()

    # 检查凭据格式
    print("1.1 检查凭据格式:")
    if client_id:
        print(f"   Client ID长度: {len(client_id)} 字符")
        print(f"   Client ID格式: {'✓ 正常' if len(client_id) > 10 else '⚠️ 可能过短'}")
    if client_secret:
        print(f"   Client Secret长度: {len(client_secret)} 字符")
        print(f"   Client Secret格式: {'✓ 正常' if len(client_secret) > 20 else '⚠️ 可能过短'}")
    print()

    if not client_id or not client_secret:
        print("❌ 缺少必要的Reddit API凭据!")
        return False
    
    # 测试基本认证
    print("2. 测试基本认证:")
    try:
        # 创建Reddit客户端 - 只读模式
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # 测试访问公开子版块
        test_subreddit = reddit.subreddit('test')
        display_name = test_subreddit.display_name
        print(f"   ✓ 基本认证成功，可以访问 r/{display_name}")
        
    except prawcore.exceptions.ResponseException as e:
        if e.response.status_code == 401:
            print(f"   ❌ 401认证失败: {e}")
            print("   可能原因:")
            print("   - Client ID或Client Secret错误")
            print("   - Reddit应用类型设置错误（应该是'script'）")
            return False
        else:
            print(f"   ❌ 其他错误: {e}")
            return False
    except Exception as e:
        print(f"   ❌ 连接错误: {e}")
        return False
    
    # 测试获取帖子
    print("\n3. 测试获取帖子内容:")
    try:
        subreddit = reddit.subreddit('stocks')
        
        # 尝试获取最新帖子
        submissions = list(subreddit.new(limit=1))
        if submissions:
            submission = submissions[0]
            print(f"   ✓ 成功获取帖子: {submission.title[:50]}...")
            print(f"   帖子ID: {submission.id}")
            print(f"   作者: {submission.author}")
            print(f"   分数: {submission.score}")
        else:
            print("   ⚠️ 没有获取到帖子")
            
    except prawcore.exceptions.ResponseException as e:
        if e.response.status_code == 401:
            print(f"   ❌ 401错误 - 获取帖子失败: {e}")
            print("   这表明认证在获取内容时失败")
            return False
        elif e.response.status_code == 403:
            print(f"   ❌ 403错误 - 访问被禁止: {e}")
            print("   可能是IP限制或临时封禁")
            return False
        else:
            print(f"   ❌ 其他HTTP错误: {e}")
            return False
    except Exception as e:
        print(f"   ❌ 获取帖子失败: {e}")
        return False
    
    # 测试用户认证模式（如果有用户名密码）
    if username and password:
        print("\n4. 测试用户认证模式:")
        try:
            reddit_user = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent,
                username=username,
                password=password
            )
            
            # 测试获取用户信息
            user = reddit_user.user.me()
            print(f"   ✓ 用户认证成功: {user.name}")
            
        except prawcore.exceptions.ResponseException as e:
            if e.response.status_code == 401:
                print(f"   ❌ 用户认证失败: {e}")
                print("   用户名或密码可能错误")
            else:
                print(f"   ❌ 用户认证错误: {e}")
        except Exception as e:
            print(f"   ❌ 用户认证异常: {e}")
    
    # 测试直接API调用
    print("\n5. 测试直接API调用:")
    try:
        import base64
        
        # 创建基本认证头
        auth_string = f"{client_id}:{client_secret}"
        auth_bytes = auth_string.encode('ascii')
        auth_b64 = base64.b64encode(auth_bytes).decode('ascii')
        
        headers = {
            'Authorization': f'Basic {auth_b64}',
            'User-Agent': user_agent
        }
        
        # 获取访问令牌
        data = {'grant_type': 'client_credentials'}
        response = requests.post('https://www.reddit.com/api/v1/access_token', 
                               headers=headers, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get('access_token')
            print(f"   ✓ 成功获取访问令牌: {access_token[:20]}...")
            
            # 使用令牌测试API调用
            api_headers = {
                'Authorization': f'Bearer {access_token}',
                'User-Agent': user_agent
            }
            
            api_response = requests.get('https://oauth.reddit.com/r/stocks/new.json?limit=1', 
                                      headers=api_headers)
            
            if api_response.status_code == 200:
                print("   ✓ API调用成功")
                data = api_response.json()
                if data.get('data', {}).get('children'):
                    post = data['data']['children'][0]['data']
                    print(f"   获取到帖子: {post.get('title', '')[:50]}...")
                else:
                    print("   ⚠️ 没有获取到帖子数据")
            else:
                print(f"   ❌ API调用失败: {api_response.status_code} - {api_response.text}")
                
        else:
            print(f"   ❌ 获取访问令牌失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"   ❌ 直接API调用异常: {e}")
    
    print("\n=== 诊断完成 ===")
    return True

def main():
    """主函数"""
    success = test_reddit_credentials()
    
    if not success:
        print("\n🔧 修复建议:")
        print("1. 检查Reddit应用设置:")
        print("   - 访问 https://www.reddit.com/prefs/apps")
        print("   - 确认应用类型为 'script'")
        print("   - 重新生成Client ID和Secret")
        print()
        print("2. 检查.env文件配置:")
        print("   - REDDIT_CLIENT_ID应该是应用下方的ID")
        print("   - REDDIT_CLIENT_SECRET应该是'secret'字段的值")
        print("   - REDDIT_USER_AGENT格式: 'appname/version'")
        print()
        print("3. 如果问题持续:")
        print("   - 尝试创建新的Reddit应用")
        print("   - 检查IP是否被Reddit限制")
        print("   - 等待一段时间后重试")

if __name__ == '__main__':
    main()
