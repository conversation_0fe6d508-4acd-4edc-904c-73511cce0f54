#!/usr/bin/env python3
"""
Reddit凭据修复脚本
解决系统环境变量覆盖.env文件的问题
"""

import os
import sys
from dotenv import load_dotenv
import praw
import prawcore

def clear_system_env_vars():
    """清除系统环境变量中的Reddit配置"""
    reddit_vars = [
        'REDDIT_CLIENT_ID',
        'REDDIT_CLIENT_SECRET', 
        'REDDIT_USER_AGENT',
        'REDDIT_USERNAME',
        'REDDIT_PASSWORD'
    ]
    
    print("=== 清除系统环境变量 ===")
    for var in reddit_vars:
        if var in os.environ:
            print(f"清除系统环境变量: {var}")
            del os.environ[var]
        else:
            print(f"系统环境变量不存在: {var}")
    print()

def load_env_file_credentials():
    """从.env文件加载凭据"""
    print("=== 从.env文件加载凭据 ===")
    
    # 强制重新加载.env文件
    load_dotenv(override=True)
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    print(f"CLIENT_ID: {client_id}")
    print(f"CLIENT_SECRET: {client_secret[:10] + '...' if client_secret else 'None'}")
    print(f"USER_AGENT: {user_agent}")
    print(f"USERNAME: {username}")
    print(f"PASSWORD: {'✓' if password else '✗'}")
    print()
    
    return client_id, client_secret, user_agent, username, password

def test_new_credentials(client_id, client_secret, user_agent, username, password):
    """测试新凭据"""
    print("=== 测试新凭据 ===")
    
    try:
        # 创建Reddit客户端
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent,
            username=username,
            password=password
        )
        
        # 测试基本认证
        print("1. 测试基本认证...")
        test_sub = reddit.subreddit('test')
        print(f"   ✓ 可以访问 r/test: {test_sub.display_name}")
        
        # 测试获取帖子
        print("2. 测试获取帖子...")
        posts = list(test_sub.new(limit=1))
        if posts:
            post = posts[0]
            print(f"   ✓ 成功获取帖子: {post.title[:50]}...")
        else:
            print("   ⚠️ 没有找到帖子，但认证成功")
        
        # 测试用户认证
        print("3. 测试用户认证...")
        user = reddit.user.me()
        print(f"   ✓ 用户认证成功: {user.name}")
        
        print("\n✅ 所有测试通过！新凭据工作正常")
        return True
        
    except prawcore.exceptions.ResponseException as e:
        if e.response.status_code == 401:
            print(f"   ❌ 401认证错误: {e}")
            print("   可能原因:")
            print("   - Client ID或Secret错误")
            print("   - 应用类型不是'script'")
            print("   - 用户名或密码错误")
        elif e.response.status_code == 403:
            print(f"   ❌ 403访问被禁止: {e}")
            print("   可能原因:")
            print("   - IP被限制")
            print("   - 应用权限不足")
        else:
            print(f"   ❌ HTTP错误 {e.response.status_code}: {e}")
        return False
        
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        return False

def create_test_script():
    """创建测试脚本"""
    test_script = '''#!/usr/bin/env python3
"""
Reddit凭据测试脚本
使用修复后的凭据进行测试
"""

import os
from dotenv import load_dotenv
import praw

# 清除系统环境变量
reddit_vars = ['REDDIT_CLIENT_ID', 'REDDIT_CLIENT_SECRET', 'REDDIT_USER_AGENT', 'REDDIT_USERNAME', 'REDDIT_PASSWORD']
for var in reddit_vars:
    if var in os.environ:
        del os.environ[var]

# 强制从.env文件加载
load_dotenv(override=True)

# 创建Reddit客户端
reddit = praw.Reddit(
    client_id=os.getenv('REDDIT_CLIENT_ID'),
    client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
    user_agent=os.getenv('REDDIT_USER_AGENT'),
    username=os.getenv('REDDIT_USERNAME'),
    password=os.getenv('REDDIT_PASSWORD')
)

# 测试
try:
    user = reddit.user.me()
    print(f"✅ Reddit认证成功: {user.name}")
    
    # 测试获取帖子
    test_sub = reddit.subreddit('test')
    posts = list(test_sub.new(limit=1))
    print(f"✅ 成功获取 {len(posts)} 个帖子")
    
except Exception as e:
    print(f"❌ 认证失败: {e}")
'''
    
    with open('test_fixed_reddit.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 创建测试脚本: test_fixed_reddit.py")

def main():
    """主函数"""
    print("Reddit凭据修复工具\n")
    
    # 1. 清除系统环境变量
    clear_system_env_vars()
    
    # 2. 从.env文件加载凭据
    credentials = load_env_file_credentials()
    client_id, client_secret, user_agent, username, password = credentials
    
    if not client_id or not client_secret:
        print("❌ .env文件中缺少必要的Reddit凭据!")
        return 1
    
    # 3. 测试新凭据
    success = test_new_credentials(*credentials)
    
    if success:
        # 4. 创建测试脚本
        create_test_script()
        
        print("\n🎉 修复完成!")
        print("\n下一步:")
        print("1. 运行测试脚本: python test_fixed_reddit.py")
        print("2. 如果测试通过，重启终端清除环境变量")
        print("3. 运行原始收集器: python reddit_live_collector.py")
        
        return 0
    else:
        print("\n❌ 凭据测试失败!")
        print("\n请检查:")
        print("1. Reddit应用设置: https://www.reddit.com/prefs/apps")
        print("2. 确认应用类型为'script'")
        print("3. 重新生成Client ID和Secret")
        print("4. 检查用户名和密码")
        
        return 1

if __name__ == '__main__':
    exit(main())
