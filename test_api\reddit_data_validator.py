#!/usr/bin/env python3
"""
Reddit数据验证和统计工具

验证收集的Reddit数据质量并生成统计报告
"""

import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter
import pandas as pd

class RedditDataValidator:
    """Reddit数据验证器"""
    
    def __init__(self, data_dir: str = "social_media_data"):
        """
        初始化验证器
        
        Args:
            data_dir: 数据目录
        """
        self.data_dir = Path(data_dir)
        self.stats = defaultdict(int)
        self.errors = []
        self.warnings = []
    
    def validate_post_structure(self, post: Dict[str, Any], file_path: Path) -> bool:
        """验证帖子数据结构"""
        required_fields = [
            'platform', 'post_id', 'title', 'content', 'author',
            'created_time', 'url', 'upvotes', 'comments_count',
            'ticker', 'sentiment', 'engagement_score', 'source_subreddit'
        ]
        
        is_valid = True
        
        for field in required_fields:
            if field not in post:
                self.errors.append(f"{file_path}: 缺少字段 '{field}' in post {post.get('post_id', 'unknown')}")
                is_valid = False
        
        # 验证数据类型
        if 'created_time' in post:
            try:
                datetime.fromisoformat(post['created_time'])
            except ValueError:
                self.errors.append(f"{file_path}: 无效的时间格式 '{post['created_time']}'")
                is_valid = False
        
        if 'upvotes' in post and not isinstance(post['upvotes'], (int, float)):
            self.warnings.append(f"{file_path}: upvotes应为数字类型")
        
        if 'comments_count' in post and not isinstance(post['comments_count'], (int, float)):
            self.warnings.append(f"{file_path}: comments_count应为数字类型")
        
        if 'sentiment' in post and post['sentiment'] not in ['bullish', 'bearish', 'neutral']:
            self.warnings.append(f"{file_path}: 无效的情感值 '{post['sentiment']}'")
        
        return is_valid
    
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """验证单个文件"""
        file_stats = {
            'total_posts': 0,
            'valid_posts': 0,
            'invalid_posts': 0,
            'date_range': None,
            'tickers': set(),
            'subreddits': set(),
            'sentiments': Counter(),
            'authors': set()
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                posts = json.load(f)
            
            if not isinstance(posts, list):
                self.errors.append(f"{file_path}: 文件格式错误，应为JSON数组")
                return file_stats
            
            file_stats['total_posts'] = len(posts)
            dates = []
            
            for post in posts:
                if self.validate_post_structure(post, file_path):
                    file_stats['valid_posts'] += 1
                    
                    # 收集统计信息
                    if 'ticker' in post:
                        file_stats['tickers'].add(post['ticker'])
                    
                    if 'source_subreddit' in post:
                        file_stats['subreddits'].add(post['source_subreddit'])
                    
                    if 'sentiment' in post:
                        file_stats['sentiments'][post['sentiment']] += 1
                    
                    if 'author' in post and post['author'] != 'deleted':
                        file_stats['authors'].add(post['author'])
                    
                    if 'created_time' in post:
                        try:
                            date = datetime.fromisoformat(post['created_time'])
                            dates.append(date)
                        except ValueError:
                            pass
                else:
                    file_stats['invalid_posts'] += 1
            
            # 计算日期范围
            if dates:
                file_stats['date_range'] = (min(dates), max(dates))
            
        except json.JSONDecodeError as e:
            self.errors.append(f"{file_path}: JSON解析错误 - {e}")
        except Exception as e:
            self.errors.append(f"{file_path}: 读取文件错误 - {e}")
        
        return file_stats
    
    def scan_data_directory(self) -> Dict[str, Any]:
        """扫描数据目录"""
        overall_stats = {
            'total_files': 0,
            'valid_files': 0,
            'total_posts': 0,
            'valid_posts': 0,
            'tickers': defaultdict(int),
            'subreddits': defaultdict(int),
            'sentiments': Counter(),
            'date_coverage': {},
            'file_details': {}
        }
        
        if not self.data_dir.exists():
            self.errors.append(f"数据目录不存在: {self.data_dir}")
            return overall_stats
        
        # 查找所有JSON文件
        json_files = list(self.data_dir.rglob("*.json"))
        overall_stats['total_files'] = len(json_files)
        
        print(f"找到 {len(json_files)} 个JSON文件")
        
        for file_path in json_files:
            print(f"验证: {file_path.relative_to(self.data_dir)}")
            
            file_stats = self.validate_file(file_path)
            
            if file_stats['valid_posts'] > 0:
                overall_stats['valid_files'] += 1
            
            overall_stats['total_posts'] += file_stats['total_posts']
            overall_stats['valid_posts'] += file_stats['valid_posts']
            
            # 合并统计
            for ticker in file_stats['tickers']:
                overall_stats['tickers'][ticker] += file_stats['valid_posts']
            
            for subreddit in file_stats['subreddits']:
                overall_stats['subreddits'][subreddit] += 1
            
            overall_stats['sentiments'].update(file_stats['sentiments'])
            
            # 记录文件详情
            relative_path = str(file_path.relative_to(self.data_dir))
            overall_stats['file_details'][relative_path] = file_stats
            
            # 日期覆盖
            if file_stats['date_range']:
                start_date, end_date = file_stats['date_range']
                date_key = start_date.strftime('%Y-%m-%d')
                if date_key not in overall_stats['date_coverage']:
                    overall_stats['date_coverage'][date_key] = 0
                overall_stats['date_coverage'][date_key] += file_stats['valid_posts']
        
        return overall_stats
    
    def generate_report(self, stats: Dict[str, Any]) -> str:
        """生成验证报告"""
        report = []
        report.append("=" * 60)
        report.append("Reddit数据验证报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 总体统计
        report.append("📊 总体统计")
        report.append("-" * 30)
        report.append(f"总文件数: {stats['total_files']}")
        report.append(f"有效文件数: {stats['valid_files']}")
        report.append(f"总帖子数: {stats['total_posts']:,}")
        report.append(f"有效帖子数: {stats['valid_posts']:,}")
        
        if stats['total_posts'] > 0:
            validity_rate = (stats['valid_posts'] / stats['total_posts']) * 100
            report.append(f"数据有效率: {validity_rate:.1f}%")
        
        report.append("")
        
        # 股票覆盖
        if stats['tickers']:
            report.append("📈 股票覆盖")
            report.append("-" * 30)
            sorted_tickers = sorted(stats['tickers'].items(), key=lambda x: x[1], reverse=True)
            for ticker, count in sorted_tickers[:10]:  # 显示前10个
                report.append(f"{ticker}: {count:,} 帖子")
            report.append("")
        
        # 子版块分布
        if stats['subreddits']:
            report.append("🏷️ 子版块分布")
            report.append("-" * 30)
            sorted_subreddits = sorted(stats['subreddits'].items(), key=lambda x: x[1], reverse=True)
            for subreddit, count in sorted_subreddits[:10]:  # 显示前10个
                report.append(f"r/{subreddit}: {count:,} 文件")
            report.append("")
        
        # 情感分析分布
        if stats['sentiments']:
            report.append("😊 情感分析分布")
            report.append("-" * 30)
            total_sentiment = sum(stats['sentiments'].values())
            for sentiment, count in stats['sentiments'].most_common():
                percentage = (count / total_sentiment) * 100
                report.append(f"{sentiment}: {count:,} ({percentage:.1f}%)")
            report.append("")
        
        # 日期覆盖
        if stats['date_coverage']:
            report.append("📅 日期覆盖")
            report.append("-" * 30)
            sorted_dates = sorted(stats['date_coverage'].items())
            report.append(f"日期范围: {sorted_dates[0][0]} 到 {sorted_dates[-1][0]}")
            report.append(f"覆盖天数: {len(sorted_dates)}")
            
            # 显示最近7天的数据
            recent_dates = sorted_dates[-7:]
            report.append("最近7天数据:")
            for date, count in recent_dates:
                report.append(f"  {date}: {count:,} 帖子")
            report.append("")
        
        # 错误和警告
        if self.errors:
            report.append("❌ 错误")
            report.append("-" * 30)
            for error in self.errors[:20]:  # 显示前20个错误
                report.append(f"  {error}")
            if len(self.errors) > 20:
                report.append(f"  ... 还有 {len(self.errors) - 20} 个错误")
            report.append("")
        
        if self.warnings:
            report.append("⚠️ 警告")
            report.append("-" * 30)
            for warning in self.warnings[:20]:  # 显示前20个警告
                report.append(f"  {warning}")
            if len(self.warnings) > 20:
                report.append(f"  ... 还有 {len(self.warnings) - 20} 个警告")
            report.append("")
        
        # 建议
        report.append("💡 建议")
        report.append("-" * 30)
        
        if stats['valid_posts'] == 0:
            report.append("- 没有找到有效数据，请检查数据收集配置")
        elif len(self.errors) > 0:
            report.append("- 发现数据错误，建议修复后重新验证")
        elif stats['valid_posts'] < 1000:
            report.append("- 数据量较少，建议增加收集时间范围或子版块")
        else:
            report.append("- 数据质量良好，可以用于分析")
        
        if len(stats['tickers']) < 5:
            report.append("- 股票覆盖较少，建议添加更多股票关键词")
        
        return "\n".join(report)
    
    def export_stats_to_csv(self, stats: Dict[str, Any], output_file: str):
        """导出统计数据到CSV"""
        # 创建DataFrame
        data = []
        
        for file_path, file_stats in stats['file_details'].items():
            ticker = file_path.split('_')[0] if '_' in file_path else 'unknown'
            date_str = file_path.split('_')[-1].replace('.json', '').replace('reddit_', '')
            
            data.append({
                'file': file_path,
                'ticker': ticker,
                'date': date_str,
                'total_posts': file_stats['total_posts'],
                'valid_posts': file_stats['valid_posts'],
                'invalid_posts': file_stats['invalid_posts'],
                'tickers_count': len(file_stats['tickers']),
                'subreddits_count': len(file_stats['subreddits']),
                'authors_count': len(file_stats['authors']),
                'bullish_count': file_stats['sentiments'].get('bullish', 0),
                'bearish_count': file_stats['sentiments'].get('bearish', 0),
                'neutral_count': file_stats['sentiments'].get('neutral', 0)
            })
        
        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False)
        print(f"统计数据已导出到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Reddit数据验证工具')
    parser.add_argument('--data-dir', default='social_media_data',
                       help='数据目录')
    parser.add_argument('--output-report', default='reddit_validation_report.txt',
                       help='报告输出文件')
    parser.add_argument('--export-csv', 
                       help='导出统计数据到CSV文件')
    parser.add_argument('--quiet', action='store_true',
                       help='静默模式')
    
    args = parser.parse_args()
    
    try:
        # 创建验证器
        validator = RedditDataValidator(args.data_dir)
        
        if not args.quiet:
            print("开始验证Reddit数据...")
        
        # 扫描数据
        stats = validator.scan_data_directory()
        
        # 生成报告
        report = validator.generate_report(stats)
        
        # 保存报告
        with open(args.output_report, 'w', encoding='utf-8') as f:
            f.write(report)
        
        if not args.quiet:
            print(f"\n验证报告已保存到: {args.output_report}")
            print("\n" + "="*60)
            print(report)
        
        # 导出CSV
        if args.export_csv:
            validator.export_stats_to_csv(stats, args.export_csv)
        
        # 返回状态码
        if validator.errors:
            return 1  # 有错误
        else:
            return 0  # 成功
        
    except Exception as e:
        print(f"验证失败: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
