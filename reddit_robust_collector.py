#!/usr/bin/env python3
"""
Reddit稳健数据收集器
专门解决401/403错误和网络连接问题的改进版本
"""

import os
import json
import time
import logging
import random
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import argparse

import praw
import prawcore
from dotenv import load_dotenv
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import urllib3

# 加载环境变量
load_dotenv()

class RobustRedditCollector:
    """稳健的Reddit数据收集器"""
    
    def __init__(self, output_dir: str = "social_media_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 创建Reddit客户端
        self.reddit = self.create_robust_reddit_client()
        
        # 股票关键词
        self.ticker_keywords = {
            'AAPL': ['Apple', 'AAPL', 'iPhone', 'iPad'],
            'MSFT': ['Microsoft', 'MSFT', 'Windows', 'Azure'],
            'NVDA': ['NVIDIA', 'NVDA', 'GeForce', 'RTX'],
            'GOOGL': ['Google', 'GOOGL', 'Alphabet'],
            'TSLA': ['Tesla', 'TSLA', 'Elon Musk']
        }
        
        # 简化的子版块列表（避免访问限制）
        self.safe_subreddits = ['test', 'stocks', 'investing']
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.output_dir / "robust_collector.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_robust_reddit_client(self) -> praw.Reddit:
        """创建稳健的Reddit客户端"""
        client_id = os.getenv('REDDIT_CLIENT_ID')
        client_secret = os.getenv('REDDIT_CLIENT_SECRET')
        user_agent = os.getenv('REDDIT_USER_AGENT', 'robust-collector/1.0')
        username = os.getenv('REDDIT_USERNAME')
        password = os.getenv('REDDIT_PASSWORD')
        
        if not client_id or not client_secret:
            raise ValueError("缺少Reddit API凭据")
        
        try:
            # 禁用SSL警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            # 创建自定义会话
            session = requests.Session()
            
            # 更保守的重试策略
            retry_strategy = Retry(
                total=3,  # 减少重试次数
                backoff_factor=3,  # 增加退避时间
                status_forcelist=[429, 500, 502, 503, 504],  # 移除401和403
                allowed_methods=["GET"],
                raise_on_status=False
            )
            
            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)
            
            # 设置更长的超时时间
            session.timeout = (60, 120)
            
            # 优先使用用户认证
            if username and password:
                self.logger.info("使用用户认证模式")
                reddit = praw.Reddit(
                    client_id=client_id,
                    client_secret=client_secret,
                    user_agent=user_agent,
                    username=username,
                    password=password,
                    requestor_kwargs={'session': session}
                )
            else:
                self.logger.info("使用应用认证模式")
                reddit = praw.Reddit(
                    client_id=client_id,
                    client_secret=client_secret,
                    user_agent=user_agent,
                    requestor_kwargs={'session': session}
                )
            
            # 验证连接
            self.verify_connection(reddit)
            return reddit
            
        except Exception as e:
            self.logger.error(f"创建Reddit客户端失败: {e}")
            raise
    
    def verify_connection(self, reddit: praw.Reddit):
        """验证Reddit连接"""
        try:
            # 测试访问一个简单的子版块
            test_sub = reddit.subreddit('test')
            _ = test_sub.display_name
            self.logger.info("Reddit连接验证成功")
        except Exception as e:
            self.logger.warning(f"Reddit连接验证失败: {e}")
            # 不抛出异常，允许继续尝试
    
    def safe_collect_posts(self, subreddit_name: str, limit: int = 10) -> List[Dict]:
        """安全地收集帖子，带有完善的错误处理"""
        posts = []
        max_retries = 3
        base_delay = 60  # 基础延迟60秒
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试收集 r/{subreddit_name} (第{attempt+1}次)")
                
                # 随机延迟避免被检测
                if attempt > 0:
                    delay = base_delay * (2 ** attempt) + random.randint(10, 30)
                    self.logger.info(f"等待 {delay} 秒后重试...")
                    time.sleep(delay)
                
                subreddit = self.reddit.subreddit(subreddit_name)
                
                # 先测试子版块访问
                try:
                    _ = subreddit.display_name
                except prawcore.exceptions.Forbidden:
                    self.logger.warning(f"r/{subreddit_name} 访问被禁止，跳过")
                    return posts
                except prawcore.exceptions.NotFound:
                    self.logger.warning(f"r/{subreddit_name} 不存在，跳过")
                    return posts
                
                # 获取帖子
                submissions = list(subreddit.new(limit=limit))
                
                for submission in submissions:
                    try:
                        # 检查是否包含股票关键词
                        title = submission.title or ""
                        content = submission.selftext or ""
                        full_text = f"{title} {content}".lower()
                        
                        found_tickers = []
                        for ticker, keywords in self.ticker_keywords.items():
                            for keyword in keywords:
                                if keyword.lower() in full_text:
                                    found_tickers.append(ticker)
                                    break
                        
                        if found_tickers:
                            post_data = {
                                'platform': 'reddit',
                                'post_id': f"reddit_{submission.id}",
                                'title': title,
                                'content': content,
                                'author': str(submission.author) if submission.author else 'deleted',
                                'created_time': datetime.fromtimestamp(submission.created_utc).isoformat(),
                                'url': f"https://reddit.com{submission.permalink}",
                                'upvotes': submission.score,
                                'comments_count': submission.num_comments,
                                'tickers': found_tickers,
                                'source_subreddit': subreddit_name
                            }
                            posts.append(post_data)
                        
                        # 添加延迟避免API限制
                        time.sleep(1)
                        
                    except Exception as e:
                        self.logger.warning(f"处理帖子失败: {e}")
                        continue
                
                self.logger.info(f"成功收集 r/{subreddit_name}: {len(posts)} 个相关帖子")
                return posts
                
            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 401:
                    self.logger.error(f"401认证错误: {e}")
                    break  # 认证错误不重试
                elif e.response.status_code == 403:
                    self.logger.warning(f"403访问被禁止: {e}")
                    if attempt < max_retries - 1:
                        continue  # 403错误可以重试
                    else:
                        self.logger.error("多次403错误，可能IP被限制")
                        break
                elif e.response.status_code == 429:
                    self.logger.warning(f"429请求过多: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(300)  # 等待5分钟
                        continue
                    else:
                        break
                else:
                    self.logger.error(f"其他HTTP错误: {e}")
                    break
                    
            except Exception as e:
                self.logger.error(f"收集失败: {e}")
                if attempt < max_retries - 1:
                    continue
                else:
                    break
        
        return posts
    
    def save_posts(self, posts: List[Dict]):
        """保存帖子数据"""
        if not posts:
            return
        
        # 按股票和日期组织数据
        posts_by_ticker_date = {}
        
        for post in posts:
            created_time = datetime.fromisoformat(post['created_time'])
            date_str = created_time.strftime('%Y-%m-%d')
            
            for ticker in post['tickers']:
                key = (ticker, date_str)
                if key not in posts_by_ticker_date:
                    posts_by_ticker_date[key] = []
                
                ticker_post = post.copy()
                ticker_post['ticker'] = ticker
                del ticker_post['tickers']
                posts_by_ticker_date[key].append(ticker_post)
        
        # 保存文件
        for (ticker, date_str), ticker_posts in posts_by_ticker_date.items():
            ticker_dir = self.output_dir / f"{ticker}_social_media"
            ticker_dir.mkdir(parents=True, exist_ok=True)
            
            file_path = ticker_dir / f"reddit_{date_str}.json"
            
            # 合并现有数据
            existing_posts = []
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        existing_posts = json.load(f)
                except Exception as e:
                    self.logger.warning(f"读取现有文件失败: {e}")
            
            # 去重并保存
            existing_ids = {post.get('post_id') for post in existing_posts}
            new_posts = [post for post in ticker_posts 
                        if post.get('post_id') not in existing_ids]
            
            if new_posts:
                all_posts = existing_posts + new_posts
                all_posts.sort(key=lambda x: x.get('created_time', ''))
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(all_posts, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"保存 {ticker} {date_str}: {len(new_posts)} 个新帖子")
    
    def collect_data(self, subreddits: Optional[List[str]] = None, 
                    limit_per_subreddit: int = 10) -> Dict[str, int]:
        """收集数据主函数"""
        target_subreddits = subreddits or self.safe_subreddits
        
        self.logger.info(f"开始收集Reddit数据")
        self.logger.info(f"目标子版块: {target_subreddits}")
        
        all_posts = []
        stats = {'subreddits_processed': 0, 'total_posts': 0}
        
        for subreddit_name in target_subreddits:
            try:
                posts = self.safe_collect_posts(subreddit_name, limit_per_subreddit)
                all_posts.extend(posts)
                stats['subreddits_processed'] += 1
                stats['total_posts'] += len(posts)
                
                # 每个子版块之间增加延迟
                time.sleep(30)
                
            except Exception as e:
                self.logger.error(f"处理 r/{subreddit_name} 失败: {e}")
                continue
        
        # 保存数据
        self.save_posts(all_posts)
        
        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='稳健的Reddit数据收集器')
    parser.add_argument('--subreddits', nargs='+', 
                       default=['test', 'stocks'],
                       help='目标子版块')
    parser.add_argument('--limit', type=int, default=10,
                       help='每个子版块的帖子限制')
    parser.add_argument('--output-dir', default='social_media_data',
                       help='输出目录')
    
    args = parser.parse_args()
    
    try:
        collector = RobustRedditCollector(args.output_dir)
        stats = collector.collect_data(args.subreddits, args.limit)
        
        print("\n=== 收集完成 ===")
        print(f"处理子版块数: {stats['subreddits_processed']}")
        print(f"收集帖子数: {stats['total_posts']}")
        print(f"数据保存在: {args.output_dir}")
        
        return 0
        
    except Exception as e:
        print(f"收集失败: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
