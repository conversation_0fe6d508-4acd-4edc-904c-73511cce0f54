{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-01-15", "api_calls": {"load_local_social_media_data": {"data": [{"title": "MSFT tech Interview: entry level", "content": "I had my virtual loop interview, 3 rounds each of 45 min. My laptop has some trouble with teams and I got super tensed before the interview started. First round I complicated it by trying to code for O(N) rather than O(N**2) and explaining the O(N**2) solution, the second round I was almost able to code the whole thing and the third round went well too. Do I have still have any chance? Or should I just give up on MSFT and continue applying?", "created_time": "2025-01-15T00:54:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 18.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Curious_Fun8087", "url": "https://reddit.com/r/microsoft/comments/1i1lif5/msft_tech_interview_entry_level/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "What do you think are the most powerful features in Microsoft Azure AI?", "content": "I'm in the process of familiarizing myself with Microsoft Azure AI and its features to improve my projects. I was curious what you think, what is the feature you use the most and what is your favorite´?", "created_time": "2025-01-15T08:24:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "JKOE21", "url": "https://reddit.com/r/microsoft/comments/1i1sz84/what_do_you_think_are_the_most_powerful_features/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "The UK is running suspiciously low on Xbox Series X console stocks, but why?", "content": "", "created_time": "2025-01-15T08:42:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "YouAreNotMeLiar", "url": "https://reddit.com/r/microsoft/comments/1i1t76u/the_uk_is_running_suspiciously_low_on_xbox_series/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "I’m using ms apps for free on mac", "content": "I don’t know how am I able to use ms word ppt and excel for free on my mac. \n\nHere’s the explanation I came up with. In 2020 I bought a windows laptop and got ms apps for free with it. I may have somehow linked that account with my Microsoft account which I’m using on my mac and it’s working. \n\nCan someone tell me if I’m right? Is there a time limit for the free apps with windows? And can Microsoft screw me up with this post?\n\n", "created_time": "2025-01-15T14:36:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "RequirementVivid6312", "url": "https://reddit.com/r/microsoft/comments/1i1yqee/im_using_ms_apps_for_free_on_mac/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Microsoft Considered Shutting Down Xbox In 2021, Opted For Studio Acquisitions To Boost Game Pass", "content": "", "created_time": "2025-01-15T15:06:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 654.0, "upvotes": 490, "num_comments": 0, "subreddit": "unknown", "author": "YouAreNotMeLiar", "url": "https://reddit.com/r/microsoft/comments/1i1zen2/microsoft_considered_shutting_down_xbox_in_2021/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Is there any info on Customer Experience Engineer role?", "content": "I have a phone tech screen coming up and I'm a bit nervous. I have no idea what to expect, it's a 30 min phone tech screen.\n\nThe Focus Areas for tech screening will be as follows:\n\n1. Technical Skills in Cloud (preferably Azure)\n\n2. Incident/Crisis Management Skills\n\n3. Customer Relationship Management Skills\n\n4. Experience with Technical Support\n\n5. Problem Management Skills\n\nAnyone else ever done an interview for this role and can offer any insights on it? ", "created_time": "2025-01-15T21:58:54", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1i29070/is_there_any_info_on_customer_experience_engineer/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Microsoft introduces more ways for players to repair Xbox Consoles, reduce waste and save energy ", "content": "[https://news.xbox.com/en-us/2025/01/15/xbox-repair-reduce-waste-energy-saving/](https://news.xbox.com/en-us/2025/01/15/xbox-repair-reduce-waste-energy-saving/) ", "created_time": "2025-01-15T22:06:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 45.0, "upvotes": 33, "num_comments": 0, "subreddit": "unknown", "author": "Felicity_Here", "url": "https://reddit.com/r/microsoft/comments/1i296ls/microsoft_introduces_more_ways_for_players_to/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Internship: Waitlist Microsoft", "content": "Waitlisted and told \"eligible for placement on a new team if headcount becomes available within the next 6 months.\" Is it likely for people to get off the waitlist at Microsoft? ", "created_time": "2025-01-14T00:02:29", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "applesandbananasbaby", "url": "https://reddit.com/r/microsoft/comments/1i0sp54/internship_waitlist_microsoft/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "Customer Success Account Specialist", "content": "Hey everyone! \n\nI have been shortlisted to the CSAS role in Microsoft and would like to understand better the responsibilities of the role. I went through the job description and it kinda sounds like a salesman kind of role instead of a consulting based role. I had a call with the recruiter and he mentioned that there are pre-sales consulting and post sales consulting and this role would be post sales where you would liase with the client and the implementation team to ensure the project goes smoothly. \n\n  \nWould like to hear your thoughts or experience on this! Thanks", "created_time": "2025-01-14T08:15:08", "platform": "reddit", "sentiment": "bearish", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "russell616", "url": "https://reddit.com/r/microsoft/comments/1i11elf/customer_success_account_specialist/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "Microsoft streaming technology ", "content": "Hello, I wonder if anyone in this group remember a couple of old tech demos that Microsoft showed off, many years ago? I would like to refresh my memory on what the technology was called.\n[I thought that it was called \"Sailfish\" or something similar, but when I search for it, there is only an operative system that shows up...]\n\nIt was a streaming technology that allowed you to display pictures over a slow dial up connection.\n\nIn one demo, you saw what looked like a bunch of different colored squares, but then they zoomed in and you could see that they where many high resolution pictures. I think they did something similar with a text (declaration of independence?).\n\nI believe they worked on the technology for a couple of years and in a later demo, they showed off something similar to Google Street View. Where they had a 360 view of a famous location and they streamed in pictures taken by tourists from that location. As you moved around the pictures came into view. And they used the metadata to show dem in the right location, in the right direction (and angle?).\n\n", "created_time": "2025-01-14T09:29:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Cane_P", "url": "https://reddit.com/r/microsoft/comments/1i12dcs/microsoft_streaming_technology/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "True up License Windows 10 / 11", "content": "m pretty sure im not the only one here with the unpleasant Windows 10 to 11 Migration.\n\nI have question Regarding these True Up-Licenses.\n\nAre they granted for update or do we have to buy some kind of add-on to migrate? Microsoft is bich and we all know that.\n\nTY guys", "created_time": "2025-01-14T09:38:44", "platform": "reddit", "sentiment": "bullish", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "SrSFlX", "url": "https://reddit.com/r/microsoft/comments/1i12ho4/true_up_license_windows_10_11/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "M365", "content": "So it is m365.office.com in the future but is the copilot integrated into the products automatically like Google or do I have to purchase it extra like it is now if I want to use it in the O365 products? I'm heavily using onenote and copilot would be a great addition. Thanks", "created_time": "2025-01-14T09:53:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1i12oks/m365/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "Microsoft testing 45 percent M365 price hikes in Asia", "content": "", "created_time": "2025-01-13T07:46:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 52.0, "upvotes": 28, "num_comments": 0, "subreddit": "unknown", "author": "Robemilak", "url": "https://reddit.com/r/microsoft/comments/1i08zl6/microsoft_testing_45_percent_m365_price_hikes_in/", "ticker": "MSFT", "date": "2025-01-13"}, {"title": "Microsoft and Amazon are using performance reviews to decide who gets laid off—experts ...", "content": "", "created_time": "2025-01-13T14:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 28.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1i0ekc8/microsoft_and_amazon_are_using_performance/", "ticker": "MSFT", "date": "2025-01-13"}, {"title": "Is a sabbatical the beginning of the end?", "content": "I’ve let a quiet quit situation go on far too long, and while there is comfort in knowing on paper I’m FI, I’ve been holding off leaving bc my job is easy enough and gives me ample free time…which for the most part I’m not using for anything better.  I also kinda expected I’d be let go by now, which would come with half a years severance.\n\nI’ve made big decisions previously I thought would better my life, and been wrong as many times as right, so my hope is a sabbatical allows me to sample what life could be like without the job accountability looming over.  Advice I’ve seen here is it will free me up in spirit as well as time, and even if my job is just wiggle the mouse (usually I have a bit more than that at minimum), it’s still occupying more of my energy than I realize until it’s gone.\n\nHave others taken time off only to realize the routine and something to do makes the time away from the office valued.  Or does the drastic change open you up to a whole new way of life.\n\nI previously asked/told my manager I planned to do this and he said if it’s what I need they’ll make do, and my reminders to make the official request following another meh review have me wondering if they might just say to not come back, and would that be good or bad?\n\nSingle no kids, and live in a city where everyone is hustling hard.  In summer I manage a rental that keeps me busy and socially engaged….but winter drags on and I find myself disengaged and second guessing a lot.  Second or third midlife crises and I’m not even 40.", "created_time": "2025-01-13T17:52:09", "platform": "reddit", "sentiment": "bullish", "engagement_score": 393.0, "upvotes": 173, "num_comments": 0, "subreddit": "unknown", "author": "GayFIREd", "url": "https://reddit.com/r/financialindependence/comments/1i0jw4g/is_a_sabbatical_the_beginning_of_the_end/", "ticker": "MSFT", "date": "2025-01-13"}, {"title": "Question regarding 365 subscription", "content": "Hi\n\nI have a microsoft 365 subscription through best buy as i got it with my laptop.  The renewal is coming up and its 175.  Since i last renewed, im currently not working and cant afford that amount.  \n\nMy question is if i were able to find a permanent license at a cheaper amount would i lose data? Another idea i had was to get it from the microsoft store as they have an option for a monthly 365 subscription.  However the same question arises.  \n\nI would appreciate any info on this.  Thank you!", "created_time": "2025-01-12T02:56:19", "platform": "reddit", "sentiment": "bullish", "engagement_score": 21.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Serenitymcw", "url": "https://reddit.com/r/microsoft/comments/1hzd42a/question_regarding_365_subscription/", "ticker": "MSFT", "date": "2025-01-12"}, {"title": "What are your favorite Microsoft Loop features and hidden game-changers?", "content": "Hey everyone,\n\nI've been diving into Microsoft Loop recently and I'm really intrigued by what it offers. I'm curious to know:\n\n* **Which features do you love the most?** Are there particular functionalities that make your workflow a breeze?\n* **Hidden or underrated features?** Have you discovered any game-changing tricks or lesser-known functionalities that have significantly improved your experience?\n* **How do you use Loop?** Whether it's for team collaboration, project management, or something entirely different, I'd love to hear about your use cases.\n* **What are <PERSON>'s limitations?** Are there any pain points or areas where you feel <PERSON> falls short?\n\nLooking forward to hearing your insights and learning some new tips!", "created_time": "2025-01-12T13:57:37", "platform": "reddit", "sentiment": "bearish", "engagement_score": 13.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hzn79b/what_are_your_favorite_microsoft_loop_features/", "ticker": "MSFT", "date": "2025-01-12"}, {"title": "Whiteboard alternatives", "content": "Good day, my dear windows users! I've been using whiteboard for the past 2 months and let me tell you, it's *poo*. Do you guys know any other whiteboards that are at least optimized better? For example, I have about 15 screenshots on one of my whiteboards with tons of writing and it freezes so much, despite the fact that I've an Intel core i9 14th gen along with 32gb ram! Also, the pen in zoom is so much smoother than whiteboard's and I really like that. So if you do know of any better alternatives that don't require you to share your data, log in, etc please do tell me the name of the app. Thank you for reading!", "created_time": "2025-01-12T18:46:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Yuga_Avner", "url": "https://reddit.com/r/microsoft/comments/1hztnzo/whiteboard_alternatives/", "ticker": "MSFT", "date": "2025-01-12"}, {"title": "Copilot app redirects me to the cloud version & says coming soon - I thought copilot was already working for enterprise?", "content": "Using the WIn11 copilot app I could use it before i tried signing in and after signing in it's asking if i want work or personal. I select work and it redirects me to the cloud [https://copilot.cloud.microsoft/](https://copilot.cloud.microsoft/) and says coming soon. Is Copilot only available for personal right now or am I missing something?", "created_time": "2025-01-12T20:14:33", "platform": "reddit", "sentiment": "neutral", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "P_<PERSON>_woker", "url": "https://reddit.com/r/microsoft/comments/1hzvras/copilot_app_redirects_me_to_the_cloud_version/", "ticker": "MSFT", "date": "2025-01-12"}, {"title": "Microsoft rules out layoffs in India amid global job cuts", "content": "", "created_time": "2025-01-12T22:31:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 583.0, "upvotes": 427, "num_comments": 0, "subreddit": "unknown", "author": "TeaAndGrumpets", "url": "https://reddit.com/r/microsoft/comments/1hzyykw/microsoft_rules_out_layoffs_in_india_amid_global/", "ticker": "MSFT", "date": "2025-01-12"}, {"title": "I have $4 worth of Microsoft credit that's expiring soon. What should I use it on?", "content": "I've already gotten the $1 trial of Xbox Game Pass. What would be a good use of the credit? Are there any good apps that you use regularly that have a one-time fee less than $4, or maybe something else? \n\nAlso, I'm not talking about rewards points - it's actual monetary credit that I got from somewhere (I don't even know when I got it, which is why I haven't used it until they sent me an email alerting me it was expiring).", "created_time": "2025-01-11T00:07:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 71.0, "upvotes": 57, "num_comments": 0, "subreddit": "unknown", "author": "magimorgiana", "url": "https://reddit.com/r/microsoft/comments/1hyj0yh/i_have_4_worth_of_microsoft_credit_thats_expiring/", "ticker": "MSFT", "date": "2025-01-11"}, {"title": "Xbox Developer Direct kicks off 2025 with Doom: The Dark Ages, South of Midnight, <PERSON>, and a \"surprise\" fourth game", "content": "All coming to Game Pass \"this year\"", "created_time": "2025-01-10T03:26:31", "platform": "reddit", "sentiment": "neutral", "engagement_score": 44.0, "upvotes": 40, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1hxv9gs/xbox_developer_direct_kicks_off_2025_with_doom/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "MS900 to SC900", "content": "Hi guys, \n\nI passed the MS900 exam in 2024 and I am now looking forward to get the SC900.\n\nIs it more difficult or it’s just a completely different test? \n\nI know MS900 is for the fundamentals and SC900 is for security, but I’ve been working at an MSP selling Microsoft products for over a year now, including teaching people on how to use Copilot, and I just wonder how many off-time it takes on average to prepare for the exam? ", "created_time": "2025-01-10T04:36:28", "platform": "reddit", "sentiment": "bearish", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "420sblahsblah", "url": "https://reddit.com/r/microsoft/comments/1hxwhfp/ms900_to_sc900/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "Microsoft releases new 'Fluid Textures' desktop wallpapers", "content": "[https://microsoft.design/wallpapers/](https://microsoft.design/wallpapers/)", "created_time": "2025-01-10T06:48:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 107.0, "upvotes": 99, "num_comments": 0, "subreddit": "unknown", "author": "MrShortCircuitMan", "url": "https://reddit.com/r/microsoft/comments/1hxykwf/microsoft_releases_new_fluid_textures_desktop/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "Donations for California", "content": "I apologize if this doesn't exactly fall into a discussion type post, but question is not an available tag and this is an urgent post:\n\nWhat charities can I donate my Microsoft Rewards points to that will help with the LA fires? There is no other way for me to donate money and I desperately want to help to do literally anything possibly to provide assistance. Absolutely ANYTHING will be extremely helpful.\n\nThank you so, so much.", "created_time": "2025-01-10T13:21:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "-iggylove-", "url": "https://reddit.com/r/microsoft/comments/1hy49ev/donations_for_california/", "ticker": "MSFT", "date": "2025-01-10"}, {"title": "For future reference, how does a Microsoft Authy transfer work to another phone?", "content": "I have had it on my phone for a long time but I remember reading you have to becareful when doing it. I think it was something about the way you login that you want to choose one option over another or something to that effect. \n\nI'm not planning on getting a new phone right now but just want to ask for down the road to reference here so I know what I'm doing, I don't want to be locked out of accounts.\n\nI don't even remember if you even login to the Authy its been so long but I believe I wrote down some password or something for it. I'm just clueless at this point and want to make sure I know what I'm doing when the time comes.", "created_time": "2025-01-09T03:08:29", "platform": "reddit", "sentiment": "bullish", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "RJP_X", "url": "https://reddit.com/r/microsoft/comments/1hx34xk/for_future_reference_how_does_a_microsoft_authy/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Wall Street Expected to Shed 200,000 Jobs as AI Erodes Roles", "content": "Global banks will cut as many as 200,000 jobs in the next three to five years—a net 3% of the workforce—as AI takes on more tasks, according to a Bloomberg Intelligence survey.\n\n - Back, middle office and operations are most at risk.\n - Banks’ profits could surge due to improved productivity.", "created_time": "2025-01-09T13:05:11", "platform": "reddit", "sentiment": "bullish", "engagement_score": 3508.0, "upvotes": 3152, "num_comments": 0, "subreddit": "unknown", "author": "PrestigiousCat969", "url": "https://reddit.com/r/finance/comments/1hxcctu/wall_street_expected_to_shed_200000_jobs_as_ai/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Looking for a script to analyze Microsoft Tenant settings", "content": "Hey fellow <PERSON>min-D lacking humans :)\n\nLike the title says, have anyone bumped into or else i will start making one and share on here in the future.", "created_time": "2025-01-09T13:50:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hxd7hh/looking_for_a_script_to_analyze_microsoft_tenant/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Microsoft cuts more jobs, this time apparently based on performance\n", "content": "", "created_time": "2025-01-09T14:24:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 391.0, "upvotes": 309, "num_comments": 0, "subreddit": "unknown", "author": "Robemilak", "url": "https://reddit.com/r/microsoft/comments/1hxdwey/microsoft_cuts_more_jobs_this_time_apparently/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Microsoft Designer: Restyle Image no longer available after 2/1/25", "content": "", "created_time": "2025-01-09T14:48:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 6.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "FriendshipFun4992", "url": "https://reddit.com/r/microsoft/comments/1hxee8h/microsoft_designer_restyle_image_no_longer/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Application Timeline Question ('Transferred')", "content": "Hi, I just have a question for current employees regarding the early stage interview/application process.\n\nI've had a couple job applications be moved into the 'Transferred' section. They still show up as 'Active' on my profile, and have the info message of 'The job description is not available. Your application is still under consideration.' after they have been 'Transferred', but I am yet to hear anything from Microsoft. Am I just in the general applicant pool but not selected for the next round? I am just unsure of the timeline. I applied to 2 positions in the beginning of November and another towards the end of December. I've had other applications for positions I am definitely less qualified for be rejected within 1-2 weeks after I apply, so I am just confused as to the timeline. Would just love some clarity as to whether or not I am still in the process and should take time to interview prep for these positions, or that HR has just forgotten to formally reject me for them and that I should just get back to work.\n\nThanks!", "created_time": "2025-01-09T15:19:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "PlentyLecture5038", "url": "https://reddit.com/r/microsoft/comments/1hxf27e/application_timeline_question_transferred/", "ticker": "MSFT", "date": "2025-01-09"}, {"title": "Entry Level Sales Role", "content": "I may have an opportunity in an entry level sales role for Microsoft and I’m curious if anybody has insight into what the job entails? I’m transitioning from a different career in my late 20s. Am I stupid if I don’t end up taking this opportunity? I realize Microsoft is one of the most profitable companies in the world and there’s tons of benefits working for MSFT including stock options, insurance, etc… I also know that this industry is pretty cutthroat and the bottom 50% are likely to be laid off. Anyways I just would like some opinions if anybody has good advice or input!", "created_time": "2025-01-08T04:21:16", "platform": "reddit", "sentiment": "bullish", "engagement_score": 5.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "DerfLedew94", "url": "https://reddit.com/r/microsoft/comments/1hwba40/entry_level_sales_role/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "Microsoft Office 365 not working?", "content": "Ok for context this laptop used to be from school, they \"unenrolled me\" but in a way where I simply lost restrictions, computer is still enterprise edition.   \nIt came as Windows 10 Enterprise edition, I updating to windows 11, enterprise edition is still active though.\n\nNote that this is NOT a support post im just curious about what yall think the problem is and why it is doing this.\n\nFor example, I open 365, enter product key for windows 11 enterprise, and it doesn't recognize? what makes it not recognize it, i'd like to know how that works.", "created_time": "2025-01-08T13:38:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 44.0, "upvotes": 32, "num_comments": 0, "subreddit": "unknown", "author": "yes_im_gavin", "url": "https://reddit.com/r/microsoft/comments/1hwkf5r/microsoft_office_365_not_working/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "BBR TCP Congestion Control", "content": "Does Windows support BBR TCP Congestion Control", "created_time": "2025-01-08T15:36:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "HeadState8730", "url": "https://reddit.com/r/microsoft/comments/1hwmxme/bbr_tcp_congestion_control/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "OneDrive requires you to have free space to copy to another user's OneDrive ", "content": "Appears if your personal OneDrive is full, you cannot upload non-OneDrive files to someone else’s OneDrive who has plenty of free space. Microsoft wants to sell space on both ends even though one end is not used in the copy at all. I had to clear out my OneDrive before the copy could be done.\n\nI have never had a use for OneDrive. If it was just a backup, I would be fine with it. However, removing my local files and storing only in the cloud is not want I want. I avoided it for years by not even signing into a MS account with my builds.\n\nSince we implemented it at work, I decided to enable it last year on my new gaming laptop at home. It promptly hit the space limit and the messages started about buying more space. Only way to stop that, without buying space I do not need, was to disable it. Then MS keeps turning it back on and does not even remember settings (desktop sync turned off). I had to create a local copy of all files in OneDrive (so much for saving space) and tell all my applications to not save to Documents folder anymore.\n\nI have been in IT for nearly 40 years. I owe much of my career to MS products. However, I really wish MS would sell an OS again. What they sell now is a platform to sell us more stuff and it is getting annoying. I don’t need OneDrive stealing my files. It is not a “backup”. I don’t need “news” clickbait that never gets you to the real story. I do not need your app store. I don’t even need your web browser. Sell me a base OS and let me chose what to install, and DON’T re-enable stuff I turn off with updates. If my games played reliably on Linux I would switch in heartbeat.\n\n/rant off", "created_time": "2025-01-08T16:51:38", "platform": "reddit", "sentiment": "bearish", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hworcs/onedrive_requires_you_to_have_free_space_to_copy/", "ticker": "MSFT", "date": "2025-01-08"}, {"title": "Does Microsoft give interns any off days during the summer?", "content": "From anyone at Microsoft. Do they allow interns to take any days off over the summer? Like vacation/sick days or PTO?", "created_time": "2025-01-08T20:42:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 34.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "EconomicsWest4989", "url": "https://reddit.com/r/microsoft/comments/1hwudd7/does_microsoft_give_interns_any_off_days_during/", "ticker": "MSFT", "date": "2025-01-08"}], "metadata": {"timestamp": "2025-07-06T19:55:08.929991", "end_date": "2025-01-15", "days_back": 7, "successful_dates": ["2025-01-15", "2025-01-14", "2025-01-13", "2025-01-12", "2025-01-11", "2025-01-10", "2025-01-09", "2025-01-08"], "failed_dates": [], "source": "local"}}}}