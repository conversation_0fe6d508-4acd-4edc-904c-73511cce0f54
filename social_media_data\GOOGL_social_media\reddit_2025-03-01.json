[{"platform": "reddit", "post_id": "reddit_1j0td4b", "title": "Is Google Undervalued? A Sum of All Parts (SOAP) Valuation", "content": "# Google Search: A Major Threat from LLMs\n\nLarge Language Models (LLMs) pose a significant challenge to Google Search. Google’s own Gemini still lags behind OpenAI’s ChatGPT, and new competitors like Grok, DeepSeek, and Perplexity continue to emerge. While it's difficult to quantify the exact impact, Google is undoubtedly losing its grip on search dominance. For now, it retains strongholds in lucrative areas like travel, auto, shopping, and health, but the broader trend suggests weakening control.\n\nThe search market is projected to grow at 11% annually through 2033, yet Google's Search revenue grew by 13% in 2024 to $198 billion. However, I expect Google's growth to slow as it loses market share to emerging players. Assuming a 5% annual growth rate for the next five years and margin compression from 40% to 35%, applying a 10x PE multiple yields a valuation of $885 billion.\n\n# YouTube: A Strong Growth Trajectory\n\nYouTube generated $36 billion in revenue in 2024, growing at 14% YoY, partly driven by election-related advertising. Assuming a 12% annual growth rate over the next five years, YouTube’s revenue trajectory remains promising. Google doesn’t disclose YouTube’s profitability, but using Netflix’s 25% margin as a benchmark and applying a 40x PE multiple (same as Netflix), we arrive at a valuation of $634 billion.\n\n# Google Cloud: Gaining Market Share, But How Profitable?\n\nGoogle Cloud has been gaining ground on Amazon Web Services (AWS) and improving its margins, reaching 17% operating margin last quarter. For 2024, Google’s cloud revenue is $43.22 billion. If we assume:\n\n* 25% annual revenue growth for the next five years,\n* Eventual margin expansion to AWS’s 36%,\n* $47.5 billion in projected profit,\n* A 40x PE multiple,\n\nwe get a valuation of $1.9 trillion ($1,900 billion)\n\n# Subscriptions, Platforms, and Devices: An Underappreciated Segment\n\nGoogle’s subscriptions, platforms, and devices division generates $40 billion in revenue, growing at 15% annually. Assuming this growth continues, with a 25% margin and a 20x PE multiple, this segment is worth $403 billion in 2029.\n\n# Waymo: A Long-Term Bet\n\nWaymo was last valued at $45 billion in its latest funding round. While this segment remains speculative, it’s still a valuable asset in Google’s portfolio.\n\n# Total Valuation: Bringing It All Together\n\n**Search: $885**\n\n**YouTube: $634**\n\n**Cloud: $1,900**\n\n**Subscriptions: $403**\n\n**Waymo: $45**\n\n**Total: $3,867**\n\n**After 20% Tax: $3,094**\n\n# Discounting to Today’s Price\n\nIf we discount the total valuation back to today’s price using a 9% IRR, we arrive at an intrinsic value that is very close to Google's current market cap. This suggests that investors buying Google today can expect about a 9% annual return over the next five years.\n\n# Final Thoughts: Is Google Undervalued?\n\nGiven this analysis, Google does not appear significantly overvalued or undervalued at current levels. While there are headwinds from LLM-driven competition in search, the strength of YouTube, Cloud, and subscriptions provides a diversified revenue base. Google Cloud’s rapid growth and improving profitability could be a key driver of future upside, particularly if AI adoption accelerates.\n\nFor investors, the key question is whether Google can maintain its dominant market position while navigating the disruptive forces of AI. If you believe in Google’s ability to adapt and innovate, then buying at today’s price offers a reasonable long-term return expectation of \\~9% annually.\n\n[Subscribe to PatchTogether Investing](https://open.substack.com/pub/patchtogether/p/is-google-undervalued-a-sum-of-all?r=os6rc&utm_campaign=post&utm_medium=web&showWelcomeOnShare=true)", "author": "Rich_Minimum_2888", "created_time": "2025-03-01T06:39:22", "url": "https://reddit.com/r/ValueInvesting/comments/1j0td4b/is_google_undervalued_a_sum_of_all_parts_soap/", "upvotes": 62, "comments_count": 60, "sentiment": "bullish", "engagement_score": 182.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0trgh", "title": "Need a badass Facebook & Google Ads Partner for My Real Estate Agency (Singapore)", "content": "Hey guys, I run a real estate marketing agency in Singapore. I started the agency 6 months ago and just signed my 30th client—so sales are going great. We just hired our third cold caller, and lead gen is on fire.\n\nBut here’s the problem: **service delivery is a mess.**\n\nOur churn rate is high, and only a few realtors are sticking with us. I’ve outsourced to freelancers (Upwork, Facebook) and even other agencies in Singapore, but none of them deliver real results. The ads just don’t work.\n\nI need a **killer Facebook and Google Ads expert** who can **actually** generate leads and conversions for realtors. If you can deliver, I’m open to either:\n\n1. Giving you a **cut of what I earn**, OR\n2. **Helping you set up your own agency** with my sales systems.", "author": "Fast_Fishing_2193", "created_time": "2025-03-01T07:05:40", "url": "https://reddit.com/r/PPC/comments/1j0trgh/need_a_badass_facebook_google_ads_partner_for_my/", "upvotes": 0, "comments_count": 18, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0ubmb", "title": "Victory against Greenwashing!", "content": "A prankster activist group (The Yes Men) snuck a fake anti-fossil fuel ad into a pro-fossil fuel maritime magazine. The ad links to this [insane commercial ](https://www.youtube.com/watch?v=7ZeYVjeZAUQ&t)they made spoofing Royal Caribbean to call out the whole maritime industry for greenwashing liquified natural gas as a \"clean fuel.\" Here's [the story in The Guardian!](https://www.theguardian.com/us-news/2025/feb/28/fake-ad-scrubby-greenwash-lng) So good.", "author": "Ok_Razzmatazz4448", "created_time": "2025-03-01T07:45:23", "url": "https://reddit.com/r/Renewable/comments/1j0ubmb/victory_against_greenwashing/", "upvotes": 26, "comments_count": 0, "sentiment": "neutral", "engagement_score": 26.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0uflf", "title": "Google’s Unannounced Update Scans All Your Photos—One Click Stops It", "content": "", "author": "mWo12", "created_time": "2025-03-01T07:53:09", "url": "https://reddit.com/r/privacy/comments/1j0uflf/googles_unannounced_update_scans_all_your/", "upvotes": 1964, "comments_count": 214, "sentiment": "neutral", "engagement_score": 2392.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0yv2f", "title": "Google’s <PERSON>s Engineers Should Work 60-Hour Weeks in Office to Build AI That Could Replace Them", "content": "", "author": "chrisdh79", "created_time": "2025-03-01T12:56:15", "url": "https://reddit.com/r/Futurology/comments/1j0yv2f/googles_sergey_brin_says_engineers_should_work/", "upvotes": 8465, "comments_count": 717, "sentiment": "neutral", "engagement_score": 9899.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j11233", "title": "MyDrive - Open Source Google Drive Clone I created", "content": "", "author": "kyle_the_mage99", "created_time": "2025-03-01T14:48:14", "url": "https://reddit.com/r/webdev/comments/1j11233/mydrive_open_source_google_drive_clone_i_created/", "upvotes": 363, "comments_count": 57, "sentiment": "neutral", "engagement_score": 477.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j13zmq", "title": "Google’s <PERSON>s Engineers Should Work 60-Hour Weeks in Office to Build AI That Could Replace Them", "content": "", "author": "MetaKnowing", "created_time": "2025-03-01T16:56:38", "url": "https://reddit.com/r/technews/comments/1j13zmq/googles_sergey_brin_says_engineers_should_work/", "upvotes": 680, "comments_count": 151, "sentiment": "neutral", "engagement_score": 982.0, "source_subreddit": "technews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j15khp", "title": "age blocked by robots.txt . The SEO guy I hired says I just need more back links however the indexed pages on Google dropped over 5x just after he started doing work on the site. Is he talking garbage?", "content": "I got someone to design me an Ecwid store which all went fine. I started adding items onto it and google was slowly indexing these. I then hired someone to do the SEO. They starting doing their work and over night the number of indexed pages fell from 30 indexed and 28 not indexed to 6 indexed and 53 not indexed. I have now added more product pages so there are over 150 pages on the site however no change to the pages Google has indexed.\n\nThey are telling me I just need to buy back links from them and wait however when I run URL checks in Google Search Console it is telling me “Page cannot be crawled: Blocked by robots.txt”. I’m using Ecwid. What am I supposed to do / believe?", "author": "TerribleFruit", "created_time": "2025-03-01T18:03:12", "url": "https://reddit.com/r/SEO/comments/1j15khp/age_blocked_by_robotstxt_the_seo_guy_i_hired_says/", "upvotes": 9, "comments_count": 73, "sentiment": "neutral", "engagement_score": 155.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j1anhw", "title": "My agency uses Google sheets heavily and then copies that content to Hootsuite. Is there a better way?", "content": "What I'm looking to do is have my text/images go directly from Sheets to Instagram. Anyone done this here? ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-01T21:45:57", "url": "https://reddit.com/r/marketing/comments/1j1anhw/my_agency_uses_google_sheets_heavily_and_then/", "upvotes": 6, "comments_count": 14, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}]