# 本地数据优先策略 (Local Data Priority Strategy)

## 概述

本地数据优先策略允许AI对冲基金回测系统优先使用本地缓存的财务数据，只有在本地数据不可用时才回退到API调用。这大大减少了对外部API的依赖，提高了系统性能和可靠性。

## 实现的代理

已成功实现本地数据优先策略的17个代理：

### 投资大师风格代理 (10个)
1. `warren_buffett.py` - 沃伦·巴菲特投资风格
2. `ben_graham.py` - 本杰明·格雷厄姆价值投资
3. `bill_ackman.py` - 比尔·阿克曼激进投资
4. `stanley_druckenmiller.py` - 斯坦利·德鲁肯米勒宏观投资
5. `michael_burry.py` - 迈克尔·伯里逆向投资
6. `phil_fisher.py` - 菲利普·费雪成长投资
7. `charlie_munger.py` - 查理·芒格理性投资
8. `aswath_damodaran.py` - 阿斯沃斯·达摩达兰估值投资
9. `peter_lynch.py` - 彼得·林奇成长投资
10. `cathie_wood.py` - 凯西·伍德创新投资

### 专业分析师代理 (4个)
11. `fundamentals_analyst.py` - 基本面分析师
12. `market_analyst.py` - 市场分析师
13. `fundamentals.py` - 基本面数据分析
14. `valuation.py` - 估值分析

### 其他功能代理 (3个)
15. `technicals.py` - 技术分析
16. `sentiment.py` - 情绪分析
17. `risk_manager.py` - 风险管理

## 支持的数据类型

- **财务指标** (`financial_metrics`) - 公司财务指标数据
- **财务科目** (`line_items`) - 详细财务报表科目
- **内幕交易** (`insider_trades`) - 内部人员交易数据
- **公司新闻** (`company_news`) - 公司相关新闻
- **市值数据** (`market_cap`) - 公司市值信息
- **股价数据** (`prices`) - 历史股价数据

## 本地数据目录结构

```
financial_data_offline/
├── AAPL_financial_metrics/
│   ├── AAPL_financial_metrics_2024-01-01.json
│   ├── AAPL_financial_metrics_2024-01-08.json
│   └── ...
├── AAPL_insider_trades/
│   ├── AAPL_insider_trades_2024-01-01.json
│   └── ...
├── AAPL_prices/
│   ├── AAPL_prices_2024-01-01_to_2024-12-31.json
│   └── ...
└── ...
```

## 配置

本地数据优先策略通过以下配置文件控制：

- `src/config/financial_data_config.py` - 主要配置文件
- 默认本地数据目录：`financial_data_offline/`
- 可通过环境变量 `LOCAL_FINANCIAL_DATA_DIR` 自定义目录

## 工作原理

1. **优先本地数据**：代理首先尝试从本地缓存加载数据
2. **智能匹配**：系统自动查找最接近目标日期的可用数据文件
3. **API回退**：如果本地数据不可用，自动回退到API调用
4. **数据解析**：支持多种数据格式，包括字符串格式的自动解析
5. **错误处理**：完善的错误处理和日志记录

## 性能优势

- **减少API调用**：大幅减少对外部API的依赖
- **提高响应速度**：本地数据访问比API调用快数倍
- **降低成本**：减少API使用费用
- **提高可靠性**：避免网络问题和API限制

## 验证

使用 `validate_local_data_agents.py` 脚本验证所有代理的实现状态：

```bash
python validate_local_data_agents.py
```

## 注意事项

- 本地数据需要定期更新以保持时效性
- 系统会自动处理数据格式兼容性问题
- 支持向后兼容，不影响现有API调用
- 日志记录详细，便于调试和监控
