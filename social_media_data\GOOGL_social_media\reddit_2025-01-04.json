[{"platform": "reddit", "post_id": "reddit_1ht54p6", "title": "Is login with google expected these days?", "content": "Personally, I want to distance my personal data and business from companies like google and Microsoft as much as possible. \n\nWhile log in with google is convenient and I still use it on some of my personal accounts, I feel like there’s a loss of control, not to mention my distrust in these larger companies in terms of data usage and privacy.\n\nSo if I decide to not add a login with google option to my own platforms, could that cause damage in any way?", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-04T03:21:14", "url": "https://reddit.com/r/webdev/comments/1ht54p6/is_login_with_google_expected_these_days/", "upvotes": 136, "comments_count": 96, "sentiment": "bearish", "engagement_score": 328.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1htcl5z", "title": "Google Analytics Made EASY", "content": "I wish Google Analytics was a little simpler to grasp.\n\nI prefer the layout of GetClicky.\n\nAnd the iOS GA app only lets you build reports.\n\nSo, obviously, I could use some direction. Is there a way to make GA more approachable? What would you recommend to watch to get the basics of how people are coming to my sites from referring URLs and SERPs.\n\nHelp me get past my block.", "author": "applesauceblues", "created_time": "2025-01-04T11:33:09", "url": "https://reddit.com/r/marketing/comments/1htcl5z/google_analytics_made_easy/", "upvotes": 13, "comments_count": 20, "sentiment": "neutral", "engagement_score": 53.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1htcwt2", "title": "Did Your Website Take a Hit After Google's 'Spam Update'?", "content": "I’ve been working on a few new SEO projects over the last few months. I’ve been managing all three segments of the project—technical, on-page, and off-page. A few months in, Google launched its core update in November, which ultimately benefitted the website by bringing a significant hike in traffic by the end of the month. I continued following my strategy, but a few weeks later, Google released another core update, followed by the “Spam Update.”\n\nAs a result, the website I had started working on a few months earlier took a severe hit, experiencing a dramatic traffic drop of more than 50%. This raised the need to investigate why the website had seen such a massive decline. Small, temporary fluctuations are acceptable, but a drop of over 50% was a significant concern for both me and the client.\n\nI began my investigation by reviewing the website's history dating back to 2019. I segmented my research based on several parameters, including:\n\n* Backlinks\n* Blog content\n* Traffic fluctuations\n\nI felt literally happy after going through the insights as my research really showed me the actual reason why the website has seen this much of traffic drop. \n\nHere’s what I uncovered:\n\n* The client began SEO efforts in 2022.\n* By the end of that year, the website was generating approximately 13,000 monthly visitors with around 25,000 backlinks.\n* When Google rolled out its “Spam Update” near December, the website suffered its first significant hit, with traffic dropping by 4,000 to around 9,000 visitors per month.\n* From January to September, the website managed to stabilize at an average monthly traffic of about 8,500.\n* In October 2023, another “Spam Update” caused traffic to drop by another 4,000, bringing it down to approximately 5,000 visitors.\n* The “Spam Update” of 2024 dealt an even harsher blow, slashing traffic by 50%.\n\nFrom my analysis, it became clear that Google has been rolling out annual “Spam Updates” to filter out websites that don’t adhere to authentic practices for improving rankings.\n\nAt one point, I suspected that irregular content publishing in the blog section was the reason for the traffic decline. However, I eventually realized that spam backlinks were the main culprit behind the hits triggered by the “Spam Updates.”\n\nUpon analyzing the website’s backlink profile, I discovered numerous poor-quality links and content generated on irrelevant topics unrelated to the website’s products. Shockingly, the client was entirely unaware of this, as it was a blunder caused by the previous agency they had hired.\n\nOnce I identified the root cause, I began disavowing the spammy links through Google Search Console. Slowly but surely, the traffic started recovering and trending back toward its previous peak.\n\nThe client was genuinely impressed with my research when I presented all these points supported by facts and data. I, too, found immense satisfaction in diving deep into the technical aspects to uncover the true cause of the issue.\n\nI’m pretty sure, many of the websites worldwide have experienced such drops. For those who witnessed it, did you get the actual reason for the traffic decline? And how you are overcoming. Please share your experience. ", "author": "DM-wizard", "created_time": "2025-01-04T11:56:39", "url": "https://reddit.com/r/SEO/comments/1htcwt2/did_your_website_take_a_hit_after_googles_spam/", "upvotes": 32, "comments_count": 70, "sentiment": "bearish", "engagement_score": 172.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1htdahl", "title": "The Real Truth: Chrome tab scary", "content": "", "author": "im_guru", "created_time": "2025-01-04T12:21:58", "url": "https://reddit.com/r/chrome/comments/1htdahl/the_real_truth_chrome_tab_scary/", "upvotes": 1146, "comments_count": 32, "sentiment": "neutral", "engagement_score": 1210.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1htfqsy", "title": "3 years ago I launched a website, to make gaming subscriptions simple, it’s good & has no ads. But SEO / Google “marketing” are killing me and I can’t get users, it actually breaks me", "content": "I was on the fence on whether I should make this post, not easy to admit failure in these times.\n\n3 years ago I made a website dedicated to gaming subscriptions, it was the first of its kind, aiming to solve a real problem I was having while searching for if X game is in any subscription.\n\nCreating it took me lot of time, taught me a lot, both technically, but also about time management, commitment and work ethic required to make even a small website live.\n\nBut ever since I launched it, I just keep investing money and time, for barely nothing. \n\nTo put it into numbers, I pay about 70$ per month, for hosting and other services, for 3 years.\n\nI wanted to make it good, though that if it will be good it will attract users naturally but ever since I launched it I just have daily battles with Google to just appear on their search, and I still couldn’t get them to index most of my pages, and even then to even appear when users are searching for the questions I intended to solve.\n\nSearch for “Is tekken 8 on game pass?” Will result in hundreds of junk content, unreadable “articles“ that will use 5 paragraphs of nothing with prompts, ads, just to give answers that might be wrong or misleading.\n\nAnd it absolutely kills me to see I cannot win this battle, I am a developer, single developer, I don’t have the money to invest in marketing, ads or SEO teams.\n\nI wanted to create something good, something of my own, put the money, put the effort, even now I travel with my laptop just to keep maintaining it, but I spend more time on google search index pulling my hair on why my pages don’t appear rather than thinking of features or improvements I wanted to make.\n\nDid any solo dev here managed in this? Turning your solo project into websites that have lot of users and can give me tips on what to do?\n\nThis is the website https://gamepasscompare.com/\n\nEdit:\n\nThank you all for your comments, I really appreciate it, the main feedback was about clarity of the purpose of the website that was not clear enough, and some general tips. So I wrote the first blog to both serve the users and increase SEO, in addition to some paragraph at the start to clarify it’s not a store. I will need to hire a designer, and to put more effort into the front page and not just single game page", "author": "xSypRo", "created_time": "2025-01-04T14:41:31", "url": "https://reddit.com/r/webdev/comments/1htfqsy/3_years_ago_i_launched_a_website_to_make_gaming/", "upvotes": 216, "comments_count": 130, "sentiment": "bullish", "engagement_score": 476.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1htlka5", "title": "I work in SEO and hate google SERPs & prefer chatGPT. How long before everyone else switches too?", "content": "I’ve noticed the past 12 months, the amount of time I’ve had to rephrase searches or click to page 2/3 has massively increased. It feels like the only places that get me close to the results im looking for, are AI tools. \n\nGoogle is now saturated with shopping ads that are the same even when you rephrase/ refine your search. \n\nOn top of that you’ve got search ads, then YouTube promoted videos… then a handful of irrelevant organic results.\n\nGoogle & Amazon, it feels like there’s no point trying to refine your search cos sponsored always wins. Are we at the cusp of a huge change? ", "author": "Plop-plop-fizz", "created_time": "2025-01-04T19:01:38", "url": "https://reddit.com/r/marketing/comments/1htlka5/i_work_in_seo_and_hate_google_serps_prefer/", "upvotes": 40, "comments_count": 35, "sentiment": "bullish", "engagement_score": 110.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1htnpwy", "title": "Can someone send me Google workspace Promo code ?", "content": "Request ", "author": "Resident-Ad-8506", "created_time": "2025-01-04T20:35:30", "url": "https://reddit.com/r/smallbusiness/comments/1htnpwy/can_someone_send_me_google_workspace_promo_code/", "upvotes": 11, "comments_count": 52, "sentiment": "neutral", "engagement_score": 115.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1htpe0t", "title": "Stocks to watch at CES", "content": "Are there are any stocks you're watching ahead of CES next week?\n \nI have some MBLY stock and calls through to end of Jan. as they will be exhibiting a range of autonomous driving tech from full robotaxi FSD (Mobileye Drive) to semi autonomous for consumer vehicles. It might not be as advanced as TSLA but looks to have broader geographic scalability and is not vendor specific. If there were the same hype as we've seen for Tesla FSD and Robotaxis, the stock should definitely see some uplift off the back of a positive exhibit. ", "author": "friendface1", "created_time": "2025-01-04T21:49:25", "url": "https://reddit.com/r/pennystocks/comments/1htpe0t/stocks_to_watch_at_ces/", "upvotes": 11, "comments_count": 36, "sentiment": "bullish", "engagement_score": 83.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}]