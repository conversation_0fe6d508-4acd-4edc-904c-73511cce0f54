{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-02-13", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Thinking of buying Microsoft Home and Business and not 365 because I don`t wanna buy a subscription!", "content": "I am thinking of spending the 250$ to buy office 2024 for home and Bussiness but at the same time I feel its a waist I do  have a windows laptop although. I use but I noticed I use my linux machines more then I do my windows. And I am fine and happy with using LibreOffice which is free is their a reason why using office is better then using say something like LibreOffice?", "created_time": "2025-02-13T01:35:40", "platform": "reddit", "sentiment": "bullish", "engagement_score": 116.0, "upvotes": 32, "num_comments": 0, "subreddit": "unknown", "author": "RecentMonk1082", "url": "https://reddit.com/r/microsoft/comments/1io7rlo/thinking_of_buying_microsoft_home_and_business/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "Onedrive had ruined my personal files!", "content": "A few days ago my system was automatically updated and when I logged in it suggested me to use onedrive to backup things and I just skipped it. But today I found my personal files was uploaded to the onedrive, I don't know how but I must had been induced or deceived to allow it to do so.\n\nThe important thing is, I am using onedrive as a tool to sync my working documents, and created a folder named \"Documents\" to save all my work data. Then I found onedrive have uploaded ALL OF MY FILES IN MY PC'S DOCUMENTS FOLDER, and fixed 'em together with my work data. Of course I don't want my personal files in my work space, I delete them immediatelly from onedrive. But then I realized ONEDRIVE WAS NOT JUST BACKED MY FILES UP, IT JUST MOVED THE ENTIRE DOCUMENTS FOLDER OF MY PC. So when I deleted them from onedrive, I deleted them from everywhere. \n\nThis is so frustrating and makes me extrmely angry, the data I had lost contains so many things for the past decade. And it shouldn't be like this. Microsoft shouldn't be so urgently promoting people to use onedrive while not refining related measures. It should have been a lot more easier to stop the backup progress without any concern of data loss. And people should be informed that when their files were backed up  through onedrive they would become THE ONE AND THE ONLY COPY. Once you delete them from onedrive, you lost them forever.\n\n\n\nP.S. As there're countless softwares in the PC using the Documents folder as their caching space, it's definitely the worst idea you guys have made up to redirect the path of the Documents folder into OneDrive. While my apps are running, Onedrive just won't stop uploading those cached files and running out all my RAMs, it makes my PC stuck as hell. This is so stupid. Please stop this non-sense.", "created_time": "2025-02-13T13:45:28", "platform": "reddit", "sentiment": "bearish", "engagement_score": 39.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "NeXagoS", "url": "https://reddit.com/r/microsoft/comments/1iojcs9/onedrive_had_ruined_my_personal_files/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "You Should Install This Windows Security Patch Right Away", "content": "", "created_time": "2025-02-13T16:32:38", "platform": "reddit", "sentiment": "neutral", "engagement_score": 37.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "sparkblue", "url": "https://reddit.com/r/microsoft/comments/1ion2q3/you_should_install_this_windows_security_patch/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "Unexpected confidence boost", "content": "Just realized the hazard ⚠️ icon by my profile pic in PowerPoint makes me feel badass.  Love it!   Thank you Microsoft! ", "created_time": "2025-02-13T17:13:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "MissMewMews", "url": "https://reddit.com/r/microsoft/comments/1ioo2g8/unexpected_confidence_boost/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "Azure AI as a bilingual screen reader?", "content": "How is Azure AI's TTS feature for bilingual texts, specifically ones written in both English and Italian? I'm in need of a good screen reader and would appreciate any advice. TIA!", "created_time": "2025-02-12T02:15:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Blissful<PERSON>utton", "url": "https://reddit.com/r/microsoft/comments/1ingakd/azure_ai_as_a_bilingual_screen_reader/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Microsoft Team Matching Odds?", "content": "I recently interviewed for a software engineer I role on some new AI team (I didn’t get a ton of information about the team during interviews) and finished the final round 2 weeks ago. Today I received an email from a recruiter saying this:\n\n“I’ve been able to confirm the results from your interview with us. We do not have an existing role for you at this time. However, you will remain eligible for placement on a new team if headcount becomes available within the next 6 months.”\n\nI was wondering what are the odds that they find a role for me. Not sure if I should keep interviewing or not. I do work as a software engineer for another company so not like it would hurt very much by waiting but I do want to leave my company sometime soon.", "created_time": "2025-02-12T07:19:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 48.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "No_Value1340", "url": "https://reddit.com/r/microsoft/comments/1inlhlg/microsoft_team_matching_odds/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "<PERSON> says he wants to 'turn warfighters into technomancers' as <PERSON><PERSON><PERSON> takes over production of the US Army's IVAS AR headset from Microsoft", "content": "Microsoft will continue to support IVAS functionality with \"advanced cloud infrastructure and AI capabilities,\" but it's out of the hardware game.", "created_time": "2025-02-12T09:35:10", "platform": "reddit", "sentiment": "neutral", "engagement_score": 92.0, "upvotes": 88, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1innag4/palmer_luckey_says_he_wants_to_turn_warfighters/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Windows Upsell (Windows Update)", "content": "The amount of upselling after Windows Updates is getting ridiculous.\n\n[https://ibb.co/album/xm8CqX](https://ibb.co/album/xm8CqX) ", "created_time": "2025-02-12T14:24:17", "platform": "reddit", "sentiment": "bearish", "engagement_score": 64.0, "upvotes": 12, "num_comments": 0, "subreddit": "unknown", "author": "slfyst", "url": "https://reddit.com/r/microsoft/comments/1inryh9/windows_upsell_windows_update/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Question about <PERSON><PERSON><PERSON>", "content": "I do not subscribe to 365.  It doesn't make sense as to why I would subscribe to something when I can just buy it outright, knowing I'll constantly be using it...saves money in the long run.  I bout myself a Microsoft Office license key.\n\n  \nI know they're incorporating copilot into Word 365, but anyone know if it's also available if you bought a license key, rather then 365 subscription ?  After all, I did pay for it, I just went the less expensive option.", "created_time": "2025-02-12T16:37:59", "platform": "reddit", "sentiment": "bullish", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "fieldday1982", "url": "https://reddit.com/r/microsoft/comments/1inv4pu/question_about_copilot/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Why do you not want me to like you?", "content": "Why is the Microsoft suite getting less useful with every iteration?  My three pet peeves for today are \n\n* the inability to drag and drop email attachments from Outlook directly to SharePoint online\n   * You wrote both sets of software.  At no time during the development did anyone raise the possibility that uses might want this functionality.  I refuse to believe that a company of this size did not see that possibility and be able to solve this simple task.\n   * Why are you making it so difficult?\n* what happened to Add to Dictionary when spell checking in Word or Outlook?\n   * There is no ability to do that while in the flow of the task.  You have to stop what you're doing and follow a convoluted process of copy/pasting the word into the dictionary?\n   * Why deprecate a feature used by millions and not replace it with something better\n   * Why?\n* In Windows 10 start menu you could mouse over the app icon, and it would show you the last X files opened with that app.\n   * This was great, file opened in one fluid motion.\n   * Windows 11 - GONE.  You need to open the app THE<PERSON> find the file from the recent dialogue. \n   * Recent may or may not include the file you had just saved before lunch.  Random recents?\n\nWhat else have we lost that makes our lives more difficult?", "created_time": "2025-02-12T21:18:23", "platform": "reddit", "sentiment": "bearish", "engagement_score": 28.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "CountryTraining", "url": "https://reddit.com/r/microsoft/comments/1io21tw/why_do_you_not_want_me_to_like_you/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "What does a Cloud Solution Architect (Security) do at microsoft?", "content": "Recently offered a CSA role for security modern work involving co-pilot, entra ID, defender for cloud, etc.\n\nWhat does this role do? More pre-sales or post-sales? How technical do I need to be (lots of coding?), am I presenting demos? More hands-off/advisory? Augmentation to the customer team? Is this a team-based role or individual presenting advice/best practice to customer? \n\nAny information is appreciated as I feel like SA and CSA are vague roles.", "created_time": "2025-02-11T02:59:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 82.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Yello88", "url": "https://reddit.com/r/microsoft/comments/1imook7/what_does_a_cloud_solution_architect_security_do/", "ticker": "MSFT", "date": "2025-02-11"}, {"title": "Discuss OneDrive", "content": "I have the free \"plan\" through Microsoft onedrive for 5GB. Only have 2.9GB used, yet \"You're over your storage limit and your files will be deleted on or after September 7, 2025.\" Why do they do this to me? I've seen nothing but complaints about OneDrive whenever I try to research this, so I don't understand why Microsoft doesn't just fix their faulty cloud services.", "created_time": "2025-02-11T15:18:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 51.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Impossible_One_7344", "url": "https://reddit.com/r/microsoft/comments/1in0xfw/discuss_onedrive/", "ticker": "MSFT", "date": "2025-02-11"}, {"title": "What are the benefits for Microsoft Brasil?", "content": "Recebi feedback que fui bem nas 3 entrevistas tecnicas para software engineer, estou aguardando a offer, ainda nao sei em qual nivel vai ser a offer.\nDisse que minha pretencao salarial é 17k BRL.\n4 anos de experiencia.\nQueria saber se tem algum BR trabalhando aqui remotamente ou em SP e quais seriam os beneficios que recebem.\n\n", "created_time": "2025-02-11T17:50:19", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "True_Bunch9427", "url": "https://reddit.com/r/microsoft/comments/1in4kxh/what_are_the_benefits_for_microsoft_brasil/", "ticker": "MSFT", "date": "2025-02-11"}, {"title": "Security Research Role", "content": "Hello all,\n\nI have been trying to land a security research role with Microsoft for about 6 months now.\n\nI know every position is flooded with candidates, but I have yet to land an interview through 25ish applications.\n\nI feel I am a pretty competitive candidate so I am starting to think the issue lies more in my resume. I’m coming from the defense contracting industry, so I am new to FAANG specific resume criteria. \n\nI was wondering if anyone with any insight would be able to take a look at my resume and give me any feedback (even if that feedback is i’m not as competitive as I thought). Anyone with some insight into the hiring criteria for these roles or holding a similar role would be much appreciated but i’ll take any feedback I can get!\n\nThanks!", "created_time": "2025-02-11T22:14:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 23.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1inb2t1/security_research_role/", "ticker": "MSFT", "date": "2025-02-11"}, {"title": "Microsoft needs to pick up the pace", "content": "Microsoft's services are starting to get worse and worse. There's a reason why people are switching to Mac and Linux. Just the other day I sent an important email, and Outlook decides to put their response in my junk folder. Teachers have been getting frustrated with Teams because of the downgrades Microsoft is making to it to add in AI (which is great, but don't take away functionally please). I'm starting to consider switching as well. Started dual booting Linux to see if I can get my things to work on it.", "created_time": "2025-02-10T04:56:21", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "tddk25", "url": "https://reddit.com/r/microsoft/comments/1ilyc1x/microsoft_needs_to_pick_up_the_pace/", "ticker": "MSFT", "date": "2025-02-10"}, {"title": "Microsoft Study Finds AI Makes Human Cognition “Atrophied and Unprepared” | Researchers find that the more people use AI at their job, the less critical thinking they use.", "content": "", "created_time": "2025-02-10T17:44:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 376.0, "upvotes": 310, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1imc1ce/microsoft_study_finds_ai_makes_human_cognition/", "ticker": "MSFT", "date": "2025-02-10"}, {"title": "Got microsoft offer, background verification started", "content": "I am so happy to share that I got a offer from microsoft after months of grinding.  However I have doubt when should I put my resignation in the current company before or after background verification.  I have asked the recruiter she told me to put the resignation. But have seen lot of post in internet stating the delay in background verification. ", "created_time": "2025-02-10T20:37:42", "platform": "reddit", "sentiment": "neutral", "engagement_score": 178.0, "upvotes": 36, "num_comments": 0, "subreddit": "unknown", "author": "the_great_danton1", "url": "https://reddit.com/r/microsoft/comments/1imge97/got_microsoft_offer_background_verification/", "ticker": "MSFT", "date": "2025-02-10"}, {"title": "Microsoft is doing a lot of stuff right, but still need to better itself", "content": "For the past two decades, I've seen a lot of change from Microsoft. Mostly good change.\n\nBut obviously there's still some ironing to do.\n\n[https://www.reddit.com/r/browsers/comments/1ikj3xt/microsoft\\_support\\_page\\_lies\\_about\\_how\\_to/](https://www.reddit.com/r/browsers/comments/1ikj3xt/microsoft_support_page_lies_about_how_to/)\n\nFound this hard to believe, so I tried it myself.\n\nWent to Google and searched:\n\n\\- how to uninstall google chome  \n\\- how to uninstall firefox  \n\\- how to uninstall brave  \n\\- how to uninstall microsoft edge\n\nThe result from the official brand took me to:  \n[https://support.google.com/chrome/answer/95319?hl=en&co=GENIE.Platform%3DDesktop](https://support.google.com/chrome/answer/95319?hl=en&co=GENIE.Platform%3DDesktop)  \n[https://support.mozilla.org/en-US/kb/uninstall-firefox-from-your-computer#](https://support.mozilla.org/en-US/kb/uninstall-firefox-from-your-computer#)  \n[https://support.brave.com/hc/en-us/articles/4404876135565-How-do-I-uninstall-Brave](https://support.brave.com/hc/en-us/articles/4404876135565-How-do-I-uninstall-Brave)  \n[https://www.microsoft.com/en-us/edge/?form=MT00OR&cs=*********&ch=1](https://www.microsoft.com/en-us/edge/?form=MT00OR&cs=*********&ch=1)\n\nThis is a very sad move from Microsoft and something I thought the company was finally being able to turn away from.\n\n**EDIT:**\n\nI didn't explain myself properly on the first attempt.\n\nIf you Google for it, you'll get an official result from Microsoft:\n\nThe page is Titled: Uninstall Microsoft Edge\n\n[https://www.microsoft.com/en-us/edge/uninstall-edge](https://www.microsoft.com/en-us/edge/uninstall-edge)\n\nBut, if you visit such page as a client, you're forwarded to another:\n\n[https://www.microsoft.com/en-us/edge/?ch=1&cs=4112006293&form=MA13FJ](https://www.microsoft.com/en-us/edge/?ch=1&cs=4112006293&form=MA13FJ)\n\n**My issue is with Microsoft blatantly messing with the search results!**\n\nI've added an image with the result from Google search:\n\n[https://imgur.com/a/16Xkxcc](https://imgur.com/a/16Xkxcc)", "created_time": "2025-02-09T01:29:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 54.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "frankielc", "url": "https://reddit.com/r/microsoft/comments/1il3b6i/microsoft_is_doing_a_lot_of_stuff_right_but_still/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "Microsoft Identity and Access Administrator SC-300 Exam", "content": "Can anyone recommend good study material I'm thinking of buying\nhttps://a.co/d/16gi1Md\n\n<PERSON> and 2 more\nMicrosoft Identity and Access Administrator SC-300 Exam Guide: Gain the confidence to pass the SC-300 exam using exam-focused study resources\nISBN-13: 978-1836200390, ISBN-10: 1836200390", "created_time": "2025-02-09T03:38:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 14.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Bulky_Novel_4224", "url": "https://reddit.com/r/microsoft/comments/1il5rcg/microsoft_identity_and_access_administrator_sc300/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "Migrating imap to exchange", "content": "Hello, \n\nCould someone explain to me how exchange works? \n\nWe have a rented email server with @domain.com(imap and web access). We want to migrate to exchange on office 365 bussines subscription. If we migrate to exchange via imap migration (providing email and password) will emails stay on imap server? \n\n", "created_time": "2025-02-09T12:48:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 11.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "blindpd", "url": "https://reddit.com/r/microsoft/comments/1ildztw/migrating_imap_to_exchange/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "What is your tried and true implementation of Microsoft app?", "content": "I'm and looking for ways to automate productivity and make life simpler.", "created_time": "2025-02-09T23:14:47", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "LivinJH", "url": "https://reddit.com/r/microsoft/comments/1ils4r0/what_is_your_tried_and_true_implementation_of/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "Why Working at Microsoft May No Longer Be Worth It (2025)", "content": "In 2023, Microsoft’s Chief Marketing Officer <PERSON> made a notable statement on the company’s internal Yammer platform: “The most important lever for almost all our employees’ compensation upside is the stock price.” Below article looks into it.\n\n[**Why Working at Microsoft May No Longer Be Worth It (2025)**](https://deepseeks.medium.com/7069e2914ec1)\n\nThis philosophy tied employees’ financial futures directly to Microsoft’s stock performance.  \nWith negative one year returns, layoffs without severance is it worth to stay at microsoft.", "created_time": "2025-02-08T14:34:44", "platform": "reddit", "sentiment": "bullish", "engagement_score": 541.0, "upvotes": 365, "num_comments": 0, "subreddit": "unknown", "author": "LowerButterscotch556", "url": "https://reddit.com/r/microsoft/comments/1ikok6h/why_working_at_microsoft_may_no_longer_be_worth/", "ticker": "MSFT", "date": "2025-02-08"}, {"title": "Just got waitlisted for a CSA role at Microsoft any chances?", "content": "I just heard back from my final round interview at Microsoft for the CSA MBA role full time position and they just waitlisted me, saying they cannot offer me a role at this time but I am still under consideration as full time hiring is not yet finished. Basically, if someone turns down the offer for the role, they will take me. They said that they will provide me with an update in a few weeks. Does that actually happen? ", "created_time": "2025-02-08T16:18:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 38.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Particular_Price4153", "url": "https://reddit.com/r/microsoft/comments/1ikqvpf/just_got_waitlisted_for_a_csa_role_at_microsoft/", "ticker": "MSFT", "date": "2025-02-08"}, {"title": "Anyone has an update on the msft software engineer ai/ml role in redmond.", "content": "So im just wondering if Anyone has heard back from Microsoft for the software engineer ai/ml role in redmond. I applied on the 2nd of January as a new grad international based in the uk and was then transferred the next day to another role called FTE SWE AI ML January.\n\nI also managed to get a refferal for the role about a week ago but still havent recieved any type of communication at all not even a phone screen. Is this normal and the wait time could be a up to a month or should I just forget about it.", "created_time": "2025-02-07T00:56:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 19.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Reason-Plenty", "url": "https://reddit.com/r/microsoft/comments/1ijio7v/anyone_has_an_update_on_the_msft_software/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "Microsoft Software Engineer Screening Round Interview Experience", "content": "Hi\n\n\n\nI completed a screening(first) round for IC2 Software Engineer at Microsoft with Hiring Manager. My primary experience is with enterprise software development with Java, but the role is more inclined with Low level programming. \n\n\n\nSo tasked with a couple of coding tasks in C. First one is simple, and I did it pretty quick. But the second one, When I'm implementing the brute force approach by having three loops. The interview mentioned It's better to do it in an optimized way by using a data structure.\n\n\n\nI could not think of this because I was coding with C (When asked about it, I rated myself a 7 in C programming) in the interview when my primary language is Java. Also, my recruiter mentioned me that there would be no coding, it will focus primarily on my resume. So, I prepared only my experience and some concepts. This was also a reason for me not doing well.\n\n\n\nThe interview is scheduled for 45 mins, but it lasted for 30 mins. The interview mentioned the role would be comprising extremely low-level programming. \n\n\n\nSo, how long should I wait for the result. Do you think I would clear this round? I'm not sure about the standards of Microsoft. I have a feeling that I won't clear this tbh.\n\n\n\nThanks in advance.", "created_time": "2025-02-07T01:40:23", "platform": "reddit", "sentiment": "bullish", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "sutbborn_face", "url": "https://reddit.com/r/microsoft/comments/1ijjk6i/microsoft_software_engineer_screening_round/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "Amazon, Echoing Microsoft, Says It Can't Keep Up With AI Demand - Bloomberg", "content": "", "created_time": "2025-02-07T14:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1ijvlty/amazon_echoing_microsoft_says_it_cant_keep_up/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "What are the upgraded features of purchased office programs?", "content": "This may be a stupid question but honestly, I don’t know what “purchased” word, powerpoint, or excel do that is better than the free apps. Can someone give me a breakdown or a link to somewhere where this is explained?", "created_time": "2025-02-07T14:34:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "chickenfeeder41", "url": "https://reddit.com/r/microsoft/comments/1ijwcmm/what_are_the_upgraded_features_of_purchased/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "What to expect in my first connect as an L63 Sr SDE?", "content": "Hello All,\n\nI joined Microsoft as an L63 Sr SDE in September in the C+E org. So far things have ben stressful, microsoft doesn't really have an onboarding bootcamp, so I feel like I am kind of expected to ramp up very fast and deliver on my sprint tasks.\n\nI feelI am doing okay. I was also a full stack developer role, but I got reorged into a data engineering role. For example, I was building typescript/react based apps and maintaining backends also in same language. My new team mostly runs jobs on Azure Data FActory, and uses Scala, and Spark, big mindset shift for me. The tasks are coming top down, and i find myself working on weekeends as the estimates are assigned by the manager.\n\nThe Nov connect was really looking forward, and was more of a mock connect if you will. My real connect will be in May/June I think, what are the odds that they give me a LITE or 80% in the first connect?\n\nI don't have a lot of faith in my manager, as she has a poker face. I am aslo struggling because my team  and manager included have a very strong accent that I cannot understand even after watching the recorded videos.\n\nThanks", "created_time": "2025-02-07T22:20:28", "platform": "reddit", "sentiment": "bullish", "engagement_score": 49.0, "upvotes": 13, "num_comments": 0, "subreddit": "unknown", "author": "PartySuccotash5011", "url": "https://reddit.com/r/microsoft/comments/1ik7gik/what_to_expect_in_my_first_connect_as_an_l63_sr/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "Microsoft interview 29th Jan intern results?", "content": "It has been exactly week since my final round at Microsoft for Redmond. From what I heard and seen on other subs if accepted a change in portal would appear to completed. Has anyone that interviewed on 1/29 received that portal status changed yet? For explore or normal swe intern? Mine is still on scheduling, I will wait until the end of the week for better confirmation. Thx", "created_time": "2025-02-06T02:32:01", "platform": "reddit", "sentiment": "neutral", "engagement_score": 15.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "Creative-Hunter8009", "url": "https://reddit.com/r/microsoft/comments/1iis2li/microsoft_interview_29th_jan_intern_results/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Updated Privacy Policy should be illegal", "content": "**TLDR:  If you opt out of Microsoft's new connected services you can't sync with OneDrive but if you opt in to connected services, Microsoft will scrape all of your content for AI use.**\n\nSo - I saw a few posts online recently about the updated privacy policy and this new \"connected services\" malarkey.  And of course I went down a rabbit hole - that I wish I hadn't.  \n\nHere's the gist of it.  If you have all of the 3 connected services options selected   \n  \n1) Turn on experiences that analyze your content   \n2) Turn on experiences that download online content   \n3) Turn on all connected experiences.  \n  \n\\- you have zero privacy, and Microsoft WILL use AI to scrape your content.  Even though they say they don't - who are we actually kidding?  The lay language in the privacy policy says certain features like **Microsoft 365 Copilot** do access and process your data to provide ***\"assistance\"***. Copilot connects large language models to your organizational data, including documents, emails, and meetings, to generate contextually relevant responses. (uh-huh).  This processing is done to ***assist*** you and is not used to train Microsoft's AI models.  \n\nYou can choose to disconnect them but here's what will happen if you do.  For example, I want to ensure that my OneNote syncs across my devices.  If I opt out of #3 above (turn on all connected experiences) this is what Microsoft says will happen:\n\n*Cloud-based features like* ***OneNote syncing, real-time collaboration in Word/Excel, and automatic saving to OneDrive*** *will* ***not work****.*\n\nMicrosoft explicitly states that disabling these services means you lose access to any feature that requires cloud connectivity, including:\n\n* ***OneNote cloud sync*** *(your notes will only be stored locally)*\n* ***Auto-save & real-time collaboration*** *in Word, Excel, and PowerPoint*\n* ***Online templates, stock images, and AI-powered tools*** *like Editor or Designer*\n\n*If you want* ***OneNote to sync across devices****, you must enable at least some level of connected services. You can still use* ***OneNote locally*** *without syncing if you keep cloud features disabled.*\n\n  \nNow - you would think that ok - I'll just use option #2 because it allows you to sync OneNote with the cloud while keeping things as private as possible. But no - you can't just select option #2.  If you want to select either #1 or #2, you MUST opt in to #3 - turning on ALL connected experiences.  \n\nSo, If your **main goal is privacy**, you'd have to disable everything and manually back up.  \n\nHOW HAS MICROSOFT BEEN ALLOWED TO GET AWAY WITH THIS???  How is this legal?  WTAF is going on???\n\n\n\n", "created_time": "2025-02-06T15:53:07", "platform": "reddit", "sentiment": "neutral", "engagement_score": 58.0, "upvotes": 12, "num_comments": 0, "subreddit": "unknown", "author": "voubar", "url": "https://reddit.com/r/microsoft/comments/1ij5pj4/updated_privacy_policy_should_be_illegal/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Any tips or insight on Business Development Manager roles?", "content": "Hi all, I recently scheduled an intro phone call with a hiring manager at Microsoft to discuss this role. Could anyone with experience provide me with some insight on it? I come from 4 years of sales, is this just another BDR role? \n\nTIA", "created_time": "2025-02-06T17:39:42", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "pbandit11", "url": "https://reddit.com/r/microsoft/comments/1ij8cgh/any_tips_or_insight_on_business_development/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Microsoft’s AI boss just raided Google. He poached two scientists who built a tool that can transform ho-hum text into a riveting podcast", "content": "", "created_time": "2025-02-06T19:29:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 111.0, "upvotes": 95, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1ijb1m0/microsofts_ai_boss_just_raided_google_he_poached/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Investors Who Shunned the U.S. Office Market Are Coming Back", "content": "The volume of office building sales increased to $63.6 billion in 2024, up 20% from 2023, according to data firm MSCI. That activity still pales compared with 2015 to 2019, when volume averaged $142.9 billion a year. But it marked the first increase since 2021.", "created_time": "2025-02-06T22:23:29", "platform": "reddit", "sentiment": "neutral", "engagement_score": 102.0, "upvotes": 60, "num_comments": 0, "subreddit": "unknown", "author": "PrestigiousCat969", "url": "https://reddit.com/r/finance/comments/1ijfbj7/investors_who_shunned_the_us_office_market_are/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "MicrosoftDocs GitHub Issues Hidden", "content": "", "created_time": "2025-02-06T23:02:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "myroon5", "url": "https://reddit.com/r/microsoft/comments/1ijg7xa/microsoftdocs_github_issues_hidden/", "ticker": "MSFT", "date": "2025-02-06"}], "metadata": {"timestamp": "2025-07-06T22:36:53.209016", "end_date": "2025-02-13", "days_back": 7, "successful_dates": ["2025-02-13", "2025-02-12", "2025-02-11", "2025-02-10", "2025-02-09", "2025-02-08", "2025-02-07", "2025-02-06"], "failed_dates": [], "source": "local"}}}}