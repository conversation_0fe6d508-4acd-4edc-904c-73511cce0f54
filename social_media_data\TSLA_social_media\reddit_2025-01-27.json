[{"platform": "reddit", "post_id": "reddit_1iaxrco", "title": "The deepseek glazing is a little tiring here is why I think its not the miracle people think it is", "content": "So lets give credit were it is do. They trained a really great model. That's it. We can't verify the true costs, we can't verify how many \"spare GPU's\" that could be 100m worth of hardware, etc.\n\nFine lets take the economic implications out for a second here:\n\"BUT IT'S A THINKER! OH MY GOOOD GOLLY GOSH!\"\n\nyeah you can make any model a thinker with consumer level fine tuning:https://www.youtube.com/watch?v=Fkj1OuWZrrI\n\nchill out broski, 01 was the first thinking model so we already had this and again its not that impressive.\n\n\n\"BUT IT COSTS SO MUCH LESS\": yeah it was some unregulated project built on the foundations of everything we have learned about machine learning to that point. Even if we choose to believe that 5mm number, it probably doesn't account for the GPU hardware, the hardware those GPU's sit on, staff training costs, data acquisition costs, electricity. For all we know its just some psyops shit.\n\n\"BUT BUT, SAM ALTMAN\": Yeah i get it you dont' like billionaires, that doesn't make some random model that performs worse than 7 month old claude 3.5 in coding is THAT worthy of constant praise and wonderment. \n\n\nIf you choose to be impressed, fine, just know its NOT that credible of a claim to begin with and even if it was, they managed to get to 90 percent of the performance of models of almost a year ago with hundreds of thousands of \"spare gpus\".\n\n\nI think the part that has FASCINATED the laymen that populate this sub is the political slap to US companies more than any actual achievements. deep down everyone is resentful about American corporations and the billionaires that own them and so you WANT them to be put in their places rather than actually believing the bullshit you tell yourself about how much you love China.\n\n", "author": "OriginalPlayerHater", "created_time": "2025-01-27T02:45:53", "url": "https://reddit.com/r/singularity/comments/1iaxrco/the_deepseek_glazing_is_a_little_tiring_here_is/", "upvotes": 5, "comments_count": 53, "sentiment": "bullish", "engagement_score": 111.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iay3s8", "title": "How are High Current 18650s typically connected together?", "content": "As a preface I tend to have fantasy thoughts/ideas and then work on the logistics to see if it's feasible. Even though I don't do it.\n\n\nUsually, when I see someone DIY an 18650 pack, or see one from a manufacturer, like Tesla's, they spot weld thin narrow strips of nickel or thin wire per cell, where each cell runs up to 10 amps.\n\nWhat about packs where each cell outputs, and can handle, higher currents like 35A, 50A, or even 10 second pulsed 120A like those A123 26650s where the Positive and Negative contacts are still flat surfaces?\n\n\nIs spot welding still viable for such cases? I cannot seem to find anything that lists maximum thickness of nickel and copper strips/bars that is possible/safe to spot weld to the cells casings. Or even the ampacity of nickel strips, only \"suggestions.\" Also, is aluminum good for spot welding?\n\nI know larger high output cells have threaded posts laser welded onto the contacts, allowing bus bars or lugs to be bolted on, but that isn't really an option for most people to do themselves.\n\nOnly other method I can think of is placing the cells in-line with each other, one stacked on top the other, compressed together from end-to-end. With thin strips or contact plates between each cell for BMS connections, or bus bars/plates to allow parallel cells. I think older laptop batteries and powered window blinds sort of do this, but not for high current.\n\n\nAs an aside, does the amount of spot welds effect the electrical connection, or does it just hold the tabs and strips against the contacts? Like 8 spots welds requires 2x the lbs to remove the strip over 4, but doesn't mean 2x the ampacity.\n\nThank you for reading, and sorry if this seems like a bit of a ramble. I was researching this as I typed it and had a few omitted questions answered during that. And for those curious, the fantasy idea that started this was an electric 600HP/440KW go-kart using 2 Tesla SDUs for AWD, using the A123 26650s mentioned before. Definitely feasible, just a question of how safe. Only hiccup in the thought is how would the battery be put together.", "author": "WaitWhat4355", "created_time": "2025-01-27T03:03:43", "url": "https://reddit.com/r/batteries/comments/1iay3s8/how_are_high_current_18650s_typically_connected/", "upvotes": 2, "comments_count": 5, "sentiment": "bearish", "engagement_score": 12.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iayb7e", "title": "Rate my investment allocations. Open to advice.", "content": "30M \n\n401k Roth: \n80% FXAIX\n10% FFLEX\n10% RNPGX  \n\nTraditional Investment account -\nGoogle, Amazon, Apple, Lowes, Tesla, Coke and Spy\n\nRoth -\nVT, VTI, VOO, SCHD\n\nLooking for a general consensus on whether this is a decent allocation of investments. I’d rather handle everything myself and not rely on a financial advisor. ", "author": "NoloveRNG", "created_time": "2025-01-27T03:14:04", "url": "https://reddit.com/r/FinancialPlanning/comments/1iayb7e/rate_my_investment_allocations_open_to_advice/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ib8u2h", "title": "Newly discovered asteroid turns out to be Tesla Roadster launched into space", "content": "", "author": "Devious_Bastard", "created_time": "2025-01-27T13:20:37", "url": "https://reddit.com/r/nottheonion/comments/1ib8u2h/newly_discovered_asteroid_turns_out_to_be_tesla/", "upvotes": 0, "comments_count": 14, "sentiment": "neutral", "engagement_score": 28.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ib8zqy", "title": "Am I saving too much for retirement?", "content": "Hello everyone! \n\nI am getting serious about planning my retirement.  I currently make about 105k a year, 25 years old, in a HCOL area.\n\nI am currently contributing:\n\n24% of income gross to 401k + 6% employer match (100% vested)\n\n6.2% of income to ROTH IRA (will be slightly higher because I’ll need to top off at end of year to max)\n\n2.5% of income to HSA\n\nSo in total 38.7% of my gross income is going to a tax advantaged accounts. \n\nI have a 20k emergency fund which is roughly 4 months expenses.\n\nI do want to save for a house, but haven’t done that yet.\n\nI currently have 30k in 401k, 6k in ROTH, and 6k in HSA.\n\nI have a car note of 12k (4%) and student loans of 20k (3% average).\n\nI am contributing a lot, but also don’t have much fun money in light of that. I have run quite a few simulations through retirement spreadsheets and different sites, and it looks like I’ll have more than enough to retire at 60 withdrawing 100k a year. \n\nShould I dial back? Could use some help.", "author": "Fun_Goat_2406", "created_time": "2025-01-27T13:27:13", "url": "https://reddit.com/r/FinancialPlanning/comments/1ib8zqy/am_i_saving_too_much_for_retirement/", "upvotes": 103, "comments_count": 138, "sentiment": "neutral", "engagement_score": 379.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ib9u9x", "title": "Did <PERSON><PERSON>’s ‘Salute’ Cripple The Tesla Robotaxi?", "content": "", "author": "walky<PERSON><PERSON><PERSON>", "created_time": "2025-01-27T14:05:57", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ib9u9x/did_elon_musks_salute_cripple_the_tesla_robotaxi/", "upvotes": 375, "comments_count": 579, "sentiment": "neutral", "engagement_score": 1533.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibcw4g", "title": "Marketers, where do you land on AI in copywriting?", "content": "We're a subscription copywriting service. Got started in 2020 and things really took off. We scaled way up to keep pace with demand and then ChatGPT came out. Thankfully, we were able to scale down and have stayed in business with a much smaller team and client base. We focus on providing the best possible service to our clients by acting as a partner to them for all their copywriting needs. Seems to be working because we generally get really good feedback from our existing clients and our rate of churn is pretty low.\n\nWe have found that people are either very unconcerned about the use of AI tools or they're very adamant that we don't use any form of AI at all. Currently, 13% of our current clients do not want us to use AI. \n\nFor reference, our standard writing process is to let our writers use AI to help them get started and then are still expected to write the copy themselves (we believe AI is a tool that can help people be more efficient, not a replacement for people). Once writing is complete, an editor reviews the work and does a plagiarism and QA check along with some copy editing. \n\nFor people who don't want us to use AI, we ask that our writers do not use AI and our editors also do an Originality.ai scan, although we can't guarantee the results. If something scores as likely 100% AI, we'll have another writer take a crack at it, not because we don't trust our team's work, but because we haven't found a reliable AI detector for the type of work we do. Most copy is formulaic to one degree or another and this alone is enough to get flagged sometimes. Plus, the technology is evolving at a pretty quick pace; guidelines we were using in the past are no longer relevant. We rely more on our editors and their eye for good writing to ensure we're putting out work that we feel good standing behind.\n\nWhere do you land on copywriting right now? Have you been writing everything yourself? Found a dedicated freelancer? Hired inhouse? What is your go-to copywriting solution in 2025?", "author": "GetPandaCopy", "created_time": "2025-01-27T16:17:22", "url": "https://reddit.com/r/marketing/comments/1ibcw4g/marketers_where_do_you_land_on_ai_in_copywriting/", "upvotes": 2, "comments_count": 30, "sentiment": "bullish", "engagement_score": 62.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibdy5y", "title": "(JOB OPENING) Platform Simulation Engineer, Waymo.", "content": "Waymo is an autonomous driving technology company with the mission to be the most trusted driver. Since its start as the Google Self-Driving Car Project in 2009, Waymo has focused on building the Waymo Driver—The World's Most Experienced Driver™—to improve access to mobility while saving thousands of lives now lost to traffic crashes. The Waymo Driver powers Waymo One, a fully autonomous ride-hailing service, and can also be applied to a range of vehicle platforms and product use cases. The Waymo Driver has provided over one million rider-only trips, enabled by its experience autonomously driving tens of millions of miles on public roads and tens of billions in simulation across 13+ U.S. states.  \n  \nSoftware Engineering builds the brains of Waymo's fully autonomous driving technology. Our software allows the Waymo Driver to perceive the world around it, make the right decision for every situation, and deliver people safely to their destinations. We think deeply and solve complex technical challenges in areas like robotics, perception, decision-making and deep learning, while collaborating with hardware and systems engineers. If you’re a software engineer or researcher who’s curious and passionate about Level 4 autonomous driving, we'd like to meet you.  \n  \nIn this hybrid role, you will report to a Software Engineering Manager.  \n  \n**You will:**  \n  \nAdvance our platform emulators, simulators and associated tools &  infrastructure to scale Waymo to more vehicles, faster.  \nImplement hardware in the loop (HIL) software features for new vehicle platforms  \nSoftware development and integration for off-the-shelf devices (simulators, adaptors, power supplies, etc.) and custom interface hardware  \nDevelop test automation frameworks: resource management and scheduling infrastructure, dashboard, user and admin utilities  \nIntegrate software models into the HIL simulation loop  \nHelp triage HIL tests failures  \nYou have:  \n  \n5+ Years of experience ein SW Development   \nStrong knowledge of modern C++  \nLinux on advanced user level  \nElectrical engineering fundamentals  \nWe prefer:  \n  \nPassion for HIL simulation  \nEmbedded programming experience  \nLinux for real-time applications  \nKnowledge of general-purpose and automotive network systems (Ethernet, CAN, etc)  \nAppreciation for design and code aesthetics  \nFamiliarity with the building blocks for data-center monitoring systems (databases, dashboards, etc)  \n\\#LI-Hybrid  \n  \nThe expected base salary range for this full-time position across US locations is listed below. Actual starting pay will be based on job-related factors, including exact work location, experience, relevant training and education, and skill level. Your recruiter can share more about the specific salary range for the role location or, if the role can be performed remote, the specific salary range for your preferred location, during the hiring process.   \n  \nWaymo employees are also eligible to participate in Waymo’s discretionary annual bonus program, equity incentive plan, and generous Company benefits program, subject to eligibility requirements.   \n  \nSalary Range  \n$158,000—$200,000 USD\n\nTo learn more and apply visit: [https://www.simulationengineerjobs.com](https://www.simulationengineerjobs.com)", "author": "Gold_Worry_3188", "created_time": "2025-01-27T17:00:14", "url": "https://reddit.com/r/AutonomousVehicles/comments/1ibdy5y/job_opening_platform_simulation_engineer_waymo/", "upvotes": 3, "comments_count": 0, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibes40", "title": "Likely that DeepSeek was trained with $6M?", "content": "Any LLM / machine learning expert here who can comment? Are US big tech really that dumb that they spent hundreds of billions and several years to build something that a 100 Chinese engineers built in $6M?\n\nThe code is open source so I’m wondering if anyone with domain knowledge can offer any insight. ", "author": "Equivalent-Many2039", "created_time": "2025-01-27T17:32:59", "url": "https://reddit.com/r/ValueInvesting/comments/1ibes40/likely_that_deepseek_was_trained_with_6m/", "upvotes": 609, "comments_count": 748, "sentiment": "neutral", "engagement_score": 2105.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibfpvb", "title": "Cryptos are shit(I think)", "content": "**The story repeats itself, not in itself, but because the actors change, and these new actors have not had the chance to gain direct experience. They perceive the past as an end in itself and feel immune to making the same mistakes. However, the new situations that lead us to repeat past mistakes do not occur in the same ways or through the exact circumstances that caused previous errors. Instead, they change: the actors change, the dynamics change, and, above all, the circumstances change. Yet we humans do not change much. The technology around us evolves quickly, but the way we are built and our way of acting remain the same.**\n\nI don't know if you've seen the movie *The Big Short*; if you haven't and you're interested in the world of cryptocurrencies, you should watch it. In my opinion, it's a film that explains to most people what the world of crypto truly is—a big nonsense. It's garbage disguised as innovation. It's a world that leverages our economic struggles and our desire for financial independence and freedom from the system as a whole.\n\nMost of those who support cryptocurrencies are people without a university education or with a university education far removed from economics. Many of these crypto supporters, Bitcoin advocates first and foremost, have no idea how the current economic system works, yet they want to overturn it. It's like wanting to change the rules of soccer without even knowing the basics of the game.\n\nThe frustration of not being able to understand an economic system as intricate as ours leads people to think the solution is a simple and basic idea like Bitcoin. Many Bitcoin supporters, when asked what it is and what it's for, always answer with the same rhetoric: its value is mathematical, it's limited, it's decentralized... without even knowing what these terms mean.\n\nThese people hate banks without even knowing how they work. Banks are companies, and they don’t give away anything for free, just like any other company. At times, they may seem a bit sneaky, but that's probably because the subject matter is outside your expertise. Understanding economic dynamics comes from study, just like any other field, and it takes years and years of study to grasp these topics.\n\nWhat’s being sold with Bitcoin and cryptocurrencies is the simplicity of understanding things. Everything is marketed as easy, fast, intuitive. That’s why the baker (sorry, I have nothing against practical professions) who knows nothing about micro, macro, and economics in general can claim that Bitcoin is the best thing ever.\n\nAnd the exorbitant price growth continues to fuel these claims among all Bitcoin and crypto supporters. But if you had a basic understanding of economics, you’d realize on your own that this doesn’t make any sense: the price growth of cryptocurrencies is driven by self-fulfilling expectations (at least for now) and nothing else. There is no underlying activity to justify these prices.\n\nLet me give you a simple example: take a generic company where any of you could work. Let’s say it has 20 employees, generates €1.5 million in annual revenue, and makes a profit of €100,000 a year. Based on these data and by evaluating some of these aspects, I can estimate that this company is worth €2 million (capitalization). This capitalization is likely justified because there’s an underlying business that sells and makes a profit.\n\nIn the crypto world, it’s not like that. A Bitcoin is worth 100k not because it has real economic justification but simply because our self-fulfilling expectations have driven it there.\n\nNow you’ll ask, “But why do large funds and major institutions invest in it?” Some are smart and make strategic moves; others are simply human like all of us. Remember, we are always human beings, and our behavior in society is somewhat like that of sheep. Watch *The Big Short.*", "author": "Ok_Educator_3569", "created_time": "2025-01-27T18:10:17", "url": "https://reddit.com/r/investing/comments/1ibfpvb/cryptos_are_shiti_think/", "upvotes": 0, "comments_count": 112, "sentiment": "bullish", "engagement_score": 224.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibgmdi", "title": "Could <PERSON><PERSON>’s Lead Be Swamped by General AI Advancements?", "content": "Waymo has a huge lead in the development of true self-driving technology—in at least two dimensions: (i) they are years ahead; and (ii) they have vast resources that they can and will devote to further improvements. With any sort of “normal” technology, you would expect these advantages to give them a huge advantage for years to come. It’s the promise of that huge market advantage that justifies the enormous R&D that <PERSON><PERSON> is throwing into the project.\n\nBut I wonder (I’m not predicting, I just wonder) whether generic AI technology will quickly improve to the point where “driving” will be trivial to solve by tomorrow’s generation of AIs. It wouldn’t be the first time that a market leader in current technology was leapfrogged by new advances.", "author": "OriginalCompetitive", "created_time": "2025-01-27T18:45:43", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ibgmdi/could_waymos_lead_be_swamped_by_general_ai/", "upvotes": 2, "comments_count": 196, "sentiment": "neutral", "engagement_score": 394.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibjlob", "title": "My pitch as to why CTM will 10x in the near future 🚀", "content": "# 1. Strong Government Contracts Pipeline\n\n* **Defense & Cybersecurity Focus:** Castellum operates in industries heavily backed by government spending, especially in areas like cybersecurity and IT solutions for defense. As cybersecurity threats increase, government contracts may surge, boosting revenues.\n* **Increased Federal Budgets:** Rising U.S. federal defense and cybersecurity budgets could lead to more contracts awarded to Castellum, driving growth.\n\n# 2. Acquisition Strategy\n\n* **Aggressive M&A:** Castellum’s strategy of acquiring smaller companies to expand its market share, services, and customer base could significantly boost revenues and profits. If executed well, this can result in exponential growth.\n* **Revenue Growth from Acquisitions:** Successful acquisitions often lead to an immediate uptick in financial performance, which attracts investor attention.\n\n# 3. Undervalued Stock with Room to Grow\n\n* **Small-Cap Potential:** As a smaller company, Castellum has significant room to grow compared to more established competitors. A strong earnings report or a big contract announcement could send its stock soaring.\n* **Attractive Valuation:** If the stock is currently undervalued relative to its peers or growth potential, it may attract institutional investors, driving up its price.\n\n# 4. Cybersecurity Industry Tailwinds\n\n* **Explosive Industry Growth:** The global cybersecurity market is expected to grow significantly over the coming years, creating a rising tide for companies like Castellum.\n* **Government Emphasis on Security:** Cyberattacks and geopolitical tensions (e.g., with China or Russia) drive demand for advanced cybersecurity solutions, where Castellum could benefit.\n\n# 5. Positive Financial Metrics\n\n* **Revenue Growth:** If Castellum reports quarter-over-quarter revenue and profit growth, this could act as a catalyst for its stock price.\n* **Profitability Milestones:** A shift from operating losses to profitability often triggers a strong upward movement in small-cap stocks.\n\n# 6. Potential Catalyst Events\n\n* **Big Contract Win:** A large-scale government contract announcement could immediately boost investor confidence and trigger a rally.\n* **Partnerships or Alliances:** Strategic partnerships with larger firms or government agencies could add credibility and drive growth.\n* **Analyst Coverage or Upgrades:** If Castellum starts receiving more coverage from Wall Street analysts or a major upgrade, it could attract institutional investors.\n\n# 7. Speculation and Retail Investor Interest\n\n* **Social Media Buzz:** If retail investors (via platforms like Reddit or StockTwits) latch onto Castellum as a growth play, it could generate massive buying pressure.\n* **Low Float Volatility:** With a smaller market cap, Castellum’s stock could experience significant volatility and rapid price increases if trading volume spikes.\n\n# Final Thoughts\n\nCastellum (CTM) is a hidden gem with massive potential in the booming cybersecurity and defense sectors. With an aggressive acquisition strategy, increasing government contracts, and a foothold in an industry set for explosive growth, CTM is poised for a breakout. Its small market cap means any major contract win or positive earnings report could send the stock skyrocketing. Combine that with a growing federal budget for cybersecurity and Castellum’s undervalued position, and you’ve got a high-upside opportunity that’s flying under Wall Street’s radar. 🚀 Don’t sleep on this one—early investors could reap big rewards.\n\n\nEDIT: I TRIED TO TELL YALL! 🚀🚀🚀", "author": "Lopsided_Ad_9166", "created_time": "2025-01-27T20:46:44", "url": "https://reddit.com/r/pennystocks/comments/1ibjlob/my_pitch_as_to_why_ctm_will_10x_in_the_near_future/", "upvotes": 1, "comments_count": 48, "sentiment": "bullish", "engagement_score": 97.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibkcex", "title": "Nvidia sheds almost $600 billion in market cap, biggest one-day loss in U.S. history", "content": "Nvidia lost close to $600 billion in market cap on Monday, the biggest drop for any company on a single day in U.S. history.\n\nThe chipmaker’s stock price plummeted 17% to close at $118.58. It was Nvidia’s worst day on the market since March 16, 2020, which was early in the Covid pandemic. After surpassing Apple last week to become the most valuable publicly traded company, Nvidia’s drop on Monday led a 3.1% slide in the tech-heavy Nasdaq.\n\nThe selloff was sparked by concerns that Chinese artificial intelligence lab DeepSeek is presenting increased competition in the global AI battle. Late last month, DeepSeek unveiled a free, open-source large language model that it says took only two months and less than $6 million to build, using reduced-capability chips from Nvidia, called H800s. \n\nNvidia’s graphics processing units (GPUs) dominate the market for AI data center chips in the U.S., with tech giants like Alphabet, Meta, and Amazon spending billions of dollars on the processors to train and run their AI models. Analysts at Cantor wrote in a report on Monday that the release of DeepSeek’s latest technology has caused “great angst as to the impact for compute demand, and therefore, fears of peak spending on GPUs.”\n\nThe analysts, who recommend buying Nvidia shares, said they “think this view is farthest from the truth,” and that advancements in AI will most likely lead to “the AI industry wanting more compute, not less.”\n\nBut after Nvidia’s huge run-up — the stock soared 239% in 2023 and 171% last year — the market is on edge about any possible pullback in spending. Broadcom, the other big U.S. chipmaker to see giant valuation gains from AI, fell 17% on Monday, pulling its market cap down by $200 billion.\n\nData center companies reliant on Nvidia’s GPUs for their hardware sales saw big selloffs as well. Dell, Hewlett Packard Enterprise and Super Micro Computer dropped at least 5.8%. Oracle, a part of President Donald Trump’s latest AI initiative, fell 14%.\n\nFor Nvidia, the loss was more than double the $279 billion drop the company saw in September, which was the biggest one-day market value loss in history at the time, unseating Meta’s $232 billion loss in 2022. Before that, the steepest drop was $182 billion by Apple in 2020.\n\nNvidia’s decline is more than double the market cap of Coca-Cola and Chevron and exceeds the market value of both Oracle and Netflix.\n\nCEO Jensen Huang’s net worth also took a massive hit, declining roughly $21 billion, according to Forbes’ real-time billionaires list. The move demoted Huang to 17th on the richest-person list.\n\nThe sudden excitement around DeepSeek over the weekend pushed its app past OpenAI’s ChatGPT as the most-downloaded free app in the U.S. on Apple’s App Store. The model’s development comes despite a slew of recent curbs on U.S. chip exports to China.\n\nVenture capitalist David Sacks, who was tapped by Trump to be the White House’s AI and crypto czar, wrote on X that DeepSeek’s model “shows that the AI race will be very competitive” and that Trump was right to rescind President Joe Biden’s executive order last week on AI safety.\n\n“I’m confident in the U.S. but we can’t be complacent,” Sacks wrote.\n\nNvidia is now the third most-valuable public company, behind Apple and Microsoft.\n\nSource: https://www.cnbc.com/2025/01/27/nvidia-sheds-almost-600-billion-in-market-cap-biggest-drop-ever.html", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-01-27T21:16:38", "url": "https://reddit.com/r/stocks/comments/1ibkcex/nvidia_sheds_almost_600_billion_in_market_cap/", "upvotes": 15744, "comments_count": 1205, "sentiment": "bearish", "engagement_score": 18154.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibkwgf", "title": "I believe Nvidia's sell-off is justified—I think they have lost their MOAT.", "content": "A large part of Nvidia’s stock price is tied to the perception that their latest and greatest chips are the only way to achieve the best AI models. However, DeepSeek has proven that this is not the case—state-of-the-art models can be developed using older chips. Granted, they still used Nvidia chips, but this fundamentally shifts the equation.\n\nThis means Nvidia has lost its pricing power. If they price their latest chips too high, customers now have the option to simply use older chips or even switch to AMD’s alternatives.\n\nEffectively, this erodes Nvidia’s competitive advantage and commoditizes their chips.\n\nI agree that AI developers largely use CUDA, and Nvidia's ecosystem is still vast, but... DeepSeek’s model development costs are **27x cheaper** than the latest models developed by American companies. DeepSeek’s subscription costs **$0.50** compared to ChatGPT’s **$20** subscription. Granted, it’s a Chinese company, and there are valid security concerns, but they have published all their research openly—meaning any other company can leverage this research to build models at a lower cost.\n\nUnless someone can definitively prove that Nvidia’s latest and greatest chips are absolutely necessary for developing the best AI models **and** that those best models will provide meaningful value to end customers, I don’t see how Nvidia can maintain its edge.", "author": "wondernits", "created_time": "2025-01-27T21:39:06", "url": "https://reddit.com/r/investing/comments/1ibkwgf/i_believe_nvidias_selloff_is_justifiedi_think/", "upvotes": 1, "comments_count": 81, "sentiment": "bearish", "engagement_score": 163.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibln85", "title": "Trading in My 2022 Tesla Model Y (Underwater) - Negative Equity Roll-Over", "content": "I bought a 2022 Tesla Model Y at the peak (with an employee discount), and I’m still $10K underwater on it. With the new model announcement, political landscape, and Tesla’s aggressive past price cuts, I’m worried it’ll keep depreciating rapidly. Rather than waiting for it to get worse, I’m looking to roll over the negative equity into something that won’t tank in value *as quickly* and keep making my current payment until I’m in the clear. My current payment is \\~$1,200, which I comfortably overpay every month, and the plan is to keep that same payment to get rid of the negative equity as fast as possible while driving something more stable depreciation wise.\n\nI know I got screwed financially and will be screwed with the new car, and there's no way to change that, but I want to try and be slightly *less* screwed faster.\n\nI have a **family friend who owns a Ford dealership** offering me:\n\n* 2023 Bronco (14K miles) for $25K or 2020 Mazda CX-30 (44K miles) for $19K– They’re taking my Tesla for $27K (out of the 38K remaining)\n\nRates are 3.9% on the Tesla, new rate would be 6.7%\n\nLoans are already approved, just debating if this is a solid plan to stem the bleeding, or if this idiocy before I pull the rigger.\n\nBonus: **Not having to charge all the time would make my SO very happy.**", "author": "Lockon007", "created_time": "2025-01-27T22:09:10", "url": "https://reddit.com/r/FinancialPlanning/comments/1ibln85/trading_in_my_2022_tesla_model_y_underwater/", "upvotes": 1, "comments_count": 2, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibmm83", "title": "Option trades for TSLA earnings", "content": "I AM BACK!!!! After being trolled incessantly by, as it turned out, other option firms, so after months of deliberation I decided to start posting again anyway and f\\*\\*\\* the haters. For those that hadn’t read any of my posts last year, my friend has an option analytic system which I use to choose the best trade to do if I want to get involved. I have been trading options since 1989 at various banks or funds but even I know you need information to be successful. It is all about risk/reward and the more information you have at hand the better off you will be.\n\n \n\nAnyway….Tesla. So as I have said before, this is about options NOT direction. I am not going to tell you buy this or sell that….but I will say, if you’re bullish do this, if you’re bearish do that.\n\n \n\nYou choose your own destiny, I am just here to help guide you and if you have any questions regarding the system I use or any option related questions feel free to DM me.\n\n \n\n[Th crossover on the DMX indicator show we may have some more downside](https://preview.redd.it/lul2htlx7mfe1.png?width=1015&format=png&auto=webp&s=fa5a1342a54b004bc11ec93168a7b114332a3e68)\n\n \n\nHaving said all that though, after having the run up that its had I do think that we could easily see a pull back to the 38.1% retracement level which comes in around $375/$380.\n\n \n\nAgain , I DON’T like the really short dated stuff…that’s a literal punt and you may as well head off to the casino…I like looking for trades that are at least 1 to 2 weeks prior to expiration. In this instance though I want to give the trade a bit more time so I decided to look out to May expiry.\n\nhttps://preview.redd.it/27bfyck38mfe1.png?width=940&format=png&auto=webp&s=8c4971ee6b3dacc27ecd6b1a264f31e4938b5318\n\n \n\nInitially out of the list I prefer the ones with the least risk or “premium only” trades , where all you risk is what you lay out….but that’s just me.\n\nThe list generated from the AI; \n\n \n\nhttps://preview.redd.it/o4co6zj48mfe1.png?width=940&format=png&auto=webp&s=9cbef640405980a4df8981aaec61de91792cd570\n\n \n\nSo for the downside I like this PUT BUTTERFLY trade….and below is the HEAT MAP, which will show you where you’ll make money over time and price…\n\n \n\n \n\nhttps://preview.redd.it/6u6wllq58mfe1.png?width=940&format=png&auto=webp&s=b14bd4819f9b34e451596193fa578efa94970809\n\n\n\nhttps://preview.redd.it/sexpcle68mfe1.png?width=940&format=png&auto=webp&s=48f0fa8be6e8d83e33eafffeca7e456a1563cfd5\n\n \n\nNow for those who are bullish, a charge for that $464 area isn’t out of the realms of possibility, so I am running that scenario in the system for “shits and giggles”.\n\nhttps://preview.redd.it/441zpki98mfe1.png?width=940&format=png&auto=webp&s=d9fdd48551e8f4537dd04677d79e43484c02b924\n\n \n\nGiving it a wide range, this is what the system generated\n\nhttps://preview.redd.it/glgggksa8mfe1.png?width=940&format=png&auto=webp&s=df063e4aba70af1ff95c1c7e4ebb709a3c795471\n\nI changed the list to “vanilla” as the system will initially come out with all types of weird ratio trades….\n\nMay as well go with what the system suggested and it picked another Butterfly trrade, this time it’s the 440/460/480 call butterfly at around 90cents\n\nhttps://preview.redd.it/ji9zhi0c8mfe1.png?width=940&format=png&auto=webp&s=31488605f58f9c8c6e984c483235cf2910a30a26\n\n\n\n \n\nhttps://preview.redd.it/f5al0sfd8mfe1.png?width=940&format=png&auto=webp&s=b0fe029c417ac4c8b91656c4c941975650844782\n\nBoth are reasonable returns with low risk…..so pick a side and come on in, the waters’ lovely\n\n \n\n \n\n ", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-27T22:50:17", "url": "https://reddit.com/r/options/comments/1ibmm83/option_trades_for_tsla_earnings/", "upvotes": 29, "comments_count": 23, "sentiment": "bullish", "engagement_score": 75.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibmxop", "title": "WOW! Everyone OK. SMH", "content": "", "author": "RunThePlay55", "created_time": "2025-01-27T23:03:38", "url": "https://reddit.com/r/economy/comments/1ibmxop/wow_everyone_ok_smh/", "upvotes": 233, "comments_count": 68, "sentiment": "neutral", "engagement_score": 369.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}]