{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-03-31", "api_calls": {"load_local_social_media_data": {"data": [{"title": "The 50 best things Microsoft has ever made", "content": "", "created_time": "2025-03-31T16:37:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 219.0, "upvotes": 125, "num_comments": 0, "subreddit": "unknown", "author": "BippityBoppityWhoops", "url": "https://reddit.com/r/microsoft/comments/1jo7k09/the_50_best_things_microsoft_has_ever_made/", "ticker": "MSFT", "date": "2025-03-31"}, {"title": "Working as a V Dash - Feeling lost and that I'll be laid off", "content": "Hey all. Just looking for some advice on my current situation as a V- for microsoft. \n\nI'll start all the way back in November 2024 when the vendor accepted me as a contractor to work for them/microsoft. After signing the paperwork in November, I was told that there are budget constraints to the program I'll be working on and might be delayed. I say this is fine and let me know a new start date, quick timeline below:\n\nNovember 2024 - Accepted Offer  \nDecember 2024 - News of budget constraints and that the role may be gone  \nDecember 2024 - MSFT has approved 2 of 8 headcounts for contractors, you are one of the 2, congrats!  \nJan 2025 - I'm told the budget was approved but they have to do some work to finally get me on boarded  \nFeb 2025 - Finally start at MSFT as a v-\n\n  \nNow that I've started, I'm finding that the work has no managerial oversite, very little direction or training, and the vendor has told us to not speak directly to MSFT unless it's thru one of their contacts. We are doing a little work and raising PR's but we're told there are upstream delays from other teams in getting us more tasks to do. I feel misguided, like the budget was never supposed to be approved, and now MSFT is doing other things on their docket and me and my coworker are very low priority.\n\nShould I expect to be laid off in the future? Any advice is welcome!", "created_time": "2025-03-31T23:56:19", "platform": "reddit", "sentiment": "neutral", "engagement_score": 39.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "ChunkySeaMen", "url": "https://reddit.com/r/microsoft/comments/1joi1dc/working_as_a_v_dash_feeling_lost_and_that_ill_be/", "ticker": "MSFT", "date": "2025-03-31"}, {"title": "MS Office Professional Plus 2019 vs Microsoft 365 Personal functionality loss??", "content": "I am an independent contractor and have used MS Office for over 20 years.  My question is: I purchased Office Professional Plus 2019 for when I just had my desktop. Now I also use my laptop and phone for work so am I \"paying twice for the same thing\" if I also have a Microsoft 365 Personal subscription (for 5 devices)?  Specifically, what functionality will I lose if I move my desktop to 365 also?", "created_time": "2025-03-29T14:52:22", "platform": "reddit", "sentiment": "bearish", "engagement_score": 88.0, "upvotes": 62, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jmnz9e/ms_office_professional_plus_2019_vs_microsoft_365/", "ticker": "MSFT", "date": "2025-03-29"}, {"title": "pls anaylse my portfolio and give me advice", "content": "Hey everyone, I'm participating in a **stock market competition for my business class** with a **$100,000 budget**. The competition **ends in May**, so my goal is to maximize short-term gains within this timeframe.\n\nI’d love some advice on which stocks to **hold, sell, or buy more of** before the competition ends. Here’s my current portfolio:\n\n# 📈 Gaining Stocks\n\n|Ticker|Company|Price|Shares|Gain/Loss ($)|Gain/Loss (%)|Value ($)|\n|:-|:-|:-|:-|:-|:-|:-|\n|**LULU**|Lululemon Athletica Inc|$341.53|10|**+37.40**|**+1.11%**|$4,888.95|\n|**ATZ**|Aritzia Inc|$53.07|20|**+11.40**|**+1.09%**|$1,061.40|\n|**AAPL**|Apple Inc|$223.85|2|**+4.64**|**+1.05%**|$640.88|\n|**PEP**|PepsiCo Inc|$149.67|4|**+4.12**|**+0.69%**|$857.00|\n|**GBTC**|Grayscale Bitcoin Trust (BTC)|$68.80|100|**+39.00**|**+0.57%**|$9,848.62|\n|**BTC**|Grayscale Bitcoin Mini Trust ETF|$38.56|100|**+21.00**|**+0.55%**|$5,519.81|\n|**DUK**|Duke Energy Corp|$117.65|4|**+2.16**|**+0.46%**|$673.66|\n|**TSLA**|Tesla Inc|$273.13|14|**+14.98**|**+0.39%**|$5,473.74|\n|**ETHE**|Grayscale Ethereum Trust ETF|$16.71|100|**+6.00**|**+0.36%**|$2,392.01|\n|**MSFT**|Microsoft Corp|$390.58|4|**+2.44**|**+0.16%**|$2,236.44|\n|**USO**|United States Oil Fund LP|$75.48|100|**+6.00**|**+0.08%**|$10,804.85|\n\n# 📉 Losing Stocks\n\n|Ticker|Company|Price|Shares|Gain/Loss ($)|Gain/Loss (%)|Value ($)|\n|:-|:-|:-|:-|:-|:-|:-|\n|**XLE**|Energy Select Sector SPDR Fund|$92.87|75|**-68.25**|**-0.97%**|$9,970.65|\n|**NVDA**|NVIDIA Corp|$111.43|36|**-83.88**|**-2.05%**|$5,742.38|\n|**PLTR**|Palantir Technologies Inc|$90.09|150|**-328.50**|**-2.37%**|$19,344.38|\n|**CRSP**|Crispr Therapeutics AG|$38.04|5|**-5.70**|**-2.91%**|$272.27|\n|**XYZ**|Block Inc|$56.99|10|**-20.60**|**-3.49%**|$815.80|\n|**GME**|GameStop Corp|$22.09|10|**-62.70**|**-22.11%**|$316.22|\n\n# 📊 Market Index\n\n* **S&P 500**: **5,693.31** (**+0.33%**)\n\n💡 **Looking for Advice:**\n\n* **Which stocks should I hold for short-term gains?**\n* **Should I cut losses on any stocks?**\n* **Any stocks worth adding to improve my portfolio before May?**\n\nbtw my cash balance is like 5k", "created_time": "2025-03-28T00:58:00", "platform": "reddit", "sentiment": "bearish", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Sea_Employer9174", "url": "https://reddit.com/r/investing_discussion/comments/1jlizo1/pls_anaylse_my_portfolio_and_give_me_advice/", "ticker": "MSFT", "date": "2025-03-28"}, {"title": "pls analyse my portfolio and provide me with advice I am a rookie under 18", "content": "Hey everyone, I'm participating in a **stock market competition for my business class** with a **$100,000 budget**. The competition **ends in May**, so my goal is to maximize short-term gains within this timeframe.\n\nI’d love some advice on which stocks to **hold, sell, or buy more of** before the competition ends. Here’s my current portfolio:\n\n# 📈 Gaining Stocks\n\n|Ticker|Company|Price|Shares|Gain/Loss ($)|Gain/Loss (%)|Value ($)|\n|:-|:-|:-|:-|:-|:-|:-|\n|**LULU**|Lululemon Athletica Inc|$341.53|10|**+37.40**|**+1.11%**|$4,888.95|\n|**ATZ**|Aritzia Inc|$53.07|20|**+11.40**|**+1.09%**|$1,061.40|\n|**AAPL**|Apple Inc|$223.85|2|**+4.64**|**+1.05%**|$640.88|\n|**PEP**|PepsiCo Inc|$149.67|4|**+4.12**|**+0.69%**|$857.00|\n|**GBTC**|Grayscale Bitcoin Trust (BTC)|$68.80|100|**+39.00**|**+0.57%**|$9,848.62|\n|**BTC**|Grayscale Bitcoin Mini Trust ETF|$38.56|100|**+21.00**|**+0.55%**|$5,519.81|\n|**DUK**|Duke Energy Corp|$117.65|4|**+2.16**|**+0.46%**|$673.66|\n|**TSLA**|Tesla Inc|$273.13|14|**+14.98**|**+0.39%**|$5,473.74|\n|**ETHE**|Grayscale Ethereum Trust ETF|$16.71|100|**+6.00**|**+0.36%**|$2,392.01|\n|**MSFT**|Microsoft Corp|$390.58|4|**+2.44**|**+0.16%**|$2,236.44|\n|**USO**|United States Oil Fund LP|$75.48|100|**+6.00**|**+0.08%**|$10,804.85|\n\n# 📉 Losing Stocks\n\n|Ticker|Company|Price|Shares|Gain/Loss ($)|Gain/Loss (%)|Value ($)|\n|:-|:-|:-|:-|:-|:-|:-|\n|**XLE**|Energy Select Sector SPDR Fund|$92.87|75|**-68.25**|**-0.97%**|$9,970.65|\n|**NVDA**|NVIDIA Corp|$111.43|36|**-83.88**|**-2.05%**|$5,742.38|\n|**PLTR**|Palantir Technologies Inc|$90.09|150|**-328.50**|**-2.37%**|$19,344.38|\n|**CRSP**|Crispr Therapeutics AG|$38.04|5|**-5.70**|**-2.91%**|$272.27|\n|**XYZ**|Block Inc|$56.99|10|**-20.60**|**-3.49%**|$815.80|\n|**GME**|GameStop Corp|$22.09|10|**-62.70**|**-22.11%**|$316.22|\n\n# 📊 Market Index\n\n* **S&P 500**: **5,693.31** (**+0.33%**)\n\n💡 **Looking for Advice:**\n\n* **Which stocks should I hold for short-term gains?**\n* **Should I cut losses on any stocks?**\n* **Any stocks worth adding to improve my portfolio before May?**\n\nbtw my cash balance is like 5k", "created_time": "2025-03-28T01:02:27", "platform": "reddit", "sentiment": "bearish", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Sea_Employer9174", "url": "https://reddit.com/r/investing_discussion/comments/1jlj2y9/pls_analyse_my_portfolio_and_provide_me_with/", "ticker": "MSFT", "date": "2025-03-28"}, {"title": "Microsoft Word is outdated", "content": "Do you all think that MS Word is outdated, and that there is no perspective of using it in ex. 5 or 10 years. Do you think that competitors are winning over the market(Canva, all productivity apps). What do you all need that word does not have and whot would you change to make Word suitable for your needs", "created_time": "2025-03-28T10:16:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Unable-Cloud8824", "url": "https://reddit.com/r/microsoft/comments/1jlrsfe/microsoft_word_is_outdated/", "ticker": "MSFT", "date": "2025-03-28"}, {"title": "Microsoft Word modernization", "content": "Since my last post was not understood as i wanted to, and i don't want to delete it because it made some discussions. My real question was what would you upgrade or change in Word to make Word more modern, since all users are thriving to simplicity and visual mixed materials nowadays. I respect what Microsoft Word is doing as text editor, but many other softwares started to make docs apps. Therefore my question was what MS Word could use from other apps and implement that to makes us/users happier using Word.", "created_time": "2025-03-28T11:23:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Unable-Cloud8824", "url": "https://reddit.com/r/microsoft/comments/1jlss4s/microsoft_word_modernization/", "ticker": "MSFT", "date": "2025-03-28"}, {"title": "Contact Microsoft via Email", "content": "Does anyone know a way I can email <PERSON>? I would like to ask them something, and I can't seem to find an email for them. And no, I don't need technical support; this is simply personal.", "created_time": "2025-03-28T13:28:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 40.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NK_BW", "url": "https://reddit.com/r/microsoft/comments/1jlv30b/contact_microsoft_via_email/", "ticker": "MSFT", "date": "2025-03-28"}, {"title": "Windows 11 is the New Linux… But Only Intel Users Know the Struggle", "content": "Only Intel users on Windows 11 can truly understand—Windows is basically turning into Linux now. You have to configure everything manually just to keep your system from degrading.\n\nTweaking voltages just to prevent your 13th-gen CPU from throttling or degrading, swapping out NVIDIA drivers to make your GPU work properly… it feels like using Linux, but without the actual benefits of Linux.\n\nAt this point, Windows 11 isn’t an OS—it’s a full-time maintenance job. Anyone else feeling this? 🤦‍♂️", "created_time": "2025-03-28T19:44:36", "platform": "reddit", "sentiment": "bearish", "engagement_score": 30.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "FunNewspaper5161", "url": "https://reddit.com/r/microsoft/comments/1jm3rb1/windows_11_is_the_new_linux_but_only_intel_users/", "ticker": "MSFT", "date": "2025-03-28"}, {"title": "Leaving full time job for contractor role at Microsoft", "content": "\nI’ve been working in a entry level accounting role for about a month now. Today I got offered a contractor position at Microsoft for 18 months. It’s a sales ops role with focus on data analysis, which is something I’m a lot more interested in. \n\nBut the contract is only until June which is the FY for Microsoft, and if this goes well, it will be extended to 18 months.\n\nI’m thinking to take this risk. But I also want to know if anyone has experience with contractor roles at Microsoft? I’m sure converting to fte here would not be realistic, but does having a contractor role at big tech actually open new doors? ", "created_time": "2025-03-27T00:37:05", "platform": "reddit", "sentiment": "neutral", "engagement_score": 51.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jkrh6c/leaving_full_time_job_for_contractor_role_at/", "ticker": "MSFT", "date": "2025-03-27"}, {"title": "Got my first laptop with Windows Hello yesterday. I had no idea it was that good.", "content": "Way quicker than I thought it would be. \n\nEdit: I'm referring to the facial recognition ", "created_time": "2025-03-27T05:10:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 35.0, "upvotes": 17, "num_comments": 0, "subreddit": "unknown", "author": "MarioDF", "url": "https://reddit.com/r/microsoft/comments/1jkwjg3/got_my_first_laptop_with_windows_hello_yesterday/", "ticker": "MSFT", "date": "2025-03-27"}, {"title": "[30 F] wanting to quit 60k corporate job for wedding photography. Thoughts on slower FIRE but a better journey? [7 year UPDATE]", "content": "HEY! 7 years ago I came to this subreddit to ask if I would be a total moron to quit a cushy corporate job to go full time with my wedding photography business. \n\nI got great responses from \"yes\" to \"yeah totally you're so dumb\" that helped ground me massively. Reading all the responses encouraged me to wait an extra 6 months to see how my bookings would pan out and re-calculate everything that could go wrong again and again.  \n  \nBut I did it! And it fucking rocks! 230 weddings later, current stats:  \n  \nAverage \\~150k income after expenses for the last few years. My cash and retirement net worth without my husband's contribution (for stats purposes) is $550K. We also own our home outright and may never move as we're planning to be childfree. Cheat code!\n\nBut most importantly I feel none of the panic I felt about my future while I was working at a large corporate office. I know I may not be able to do this job all the way until retirement, but I now know that there are jobs out there that sit well in my brain and make me overall a happy person. \n\n", "created_time": "2025-03-27T05:56:33", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2718.0, "upvotes": 2552, "num_comments": 0, "subreddit": "unknown", "author": "unorthodoxninja", "url": "https://reddit.com/r/financialindependence/comments/1jkx7lf/30_f_wanting_to_quit_60k_corporate_job_for/", "ticker": "MSFT", "date": "2025-03-27"}, {"title": "microsoft at 50 event", "content": "Who was the lady that wanted to schedule a meeting to talk about Palestine? Just want to schedule a meeting with HER. ", "created_time": "2025-03-27T06:43:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "showme420", "url": "https://reddit.com/r/microsoft/comments/1jkxumo/microsoft_at_50_event/", "ticker": "MSFT", "date": "2025-03-27"}, {"title": "Anyone familiar with the 'Customer Zero' team at Microsoft (Job ID 1805500)?", "content": "Hey folks,  \nI’m considering applying for a **Senior Software Engineer** role at Microsoft (Job ID **1805500**). The team is under the **Customer Zero Group**, part of the **Business & Industry Copilots** org.\n\nDoes anyone here have experience with or know about this team? I’d love to understand:\n\n* What kind of work they do\n* Team culture and work-life balance\n* Tech stack\n* Growth opportunities\n\nAny insights would be really appreciated. Thanks a lot!", "created_time": "2025-03-27T14:12:11", "platform": "reddit", "sentiment": "bullish", "engagement_score": 29.0, "upvotes": 7, "num_comments": 0, "subreddit": "unknown", "author": "Great-Ad-4616", "url": "https://reddit.com/r/microsoft/comments/1jl4tm1/anyone_familiar_with_the_customer_zero_team_at/", "ticker": "MSFT", "date": "2025-03-27"}, {"title": "Microsoft developed this technique which combines RAG and fine-tuning for better domain adaptation", "content": "I've been exploring Retrieval Augmented Fine-Tuning (RAFT). Combines RAG and finetuning for better domain adaptation. Along with the question, the doc that gave rise to the context (called the oracle doc) is added, along with other distracting documents. Then, with a certain probability, the oracle document is not included. Has there been any successful use cases of RAFT in the wild? Or has it been overshadowed. In that case, by what?\n\n\\---  \n[Here's how I tried it](https://github.com/bespokelabsai/curator/tree/main/examples/blocks/raft)  \nMore on [RAFT](https://techcommunity.microsoft.com/blog/aiplatformblog/raft-a-new-way-to-teach-llms-to-be-better-at-rag/4084674) by Microsoft", "created_time": "2025-03-27T21:41:56", "platform": "reddit", "sentiment": "bullish", "engagement_score": 33.0, "upvotes": 31, "num_comments": 0, "subreddit": "unknown", "author": "Ambitious_Anybody855", "url": "https://reddit.com/r/microsoft/comments/1jlelvp/microsoft_developed_this_technique_which_combines/", "ticker": "MSFT", "date": "2025-03-27"}, {"title": "Microsoft 360 Fmily price question", "content": "HI! I've been checking the diferent options to buy Microsoft 365 and I saw the family option for 13€/month, which allows up to 6 people. I really like everything it comres with, but I have a few questions:\n\n\\- The price is applied to each person individually? So like, eveyrone pays 13€? Or is it like one person pays 13€ and up to 6 people can get the benefits?\n\n\\- It says there's up to 6TB of space (1TB per person). Regarding this, I have to questions:\n\n \\- What exactly can I save? I've never used Microsoft 360 or antyhing, so is it only stuff like PowerPoint, Word etc... or can I save any sort of file of my computer? I was looking into this because I'm making a backup of my computer and I wanted to put everything in One Drive but as I don't have any subscription I need more space. 1TB would be amazing specially since I could use it for saving other stuff from external disks that take up space. But again, I'm not sure if I can just save whatever I want or its only reserved to Microsoft services (PowerPoint, Word...)\n\n\\- If I buy this, I doubt my family plan wpuld include 6 people. That means that I could use more than 1TB or the TBs are reserved for each person? My girlfriend and I are planning to buy the family plan (hence why I'm asking) so each one of us could use 3TB or is it limited to 1 per person?\n\nI hope eveyrthing is clear; thank you!", "created_time": "2025-03-27T22:52:02", "platform": "reddit", "sentiment": "bullish", "engagement_score": 28.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON>_<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jlg9o8/microsoft_360_fmily_price_question/", "ticker": "MSFT", "date": "2025-03-27"}, {"title": "Microsoft Azure SWEs : Do you like work?", "content": "I'm wondering if the work is bearable/likeable as a full-time job, and if co-workers are hard-working but not toxic. Especially with the recent layoffs without severance.", "created_time": "2025-03-26T01:45:39", "platform": "reddit", "sentiment": "bearish", "engagement_score": 17.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "No-Tangelo-1857", "url": "https://reddit.com/r/microsoft/comments/1jk0mht/microsoft_azure_swes_do_you_like_work/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Is Copilot+ PC Software Actually Limited to Having an \"NPU\" or Will It Eventually Be Compatible with Older PCs? How Good Is the Live Translation?", "content": "Historically Microsoft has launched proprietary software with \"recommended hardware\" and then \"minimum hardware\" specs. I'm wondering if this is perhaps the case with the Copilot+ feautures, which are described using similar terms: [https://www.microsoft.com/en-us/windows/copilot-plus-pcs?r=1#faq1](https://www.microsoft.com/en-us/windows/copilot-plus-pcs?r=1#faq1)\n\nIs an \"NPU\" actually a totally new component such that trying to run Copilot+ would be like trying to run a game without a videocard? ...or is Copilot+ just temporarily exclusive to hardware branded as \"Copilot+ ready\" to sell some new computers and it will eventually be available on PCs without those specs?\n\nI'm also wondering if anyone can speak to how effective the live translation is. I moved to Norway for a job and don't speak Norwegian yet, so I am often sitting in meetings without understanding a word of what is going on. Google translate and similar software have been basically useless for live translation. The Copilot+ live translation seems like the perfect solution to this if it works well.", "created_time": "2025-03-26T10:57:51", "platform": "reddit", "sentiment": "bearish", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jk91ej/is_copilot_pc_software_actually_limited_to_having/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Microsoft Defender for Endpoint Plan 1 vs Microsoft Defender for Endpoint Plan 2", "content": "Got about 400 users that need an endpoint protection plan...Wondering if it is worth paying the difference on Microsoft Defender for Endpoint Plan 1 and get Microsoft Defender for Endpoint Plan 2.... Getting hassled by auditors, I guess reports from sccm on the Microsoft defender that is shipped with windows doesn't cut it any more.\n\nWhat is the experience out here? Do you have an opinion on either of them, better yet, maybe both? I would like to hear it.", "created_time": "2025-03-26T12:31:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Kitchen-Magician-421", "url": "https://reddit.com/r/microsoft/comments/1jkammt/microsoft_defender_for_endpoint_plan_1_vs/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Problems with Microsoft Learn?", "content": "Hi,\n\nMy Microsoft Learn webpages are not loading, no matter how many times i refresh it.  I'm learning C# and here's an example what a knowledge check page looks like for me: [https://imgur.com/a/3Cg5zIV](https://imgur.com/a/3Cg5zIV) \n\nCan anyone here help me?", "created_time": "2025-03-26T14:08:56", "platform": "reddit", "sentiment": "neutral", "engagement_score": 47.0, "upvotes": 13, "num_comments": 0, "subreddit": "unknown", "author": "villell", "url": "https://reddit.com/r/microsoft/comments/1jkcn1d/problems_with_microsoft_learn/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Microsoft’s account sign-in UI gets a new design and dark mode", "content": "", "created_time": "2025-03-26T17:31:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 65.0, "upvotes": 57, "num_comments": 0, "subreddit": "unknown", "author": "BippityBoppityWhoops", "url": "https://reddit.com/r/microsoft/comments/1jkhgy9/microsofts_account_signin_ui_gets_a_new_design/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Around six weeks ago, I applied to a couple software development jobs at Microsoft, including the \"Neurodiversity Hiring Program\". One application is in review, one is transferred. How long should I keep waiting for?", "content": "Any suggestions for following up?", "created_time": "2025-03-26T17:47:32", "platform": "reddit", "sentiment": "bullish", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Cheetah3051", "url": "https://reddit.com/r/microsoft/comments/1jkhv9d/around_six_weeks_ago_i_applied_to_a_couple/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Microsoft pulls back from more data center leases in US and Europe, analysts say", "content": "", "created_time": "2025-03-26T18:40:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 67.0, "upvotes": 53, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jkj5xm/microsoft_pulls_back_from_more_data_center_leases/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "What to expect for your first week at Microsoft?", "content": "I haven't been in touch since I received my start date (3/20). My start date is 3/31, should I expect to get my equipment before my start date? Will I receive instructions for the first day/week sometime this week? Should I just reach out to my hiring manager? Any tips or advice is welcomed. ", "created_time": "2025-03-25T02:20:50", "platform": "reddit", "sentiment": "neutral", "engagement_score": 98.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "rusty919", "url": "https://reddit.com/r/microsoft/comments/1jj8zho/what_to_expect_for_your_first_week_at_microsoft/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "CoPilot = Microsoft just cant get it right", "content": "Copilot on my Windows PC was working great at first. Could talk with it using a microphone and have a semi natural conversation. A few months ago, an update must have occurred and that  stopped working. Could only txt chat with it via the keyboard. \n\nI do have a 365 business license which seems to have caused some conflicts. But why would a free MS Live account have better copilot features than any PAID 365 license? \n\nNow today it will not launch at all. \n\nWhat's going on with MS Copilot?  Too many cooks at the kitchen in MS or are they just unable to do anything right?", "created_time": "2025-03-25T12:20:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "captain_222", "url": "https://reddit.com/r/microsoft/comments/1jji112/copilot_microsoft_just_cant_get_it_right/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "Microsoft AI Tour Resources Now Available for public use", "content": "", "created_time": "2025-03-25T12:23:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "Wireless_Life", "url": "https://reddit.com/r/microsoft/comments/1jji2v5/microsoft_ai_tour_resources_now_available_for/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "How a Microsoft exec managed to pitch Microsoft Word through the genius tactic of being able to actually use it in a 'type-off' demanded by clients: 'I was the only one who'd actually been a secretary'", "content": "", "created_time": "2025-03-25T19:35:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 174.0, "upvotes": 170, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jjs46o/how_a_microsoft_exec_managed_to_pitch_microsoft/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "Microsoft Designer got worse", "content": "If the usage limit wasn't enough today I find it is ignoring the image size specification and always creating square even if set to landscape or portrait.\n\nPlus usual moan about filtering...", "created_time": "2025-03-25T21:22:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 17.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "Mysterious_Peak_6967", "url": "https://reddit.com/r/microsoft/comments/1jjur9m/microsoft_designer_got_worse/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "What are your top picks among institutional held stocks?", "content": "Hey investing community,\n\nInstitutional investors often have deep research and insights in their investment choices. \n\nWhich institutional held stock you think are the cream of the crop?\n\n  \nHere are a few examples of stocks that are commonly held by institutions:\n\nTechnology sector: $AAPL $MSFT $GOOGL\n\nHealthcare sector: $JNJ $UNH\n\nFinancial sector: $JPM $V\n\nConsumer Goods sector: $PG $KO\n\nIndustrial sector: $GE $CAT\n\nEnergy sector: $XOM $CVX\n\n  \nI've also cultivated my own list of AI stocks:\n\n🔧 AI Infrastructure\n\n\\- Compute & Chips: $NVDA, $AVGO, $AMD\n\n\\- Cloud Computing & AI Models: $MSFT, $GOOG, $AMZN, $ORCL\n\n🤖 AI + Hardware (Smart Device Era)\n\n\\- Robotics & Smart Devices: $TSLA, $META, $SMCI\n\n\\- Chip Design & Specialized Hardware: $SNPS, $GFS\n\n🚀AI Application Layer (Accelerating Commercialization)\n\n\\- Productivity Tools & SaaS: $ADBE, $TTD, $CRWD\n\n\\- Fintech: $PYPL, $V, $MA\n\n\\- Vertical-specific AI: $PLTR, $COIN, $AIFU\n\n🌐 Traditional Enterprises Undergoing AI Transformation (Stealth Growth Opportunities)\n\n\\- Retail & Consumer: $COST, $PDD\n\n\\- Energy & Manufacturing: $XOM, $MPWR\n\n\\- Finance & Logistics: $JPM, $UPS\n\n🚀 Dark Horse Potential: $AIFU\n\nThis under-the-radar  AIdriven insurance & healthcare company could be a major beneficiary if AI truly disrupts the insurance industry.\n\nFeel free to share your picks too!", "created_time": "2025-03-24T09:00:28", "platform": "reddit", "sentiment": "bullish", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Full-Law-8206", "url": "https://reddit.com/r/investing_discussion/comments/1jimbfk/what_are_your_top_picks_among_institutional_held/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "Annoying \"Restore Pages\" popup on Edge", "content": "The \"Restore pages\" popup on <PERSON> appears every time I open the browser.\n\nI know this has been reported thousands of times, and the \"solutions\" include editing the windows registry, or manually tweaking files. \n\n**But the thing is that most of companies that gives us work laptops, and doing these solutions on work laptops or company virtual machines is a no no.** \n\nIs it SO hard to just put a slider to \"Restore pages? Yes/No\" ???\n\nAnd no, this annoying popup doesn't go away with the Esc key, and is not because my computer failed or something, it is shown always. ", "created_time": "2025-03-24T14:23:38", "platform": "reddit", "sentiment": "bearish", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Normal_Helicopter_22", "url": "https://reddit.com/r/microsoft/comments/1jirugs/annoying_restore_pages_popup_on_edge/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "Final round done, no update from recruiter yet – is this normal for Microsoft?", "content": "Hi all, I recently completed all rounds for a **Senior Software Engineer role (L63/L64)** at Microsoft, including a final short round with a senior leader. The last interview was **general/behavioral** and went fairly well in my opinion.\n\nI had my final round on **week before**, and I connected with the recruiter again today, but she said she **still doesn’t have an update**. This is after 5 previous rounds and a hiring call was supposed to happen post-interview.\n\nIs it typical for things to go silent for several days even after all interviews are done? Could this be a sign they’re stalling or waiting on approvals? How long does it usually take for the team to finalize post-final round?\n\nAppreciate any insights from folks who’ve been through the Microsoft hiring loop — or anyone internally who can shed some light on how final decisions are made at this stage.\n\nThanks!\n\nEdit: THEY ARE MOVING AHEAD WITH OTHER CANDIDATE! 🫠\nAlthough it was hire call in loop interview.\n\nEdit Again: They offered me the position.", "created_time": "2025-03-24T17:29:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 40.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Great-Ad-4616", "url": "https://reddit.com/r/microsoft/comments/1jiwezb/final_round_done_no_update_from_recruiter_yet_is/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "If you invested in one share of Microsoft stock when it first became public, it would be worth $128,206 today (including dividends).", "content": "", "created_time": "2025-03-24T18:07:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 173.0, "upvotes": 119, "num_comments": 0, "subreddit": "unknown", "author": "MaxGoodwinning", "url": "https://reddit.com/r/microsoft/comments/1jixdko/if_you_invested_in_one_share_of_microsoft_stock/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "Where to buy Publisher 2021?", "content": "With Publisher being discontinued in 2026, I've been informed by an agent that the 2021 version is the next best thing to get as it won't be affected by the delisting.\n\nIssue is the Office Pro 2021 package costs a fortune:\nhttps://www.microsoft.com/en-us/microsoft-365/p/office-professional-2021/CFQ7TTC0HHJ9?Invisibiliarevelare=true&msockid=1c81a6cd30306d1e243fb24831756ccf&activetab=pivot:overviewtab\n\nSo is there a cheaper way to buy just Publisher 2021 on it's own?", "created_time": "2025-03-24T23:42:23", "platform": "reddit", "sentiment": "bullish", "engagement_score": 7.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "DaveMan1K", "url": "https://reddit.com/r/microsoft/comments/1jj5p2n/where_to_buy_publisher_2021/", "ticker": "MSFT", "date": "2025-03-24"}], "metadata": {"timestamp": "2025-07-06T21:22:04.890126", "end_date": "2025-03-31", "days_back": 7, "successful_dates": ["2025-03-31", "2025-03-29", "2025-03-28", "2025-03-27", "2025-03-26", "2025-03-25", "2025-03-24"], "failed_dates": ["2025-03-30"], "source": "local"}}}}