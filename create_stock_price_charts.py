#!/usr/bin/env python3
"""
股票价格波动图绘制脚本
基于AI对冲基金回测系统的价格数据获取逻辑，为MSFT和AAPL创建价格可视化图表
时间范围：2025年1月1日至2025年6月1日
参考NVDA_price_chart_2025.png的样式模板
"""

import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import numpy as np
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目中的价格获取函数
try:
    from src.tools.api import get_prices, get_price_data
    from src.data.models import Price
    print("✅ 成功导入项目价格获取模块")
except ImportError as e:
    print(f"❌ 导入价格获取模块失败：{e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

def analyze_price_data_system():
    """分析AI对冲基金系统中的价格数据获取实现"""
    print("=== AI对冲基金价格数据系统分析 ===")
    
    print("📊 价格数据获取方式：")
    print("   1. 主要数据源：Financial Datasets API (https://api.financialdatasets.ai)")
    print("   2. 备用数据源：本地缓存系统")
    print("   3. 数据格式：OHLCV (开盘价、最高价、最低价、收盘价、成交量)")
    print("   4. 时间间隔：日线数据")
    
    print("\n🔧 核心函数：")
    print("   - get_prices(): 获取价格数据，支持缓存")
    print("   - get_price_data(): 返回pandas DataFrame格式")
    print("   - Price模型：Pydantic数据模型，包含OHLCV字段")
    
    print("\n💾 缓存机制：")
    print("   - 内存缓存：避免重复API调用")
    print("   - 数据合并：自动去重和数据整合")
    print("   - 按股票代码分别缓存")
    
    print("\n🔑 API配置：")
    print("   - 环境变量：FINANCIAL_DATASETS_API_KEY")
    print("   - 请求头：X-API-KEY")
    print("   - 重试机制：内置错误处理")

def fetch_stock_price_data(ticker: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
    """
    获取股票价格数据
    
    Args:
        ticker: 股票代码 (如 'MSFT', 'AAPL')
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        
    Returns:
        pd.DataFrame: 价格数据，如果失败则返回None
    """
    print(f"=== 获取 {ticker} 价格数据 ===")
    print(f"📅 时间范围：{start_date} 到 {end_date}")
    
    try:
        # 使用项目中的价格获取函数
        prices = get_prices(ticker, start_date, end_date)
        
        if not prices:
            print(f"❌ 未获取到 {ticker} 价格数据")
            return None
            
        print(f"✅ 成功获取 {len(prices)} 条 {ticker} 价格记录")
        
        # 转换为DataFrame
        df = pd.DataFrame([p.model_dump() for p in prices])
        df['Date'] = pd.to_datetime(df['time'])
        df.set_index('Date', inplace=True)
        df.sort_index(inplace=True)
        
        # 确保数值列为数值类型
        numeric_cols = ['open', 'close', 'high', 'low', 'volume']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"📊 数据范围：{df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
        print(f"💰 价格范围：${df['low'].min():.2f} - ${df['high'].max():.2f}")
        
        return df
        
    except Exception as e:
        print(f"❌ 获取 {ticker} 价格数据失败：{e}")
        return None

def create_stock_price_chart(df: pd.DataFrame, ticker: str, company_name: str = "") -> plt.Figure:
    """
    创建股票价格波动图
    
    Args:
        df: 价格数据DataFrame
        ticker: 股票代码
        company_name: 公司名称（用于显示）
        
    Returns:
        plt.Figure: matplotlib图表对象
    """
    print(f"=== 创建 {ticker} 价格波动图 ===")
    
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表 - 参考NVDA图表的布局
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), height_ratios=[3, 1])
    
    # 主图：价格走势
    ax1.plot(df.index, df['close'], label='收盘价', color='#1f77b4', linewidth=2.5)
    ax1.fill_between(df.index, df['low'], df['high'], alpha=0.2, color='lightblue', label='日内波动范围')
    
    # 添加移动平均线
    df['MA5'] = df['close'].rolling(window=5).mean()
    df['MA20'] = df['close'].rolling(window=20).mean()
    
    ax1.plot(df.index, df['MA5'], label='5日均线', color='orange', linewidth=1.5, alpha=0.8)
    ax1.plot(df.index, df['MA20'], label='20日均线', color='red', linewidth=1.5, alpha=0.8)
    
    # 设置主图标题和标签
    title = f'{ticker}'
    if company_name:
        title += f' ({company_name})'
    title += ' 股价走势图 (2025.01.01 - 2025.06.01)'
    
    ax1.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('价格 (USD)', fontsize=12)
    ax1.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, linestyle='--')
    
    # 格式化Y轴
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:.0f}'))
    
    # 子图：成交量
    ax2.bar(df.index, df['volume'], alpha=0.7, color='gray', width=0.8, label='成交量')
    ax2.set_ylabel('成交量', fontsize=12)
    ax2.set_xlabel('日期', fontsize=12)
    ax2.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3, linestyle='--')
    
    # 格式化成交量Y轴
    ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e6:.1f}M'))
    
    # 设置X轴日期格式
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_filename = f'{ticker}_price_chart_2025.png'
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"📈 图表已保存为：{chart_filename}")
    
    return fig

def calculate_stock_statistics(df: pd.DataFrame, ticker: str) -> Dict[str, Any]:
    """
    计算股票价格统计信息

    Args:
        df: 价格数据DataFrame
        ticker: 股票代码

    Returns:
        Dict[str, Any]: 统计信息字典
    """
    print(f"=== {ticker} 价格统计分析 ===")

    # 基本统计
    start_price = df['close'].iloc[0]
    end_price = df['close'].iloc[-1]
    total_return = (end_price - start_price) / start_price * 100

    max_price = df['high'].max()
    min_price = df['low'].min()
    avg_price = df['close'].mean()

    # 波动率计算
    df['daily_return'] = df['close'].pct_change()
    volatility = df['daily_return'].std() * np.sqrt(252) * 100  # 年化波动率

    # 最大回撤
    df['cumulative'] = (1 + df['daily_return']).cumprod()
    df['running_max'] = df['cumulative'].expanding().max()
    df['drawdown'] = (df['cumulative'] - df['running_max']) / df['running_max']
    max_drawdown = df['drawdown'].min() * 100

    # 平均成交量
    avg_volume = df['volume'].mean()

    stats = {
        'ticker': ticker,
        'start_price': start_price,
        'end_price': end_price,
        'total_return': total_return,
        'max_price': max_price,
        'min_price': min_price,
        'avg_price': avg_price,
        'volatility': volatility,
        'max_drawdown': max_drawdown,
        'avg_volume': avg_volume,
        'trading_days': len(df)
    }

    # 打印统计信息
    print(f"📊 {ticker} 统计摘要：")
    print(f"   起始价格: ${start_price:.2f}")
    print(f"   结束价格: ${end_price:.2f}")
    print(f"   总收益率: {total_return:.2f}%")
    print(f"   最高价格: ${max_price:.2f}")
    print(f"   最低价格: ${min_price:.2f}")
    print(f"   平均价格: ${avg_price:.2f}")
    print(f"   年化波动率: {volatility:.2f}%")
    print(f"   最大回撤: {max_drawdown:.2f}%")
    print(f"   平均成交量: {avg_volume/1e6:.1f}M")
    print(f"   交易天数: {len(df)} 天")

    return stats

def create_comparison_chart(msft_df: pd.DataFrame, aapl_df: pd.DataFrame):
    """
    创建MSFT和AAPL的对比图表

    Args:
        msft_df: MSFT价格数据
        aapl_df: AAPL价格数据
    """
    print("=== 创建MSFT vs AAPL对比图表 ===")

    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 计算标准化收益率（以第一天为基准）
    msft_normalized = (msft_df['close'] / msft_df['close'].iloc[0] - 1) * 100
    aapl_normalized = (aapl_df['close'] / aapl_df['close'].iloc[0] - 1) * 100

    # 创建对比图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), height_ratios=[2, 1])

    # 主图：标准化收益率对比
    ax1.plot(msft_df.index, msft_normalized, label='MSFT (微软)', color='#00A4EF', linewidth=2.5)
    ax1.plot(aapl_df.index, aapl_normalized, label='AAPL (苹果)', color='#007AFF', linewidth=2.5)

    ax1.set_title('MSFT vs AAPL 收益率对比 (2025.01.01 - 2025.06.01)', fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('累计收益率 (%)', fontsize=12)
    ax1.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, linestyle='--')
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)

    # 子图：价格对比（双Y轴）
    ax2_twin = ax2.twinx()

    line1 = ax2.plot(msft_df.index, msft_df['close'], label='MSFT 价格', color='#00A4EF', linewidth=2)
    line2 = ax2_twin.plot(aapl_df.index, aapl_df['close'], label='AAPL 价格', color='#007AFF', linewidth=2)

    ax2.set_ylabel('MSFT 价格 (USD)', fontsize=12, color='#00A4EF')
    ax2_twin.set_ylabel('AAPL 价格 (USD)', fontsize=12, color='#007AFF')
    ax2.set_xlabel('日期', fontsize=12)

    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax2.legend(lines, labels, loc='upper left', frameon=True, fancybox=True, shadow=True)

    ax2.grid(True, alpha=0.3, linestyle='--')

    # 设置X轴日期格式
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

    # 调整布局
    plt.tight_layout()

    # 保存对比图表
    comparison_filename = 'MSFT_vs_AAPL_comparison_2025.png'
    plt.savefig(comparison_filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"📊 对比图表已保存为：{comparison_filename}")

    return fig

def main():
    """主函数 - 创建MSFT和AAPL价格图表"""
    print("🚀 AI对冲基金系统 - MSFT & AAPL 价格数据分析和可视化")
    print("=" * 60)

    # 分析价格数据获取系统
    analyze_price_data_system()

    # 设置日期范围
    start_date = "2025-01-01"
    end_date = "2025-06-01"

    # 股票信息
    stocks = {
        'MSFT': '微软',
        'AAPL': '苹果'
    }

    # 存储数据和统计信息
    stock_data = {}
    stock_stats = {}

    print(f"\n📅 分析时间范围：{start_date} 到 {end_date}")
    print("=" * 60)

    # 获取每只股票的数据
    for ticker, company_name in stocks.items():
        print(f"\n🔍 处理 {ticker} ({company_name}) 股票...")

        # 获取价格数据
        df = fetch_stock_price_data(ticker, start_date, end_date)

        if df is None or df.empty:
            print(f"❌ 无法获取 {ticker} 价格数据，跳过...")
            continue

        # 存储数据
        stock_data[ticker] = df

        # 计算统计信息
        stats = calculate_stock_statistics(df, ticker)
        stock_stats[ticker] = stats

        # 创建个股价格图表
        try:
            fig = create_stock_price_chart(df, ticker, company_name)
            plt.close(fig)  # 关闭图表以释放内存
            print(f"✅ {ticker} 图表创建成功")
        except Exception as e:
            print(f"❌ 创建 {ticker} 图表失败：{e}")

    # 创建对比图表（如果两只股票数据都可用）
    if 'MSFT' in stock_data and 'AAPL' in stock_data:
        print(f"\n📊 创建MSFT vs AAPL对比分析...")
        try:
            comparison_fig = create_comparison_chart(stock_data['MSFT'], stock_data['AAPL'])
            plt.close(comparison_fig)
            print("✅ 对比图表创建成功")
        except Exception as e:
            print(f"❌ 创建对比图表失败：{e}")

    # 打印总结报告
    print("\n" + "=" * 60)
    print("📈 分析总结报告")
    print("=" * 60)

    if stock_stats:
        for ticker, stats in stock_stats.items():
            company_name = stocks.get(ticker, ticker)
            print(f"\n🏢 {ticker} ({company_name}):")
            print(f"   📊 总收益率: {stats['total_return']:+.2f}%")
            print(f"   📈 价格区间: ${stats['min_price']:.2f} - ${stats['max_price']:.2f}")
            print(f"   📉 最大回撤: {stats['max_drawdown']:.2f}%")
            print(f"   📊 年化波动率: {stats['volatility']:.2f}%")
            print(f"   💹 平均成交量: {stats['avg_volume']/1e6:.1f}M")

    # 生成的文件列表
    print(f"\n📁 生成的图表文件：")
    generated_files = []
    for ticker in stocks.keys():
        if ticker in stock_data:
            filename = f"{ticker}_price_chart_2025.png"
            if os.path.exists(filename):
                generated_files.append(filename)
                print(f"   ✅ {filename}")

    if 'MSFT' in stock_data and 'AAPL' in stock_data:
        comparison_file = "MSFT_vs_AAPL_comparison_2025.png"
        if os.path.exists(comparison_file):
            generated_files.append(comparison_file)
            print(f"   ✅ {comparison_file}")

    if not generated_files:
        print("   ❌ 未生成任何图表文件")
        print("\n🔧 故障排除建议：")
        print("   1. 检查网络连接是否正常")
        print("   2. 确认 FINANCIAL_DATASETS_API_KEY 环境变量已设置")
        print("   3. 验证API配额是否充足")
        print("   4. 检查项目依赖是否正确安装")
    else:
        print(f"\n✅ 分析完成！共生成 {len(generated_files)} 个图表文件")
        print("📊 所有图表均采用与NVDA参考图表一致的样式和格式")

if __name__ == "__main__":
    main()
