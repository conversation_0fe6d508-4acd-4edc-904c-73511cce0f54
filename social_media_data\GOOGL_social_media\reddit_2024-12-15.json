[{"platform": "reddit", "post_id": "reddit_1heov0m", "title": "Starting on my fire journey, advice needed!", "content": "Advice on portfolio/finances needed:\n\n36/M/Single- I only started earning more money in the last 3-4 years. I spent a lot in my 20s and didn’t earn as much. \n\nI didn’t grow up rich, so having such $ is new to me. I am from Singapore, so we should purely just look at my few “investment” strategy/plan below and ignore things like insurance, apartment (where I’d stay), day to day stuff etc as it gets complex trying to explain it. \n\nCurrently, I have:\n\n180k~ in IBKR\n40K in endowment maturing in about 3 years\n80k in emergency cash earning 3% a year. \n\nShared investment property with my friend and we will take profit in about 1.5-2 years time. We can assume my take profit then is around 70k. Pls discount my capital because it needs to go somewhere else. \n\nBulk of my IBKR portfolio comes from TSLA and PLTR. \n\nMkt Value \nMajority pool $160k\nTSLA PLTR MSFT \n\nMinority Pool\nSOFI, NU, NVDA, AMD - Around 20k\n\nI started dca-ing in VUAA recently at $500usd a month.\n\nRight now, I am thinking of:\n\n1) Selling 20% of tsla (20k), and selling my pool of minority group of shares 20k and with this 40k put it into a single company. A bit more risky? So hopefully higher reward.\n\n2) Sell ALL put 100k into microsoft, google or one of those stable firms that is likely to grow in the long run and buy more when it dips (or dca) with the remaining 80k. \n\n3) Sell ALL, use 100k to trade, remaining 80k into the emergency funds pool. I am not an experienced trader but I started this journey with 85k and have already more than doubled many thanks to tsla and PLTR.\n\nI do not intend to put fresh funds into IBKR (aside from the $500 usd monthly vuaa) now because I feel that while 80k emergency savings is ok, I would like to have more. Maybe 120k (?) to feel more secure. I also struggle with how much is enough as I am also responsible for my parents. I am fine caring for them, so we don’t need to go into details about their finances or any other help.\n\nMy goal is that I would like my money to grow faster, and more for rainy days, live a little and hopefully retire sooner than later. \n\nAny two cents worth in today’s market? \n", "author": "No-Jicama-848", "created_time": "2024-12-15T09:08:09", "url": "https://reddit.com/r/Fire/comments/1heov0m/starting_on_my_fire_journey_advice_needed/", "upvotes": 2, "comments_count": 3, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hepy56", "title": "Dropped everything this year, moved back in with my parents, built my first real product in 3 days, and sold 50 copies in 50 hours", "content": "After six years in my field, I hit a breaking point. I’d been working hard, but nothing I was building felt personal or meaningful to me. Late last year, I split ways with my last gig, and everything I’d been avoiding hit me at once.\n\nI didn’t know exactly what I wanted, but I knew I couldn’t keep doing the same thing.\n\nIn January, my partner and I packed up and moved out of our place to live with my parents. It wasn’t where I thought I’d be at 25, and honestly, it felt like a step backward. Those months were full of frustration and self-doubt, but they also forced me to rethink everything.\n\nFor the first time in years, I had space to *create*.\n\nI dove into no-code tools like Webflow and Wized, rediscovering how much I loved building things. Eventually, I pushed myself back into coding after years away. I picked up new frameworks, started experimenting, and challenged myself to see what I could create from scratch.\n\nWhen my design and development business finally started gaining traction, we moved back out on our own. It was a huge win, but I was still battling the financial ups and downs of being my own boss.\n\nAnd that’s when it hit me: I needed to rebuild my relationship with my personal finances.\n\nFor years, traditional budgeting apps had frustrated me. They felt so intrusive—trying to guess my spending habits, auto-categorizing things wrong, and pushing me to manage my money *their way*.\n\nManual tracking was the only thing that ever worked for me. Every few weeks, I’d sit down with spreadsheets, go over my expenses, and reflect on where I stood. It gave me control and clarity, but as life got busier, keeping up with spreadsheets became harder. I needed something faster, simpler, and still personal.\n\nSo, I built it.\n\nIn 72 hours, I had a clean, distraction-free budgeting app for people like me. It wasn’t fancy, but it worked. I threw together a quick name and logo, set up a simple licensing server, built a marketing site, and launched it at $5 to test the waters.\n\nI posted about it on Reddit. No ads. No email list. No audience. Just an idea.\n\nWhat happened next shocked me:\n\n* 50 copies sold in 50 hours.\n* People loved my decisions on simplicity, straight-forward design, and mindful workflows - points I sought to differentiate with from the start.\n* And almost everyone said the same thing: “You’re undervaluing this and can't sustain its growth.”\n\nI kept the price at $5 for a while to gather feedback and figure out what users needed most. But as I refined the app and listened to what people were saying, I realized the value was higher than I thought. I eventually raised the price to $12—still lower than err.. all of suggestions—and kept building.\n\nHere’s what I’ve learned:\n\n*Sometimes, testing your value is scarier than building the product.*  \nI was terrified to raise the price. I thought sales would stop, that people would walk away. But instead, raising the price told users I was serious about the app—and they responded. For founders, this was a huge realization: your pricing doesn’t just reflect what your product does—it reflects your confidence in it.\n\nWhat’s next:  \nI’m wrapping up a major update that includes CSV import support, a full refactor, and a ton of quality-of-life improvements. These updates will go live soon and will be free for all my users. Next on the roadmap is a Projects tab to help organize by ranging topics basically.\n\nThis app started as a way for me to reconnect with my finances, but it’s turned into something I've been proud to share with others.\n\nThis journey—from splitting ways with my last job, moving back in with my parents, and eventually creating something that’s helping dozens of people—has been a lot but I came out with such a different perspective on things.\n\nI've been thinking a lot about:\n\n* How do you balance keeping products simple while adding features users request?\n\nIf anyone has some insight to share their for a first-time founder, I'd appreciate any advice. Thanks for reading.", "author": "brody<PERSON>ie", "created_time": "2024-12-15T10:31:36", "url": "https://reddit.com/r/Entrepreneur/comments/1hepy56/dropped_everything_this_year_moved_back_in_with/", "upvotes": 0, "comments_count": 25, "sentiment": "neutral", "engagement_score": 50.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1herfj9", "title": "Waymo as Personal Vehicle", "content": "I’ve been following Way<PERSON> for the past few years but haven’t had the chance to ride in one yet. I hope that someday Waymo will be available for purchase, just like any other car. I understand that Waymo’s founders have invested a lot of money, and their business model isn't about selling personal vehicles. The ultimate goal, however, should be to have reliable autonomous vehicles available to the public. I believe the government should encourage Waymo to make this a reality. While they allow Waymo taxis to operate, despite their impact on human-driven taxis, I think the government should eventually require Waymo to make their cars available for regular consumers if they want to continue running taxis in the city. <PERSON><PERSON> can decide the pricing, but I hope this happens soon so that I can travel from Austin to San Jose overnight, sleeping in the car while <PERSON><PERSON> drives :-)", "author": "PsychologicalDesk699", "created_time": "2024-12-15T12:17:19", "url": "https://reddit.com/r/SelfDrivingCars/comments/1herfj9/waymo_as_personal_vehicle/", "upvotes": 0, "comments_count": 38, "sentiment": "bearish", "engagement_score": 76.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hew2qf", "title": "A twist on the eternal \"how to learn SEO in 2024/5\" questions - which professional certifications are worth getting as an SEO?", "content": "As in the title. What are worthwhile certs to get, such as in Google analytics.", "author": "ObjectiveBasis6978", "created_time": "2024-12-15T16:23:31", "url": "https://reddit.com/r/SEO/comments/1hew2qf/a_twist_on_the_eternal_how_to_learn_seo_in_20245/", "upvotes": 3, "comments_count": 56, "sentiment": "neutral", "engagement_score": 115.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hewibv", "title": "US city tells residents to stop sticking googly eyes on its statues", "content": "", "author": "halxp01", "created_time": "2024-12-15T16:43:33", "url": "https://reddit.com/r/nottheonion/comments/1hewibv/us_city_tells_residents_to_stop_sticking_googly/", "upvotes": 39381, "comments_count": 830, "sentiment": "neutral", "engagement_score": 41041.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hf3d7p", "title": "Devil's advocate: Reddit stock is not a buy and is bad value today", "content": "Everyone on this subreddit has been raving about Reddit stock and saying things such as \"It's good value.\" I completely disagree and I want to share some of my input which may help people on this subreddit better understand where Reddit's valuation is today.\n\nIn simple terms, Reddit's valuation is very high. So, let's do back-of-the-napkin math where I assume an ultra-bull case. See below for my assumptions:\n\n* Revenue grows 30% CAGR for the next 5 years. It reaches $4.75 billion in 2029. (Analysts expect 20-25% CAGR for next 5 years)\n* Reddit has a profit margin of 30%. (This is an ultra-bull case - Meta has 34%, Google has 23%)\n* In 2029, Reddit is a more mature company and trades at a valuation of 30 PE (Meta - 29 today, Google - 25 today)\n* I completely ignore share dilution and taxes in this valuation just to make it more bullish. (This would drop valuation by about 20-30%)\n\nSo, assuming $4.75 billion with a 30% margin, gives a margin of $1.425 billion. If we multiply this by 30 times earnings, we get a $42 billion dollar valuation. Today, Reddit is valued at $30 billion. Assuming this unrealistic ultra-bull scenario is right, you would only achieve a 7% CAGR growth on your investment for a lot of risk.\n\nSo, how exactly is this a value investment? I wouldn't even consider it a good growth investment either.", "author": "deleted", "created_time": "2024-12-15T21:50:58", "url": "https://reddit.com/r/ValueInvesting/comments/1hf3d7p/devils_advocate_reddit_stock_is_not_a_buy_and_is/", "upvotes": 310, "comments_count": 305, "sentiment": "bullish", "engagement_score": 920.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hf5k8v", "title": "Friendly reminder...Roth IRA rocks", "content": "Saying what I want to say here, since I have a hard time explaining this when others talk to me that don't know.. Maybe y'all can back me up and add any anecdotes that further my argument that... the Roth IRA simply rocks and you'd be insane not to use it if you can!\n\nWhat prompted this post... I was shocked recently to learn my parents, who should be no less than 5 years from retirement based on lifetime earnings (but unknown if they are anywhere close), have never opened a Roth IRA! Yet, they sock away extra money in a TAXABLE account to which they pay a financial advisor over 1%! In my opinion, this person \"advising\" should lose their license... \n\nTell your family, friends, neighbors, rando, open a Roth IRA! PSA!\n\n$7000 invested each year, or $583.33 a month - less than your average car payment - invested in broad market index fund will likely result in between $1.5 to $1.6 million while earning an average of 10.5% over 30 years. I feel like I often need to tell people as they don't believe me, simply Google it! The S&P 500 average over the last 100 years was 10.628%.\n\nConsider it a ~~necessity~~ bill like having a car and pay yourself first! \n\nI feel that a Roth IRA is the easiest, and most flexible option for the low to mid income household. This money grows tax free!! You can take out the principal - what you put in - any time, no penalty, if you need it under any circumstances. Unlike a 401k where that money is locked up under penalty or other less advantageous options like a 401k loan. (Edited to Add: Don't stop your 401k contributions to switch to Roth, but if you are already contributing the % needed to get your full employer match in a 401k, do the Roth IRA next).\n\nOnly 3.2% of retirees have a million or more in retirement.\n\nAnyway, add your favorite things about a Roth IRA, how you've used it to save, or how its flexibility came through for you, either way. I think it is simply the best option there is for beginner through expert investors. Tell me I'm wrong!! And hopefully I will update you soon that my folks have fired their FA and moved their money to Vanguard or Fidelity reducing their fees to about 0.4%, too!!\n\nOut.", "author": "uim_trashbag", "created_time": "2024-12-15T23:34:44", "url": "https://reddit.com/r/Fire/comments/1hf5k8v/friendly_reminderroth_ira_rocks/", "upvotes": 145, "comments_count": 109, "sentiment": "neutral", "engagement_score": 363.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}]