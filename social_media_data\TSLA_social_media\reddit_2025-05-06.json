[{"platform": "reddit", "post_id": "reddit_1kfsffb", "title": "How would you invest $1 Million?", "content": "So I recently inherited close to a million dollars, the funds are not liquid as of this moment though as they are invested in Real Estate, but due to division of assets between my family we are going to liquidate our assets and I will roughly inherit close to this amount. I’m 22 years old and want some advice by the people of this community how they would go about to making sure that they’re invested smartly. I don’t have access to the US Market, since our setup is mostly based in Dubai. Thanks everyone!\n\nEDIT : I would have another 1-1.5 Million Dollars but that’s going to stay invested in Real Estate for some time now. As those are invested in properties we actively use and I have no debt. I’ve just completed my university degree in Business Management and Marketing in London and I have monthly income of roughly $5000 as of right now.", "author": "Then_Programmer941", "created_time": "2025-05-06T01:16:15", "url": "https://reddit.com/r/Fire/comments/1kfsffb/how_would_you_invest_1_million/", "upvotes": 31, "comments_count": 58, "sentiment": "neutral", "engagement_score": 147.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfvjwb", "title": "Tesla Cybertruck inventory skyrockets to record high", "content": "Tesla’s Cybertruck inventory has skyrocketed to a new record high of more than 10,000 units. The vehicle program is in crisis.\n\nA conservative estimate would be at an average sale price of $78,000, Tesla could have almost $800 million worth of Cybertrucks.\n\nTesla has been reducing production on the cyber truck, but other headwinds for the Y and 3 also exist.   The current live total is 27676 cars. This is an increase of 15927 from last weeks 11749, an increase of 21388 from last months 6288, and an increase of 9140 from last quarters 18536,", "author": "TheAarj", "created_time": "2025-05-06T04:02:26", "url": "https://reddit.com/r/StockMarket/comments/1kfvjwb/tesla_cybertruck_inventory_skyrockets_to_record/", "upvotes": 1299, "comments_count": 125, "sentiment": "bullish", "engagement_score": 1549.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfxftq", "title": "Will Google ads survive?", "content": "Do you think PPC and Google still have a future? With ChatGPT and other LLMs now showing local businesses and even ecom results?\n\nIs this career still safe long term? I realize things evolve, and only the best stick around. People were asking the same questions even 5 years ago. But still… things feel kinda rough right now. Will this industry actually stay relevant in the long run? Genuinely curious what the experts think\n", "author": "user-agent007", "created_time": "2025-05-06T06:04:07", "url": "https://reddit.com/r/PPC/comments/1kfxftq/will_google_ads_survive/", "upvotes": 1, "comments_count": 45, "sentiment": "bullish", "engagement_score": 91.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfz0o0", "title": "Google Timeline Suddenly Restored After 2 Months (Hope for other users?)", "content": "Hey, everyone. I was affected by the Timeline bug on March 6th (?) and had my entire timeline wiped. Unfortunately, it backed up to the cloud before I could pause it, so the data was allegedly overwritten.\n\nFrom March 6th - May 6th, all of my timeline data prior to March 6th was missing, and only new visits were saving.\n\nFast forward 2 months (May 3rd) -- I factory reset my phone and used Smart Switch to restore apps. This required me to login to Google Maps, and re-enable my timeline again. I attempted to restore my backup, but the import seemed to hang and then fail. I gave up, and moved on to other things.\n\nToday (May 6th) -- I went into my cloud Timeline backups again, and there were 2 entries - one from my \"old device\" (same device, same name, it just decided that a factory reset makes the current device \"new\" again), and one from my \"new device\".\n\nI restored the backup from my \"old device\" (last backed up 2 days ago), and what do you know, not only has all of my missing timeline data has been restored (prior to March 6th), but all of the entries since March 6th have been restored as well. It seems that the data was there the whole time, and it had been concatenating entries on to the end of the \"missing\" timeline data. In other words, it didn't matter that the backup had been overwritten -- it clearly hadn't been, it was just inaccessible for some reason.\n\nAnyway, I have no idea why this fixed the issue, but I suppose it might provide a path forward for users who value their decade of Timeline data as much as I do. Good luck!\n\nEDIT: I suppose it could be related to this solution: https://www.reddit.com/r/GoogleMaps/comments/1j5bjps/timeline_deleted/mm8qn3h/ ...and the edit in this one: https://www.reddit.com/r/GoogleMaps/comments/1jgra21/google_just_emailed_me_about_timeline/\n\n...but I'm shocked that this would work a month and a half after Google distributed that email.\n\nEDIT 2: If you do manage to restore your timeline data, you can export a .json backup of your timeline by going to Android Settings > Location Services > Timeline > Export Timeline data", "author": "zkhcohen", "created_time": "2025-05-06T07:58:42", "url": "https://reddit.com/r/GoogleMaps/comments/1kfz0o0/google_timeline_suddenly_restored_after_2_months/", "upvotes": 12, "comments_count": 17, "sentiment": "neutral", "engagement_score": 46.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kfzkc6", "title": "Tesla UK Sales Plunge 62.1% in April to 512 Vehicles", "content": "", "author": "afonso_investor", "created_time": "2025-05-06T08:40:13", "url": "https://reddit.com/r/electriccars/comments/1kfzkc6/tesla_uk_sales_plunge_621_in_april_to_512_vehicles/", "upvotes": 880, "comments_count": 106, "sentiment": "neutral", "engagement_score": 1092.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg12rp", "title": "I've been out of the loop(still am now to an extent), specially on this topic, but why does everyone suddenly hate DLSS and all the other technology advancements?", "content": "The last time I actually paid attention to this discussion people were all celebrating DLSS 2 and how good it was and how much better than DLSS 1 it was and how useful it was gonna be and how much it preserved the game's visuals and boosted performance. Like it was pretty much universally loved. I haven't kept up with the discussion since about that time(idk how far back it was but DLSS 2 wasn't out yet or was barely out). \n\nBut now almost on every pc forum all this DLSS and the frame gen and other technological advancements are treated like hot garbage and like it ruins games. Now common sense to me would dictate that that technology would only get better and better as it's further refined, but people don't seem to see it that way and I see so many posts about how it's ruining gaming and how AMD is like this savior(even though it has its own version of DLSS and frame gen, with worse performance and weaker cards?)and if you buy nvidia or use any of those features you're the antichrist or some shit and you're the problem.\n\nNow I personally haven't had any experience with DLSS because I've not had a gpu that can do anything of that shit. But I've had some experience with some version of FSR and AMD's frame generation technology on a friend's laptop on god of war Ragnarok. And maybe it was just the laptop screen but none of us could make out a difference between FSR on vs off, or frame gen on vs off, or both on together vs both off. Like literally looked the same, couldn't make out any artefacting or anything, the only thing that happened was that we got more performance with those on. And again, common sense dictates nvidia would be better at both frame gen and super sampling because it's been doing it for longer. So I don't understand. Like why? Why do people hate those things. And more importantly only hate nvidia for it, I have not seen one post talking about AMD's version of either of those positively or negatively (that may just be me being out of touch). There's so much vitriol against nvidia and these enhancement technologies and i just don't get it man. \n\nTLDR: Why to people hate super sampling, frame gen etc., hate only nvidia for it even though AMD has its own versions of it, and hail AMD as some sort of champions of gamers even though their cards cost nearly the same as Nvidia's comparative cards with worse performance in those aspects(can't possibly be because they hate those technologies, because to my knowledge AMD offers those aswell just not as good and polished as Nvidia's)", "author": "JediMaster_221", "created_time": "2025-05-06T10:26:09", "url": "https://reddit.com/r/nvidia/comments/1kg12rp/ive_been_out_of_the_loopstill_am_now_to_an_extent/", "upvotes": 0, "comments_count": 79, "sentiment": "bullish", "engagement_score": 158.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg249v", "title": "It's 2025, and Google's screen-based Nest Hub Devices Still Run off 2016 Google Assistant. Seriously?", "content": "TLDR: When can users of Google 's there versions of its Nest Hub devices expect integration of Gemini?\n\nIt’s hard not to notice the gap.\n\nPixel phones have had Gemini for a while now — powerful, multimodal, context-aware AI. If I recall correctly, it first arrived on Pixel devices in late 2023.\n\nBut over in smart display land? We’re still using Google Assistant — the same version from 2016 (or what feels like the same version). I’ve been using Google Assistant since I bought the first-gen Google Nest Hub in 2018, and honestly, the experience hasn’t meaningfully changed (unless I am seriously misremembering extreme advances in Google Assistant's capabilities, but I don't think that's the case, I think it's been pretty stagnant).\n\nLet’s lay it out:\n\n- The original Nest Hub came out in 2018.\n\n- The Nest Hub Max followed in 2019 with upgraded hardware.\n\n- The 2nd gen Nest Hub launched in 2021.\n\n\nDespite that, none of these devices have received Gemini. \n\nThis isn’t a hardware limitation — Gemini was pushed to Pixel 6 and 7 series devices, which have comparable or lesser specs. So why is the Android ecosystem so fragmented?\n\nIt’s wild to think that in 2025, I am still issuing voice commands to a 9-year-old \"assistant\" that never developed mentally into even a teenager, on products that Google still sells. \n\nThere’s no upgrade path. No formal Gemini roadmap for smart displays. Just silence — or, more recently, vague promises to expand Gemini “across devices,” with no specific mention of the Nest Hub line.\n\nFor a company that claims it wants AI “everywhere,” this kind of internal inconsistency is getting harder to defend.TLDR: When can users of Google 's there versions of its Nest Hub devices expect integration of Gemini?\n\nIt’s hard not to notice the gap.\n\nPixel phones have had Gemini for a while now — powerful, multimodal, context-aware AI. If I recall correctly, it first arrived on Pixel devices in late 2023.\n\nBut over in smart display land? We’re still using Google Assistant — the same version from 2016 (or what feels like the same version). I’ve been using Google Assistant since I bought the first-gen Google Nest Hub in 2018, and honestly, the experience hasn’t meaningfully changed (unless I am seriously misremembering extreme advances in Google Assistant's capabilities, but I don't think that's the case, I think it's been pretty stagnant).\n\nLet’s lay it out:\n\n- The original Nest Hub came out in 2018.\n\n- The Nest Hub Max followed in 2019 with upgraded hardware.\n\n- The 2nd gen Nest Hub launched in 2021.\n\n\nDespite that, none of these devices have received Gemini. \n\nI have both the first and second generation devices, and had thought Gemini would have been pushed easily into at least the second generation version months ago by now.\n\nThis isn’t a hardware limitation — Gemini was pushed to Pixel 6 and 7 series devices, which have comparable or lesser specs. So why is the Android ecosystem so fragmented?\n\nIt’s wild to think that in 2025, I am still issuing voice commands to a 9-year-old \"assistant\" that never developed mentally into even a teenager, on products that Google still sells. \n\nThere’s no upgrade path. No formal Gemini roadmap for smart displays. Just silence — or, more recently, vague promises to expand Gemini “across devices,” with no specific mention of the Nest Hub line.\n\nFor a company that claims it wants AI “everywhere,” this kind of internal inconsistency is getting harder to defend.", "author": "TheLawIsSacred", "created_time": "2025-05-06T11:30:22", "url": "https://reddit.com/r/artificial/comments/1kg249v/its_2025_and_googles_screenbased_nest_hub_devices/", "upvotes": 5, "comments_count": 8, "sentiment": "bearish", "engagement_score": 21.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg2sxa", "title": "AI First, Advertisers Last: Google's new Motto", "content": "We run a PPC consulting agency with 10+  clients. Across the board, we’re seeing Google completely tank some of the most reliable, transactional queries with this AI Overview rollout. It has been gradual but we are just seeing things get worse and worse.\n\nAnd these are not just any top-of-funnel queries. These are high-intent, bottom-funnel, money-in-the-bank searches. The kind that drives SQLs and 'closed deals'. We’ve seen these keywords work across markets for years.\n\nNow suddenly, Google thinks it’s smart to hijack these SERPs with an AI-generated summary that completely misreads the intent. Half the time, the \"overview\" mentions products or companies that don't even solve the problem. Sometimes they don't even operate in the user’s country. \n\nIt’s like Google is cannibalizing its monetizable real estate and swapping it for content that wouldn't pass a junior copy test. And they are pitching PMAX knows more and let them trust with handling the acquisition. \n\nNot sure what the end game is here. If you're running lean paid funnels, this is taking LTV straight up. \n\nThere’s a real pain here that I hope ChatGPT, Perplexity, or someone else figures out how to solve!! Someone put the Advertisers First!!!", "author": "Antique-Ad-9913", "created_time": "2025-05-06T12:07:07", "url": "https://reddit.com/r/PPC/comments/1kg2sxa/ai_first_advertisers_last_googles_new_motto/", "upvotes": 63, "comments_count": 35, "sentiment": "neutral", "engagement_score": 133.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg4hdh", "title": "Tesla Sales in Germany are Down 60% Year to Date", "content": "", "author": "afonso_investor", "created_time": "2025-05-06T13:28:39", "url": "https://reddit.com/r/electriccars/comments/1kg4hdh/tesla_sales_in_germany_are_down_60_year_to_date/", "upvotes": 777, "comments_count": 75, "sentiment": "neutral", "engagement_score": 927.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg4rbf", "title": "Carbon to Clean Tracker: Repurposing Fossil Fuel Power Stations into Clean Energy Hubs", "content": "", "author": "carnegieendowment", "created_time": "2025-05-06T13:40:38", "url": "https://reddit.com/r/CleanEnergy/comments/1kg4rbf/carbon_to_clean_tracker_repurposing_fossil_fuel/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg576a", "title": "Amazon revamps pay structure to favor 'consistently high-performing' employees", "content": "", "author": "AmazonNewsBot", "created_time": "2025-05-06T14:00:04", "url": "https://reddit.com/r/amazon/comments/1kg576a/amazon_revamps_pay_structure_to_favor/", "upvotes": 224, "comments_count": 34, "sentiment": "neutral", "engagement_score": 292.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg5xe7", "title": "SEO News: ChatGPT introduces shopping features with personalized product recommendations, new AI mode outside Labs along with more features, Google confirms search signals used to train Gemini AI, beyond", "content": "Hey guys! Our team has collected the latest news from the digital world over the past week. Believe us, there is a lot of important stuff for your strategy:\n\n**GSC**\n\n* **Experts spot separate desktop and mobile data in Discover report via temporary URL tweak**\n\nSome SEO professionals recently discovered that applying Search Console’s URL filter parameters to the Discover report revealed separate performance data for desktop and mobile. This wasn't an official feature rollout, but rather a workaround that Google quickly blocked after it gained attention.\n\nStill, experts managed to extract some insights. The leaked data showed that Google has likely been testing Discover on desktop for over 16 months. One key finding: desktop click-through rates are much lower than mobile—U.S. desktop traffic made up only about 4% of mobile Discover traffic.\n\n**Source:**\n\nB<PERSON><PERSON> | LinkedIn\n\n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n\n**SERP features / Interface**\n\n* **(test) “Sponsored” labels for commercial queries in search**\n\nGoogle is trying out a new “Sponsored” label for certain search results that point to commercial content—even when no ads are involved. According to Google Ads Liaison Ginny <PERSON>, the goal is to clarify when a result leads to commercial information.\n\n**Source:**\n\n<PERSON> | Search Engine Roundtable \n\n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n\n**AI**\n\n* **Google expands AI Mode testing outside Search Labs and with new features**\n\nGoogle is now testing AI Mode beyond its Search Labs environment. The experimental experience is available to all U.S. users age 18 and over—no waitlist required.\n\nAI Mode now includes product and place cards powered by data from Google Shopping and Google Business Profiles. These cards can display:\n\n* Real-time pricing\n* Promotions\n* Ratings\n* Reviews\n* Local inventory for products and businesses\n\nA new \"History\" panel has also been added which allows users to revisit their past search queries for easier navigation.\n\n* **Google confirms use of search signals to train Gemini AI**\n\nIn recent statements, Google confirmed it uses search engine data and user behavior signals to train its Gemini AI models. Internal sources say this helps the system prioritize authoritative content and filter out low-trust pages.\n\nAdditionally, the AI Overviews feature was pretrained on search data and refined using user feedback to determine when it appears in results.\n\n**Source:**\n\nGoogle The Keyword > Products > Search\n\nGlenn Gabe | X\n\n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n\n**Documentation**\n\n* **Google refines definition of low-quality content**\n\nGoogle has updated its Search Quality Rater Guidelines to focus more heavily on content that serves the publisher over the user. Raters are now instructed to assess whether a page actually provides value to visitors or simply exists to promote the publisher's interests.\n\n**Source:**\n\nRoger Montti | Search Engine Journal \n\n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n\n**Local SEO**\n\n* **(test) AI Overviews replace review buttons in local panels**\n\nSome users have spotted Google testing a change in local business panels: clicking the \"Reviews\" button now leads to an AI-generated Overview page instead of the standard list of customer reviews.\n\n**Source:**\n\nTodd Hayes | X\n\n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n\n**E-commerce**\n\n* **Merchant Center adds “Search for products” filter tool**\n\nMerchants can now use the “Search for products” button in the Merchant Center interface. This feature allows them to quickly filter product listings by selecting from predefined queries or entering custom search terms.\n\nOnce a query is selected or typed, the system dynamically applies it as a filter, streamlining the process of locating specific products within the dashboard.\n\n* **(test) Enhanced merchant panels with shipping, returns, and payment info**\n\nGoogle is testing an updated layout for merchant knowledge panels that prominently displays shipping, return, and payment details. The new design places this information higher up in the panel and introduces a cleaner, popup-style interface.\n\n* **ChatGPT introduces shopping features with personalized product recommendations**\n\nOpenAI has rolled out new shopping features in ChatGPT, allowing users to receive personalized product recommendations directly through the chatbot. These suggestions include *product images, prices, star ratings, and direct purchase links*—all presented in a user-friendly format.\n\nUnlike traditional search engines, ChatGPT’s results are organic and not influenced by paid ads.\n\n**Source:**\n\nEmmanuel Flossie | LinkedIn\n\nSERP Alert | X\n\nOpen AI > Search > Product Discovery\n\n", "author": "SE_Ranking", "created_time": "2025-05-06T14:30:18", "url": "https://reddit.com/r/DigitalMarketing/comments/1kg5xe7/seo_news_chatgpt_introduces_shopping_features/", "upvotes": 28, "comments_count": 11, "sentiment": "bearish", "engagement_score": 50.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg674z", "title": "NJ (USA) has been exploring hydrogen-blended LNG and carbon-free biomass as part of its broader sustainability strategy.", "content": "https://preview.redd.it/avnhjhfib6ze1.jpg?width=2048&format=pjpg&auto=webp&s=307cad068273c060eecc3fe59d76965182b20a8c\n\nNJ has been exploring **hydrogen-blended LNG** and **carbon-free biomass** as part of its broader sustainability strategy. For example, **New Jersey Natural Gas (NJNG)** has launched a **green hydrogen project** that blends zero-carbon hydrogen into its existing gas distribution system. Additionally, **South Jersey Industries (SJI)** is working on renewable natural gas (RNG) projects, including converting methane from food waste into energy.\n\nHydrogen blending with LNG could help reduce emissions while maintaining reliability, and carbon-free biomass offers another pathway to decarbonization. With New Jersey aiming for **50% clean energy by 2030**, these technologies could play a significant role in achieving that goal.", "author": "Strict-Marsupial6141", "created_time": "2025-05-06T14:41:35", "url": "https://reddit.com/r/CleanEnergy/comments/1kg674z/nj_usa_has_been_exploring_hydrogenblended_lng_and/", "upvotes": 1, "comments_count": 5, "sentiment": "neutral", "engagement_score": 11.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg6ta6", "title": "Tesla's German car sales nearly halved in April", "content": "", "author": "donutloop", "created_time": "2025-05-06T15:06:50", "url": "https://reddit.com/r/Economics/comments/1kg6ta6/teslas_german_car_sales_nearly_halved_in_april/", "upvotes": 81, "comments_count": 2, "sentiment": "neutral", "engagement_score": 85.0, "source_subreddit": "Economics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kg8olp", "title": "Tesla Has A Three-Month Supply Of Unsold Cybertrucks", "content": "", "author": "1oneplus", "created_time": "2025-05-06T16:21:17", "url": "https://reddit.com/r/electriccars/comments/1kg8olp/tesla_has_a_threemonth_supply_of_unsold/", "upvotes": 510, "comments_count": 215, "sentiment": "neutral", "engagement_score": 940.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kga6ic", "title": "Google Launches “AI Max” for Search Ads", "content": "\nTL;DR: AI Max is Google’s new AI-powered boost button for Search Ads. more reach, better creative, and smarter targeting in one click.\n\nGoogle just dropped a new feature called AI Max for Search campaigns. a one-click tool for AI targeting and ad creation to campaigns.\n\nWhat it does :\n\nFinds new customers beyond your current keywords. Sounds like broad match?\n\nWrites better headlines and descriptions using AI - hard to believe. \n\nSends people to the most relevant landing page based on what they searched - nice, sound like DSA and less control\n\nAdds smart targeting like showing ads based on where people want to go, not just where they are. - Sounds interesting but won’t work for 3 years after a law suit. \n\nGives you more control like avoiding certain brands or pages - clearly a sales pitch. \n\nImproves reporting so you can see which AI assets are actually performing - seems unlikely. \n\nGoogle says:\n+14% more conversions on average, and up to +27% if you’re mostly using exact/phrase match.\n+46% conversions if you are a polar bear. \n\nRolling out globally this month in beta. See you in 2027\n", "author": "keep-the-momentum", "created_time": "2025-05-06T17:21:14", "url": "https://reddit.com/r/PPC/comments/1kga6ic/google_launches_ai_max_for_search_ads/", "upvotes": 123, "comments_count": 57, "sentiment": "bearish", "engagement_score": 237.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kgacjh", "title": "If you are in the market for a new car, do not just trade in your old car to the same dealer.", "content": "Always make sure to get a written offer, not a verbal one.\n \nI bought a new EV. I was trying to sell my ICE (16 Accord). The 1st dealer from whom I wanted to buy the EV offered 8k. I asked them to check the car while I test drove.\n \nI had already googled the basic value of my car, and I knew it should be in the range of 10 to 12k. I told the dealer that's too low and I will take 10k with the incentive deal they had (1k discount for trading in). He said no.\n \nI went to CarMax, they gave me a 10k estimate. Same for Edmund and Carvana.\n \nThe best way to get the max seemed to be the KBB. They don't buy the car outright like CarMax and Carvana. They have dealerships that they connect you with. KBB offered $12k.\n \nDealer one (Ford) just looked at the car, didn't even check under the hood, or test drive it. Gave a 12k verbal offer. I asked for a written offer, and he said we don't do that. I knew it was BS, so I just said OK and moved on.\n \nDealer two (Honda) gave me an 8k offer. I told him KBB has it at 12k, I have other offers at 11k from a dealer across from them (fake it till you make it). 15 mins later, he gave me a revised offer at 11k.\n \nDealer three (Hyundai) wouldn't check my car until I was willing to actually sell my car. I told him I can't sell it now as I have to 1st buy a car, otherwise I won't have a car to go to work. They said, buy the CSR and come back.\n \nDealer four (Jaguar) is the best dealership I have ever gone to. No wait time. The lady came by and asked me to sit at her desk. Took my keys, had someone test drive it, and check the car. 30 mins later, gave me a written offer of 11.1k, good for 7 days. Didn't upsell me or anything.\n \n<PERSON>ught my new car, went back to Jaguar, and told them I am ready to sell my ICE. They took a total of 20 minutes to sign the paperwork, and it was all done.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-06T17:27:56", "url": "https://reddit.com/r/personalfinance/comments/1kgacjh/if_you_are_in_the_market_for_a_new_car_do_not/", "upvotes": 2054, "comments_count": 187, "sentiment": "bearish", "engagement_score": 2428.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kgacn7", "title": "Tesla's share price and buoyancy is explained by longterm projections", "content": "I was curious about the longterm performance of Tesla share price vs growth projections made several years ago. Like the kind of scale a value investor might take. Particularly for the current (2025 Q1) and previous quarter which have missed even conservative short-term earnings estimates made within the quarter. Tldr: a lot hinges on the revenue performance for the year 2025.\n\nHere are the sources I used (first link is a pdf):\n- [ARK Invest Big Ideas 2021](https://research.ark-invest.com/hubfs/1_Download_Files_ARK-Invest/White_Papers/ARK–Invest_BigIdeas_2021.pdf)\n- [Tesla Investor Relations](https://ir.tesla.com/)\n- [Tesla Share Price History - Macrotrends](https://www.macrotrends.net/stocks/charts/TSLA/tesla/stock-price-history)\n- [Tesla Net Income - Macrotrends](https://www.macrotrends.net/stocks/charts/TSLA/tesla/net-income)\n\nIn real terms, the peak of Tesla's share price (on an inflation-corrected basis) aligned really well with the positive inflection point of its EV revenue. Across several quarters in 2021-2023, Tesla even exceeded several bullish projections and the share price was understandably buoyant. Earnings started to fall short of projections in 2024. A temporary dip before recovery to the proven track, or a sign of a fundamental problem with the earnings projections/business model?\n\nFrom the 2021 standpoint, the real \"break-out\" growth was projected to occur during 2025, supported by updated / next generation EVs, robotaxi, FSD, plus all the energy storage and robotics side projects. But we don't (yet) have that.\n\n| Year | ARK Conservative Revenue | ARK Bullish Revenue | Tesla Actual/Estimated Revenue | % Diff (Conservative) | % Diff (Bullish) | Avg Share Price (USD) |\n|------|--------------------------|---------------------|-------------------------------|------------------------|------------------|------------------------|\n| 2021 | $50B                     | $50B                | $53.8B                        | +7.6%                  | +7.6%            | $260.00                |\n| 2022 | $70B                     | $90B                | $81.5B                        | +16.4%                 | -9.4%            | $263.10                |\n| 2023 | $100B                    | $150B               | $96.8B                        | -3.2%                  | -35.5%           | $217.50                |\n| 2024 | $150B                    | $250B               | $100.1B (est.)               | -33.3%                 | -60.0%           | $230.60                |\n| 2025 | $250B                    | $700B               | $116.3B (est.)               | -53.5%                 | -83.4%           | $312.40                |\n\n\n| Year | ARK Conservative Profit (10%) | ARK Bullish Profit (20%) | Actual/Estimated Net Income | Avg Share Price (USD) |\n|------|-------------------------------|---------------------------|-----------------------------|------------------------|\n| 2021 | $5.0B                         | $10.0B                    | $5.5B                       | $260.00                |\n| 2022 | $7.0B                         | $18.0B                    | $12.6B                      | $263.10                |\n| 2023 | $10.0B                        | $30.0B                    | $15.0B                      | $217.50                |\n| 2024 | $15.0B                        | $50.0B                    | $6.5B (est.)                | $230.60                |\n| 2025 | $25.0B                        | $140.0B                   | $9.7B (est.)                | $312.40                |\n\nYou can see that at the scale of a year, the averaged share price is kind of following the longterm revenue projection (and of course there are loads of macro economic and fund investment effects in here). That projection has not really been modified by Tesla and I think that a lot of institutional investors can still see some potential for the take-off in profit but it is notable that we've missed EPS quite a bit now.\n\nSo what? The share price of Tesla continues to reflect old projections and is not a reflection of recent performance, nor of any kind of 'current' 5 year projection from Tesla. I think it is not in their interest to make one of those new, detailed projections right now because their share price is being helped by the old projections and blue sky/skies thinking. They continue to be able to raise capital and take cheap debt. But that may chance if 2025 is not only a miss on earnings but also shows signs of negative growth.\n\nEdit: added revenues table. Note that profits are calculated at a fixed assumed margin in that ARK pdf.", "author": "stockhounder", "created_time": "2025-05-06T17:28:03", "url": "https://reddit.com/r/ValueInvesting/comments/1kgacn7/teslas_share_price_and_buoyancy_is_explained_by/", "upvotes": 4, "comments_count": 15, "sentiment": "bullish", "engagement_score": 34.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kgdoaf", "title": "Live database of on-demand GPU pricing across the cloud market", "content": "This is a resource we put together for anyone building out cloud infrastructure for AI products that wants to cost optimize.\n\nIt's a live database of on-demand GPU instances across \\~ 20 popular clouds like Lambda Labs, Nebius, Paperspace, etc.\n\nYou can filter by GPU types like B200s, H200s, H100s, A6000s, etc., and it'll show you what everyone charges by the hour, as well as the region it's in, storage capacity, vCPUs, etc.\n\nHope this is helpful!\n\n[https://www.shadeform.ai/instances](https://www.shadeform.ai/instances)", "author": "Dylan-from-Shadeform", "created_time": "2025-05-06T19:42:07", "url": "https://reddit.com/r/cloudcomputing/comments/1kgdoaf/live_database_of_ondemand_gpu_pricing_across_the/", "upvotes": 7, "comments_count": 3, "sentiment": "neutral", "engagement_score": 13.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kgdwea", "title": "Tesla Cybertruck inventory skyrockets to record high", "content": "", "author": "p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-06T19:51:18", "url": "https://reddit.com/r/business/comments/1kgdwea/tesla_cybertruck_inventory_skyrockets_to_record/", "upvotes": 499, "comments_count": 69, "sentiment": "bullish", "engagement_score": 637.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kgep5w", "title": "<PERSON>: Putting 75% Of Your Net Worth Into A ‘Lead-Pi<PERSON>ch’", "content": "<PERSON> discussed in 2021 putting seventy five percent of his net worth into one position when you’re working with smaller sums. Here’s an excerpt from the meeting:\n\nThere have been times… well initially I had 70, several times I had 75% of my net worth in one situation.\n\nThere are situations you will see over a long period of time… I mean you will see things that it would be a mistake if you’re working with smaller sums, it would be a mistake not to have half your net worth in.\n\nI mean you really do sometimes in securities see things that are lead pipe cinches and you’re not going to see them often, and they’re not going to be talking about them on television or anything of the sort, but there will be some extraordinary things happen in a lifetime where you can put 75% of your net worth or something like that in a given situation.\n\nYou can watch the discussion here:\n\n[https://www.youtube.com/watch?time\\_continue=107&v=ZDpuhEv8D5M&embeds\\_referring\\_euri=https%3A%2F%2Facquirersmultiple.com%2F&source\\_ve\\_path=Mjg2NjY](https://www.youtube.com/watch?time_continue=107&v=ZDpuhEv8D5M&embeds_referring_euri=https%3A%2F%2Facquirersmultiple.com%2F&source_ve_path=Mjg2NjY)  \n", "author": "Long_Illustrator3439", "created_time": "2025-05-06T20:23:13", "url": "https://reddit.com/r/ValueInvesting/comments/1kgep5w/warren_buffett_putting_75_of_your_net_worth_into/", "upvotes": 465, "comments_count": 158, "sentiment": "bullish", "engagement_score": 781.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kgg2ds", "title": "Critical Analysis Requirements and AI Integration for New Jersey Offshore Wind Projects: A Path Forward", "content": "The current freeze on offshore wind projects in New Jersey demands a comprehensive approach to address stakeholder concerns and ensure project viability. This framework outlines the essential studies and documentation needed to move these crucial renewable energy initiatives forward, enhanced by cutting-edge artificial intelligence technologies.\n\nAt the forefront are Ratepayer Impact Studies, which must meticulously analyze potential changes in electricity costs. These studies need to provide transparent comparisons between wind power and traditional energy sources, while proposing concrete strategies to protect consumers from excessive rate increases. AI-powered economic modeling tools will enable more accurate cost projections and impact assessments, ensuring economic feasibility and public support.\n\nGrid reliability remains a paramount concern. Detailed assessments must demonstrate how the power grid will maintain stability during periods of low wind generation. This includes developing robust backup systems, seamless integration with existing infrastructure, and comprehensive emergency response protocols. Smart AI systems will optimize power distribution, predict wind patterns, and manage load balancing automatically, demonstrating New Jersey's commitment to building a modern, efficient clean energy infrastructure.\n\nEnvironmental protection requires particular attention through detailed impact reports. These must encompass extensive marine life studies, thorough analysis of bird migration patterns, and long-term ecosystem monitoring plans. Advanced AI systems will enhance these efforts through automated tracking, real-time ecosystem monitoring, and predictive analytics for wildlife patterns, ensuring more accurate and efficient environmental assessments while balancing renewable energy benefits with wildlife conservation.\n\nThe commercial fishing industry's concerns require dedicated attention through detailed compensation plans. This involves precise mapping of affected fishing zones, calculating economic impacts on the industry, and developing fair compensation frameworks for impacted fishermen. AI-powered mapping and economic modeling tools will provide more accurate impact assessments and fair compensation calculations, demonstrating commitment to preserving this vital economic sector while advancing clean energy goals.\n\nTourism and visual impact considerations need careful evaluation through detailed studies. Modern AI visualization tools will generate precise simulations from various coastal viewpoints, while machine learning algorithms will analyze potential tourism impacts and property value effects. These advanced tools ensure more accurate and comprehensive assessments than traditional methods, showing how offshore wind development can coexist with New Jersey's vibrant tourism industry.\n\nCrucially, New Jersey must also demonstrate its commitment to internal regulatory reform, powered by cutting-edge technology. This includes implementing AI-enhanced permitting systems that can reduce processing times from years to months, while maintaining rigorous standards. The state is developing automated application processing, smart document verification, and machine learning-based completeness checks. A centralized digital platform, supported by AI, will streamline permit submissions and track applications in real-time.\n\nThese parallel tracks - comprehensive impact studies and AI-enhanced regulatory reform - create a compelling case for unfreezing offshore wind development. All elements require rigorous peer review and stakeholder engagement, now enhanced by AI-powered communication systems and digital twin modeling for better public visualization. Success depends not just on collecting data, but on leveraging modern technology to process and present it effectively while showing concrete steps toward streamlined implementation.\n\nThe path forward requires collaboration between government agencies, energy developers, environmental groups, and local communities, all connected through advanced digital platforms. Through this thorough documentation, analysis, regulatory reform, and technological integration, New Jersey can advance its clean energy goals while addressing legitimate stakeholder concerns and demonstrating its capability to execute efficiently in the modern era.", "author": "Strict-Marsupial6141", "created_time": "2025-05-06T21:19:15", "url": "https://reddit.com/r/CleanEnergy/comments/1kgg2ds/critical_analysis_requirements_and_ai_integration/", "upvotes": 2, "comments_count": 2, "sentiment": "bullish", "engagement_score": 6.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kggavc", "title": "Proud of my results as a non expert and want to share and say thanks!", "content": "I posted about my SEO efforts about 5 months ago and I had such a warm response from everyone that I kind of wanted to provide an update. I had both shared my results and asked for advice on next steps and well... it's working. I hope this doesn't come off as bragging, I just find this community both so supportive and helpful.\n\nI run a video production company in Boston and for the first few years of our business most of our clients were recommended to us / booked via word of mouth. Over the last year I really started to take my company's SEO seriously. From writing a few blogs every week to fixing things like H1s and a whole lot in between I just started to work at it for about 2 hours every day.\n\nBack in January I was so proud because we had more traffic to our website and I was like man, maybe this will lead to booked jobs soon! WELL thanks to a lot of advice I got from you all on that post our impressions and clicks have sky rocketed. In just the last month we have gotten over 21 form submissions and booked over 10 jobs via people who found us on google.\n\nThings that I've been working on not only in the last few months, but the last year:\n\n* Writing blog posts that got attention on google\n* Building individual service pages for everything we do\n* Using Google Search Console and GA4 to figure out what was working\n* Fixing technical stuff in Squarespace\n* Learning how to write and inject structured data. To be honest, I use chatGPT to help me write this code. I then take it to Google Rich Results and test it and go back and forth with ChatGPT to make sure it's perfect.\n* Internally linking like a maniac\n* Getting every client to leave a Google review\n* Asking other video production companies around the country to take meetings with me and learn more about who they are. If we think we're a good partner to work together on something in the future we will both write a really detailed blog about one another for backlinks. I'm very careful about this and admit that backlinks are my weakest subject\n* Updating our Google Business Profile weekly with posts and photos\n\nI've attached some photos in the comments from the last 16 months of data. Now I'm working to improve my CTR but ya anyway thank you all so much for being so cool and helpful! xoxo!", "author": "Equivalent_Degree_47", "created_time": "2025-05-06T21:29:14", "url": "https://reddit.com/r/SEO/comments/1kggavc/proud_of_my_results_as_a_non_expert_and_want_to/", "upvotes": 198, "comments_count": 92, "sentiment": "neutral", "engagement_score": 382.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kggj17", "title": "Tesla in autopilot crashes into van parked in driveway, driver ticketed for careless driving", "content": "", "author": "Adventurous_Row3305", "created_time": "2025-05-06T21:38:46", "url": "https://reddit.com/r/nottheonion/comments/1kggj17/tesla_in_autopilot_crashes_into_van_parked_in/", "upvotes": 3123, "comments_count": 174, "sentiment": "bearish", "engagement_score": 3471.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "TSLA"}]