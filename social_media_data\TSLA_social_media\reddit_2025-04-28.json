[{"platform": "reddit", "post_id": "reddit_1k9jcb1", "title": "Help, I am About to Give Up", "content": "Anyone else having problems with Shopify store? I have been working on mine since November and I still keep getting failed audits when I use Screaming Frog and Plug Ins and other sources. I had the website done (so I thought) several times over, but issues keep arising. I have been using Chat GPT for 80% of it, so I am curious if this has been causing me more problems than if I just did it myself. I think before I started using Chat GPT I may not have been in this bad a predicament but than again, maybe I was worse beforehand, I am not sure because I just started doing audits. I am seriously at my breaking point and about to throw it all out the window and gorge my eyes out. I am having alot of issues with keywords, Meta descriptions, etc.", "author": "mercantilemuse", "created_time": "2025-04-28T00:58:29", "url": "https://reddit.com/r/adwords/comments/1k9jcb1/help_i_am_about_to_give_up/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9l2ww", "title": "Chrome translates Google search results even when Google Translate is turned off in the settings", "content": "I'm having this problem that has been troubling me for months now, and it's bothering me a lot. My main language is portuguese, but I often search for things in english too. However, no matter what I change in the settings, Google results keep getting automatically translated to portuguese.\n\nThis was forcing me to have to click in the \"show original\" text before clicking the result. More recently though, clicking the text still sends me to the translated page, and then I have to go back to the search results and click the result again in order to get the original page in english.\n\nI have already tried changing the settings in \"three dots > settings > language\", both messing around with the languages and disabling Google Translate. None of it made any difference. I have searched around in the flags, nothing there disables this. The issue persists even in incognito mode.\n\nDoes someone have any idea of how to possibly disable this feature?", "author": "ErasedX", "created_time": "2025-04-28T02:31:34", "url": "https://reddit.com/r/chrome/comments/1k9l2ww/chrome_translates_google_search_results_even_when/", "upvotes": 15, "comments_count": 24, "sentiment": "neutral", "engagement_score": 63.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9lo32", "title": "New York Moves to Shutter Tesla Dealerships in Blow to Musk", "content": "", "author": "HotHuckleberry8904", "created_time": "2025-04-28T03:04:05", "url": "https://reddit.com/r/politics/comments/1k9lo32/new_york_moves_to_shutter_tesla_dealerships_in/", "upvotes": 5174, "comments_count": 205, "sentiment": "neutral", "engagement_score": 5584.0, "source_subreddit": "politics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9m4dh", "title": "51 and want to FIRE but market is so wild", "content": "What do we think? I am 51, single, no kids. VHCOL city and live in a 1.4M small house and owe $390K on it at 3.4%. $1.5M 401K, $2M in stocks (dividends approx $30K per year), $200K CDs/cash. I earn $180K and have a pension too but have only been with the job for 10 years so don't even count on much from that.  I don't want to cut back much on spending at all. I like a couple $5K-10K vacations a year. ", "author": "Ok_Part_7051", "created_time": "2025-04-28T03:29:42", "url": "https://reddit.com/r/Fire/comments/1k9m4dh/51_and_want_to_fire_but_market_is_so_wild/", "upvotes": 23, "comments_count": 77, "sentiment": "neutral", "engagement_score": 177.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9n5uh", "title": "USA based businesses, how close are you to seriously struggling due to China tariffs?", "content": "Hello. I am a full time artist managing a small art business. I have one employee. About half of my merch with all my designs printed on it comes from China. I've tried finding manus in the US to no avail. I'm about two weeks away from basically being screwed as my stock runs low.  I've had highs and lows but never such an abrupt loss of revenue that's pretty much out of my control. I'm not sure what to do. Where are you guys at?", "author": "Oddarette", "created_time": "2025-04-28T04:29:39", "url": "https://reddit.com/r/smallbusiness/comments/1k9n5uh/usa_based_businesses_how_close_are_you_to/", "upvotes": 728, "comments_count": 551, "sentiment": "bearish", "engagement_score": 1830.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9qsf6", "title": "Can 1.2V NiMH Rechargeable Batteries Replace 1.5V alkaline(single use) batteries in My Device?", "content": "", "author": "101_STRANGER", "created_time": "2025-04-28T08:44:20", "url": "https://reddit.com/r/batteries/comments/1k9qsf6/can_12v_nimh_rechargeable_batteries_replace_15v/", "upvotes": 100, "comments_count": 73, "sentiment": "neutral", "engagement_score": 246.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9rkdx", "title": "Runpod how to use the same project files and environments after pausing?", "content": "Hi, all\n\nI am running my code in Runpod. I have a storage volume, and everytime I need to run my project, I'd deploy a pod from that volume. \n\nConsidering the cost, I'd pause it everytime I leave my project for longer period. \n\nHowever, everytime I restart my software, libraries are all gone. And I'd need to reinstall everything. \n\nIs there anyway I can avoid reinstalling everything and pause my project as i need? \n\nThanks!", "author": "FFFFFQQQQ", "created_time": "2025-04-28T09:42:18", "url": "https://reddit.com/r/cloudcomputing/comments/1k9rkdx/runpod_how_to_use_the_same_project_files_and/", "upvotes": 6, "comments_count": 3, "sentiment": "bullish", "engagement_score": 12.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9tqe5", "title": "Is there an online calculator with different age inputs for retirement age and social security drawing age?", "content": "Looking for a calculator that lets me input separate ages for \"when I will stop working\" and \"when I will start taking social security\". All the calculators I've found assume those are the same age?\n\nSay I have retirement savings and can stop working, then have a gap of X years and draw from savings, then start taking social security at 67 or 70 for example.\n\nEDIT: my quick and terrible reviews of some of the options\n\nboldin.com - requires signup, meh\n\nprojectionlab.com - requires signup, meh\n\nssa.tools - requires SSA sign in and copy/paste, meh\n\nmoneybee.net - somewhat limited and frustrating, can't input dollar amounts for how much I'm saving per year plus that is vague (does it include employer match or not?) plus it's only whole percentages, no apparent calculation for employer raises. Social Security inputs are frustrating. No way to have different saving percentage for myself vs. spouse. No easy way to go back and change inputs, it's a bunch of \"save and next\" separate pages. It's like the calculator is overly complex where it shouldn't be, and over simplified when it shouldn't be.\n\nRich/Broke/Dead (engaging-data.com) - this one looked good initially, but unfortunately it's based on knowing the precise amounts of everything at the instant you stop working, and knowing the exact age you will stop working. So not great for my case since I'm trying to compare what my retirement would look like depending on different stop-working ages.\n\nficalc.app - slick website but same problem as Rich/Broke/Dead, it's based on knowing your stop-working age and precise financial picture before you start using the calculator. Suppose I could use another calculator first to get those numbers, then bring them in here to get at what I want.\n\nIn the end I just made a google spreadsheet where most everything is dynamic/calculated, and all I have to do is enter what age I want to stop working. Left in some static assumptions like average annual salary increase, percent of income to contribute each year for now, SSA COLA, inflation, etc. and it works well. The output goes to a table where each row is a given future year and shows how much retirement I'll have, how much I need to draw considering inflation, SSA benefit in future dollars, etc.\n\nThanks to everyone for recommendations though!", "author": "RedditSubUser", "created_time": "2025-04-28T11:57:50", "url": "https://reddit.com/r/Fire/comments/1k9tqe5/is_there_an_online_calculator_with_different_age/", "upvotes": 11, "comments_count": 26, "sentiment": "bearish", "engagement_score": 63.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9xopa", "title": "That Big Short Scene", "content": "You know that scene in The Big Short where the housing market is collapsing? The main players who made the bet the stock market would collapse are all correct, but the market is going sideways. Nothing is happening. All the people involved who bet on the market collapsing are yelling about how corrupt the corrupt system actually is. That's what this market feels like right now.\n\nTSLA is down 71% on sales, the stock is up. China cancelled billions in Boeing planes, the stock is up. There has been no tariff deals with China or any other country, the tech market is going up. Target's main customer base are boycotting, the stock is going sideways. Walmart warning the president shelves will be empty with these tariffs in place, the stock is up.", "author": "kwalitykontrol1", "created_time": "2025-04-28T15:01:19", "url": "https://reddit.com/r/StockMarket/comments/1k9xopa/that_big_short_scene/", "upvotes": 2093, "comments_count": 448, "sentiment": "bearish", "engagement_score": 2989.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9ylbi", "title": "<PERSON><PERSON> is in worse shape than you think", "content": "", "author": "DrThomasBuro", "created_time": "2025-04-28T15:38:59", "url": "https://reddit.com/r/StockMarket/comments/1k9ylbi/tesla_is_in_worse_shape_than_you_think/", "upvotes": 1453, "comments_count": 222, "sentiment": "neutral", "engagement_score": 1897.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k9ztyf", "title": "Anyone interested in fixing cloud computing? I'm looking for co-founders with fair equity split.", "content": "I'm not sure if sharing my idea is a good move, but considering it's unlikely anyone would actually build it, I'm probably worrying for nothing. It's pretty complex anyway. Easier to find someone as committed as I am than trying to build it with random people.\n\nThe idea: cloud costs for AI-heavy apps are insane and only getting worse. The plan is to fix that with a new platform; DCaaS (Decentralized Compute as a Service). Instead of paying through the nose for centralized servers, apps could tap into \\*their\\* users' devices, cutting cloud bills by 30–80%. It’s deep tech, involves AI model sharding, chain inference, security, but should be doable, and honestly I find it exciting.", "author": "Lumpy_Signal2576", "created_time": "2025-04-28T16:28:42", "url": "https://reddit.com/r/cloudcomputing/comments/1k9ztyf/anyone_interested_in_fixing_cloud_computing_im/", "upvotes": 8, "comments_count": 28, "sentiment": "neutral", "engagement_score": 64.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ka15j2", "title": "As a $TSLA HODLer, why I’m Adding $GOOGL for Waymo & DeepMind’s AI Value", "content": "TSLA has led the auto industry’s shift into a new era, one powered by autonomy, AI, and robotics.  \nI’ve been a proud $TSLA HODLer for years, and I’m still 100% committed to <PERSON><PERSON>’s vision: Full Self-Driving (FSD), Optimus robotics, Energy scaling.. Tesla is at the center of the AI-driven industrial revolution. But when I step back and think as an investor, I realize that *revolutions aren’t won by a single player* — and sometimes, parallel technologies can also dominate in their niches.\n\nThat’s why lately, I’ve been looking beyond just $TSLA, **not to replace it**, but to **strategically complement it**. Specifically:  \n**Waymo** (under Alphabet / $GOOGL) — leading in Level 4-5 autonomous driving at scale.  \n**DeepMind** — driving breakthroughs in AI, healthcare, and systems optimization.\n\nMost people only see $GOOGL as a Search and Ads giant, but the hidden value in Waymo and DeepMind could be *explosive* as the AI economy matures. If <PERSON><PERSON> nails autonomy + robotics, and Way<PERSON> dominates robotaxis, then **holding both $TSLA and $GOOGL could mean mastering the next auto and AI revolutions from both angles.** 🚀\n\nI recently wrote a [**value investing analysis on Alphabet**](https://blog.alert-invest.com/alphabet-value-investing/) (GOOGLE) covering Waymo, DeepMind, and other Google's venture. Would love your take:\n\n* Do you see $GOOGL as a smart complement to $TSLA?\n* Or do you think Tesla will dominate so much that Waymo becomes irrelevant?", "author": "AcceptableGiraffe172", "created_time": "2025-04-28T17:22:58", "url": "https://reddit.com/r/teslainvestorsclub/comments/1ka15j2/as_a_tsla_hodler_why_im_adding_googl_for_waymo/", "upvotes": 0, "comments_count": 17, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ka59dn", "title": "OMG i just realized google deleted years of my timeline data", "content": "Is there anything I can do to retrieve this?\n\nThis isn't something I go in and check on everyday so I was completely unaware...\n\nEverything prior 2024 is gone and I'm devastated. I had so many trips in there with friends i no longer have contact with and this was a great way to get back through memory lane.  \nThis was such a valuable feature to me. \n\nI've checked all 8 of my google accounts for backups and only one had some early 2024 stuff backed up since it had to manually be turned on... \n\nApparently there was some deadline to \"save your timeline\" that was sent in an ordinary email with a deadline of December 8, 2024... seriously?! '\n\nI'm about to cry man i can't believe this \n\n", "author": "antilytics", "created_time": "2025-04-28T20:09:45", "url": "https://reddit.com/r/GoogleMaps/comments/1ka59dn/omg_i_just_realized_google_deleted_years_of_my/", "upvotes": 23, "comments_count": 40, "sentiment": "bullish", "engagement_score": 103.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ka5ddp", "title": "‘If you’re a fascist, get a Tesla’: <PERSON> targets <PERSON><PERSON> in new song", "content": "", "author": "BreakfastTop6899", "created_time": "2025-04-28T20:14:26", "url": "https://reddit.com/r/politics/comments/1ka5ddp/if_youre_a_fascist_get_a_tesla_neil_young_targets/", "upvotes": 5884, "comments_count": 163, "sentiment": "neutral", "engagement_score": 6210.0, "source_subreddit": "politics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ka5roj", "title": "<PERSON><PERSON> is in worse shape than you think", "content": "", "author": "taloSilva2005", "created_time": "2025-04-28T20:31:01", "url": "https://reddit.com/r/technology/comments/1ka5roj/tesla_is_in_worse_shape_than_you_think/", "upvotes": 5217, "comments_count": 548, "sentiment": "neutral", "engagement_score": 6313.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ka6lkz", "title": "Adobe - ADBE", "content": "ADBE\n\nMarket cap - $156 billion\n\nEnterprise value - $156 billion\n\nNet cash - $800 million\n\nTrailing PE - 24X\n\nForward PE - 17.6X\n\nForward P/FCF - 17X\n\nAdobe seems like a wonderful business at a fair price at $360-370. It trades at a 24X trailing PE, but the cash flow generation is consistently better than earnings, because of large depreciation and amortization expenses that regularly exceed capex, and deferred revenue collection from its subscription model that generates lots of float.\n\nThe business has incredible margins that just keep growing over time. They rarely raise prices, and when they do, they don't experience much churn (though they don't disclose churn metrics). They keep adding new features to the product that make it more useful and sticky. There are high switching costs now that there is a user base well trained on the Adobe system.\n\nThe ROE of the business is a whopping 50%, and operating margin has been north of 30% for many years. Operating margin was 36% in the TTM period, and FCF margins regularly exceed 40%. The business spends 18% of its revenue on R&D and less than 1% of revenue on capex. Pretty cash flow generative and very low capital requirements.\n\nThe balance sheet is probably underlevered. There is $6.1 billion of debt (offset by $7.4 billion in cash), with an average cost of debt less than 5%. After tax, the cost of debt is actually lower because of the tax shelter from interest costs. The equity is only $13 billion, but adjusted for treasury shares is around $54 billion, putting debt to equity at 11%. The company could significantly lever up to buy back shares, and might be well justified in doing so if the price goes any lower.\n\nThe company generally spends all of its free cash flow (and then some) on share buybacks, and the share count has been shrinking by over 2% per year despite the large stock-based compensation expenses.\n\nThe vast majority of revenue (74%) is from the Digital Media segment, which includes creative cloud (58% of revenue) and document cloud (15% of revenue). The other big segment is Digital Experience (25% of revenue), which includes web and mobile analytics, content analytics, and marketing analytics. It complements the creative cloud segment nicely by enhancing the communication between creative and marketing teams. Digital Experience grew from the Omniture acquisition in 2009 for $1.8 billion, and now generates over $5.3 billion in revenue per year.\n\nThe business has come under some competitive threat in recent years. Figma challenged them on UI/UX design, and Adobe tried to acquire them but the acquisition was blocked. Adobe has effectively ceded this part of the market to Figma. Canva came along with a simple web-based tool for image creation, but Adobe has been able to effectively counter with Adobe Spark, now branded as Adobe Express. I have used the tools on the phone and they are quite powerful.\n\nAdobe document cloud has come under some competitive threat from Docusign, which leads in e-signature solutions. However Adobe has a much more comprehensive solution than Docusign, with PDF editing and document prep tools beyond what Docusign offers. Adobe has also integrated Adobe Sensei, an AI tool for document analysis and editing, and Docusign does not yet have this integrated into its solutions.\n\nWall Street keeps changing its mind on whether AI generated images and video are a threat or opportunity for Adobe. I am leaning more towards opportunity. While text-to-image and text-to-video is pretty good right now, Adobe has all the tools needed for finishing touches and customization. By integrating Firefly (Adobe's AI image solution) to tools like Premier and Photoshop, you get a lot more creative control than more basic AI image and video generation tools out there on the market.\n\nManagement is pretty good. Shantanu Narayan has been CEO since 2007 (long tenure - good sign for CEOs). He led the company through the transition to cloud, and actually overdelivered on the company's goals during the transition. He also led the company through the successful acquisition of Omniture to create the complementary Digital Experience business.\n\nThe rest of senior management has shorter tenures in the current roles but there is a lot of promotion from within which I usually take as a positive sign (intimate knowledge of the lower levels of the business).\n\nIt seems to me this is a really quality business and a trailing 24X PE, forward 17.6X PE looks too cheap for the business. The PE ratio over the past 10 years has generally been in the 30-50 range.", "author": "jack<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-28T21:05:55", "url": "https://reddit.com/r/SecurityAnalysis/comments/1ka6lkz/adobe_adbe/", "upvotes": 12, "comments_count": 4, "sentiment": "bullish", "engagement_score": 20.0, "source_subreddit": "SecurityAnalysis", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ka7kld", "title": "Funded Startup CEO <PERSON><PERSON>, No Revenue, No Commercial Application Yet. I will not promote.", "content": "Is $900k ridiculous for a startup CEO salary without revenue?\n\nI invested in a biotech startup that has a bright future and has had some wins (patents pending, positive testing, etc). I recently learned the CEO is paying himself almost $1mm/year. There is a board, but they are all in the pocket of the CEO and other founder. This really rubs me wrong. Seems like WAAAY too much for a startup. They raised a big round - mid-teens millions. They are about to close another similar size. Not sure what if anything I can do, but would also just like to hear people's opinions.\n\nYes, he has ownership.\n\n\n\nUpdate: A ton of people have contacted me directly after this post.  \n\n   * Yes, I invest from time to time but no I'm not interested right now because I'm working on buying a company for myself to own/operate. \n   * My background is digital advertising. I have had 2 successful multi-million exits and one failure. \n   * I could only offer operations experience in the world of digital advertising, B2B sales, B2C marketing and the like. I know nothing about biotech, per se. \n   * The serious messages and posts have been great here and I appreciate the intelligent,  thoughtful comments provided. I have learned from them. \n   * I do consult for businesses and would do that again. That was not the goal of this post. ", "author": "prescott0330", "created_time": "2025-04-28T21:47:27", "url": "https://reddit.com/r/startups/comments/1ka7kld/funded_startup_ceo_salary_no_revenue_no/", "upvotes": 767, "comments_count": 291, "sentiment": "bullish", "engagement_score": 1349.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ka8rin", "title": "Getting 90k from Life Insurance, I need help", "content": "Hello everyone, recently my mother passed away due to complications from an infection. She has a life insurance policy. I am going to be getting about 90k from it. I need help with making a few decisions. I have layered what I am going to do with the money into 2 layers.\n\n Layer 1 is essentials I.E things that need to get paid off for my own future that are currently or will soon be effecting me. This includes about 27k in student loans and about 11k in Credit card debt. (Student loans are however in forbearance until the end of next year)\n\nLayer 2 is ideas, these are things that I am unsure about and need assistance with. This includes paying off the negative equity in my car and then either A: Keeping it and paying it off sooner due to this, B: Trade it, get a new (Used) car with a significantly lower payment but then I have to pay for gas and maintenance (My current car is electric) OR C: Sell the car and buy a older used car for < 5k\n\nEach of these options have pro's and cons, context of my current situation is that I own a Tesla that I had gotten a few years ago while they were still worth something. I had a lot of negative equity (I know, I was dumb) and that went into the loan. So I owe about 39k on a car that is only worth 17k. My current payment for it is about $740 and my insurance is about $327 (I have an old truck on the policy, though it is about 40 years old and only used once a week if that).\n\nThere is one more curve-ball here. I work in tech (System Admin) and if I have a job with my current salary is a bit fluid unfortunately. My current job is stable, but I always want to plan in case I do lose my job. Benefit of the ev for me is that I A: No/low maintenance costs and B: Gas Prices\n\nWhatever is left after all of this will be going into either an investment account or into a regular savings account as a start to a down payment for a house/condo\n\nI appreciate any help that any of you can offer, this is a bit complex and its hard for even me to wrap my head around it.\n\n", "author": "TheSpaceMan875", "created_time": "2025-04-28T22:40:07", "url": "https://reddit.com/r/FinancialPlanning/comments/1ka8rin/getting_90k_from_life_insurance_i_need_help/", "upvotes": 1, "comments_count": 3, "sentiment": "bearish", "engagement_score": 7.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}]