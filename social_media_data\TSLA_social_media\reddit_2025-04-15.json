[{"platform": "reddit", "post_id": "reddit_1jzerr9", "title": "could someone dumb it down and explain to me what the dividend % means?", "content": "say i google a company and it says it pays 7% dividends, is that 7% of the initial investiment monthly or yearly? \n\nif i spend 100$ on a stock, and it pays a 5% dividend, do i get 5$ back every year or every month?\n\nthanks in advance.", "author": "M4ldarc", "created_time": "2025-04-15T00:41:11", "url": "https://reddit.com/r/dividends/comments/1jzerr9/could_someone_dumb_it_down_and_explain_to_me_what/", "upvotes": 2, "comments_count": 19, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzhipc", "title": "How bad is this for TSLA?", "content": "Tesla is facing significant pressure to address a major shortfall in its Full Self-Driving (FSD) technology.\nApproximately 4 million vehicles equipped with the Hardware 3 (HW3) computer- installed in Teslas produced from April 2019 through late 2023-are unable to achieve the promised unsupervised autonomous driving capabilities. This revelation contradicts Tesla's earlier assurances that all vehicles produced since 2016 had \"all the hardware necessary for full self-driving capability.\"\n\nIn January 2025, CEO <PERSON><PERSON> acknowledged that HW3 lacks the necessary processing power for full autonomy. He stated that Tesla would need to upgrade the computers in vehicles of customers who purchased the FSD package. This admission has sparked discussions about potential compensation or hardware upgrades for affected owners.\n\nThe situation is further complicated by (HW4) computers. In early 2025, Tesla recalled over 200,000 vehicles due to HW4 units short-circuiting, leading to failures in safety features like rearview cameras. The company is addressing these problems through over-the-air software updates and, when necessary, hardware replacements.\n\nGiven the scale of the HW3 issue and the costs associated with potential retrofits or compensation, this could become one of the most expensive recalls in automotive history. Tesla has not yet detailed a comprehensive plan for addressing the HW3 limitations across its fleet. \n\nFor more detailed information, you can read the full article on Electrek:\n\nhttps://electrek.co/2025/04/14/tesla-tsla-replace-computer-4-million-cars-or-compensate-their-owners/", "author": "Much-Dealer3525", "created_time": "2025-04-15T03:00:20", "url": "https://reddit.com/r/stocks/comments/1jzhipc/how_bad_is_this_for_tsla/", "upvotes": 518, "comments_count": 204, "sentiment": "bearish", "engagement_score": 926.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzifew", "title": "Forget tariffs. The real war is happening in the bond market.", "content": "\n\nWhile everyone was watching headlines about chip exemptions and auto tariff “pauses,” the actual battlefront quietly shifted to something much more serious U.S. Treasuries.\n\nChina has begun selling off U.S. government bonds, and this week the yield on the 10-year surged above 4.5%. That’s not just volatility it’s a red flag. For those unfamiliar: bond yields go up when demand drops. And the 10-year is the backbone of global risk pricing.\n\nHistorically, when stocks drop, bonds rally they’re the safe haven. But not now. Stocks are falling. Bonds are falling. That’s not “normal” even Barclays titled their client note: “This is not normal.”\n\nWhy it matters?\n\n    1. China is signaling it’s done playing nice. Selling Treasuries isn’t just diversification it’s a geopolitical move.\n 2. If Europe joins the sell-off (and some signs suggest they might), this becomes more than a warning it’s a structural unraveling of confidence in U.S. fiscal stability.\n3. Every long red candle you see? That’s not panic over tariffs or Tesla’s margins that’s institutional capital quietly stepping off the table.\n\nSure, the market bounced on Friday. But don’t let that fool you these rebounds are like spasms in a body under shock. The fundamental shift is already underway. No tweet will stop it. Not even one from the king of tariffs himself.\n\nThe U.S. can’t keep applying band-aids with election-year PR while the world begins to hedge against the dollar and U.S. debt. So if you’re wondering why “good news” isn’t saving the market anymore it’s because the people who move this market have already left the room.\n\nUpdate: Yes the sell-off isn’t typical. We saw a similar move back in 2018, when Russia sharply reduced its U.S. Treasury holdings it was visible in the TIC reports with a sudden $80B drop. They used custodial accounts in Belgium, masking direct attribution at first.\n\nNow we see similar behavior: yields are rising fast without major domestic triggers, and China just halted rare earth exports a clear geopolitical signal. Add to that the drop in FX reserves and quiet USD accumulation by the PBoC this points to China likely selling Treasuries.\n\nThis isn’t just technical foreign exit is real, and it’s strategic.", "author": "AffectionateMaize523", "created_time": "2025-04-15T03:48:45", "url": "https://reddit.com/r/StockMarket/comments/1jzifew/forget_tariffs_the_real_war_is_happening_in_the/", "upvotes": 5878, "comments_count": 884, "sentiment": "bearish", "engagement_score": 7646.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzm5i9", "title": "The Dodge Charger EV Doesn’t Like It When You Slam The Hood. Here’s Why", "content": "", "author": "1oneplus", "created_time": "2025-04-15T07:47:15", "url": "https://reddit.com/r/electriccars/comments/1jzm5i9/the_dodge_charger_ev_doesnt_like_it_when_you_slam/", "upvotes": 2, "comments_count": 9, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzp9d0", "title": "[D] How to train this model with constrained resources?", "content": "So I have made a model following this [paper](https://arxiv.org/abs/2006.04768). They basically reduced the complexity of computing the attention weights. So I modified the attention mechanism accordingly. Now, the problem is that to compare the performance, they used 64 tesla v100 gpus and used the BookCorpus along with English Wiki data which accounts to over 3300M words. I don't have access to that much resources(max is kaggle).  \nI want to show that my model can show comparable performance but at lower computation complexity. I don't know how to proceed now. Please help me.  \nMy model has a typical transformer decoder architecture, similar to gpt2-small, 12 layers, 12 heads per layer. Total there are 164M parameters in my model.", "author": "ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-15T11:16:32", "url": "https://reddit.com/r/MachineLearning/comments/1jzp9d0/d_how_to_train_this_model_with_constrained/", "upvotes": 5, "comments_count": 7, "sentiment": "bullish", "engagement_score": 19.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzue7o", "title": "TIL that the more you hear a lie, the more you're likely to believe it. It's called the illusory truth effect. Some study in 1977 figured it out. Basically, if you hear something enough, your brain's like, \"Yeah, that sounds right.\"", "content": "", "author": "bin_rob", "created_time": "2025-04-15T15:13:33", "url": "https://reddit.com/r/todayilearned/comments/1jzue7o/til_that_the_more_you_hear_a_lie_the_more_youre/", "upvotes": 12971, "comments_count": 474, "sentiment": "neutral", "engagement_score": 13919.0, "source_subreddit": "todayilearned", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzuvtc", "title": "Robotics. Get in on it now. Seriously.", "content": "With the work done with Tesla Optimus, Boston Dynamics, Amazon Agility Robotics (Digit), Apptronik (Apollo), BMW's Figure AI (Figure 02), 1X Technologies (NEO), UBTECH (Walker S1), and Unitree Robotics (G1); the commercial adoption for robotics for 90% of service related industry is the future. \n\nEVERY blue collar job- landscaper, lumberjack, forester, truck driver, arborist, construction, custodial, trade skill, will be supplemented or replaced by robots.\n\nUsing the auto as a baseline, you can be out of the gate industry leader in any of the following areas:\n\n* Sales\n* Enginering/Design\n* Programing\n* Resale\n* Towing\n* Service - onsite, offsite\n* Delivery\n* Training\n\nThink of what you do now.  Who is making the most now. And start your networking, planning, and training.\n\n\n\n\n\n\n\n\n\n", "author": "KidBeene", "created_time": "2025-04-15T15:32:59", "url": "https://reddit.com/r/Entrepreneur/comments/1jzuvtc/robotics_get_in_on_it_now_seriously/", "upvotes": 7, "comments_count": 162, "sentiment": "neutral", "engagement_score": 331.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzuycv", "title": "{Repeat SEO Myth} Google Again Says Structured Data Does Not Make Your Site Rank Better", "content": "This is always mentioned in these silly SEO posts and checklists and infographics of \"everything you need to rank in Google\" that spam this sub, and other SEO, Marketing and Content subs, here and on LinkedIn and X\n\nSchema just helps Google know where data starts and ends - its a delimiter - like CSV files, like a table\n\nBut \"Schema\" doesnt make your site \"rank better\" or \"rank higher\"\n\nIt's maybe a rank signal but its NOT a rank factor\n\nIt's fine to use it for other things in [schema.org](http://schema.org), that won't cause problems, but you're unlikely to see any visible change from it in Google Search. (I know some people take the \"unlikely\" & \"visible change\" to mean they should optimize for it regardless - knock yourself out; others move faster)\n\nSo please stop posting this, please stop telling people this is why they're not ranking and lets improve our SEO standards here.\n\n[https://www.seroundtable.com/google-structured-data-ranking-39232.html](https://www.seroundtable.com/google-structured-data-ranking-39232.html)", "author": "WebLinkr", "created_time": "2025-04-15T15:35:53", "url": "https://reddit.com/r/SEO/comments/1jzuycv/repeat_seo_myth_google_again_says_structured_data/", "upvotes": 19, "comments_count": 88, "sentiment": "neutral", "engagement_score": 195.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzw0zj", "title": "AI Innovation in Nuclear Power for Clean Energy Efforts", "content": "\n**California's Diablo Canyon takes a significant step towards clean energy through AI advancements.**  The utilization of PG&E's 'Neutron Enterprise' generative AI system at Diablo Canyon aims to improve efficiency in operational processes, particularly concerning regulatory documentation. This initiative blends nuclear energy with innovative technology, presenting a forward-looking approach to operational challenges.\n\nConcerns remain regarding the implications of deploying AI in highly regulated environments, sparking discussions around responsibility and oversight in clean energy initiatives.\n\n- A major shift for the last operational nuclear plant in California.\n\n- AI aims to assist in regulatory tasks to enhance efficiency.\n\n- Reliance on AI raises safety concerns that deserve attention.\n\n- Emphasizing responsible innovation in nuclear power for a sustainable future.\n\n[(View Details on PwnHub)](https://www.reddit.com/r/pwnhub/comments/1jzvz6e/california_nuclear_plant_introduces_ai_safety/)\n        ", "author": "<PERSON>-<PERSON>", "created_time": "2025-04-15T16:19:08", "url": "https://reddit.com/r/CleanEnergy/comments/1jzw0zj/ai_innovation_in_nuclear_power_for_clean_energy/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzw66j", "title": "Anyone containerizing LLM workloads in a hybrid cloud setup? Curious how you’re handling security.", "content": "We’re running containerized AI workloads—mostly LLM inference—across a hybrid cloud setup (on-prem + AWS). Great for flexibility, but it’s surfaced some tough security and observability challenges.\n\nHere’s what we’re wrestling with:\n\n\\- Prompt injection filtering (especially via public API input)\n\n\\- Output sanitization before returning to users\n\n\\- Auth/session control across on-prem and cloud zones\n\n\\- Logging AI responses in a way that respects data sensitivity\n\nWe’ve started experimenting with a reverse proxy + AI Gateway approach to inspect, modify, and validate prompt/response traffic at the edge.\n\nAnyone else working on this? Curious how other teams are thinking about security at scale for containerized LLMs.\n\nWould love to hear what’s worked—and what hasn’t.", "author": "opsbydesign", "created_time": "2025-04-15T16:25:02", "url": "https://reddit.com/r/cloudcomputing/comments/1jzw66j/anyone_containerizing_llm_workloads_in_a_hybrid/", "upvotes": 2, "comments_count": 5, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzwsvf", "title": "Want to Learn How to Run Google Ads - Any Recommendations on a Detailed Course, Tutorials, Youtube channel, Ebook, etc.", "content": "I am getting overwhelmed with the fluffy scammed stuff that is out there. Trying to filter through what is the real deal, from where I can truly learn and immediately put into action vs theory and no actionable follow-up.\n\nI looked at the pinned list for this sub.\n\nAny feedback on relevent Current resources or recommendations for 2025 would be much appreciated!!", "author": "els1107", "created_time": "2025-04-15T16:50:24", "url": "https://reddit.com/r/adwords/comments/1jzwsvf/want_to_learn_how_to_run_google_ads_any/", "upvotes": 3, "comments_count": 8, "sentiment": "neutral", "engagement_score": 19.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzwu6n", "title": "We hired a college fresher as a front-end intern. She outperformed experienced UI/UX designers and developers combined.  \"i will not promote\"", "content": "A few months back, we were hiring for a front-end role. We received over 600 applications and shortlisted 100. Instead of diving into long interviews or sending out take-home assignments, we did something simple.   \"i will not promote\" \n\nWe shared a 5-page study doc on the basics of UX, just enough to level the playing field. Then we spent 15 minutes with each person, asking twisted conceptual questions based only on that material. That’s all it took.\n\nIt gave everyone a sort of  fair shot. And from their answers, we could immediately see who could learn fast, think deeply, and apply creatively.\n\nThe thing is, startups can’t afford to hire for knowledge. There’s a disproportionate premium on it in the market, and big companies can pay that. Most startups simply can’t.\n\nBut what we can do is bet on potential. On people who pick things up quickly, who care about what they build, and who are kind and driven enough to work well with others.\n\nWhat I really dislike is when companies give out long assignments or ask candidates to work with internal boilerplate codes and call it “assessment.” That’s not assessment, it’s disguised exploitation. You’re asking someone to work for free without hiring them. And the worst part is, the candidate can’t even say anything because the power dynamics are too skewed. One side is offering a job, the other is just hoping.\n\nThat’s why our approach worked so well.\n\nOut of 100 candidates, ten stood out. One of them was still in college. I was skeptical. Our CTO insisted. She joined as an intern.\n\nAnd she’s now outperforming people with years of experience. Not because she knew everything, but because she learned fast, executed consistently, and took feedback without ego.\n\nIt sounds like common sense, but only once you’ve lived through it.\n\nStartups should optimize for learning ability, not experience. And the smartest ones do it in ways that are humane, fair, and simple.\n\nThat’s the only hiring framework we follow, and it’s worked beautifully.\n\nCurious to know how others approach hiring in early-stage teams. What has worked for you\n\n ", "author": "KOgenie", "created_time": "2025-04-15T16:51:51", "url": "https://reddit.com/r/startups/comments/1jzwu6n/we_hired_a_college_fresher_as_a_frontend_intern/", "upvotes": 802, "comments_count": 224, "sentiment": "bullish", "engagement_score": 1250.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzxfdf", "title": "I started investing in the middle of a market crash 5 years ago. Here’s what I wish I knew.", "content": "I started investing in 2020, when the market was chaotic and uncertain. I started watch YouTube videos about investing and trying to figure it all out. What I got was that I basically needed to stay on top of every market, every news piece, frantically following all of it for hours a day. This is what I thought the experts did, and hence what would bring me success.\n\nI thought I needed as much information as possible. I went on to read every book, every finance textbook, studied all of it in university. Until I realised that while I had immense knowledge now, I was just as lost and overwhelmed, not knowing where to begin or where to go from there.\n\nMy mistake was the following: I didn't need more information, I needed a solid, timeless but simple mental model of fundamental investing principles to guide me. This would allow me to focus only on what matters, giving me 90% of the results I was looking for. This would allow me to IGNORE the noise and stay calm in any market environment.\n\nI realised that all of investing wisdom can be summed up in around 10 principles, that if followed completely will allow me to be successful, while spending a fraction of the time I thought was needed.\n\nA stock is a part of a business, if the business does well, your stock does well. Based on how well a business is doing, and how good its prospects looks, it has an intrinsic value that can be calculated simply or more complexly. If you underpay compared to that value, you will do well. If you over pay, you will not do well. Most stock moves are emotional, ignore them and be greedy when others are fearful.\n\nThese are some of those principles. Thanks to them, I now am completely calm in a chaotic market, and with clarity I see massive opportunity to make lots of money in such an uncertain time. All because I am able to be grounded in the ironclad principles, and consider nothing else. As <PERSON> <PERSON> said, the simpler it is, the better I like it. That's exactly my philosophy.\n\nWhat makes you feel calm and clear in this chaotic market?", "author": "_TheLongGame_", "created_time": "2025-04-15T17:15:23", "url": "https://reddit.com/r/investing_discussion/comments/1jzxfdf/i_started_investing_in_the_middle_of_a_market/", "upvotes": 0, "comments_count": 30, "sentiment": "bearish", "engagement_score": 60.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jzyt7m", "title": "Recent Major \"Death Crosses\" Include: Tesla [TSLA], BlackRock [BLK], Bank of America [BAC], Applied Digital [APLD], Apple [AAPL]", "content": "", "author": "GroundbreakingLynx14", "created_time": "2025-04-15T18:10:34", "url": "https://reddit.com/r/economy/comments/1jzyt7m/recent_major_death_crosses_include_tesla_tsla/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k04h6s", "title": "Tesla Stock Price Reaches ‘Death Cross’ Status", "content": "", "author": "MarvelsGrantMan136", "created_time": "2025-04-15T22:06:03", "url": "https://reddit.com/r/technology/comments/1k04h6s/tesla_stock_price_reaches_death_cross_status/", "upvotes": 42881, "comments_count": 2294, "sentiment": "neutral", "engagement_score": 47469.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k05rw4", "title": "Tesla Odometers Could Be Overestimating Mileage By As Much As 117%", "content": "\n", "author": "deleted", "created_time": "2025-04-15T23:05:19", "url": "https://reddit.com/r/electricvehicles/comments/1k05rw4/tesla_odometers_could_be_overestimating_mileage/", "upvotes": 2813, "comments_count": 524, "sentiment": "neutral", "engagement_score": 3861.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1k06dt9", "title": "Tesla accused of hacking odometers to weasel out of warranty repairs", "content": "", "author": "Doener23", "created_time": "2025-04-15T23:34:18", "url": "https://reddit.com/r/technology/comments/1k06dt9/tesla_accused_of_hacking_odometers_to_weasel_out/", "upvotes": 8571, "comments_count": 469, "sentiment": "neutral", "engagement_score": 9509.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}]