[{"platform": "reddit", "post_id": "reddit_1j4q7gb", "title": "Australia Tesla sales slump by 70 per cent in February 2025", "content": "", "author": "lokesen", "created_time": "2025-03-06T07:31:59", "url": "https://reddit.com/r/worldnews/comments/1j4q7gb/australia_tesla_sales_slump_by_70_per_cent_in/", "upvotes": 13681, "comments_count": 547, "sentiment": "neutral", "engagement_score": 14775.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j4rmxy", "title": "bZ3X: A Toyota Compact EV with Tesla Level FSD and Huge Cabin", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-06T09:21:43", "url": "https://reddit.com/r/electricvehicles/comments/1j4rmxy/bz3x_a_toyota_compact_ev_with_tesla_level_fsd_and/", "upvotes": 24, "comments_count": 48, "sentiment": "neutral", "engagement_score": 120.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j4tctg", "title": "Stop using chrome from now on", "content": "I've been using chrome for more than 10 years. Whenever I try to switch to another browser I never did it because of I have been using it for so long. But today they trigger the switch to uninstall chrome and use another browser. Bye bye chrome.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-06T11:25:25", "url": "https://reddit.com/r/chrome/comments/1j4tctg/stop_using_chrome_from_now_on/", "upvotes": 0, "comments_count": 37, "sentiment": "bullish", "engagement_score": 74.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j4ucnr", "title": "Tesla's Sales Are Falling So Much That the Cash It Rakes in for Carbon Credits Is Under Threat", "content": "", "author": "Full-Discussion3745", "created_time": "2025-03-06T12:28:31", "url": "https://reddit.com/r/Economics/comments/1j4ucnr/teslas_sales_are_falling_so_much_that_the_cash_it/", "upvotes": 3063, "comments_count": 175, "sentiment": "bearish", "engagement_score": 3413.0, "source_subreddit": "Economics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j4whcq", "title": "Oregon Man Arrested After $500K Attack on Tesla Showroom with Molotov Cocktails", "content": "", "author": "orangechen1115", "created_time": "2025-03-06T14:19:53", "url": "https://reddit.com/r/electriccars/comments/1j4whcq/oregon_man_arrested_after_500k_attack_on_tesla/", "upvotes": 660, "comments_count": 390, "sentiment": "neutral", "engagement_score": 1440.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j4xtgu", "title": "Uh, I kinda shorted <PERSON><PERSON>", "content": "Well, not really. <PERSON> and the bunch spook me. Not a real short but what I did was buy TSLQ eight days ago. Am up 25% at this time, a nice chunk of change cuz I bought a significant amount for me. A gamble, yes. Any thoughts on this purchase and when you'd bail out? It is TRADR 2x Short TSLA Daily ETF. Just curious. I'm 71 but a businessman, I take risks. But I don't like to lose, lol. I've done my share, but overall, pretty well in my day. These times are scary. Am probably going to move a lot to cash and get about 4%. But I thought I'd gamble against <PERSON>. This whole trump team is something else!", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-03-06T15:20:34", "url": "https://reddit.com/r/investing/comments/1j4xtgu/uh_i_kinda_shorted_tesla/", "upvotes": 358, "comments_count": 211, "sentiment": "bearish", "engagement_score": 780.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j5032r", "title": "Tesla now offers discounted financing on Cybertruck as the truck turns out to be a flop", "content": "", "author": "RuggedHank", "created_time": "2025-03-06T16:56:32", "url": "https://reddit.com/r/electricvehicles/comments/1j5032r/tesla_now_offers_discounted_financing_on/", "upvotes": 2055, "comments_count": 717, "sentiment": "neutral", "engagement_score": 3489.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j51nj7", "title": "What’s working in digital marketing right now?", "content": "Hey everyone!\n\nIt’s no secret that digital marketing has seen some big shifts lately, and what worked six months ago might not be as effective today. Let’s help each other out. What strategies are bringing you the best results right now? For instance:\n\nAre short-form videos still crushing it for you?  \nIs email marketing making a comeback?  \nHow are you using AI in content creation or automation?  \nWhat’s been the best ROI channel for you so far this year?\n\nWhether you’re a business owner, agency pro, or just experimenting, drop your insights!", "author": "hibuofficial", "created_time": "2025-03-06T18:00:43", "url": "https://reddit.com/r/DigitalMarketing/comments/1j51nj7/whats_working_in_digital_marketing_right_now/", "upvotes": 120, "comments_count": 63, "sentiment": "bearish", "engagement_score": 246.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j521ug", "title": "AAPL is the second most expensive Mag 7 behind TSLA.", "content": "Not one to ever care about the MAG 7 or the usual \"I like Google\" comment, but how is this even remotely rational? Naturally stocks can differ on many different aspects, so I'm already quiet on how Costco can trade at 60x Earnings on a mid-single digit growth rate, but these companies are at least all somewhat interconnected (outside of Tesla, that's just fantasy).\n\nWith today's decline, Apple and Nvidia are on basically the same P/E ratio, yet Nvidia has almost 15x higher revenue growth. Even the next two lowest growth stocks (Google and Amazon) are still over double what Apple is doing. I'm sitting here and I'm questioning as to how this is even possible.\n\nI'd love to hear any kind of plausible explanation as to how Apple can demand such an insane valuation on growth that's just marginally beating inflation.\n\nSorry for the low effort post, but I'm seriously stunned by this nonsensical market.", "author": "DylanIE_", "created_time": "2025-03-06T18:16:51", "url": "https://reddit.com/r/ValueInvesting/comments/1j521ug/aapl_is_the_second_most_expensive_mag_7_behind/", "upvotes": 103, "comments_count": 153, "sentiment": "bullish", "engagement_score": 409.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j52coe", "title": "71% of Canadians support a moratorium on Tesla sales in Canada", "content": "", "author": "Wagamaga", "created_time": "2025-03-06T18:29:27", "url": "https://reddit.com/r/technology/comments/1j52coe/71_of_canadians_support_a_moratorium_on_tesla/", "upvotes": 23216, "comments_count": 449, "sentiment": "neutral", "engagement_score": 24114.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j52roh", "title": "WARNING: Tesla [$TSLA] Now Testing One-Year Uptrend!", "content": "", "author": "GroundbreakingLynx14", "created_time": "2025-03-06T18:46:23", "url": "https://reddit.com/r/economy/comments/1j52roh/warning_tesla_tsla_now_testing_oneyear_uptrend/", "upvotes": 0, "comments_count": 0, "sentiment": "bullish", "engagement_score": 0.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j54rwc", "title": "PSA: Data Analytics job market is very poor", "content": "I don’t want to discourage people trying to make a career switch— but, right now is a terrible time for entry level data analytics jobs and tech in general. You’ll be competing with those who were laid off from prestigious big tech companies amongst many others in smaller firms. \n\nI was recently laid off in January and have 6-8 years of relevant experience in Analytics, Python, SQL, and R. It’s been a struggle… most of the jobs I’m applying for require 2-3 years of experience and I have received 2 callbacks for jobs out of 100+ applications so far. If your degree is outside of statistics, data science, computer science, or some quantitative / analytics program, (🙋‍♂️) it’s going to be difficult. \n\nI’m posting this so you can set your expectations if you struggle to find a job at first. If you’re in it for the long haul, awesome— keep your current role and apply like no other. But, I wouldn’t expect it to come easily or in the short term. \n\nA quick tip: focus on one industry and have case studies you can use relevant to that industry (insurance, healthcare, SaaS, finance, etc.). Most of these companies are looking for experience in their industry. A major part of analytics is understanding the environment you’re operating in— far beyond code and charts. \n\nThere are some other factors, too. On-site jobs are easier to get. Remote work is very competitive, and depending on what state you live in they might not consider you off of that alone. \n\nI hope this helps shed some light on the current market, I’m free to answer any questions you may have. ", "author": "Ok-Mathematician966", "created_time": "2025-03-06T20:09:58", "url": "https://reddit.com/r/analytics/comments/1j54rwc/psa_data_analytics_job_market_is_very_poor/", "upvotes": 544, "comments_count": 165, "sentiment": "neutral", "engagement_score": 874.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j55cwz", "title": "Daly BMS", "content": "Hi. I've got a little setup to run my office. The problem is these darn Daly BMSs. Their app is complete trash. I have 3 different BMSs and none of them work with the app. I have two Tesla 24v 250ah modules currently. Bat1 POS goes to growatt POS. Bat1 NEG goes to B- on Daly BMS. Daly BMS P- to growatt NEG.\nBatt2 POS goes to BATT1 POS. BATT2 NEG goes to Daly BMS B- then the P- goes to growatt NEG. Batts seem to be charging according to growatt. They will cut off in a few hours and then I have to put the negs together on BMSs to get it to turn back on. Get the inverter going and pulling 500w for the office, and it won't turn on....I put the two negs together and it stays running all day (I'm guessing)... I did, however, get rid of the BMSs temp for a day when the power was out (running an internet company and need my own to work) and it worked flawlessly. Just don't want to continue running it like that and get the batts unbalanced. I have another batt I'm going to install but I need advice. 1. What should I use for the batts BMS if Daly is so unpredictable. 2. Has anyone gotten the app to work? Mine just pops up an error every time I try to make a change. Or sometimes won't ever connect. I've used different phones...have ordered different wifi dongles. Tried to hardwire into a phone...tried to sniff packets via Bluetooth... (Big BMS won't stay on long enough to see anything). Im kinda at a loss here. Just junk the Daly and go with jk or something? What are your thoughts? Here's some pics. No judgement, just sandboxing right now", "author": "Wonderful-Cup-9398", "created_time": "2025-03-06T20:34:06", "url": "https://reddit.com/r/batteries/comments/1j55cwz/daly_bms/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j56bob", "title": "Marketing So Good You Barely Need Sales", "content": "**Positioning that slaps.** One sentence. No confusion. If people don’t get it instantly, you’ve already lost.\n\n**Be the only option.** Not the best choice—*the only choice.* People don’t compare Apple or Tesla. They just *want*them.\n\n**Price like you mean it.** Cheap = forgettable. Expensive = valuable. Perception is everything.\n\n**Forget awareness. Build obsession.** You don’t need *more* people. You need the *right* people who buy, rave, and defend you online.\n\n**Sell the story, not the product.** People don’t buy things. They buy identity. If your brand means nothing, your sales will too.\n\nDo this right and you won’t need to chase customers. They’ll chase you.", "author": "orionbixby", "created_time": "2025-03-06T21:14:45", "url": "https://reddit.com/r/marketing/comments/1j56bob/marketing_so_good_you_barely_need_sales/", "upvotes": 0, "comments_count": 18, "sentiment": "bullish", "engagement_score": 36.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j58em0", "title": "Elon Musk loyalist and board chair <PERSON><PERSON> offloads $117 million in Tesla stock as shares sink; a sign of trouble ahead?", "content": "", "author": "livinginahologram", "created_time": "2025-03-06T22:44:02", "url": "https://reddit.com/r/StockMarket/comments/1j58em0/elon_musk_loyalist_and_board_chair_rob<PERSON>_den<PERSON>/", "upvotes": 1795, "comments_count": 161, "sentiment": "neutral", "engagement_score": 2117.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j598ac", "title": "Overclocking the ASUS TUF 5080 OC = Beastly gains", "content": "TLDR: This 5080 overclocks to 3.3GHz, pushing 4090 numbers, while maintaining low temps and relatively low power draws. Overall equating to 20-30% above 4080S and nearly double the performance of my former 7900 GRE.\n\nI recently upgraded to the ASUS TUF 5080 OC edition from a 7900 GRE. I also had a quick pit stop with a 4080S but sold it for the price I paid for it and moved on a 5080 once I could get my hands on one in stock.\n\nThe gains on this card are crazy, at stock levels producing anywhere from  50% to at times double the performance of my former 7900 GRE in raster. Plus, I can finally turn on Ray Tracing and see all the expensive pixels. While RT can at times be overrated and cost unnecessary performance, now I have a card that can run it I find myself turning on Ray Tracing and Path Tracing wherever it is available, and in games like Cyberpuink it is absolutely transformative!\n\nBut that was just the beginning, because I have since been tweaking with overclocks and have found that I can get a relatively stable overclock running +375 on the core clock and +1000MHz on the memory for insane gains of between 10-15% depending on the title I am running. I have also tested pushing it as high as +450 on the core and +2000 on the memory, and while that appears to be my cards stable limit, it only nets an additional 1-2%.\n\nThe best thing about this card is that even when pushing it to its limits, seeing the core clock hit 3.3GHz, the card remains under 64c (the highest I've seen it go), the power still hovering around 300W and barely exceeding it, and the fans are no louder than what they are at any given time. Heck, my case fans actually run louder than my GPU fans as I haven't heard them spinning at all.\n\nFor all the flack the 50 series launch gets (which is understandable), these cards can be seriously impressive. Especially when overclocking to the point that in my  gaming tests, I'm achieving close or equal to 4090 numbers.\n\nThe screenshot you will see is from Steel Nomad running at +450 on the core and +2000 on the memory, and while it is not the highest score out there, it's seriously impressive considering at stock my score is about 81 FPS, resulting in a +13% gain in this benchmark, which is pretty consistent with my improvments in game v stock settings.\n\nAs noted earlier, I did also own a 4080S for a couple of weeks while I was waiting to get my hands on the 5080, and my overall performance gain v the 4080S across both games and benchmarks is more than 20%, including the fact that the 4080S I owned was OC'd to run about 10% above stock 4080S levels.\n\nI know the generational uplift wasn't there, the stock situation hasn't been great, and this launch has been plagued by drama. However, I make this post ultimately to say this, the 5080 is a great card. I am impressed by its performance, it is a beast when overclocked and I am extremely happy with my purchase.\n\nSo what about you guys, how has your experience been and what numbers have you all been able to push overclocking your 50 series cards?\n\n\\*Edit, screenshot didn't post so it is below in the comments.", "author": "Electrical_Good_4903", "created_time": "2025-03-06T23:20:12", "url": "https://reddit.com/r/nvidia/comments/1j598ac/overclocking_the_asus_tuf_5080_oc_beastly_gains/", "upvotes": 10, "comments_count": 156, "sentiment": "bearish", "engagement_score": 322.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j59tiq", "title": "Just got a pension", "content": "I just got onto a pension plan (YAY) that (according to the numbers) should equal about 45k in today's dollars per year and it will be adjusted for inflation. I have been saving but now I don't know what I'm supposed to do. I honestly never thought I'd see a pension in my line of work so I'm just gobsmacked. I have about 20 years of working left until freedom 55 and the 45k is what it would be if I retired today at 55 (added for clarity). My question is for people who have pensions, do you still save 40% of your income or no? Sorry if this  is the wrong community! I'm just looking at the possibility of retiring even earlier if I keep saving aggressively and take an earlier pension. Thoughts? Help? ", "author": "fabulousfrugalfemm", "created_time": "2025-03-06T23:47:33", "url": "https://reddit.com/r/Fire/comments/1j59tiq/just_got_a_pension/", "upvotes": 16, "comments_count": 34, "sentiment": "neutral", "engagement_score": 84.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j59tq6", "title": "Just use Ublock Origin Lite", "content": "That's it. Stop complaining about Chrome disabling the original addon every hour, the Ublock developers have been working on Ublock Origin Lite for almost 1 year now (and users were made aware of it). \n\nIt does have tree different permissions options, and even the basic one works perfectly fine. If you want to have even more control, use one of the other options, that works for 99% of people.", "author": "edu4rdshl", "created_time": "2025-03-06T23:47:49", "url": "https://reddit.com/r/chrome/comments/1j59tq6/just_use_ublock_origin_lite/", "upvotes": 0, "comments_count": 84, "sentiment": "neutral", "engagement_score": 168.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}]