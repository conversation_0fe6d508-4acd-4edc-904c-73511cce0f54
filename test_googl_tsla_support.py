#!/usr/bin/env python3
"""
测试GOOGL和TSLA股票支持的脚本
验证新闻和社交媒体配置是否正确工作
"""

import sys
import os
from datetime import datetime

# 添加src目录到路径
sys.path.append('src')

from config.news_config import NewsConfig
from config.social_media_config import SocialMediaConfig

def test_news_config():
    """测试新闻配置"""
    print("🔍 测试新闻配置...")
    
    config = NewsConfig()
    
    # 测试GOOGL和TSLA的目录映射
    test_tickers = ['GOOGL', 'TSLA']
    test_sources = ['alpha_vantage', 'newsapi', 'finnhub']
    
    for ticker in test_tickers:
        print(f"\n📊 测试 {ticker} 股票:")
        for source in test_sources:
            directory = config.get_local_data_directory(source, ticker)
            print(f"  {source}: {directory}")
            
            # 检查目录是否存在
            if directory and os.path.exists(directory):
                files = os.listdir(directory)
                file_count = len([f for f in files if f.endswith('.json')])
                print(f"    ✅ 目录存在，包含 {file_count} 个JSON文件")
            else:
                print(f"    ❌ 目录不存在或为空")

def test_social_media_config():
    """测试社交媒体配置"""
    print("\n🔍 测试社交媒体配置...")
    
    config = SocialMediaConfig()
    
    # 测试GOOGL和TSLA的目录映射
    test_tickers = ['GOOGL', 'TSLA']
    test_date = "2024-12-01"
    
    for ticker in test_tickers:
        print(f"\n📊 测试 {ticker} 股票:")
        
        # 测试Reddit数据
        reddit_path = config.get_social_media_file_path(ticker, test_date, "reddit")
        print(f"  Reddit路径: {reddit_path}")
        
        if reddit_path and os.path.exists(reddit_path):
            print(f"    ✅ Reddit文件存在")
        else:
            print(f"    ❌ Reddit文件不存在")
            
        # 检查目录中的文件数量
        base_dir = f"social_media_data/{ticker}_social_media"
        if os.path.exists(base_dir):
            files = os.listdir(base_dir)
            file_count = len([f for f in files if f.endswith('.json')])
            print(f"    📁 目录包含 {file_count} 个JSON文件")

def test_configuration_completeness():
    """测试配置完整性"""
    print("\n🔍 测试配置完整性...")
    
    # 检查所有支持的股票
    news_config = NewsConfig()
    social_config = SocialMediaConfig()
    
    expected_tickers = ['AAPL', 'MSFT', 'NVDA', 'GOOGL', 'TSLA']
    
    # 检查新闻配置
    news_stock_dirs = news_config.config.get("stock_specific_directories", {})
    print(f"📰 新闻配置支持的股票: {list(news_stock_dirs.keys())}")
    
    for ticker in expected_tickers:
        if ticker in news_stock_dirs:
            print(f"  ✅ {ticker}: 已配置")
        else:
            print(f"  ❌ {ticker}: 未配置")
    
    # 检查社交媒体配置
    social_stock_dirs = social_config.config.get("stock_specific_directories", {})
    print(f"\n📱 社交媒体配置支持的股票: {list(social_stock_dirs.keys())}")
    
    for ticker in expected_tickers:
        if ticker in social_stock_dirs:
            print(f"  ✅ {ticker}: 已配置")
        else:
            print(f"  ❌ {ticker}: 未配置")

def main():
    """主函数"""
    print("🚀 AI对冲基金系统 - GOOGL和TSLA支持测试")
    print("=" * 50)
    
    try:
        test_news_config()
        test_social_media_config()
        test_configuration_completeness()
        
        print("\n" + "=" * 50)
        print("✅ 测试完成！")
        print("\n💡 如果所有测试通过，您现在可以使用以下命令进行回测:")
        print("   python src/backtester.py --tickers GOOGL --use_local_news --use_local_social_media")
        print("   python src/backtester.py --tickers TSLA --use_local_news --use_local_social_media")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
