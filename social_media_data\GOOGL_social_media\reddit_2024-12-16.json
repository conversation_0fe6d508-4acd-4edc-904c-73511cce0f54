[{"platform": "reddit", "post_id": "reddit_1hf899v", "title": "Ex-Google CEO <PERSON> warned that when AI can self-improve, \"we seriously need to think about unplugging it.\"", "content": "", "author": "MetaKnowing", "created_time": "2024-12-16T01:49:47", "url": "https://reddit.com/r/technews/comments/1hf899v/exgoogle_ceo_er<PERSON>_schmi<PERSON>_warned_that_when_ai_can/", "upvotes": 1573, "comments_count": 182, "sentiment": "neutral", "engagement_score": 1937.0, "source_subreddit": "technews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hf8gx5", "title": "Ex-Google CEO <PERSON> warns that in 2-4 years AI may start self-improving and we should consider pulling the plug", "content": "", "author": "MetaKnowing", "created_time": "2024-12-16T02:00:55", "url": "https://reddit.com/r/singularity/comments/1hf8gx5/exgoogle_ceo_er<PERSON>_schmidt_warns_that_in_24_years/", "upvotes": 478, "comments_count": 293, "sentiment": "neutral", "engagement_score": 1064.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hf8htq", "title": "Ex-Google CEO <PERSON> warns that in 2-4 years AI may start self-improving and we should consider pulling the plug", "content": "", "author": "MetaKnowing", "created_time": "2024-12-16T02:02:07", "url": "https://reddit.com/r/artificial/comments/1hf8htq/exgoogle_ceo_er<PERSON>_schmi<PERSON>_warns_that_in_24_years/", "upvotes": 59, "comments_count": 85, "sentiment": "neutral", "engagement_score": 229.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hf9n4e", "title": "How Google turned Jaguars into self-driving taxis, but General Motors gave up", "content": "", "author": "nick7566", "created_time": "2024-12-16T03:02:30", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hf9n4e/how_google_turned_jaguars_into_selfdriving_taxis/", "upvotes": 143, "comments_count": 32, "sentiment": "neutral", "engagement_score": 207.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hffblq", "title": "I am <PERSON><PERSON>pec<PERSON>t how many days to take learn to google ads?", "content": "I am seo specilist with 3 year experience I want to learn performance marketing. Anyone  here who can suggest me how many days it will take to learn performance marketing and please also suggest any channel where I can learn performance marketing.", "author": "Real-Carpenter-7891", "created_time": "2024-12-16T09:18:20", "url": "https://reddit.com/r/SEO/comments/1hffblq/i_am_seo_specilist_how_many_days_to_take_learn_to/", "upvotes": 2, "comments_count": 25, "sentiment": "neutral", "engagement_score": 52.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hffcp5", "title": "Battery Engineers/Manufacturers: Is Machine Learning ACTUALLY used?", "content": "Hi all, I'm a battery researcher and I've noticed a lot of recent publications suggest that machine learning is effective at things like optimising formation parameters and detecting faulty batteries. \n\nBut literature is literature and reality is reality.\n\nDoes anyone working on batteries actually use ML at all for any process? And if not, why not/what DO you use?", "author": "use_excalidraw", "created_time": "2024-12-16T09:20:45", "url": "https://reddit.com/r/batteries/comments/1hffcp5/battery_engineersmanufacturers_is_machine/", "upvotes": 15, "comments_count": 10, "sentiment": "neutral", "engagement_score": 35.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfg18d", "title": "<PERSON> on why stock market valuations are scary", "content": "[https://www.youtube.com/watch?v=0fja4hu4FeU](https://www.youtube.com/watch?v=0fja4hu4FeU)", "author": "Wrighhhh", "created_time": "2024-12-16T10:13:39", "url": "https://reddit.com/r/SecurityAnalysis/comments/1hfg18d/david_giroux_on_why_stock_market_valuations_are/", "upvotes": 34, "comments_count": 0, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "SecurityAnalysis", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfg752", "title": "What are some examples of extremely financially wealthy people/celebrities who don't like to flaunt their wealth? I", "content": "I want to achieve true financial independence one day, but it seems like every wealthy person on social media who has money just acts like a pretentious asshole, for example I've seen reels of celebrities wearing ridiculous jewel-studded designer clothing just to go buy coffee, or those viral videos of rich international students driving their Lamborghinis to class. It honestly really puts me off the idea of having wealth, because I would never want to be in the company of this type of person - they honestly make me feel a bit sick lol. These aren't really the kind of role models that I want to have, even though I aspire to attain their level of wealth so that I no longer have to work for a living. I feel like most rich people buy and wear extremely expensive things just to show that they can afford it, and they also act like douchebags to people who aren't as well off. Are there any noteworthy examples of famous people who've achieved high levels of wealth but prefer to live a more down-to-earth lifestyle, and more importantly are actually good, decent human beings?", "author": "cs342", "created_time": "2024-12-16T10:26:11", "url": "https://reddit.com/r/Fire/comments/1hfg752/what_are_some_examples_of_extremely_financially/", "upvotes": 0, "comments_count": 50, "sentiment": "bullish", "engagement_score": 100.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfi4nk", "title": "Many people talk about LLMs but a lot of people sleeping on real world AI ", "content": "Having been interested in this field for many years, I’m still in awe of the progress Google and Tesla have made this year. It’s not just about self-driving cars like Waymo or Tesla’s FSD v13.2, but also advancements in robotics. The stock market seems to be catching on, with GOOGL and TSLA performing very well. I’m really looking forward to what 2025 will bring! Do you disagree, pls tell me your opinion :) ", "author": "AlbatrossHummingbird", "created_time": "2024-12-16T12:35:53", "url": "https://reddit.com/r/singularity/comments/1hfi4nk/many_people_talk_about_llms_but_a_lot_of_people/", "upvotes": 78, "comments_count": 48, "sentiment": "neutral", "engagement_score": 174.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfijue", "title": "Bogleheads conference interview with <PERSON> regarding 4% rule", "content": "Great video from the bogleheads conference regarding the 4%. With the number of posts not understanding exactly what it is or how <PERSON> came up with this, this is a must watch. \n\n[https://www.youtube.com/watch?v=vA\\_69\\_qAzeU](https://www.youtube.com/watch?v=vA_69_qAzeU)", "author": "Mre1905", "created_time": "2024-12-16T13:00:34", "url": "https://reddit.com/r/financialindependence/comments/1hfijue/bogleheads_conference_interview_with_bill_bengen/", "upvotes": 261, "comments_count": 190, "sentiment": "neutral", "engagement_score": 641.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfipzt", "title": "Can I click on my own ads that I am paying for in Google Ads to test a conversion and does this violate Google Ads policy (rather than violating Adsense policy)?", "content": "Hi. A redditor had mentioned to me that clicking my own ads to test a conversion could be a violation of Google Ads policy.  Now clicking your own ad is definitely a violation of Google Adsense policy as per their documentation for Adsense:\n\n> [Although publishers are not permitted to click on their own ads for any reason ...](https://support.google.com/adsense/answer/1348754?hl=en)\n\nand\n\n> [Please note that clicks on Google ads must result from genuine user interest, and any method that artificially generates clicks or impressions is strictly prohibited by our program policies. If we observe high levels of invalid traffic on your account, we may suspend or disable the account to protect our advertisers and users](https://support.google.com/admob/answer/3342054?hl=en) \n\nBut is clicking on your own ad that you are paying for a violation of Google Ads policy?  Of course, I am referring to Google Ads policy here and not Google Adsense policy.  I want to test a conversion and ensure the conversion is counted in Google Ads (I know that I can use Tag Assistant to debug a conversion, but testing in Tag Assistant is ultimately not counted in Google Ads). I searched for this on Google and I can't find any official documentation on whether clicking on your own ad that you are paying for in Google Ads, is a violation of Google Ads policy.\n\nIn the Google Community forums, a [Platinum Product Expert suggests clicking on your ad to test a conversion](https://support.google.com/google-ads/thread/*********/manually-clicking-on-google-ads-to-test-conversion-tracking-is-a-standard-way-to-test?hl=en) - surely, the Platinum Product Expert wouldn't be giving advice that is violating Google Ads policy that could get your account suspended/banned?\n\nSo can I click on my own Google Ads that I am paying for, to test a conversion and does this violate Google Ads policy (not Adsense policy)?", "author": "trucker-123", "created_time": "2024-12-16T13:09:40", "url": "https://reddit.com/r/adwords/comments/1hfipzt/can_i_click_on_my_own_ads_that_i_am_paying_for_in/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfjvh8", "title": "2.5 year follow-up on buying the dip on pandemic stocks", "content": "I bought the dip on several 'pandemic stocks' that had significant declines (70%+) in 2022 and started sharing public updates 1-2 times a year.\n\nIn Q3 2023 I began reallocating into AI-related stocks when I developed strong conviction. I'm also working on a self-funded AI startup, which keeps me in the loop on AI.\n\nReturns have been strong and continue to give me the runway to work on my startup and support my family after leaving my corporate tech job. There's more context in my previous updates linked below.\n\n**Previous updates:**\n\n* [1 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/147v53k/1_year_followup_on_buying_the_dip_on_pandemic/)\n* [1.5 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/17mzc07/15_year_followup_on_buying_the_dip_on_pandemic/)\n* [2 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/1d758m7/2_year_followup_on_buying_the_dip_on_pandemic/)\n\n# Progress updates\n\nBelow are the returns for this portfolio (opened in 2022) as of December 2024.\n\n# Realized (sold) in 2023\n\n* **Affirm (AFRM):** \\+18.09%\n* **Allbirds (BIRD):** \\+6.89%\n* **Coinbase (COIN):** \\+6.42%\n* **Carvana (CVNA):** \\+845.57%\n* **Meta (META):** \\+256.36%\n* **Cloudflare (NET):** \\+50.67%\n* **Netflix (NFLX):** \\+122.39%\n* **Peloton (PTON):** \\-71.51%\n* **Roblox (RBLX):** \\+25.57%\n* **Shopify (SHOP):** \\+51.27%\n* **Snapchat (SNAP):** \\-1.99%\n* **Unity (U):** \\+27.57%\n\nSee the [previous update](https://www.reddit.com/r/investing/comments/1d758m7/2_year_followup_on_buying_the_dip_on_pandemic/) for comments.\n\n# Realized (sold) in 2024\n\n* **Advanced Micro Devices (AMD):** \\-18.69% (\\*)\n* **Amazon (AMZN):** \\+97.41%\n* **Alphabet/Google (GOOGL):** \\+75.05%\n* **Apple (AAPL):** \\*****% (\\*)\n* **ARM (ARM):** \\+10.52% (\\*)\n* **ASML (ASML):** \\-7.40% (\\*)\n* **Intel (INTC):** \\-20.12% (\\*)\n* **Microsoft (MSFT):** \\*****% (\\*)\n* **Netflix (NFLX):** \\+361.74%\n* **Palantir (PLTR):** \\+65.72% (\\*)\n* **Roblox (RBLX):** \\+42.66%\n* **Shopify (SHOP):** \\+86.96%\n* **Snowflake (SNOW):** \\+51.57%\n* **Super Micro Computer (SMCI):** \\+14.48% (\\*)\n* **Taiwan Semiconductor (TSM):** \\+103.19%\n* **Tesla (TSLA):** \\+92.16% (\\*)\n* **Unity (U):** \\-8.73%\n\n(*\\*) Short term capital gain/loss held for <1yr*\n\nI held several companies (\\*) for a short time to spread out my AI bets, but some of the increasingly high P/E ratios concerned me (PLTR, TSLA, etc.).\n\nSo, I sold them and purchased more NVIDIA. This approach is considered risky, but I have strong conviction in their relative valuation, defensibility, and long-term prospects.\n\nIn this case I believe that 'diversification' among AI stocks would do more to dilute my returns than mitigate my risks. Particularly because they're all highly correlated and despite its market cap, Nvidia has the most reasonable valuation all things considered. We'll see if the bet pays off.\n\nTo clarify, I am confident in the long-term prospects of Palantir, Tesla, and others, but I'm just not comfortable with the valuations. I may reassess in the future when I have new information or valuations change.\n\nMeta is the only other stock I held onto, given my bullish view on their AI and hardware strategy combined with their attractive valuation.\n\n# Unrealized (current investments)\n\n* **Meta (META):** \\+367.97%\n* **Nvidia (NVDA):** \\+117.05%\n\nNvidia's performance here is understated since I recently increased my holdings, but it's up \\~200% since I initially purchased it last year after dithering for months.\n\nThese returns are less impactful without the values or relative weights, but I’d like to maintain some anonymity around it.\n\n# Rate of return (IRR)\n\nMy annual return (IRR) for this portfolio (Jun 2022 to Dec 2024) is **73.6%**.\n\n# Investment thesis\n\nSee a summary of my investment thesis in a [previous update](https://www.reddit.com/r/investing/comments/17mzc07/15_year_followup_on_buying_the_dip_on_pandemic/). **TL;DR:**\n\n>“…AI is another secular trend like PCs (Windows, Mac), the internet (browsers, search, social) and mobile (iOS, Android, wearables). The difference is that new technology like AI can now spread faster than ever before and get used in new ways. Every new epoch uniquely benefits from the past, potentially bending the growth curve in new ways.\n\n>The other difference is that Nvidia has a monopoly position on the core technology driving this innovation. Therefore, the \\~350% run up over the last 12 months doesn’t make NVIDIA the stock of the last year, but rather it’s the stock of the next decade. The recent 3X gain will be a blip compared to what’s coming thanks to NVIDIA’s CUDA (moat), among [other things](https://stratechery.com/2023/nvidia-on-the-mountaintop/).”\n\n>\\- [1.5 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/17mzc07/15_year_followup_on_buying_the_dip_on_pandemic/), Nov. 2023\n\n# Nvidia's dominance\n\nI'm still working through my thoughts here, but my strong conviction around Nvidia comes in part from these observations:\n\n* Nvidia is [founder-led](https://www.amazon.com/Nvidia-Way-Jensen-Huang-Making/dp/1324086718/) and focuses on accelerated computing while remaining broad enough to allow for innovation and insight across industries where it can apply its core competencies.\n* It's unprecedented to have such dominance in such a fast-growing, valuable industry while maintaining such a long-term sustainable advantage.\n* This advantage is due to unreasonable investments (research, hardware, software, ecosystem, relationships) over decades, making it hard to copy.\n* AI will consistently make software easier to create, thus reducing the moats of software companies and make them less attractive than hardware companies (for now).\n* Despite their impressive growth, Nvidia is still supply constrained, which is fundamentally easier to predict than demand constraints.\n\n# We still underestimate the AI opportunity\n\nMost importantly, we’ve barely scratched the surface of AI’s opportunities and benefits. Even the most ambitious targets underestimate it because the better and cheaper AI gets, the more use cases we'll find.\n\nWe have a habit of confusing the limits of our imagination with the limits of reality. Our imaginations are trained on what happened before, but there has never been anything like this before.\n\n# Final thoughts\n\nI'm still well within the '*maybe I'm just lucky*' phase since it's only been a couple years. Towards the end of [my last update](https://www.reddit.com/r/investing/comments/1d758m7/2_year_followup_on_buying_the_dip_on_pandemic/) I also shared a few ways my investment approach has changed, which I’m still benefiting from.\n\nI expect my next update to be the 3-year update in mid 2025.\n\n**UPDATE:** Thanks for mentioning I should have included indexes for benchmark context. Indexes like QQQ and S&P 500 also had good returns during the same period (June 2022 - Dec 2024). For an apples to apples comparison, the IRR of QQQ and S&P 500 respectively during this period were \\~24% and \\~16%.", "author": "gabe736", "created_time": "2024-12-16T14:08:16", "url": "https://reddit.com/r/investing/comments/1hfjvh8/25_year_followup_on_buying_the_dip_on_pandemic/", "upvotes": 839, "comments_count": 184, "sentiment": "bullish", "engagement_score": 1207.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfli5p", "title": "[Giveaway] Unihertz Jelly Max × r/gadgets – Win the World’s Smallest 5G Smartphone!", "content": "Unihertz is a smartphone manufacturer dedicated to creating smartphones with unique\nfeatures, such as ultra-compact sizes, QWERTY keyboards, rugged builds, massive\nbattery capacities, and even projector functions.\n\nIn a market dominated by larger smartphones, Unihertz's Jelly series has stood out for its\ncompact size. Now, Unihertz reimagines the series with the Jelly Max, perfectly\nbalancing portability and performance. As the world’s smallest 5G smartphone, Jelly Max\noffers:\n\n* 5-inch display designed to balance portability and power.\n* Dimensity 7300 5G chipset with 12GB RAM and 256GB ROM for smooth\nmultitasking and powerful performance.\n* 4000mAh battery with 66W fast charging, reaching 90% in just 20 minutes.\n* Android 14 OS for enhanced security, seamless connectivity, and user-friendly\nfeatures.\n* 100MP main camera with 3.4X optical zoom and a 32MP front camera for\nincredible photos and selfies.\n* Transparent curved design for a sleek, comfortable, and pocket-friendly grip.\n\nLearn more about the Jelly Max on the official Unihertz website:\n\nhttps://www.unihertz.com/products/jelly-max.\n\nTo celebrate the launch of this incredible smartphone, we've partnered with Unihertz to\ngive away a Jelly Max to one lucky gadget lover on Reddit!\n\n#How To Enter:\n\n#Contest 1:\n\n* Leave a top-level comment about how a small smartphone fits seamlessly\ninto your lifestyle.\n\n#Contest 2:\n\n* Navigate to https://bit.ly/UMaxGiveaway and use any of the entry methods for a\nchance to win a Jelly Max.\nYou can win either contest, so we recommend participating in both!\nRules:\n* One winner will be randomly selected from top-level comments that meet the\nentry requirement (Contest 1)\n* One top-level comment/entry per person. Duplicate entries will be removed.\n* Accounts must be 90 days old by December 15, 2024\n* Contest restricted to residents in regions where Unihertz’s official website can\nship. For more details, visit https://www.unihertz.com.\n* Entries are open until January 15th.\n* Moderators are not eligible to win.\n* Winners of the two contests must be different individuals.\n* If the winner does not meet the eligibility requirements, a new winner will be\nselected from the comments.", "author": "noeatnosleep", "created_time": "2024-12-16T15:24:26", "url": "https://reddit.com/r/gadgets/comments/1hfli5p/giveaway_unihertz_jelly_max_rgadgets_win_the/", "upvotes": 3, "comments_count": 147, "sentiment": "neutral", "engagement_score": 297.0, "source_subreddit": "gadgets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfnx67", "title": "Google about to announce Veo 2", "content": "Saw a bunch of videos on the deepmind YouTube channel pop up", "author": "TFenrir", "created_time": "2024-12-16T17:09:56", "url": "https://reddit.com/r/singularity/comments/1hfnx67/google_about_to_announce_veo_2/", "upvotes": 1199, "comments_count": 225, "sentiment": "neutral", "engagement_score": 1649.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfp2qj", "title": "Google Surprises Everyone With Veo 2: <PERSON><PERSON> Videos Outshine OpenAI's Sora", "content": "", "author": "Ethan<PERSON>ill<PERSON>s_TG", "created_time": "2024-12-16T17:58:19", "url": "https://reddit.com/r/artificial/comments/1hfp2qj/google_surprises_everyone_with_veo_2_sample/", "upvotes": 90, "comments_count": 25, "sentiment": "neutral", "engagement_score": 140.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfqdvs", "title": "Google's new Imagen 3 compared to other leading AI Image models", "content": "", "author": "Applemoi", "created_time": "2024-12-16T18:52:46", "url": "https://reddit.com/r/google/comments/1hfqdvs/googles_new_imagen_3_compared_to_other_leading_ai/", "upvotes": 571, "comments_count": 101, "sentiment": "neutral", "engagement_score": 773.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfrf7r", "title": "Google is about to Destroy OpenAI", "content": "Are you sensing that Google is about to do with OpenAI what it did with Yahoo back in 90's as second Mover company. I have strong feeling that soon google will outsmart all his competitors in GenAI, LLM arena. (I am Not talking about AGI/ASI yet)", "author": "IndependentFresh628", "created_time": "2024-12-16T19:35:32", "url": "https://reddit.com/r/singularity/comments/1hfrf7r/google_is_about_to_destroy_openai/", "upvotes": 566, "comments_count": 265, "sentiment": "bullish", "engagement_score": 1096.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfrsrx", "title": "Looking for partner [My strongest skill; sales] needed [experienced paid ads manager]. ", "content": "Hello.\n\nI am looking for a potential partner to start a SMMA.\n\nWhat I can offer is my experience selling high-ticket products in B2B and B2C. In my B2B experience, I've closed contracts after 1.5 years of negotiation and retained them for more than 3 years. In my B2C experience, I've closed $120,000 deals from cold calling and hyping customers who weren't actively looking in the market.\n\nWhat I'd do in the business is manage the sales cycle and bring in business. Part of the offer I am building requires someone to be in person with the customer, so I'll do that part as well.\n\nThe partner I need; proven experience in Google ads, Meta ads, Linkedin Ads, and Pinterest ads. Experience in sales funnels, and video editing. (I can also help with video editing, but a partner with this skill would add up too).\n\nDM me if you have;\n\nSpent +$50,000 in paid ads.\n\nProven ROI 5/10:1. \\[you've generated from $5 to $10 for every spent dollar in ads\\]\n\nOur goal is to close, deliver and maintain 200 retainers at $2,000 each. 51-49% profit sharing.", "author": "wanna_become", "created_time": "2024-12-16T19:51:42", "url": "https://reddit.com/r/adwords/comments/1hfrsrx/looking_for_partner_my_strongest_skill_sales/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfs7da", "title": "Sell Now to Lock in Profits or Hold Google for the Long Term?", "content": "Hey! I have an average price of $156 per share for Google, and I'm curious about your thoughts on the current situation. With the price now between $195 and $198, do you think it’s a good idea to sell now and lock in profits, then buy back in during the next dip, or should I hold for the long term? What strategies do you use in situations like this?", "author": "Realistic-Border-857", "created_time": "2024-12-16T20:08:39", "url": "https://reddit.com/r/ValueInvesting/comments/1hfs7da/sell_now_to_lock_in_profits_or_hold_google_for/", "upvotes": 0, "comments_count": 59, "sentiment": "bullish", "engagement_score": 118.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hft37m", "title": "Neighbors reported my business. Help? ", "content": "Hey so I run a detailing business on the side and usually my operations are mobile but in the PNW our weather gets bad this season so I recently started accepting clients at my home garage. Everything was fine until a neighbor confronted me saying that he'd report me if i didn't stop because he claimed i was being too loud and \"disrupting the neighborhood\". I didn't actually expect him to do anything and I kind of just laughed it off. Well this morning 2 cops showed up saying they'd received a formal noise complaint and I was basically ordered to stop or get fined. WTF do I do?! I can't run my business without this garage. \n\nEdit- I read my counties code laws beforehand and saw nothing about noise or running operations out of my garage. Basically the police told me I'd get fined every time they were called out. I just really don't understand how this is considered \"disturbing the peace\". \n\nEdit 2-  A lot of people in the comments are asking how I'm making so much noise and it's honestly because my air compressor and vacuum are being used pretty much constantly throughout the day. I'll also add that I live in a town home type complex so the houses are close together so as the garages. ", "author": "Big_bag_chaser", "created_time": "2024-12-16T20:46:46", "url": "https://reddit.com/r/smallbusiness/comments/1hft37m/neighbors_reported_my_business_help/", "upvotes": 122, "comments_count": 412, "sentiment": "neutral", "engagement_score": 946.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hftq4h", "title": "Google Labs just released Whisk, a new image generator that lets you input a subject, scene, and style to remix images. You can actually try it now, link in comments.", "content": "", "author": "MassiveWasabi", "created_time": "2024-12-16T21:13:13", "url": "https://reddit.com/r/singularity/comments/1hftq4h/google_labs_just_released_whisk_a_new_image/", "upvotes": 1262, "comments_count": 256, "sentiment": "neutral", "engagement_score": 1774.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfw213", "title": "Waymo - avoiding a falling skateboarder ", "content": "https://x.com/dmit<PERSON>_dolgov/status/1868778679868047545", "author": "RepresentativeCap571", "created_time": "2024-12-16T22:53:33", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hfw213/waymo_avoiding_a_falling_skateboarder/", "upvotes": 199, "comments_count": 63, "sentiment": "bearish", "engagement_score": 325.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}]