#!/usr/bin/env python3
"""
简化的网络连接测试工具
专门用于诊断Reddit连接问题
"""

import socket
import time
import requests
import os
from dotenv import load_dotenv
import praw
import prawcore

load_dotenv()

def test_dns():
    """测试DNS解析"""
    print("1. 测试DNS解析...")
    try:
        ip = socket.gethostbyname('www.reddit.com')
        print(f"   SUCCESS: www.reddit.com -> {ip}")
        return True
    except Exception as e:
        print(f"   FAILED: {e}")
        return False

def test_tcp_connection():
    """测试TCP连接"""
    print("2. 测试TCP连接...")
    hosts = [
        ('www.reddit.com', 443),
        ('oauth.reddit.com', 443),
        ('*******', 53),  # Google DNS作为对照
    ]
    
    success_count = 0
    for host, port in hosts:
        try:
            sock = socket.create_connection((host, port), timeout=15)
            sock.close()
            print(f"   SUCCESS: {host}:{port}")
            success_count += 1
        except Exception as e:
            print(f"   FAILED: {host}:{port} - {e}")
    
    return success_count > 0

def test_http_requests():
    """测试HTTP请求"""
    print("3. 测试HTTP请求...")
    urls = [
        'https://www.google.com',  # 对照测试
        'https://www.reddit.com',
        'https://oauth.reddit.com',
    ]
    
    success_count = 0
    for url in urls:
        try:
            response = requests.get(url, timeout=15)
            print(f"   SUCCESS: {url} - Status: {response.status_code}")
            success_count += 1
        except Exception as e:
            print(f"   FAILED: {url} - {e}")
    
    return success_count > 0

def test_reddit_api():
    """测试Reddit API"""
    print("4. 测试Reddit API...")
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'test-script/1.0')
    
    if not client_id or not client_secret:
        print("   FAILED: Reddit API凭据未配置")
        return False
    
    try:
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # 测试基本访问
        subreddit = reddit.subreddit('test')
        name = subreddit.display_name
        print(f"   SUCCESS: 可以访问 r/{name}")
        
        # 测试获取帖子
        try:
            posts = list(subreddit.new(limit=1))
            print(f"   SUCCESS: 可以获取帖子")
            return True
        except Exception as e:
            print(f"   PARTIAL: 可以访问子版块但无法获取帖子 - {e}")
            return True
            
    except prawcore.exceptions.ResponseException as e:
        print(f"   FAILED: Reddit API错误 - HTTP {e.response.status_code}")
        return False
    except Exception as e:
        print(f"   FAILED: {e}")
        return False

def test_with_different_settings():
    """使用不同设置测试"""
    print("5. 测试不同网络设置...")
    
    # 测试不同的超时设置
    timeouts = [5, 10, 30]
    for timeout in timeouts:
        try:
            response = requests.get('https://www.reddit.com', timeout=timeout)
            print(f"   SUCCESS: 超时{timeout}秒 - Status: {response.status_code}")
            break
        except Exception as e:
            print(f"   FAILED: 超时{timeout}秒 - {e}")
    
    # 测试是否需要代理
    print("   检查代理设置...")
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    for var in proxy_vars:
        value = os.getenv(var)
        if value:
            print(f"   发现代理设置: {var}={value}")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "="*50)
    print("可能的解决方案:")
    print("="*50)
    
    print("1. 网络连接问题:")
    print("   - 检查防火墙设置，确保允许Python访问网络")
    print("   - 检查是否需要配置代理")
    print("   - 尝试更换网络连接")
    
    print("\n2. Reddit特定问题:")
    print("   - Reddit可能对您的IP进行了临时限制")
    print("   - 尝试使用VPN或更换IP地址")
    print("   - 减少请求频率，增加延迟")
    
    print("\n3. SSL/证书问题:")
    print("   - 更新系统时间")
    print("   - 更新Python的requests库: pip install --upgrade requests")
    print("   - 更新证书: pip install --upgrade certifi")
    
    print("\n4. 临时解决方案:")
    print("   - 使用更长的超时时间")
    print("   - 添加重试机制")
    print("   - 使用不同的Reddit API端点")

def main():
    print("Reddit网络连接诊断工具")
    print("="*50)
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    results = []
    
    # 运行所有测试
    results.append(("DNS解析", test_dns()))
    results.append(("TCP连接", test_tcp_connection()))
    results.append(("HTTP请求", test_http_requests()))
    results.append(("Reddit API", test_reddit_api()))
    
    test_with_different_settings()
    
    # 总结结果
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    for test_name, success in results:
        status = "SUCCESS" if success else "FAILED"
        print(f"{test_name}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    print(f"\n成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count < len(results):
        provide_solutions()
    else:
        print("\n所有测试通过！网络连接正常。")

if __name__ == '__main__':
    main()
