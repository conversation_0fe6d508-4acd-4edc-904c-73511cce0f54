[{"platform": "reddit", "post_id": "reddit_1ki8jep", "title": "SPX options changed my life", "content": "Well,\n\nI gave advice last time and it got $#!t on. But that’s okay. Ill try and be a little more detailed and will probably get shizzed on again. Honestly just got some messages about more in depth info so I’d figure id share some more.\n\nWhat I typically do, and has worked for me for the last couple years:\n\nWait til about 9:30-10am CST (my time zone)\n\nIdentify trend\n\nIdentify support/resistance zones \n\nIdentify what SPX is respecting as far as EMA’s, and in which time frame. I usually look at 5/15min to identify patterns and ema trends, and use 1min or even 30 sec time frames for entry. (I chart on webull and buy on robinhood)\n\nHere’s the caveat: It won’t always work. I buy one contract ITM or close to ITM, 0DTE. I just try and be right more than I’m wrong. \n\nDon’t get into the market with a preplan. Look at the trend and trade with the trend. Never against it. \n\n“Stocks can’t just keep going up” are exactly how face ripping rallies happen because shorts have to cover. \n\nThis market is in unprecedented times. You can’t just buy stocks based of valuations anymore, its not 1990. Just saying. \n\nI have a set plan, max pain, or where I believe the last support is. If it fails, it fails I sell out and wait for the next opportunity. I sometimes start the morning down $1000 but after a couple trades, im up again. I don’t hold more than 5-10 minutes at the most. \n\nThis is not a race, take your time let the plays come to you. \n\nOvertime I almost have a “feel” for SPX movements and price action. I can tell low volume days and when its strong support or weak resistance based of candle movements etc. \n\nI sometimes use SPY to see different perspectives because the support/resistance zones are different, as well as EMAs. \n\nDont follow anyone else’s trades. Find what works for you, and follow your rules. \n\nAbout the dip in my account- Yeah, got caught in TSLL a little early on a big dip. I averaged down. It’s okay, obviously my account survived and im still up. Over 50% the last 4 months to be exact. \n\nNow, with that money I invest into stocks I believe in longterm. TSLL and MSTY for example. You can hate it all you want, im young and risking money is not new to me. I believe in Tesla longterm, I believe in BTC longterm. \n\nI don’t do this full time, yet. Just a blue collar guy trying to make it to the next day. \n\nAlso, stocks don’t care about politics. Leave that stuff elsewhere. lol\n", "author": "Fireman<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-09T03:04:18", "url": "https://reddit.com/r/options/comments/1ki8jep/spx_options_changed_my_life/", "upvotes": 558, "comments_count": 228, "sentiment": "bullish", "engagement_score": 1014.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ki8ma1", "title": "How is every dip getting bought instantly?", "content": "Not trying to sound crazy but lately it feels like no matter what happens, every single dip just gets eaten alive within hours. Just yesterday, Google dropped 7% within two hours, but then quickly bounced back today.\n\nI’m not trying to jump to conclusions, and I know how \"buy the dip\" works, but it still feels strange. Why do we keep seeing full reversals after negative news? And why doesn’t the market react as strongly to good news in the same way?", "author": "hulkingcylinder", "created_time": "2025-05-09T03:08:45", "url": "https://reddit.com/r/investing/comments/1ki8ma1/how_is_every_dip_getting_bought_instantly/", "upvotes": 639, "comments_count": 380, "sentiment": "neutral", "engagement_score": 1399.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kigsl5", "title": "Why is everybody on YouTube and TikTok talking about recession/depression even though stock market keeps going up and everybody I see around me spending money like it’s 1999.", "content": "I think too many people out there love the fearmonger, I mean when I’m out on the interstate third jam packed with traffic tie ups as far as the I can see everybody traveling everywhere just got back from Disney World two weeks ago every park jam packed like sardines. I mean, if people are hurting for cash. You would never know it. I think when all these people make their videos talking about recession and depression. be talking about another country. There is no way in hell they’re talking about the United States.", "author": "Academic_Plant6974", "created_time": "2025-05-09T12:03:56", "url": "https://reddit.com/r/economy/comments/1kigsl5/why_is_everybody_on_youtube_and_tik<PERSON>_talking/", "upvotes": 17, "comments_count": 145, "sentiment": "neutral", "engagement_score": 307.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kip919", "title": "Mexico sues Google over changing Gulf of Mexico’s name for US users", "content": "", "author": "Aggravating_Money992", "created_time": "2025-05-09T18:11:36", "url": "https://reddit.com/r/worldnews/comments/1kip919/mexico_sues_google_over_changing_gulf_of_mexicos/", "upvotes": 56739, "comments_count": 1122, "sentiment": "neutral", "engagement_score": 58983.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kip9gs", "title": "Mexico sues Google over changing Gulf of Mexico’s name for US users", "content": "", "author": "Aggravating_Money992", "created_time": "2025-05-09T18:12:06", "url": "https://reddit.com/r/technology/comments/1kip9gs/mexico_sues_google_over_changing_gulf_of_mexicos/", "upvotes": 37888, "comments_count": 1134, "sentiment": "neutral", "engagement_score": 40156.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kir7uv", "title": "Problema Google Tag Manager - Google Analytics", "content": "", "author": "Federal-Paramedic-73", "created_time": "2025-05-09T19:35:36", "url": "https://reddit.com/r/analytics/comments/1kir7uv/problema_google_tag_manager_google_analytics/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kirvbp", "title": "Mexico sues Google over 'Gulf of America' name change", "content": "", "author": "BreakfastTop6899", "created_time": "2025-05-09T20:03:46", "url": "https://reddit.com/r/politics/comments/1kirvbp/mexico_sues_google_over_gulf_of_america_name/", "upvotes": 507, "comments_count": 59, "sentiment": "neutral", "engagement_score": 625.0, "source_subreddit": "politics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kiufmg", "title": "What are your biggest infra pain points right now?", "content": "Hey everyone, we’re doing a quick, anonymous survey to better understand the real infrastructure struggles people face today (DevOps, deployment, scaling, reliability, etc).\n\nJust want honest input from people who actually live in this space. If you’ve got 2–3 mins, it’d mean a lot: https://docs.google.com/forms/d/e/1FAIpQLSfadPrJIYpMpH8ETJKfITGc5sd4M3E-E6tnct6hC3a9lJ0DJQ/viewform\n\nThanks in advance! ", "author": "lifewith<PERSON><PERSON>", "created_time": "2025-05-09T21:54:40", "url": "https://reddit.com/r/cloudcomputing/comments/1kiufmg/what_are_your_biggest_infra_pain_points_right_now/", "upvotes": 2, "comments_count": 1, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kiuq80", "title": "Google mislabeled my ranch as a park and we constantly have people on our property. How do I fix this?", "content": "Iv reported it through the app 100x and for a while it was fixed for a wile but someone changes it back. People are knocking over fencing to get in causing damage. People constantly pull out google to show me my backyard is a park. Do I have grounds to sue Google? ", "author": "SoundCA", "created_time": "2025-05-09T22:08:12", "url": "https://reddit.com/r/GoogleMaps/comments/1kiuq80/google_mislabeled_my_ranch_as_a_park_and_we/", "upvotes": 173, "comments_count": 41, "sentiment": "neutral", "engagement_score": 255.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kiwlgi", "title": "Mexico files lawsuit against Google over 'Gulf of America' name change", "content": "", "author": "IrishStarUS", "created_time": "2025-05-09T23:37:59", "url": "https://reddit.com/r/nottheonion/comments/1kiwlgi/mexico_files_lawsuit_against_google_over_gulf_of/", "upvotes": 3564, "comments_count": 71, "sentiment": "neutral", "engagement_score": 3706.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}]