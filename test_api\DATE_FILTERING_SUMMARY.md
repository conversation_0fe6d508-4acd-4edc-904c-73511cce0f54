# AI对冲基金系统LLM模型代理准确率分析工具 - 日期范围过滤功能

## 修改总结

本次修改为 `llm_accuracy_analyzer.py` 脚本添加了日期范围过滤功能，使其能够仅分析特定时间段的数据。

## 主要修改内容

### 1. 类初始化修改

**文件**: `llm_accuracy_analyzer.py`
**位置**: `LLMAccuracyAnalyzer.__init__()` 方法

```python
def __init__(self, base_path: str = "reasoning_logs", date_range: Optional[str] = None):
    """
    初始化分析器
    
    Args:
        base_path: reasoning_logs文件夹的路径
        date_range: 目标日期范围，格式为 "YYYYMMDD-YYYYMMDD"，如果为None则不过滤日期
    """
    self.base_path = Path(base_path)
    self.date_range = date_range  # 新增日期范围属性
    # ... 其他初始化代码
```

### 2. 新增日期范围检查方法

**新增方法**: `check_date_range()`

```python
def check_date_range(self, folder_name: str) -> bool:
    """
    检查文件夹名称中的日期范围是否符合要求
    
    Args:
        folder_name: 文件夹名称
        
    Returns:
        如果日期范围符合要求或未设置日期过滤则返回True，否则返回False
    """
    # 如果未设置日期范围过滤，则接受所有文件夹
    if not self.date_range:
        return True
    
    # 从文件夹名称中提取日期范围并与目标日期范围比较
    pattern = r'accuracy_tracking_[A-Z]+_(\d{8}-\d{8})_'
    match = re.search(pattern, folder_name)
    if match:
        folder_date_range = match.group(1)
        return folder_date_range == self.date_range
    
    # 备用模式：experiment_TICKER_DATERANGE_MODEL
    pattern2 = r'experiment_[A-Z]+_(\d{8}-\d{8})_'
    match2 = re.search(pattern2, folder_name)
    if match2:
        folder_date_range = match2.group(1)
        return folder_date_range == self.date_range
        
    return False
```

### 3. 修改数据加载逻辑

**修改位置**: `load_accuracy_data()` 方法

在遍历文件夹时添加日期范围检查：

```python
# 遍历所有子文件夹
for folder in self.base_path.iterdir():
    if not folder.is_dir():
        continue
    
    # 检查日期范围是否符合要求 (新增)
    if not self.check_date_range(folder.name):
        continue
        
    # ... 其他处理逻辑
```

### 4. 命令行参数扩展

**新增参数**:

```python
parser.add_argument('--date-range', type=str, default='20250101-20250601',
                   help='日期范围过滤，格式: YYYYMMDD-YYYYMMDD (默认: 20250101-20250601)')
parser.add_argument('--no-date-filter', action='store_true',
                   help='不进行日期范围过滤，分析所有可用数据')
```

### 5. 主函数逻辑修改

**修改位置**: `main()` 函数

```python
# 初始化分析器
date_range = None if args.no_date_filter else args.date_range
analyzer = LLMAccuracyAnalyzer(args.base_path, date_range)
```

## 功能特性

### 1. 默认行为
- 脚本默认只分析日期范围为 "20250101-20250601" 的数据文件夹
- 这确保了用户主要关注的2025年1-6月数据被优先分析

### 2. 灵活的日期过滤
- 支持自定义日期范围：`--date-range 20240102-20241231`
- 支持禁用日期过滤：`--no-date-filter`

### 3. 文件夹匹配模式
脚本能够识别以下两种文件夹命名模式：
- `accuracy_tracking_{TICKER}_{DATERANGE}_{MODEL}`
- `experiment_{TICKER}_{DATERANGE}_{MODEL}`

### 4. 向后兼容性
- 所有现有的命令行参数和功能保持不变
- 现有的脚本调用方式仍然有效

## 使用示例

### 基本用法

```bash
# 使用默认日期范围 (20250101-20250601)
python llm_accuracy_analyzer.py --ticker AAPL

# 指定特定日期范围
python llm_accuracy_analyzer.py --ticker AAPL --date-range 20240102-20241231

# 分析所有数据，不进行日期过滤
python llm_accuracy_analyzer.py --ticker AAPL --no-date-filter
```

### 高级用法

```bash
# 查看特定日期范围的可用数据
python llm_accuracy_analyzer.py --list-available --date-range 20240102-20241231

# 分析所有股票的特定日期范围数据
python llm_accuracy_analyzer.py --all-tickers --date-range 20250101-20250601
```

## 测试验证

### 测试场景

1. **默认日期过滤**: 验证默认只加载2025年1-6月数据
2. **自定义日期过滤**: 验证能正确过滤指定日期范围的数据
3. **无日期过滤**: 验证 `--no-date-filter` 参数能加载所有数据
4. **不存在日期范围**: 验证不存在的日期范围返回空结果

### 测试结果

所有测试场景均通过验证：

- ✅ 默认日期范围过滤正常工作
- ✅ 自定义日期范围过滤正常工作  
- ✅ 无日期过滤选项正常工作
- ✅ 不存在日期范围的处理正常

## 文档更新

同时更新了 `README_LLM_Accuracy_Analyzer.md` 文件，添加了：

1. 日期范围过滤功能说明
2. 新的命令行参数文档
3. 使用示例和注意事项
4. 详细的过滤规则说明

## 总结

本次修改成功为AI对冲基金系统LLM模型代理准确率分析工具添加了强大的日期范围过滤功能，使用户能够：

- 专注于特定时间段的数据分析
- 比较不同时间段的模型表现
- 灵活控制数据范围
- 保持与现有工作流程的兼容性

这个功能特别适合用户的需求，因为根据用户偏好记录，用户明确提到了对 "20250101-20250601" 日期范围的关注。
