[{"platform": "reddit", "post_id": "reddit_1is3ocv", "title": "I just raised $1.5M - I will not promote", "content": "The purpose of this post is not meant to brag, but to seriously get some feedback, insight and advice. \n\nI’ve been working on a startup for a few years, have a co-founder, now a small team (8 employees). We bootstrapped to $250k in ARR and closed a round in 4Q24 that included a few VC funds and angels. We will likely grow to $1M or $2M in ARR over the next 12mo. So very early, still figuring things out, but for the most part I’m very grateful for things. \n\nBut if I’m being honest, I have no idea what I’m doing and I constantly feel a sense of falling behind. We never have enough capital, never enough people, product is always behind, something is always breaking, i always want more revenue, and I feel as if it’s an endless cycle of figuring things out or biting more than we can chew. \n\nMeanwhile every day I see headlines and other founders at our stage acting as if they have it all figured out. As if each day is calculated, planned, and executed to perfection.\n\nDoes anyone else feel this way? Am I crazy? Is this just part of the “founder journey?”\n", "author": "Extension-Cold2635", "created_time": "2025-02-18T03:52:26", "url": "https://reddit.com/r/startups/comments/1is3ocv/i_just_raised_15m_i_will_not_promote/", "upvotes": 580, "comments_count": 165, "sentiment": "bearish", "engagement_score": 910.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1is476f", "title": "I've $100,000 cash and I'm 22", "content": "Edit to update:  I own a service based business in IL.  Business revenue is down and I will be closing in IL.  I will reopen a new or buy an existing business when I move to TX.  I have a TBI and 'minor' heart attacks due to the TBI, as well as other medical issues from TBI.  I do have a dependent that has no other family.  I do not want to burn through this money, and actually there more than $100k.  I am just trying to set my dependent up, because I will not likely live past 40 but I am planning as if I will.  I have considered building my own house too, if I could get a few acres from a county land auction (not looking for raw land).  It sounds like ETFs are the way to go.\n\nI've no idea what to do to.  I want to buy land and homestead.  I do have one dependent.  My plan so far is to buy land at a land auction.  Use it as collateral for a loan on 4-5 bedrm manufactured home.  Put everything into into a revocable PP trust, and the beneficiary would be another trust.  Create a holdings LLC, managed by the main trust, and this LLC will own an operational LLC.  Start a rothIRA and anything leftover just let sit in a mutual fund.  Any good info and ideas, or reading material would be appreciated.", "author": "Ornery-Advance-5632", "created_time": "2025-02-18T04:20:04", "url": "https://reddit.com/r/FinancialPlanning/comments/1is476f/ive_100000_cash_and_im_22/", "upvotes": 1, "comments_count": 28, "sentiment": "bullish", "engagement_score": 57.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1is7ytm", "title": "How do I prevent negative reviews on Google Business Profile?", "content": "Hey all- I run a small business in United States and currently the process to collect review is, I blast out an email to all my customers to leave a review.\n\nThis works quite well but except sometimes some angry customer leaves a 5 star review. Often times it’s something we could have easily fixed but they never talk to us. Instead they directly leave a review. What are some ways to prevent this?\n\n**Edit**: Took the advice to use a review management system to gate negative reviewed and signed up for visihero. Thanks everyone", "author": "Particular-Will1833", "created_time": "2025-02-18T08:21:53", "url": "https://reddit.com/r/smallbusiness/comments/1is7ytm/how_do_i_prevent_negative_reviews_on_google/", "upvotes": 29, "comments_count": 49, "sentiment": "bearish", "engagement_score": 127.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1isbnbj", "title": "Google Maps used to be so good...What happened?", "content": "I remember when Google Maps reached its peak performance around 2-3 years ago. Now it's a complete mess.\n\nSome key points:\n\n- \"Search along route\" feature is gone (why?)\n\n- If I look for a business/shop whatever, it will zoom out to a 500km+ range and show me businesses or shops that are extrmely far away. To find the one that is nearby, I have to zoom in again and pick it\n\n- Speed camera feature: Nice idea, but terrible implementation. The whole bottom screen is covered, until the timer to confirm that there's a speed camera is gone. \n\n- Routing feature: If I select an alternative route, it will still try to re-route me onto the initial route. Even if I actively chose to avoid tolls etc.\n\n- Language feature: I used to be able to choose English, but only for the voice guidance. Now I have to choose English for the entire app, and while using it, it will change my entire phone to English.", "author": "mark01254", "created_time": "2025-02-18T12:33:32", "url": "https://reddit.com/r/GoogleMaps/comments/1isbnbj/google_maps_used_to_be_so_goodwhat_happened/", "upvotes": 203, "comments_count": 83, "sentiment": "bullish", "engagement_score": 369.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1isdsh2", "title": "Google Starts Tracking All Your Devices As Chrome Changes", "content": "", "author": "lurker_bee", "created_time": "2025-02-18T14:23:22", "url": "https://reddit.com/r/technology/comments/1isdsh2/google_starts_tracking_all_your_devices_as_chrome/", "upvotes": 2874, "comments_count": 463, "sentiment": "neutral", "engagement_score": 3800.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1isfoo4", "title": "After 5 years in consulting, I believe AI Data Analyst will be there to end junior consultant suffering", "content": "After half a decade in data consulting, I’ve reached a conclusion: **AI could (and should) replace 90% of the grunt work I did as a junior consultant**\n\nHere’s my rant, my lessons, and what I think needs to happen next\n\nMy rant:\n\n* As junior consultants, we were essentially workhorses doing repetitive tasks like writing queries, building slides, and handling hundreds of ad hoc requests—especially before client meetings. However, with\n* We had limited domain knowledge and often guessed which data to analyze when receiving business questions. In 90% of cases, business rules were hidden in the clients' legacy queries\n* Our clients and project managers often lacked awareness of available data because they rarely examined the database or didn't have technical backgrounds\n* I spent most of my time on back-and-forth communications and rewriting similar queries with different filters or aggregate functions\n* Dashboards weren't an option unless clients were willing to invest\n* I sometimes had to take over work from other consultants who had no time for proper handovers\n\nMy lessons:\n\n* Business owners typically need simple aggregation analysis to make decisions\n* Machine learning models don't need to be complex to be effective. Simple solutions like random forests often suffice\n* A communication gap exists between business owners and junior analysts because project managers are overwhelmed managing multiple projects\n* Projects usually ended just as I was beginning to understand the industry\n\nWhat I wished for is a tool that can help me:\n\n* Break down business questions into smaller data questions\n* Store and quickly access reusable queries without writing excessive code\n* Write those simple queries for me\n* Answer ad hoc questions from business people\n* Get familiar with the situation more quickly\n* Guide me through the database schema of the client company\n\nThese are my personal observations. While there's ongoing debate about AI replacing analysts, I've simply shared my perspective based on my humble experience in the field.", "author": "jhnl_wp", "created_time": "2025-02-18T15:46:56", "url": "https://reddit.com/r/analytics/comments/1isfoo4/after_5_years_in_consulting_i_believe_ai_data/", "upvotes": 6, "comments_count": 30, "sentiment": "neutral", "engagement_score": 66.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1islh9z", "title": "Tesla Shanghai begins mass production of new Model Y \"Juniper\" ahead of schedule", "content": "", "author": "HH0R1Z0N", "created_time": "2025-02-18T19:37:43", "url": "https://reddit.com/r/electricvehicles/comments/1islh9z/tesla_shanghai_begins_mass_production_of_new/", "upvotes": 0, "comments_count": 27, "sentiment": "neutral", "engagement_score": 54.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1islj2p", "title": "<PERSON><PERSON>lio Advice.. 29yo and married", "content": "Hey everyone, love this sub. I been working on rebalancing my portfolio and would love some thoughts - I primarily trimmed down my single stock picks into ETFs (TSLA and PLTR). \n\nI’m 29 and my wife is 27 - we have a NW of about 800K across brokerage and retirement accounts. \n\nOur breakdown is as follows across that 800K: \n\n- Vanguard ETFs (VOO/VGT): 567K (70%)\n- TSLA: 100K (12.5%) \n- SOFI: 48K (6%)\n- PLTR: 35K (4.3%)\n- PYPL: 16K (2%)\n\n- Cash + Gold: 30K\n\nI recently trimmed down about half of both my TSLA and PLTR positions, and moved them into VOO/VGT. Feeling a lot better/safer with that decision, but would love some thoughts on the current allocation. I know TSLA is pretty hated on Reddit, but I do believe in their FSD, energy, and, cybercab for the next few years. Moving forward, my contributions are going to be primarily towards building that Vanguard ETFs fund.\n\nWould appreciate any advice on the risk/reward balance in my port! ", "author": "Top-Travel7425", "created_time": "2025-02-18T19:39:48", "url": "https://reddit.com/r/Fire/comments/1islj2p/portfolio_advice_29yo_and_married/", "upvotes": 0, "comments_count": 3, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ismyes", "title": "My thoughts after owning both a ‘23 MY and ‘23 Ioniq 5", "content": "I owned a ‘23 Ioniq 5 SEL and traded it in to get a ‘23 MY from Carvana. I liked the Ioniq 5 but there were some things that I just couldn’t get past. The top thing was seat ergonomics. I could not see the speedometer if the seat and steering wheel were at a comfortable position. My job takes a toll on my body already so that combined with pretty bad discomfort after long drives made me look to trade in the Hyundai. Initially I chose the Ioniq5 because of reports of Tesla’s phantom braking. After about 5 months driving the MY I haven’t experienced phantom breaking once. I thought I’d share my perspective for anyone debating between the cars. \n\nThings I like more about the Tesla:\n- Speedometer is not blocked by wheel. This makes the car much more comfortable for me. \n- Full Self Driving (Supervised) is amazing (I purchased a used Model Y that had enhanced auto pilot so it cost me 4k to upgrade to FSD and it’s worth every penny). Tesla’s driver assistance system is just much better than Hyundai’s—. 1) it drives for you without you needing to put hands on the wheel in the vast majority of situations 2) there is cabin attention monitoring so hands don’t need to be on wheel\n- Dog mode \n- auto lock when you walk away (why does the Ioniq not have this???) \n- Memory seat and mirror positions\n- Auto dimming side mirrors\n- More storage \n- Charging network just works. No plugging into multiple EA chargers to get one that works. \n- Lumbar support doesn’t deflate in the Tesla like it did on my Ioniq 5 \n- No recurring subscription fee to condition the cars climate remotely. \n- Service appts are super easy to schedule \n\nCons of the Tesla: \n- The inside of the Tesla has a little more cabin noise and the ride is slightly rougher. \n- The navigation system is pretty good but there’s room for improvement in its routing logic", "author": "Ill-Musician-4000", "created_time": "2025-02-18T20:36:41", "url": "https://reddit.com/r/electricvehicles/comments/1ismyes/my_thoughts_after_owning_both_a_23_my_and_23/", "upvotes": 28, "comments_count": 266, "sentiment": "bullish", "engagement_score": 560.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ispeoi", "title": "Business listing services that aren't run by support-free corporations? Google, Apple, Yelp, Mapquest, and Bing's verification processes are literally broken. There is no resolution.", "content": "Any business search providers that don't hate new business owners?  \nWhere do I get a magic phone number that Google et al think is valid for a business?  \nI know how these systems and processes are supposed to work.. and they're broken.  \nI'm an IT systems engineer. I'm not technically inept. These are basic tasks.  \n  \nHave a business phone number that their verification systems won't accept?  \nMay as well go curl up and die, because support cannot and will not fix that.  \n*Whoops our agent has mysteriously disconnected, after repeating our website error.*  \n  \nHave to make a video of your place of business, to get verified?  \nMay as well light yourself on fire, because their QR code link doesn't even scan.  \n  \nHave to update your business address on our review site?  \nHey there friendo, our login process just repeats in an endless loop!  \n  \nWhat the hell?", "author": "rrab", "created_time": "2025-02-18T22:39:49", "url": "https://reddit.com/r/business/comments/1ispeoi/business_listing_services_that_arent_run_by/", "upvotes": 5, "comments_count": 15, "sentiment": "bullish", "engagement_score": 35.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1isr2lw", "title": "Take the L on Google or roll over position?", "content": "I have a Google call with a srirke of $195 expiring 3/7. I bought it after earnings with the belief that the 7% drop was overdone given their P/E ratio, their free cash flow generation, and position as a huperscaler. Then the stock went down another 5%, some of which I attribute to the NFP report causing sector allocation changes.\n\nI continue to believe that the market's reaction to Google was overly pessimistic and that the stock has the potential to rebound. However, I think my option is probably going to expire worthless. \n\nI bought the option at $4.45 and now it's about $1.22. I meant to do a bull spread but hadn't been approved on Robinhood so that ended up not happening. I will need to get approved to do that. \n\nAnyway, I'm thinking of two options: 1. Taking the loss, or 2. Take the loss and enter a bull spread at $190/$195 expiring 4/17. At present prices, this will cost me about $200 to enter, if I sell my option at current prices I'll basically add another $80 to my already-losing bet. ", "author": "Shapen361", "created_time": "2025-02-18T23:48:09", "url": "https://reddit.com/r/options/comments/1isr2lw/take_the_l_on_google_or_roll_over_position/", "upvotes": 8, "comments_count": 11, "sentiment": "bearish", "engagement_score": 30.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}]