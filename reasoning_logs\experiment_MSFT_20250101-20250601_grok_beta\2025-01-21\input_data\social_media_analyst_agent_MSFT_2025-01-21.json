{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-01-21", "api_calls": {"load_local_social_media_data": {"data": [{"title": "What is the cooling period to reapply for a position in Microsoft?", "content": "I had applied for a position in Microsoft a couple of months ago and my application was rejected. Since then, I've upskilled myself and noticed that the position is still open. When I tried to apply to it I get a message \"you've already applied for this job\"\n\nIs there a way I can reapply to that job with an updated resume? If not, how long do I have to wait to reapply?", "created_time": "2025-01-21T00:47:10", "platform": "reddit", "sentiment": "bullish", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "prv<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i667b9/what_is_the_cooling_period_to_reapply_for_a/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Microsoft brand pocket knife passed down from generation to generation, yes that exists ", "content": "Today, January 20, 2025, I received a gift from my father, passed from father to son, yes, I received a Microsoft brand pocket knife, I didn't understand why it was Microsoft brand, I searched and nothing was found, no There are records, but it is very suspicious ", "created_time": "2025-01-21T02:33:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i68d6n/microsoft_brand_pocket_knife_passed_down_from/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Xbox sees red with latest Cipher Special Edition wireless controller Revealed", "content": "", "created_time": "2025-01-21T17:55:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "great_maccc", "url": "https://reddit.com/r/microsoft/comments/1i6om5g/xbox_sees_red_with_latest_cipher_special_edition/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Supplier Emissions Reduction Planning PAIN POINTS", "content": "Any Microsoft Suppliers gaining traction with pushback on Microsoft's SCOC initiative of carbon emissions reduction planning at the service line level?\n\nOur organization is emissions resilient without operational control over our building leases/energy usage, and only provide staffing or consulting individuals with an unimaged laptop, without operational control over commute, remote work, or business travel. \n\nThey have refused time and again, to install a reasonable threshold by which to measure relevance and impact of those suppliers with annual emissions totals, comparable to a day or two of Microsoft operations.\n\nQUESTION: How are you and your organization, if similar, working to meet this new SCOC requirement?", "created_time": "2025-01-21T18:35:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Successful_Charge_73", "url": "https://reddit.com/r/microsoft/comments/1i6pmlz/supplier_emissions_reduction_planning_pain_points/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Question about Outlook for Windows and Copilot", "content": "After finally being forced off the old Mail app, I was begrudgingly looking at the new Outlook for Windows and saw it had Copilot features. I don't want Microsoft training Copilot on my emails but as far as I am aware the free version of outlook doesn't use Copilot, can I assume that it will not be active when using the Outlook app as well?", "created_time": "2025-01-21T19:40:31", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "DogWith<PERSON>a<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i6r7va/question_about_outlook_for_windows_and_copilot/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "CSAM Microsoft", "content": "Hi everyone, I was suppose to interview for for a CSAM role at Microsoft but I got an email today saying \n\nWe hope you are doing well. We wanted to reach out and inform you that, due to business needs, we will need to cancel the screen event 1/22 - 1/24/2025, Microsoft Customer Success Account Manager - First Round Teams Interview for the () location that you are a part of this week. We apologize for the inconvenience of this change and appreciate you considering Microsoft for your career next step.\nWe will get back to you as soon as we have clarity regarding timing and next steps. Thank you for your time and consideration.\n\nDid this happen to anyone else\n\n", "created_time": "2025-01-21T22:29:33", "platform": "reddit", "sentiment": "neutral", "engagement_score": 61.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Horror-Context-237", "url": "https://reddit.com/r/microsoft/comments/1i6vbgd/csam_microsoft/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Stargate without Microsoft?", "content": "President trump is announcing with <PERSON> @ OpenAI and <PERSON>@Oracle as well as <PERSON><PERSON> @Softbank.\n\nWhere is Microsoft here? Seems strange Oracle is leading a charge here", "created_time": "2025-01-21T22:39:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 32.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "WittinglyWombat", "url": "https://reddit.com/r/microsoft/comments/1i6vk11/stargate_without_microsoft/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Updates?", "content": "What is Microsoft doing with the updates? I just had a pop-up saying would you like to restart now to finish updating. I click no. Started watching a movie and my computer started updating...\n\nMicrosoft really lost its touch with update lately. What's going on??", "created_time": "2025-01-21T22:44:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 26.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "mlandry2011", "url": "https://reddit.com/r/microsoft/comments/1i6vnyb/updates/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Hot Take: Copilot shine as a search engine tool.", "content": "Copilot become really powerful if you treat it like a search engine tool. Because it does bing searches for you, you can ask something and it will compile, and summarized the result\n\nIt help alot when I do plenty of tinkering. It also help that it doesn't' hallucinate as much like ChatGPT, so you can reasonably rely on the answer being decently accurate.\n\n  \nBecause of that, I would not be oppose for Copilot to be integrated to Office, in all case, it would be a much more powerful Clippy, that actually talks when you want to talk to them.", "created_time": "2025-01-20T02:07:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 105.0, "upvotes": 51, "num_comments": 0, "subreddit": "unknown", "author": "AsrielPlay52", "url": "https://reddit.com/r/microsoft/comments/1i5fjfa/hot_take_copilot_shine_as_a_search_engine_tool/", "ticker": "MSFT", "date": "2025-01-20"}, {"title": "What are the benefits for Microsoft Canada?", "content": "Just got an offer to work at Microsoft Canada. <PERSON><PERSON><PERSON><PERSON> only gave some information about health plan and vacation policy. \n\nFolks who work at Microsoft Canada, what are the benefits you would highlight for me? I am too excited but I don’t want to shoot a lot of questions about benefits until I am officially in lol (doing background check rn). I found a lot of info about US benefits, but not a lot about Canada.\n\nSome questions I have:\nDo all Microsoft employees get xbox game pass, or just the gaming employees?\nHow’s the pat/mat leave? How much top-up, for how long?\nIs there any childcare benefits for Canada?\nIs there any gym/well being benefits or an access to gym?", "created_time": "2025-01-20T04:45:49", "platform": "reddit", "sentiment": "bullish", "engagement_score": 28.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Emergency-Cup1360", "url": "https://reddit.com/r/microsoft/comments/1i5idys/what_are_the_benefits_for_microsoft_canada/", "ticker": "MSFT", "date": "2025-01-20"}, {"title": "HireRight is a nightmare", "content": "I’m onboarding at Microsoft, and HireRight flagged my education as **“Unable to Verify”** even though I provided official transcripts and directed them to NSCH (the only available source since the school is closed) for verification.\n\nI didn’t graduate from the Art Institute (and never claimed to—even on HireRight’s initial questionnaire). Education wasn’t discussed during the interview process, and the job posting states that “equivalent experience” is accepted for the role.\n\nDespite this, HireRight marked my background check as **“Completed”** with a yellow flag under the education section. Everything else came back clear, but this one issue has me worried about how it might affect my onboarding.\n\nHas anyone dealt with a similar situation? Did it cause any delays or issues? Would love to hear your experiences.", "created_time": "2025-01-20T17:22:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 166.0, "upvotes": 106, "num_comments": 0, "subreddit": "unknown", "author": "Whole-Finish-6905", "url": "https://reddit.com/r/microsoft/comments/1i5vhf0/hireright_is_a_nightmare/", "ticker": "MSFT", "date": "2025-01-20"}, {"title": "Entry level business analyst", "content": "Hey guys, looking to see if anyone knows how the interview process is for the entry level BA position at Microsoft (final round specifics)? Any experiences, things I should prepare for, anything of the sort? Would love any advice I can get, thanks.\n", "created_time": "2025-01-19T03:23:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "b<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i4ozyv/entry_level_business_analyst/", "ticker": "MSFT", "date": "2025-01-19"}, {"title": "Reduced-rate Microsoft 365 without Copilot", "content": "Hey Microsoft. It's cool and all that you're playing with AI, but I DON'T WANT IT, and I resent that you're increasing the annual cost of Microsoft 365 to pay for the obscene investments into massive world-killing server farms. Please... give us a cheaper subscription option that locks out the AI functions. I don't want them. ", "created_time": "2025-01-19T04:22:07", "platform": "reddit", "sentiment": "neutral", "engagement_score": 252.0, "upvotes": 138, "num_comments": 0, "subreddit": "unknown", "author": "Phlucious", "url": "https://reddit.com/r/microsoft/comments/1i4q5ej/reducedrate_microsoft_365_without_copilot/", "ticker": "MSFT", "date": "2025-01-19"}, {"title": "Microsoft Relocation", "content": "Hi there, \n\nDo Microsoft offer Relocation fees/bonus when you are hired and need to relocate to another town ?\nOne of the HR screeening questions was if I would be able to relocate on my own. I said YES but Im wondering if it’s common for Microsoft new hires.\n\nThank you", "created_time": "2025-01-18T05:12:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 71.0, "upvotes": 35, "num_comments": 0, "subreddit": "unknown", "author": "Ok_Present_8445", "url": "https://reddit.com/r/microsoft/comments/1i40czw/microsoft_relocation/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "What to do with vested RSUs?", "content": "Are you holding onto those individual stocks? Or are you selling and diversifying in some ETF like VOO, VTI QQQ. \n\nI feel like if you were to invest in ETFs while still holding onto $MSFT or GOOG AMZN etc it would be redundant. Thoughts on how others have carried this situation out? I’m still holding onto my vested RSUs and thinking if I should diversify into my VOO portfolio? ", "created_time": "2025-01-18T14:11:15", "platform": "reddit", "sentiment": "bearish", "engagement_score": 130.0, "upvotes": 48, "num_comments": 0, "subreddit": "unknown", "author": "Ok-Intention-384", "url": "https://reddit.com/r/microsoft/comments/1i48bp2/what_to_do_with_vested_rsus/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Microsoft AutoGen v0.4: A turning point toward more intelligent AI agents for enterprise developers", "content": "", "created_time": "2025-01-18T19:06:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Ethan<PERSON>ill<PERSON>s_TG", "url": "https://reddit.com/r/microsoft/comments/1i4eraf/microsoft_autogen_v04_a_turning_point_toward_more/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Your sign-in experience is changing", "content": "\"The web browser sign-in experience is changing when you sign in to any product or service using your Microsoft account. Starting in February 2025, ***you will stay signed in automatically*** unless you sign out or use private browsing.\" [Avoid staying signed in on a public computer](https://support.microsoft.com/en-us/account-billing/avoid-staying-signed-in-on-a-public-computer-d3f1448b-64b9-4b35-89d0-ce56715c6756)\n\nAm I the only one that thinks this change is moronic and will probably end badly for quite a few people?  ", "created_time": "2025-01-18T22:17:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 70.0, "upvotes": 20, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i4iyq1/your_signin_experience_is_changing/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Microsoft 365 Business code stack", "content": "Does anyone know how long M365 Business Standard product keys can be stacked for?\n\n(I'm aware you can stack up to 5 years of M365 personal subscriptions using product keys, but do not want to assume the same for business as it's a different platform)", "created_time": "2025-01-18T22:29:04", "platform": "reddit", "sentiment": "bullish", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "StormB2", "url": "https://reddit.com/r/microsoft/comments/1i4j7h7/microsoft_365_business_code_stack/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Should I be worried about applying for too many jobs?", "content": "I've been applying for jobs at Microsoft for the past few months. I have 20+ inactive applications, and currently have 10 pending. I recently completely overhauled my resume and want to apply for more positions, but I am getting worried that it looks like I am spamming them. I only apply for positions where I meet all the required criteria. Am I worried about nothing? I am tempted to make another account for a fresh start, but honestly that feels shady.", "created_time": "2025-01-17T00:54:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 47.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "ItWorkedLastTime", "url": "https://reddit.com/r/microsoft/comments/1i34bua/should_i_be_worried_about_applying_for_too_many/", "ticker": "MSFT", "date": "2025-01-17"}, {"title": "technical program manager - interview prep", "content": "Hi, got a question - TPM role at Microsoft, how much coding experience is required? I've read that coding / programming experience is very light, but on the other hand, I heard there are coding questions in the interview. \n\n  \nCan anyone shine a light on that?\n\n  \nThanks!", "created_time": "2025-01-17T01:51:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Clay20222", "url": "https://reddit.com/r/microsoft/comments/1i35gt1/technical_program_manager_interview_prep/", "ticker": "MSFT", "date": "2025-01-17"}, {"title": "Contract role on hold for a month due to funding?", "content": "Hey everyone I was told I was hired for a contract role as a program manager in ux for Microsoft through a vendor company but now it's on hold. I interviewed with the internal managers, established pay, was told I was hired and that it would most likely be ready to onboard end of January. I just came back from vacation and now the vendor company is telling me the budget for the contract length is still pending with the PO they sent and most likely won't be approved till near end of February. I was never told this up front by anyone and this is the first time this has happened to me as a contractor of 5 years. I like the pay and the role but wondering if I should just keep looking in the mean time. Is this poor timing due to budget submissions for 2025 fiscal?", "created_time": "2025-01-17T23:57:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "iceetoomuch", "url": "https://reddit.com/r/microsoft/comments/1i3uibk/contract_role_on_hold_for_a_month_due_to_funding/", "ticker": "MSFT", "date": "2025-01-17"}, {"title": "Is MS 900 and Pl 300 worth it as a aspiring developer?", "content": "I'm getting a 50% off coursera discount voucher for pl 300 and ms 900 upon completion of its respective courses. Is it worth getting or should I just stick with getting JUST the azure developer cert?", "created_time": "2025-01-16T08:25:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "AmazingInflation58", "url": "https://reddit.com/r/microsoft/comments/1i2kexj/is_ms_900_and_pl_300_worth_it_as_a_aspiring/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "Microsoft 365 users still on Windows 10 will be out of luck when Windows 10 is retired in October", "content": "", "created_time": "2025-01-16T12:54:14", "platform": "reddit", "sentiment": "neutral", "engagement_score": 119.0, "upvotes": 49, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1i2o8yq/microsoft_365_users_still_on_windows_10_will_be/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "How is Microsoft still alive?", "content": "Genuine question. Let’s go over it:\n\n- Microsoft accounts: log-in issues of every type, going from your personal accounts to work accounts. One time I went to a Microsoft store and even the cashier was having log in problems. If you log in and connect your account to another (let’s say Microsoft-PlayStation for Minecraft) you’re stuck with it because MICROSOFT DOES NOT HAVE A WAY TO UNLINK TWO ACCOUNTS. \n\n- Surface products: been using surfaces for ages, overall decent but overpriced. However: literally every complementary object has a high chance of dying after the 3 year mark. Keyboard died in one year, pen in 3 and a half. They made a pen which can change tips where the tip connector breaks before the interchangeable tip. Genius work.\n\n- Office: anyone that ever had to deal with Office Notes knows it’s as comfortable as lying down on anti-homeless architecture. Possibly the least seamless software suite anyone has ever created.\n\n- Xbox: I feel so sorry for y’all. \n\n- AI: Bought shares in Open AI to collaborate with it only for Open AI to give the same privilege to Apple without them having to pay a dime. Another management masterclass. \n\nNot saying Microsoft hasn’t done any good work, but so far I had problems with anything I’ve ever used made by Microsoft. How does a company survive like this? Are we all just too afraid of switching to competitors? Is it on Us? I’m genuinely curious.", "created_time": "2025-01-16T15:46:30", "platform": "reddit", "sentiment": "bullish", "engagement_score": 36.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>_", "url": "https://reddit.com/r/microsoft/comments/1i2rtom/how_is_microsoft_still_alive/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "Grrrr I'm so over it. Microsoft or bust ", "content": "\"or bust\"? I'm just being a little dramatic but what gives? I've been actively applying to Microsoft roles since September 2024, and even had FTE friends submit referrals on my behalf. However, when I logged in today, I saw that I wasn’t considered for three of the six positions I applied for. Are these ghosts jobs? Based on the listed requirements, I meet the qualifications, have the necessary certifications, and relevant experience. Yet, I haven’t even been offered an interview, and it’s incredibly frustrating. I’m at a loss trying to understand what’s going wrong. I've written and written/tailored my resume countless times w/ AI and I've even broken down and included cover letters.  What steps can I take to improve my chances? ", "created_time": "2025-01-16T19:50:40", "platform": "reddit", "sentiment": "bearish", "engagement_score": 104.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Fair-Cap-1048", "url": "https://reddit.com/r/microsoft/comments/1i2xlm9/grrrr_im_so_over_it_microsoft_or_bust/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "Microsoft raises price of consumer Microsoft 365 first time since 2013", "content": "", "created_time": "2025-01-16T20:18:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 470.0, "upvotes": 336, "num_comments": 0, "subreddit": "unknown", "author": "Novel_Negotiation224", "url": "https://reddit.com/r/microsoft/comments/1i2y8v3/microsoft_raises_price_of_consumer_microsoft_365/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "MSFT tech Interview: entry level", "content": "I had my virtual loop interview, 3 rounds each of 45 min. My laptop has some trouble with teams and I got super tensed before the interview started. First round I complicated it by trying to code for O(N) rather than O(N**2) and explaining the O(N**2) solution, the second round I was almost able to code the whole thing and the third round went well too. Do I have still have any chance? Or should I just give up on MSFT and continue applying?", "created_time": "2025-01-15T00:54:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 18.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Curious_Fun8087", "url": "https://reddit.com/r/microsoft/comments/1i1lif5/msft_tech_interview_entry_level/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "What do you think are the most powerful features in Microsoft Azure AI?", "content": "I'm in the process of familiarizing myself with Microsoft Azure AI and its features to improve my projects. I was curious what you think, what is the feature you use the most and what is your favorite´?", "created_time": "2025-01-15T08:24:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "JKOE21", "url": "https://reddit.com/r/microsoft/comments/1i1sz84/what_do_you_think_are_the_most_powerful_features/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "The UK is running suspiciously low on Xbox Series X console stocks, but why?", "content": "", "created_time": "2025-01-15T08:42:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "YouAreNotMeLiar", "url": "https://reddit.com/r/microsoft/comments/1i1t76u/the_uk_is_running_suspiciously_low_on_xbox_series/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "I’m using ms apps for free on mac", "content": "I don’t know how am I able to use ms word ppt and excel for free on my mac. \n\nHere’s the explanation I came up with. In 2020 I bought a windows laptop and got ms apps for free with it. I may have somehow linked that account with my Microsoft account which I’m using on my mac and it’s working. \n\nCan someone tell me if I’m right? Is there a time limit for the free apps with windows? And can Microsoft screw me up with this post?\n\n", "created_time": "2025-01-15T14:36:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "RequirementVivid6312", "url": "https://reddit.com/r/microsoft/comments/1i1yqee/im_using_ms_apps_for_free_on_mac/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Microsoft Considered Shutting Down Xbox In 2021, Opted For Studio Acquisitions To Boost Game Pass", "content": "", "created_time": "2025-01-15T15:06:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 654.0, "upvotes": 490, "num_comments": 0, "subreddit": "unknown", "author": "YouAreNotMeLiar", "url": "https://reddit.com/r/microsoft/comments/1i1zen2/microsoft_considered_shutting_down_xbox_in_2021/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Is there any info on Customer Experience Engineer role?", "content": "I have a phone tech screen coming up and I'm a bit nervous. I have no idea what to expect, it's a 30 min phone tech screen.\n\nThe Focus Areas for tech screening will be as follows:\n\n1. Technical Skills in Cloud (preferably Azure)\n\n2. Incident/Crisis Management Skills\n\n3. Customer Relationship Management Skills\n\n4. Experience with Technical Support\n\n5. Problem Management Skills\n\nAnyone else ever done an interview for this role and can offer any insights on it? ", "created_time": "2025-01-15T21:58:54", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1i29070/is_there_any_info_on_customer_experience_engineer/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Microsoft introduces more ways for players to repair Xbox Consoles, reduce waste and save energy ", "content": "[https://news.xbox.com/en-us/2025/01/15/xbox-repair-reduce-waste-energy-saving/](https://news.xbox.com/en-us/2025/01/15/xbox-repair-reduce-waste-energy-saving/) ", "created_time": "2025-01-15T22:06:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 45.0, "upvotes": 33, "num_comments": 0, "subreddit": "unknown", "author": "Felicity_Here", "url": "https://reddit.com/r/microsoft/comments/1i296ls/microsoft_introduces_more_ways_for_players_to/", "ticker": "MSFT", "date": "2025-01-15"}, {"title": "Internship: Waitlist Microsoft", "content": "Waitlisted and told \"eligible for placement on a new team if headcount becomes available within the next 6 months.\" Is it likely for people to get off the waitlist at Microsoft? ", "created_time": "2025-01-14T00:02:29", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "applesandbananasbaby", "url": "https://reddit.com/r/microsoft/comments/1i0sp54/internship_waitlist_microsoft/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "Customer Success Account Specialist", "content": "Hey everyone! \n\nI have been shortlisted to the CSAS role in Microsoft and would like to understand better the responsibilities of the role. I went through the job description and it kinda sounds like a salesman kind of role instead of a consulting based role. I had a call with the recruiter and he mentioned that there are pre-sales consulting and post sales consulting and this role would be post sales where you would liase with the client and the implementation team to ensure the project goes smoothly. \n\n  \nWould like to hear your thoughts or experience on this! Thanks", "created_time": "2025-01-14T08:15:08", "platform": "reddit", "sentiment": "bearish", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "russell616", "url": "https://reddit.com/r/microsoft/comments/1i11elf/customer_success_account_specialist/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "Microsoft streaming technology ", "content": "Hello, I wonder if anyone in this group remember a couple of old tech demos that Microsoft showed off, many years ago? I would like to refresh my memory on what the technology was called.\n[I thought that it was called \"Sailfish\" or something similar, but when I search for it, there is only an operative system that shows up...]\n\nIt was a streaming technology that allowed you to display pictures over a slow dial up connection.\n\nIn one demo, you saw what looked like a bunch of different colored squares, but then they zoomed in and you could see that they where many high resolution pictures. I think they did something similar with a text (declaration of independence?).\n\nI believe they worked on the technology for a couple of years and in a later demo, they showed off something similar to Google Street View. Where they had a 360 view of a famous location and they streamed in pictures taken by tourists from that location. As you moved around the pictures came into view. And they used the metadata to show dem in the right location, in the right direction (and angle?).\n\n", "created_time": "2025-01-14T09:29:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Cane_P", "url": "https://reddit.com/r/microsoft/comments/1i12dcs/microsoft_streaming_technology/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "True up License Windows 10 / 11", "content": "m pretty sure im not the only one here with the unpleasant Windows 10 to 11 Migration.\n\nI have question Regarding these True Up-Licenses.\n\nAre they granted for update or do we have to buy some kind of add-on to migrate? Microsoft is bich and we all know that.\n\nTY guys", "created_time": "2025-01-14T09:38:44", "platform": "reddit", "sentiment": "bullish", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "SrSFlX", "url": "https://reddit.com/r/microsoft/comments/1i12ho4/true_up_license_windows_10_11/", "ticker": "MSFT", "date": "2025-01-14"}, {"title": "M365", "content": "So it is m365.office.com in the future but is the copilot integrated into the products automatically like Google or do I have to purchase it extra like it is now if I want to use it in the O365 products? I'm heavily using onenote and copilot would be a great addition. Thanks", "created_time": "2025-01-14T09:53:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1i12oks/m365/", "ticker": "MSFT", "date": "2025-01-14"}], "metadata": {"timestamp": "2025-07-06T22:04:29.847620", "end_date": "2025-01-21", "days_back": 7, "successful_dates": ["2025-01-21", "2025-01-20", "2025-01-19", "2025-01-18", "2025-01-17", "2025-01-16", "2025-01-15", "2025-01-14"], "failed_dates": [], "source": "local"}}}}