[{"platform": "reddit", "post_id": "reddit_1jq6q99", "title": "Help, Google translate not working.🙏", "content": "This morning, I opened some online novels that I read via Google translating into English, everyday.\nBut its not working. Its not translating into English.😭\nEverytime I manually click the translate button, it says- 'Oops. This page could not be translated'\n", "author": "Klutzy_Interest5673", "created_time": "2025-04-03T02:16:14", "url": "https://reddit.com/r/chrome/comments/1jq6q99/help_google_translate_not_working/", "upvotes": 11, "comments_count": 16, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jq7twn", "title": "Google Gemini executive <PERSON><PERSON> to step down", "content": "", "author": "ControlCAD", "created_time": "2025-04-03T03:11:14", "url": "https://reddit.com/r/business/comments/1jq7twn/google_gemini_executive_sissie_<PERSON><PERSON><PERSON>_to_step_down/", "upvotes": 167, "comments_count": 20, "sentiment": "neutral", "engagement_score": 207.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jqetd9", "title": "Can you trust chatGPT/ gemini to create a google ads campaign?", "content": "Can you trust chatGPT/ gemini to create a google ads campaign, if all the information given properly as a prompt?\nI tried it, it looks good to me, what are your thoughts?", "author": "RelativePlenty9945", "created_time": "2025-04-03T10:24:15", "url": "https://reddit.com/r/marketing/comments/1jqetd9/can_you_trust_chatgpt_gemini_to_create_a_google/", "upvotes": 0, "comments_count": 9, "sentiment": "neutral", "engagement_score": 18.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jqffdl", "title": "What's up with <PERSON><PERSON><PERSON> and them having to caption EVERY SINGLE WORD they say?", "content": "title\n\nThey're really annoying. If I wanted captions I'd just turn them on myself\n\nEDIT: To everyone misunderstanding what I mean, I'm talking about [this example](https://www.youtube.com/shorts/Gekf6hDs-BE) (Ignore that it's shorts, this is just a good example that someone posted in comments already)", "author": "_No-Life_", "created_time": "2025-04-03T11:00:54", "url": "https://reddit.com/r/youtube/comments/1jqffdl/whats_up_with_ytbers_and_them_having_to_caption/", "upvotes": 2, "comments_count": 36, "sentiment": "bearish", "engagement_score": 74.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jqhuda", "title": "Google In Talks To Rent Nvidia Blackwell GPUs From CoreWeave", "content": "", "author": "Mynameis__--__", "created_time": "2025-04-03T13:01:51", "url": "https://reddit.com/r/nvidia/comments/1jqhuda/google_in_talks_to_rent_nvidia_blackwell_gpus/", "upvotes": 12, "comments_count": 0, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jqj0yl", "title": "Here are the camera specs for the Google Pixel 10 series", "content": "", "author": "RandomCheeseCake", "created_time": "2025-04-03T13:53:06", "url": "https://reddit.com/r/GooglePixel/comments/1jqj0yl/here_are_the_camera_specs_for_the_google_pixel_10/", "upvotes": 326, "comments_count": 225, "sentiment": "neutral", "engagement_score": 776.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jqjpv2", "title": "Google calls for urgent AGI safety planning", "content": "", "author": "MetaKnowing", "created_time": "2025-04-03T14:20:56", "url": "https://reddit.com/r/artificial/comments/1jqjpv2/google_calls_for_urgent_agi_safety_planning/", "upvotes": 19, "comments_count": 12, "sentiment": "bullish", "engagement_score": 43.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jqwjsi", "title": "CAD/USD Currency Exchange Rate & News - Google Finance", "content": "", "author": "49orth", "created_time": "2025-04-03T22:41:41", "url": "https://reddit.com/r/Economics/comments/1jqwjsi/cadusd_currency_exchange_rate_news_google_finance/", "upvotes": 0, "comments_count": 3, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "Economics", "hashtags": null, "ticker": "GOOGL"}]