[{"platform": "reddit", "post_id": "reddit_1h5dct8", "title": "Google Ads Account hacked, racked up 75 dollar bill on my CC. How to contact google ads support to get it refunded ?", "content": "title.", "author": "delsystem32exe", "created_time": "2024-12-03T02:48:29", "url": "https://reddit.com/r/adwords/comments/1h5dct8/google_ads_account_hacked_racked_up_75_dollar/", "upvotes": 2, "comments_count": 6, "sentiment": "neutral", "engagement_score": 14.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h5ofpm", "title": "Is it possible to exclude certain users with a certain TLD from seeing our ads?", "content": "We want our google ads to target b2b users and ignore users with \"free\" email accounts such as [gmail.com](http://gmail.com) or hotmail.com. Is this possible? ", "author": "t83357", "created_time": "2024-12-03T14:19:31", "url": "https://reddit.com/r/adwords/comments/1h5ofpm/is_it_possible_to_exclude_certain_users_with_a/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h5sut2", "title": "Google Confirms New Gmail Security Surprise—And It’s So Simple", "content": "", "author": "hunterd189", "created_time": "2024-12-03T17:29:32", "url": "https://reddit.com/r/Android/comments/1h5sut2/google_confirms_new_gmail_security_surpriseand/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h5xagw", "title": "Not sure if this is the right sub to ask, but I'm tempted to switch from an iPhone to a Pixel phone? Is that a dumb idea?", "content": "I really like all Google services and clean stock android experience… But I'm hesitant as after all Google is an advertising company.......", "author": "t<PERSON><PERSON><PERSON>", "created_time": "2024-12-03T20:29:30", "url": "https://reddit.com/r/privacy/comments/1h5xagw/not_sure_if_this_is_the_right_sub_to_ask_but_im/", "upvotes": 9, "comments_count": 49, "sentiment": "neutral", "engagement_score": 107.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}]