[{"platform": "reddit", "post_id": "reddit_1kt8kfi", "title": "New head of Social Security, hired from Wall Street, tells staff he had to Google the job when he was offered it", "content": "", "author": "Huge_Cap_1076", "created_time": "2025-05-23T02:22:08", "url": "https://reddit.com/r/news/comments/1kt8kfi/new_head_of_social_security_hired_from_wall/", "upvotes": 23985, "comments_count": 550, "sentiment": "neutral", "engagement_score": 25085.0, "source_subreddit": "news", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kt8kn8", "title": "New head of Social Security, hired from Wall Street, tells staff he had to Google the job when he was offered it", "content": "", "author": "undercurrents", "created_time": "2025-05-23T02:22:26", "url": "https://reddit.com/r/nottheonion/comments/1kt8kn8/new_head_of_social_security_hired_from_wall/", "upvotes": 263, "comments_count": 6, "sentiment": "neutral", "engagement_score": 275.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktcxbu", "title": "What changes will you make to your SEO strategy with Google's new AI Mode?", "content": "First AI overviews and now a chatbot type search experience - AI mode search. Google really is not giving SEOs a break. I am curios to know what are the new things you plan to start doing now that you weren’t doing until now?", "author": "Shakyshekhy4360", "created_time": "2025-05-23T06:41:54", "url": "https://reddit.com/r/DigitalMarketing/comments/1ktcxbu/what_changes_will_you_make_to_your_seo_strategy/", "upvotes": 35, "comments_count": 35, "sentiment": "neutral", "engagement_score": 105.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktfhmh", "title": "What features do you wish Google My Maps had?", "content": "Hey everyone,\n\nI’ve been using Google My Maps for a variety of projects and love how accessible and easy it is to use. That said, I’ve often found myself wishing it had a few extra features — and I’m sure others here have too.\n\nI’m curious:\nWhat features do you wish Google My Maps had but currently doesn’t?\nAre there any limitations that consistently get in your way, or anything you’ve had to work around?\n\nWhether it’s better collaboration tools, more customization options, data handling improvements, or anything else — I’d love to hear your thoughts. I’m just trying to get a better understanding of what people actually want or need in a custom mapping tool.\n\nThanks in advance!\n\n\n", "author": "BadPutrid2046", "created_time": "2025-05-23T09:45:17", "url": "https://reddit.com/r/GoogleMaps/comments/1ktfhmh/what_features_do_you_wish_google_my_maps_had/", "upvotes": 17, "comments_count": 40, "sentiment": "neutral", "engagement_score": 97.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktgpmd", "title": "Google Ads Support is a Nightmare", "content": "Does anyone else feel that the quality of support for Google Ads has plummeted. \n\nA couple of years back I used to get actual folks with technical knowhow who tried to fix the issues and succeeded at least 50% of the time.\n\nFor the last 4/5 issues, we have been only getting irrelevant nonsense. I will send them a message saying my Ads are not spending any money. And they come back with recommendations on how to optimise my campaigns.\n\nA recurring issue for us has been that a perfectly running campaign suddenly stops gathering impressions and Google support has been absolutely hopeless at debugging it. ", "author": "Individual-Lab-2008", "created_time": "2025-05-23T11:02:33", "url": "https://reddit.com/r/adwords/comments/1ktgpmd/google_ads_support_is_a_nightmare/", "upvotes": 11, "comments_count": 17, "sentiment": "neutral", "engagement_score": 45.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktjm81", "title": "Google Veo 3 could become a real problem for content creators as convincing AI videos flood the web", "content": "", "author": "Tiny-Independent273", "created_time": "2025-05-23T13:30:12", "url": "https://reddit.com/r/artificial/comments/1ktjm81/google_veo_3_could_become_a_real_problem_for/", "upvotes": 65, "comments_count": 65, "sentiment": "neutral", "engagement_score": 195.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktjn4l", "title": "YouTube promoting racism again.", "content": "When people post comments saying that \"Pakis are criminals\" or immigrants commit more crime, I post government statistics proving them wrong.\n\nYouTube protects the racist comments while deleting the government stats. I don't post a link in the comments because that's not allowed. My comment might be something like:\n\n>\"In the UK, 60 Roma committed crimes in the last 10 years compared to 60 MILLION white Brits.\"\n\nEvery time I post this comment it gets deleted. Proving beyond any doubt that YouTube promotes racism. YouTube promotes racism, pornography to children and gun crime to countries that don't have guns. YouTube is a criminal organisation. I bet this gets downvoted by simps brainwashed by YouTube. Such is the world. 🤷‍♂️", "author": "Advanced-Welcome-928", "created_time": "2025-05-23T13:31:19", "url": "https://reddit.com/r/youtube/comments/1ktjn4l/youtube_promoting_racism_again/", "upvotes": 0, "comments_count": 59, "sentiment": "neutral", "engagement_score": 118.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktk1zu", "title": "Googles Discovery & Display Channels Really Are Utter Trash Traffic Aren't They? So why do we all do performance max campaigns?", "content": "If anyone here, has ever tried to run a standalone campaign on either of the display and/or discovery/demand gen channels, chances are you will see lots of worthless clicks - along with high spend, and astonishingly high cpc's!\n\nAs far as we are concerned, both of these channels, always have been, and likely always will be, completely and utterly useless junk/trash traffic which is worthless to the vast majority of businesses.\n\nEveryone know's that this is the reason Performance Max was created in the first place - so google could easily package up and mix in their shitty junk traffic with the better quality traffic from their search channels - simultaneously raising CPCs across them all.\n\nIsn't it about time google just come clean with this, and stop trying to have us all on - scrap PMAX, and let us all judge the merits and worth of each channel individually. All marketers and CFOs etc need to be able to critically judge the effectiveness of their spend across channels - wasted spend is unacceptable and google should respect this rather than trying to pull the wool over everyone's eyes and attempt to completely manipulate cpc's across different channels.\n\nThe sooner ChatGPT gets going with it's advertising the better - so long as they are more transparent and honest with us, they are bound to win a lot of advertisers over compared to googles sneaky snakey tactics of late.", "author": "ConstructionOdd4862", "created_time": "2025-05-23T13:49:59", "url": "https://reddit.com/r/PPC/comments/1ktk1zu/googles_discovery_display_channels_really_are/", "upvotes": 29, "comments_count": 47, "sentiment": "bullish", "engagement_score": 123.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktlazj", "title": "New head of Social Security, hired from Wall Street, tells staff he had to Google the job when he was offered it", "content": "", "author": "teruteru-fan-sam", "created_time": "2025-05-23T14:42:52", "url": "https://reddit.com/r/nottheonion/comments/1ktlazj/new_head_of_social_security_hired_from_wall/", "upvotes": 281, "comments_count": 18, "sentiment": "neutral", "engagement_score": 317.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktnjt8", "title": "Moving from account executive in an agency to in-house marketing specialist. <PERSON>orried about having to use tools like Google Analytics, Meta Analytics and Hubspot Automation. How can I get better at them?", "content": "I have around 2 years of marketing experience working at an agency.  \nIn our agency, we had experts who deal with SEM/SEO, Google and Meta Analytics and Hubspot Email Marketing Automation. As account managers, we had exposure to these platforms, but they did not fall under our responsibility.  \nGot laid off a while back and now I'm currently job hunting.  \nFound a few position in-house that would need someone to have knowledge with Google Analytics, Meta Analytics and Hubspot Automation. As I said, during my agency work, we had experts who handled such software. How difficult would it be to become accustomed to these platforms?", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-23T16:14:18", "url": "https://reddit.com/r/marketing/comments/1ktnjt8/moving_from_account_executive_in_an_agency_to/", "upvotes": 4, "comments_count": 8, "sentiment": "bearish", "engagement_score": 20.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktoz86", "title": "SOS Assistance Needed Undoing The Mess My \"Google Ads Expert\" Made, Willing To Pay For Help!!!", "content": "Like the title says, I made the same mistake as many people on here of thinking working with a Google Ads Expert would be a good idea. The call was at best useless, at worst (or so I thought) an infuriating take on the state of the world.   \nNow, about 3 weeks later, I am under threat of being fired because the google ads campaign has tanked SO hard that the business I am working for has seen a 40%+ decline in sales.   \nI cant get a hold of anybody at Google, I cant get a hold of the \"expert\" and if I don't fix this, I'm going to be fired.   \nI am under the changes section and I can see what changes were made but can not for the life of me figure out how to undo any of the changes  or figure out how to get back to the section where the changes were made. \n\nIf there is anybody who knows what they're doing and is willing to help, I'm willing to pay; I just need help. I've seen quite a few posts similar to this one with people who were able to find solutions. I'm familiar with Adwords but by no means an expert. ", "author": "llbboutique", "created_time": "2025-05-23T17:12:28", "url": "https://reddit.com/r/adwords/comments/1ktoz86/sos_assistance_needed_undoing_the_mess_my_google/", "upvotes": 5, "comments_count": 8, "sentiment": "bearish", "engagement_score": 21.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktpsim", "title": "Thanks GOOGL", "content": "", "author": "spellbreaker", "created_time": "2025-05-23T17:44:47", "url": "https://reddit.com/r/wallstreetbets/comments/1ktpsim/thanks_googl/", "upvotes": 754, "comments_count": 34, "sentiment": "neutral", "engagement_score": 822.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktsrlv", "title": "Building a tool to make Google analytics (GA4) somewhat easier to use", "content": "Hey everyone, I’m building a tool that plugs your Google Analytics 4 data right into Slack.\n\nYou just install it, connect your GA4 account, then tag it in any channel and ask things like *“How many new users did we get last week?*” or *“Compare mobile vs desktop conversions for our spring promo.”*\n\nIt pulls the data in real time and drops back a quick summary, optionally with chart in the channel (or DM). You don't have to deal with the GA4 dashboard at all.\n\nWould you use something like this in your Slack workspace? Would love to hear your thoughts. Thanks!", "author": "prous5tmaker", "created_time": "2025-05-23T19:48:11", "url": "https://reddit.com/r/DigitalMarketing/comments/1ktsrlv/building_a_tool_to_make_google_analytics_ga4/", "upvotes": 8, "comments_count": 16, "sentiment": "bearish", "engagement_score": 40.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktu9i7", "title": "New head of Social Security, hired from Wall Street, tells staff he had to Google the job when he was offered it", "content": "", "author": "AwakeGroundhog", "created_time": "2025-05-23T20:52:38", "url": "https://reddit.com/r/nottheonion/comments/1ktu9i7/new_head_of_social_security_hired_from_wall/", "upvotes": 448, "comments_count": 20, "sentiment": "neutral", "engagement_score": 488.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}]