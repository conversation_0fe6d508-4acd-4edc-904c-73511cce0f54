{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-03-26", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Microsoft Azure SWEs : Do you like work?", "content": "I'm wondering if the work is bearable/likeable as a full-time job, and if co-workers are hard-working but not toxic. Especially with the recent layoffs without severance.", "created_time": "2025-03-26T01:45:39", "platform": "reddit", "sentiment": "bearish", "engagement_score": 17.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "No-Tangelo-1857", "url": "https://reddit.com/r/microsoft/comments/1jk0mht/microsoft_azure_swes_do_you_like_work/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Is Copilot+ PC Software Actually Limited to Having an \"NPU\" or Will It Eventually Be Compatible with Older PCs? How Good Is the Live Translation?", "content": "Historically Microsoft has launched proprietary software with \"recommended hardware\" and then \"minimum hardware\" specs. I'm wondering if this is perhaps the case with the Copilot+ feautures, which are described using similar terms: [https://www.microsoft.com/en-us/windows/copilot-plus-pcs?r=1#faq1](https://www.microsoft.com/en-us/windows/copilot-plus-pcs?r=1#faq1)\n\nIs an \"NPU\" actually a totally new component such that trying to run Copilot+ would be like trying to run a game without a videocard? ...or is Copilot+ just temporarily exclusive to hardware branded as \"Copilot+ ready\" to sell some new computers and it will eventually be available on PCs without those specs?\n\nI'm also wondering if anyone can speak to how effective the live translation is. I moved to Norway for a job and don't speak Norwegian yet, so I am often sitting in meetings without understanding a word of what is going on. Google translate and similar software have been basically useless for live translation. The Copilot+ live translation seems like the perfect solution to this if it works well.", "created_time": "2025-03-26T10:57:51", "platform": "reddit", "sentiment": "bearish", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jk91ej/is_copilot_pc_software_actually_limited_to_having/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Microsoft Defender for Endpoint Plan 1 vs Microsoft Defender for Endpoint Plan 2", "content": "Got about 400 users that need an endpoint protection plan...Wondering if it is worth paying the difference on Microsoft Defender for Endpoint Plan 1 and get Microsoft Defender for Endpoint Plan 2.... Getting hassled by auditors, I guess reports from sccm on the Microsoft defender that is shipped with windows doesn't cut it any more.\n\nWhat is the experience out here? Do you have an opinion on either of them, better yet, maybe both? I would like to hear it.", "created_time": "2025-03-26T12:31:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Kitchen-Magician-421", "url": "https://reddit.com/r/microsoft/comments/1jkammt/microsoft_defender_for_endpoint_plan_1_vs/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Problems with Microsoft Learn?", "content": "Hi,\n\nMy Microsoft Learn webpages are not loading, no matter how many times i refresh it.  I'm learning C# and here's an example what a knowledge check page looks like for me: [https://imgur.com/a/3Cg5zIV](https://imgur.com/a/3Cg5zIV) \n\nCan anyone here help me?", "created_time": "2025-03-26T14:08:56", "platform": "reddit", "sentiment": "neutral", "engagement_score": 47.0, "upvotes": 13, "num_comments": 0, "subreddit": "unknown", "author": "villell", "url": "https://reddit.com/r/microsoft/comments/1jkcn1d/problems_with_microsoft_learn/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Microsoft’s account sign-in UI gets a new design and dark mode", "content": "", "created_time": "2025-03-26T17:31:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 65.0, "upvotes": 57, "num_comments": 0, "subreddit": "unknown", "author": "BippityBoppityWhoops", "url": "https://reddit.com/r/microsoft/comments/1jkhgy9/microsofts_account_signin_ui_gets_a_new_design/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Around six weeks ago, I applied to a couple software development jobs at Microsoft, including the \"Neurodiversity Hiring Program\". One application is in review, one is transferred. How long should I keep waiting for?", "content": "Any suggestions for following up?", "created_time": "2025-03-26T17:47:32", "platform": "reddit", "sentiment": "bullish", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Cheetah3051", "url": "https://reddit.com/r/microsoft/comments/1jkhv9d/around_six_weeks_ago_i_applied_to_a_couple/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "Microsoft pulls back from more data center leases in US and Europe, analysts say", "content": "", "created_time": "2025-03-26T18:40:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 67.0, "upvotes": 53, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jkj5xm/microsoft_pulls_back_from_more_data_center_leases/", "ticker": "MSFT", "date": "2025-03-26"}, {"title": "What to expect for your first week at Microsoft?", "content": "I haven't been in touch since I received my start date (3/20). My start date is 3/31, should I expect to get my equipment before my start date? Will I receive instructions for the first day/week sometime this week? Should I just reach out to my hiring manager? Any tips or advice is welcomed. ", "created_time": "2025-03-25T02:20:50", "platform": "reddit", "sentiment": "neutral", "engagement_score": 98.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "rusty919", "url": "https://reddit.com/r/microsoft/comments/1jj8zho/what_to_expect_for_your_first_week_at_microsoft/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "CoPilot = Microsoft just cant get it right", "content": "Copilot on my Windows PC was working great at first. Could talk with it using a microphone and have a semi natural conversation. A few months ago, an update must have occurred and that  stopped working. Could only txt chat with it via the keyboard. \n\nI do have a 365 business license which seems to have caused some conflicts. But why would a free MS Live account have better copilot features than any PAID 365 license? \n\nNow today it will not launch at all. \n\nWhat's going on with MS Copilot?  Too many cooks at the kitchen in MS or are they just unable to do anything right?", "created_time": "2025-03-25T12:20:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "captain_222", "url": "https://reddit.com/r/microsoft/comments/1jji112/copilot_microsoft_just_cant_get_it_right/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "Microsoft AI Tour Resources Now Available for public use", "content": "", "created_time": "2025-03-25T12:23:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "Wireless_Life", "url": "https://reddit.com/r/microsoft/comments/1jji2v5/microsoft_ai_tour_resources_now_available_for/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "How a Microsoft exec managed to pitch Microsoft Word through the genius tactic of being able to actually use it in a 'type-off' demanded by clients: 'I was the only one who'd actually been a secretary'", "content": "", "created_time": "2025-03-25T19:35:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 174.0, "upvotes": 170, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jjs46o/how_a_microsoft_exec_managed_to_pitch_microsoft/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "Microsoft Designer got worse", "content": "If the usage limit wasn't enough today I find it is ignoring the image size specification and always creating square even if set to landscape or portrait.\n\nPlus usual moan about filtering...", "created_time": "2025-03-25T21:22:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 17.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "Mysterious_Peak_6967", "url": "https://reddit.com/r/microsoft/comments/1jjur9m/microsoft_designer_got_worse/", "ticker": "MSFT", "date": "2025-03-25"}, {"title": "What are your top picks among institutional held stocks?", "content": "Hey investing community,\n\nInstitutional investors often have deep research and insights in their investment choices. \n\nWhich institutional held stock you think are the cream of the crop?\n\n  \nHere are a few examples of stocks that are commonly held by institutions:\n\nTechnology sector: $AAPL $MSFT $GOOGL\n\nHealthcare sector: $JNJ $UNH\n\nFinancial sector: $JPM $V\n\nConsumer Goods sector: $PG $KO\n\nIndustrial sector: $GE $CAT\n\nEnergy sector: $XOM $CVX\n\n  \nI've also cultivated my own list of AI stocks:\n\n🔧 AI Infrastructure\n\n\\- Compute & Chips: $NVDA, $AVGO, $AMD\n\n\\- Cloud Computing & AI Models: $MSFT, $GOOG, $AMZN, $ORCL\n\n🤖 AI + Hardware (Smart Device Era)\n\n\\- Robotics & Smart Devices: $TSLA, $META, $SMCI\n\n\\- Chip Design & Specialized Hardware: $SNPS, $GFS\n\n🚀AI Application Layer (Accelerating Commercialization)\n\n\\- Productivity Tools & SaaS: $ADBE, $TTD, $CRWD\n\n\\- Fintech: $PYPL, $V, $MA\n\n\\- Vertical-specific AI: $PLTR, $COIN, $AIFU\n\n🌐 Traditional Enterprises Undergoing AI Transformation (Stealth Growth Opportunities)\n\n\\- Retail & Consumer: $COST, $PDD\n\n\\- Energy & Manufacturing: $XOM, $MPWR\n\n\\- Finance & Logistics: $JPM, $UPS\n\n🚀 Dark Horse Potential: $AIFU\n\nThis under-the-radar  AIdriven insurance & healthcare company could be a major beneficiary if AI truly disrupts the insurance industry.\n\nFeel free to share your picks too!", "created_time": "2025-03-24T09:00:28", "platform": "reddit", "sentiment": "bullish", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Full-Law-8206", "url": "https://reddit.com/r/investing_discussion/comments/1jimbfk/what_are_your_top_picks_among_institutional_held/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "Annoying \"Restore Pages\" popup on Edge", "content": "The \"Restore pages\" popup on <PERSON> appears every time I open the browser.\n\nI know this has been reported thousands of times, and the \"solutions\" include editing the windows registry, or manually tweaking files. \n\n**But the thing is that most of companies that gives us work laptops, and doing these solutions on work laptops or company virtual machines is a no no.** \n\nIs it SO hard to just put a slider to \"Restore pages? Yes/No\" ???\n\nAnd no, this annoying popup doesn't go away with the Esc key, and is not because my computer failed or something, it is shown always. ", "created_time": "2025-03-24T14:23:38", "platform": "reddit", "sentiment": "bearish", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Normal_Helicopter_22", "url": "https://reddit.com/r/microsoft/comments/1jirugs/annoying_restore_pages_popup_on_edge/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "Final round done, no update from recruiter yet – is this normal for Microsoft?", "content": "Hi all, I recently completed all rounds for a **Senior Software Engineer role (L63/L64)** at Microsoft, including a final short round with a senior leader. The last interview was **general/behavioral** and went fairly well in my opinion.\n\nI had my final round on **week before**, and I connected with the recruiter again today, but she said she **still doesn’t have an update**. This is after 5 previous rounds and a hiring call was supposed to happen post-interview.\n\nIs it typical for things to go silent for several days even after all interviews are done? Could this be a sign they’re stalling or waiting on approvals? How long does it usually take for the team to finalize post-final round?\n\nAppreciate any insights from folks who’ve been through the Microsoft hiring loop — or anyone internally who can shed some light on how final decisions are made at this stage.\n\nThanks!\n\nEdit: THEY ARE MOVING AHEAD WITH OTHER CANDIDATE! 🫠\nAlthough it was hire call in loop interview.\n\nEdit Again: They offered me the position.", "created_time": "2025-03-24T17:29:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 40.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Great-Ad-4616", "url": "https://reddit.com/r/microsoft/comments/1jiwezb/final_round_done_no_update_from_recruiter_yet_is/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "If you invested in one share of Microsoft stock when it first became public, it would be worth $128,206 today (including dividends).", "content": "", "created_time": "2025-03-24T18:07:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 173.0, "upvotes": 119, "num_comments": 0, "subreddit": "unknown", "author": "MaxGoodwinning", "url": "https://reddit.com/r/microsoft/comments/1jixdko/if_you_invested_in_one_share_of_microsoft_stock/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "Where to buy Publisher 2021?", "content": "With Publisher being discontinued in 2026, I've been informed by an agent that the 2021 version is the next best thing to get as it won't be affected by the delisting.\n\nIssue is the Office Pro 2021 package costs a fortune:\nhttps://www.microsoft.com/en-us/microsoft-365/p/office-professional-2021/CFQ7TTC0HHJ9?Invisibiliarevelare=true&msockid=1c81a6cd30306d1e243fb24831756ccf&activetab=pivot:overviewtab\n\nSo is there a cheaper way to buy just Publisher 2021 on it's own?", "created_time": "2025-03-24T23:42:23", "platform": "reddit", "sentiment": "bullish", "engagement_score": 7.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "DaveMan1K", "url": "https://reddit.com/r/microsoft/comments/1jj5p2n/where_to_buy_publisher_2021/", "ticker": "MSFT", "date": "2025-03-24"}, {"title": "Headless business applications", "content": "<PERSON><PERSON><PERSON> has gone on the record predicting (the poorly hidden masterplan to combat SFDC) headless BA/CRM. The original thought behind Viva Sales (now Copilot) with Lama<PERSON> fingerprints all over it. He never liked BA as a business pillar. \n\nSFDC replied “<PERSON><PERSON><PERSON> says that because he has to” and deemed it completely unrealistic. No BA user will interact (solely) with the data and logic using an agent.\n\nAnother ‘if you don’t like the game change the rules’ move from MSFT?\n\nWhat do you think?\n\n", "created_time": "2025-03-23T06:59:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "RedditClarkKentSuper", "url": "https://reddit.com/r/microsoft/comments/1jhth3g/headless_business_applications/", "ticker": "MSFT", "date": "2025-03-23"}, {"title": "Microsoft is killing OneNote for Windows 10", "content": "", "created_time": "2025-03-23T07:50:05", "platform": "reddit", "sentiment": "neutral", "engagement_score": 171.0, "upvotes": 93, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jhu570/microsoft_is_killing_onenote_for_windows_10/", "ticker": "MSFT", "date": "2025-03-23"}, {"title": "Does Microsoft require female or LGBTQ candidates to participate in interviews during hiring?", "content": "Hi community, I am having some concerns about the hiring process for SDE role. And could really use some advice on this!\n\nSo the background story is I have been involved in 2 different interview loops for two separate SDE opportunities last year, and the loops are all: all rounds finished  ->  application status became inactive very fast -> no feedback.  \n\nRecently I get connected by the very same hr for new opportunity, which is obviously higher job rank compared to my current working experience, but according to hr they can adjust the job rank based on interviewing rating blablabla. Still I kinda have concerns on whether I am some kind of diversity quota here? and whether to carry on this interview loop since it can be really frustrating to repeat the same failure.\n\nBig thanks!", "created_time": "2025-03-23T15:48:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "kally-gally", "url": "https://reddit.com/r/microsoft/comments/1ji29ry/does_microsoft_require_female_or_lgbtq_candidates/", "ticker": "MSFT", "date": "2025-03-23"}, {"title": "Question about Microsoft Teams", "content": "On Microsoft Teams when you are in a group and you delete a comment can the other people see you deleted it. Like for example if you deleted a comment and it says you deleted it can they see it too? Also, if you add Power Apps to the conversation can the other person see you added power apps to the conversation. ", "created_time": "2025-03-23T18:05:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Gatortheskater96", "url": "https://reddit.com/r/microsoft/comments/1ji5hvl/question_about_microsoft_teams/", "ticker": "MSFT", "date": "2025-03-23"}, {"title": "Transportation to Microsoft in Redmond from First Hill Seattle", "content": "Hi Microsoft employees! I am an incoming intern and have never been to Redmond or Seattle before. Over the summer I will need to commute from the First Hill area (near the central library) in Seattle to Microsoft's campus. I looked up the Microsoft connector bus stops, but would like to ask how reliable the transportation is. The closest connector stop to me is the one at the Starbucks Reserve, how long would you all \"geusstimate\" that it would take me to get from the starbucks reserve to Microsoft Campus? Also, I can't find any pick up times online, all I found was that the it operates from 6:30am-9:30am and 4:30pm-7:30pm?   \n  \nAny help would be greatly appreciated, I am a broke college student and have no clue what I am doing! Thanks guys!", "created_time": "2025-03-23T19:30:24", "platform": "reddit", "sentiment": "bullish", "engagement_score": 12.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Serious_Heat3416", "url": "https://reddit.com/r/microsoft/comments/1ji7ihy/transportation_to_microsoft_in_redmond_from_first/", "ticker": "MSFT", "date": "2025-03-23"}, {"title": "Car advise", "content": "Hello all! Looking for some advice on the possibility of a new car.\n\n37 male, DIN<PERSON>, ~12-15 years from FI and paid off mortgage.\n\nCurrently own a 2017 Rav 4 hybrid, only 52k miles. I put on 7k miles a year and my commute to and from work is just under 9 miles. I have done basic maintenance and gotten tires on it, etc, and otherwise it's fine. It's fully paid off, and I have been putting in 3-5k/year in an s&p type ETF as a 'new car fund' for when it's time, currently about 13k.\n\nMy FIL and wife just got plug in EVs and are pretty high on them. They just got a charger installed at their office (family business, 5 min from home) where my wife would fill my car up once or twice a week (free fuel) . I wanted to look into the possibility of upgrading and jump on the bandwagon but I'm struggling with the decision. \n\nI was looking at a Prius prime xse, and the lease deal is trade in my car (valued at 17k), get 3k back in cash, and no lease payments or interest or taxes/fees (included). The buyout would be 19k in 3 years, which I'd use the new car fund for, and during and after this time I'd continue to put money in (in addition to the 3k). My FIL and wife's argument is that I should get the car because my current cars value and this deal, and the fuel thing, seems to be a good value before my car depreciates further and starts needing higher maintenance costs like a battery. \n\nSo I had chatgpt run some scenarios, which take into account taxes, fees, maintenance, residual value, needing a car at the end of each timeline:\n\"\nFinal Thoughts\nScenario 3 (Prius for 5 yrs then New Car) shows the lowest effective cost ($37,825) and a healthy ending NCF (≈$43K), thanks to a high residual value at 5 years.\n\nScenario 2 (Prius for 10 yrs) has a very competitive total effective cost ($49,250) but a lower ending NCF due to the larger early withdrawal.\n\nScenario 1 (RAV4 for 10 yrs) and Scenario 5 (RAV4 for 15 yrs) preserve more of your NCF, though they require higher effective outlays when you eventually buy a new car.\n\nScenario 4 (Prius for 15 yrs) results in a higher overall cost and a lower NCF, due to extended operating costs and a larger new-car purchase cost at 15 years.\n\"\n\nSome of these scenarios look pretty good but I'm still skeptical. My wife's final argument was that this is the reason I have a new car fund because in the back of my mind I was thinking I'd just and up using it to get me to FI faster. Not to mention the new car is pretty damn sweet.\n\nAny thoughts? I figured this community would give me the best insight. \n\nThanks!", "created_time": "2025-03-23T20:11:25", "platform": "reddit", "sentiment": "bullish", "engagement_score": 58.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Sokolowskierj", "url": "https://reddit.com/r/financialindependence/comments/1ji8hd0/car_advise/", "ticker": "MSFT", "date": "2025-03-23"}, {"title": "This is very unserious.", "content": "Recently my microsoft account was hacked into, i contacted support and gave them all the information they asked me for, they told me theyd contact me on my recovery email i gave them, and then in 24 hrs i get told they have noticed that there was suspicious activity where they changed my email and password, but they are apparently unable to do anything about it? for a company this big, this is very unserious. everything has been provided, and yet i get told this. im insanely dissapointed", "created_time": "2025-03-23T21:41:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 46.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Megabytelul", "url": "https://reddit.com/r/microsoft/comments/1jiala7/this_is_very_unserious/", "ticker": "MSFT", "date": "2025-03-23"}, {"title": "Found a Typo on Microsoft's Download Page", "content": "Hey everyone,\n\nI was on Microsoft's official download page for the Mouse and Keyboard Center 3.0 and noticed a small typo. The description says:\n\n\"Mouse and Keyboard Center helps you personalize and customize **hoe** you work on your PC.\"\n\nI think the word \"hoe\" should be \"how\" for better clarity. It seems like a simple mistake, but I thought I'd point it out here.\n\nHere's the link to the page: [https://www.microsoft.com/en-us/download/details.aspx?id=55977](https://www.microsoft.com/en-us/download/details.aspx?id=55977)", "created_time": "2025-03-22T06:33:24", "platform": "reddit", "sentiment": "neutral", "engagement_score": 13.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "PaymentBrief9916", "url": "https://reddit.com/r/microsoft/comments/1jh2d6x/found_a_typo_on_microsofts_download_page/", "ticker": "MSFT", "date": "2025-03-22"}, {"title": "The Microsoft Excel World Champion Isn’t Worried About Copilot Beating Him (Yet)", "content": "", "created_time": "2025-03-22T13:46:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 54.0, "upvotes": 52, "num_comments": 0, "subreddit": "unknown", "author": "Ok-Inflation5711", "url": "https://reddit.com/r/microsoft/comments/1jh8sce/the_microsoft_excel_world_champion_isnt_worried/", "ticker": "MSFT", "date": "2025-03-22"}, {"title": "Company culture", "content": "For those who happened to have work experience in FAANG companies and Microsoft too, how does company culture feel like in Microsoft compared to other FAANG companies? I know the answer is probably very depending on the boss and team you directly work in but there should be some general perception. \nI was told Amazon / AWS is the most toxic place, Google is the most chill place etc. So I wonder where is Microsoft on the scale. ", "created_time": "2025-03-22T13:59:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 34.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "snailteaser", "url": "https://reddit.com/r/microsoft/comments/1jh91ef/company_culture/", "ticker": "MSFT", "date": "2025-03-22"}, {"title": "Expectations for L63 promotion? Compensation expectations?", "content": "Joined MSFT recently in fed space as L62. Base 170. I am curious what the expected range is if I were to be promoted to L63. A coworker said it's not that much in terms of base but maybe more rsus. Also, will the promotion increase depend on my current base or TC, in case I am in a lower part of band?", "created_time": "2025-03-22T14:10:21", "platform": "reddit", "sentiment": "neutral", "engagement_score": 45.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jh99zs/expectations_for_l63_promotion_compensation/", "ticker": "MSFT", "date": "2025-03-22"}, {"title": "Microsoft should come with a new phone with graphene OS or lineage OS", "content": "That’s an intriguing idea! Combining Microsoft’s reputation for innovation with the lightweight, privacy-focused design of GrapheneOS could potentially create a groundbreaking device. It could appeal to users seeking a secure, efficient, and highly customizable smartphone experience.\n\nTo give strong competition to Android", "created_time": "2025-03-22T18:18:40", "platform": "reddit", "sentiment": "bullish", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Perfect_Reserve_4566", "url": "https://reddit.com/r/microsoft/comments/1jhew6e/microsoft_should_come_with_a_new_phone_with/", "ticker": "MSFT", "date": "2025-03-22"}, {"title": "Microsoft just \"leaked\" an Xbox interface mock up which has Steam game integration | Microsoft's Xbox app is undergoing rapid development ahead of its partner handheld \"Kennan.\" This might be an early look at the next big update.", "content": "", "created_time": "2025-03-22T20:03:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 50.0, "upvotes": 44, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jhhauo/microsoft_just_leaked_an_xbox_interface_mock_up/", "ticker": "MSFT", "date": "2025-03-22"}, {"title": "How to get hired at Microsoft?", "content": "Can anyone give me any tips on getting hired at Microsoft? I just finished my bachelors degree and looking to apply. Any tips?", "created_time": "2025-03-21T04:05:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Separate-Cry3678", "url": "https://reddit.com/r/microsoft/comments/1jg8bh3/how_to_get_hired_at_microsoft/", "ticker": "MSFT", "date": "2025-03-21"}, {"title": "\"Game-Changing Performance Boosts\" Microsoft announces DirectX upgrade that makes ray tracing easier to handle", "content": "", "created_time": "2025-03-21T10:28:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 119.0, "upvotes": 119, "num_comments": 0, "subreddit": "unknown", "author": "Odd-Onion-6776", "url": "https://reddit.com/r/microsoft/comments/1jgdoxd/gamechanging_performance_boosts_microsoft/", "ticker": "MSFT", "date": "2025-03-21"}, {"title": "Skype to teams", "content": "Is it okay that the chat ports from Skype work only on iOS and not on Android, Windows apps, or Teams for the web? I think it's frustrating...", "created_time": "2025-03-21T12:38:02", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jgfto0/skype_to_teams/", "ticker": "MSFT", "date": "2025-03-21"}, {"title": "Investing into a Roth IRA", "content": "I recently opened a Roth and am investing into SPLG SCHD MA V COST and MSFT I don’t have a lot of money to invest every month so I have it set up to put $200 a month. Because I don’t have as much money to put down should I sell the single stocks that I have fractions of? I would sell those and go all into SPLG QQQM and SCHD", "created_time": "2025-03-21T13:37:34", "platform": "reddit", "sentiment": "bearish", "engagement_score": 9.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "ProfessionalJaguar79", "url": "https://reddit.com/r/investing_discussion/comments/1jgh08l/investing_into_a_roth_ira/", "ticker": "MSFT", "date": "2025-03-21"}, {"title": "It’s crazy that windows slows itself down when there’s a update available to force you to update", "content": "I have a laptop that runs 24/7 without issues, but as soon as windows needs to update, it slows to a absolute halt. I sat here for 5 minutes watching it struggle to refresh a Google page and it didn’t even end up doing it at the end, I just gave up and updated it\n\nThis is my system, if I don’t want to update it, it’s my decision, I didn’t pay for this so you could slow it down every time you wanted to patch something, and yes I know there’s safety reasons behind updating but I frankly don’t care bc the laptop just refreshes one Google page all day", "created_time": "2025-03-21T18:17:24", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jgnkch/its_crazy_that_windows_slows_itself_down_when/", "ticker": "MSFT", "date": "2025-03-21"}, {"title": "Microsoft Copilot is not solving/working with uploaded sudoku", "content": "Sometimes I like to play sudoku and I went to solve in \"hard\" mode. \n\nI wanted <PERSON>pi<PERSON> to give me advice for further solving because I got stuck.\n\nI uploaded screenshot of it and it said that cannot talk about it, we should changed the topic. I asked why, it answered that is AGAINST user privacy.\n\nAny ideas?", "created_time": "2025-03-20T12:24:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Fearless-Day7420", "url": "https://reddit.com/r/microsoft/comments/1jfnnr2/microsoft_copilot_is_not_solvingworking_with/", "ticker": "MSFT", "date": "2025-03-20"}, {"title": "CDX", "content": "Does anyone know why Microsoft completely de-invested in CDX, while SDFC at the same does the opposite, invest heavily in partners demo environments?", "created_time": "2025-03-20T16:37:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 18.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "RedditClarkKentSuper", "url": "https://reddit.com/r/microsoft/comments/1jft51o/cdx/", "ticker": "MSFT", "date": "2025-03-20"}, {"title": "Microsoft Workplace Discount Program (used to be Home Use Program)", "content": "Does anyone know if the Microsoft Home Use Program (also known as the Microsoft Workplace Discount Program) is still a thing?  We had this program configured and enabled decades ago so that users could purchase Office at a discounted rate if they had an organizational E-Mail address.  I had forgotten about it through the pandemic and am now checking to see if its still being provided.  I am able to enter my org email address and it sends me a new email saying I'm eligible, with a link to \"Shop now\", but once I click it, a web browser tab opens and just spins endlessly until it finally errors out with \"An error occurred while processing your request.\"", "created_time": "2025-03-20T19:27:19", "platform": "reddit", "sentiment": "neutral", "engagement_score": 53.0, "upvotes": 27, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jfx95k/microsoft_workplace_discount_program_used_to_be/", "ticker": "MSFT", "date": "2025-03-20"}, {"title": "The 11 Microsoft apps I ditch on every new Windows install - and the 11 I keep", "content": "What are your TOP 11 APPS that you keep, and 11 that you would ditch!! ", "created_time": "2025-03-20T20:47:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "LordKrazyMoos<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jfz72f/the_11_microsoft_apps_i_ditch_on_every_new/", "ticker": "MSFT", "date": "2025-03-20"}, {"title": "Why 2025 will be the year for foreign investments", "content": "The S&P 500 has been on a decline since the end of December, while since the same period, foreign equities have steadily increased such as $IEFA, a foreign equity ETF. Based on the decline of major S&P 500 holdings such as Apple, Nvidia, and Microsoft, being overvalued by ~40%, ~280%, and ~6% respectively, https://stockvalu8or.com/screener, is this the year for foreign investments?\n\nFurther, the Allocation of US stocks just had the biggest drop ever due to concerns over stagflation, trade wars, and the end of US exceptionalism leading to a \"bull crash\" in sentiment. Contrastingly, global growth expectations saw the second biggest drop on record, but, at the same time, the allocation of European stocks is the highest since July 2021, with banks becoming the world's favorite sector, according to a survey of 171 participants. \n\nNot to mention that global investors raised their allocation of cash from 3.5% to 4.1%, ending a \"sell signal\" triggered in December, with the speed of the downturn in sentiment being consistent with the end of equity correction.\n\n", "created_time": "2025-03-19T00:31:03", "platform": "reddit", "sentiment": "bearish", "engagement_score": 4.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "duwjwnrbf", "url": "https://reddit.com/r/investing_discussion/comments/1jekk8n/why_2025_will_be_the_year_for_foreign_investments/", "ticker": "MSFT", "date": "2025-03-19"}, {"title": "How long does Microsoft take to review applications?", "content": "I just recently applied to a few different o0en positions within Microsoft. A majority of them, especially the one I'm counting on stopped taking applications about two weeks ago. When I go to check my application status, it still says \"In Review\".          \nI know it's like a mixed back, not having received a rejection email yet, but I'm still so nervous, because the job fits my skills etc perfectly. Normally in these scenarios, I'd call or email them, but I dont really have any contact information.        \n\nSo, does anyone know how long this can typically take them?\n", "created_time": "2025-03-19T03:00:07", "platform": "reddit", "sentiment": "bullish", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ratat-atat", "url": "https://reddit.com/r/microsoft/comments/1jengxk/how_long_does_microsoft_take_to_review/", "ticker": "MSFT", "date": "2025-03-19"}, {"title": "Wiz founder thought leaving Microsoft was the ‘most horrible decision ever.’ Now he’s selling his startup to Google for $32 billion", "content": "", "created_time": "2025-03-19T05:11:35", "platform": "reddit", "sentiment": "bearish", "engagement_score": 267.0, "upvotes": 243, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jepo1c/wiz_founder_thought_leaving_microsoft_was_the/", "ticker": "MSFT", "date": "2025-03-19"}, {"title": "Microsoft walks back the gaming AI it showed off in Overwatch 2 last week, says it will only be added if devs don't think it's \"unfair\"", "content": "", "created_time": "2025-03-19T12:00:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "samiy2k", "url": "https://reddit.com/r/microsoft/comments/1jevb4w/microsoft_walks_back_the_gaming_ai_it_showed_off/", "ticker": "MSFT", "date": "2025-03-19"}, {"title": "Microsoft Certifications locations", "content": "I have both MCP and MCDST certifications, but since [https://mcp.microsoft.com/](https://mcp.microsoft.com/) is no longer available and I have been trying to access them, I tried the Microsoft Learning website but don't see them listed.", "created_time": "2025-03-19T14:51:49", "platform": "reddit", "sentiment": "bullish", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "the_mhousman", "url": "https://reddit.com/r/microsoft/comments/1jeysm6/microsoft_certifications_locations/", "ticker": "MSFT", "date": "2025-03-19"}, {"title": "Levels matching between M and IC", "content": "Hi, I have my eyes on two open roles at Microsoft. One of it is Level M4 the other one is level 64. While compensation is not my only motivation it is important. How do the pay ranges of individual contributor levels match up with the M levels? ", "created_time": "2025-03-19T15:10:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 19.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "snailteaser", "url": "https://reddit.com/r/microsoft/comments/1jez7ty/levels_matching_between_m_and_ic/", "ticker": "MSFT", "date": "2025-03-19"}, {"title": "Microsoft announces new HR executive, company veteran <PERSON>", "content": "", "created_time": "2025-03-19T18:21:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 117.0, "upvotes": 77, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jf3tpa/microsoft_announces_new_hr_executive_company/", "ticker": "MSFT", "date": "2025-03-19"}, {"title": "Sorry, Microsoft has fixed the Copilot automatic uninstall bug", "content": "", "created_time": "2025-03-19T23:37:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 21.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "LordKrazyMoos<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jfba0a/sorry_microsoft_has_fixed_the_copilot_automatic/", "ticker": "MSFT", "date": "2025-03-19"}], "metadata": {"timestamp": "2025-07-06T20:21:25.004637", "end_date": "2025-03-26", "days_back": 7, "successful_dates": ["2025-03-26", "2025-03-25", "2025-03-24", "2025-03-23", "2025-03-22", "2025-03-21", "2025-03-20", "2025-03-19"], "failed_dates": [], "source": "local"}}}}