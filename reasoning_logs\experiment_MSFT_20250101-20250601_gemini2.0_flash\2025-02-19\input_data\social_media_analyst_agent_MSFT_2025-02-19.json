{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-02-19", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Does anyone else fondly look back at the Windows Insider Program from 2014-15?", "content": "2014-15 was when the Windows Insider Program got beta builds of Windows 10. I enrolled on Day 1, I would always download the updates ASAP on the family computer no matter how much it pissed off my sister. I got excited for new builds on Wednesdays and often looked at  the Windows blog as well as winbeta.org before its rebrands. I think it played a big role in my interest in software and tech.", "created_time": "2025-02-19T07:24:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 19.0, "upvotes": 11, "num_comments": 0, "subreddit": "unknown", "author": "TheTwelveYearOld", "url": "https://reddit.com/r/microsoft/comments/1iszuff/does_anyone_else_fondly_look_back_at_the_windows/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft Solutions Partner", "content": "Our company specializes in IT solutions, and providing Infrastructure as a Service (IaaS) and Software as a Service (SaaS). Unlike traditional service providers, we don’t have external customers in a direct sales model; instead, we deliver services on behalf of our customers, functioning as a third-party service provider. We managed our current customers' azure services under our own tenant.\n\nCurrently, we are a legacy Gold member, but Microsoft discontinued renewals as of January 22. We still have access until November 6, 2025, and we are planning to enroll in the Solutions Partner program for either Modern Work (Enterprise) or Infrastructure.\n\nAccording to Microsoft Partner Center, achieving a Solutions Partner designation is measured by performance, skilling, and customer success.\n\nOur situation is that even with significant effort, we could acquire at most five customers. Beyond that, we would not be able to expand our customer base for the foreseeable future.\n\nSince we cannot continuously acquire new customers, meeting the customer success requirements seems impossible. What we would like to understand is: If we don’t bring in new customers, can we still earn customer success points through deployments and usage growth of existing customers for adding new VMs and Azure subscriptions? Would these count toward the performance metrics? Please advise how do we manage to become Microsoft Partner.", "created_time": "2025-02-19T10:24:20", "platform": "reddit", "sentiment": "bullish", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "sleepeezz", "url": "https://reddit.com/r/microsoft/comments/1it2cnb/microsoft_solutions_partner/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Azure AI services for contract analysis", "content": "Hi everyone🖐\n\nI would like to know if an AI agent or automation flow can be created in Azure using Azure AI services, OpenAI services, or any other Azure services to help me with the following:\n\nI have a database—a folder in SharePoint—where I store general terms and conditions of sales, template sales agreements, main contractual provisions, and similar documents.\n\nWhenever I receive agreements or contracts from potential clients, I want them to be automatically compared against the database. The AI should answer my predefined questions, cite the relevant page and paragraph, and generate a report.\n\nHere are some of the questions:\n\n1. Do the provisions on warranty and liability in \\[Agreement A\\] and \\[Agreement B\\] Standard Terms and Conditions deviate from the warranty and liability provisions we typically include in our agreements? What kind of risks result from these deviations?\n2. Do the provisions in the provided agreements deviate from those we usually include in our agreements in any other way that poses a substantial risk to \\[Company X\\]?\n3. Are there any contractual penalties included in \\[Agreement A\\] and \\[Agreement B\\] Standard Terms and Conditions provided by \\[Supplier Y\\]?\n\nI want all of this to be done autonomously using an AI agent.\n\nDoes anyone have any ideas on how this can be achieved in Azure? Also can my logic be improved?", "created_time": "2025-02-19T10:48:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1it2pb6/azure_ai_services_for_contract_analysis/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft is spending $700 million to ramp up security and computing power in Poland | AI infrastructure and cybersecurity are getting a boost from Microsoft", "content": "", "created_time": "2025-02-19T11:42:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 80.0, "upvotes": 76, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1it3jdt/microsoft_is_spending_700_million_to_ramp_up/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft demonstrates working qubits based on exotic physics | Stronger evidence for a hypothetical quasiparticle, plus actual processing hardware.", "content": "", "created_time": "2025-02-19T16:42:20", "platform": "reddit", "sentiment": "bullish", "engagement_score": 192.0, "upvotes": 174, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1it9xrk/microsoft_demonstrates_working_qubits_based_on/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "10-core vs 12-core", "content": "i’m hoping to get opinions ahead of purchasing a new laptop. \ni love the microsoft surface laptop and have used it both personally and professionally in the past. i’m getting a new one for myself at my office which is the Surface Laptop, Copilot+ PC. there is the option for the Snapdragon X Plus (10 Core) or the Snapdragon X Elite (12 core) processor - with about a $250 difference between the two. \ni handle firm business operations and am the executive assistant to our CEO. i love to multitask and am often using multiple programs and have numerous chrome tabs open, but I am not doing things like video editing. i use outlook, dropbox, adobe pro, chrome (for QBO, bank management, general research) most extensively - i also use word, excel, one note and a scanning program on occasion. \n\nshould i be ok if i get the 10 core? or is the additional $250 really worth it for the 12 core?\n\nthanks so much! ", "created_time": "2025-02-19T21:07:02", "platform": "reddit", "sentiment": "bearish", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "heatles22", "url": "https://reddit.com/r/microsoft/comments/1itgmm2/10core_vs_12core/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft releases Muse, an AI model for 3D game environment creation", "content": "", "created_time": "2025-02-19T21:08:56", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 20, "num_comments": 0, "subreddit": "unknown", "author": "dreadpiratewombat", "url": "https://reddit.com/r/microsoft/comments/1itgoc4/microsoft_releases_muse_an_ai_model_for_3d_game/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Why is Microsoft stuff considered bad and bloated, but Google, Apple, and many other stuff is not?", "content": "Why is Microsoft stuff considered bad and bloated, but Google, Apple, and many other stuff is not?\n\nHonestly, I've had this question for a very long while now. Everywhere I go, I always see excessive hate on Microsoft products and software, with people trying to debloat Windows, and while as a tech enthusiast I see the appeal of it, it simply doesn't do much to affect system performance, IMHO. Things like Edge, OneDrive, and other apps from Windows and the Microsoft ecosystem are considered bad, but when it comes to things from Google and Apple, they seem to be praised more, and people don't hate them as much. People actually want them on their system and don't try to remove them. \nWhen it comes to third-party software from other companies, it gets worse. They will praise them like there is no other. I'm sure they're not much better than the other offerings; they serve the same purpose anyways. \nI get everything with it being expensive or having subscriptions or not being customizable, but I'm curious on everybody's reasoning for hating Microsoft products, if you do at all, as I never understood why people do. \nAs someone that has jumped between ecosystems, and eventually settled with a hybrid Google ecosystem, I want to understand the hate for other software. \nI personally don't hate Microsoft apps, they just don't work well for me.\n\ntl;dr: Why the hate for Microsoft products when similar stuff from Google/Apple/others gets praise?\n", "created_time": "2025-02-18T06:17:41", "platform": "reddit", "sentiment": "bullish", "engagement_score": 536.0, "upvotes": 204, "num_comments": 0, "subreddit": "unknown", "author": "former-ad-elect723", "url": "https://reddit.com/r/microsoft/comments/1is6620/why_is_microsoft_stuff_considered_bad_and_bloated/", "ticker": "MSFT", "date": "2025-02-18"}, {"title": "How can I appeal a false-positive Email quarantine without being a MS365 customer?", "content": "My small business' emails (we're on Google Workspace) are being blocked/quarantined by MS365 \\*for Malware\\*. This means that we are not able to email any of our clients who use MS365.\n\nThis is not cold outreach / mass email marketing, we don't and never have done that. Just regular business emails. We don't have malware, as far as any scans have shown.\n\nI have updated our DKIM which Google suggested could have been causing the issue, though I don't actually have any insight into why. The issue started several weeks ago, while I was overseas, so it's not IP related.\n\nOne client asked her tech team to look into it and they found all our emails quarantined and were able to release them. But I don't think this will remedy the issue going forward (new emails will still get blocked) and won't help with any potential new customers, who we won't know whether or not they're receiving our mails as we won't know if they're on MS365.\n\n\\*\\*EDITED TO ADD\\*\\* Another client's IT team has confirmed my mail was blocked for Malware. I've scanned both my laptop and my website and haven't found any sign of malware anywhere.\n\nIs there \\*any\\* way to contact Microsoft / MS365 as a non-user? Or any other way to lodge an appeal against this false positive for our domain?\n\nI've found various help centre listings but they all assume I'm a MS customer. I would be super grateful for any help or advice you could offer.", "created_time": "2025-02-18T15:01:26", "platform": "reddit", "sentiment": "bullish", "engagement_score": 20.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Mysterious_Beach5860", "url": "https://reddit.com/r/microsoft/comments/1isemb8/how_can_i_appeal_a_falsepositive_email_quarantine/", "ticker": "MSFT", "date": "2025-02-18"}, {"title": "Copilot Pro Doesn't have Access to Microsoft Editor, that's Copilot 365", "content": "I pay $20/month for Copilot Pro, which has GPT and image generation. However, that doesn't give me access to the Microsoft Editor to rewrite text. That's the wrong version of Copilot.\n\nI need to subscribe to Microsoft 365 for $30/month for 365 Copilot.\n\nBut I can't cancel my previous subscription because 365 Copilot doesn't have GPT-4 or image generation capabilities.\n\nIf I pay for fancy Copilot, let me do fancy Copilot things!\n\nHow can Microsoft expect the user to understand or care about the differences between these two tools?", "created_time": "2025-02-18T21:55:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "livejamie", "url": "https://reddit.com/r/microsoft/comments/1isos95/copilot_pro_doesnt_have_access_to_microsoft/", "ticker": "MSFT", "date": "2025-02-18"}, {"title": "Seeking Advice on Housing, Finances, and Next Steps—Buying Condo Cash vs. Renting & Investing", "content": "**Body:**\n\nHey everyone,\n\nI’d love some outside perspectives on my financial and life situation. I’m 30M, a full-time law student in my final year, and I have no family or friends in my life—just my girlfriend. I’ll be graduating in a year and plan to start my own business. Admittedly, I’m not very financially literate, and I know there are probably better ways to invest my capital, but the only thing I’ve really done so far is put it into a GIC.\n\nHere’s where I stand financially:\n\n* **Liquid capital:** \\~$480K in a GIC earning 3.05%\n\n* **Real estate:** I own a commercial office unit outright, which used to generate $1,000/month net. The lease recently ended, and I’m trying to re-rent it.\n\n* **Car:** Owned outright\n\n* **Current housing:** Just returned to Toronto from a 3-month trip to Southeast Asia. Sold all my belongings before leaving. I’ve been struggling to secure a rental despite an 840+ credit score, strong bank statements, and references. I was looking at a 1-year lease around $2,200/month but am now considering a cheaper $1,400/month shared unit near a university with parking and utilities included.\n\nFor the past two years, I’ve been wanting to buy a property. Initially, I was looking at condos (1-bed, parking, North York area) but found dealing with real estate agents and mortgage brokers frustrating. I also considered waiting a year and saving while the market cools, hoping to buy a townhouse later with a mortgage, possibly renting out part of it to offset costs.\n\nHowever, now that I’m back, I’m feeling tempted to just **buy a condo outright in cash** (around $450K). I know it’s not the best idea to put all my eggs in one basket, but I’m **so tired** of renting, dealing with roommates, and going through the mortgage/agent/bank process. I’d rather just cut through all that and secure a place to live with peace of mind.\n\nI know a condo isn’t the best investment, and at one point, I viewed real-estate as a wealth-building tool. But at this point, I see my residence as **just that—a place to live, not a money-making asset**. I’m confident I’ll make money in the future through my business, so I’m not relying on my home to generate returns.\n\nStill, I can’t shake the feeling that I might be making an emotional decision rather than a rational one. I’d love to hear some objective perspectives—what would you do in my position? Are there smarter ways I could invest my capital?", "created_time": "2025-02-17T18:12:26", "platform": "reddit", "sentiment": "bullish", "engagement_score": 19.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "Sufficient_Insect812", "url": "https://reddit.com/r/financialindependence/comments/1irqgb3/seeking_advice_on_housing_finances_and_next/", "ticker": "MSFT", "date": "2025-02-17"}, {"title": "The same job  has been posted again with a different job number.", "content": "I applied for a role at Microsoft and successfully completed the technical interviews. The interviewer acknowledged that I did well - i answered all the questions - . I then proceeded to the final behavioral round, which I believe didn’t go as well as I had hoped.\n\nAfter multiple attempts to contact the recruiter, I was eventually informed that I was rejected due to technical skills. However, I was added to the active talent pool.\n\nThe thing is, I don’t believe technical skills were the issue, as I wasn’t asked any technical questions in the final round.\n\nNow, I’ve noticed that the same job has been reposted with a different job number, but the location, title, and requirements remain the same.\n\nIs there any chance I could secure this offer this time? Has anyone been in a similar situation? Any advice would be greatly appreciated!  \nNote: in the Action Center, the status for my previous application is still marked as \"Scheduled.\"", "created_time": "2025-02-17T23:52:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 32.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Walterwhite_234", "url": "https://reddit.com/r/microsoft/comments/1iryssa/the_same_job_has_been_posted_again_with_a/", "ticker": "MSFT", "date": "2025-02-17"}, {"title": "RDUS - <PERSON><PERSON>", "content": "Radius Recycling - RDUS  \n  \nMarket cap $370  \nTangible Book of $540 million  \nEV of $940 million  \nNet debt $400 million with $160 million of operating lease liabilities\n\nTTM operating loss of $83 million. 2021-2022 operating income was circa $200 million annually.\n\nP/Book of 0.68.\n\nEstimate of fair value: **0.9-1X tangible book, with further upside if profitability can get to 2018 or 2021-2022 levels.**\n\n**20-50% upside, possibly 70%+ if profitability gets close to 2018 or 2021-2022 levels**\n\nRadius Recycling is a metal scrapper based in Portland, Oregon, but with scrapping locations in California, Mississippi, Tennessee, Kentucky, Georgia, and Alabama. The two biggest products are \"ferrous scrap\" and \"non-ferrous scrap\" which are metallic scrap processed/recycled from junk - think old cars, railway cars, etc.\n\nFerrous scrap was $370 million in revenue, 56% of Fiscal Q1 2025 revenue of $660 million. The division produced 1.1 million tons of ferrous scrap priced at $338/ton in Q1 2025. Ferrous scrap can be fed into electric arc furnaces (like those at Nucor NUE or Steel Dynamics STLD) to make new steel.\n\nNon-ferrous scrap produced $180 million in revenue, 27% of Q1 2025 revenue. Non-ferrous scrap is dominated by aluminum and copper scrap, so prices mainly off of aluminum and copper pricing.\n\nThe company has also done some vertical integration, and it built its own electric arc furnace steel mill, which can process the company's own scrap. RDUS own EAF produced 125,000 tons of steel, sold at $771 per ton last quarter, for $97 million in revenue, or 15% of total revenue.\n\nThe company had a surge of profitability in 2022 during the strong pricing environment, but if you look over its history, it has been a boom and bust cyclical. It did very well in the pre-2008 industrial metals bull market, and has struggled to make consistent profits since, occasionally doing well like in 2017-2018, then a weak 2019-2020, then a strong 2021-2022, and now an abysmal 2023-2024 cycle.\n\nSo why would it be worth book? A crummy cyclical that can barely earn a 20% ROE in good times and earns a -10-20% ROE in bad times should get a discount to book right?\n\nI think there's a thesis the situation has changed with the latest tariffs.\n\nThe thesis:\n\nThe 25% tariffs on steel and aluminum imports from Trump are likely not going away. IMO, the 25% Canada/Mexico universal tariffs were likely a negotiating chip, but the 25% tariffs on steel from Canada and Mexico are for real.\n\nThe initial tariffs under Trump 1.0 were enacted March 8, 2018 and included a 25% tariff on steel and a 10% tariff on imported aluminum. This led to an improvement in operating margins at Radius to 6%, resulting in over $180 million in operating income. This was despite relatively flat steel scrap prices (priced $300-360 per ton during 2018). This was mainly on the back of higher VOLUMES in steel scrap and capacity additions. That capacity is still available today but has been underutilized.\n\nIn 2019, the tariffs on Canadian and Mexican steel and aluminum were lifted under the USMCA. In 2020 Trump briefly placed on aluminum tariffs back on Canada before pulling them again. Then the Biden admin weakened the impact of the tariffs further through strategic exemptions for Japan, Europe, and the UK, and allowed Chinese shipments of steel as long as it was \"melted and poured\" in the US, Canada, or Mexico. China took great advantage of these re-routing semi-finished steel through Mexico to avoid tariffs, and Biden admin had to crack down again in July 2024: [https://www.swlaw.com/publication/new-tariffs-and-metal-melt-and-pour-requirements-implemented-to-prevent-chinese-circumvention-through-mexico/](https://www.swlaw.com/publication/new-tariffs-and-metal-melt-and-pour-requirements-implemented-to-prevent-chinese-circumvention-through-mexico/)\n\nUltimately, volumes fell at RDUS and then eventually scrap prices went into a deep bear market 2019-2020 where they went to the $200-300/ton range. Furthermore, RDUS had previously sold a lot of scrap from the US to China for processing, and this was effectively shut down in the wake of the 2018 tariffs, so the company had to find alternate buyers, domestically and internationally and volumes suffered.\n\nThis time around, Trump has announced a 25% tariff on all steel AND ALUMINUM imports, with no exemptions for Canada or for semi-finished steel that is \"melted and poured\" in the US. These tariffs will take effect on March 12, 2025. Importantly, this tariff also applies to steel scrap, and does not allow for imports of scrap for EAF processing to get around tariffs. This means that a domestic producer of scrap like RDUS should get a boost.\n\nSteel scrap pricing has already been doing better and has been back in the $300-360/ton range which enabled RDUS to produce good profits in 2018. Combined with tariff effects, I think the volumes should boost and capacity should get fully utilized, pushing the company back into profitability and maybe back into that 10-20% ROE range.\n\nThe company is currently producing around 4 million tons of ferrous scrap per year, and has capacity for 5 million tons. If pricing gets to $360/ton, this could be over $1.8 billion of revenue from the ferrous scrap division alone.\n\nThe downside:\n\nThere is a risk these tariffs could backfire. RDUS still sells about 55% of its scrap internationally for processing, mostly to Bangladesh, Turkey, and India, and they would have to reroute transportation to get their scrap to US EAF mills in the midwest and east coast of the US to take full advantage of the shift these tariffs represent. Since they have a lot of facilities in the Southeast, these may be easier to reroute. There is limited takeaway capacity and higher transport costs from the west coast to the Midwest and East Coast.\n\nAt a P/TBV of 0.68, I think the scrapping plants are already below replacement cost, so there is a limit to how low the pricing gets.\n\nThe biggest issue is the debt, and they have $400 million of debt, most of which is held under a credit facility with an interest rate of over 8% currently. This is a pretty steep cost of financing and they paid over $30 million in interest expenses in the last 12 months on this. They have up to $800 million available on the credit facility, so I don't think there's a major liquidity issue for them on the horizon as long as the bank keeps the facility open.\n\nThey also have operating leases on some of the scrapping facilities, scrapping machinery, and offices, though they do own some proportion outright. Currently carrying value of the operating leases is around $160 million, with an average lease life of 8 years.\n\nThe base case:\n\nI think there's a good case for a re-rating to closer to 0.9-1X book, if the company can get back to profitability on increased volume and a continued fair to strong scrap pricing environment. I've mostly focused on the ferrous scrap environment, but the current tariffs are also much more significant than anything we have seen in aluminum markets, so should really benefit non-ferrous scrap as well. If the company gets to a 0.9-1X book, this would be a market cap of around $480 million, or a $17.30 share price.\n\nI think the primary reason this is overlooked is there is only 1 analyst covering the company nowadays and the conference calls are a ghost town. However, there was a small pop on tariff news and if I am right on the thesis, we should know pretty quickly in the Q2 earnings and conference call.\n\nThe best case:\n\nIf US scrap pricing improves and US EAFs have to ramp up production to overcome reduced imports, US based scrappers could do really well. I think RDUS could get back to the $200 million operating income range. At a 6X EV, that would be around $1.2 billion in EV. After $560 million in debt and operating lease liabilities, that leaves a $640 million market cap, or a $22 share price, compared to the current $12.65 share price, for 74% upside.\n\nAt the $12-13 range, I think its a decent value with some downside protection from replacement cost of the owned scrapping facilities. It has some upside with optionality if things go well in the domestic steel and steel scrap market, as well as domestic non-ferrous scrap markets.", "created_time": "2025-02-16T07:54:15", "platform": "reddit", "sentiment": "bullish", "engagement_score": 21.0, "upvotes": 21, "num_comments": 0, "subreddit": "unknown", "author": "jack<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/SecurityAnalysis/comments/1iqncw1/rdus_radius_recycling/", "ticker": "MSFT", "date": "2025-02-16"}, {"title": "Microsoft Outlook Exploited by FinalDraft Malware for Hidden Communication", "content": "**Elastic Security Labs discovered that new malware called FinalDraft** is exploiting Microsoft Outlook drafts for hidden communication in a cyber-espionage campaign. By blending into Microsoft 365 traffic, attackers avoid detection while targeting a South American ministry.\n\nThe attack begins with PathLoader, which installs the FinalDraft backdoor. Instead of sending actual emails, the backdoor uses Outlook drafts to communicate with the attacker’s infrastructure, hiding commands and responses in draft emails (r\\_<session-id>, p\\_<session-id>). After execution, drafts are deleted, making it difficult to trace. ([View Details on PwnHub](https://www.reddit.com/r/pwnhub/comments/1iqwed7/microsoft_outlook_exploited_by_finaldraft_malware/))", "created_time": "2025-02-16T16:52:05", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON>-<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1iqwhua/microsoft_outlook_exploited_by_finaldraft_malware/", "ticker": "MSFT", "date": "2025-02-16"}, {"title": "Formerly Microsoft was known for good translations...", "content": "[https://i.imgur.com/s0onYiz.png](https://i.imgur.com/s0onYiz.png)\n\nIn Russian version, the menu item says \"Near Microsoft Visual Studio\" instead of \"About Microsoft Visual Studio\".", "created_time": "2025-02-16T20:35:25", "platform": "reddit", "sentiment": "neutral", "engagement_score": 30.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Anuclano", "url": "https://reddit.com/r/microsoft/comments/1ir1vgu/formerly_microsoft_was_known_for_good_translations/", "ticker": "MSFT", "date": "2025-02-16"}, {"title": "Microsoft email/text spam", "content": "today I've gotten a collective 27 emails and texts with the same verification code from Microsoft support verification. anyone else had something like this happen?\n\nedit: I've already changed some security settings and changed my password just in case but this is the first time during the entire time I've had a Microsoft account that something like this has happened", "created_time": "2025-02-16T21:00:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "qasually", "url": "https://reddit.com/r/microsoft/comments/1ir2gku/microsoft_emailtext_spam/", "ticker": "MSFT", "date": "2025-02-16"}, {"title": "Why don't microsoft make Creation Engine their main engine?", "content": "See, creation engine is my favorite engine, I know that the games that use it usually has some bugs(or quite)...but hear me out...\nThe scope of the games bethes<PERSON> did, with the amount of employees they did is quite impressive.\nImagine if all of microsoft gaming started to use it, they could find bugs easier, improve it...all of their games could have built in persistence physics in its itens..and since I mod skyrim(even made some myself) I know you can pretty much create any game in that engine...imagine being able to knock and throw every single item in every single microsoft game and being able to mod all of them at will..\n\nAnd, of course, they wouldnt have to pay royalties to the owners of third partie engines...\n\nAvowed would be more impressive of made in the creation engine for example...\n\nEasier to give every single npc their own life in routine( I heard some people complaining about the npcs in avowed standing around, havent played it yet though)\n\nWhat are your opinions?", "created_time": "2025-02-16T21:43:31", "platform": "reddit", "sentiment": "neutral", "engagement_score": 46.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "MindlessPeanut7097", "url": "https://reddit.com/r/microsoft/comments/1ir3guu/why_dont_microsoft_make_creation_engine_their/", "ticker": "MSFT", "date": "2025-02-16"}, {"title": "Why am I seeing advertisements in my M365 Copilot subscription?", "content": "I pay for the M365 Copilot (previously Microsoft 365). Yet when I work on any app, like Word, I can see advertisements on the top of the app on my phone. It is very distracting. Any idea how to stop the advertisements? Can't I opt out because I am already paying the full amount for the subscription? ", "created_time": "2025-02-15T07:02:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 11.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "powercut_in", "url": "https://reddit.com/r/microsoft/comments/1ipvy56/why_am_i_seeing_advertisements_in_my_m365_copilot/", "ticker": "MSFT", "date": "2025-02-15"}, {"title": "Microsoft Explore Intern Housing (Redmond 2025)", "content": "Hello,\n\nIm going to be a microsoft explore intern (sophomore) this upcoming summer in Redmond. I kind of want to take the lump sum amount ($10k )so Im looking for roommates. I am 20 years old male from Texas A&M University. If anyone is interested or is looking for people to stay with over the summer please lmk. Im not sure if there is like a dedicated place to do this so im just posting here.", "created_time": "2025-02-15T07:39:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "CelestialCrown7", "url": "https://reddit.com/r/microsoft/comments/1ipwfsb/microsoft_explore_intern_housing_redmond_2025/", "ticker": "MSFT", "date": "2025-02-15"}, {"title": "I just applied to the Microsoft Neurodiversity Hiring Program, because I am on the autism spectrum. Should I also apply to regular postings just in case?", "content": "I picked the software engineering position.  Also, I see the Neurodiversity Hiring Program has a relatively few amount of roles.", "created_time": "2025-02-14T00:30:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 69.0, "upvotes": 55, "num_comments": 0, "subreddit": "unknown", "author": "Cheetah3051", "url": "https://reddit.com/r/microsoft/comments/1ioy26t/i_just_applied_to_the_microsoft_neurodiversity/", "ticker": "MSFT", "date": "2025-02-14"}, {"title": "Microsoft Uncovers Sandworm Subgroup's Global Cyber Attacks Spanning 15+ Countries (PwnHub)", "content": "", "created_time": "2025-02-14T07:10:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 14, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON>-<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1ip51q7/microsoft_uncovers_sandworm_subgroups_global/", "ticker": "MSFT", "date": "2025-02-14"}, {"title": "M365 Copilot Price Pressure", "content": "How long until Microsoft will start offering M365 Copilot for free and ditch the $30/month surcharge? The pay-as-you-go model for agents is interesting but for most organizations it adds unneeded complexity. With Googles move to bundle Gemini it is putting major pressure on MSFT to respond. Now you have ChatGPT also offering former subscription services for free. When will MSFT get off their hands and open this thing up? It has to happen. ", "created_time": "2025-02-14T12:37:25", "platform": "reddit", "sentiment": "bullish", "engagement_score": 38.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>y", "url": "https://reddit.com/r/microsoft/comments/1ip9n08/m365_copilot_price_pressure/", "ticker": "MSFT", "date": "2025-02-14"}, {"title": "MS to Open New 1.1mln Sq Ft Office in India", "content": "Good for India. Maybe not so good for US employees and those that want to work there.", "created_time": "2025-02-14T15:26:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 457.0, "upvotes": 239, "num_comments": 0, "subreddit": "unknown", "author": "Illustrious-Mango286", "url": "https://reddit.com/r/microsoft/comments/1ipd10p/ms_to_open_new_11mln_sq_ft_office_in_india/", "ticker": "MSFT", "date": "2025-02-14"}, {"title": "Trying to get on a new path - what would you advise?", "content": "$125K base - tech-ish job (fully remote)  \n$80K net from multifamily real estate (I save or reinvest most of it)  \n\n\nretirement accounts  \n$65K - job 401K  \n$15K - Roth IRA  \n$96K - 401k from old job (need to roll?)  \n  \n$972K - dry powder in mostly money markets/treasuries/other low yield savings  \n\\~$900K - real estate investments (includes subtraction of \\~80K commercial mortgage. not including a home, I don't own one)  \n  \n  \nWife makes about $150K, 50K savings, \\~100K retirement  \nWe have a baby.   \nI am 40.  \n  \nI run the real estate mainly myself with one part-time helper (who lives on site) and the rest through vendors as needed. One commercial building in particular needs a significant overhaul. The units themselves are mostly fine, but the exterior (siding, windows, parking) has been neglected and needs probably $150K invested into it. I've been pretty lazy/overwhelmed getting this going. I used to do this type of work myself, but this is a big project and I'm not sure I'm (physically) up to it. I've considered leaving my job and going back into construction with my siding project as a starting point - and parlaying into GCing/property managment. \n\nI also consider starting my own software studio. I've worked in large companies and startups helping design solutions. I think this could be exciting, but I know I'd need to build it out a team.\n\nMy mental health hasn't been the greatest. I feel like I'm irritable all the time. I had some very significant stressors in my 30's (health, family) and was in a near-constant state of overwhelm and perhaps haven't fully dealt with that. I'm kinda going through the motions. Some of the health issues pulled me away from hobbies/social activities. \n\n  \nI'm not quite sure what to do. I know no one can solve this for me, but I've been in a funk with my job, which is in an industry currently experiencing significant cuts. I work from home so I get to spend a lot of time with my family, which is nice, but otherwise don't have much of a social life. We don't own a home or feel settled - which we talk about a lot but never seem to get going on. Honestly I'm just feeling quite stuck. I have a bunch of cash sitting there, projects that need doing, and am looking at the next half unsure of where to begin. I'm not really sure how to assess how I'm doing, if that makes sense?  \n  \nI would love to hear any thoughts/criticisms and answer any questions as to how I got here or anything else that seems relevant. Thank you for your time. ", "created_time": "2025-02-14T16:33:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ThrowAway_Crash123", "url": "https://reddit.com/r/financialindependence/comments/1ipelgq/trying_to_get_on_a_new_path_what_would_you_advise/", "ticker": "MSFT", "date": "2025-02-14"}, {"title": "I would use Microsoft apps, if they would stop forcing me to use Edge.", "content": "It's really, really annoying.\n\nI'm on Android and I happen to like the Outlook app a lot more than what I like the Gmail app.\n\nI absolutely do not like using edge or chrome.\n\nI use Firefox. And I hate that outlook keeps forcing me to use Microsoft Edge as the browser, whenever I click an extension.\n\nI hope that this is something that somebody eventually brings an antitrust lawsuit against Microsoft for.\n\n", "created_time": "2025-02-14T17:28:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "TxTechnician", "url": "https://reddit.com/r/microsoft/comments/1ipfw3y/i_would_use_microsoft_apps_if_they_would_stop/", "ticker": "MSFT", "date": "2025-02-14"}, {"title": "Thinking of buying Microsoft Home and Business and not 365 because I don`t wanna buy a subscription!", "content": "I am thinking of spending the 250$ to buy office 2024 for home and Bussiness but at the same time I feel its a waist I do  have a windows laptop although. I use but I noticed I use my linux machines more then I do my windows. And I am fine and happy with using LibreOffice which is free is their a reason why using office is better then using say something like LibreOffice?", "created_time": "2025-02-13T01:35:40", "platform": "reddit", "sentiment": "bullish", "engagement_score": 116.0, "upvotes": 32, "num_comments": 0, "subreddit": "unknown", "author": "RecentMonk1082", "url": "https://reddit.com/r/microsoft/comments/1io7rlo/thinking_of_buying_microsoft_home_and_business/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "Onedrive had ruined my personal files!", "content": "A few days ago my system was automatically updated and when I logged in it suggested me to use onedrive to backup things and I just skipped it. But today I found my personal files was uploaded to the onedrive, I don't know how but I must had been induced or deceived to allow it to do so.\n\nThe important thing is, I am using onedrive as a tool to sync my working documents, and created a folder named \"Documents\" to save all my work data. Then I found onedrive have uploaded ALL OF MY FILES IN MY PC'S DOCUMENTS FOLDER, and fixed 'em together with my work data. Of course I don't want my personal files in my work space, I delete them immediatelly from onedrive. But then I realized ONEDRIVE WAS NOT JUST BACKED MY FILES UP, IT JUST MOVED THE ENTIRE DOCUMENTS FOLDER OF MY PC. So when I deleted them from onedrive, I deleted them from everywhere. \n\nThis is so frustrating and makes me extrmely angry, the data I had lost contains so many things for the past decade. And it shouldn't be like this. Microsoft shouldn't be so urgently promoting people to use onedrive while not refining related measures. It should have been a lot more easier to stop the backup progress without any concern of data loss. And people should be informed that when their files were backed up  through onedrive they would become THE ONE AND THE ONLY COPY. Once you delete them from onedrive, you lost them forever.\n\n\n\nP.S. As there're countless softwares in the PC using the Documents folder as their caching space, it's definitely the worst idea you guys have made up to redirect the path of the Documents folder into OneDrive. While my apps are running, Onedrive just won't stop uploading those cached files and running out all my RAMs, it makes my PC stuck as hell. This is so stupid. Please stop this non-sense.", "created_time": "2025-02-13T13:45:28", "platform": "reddit", "sentiment": "bearish", "engagement_score": 39.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "NeXagoS", "url": "https://reddit.com/r/microsoft/comments/1iojcs9/onedrive_had_ruined_my_personal_files/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "You Should Install This Windows Security Patch Right Away", "content": "", "created_time": "2025-02-13T16:32:38", "platform": "reddit", "sentiment": "neutral", "engagement_score": 37.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "sparkblue", "url": "https://reddit.com/r/microsoft/comments/1ion2q3/you_should_install_this_windows_security_patch/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "Unexpected confidence boost", "content": "Just realized the hazard ⚠️ icon by my profile pic in PowerPoint makes me feel badass.  Love it!   Thank you Microsoft! ", "created_time": "2025-02-13T17:13:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "MissMewMews", "url": "https://reddit.com/r/microsoft/comments/1ioo2g8/unexpected_confidence_boost/", "ticker": "MSFT", "date": "2025-02-13"}, {"title": "Azure AI as a bilingual screen reader?", "content": "How is Azure AI's TTS feature for bilingual texts, specifically ones written in both English and Italian? I'm in need of a good screen reader and would appreciate any advice. TIA!", "created_time": "2025-02-12T02:15:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Blissful<PERSON>utton", "url": "https://reddit.com/r/microsoft/comments/1ingakd/azure_ai_as_a_bilingual_screen_reader/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Microsoft Team Matching Odds?", "content": "I recently interviewed for a software engineer I role on some new AI team (I didn’t get a ton of information about the team during interviews) and finished the final round 2 weeks ago. Today I received an email from a recruiter saying this:\n\n“I’ve been able to confirm the results from your interview with us. We do not have an existing role for you at this time. However, you will remain eligible for placement on a new team if headcount becomes available within the next 6 months.”\n\nI was wondering what are the odds that they find a role for me. Not sure if I should keep interviewing or not. I do work as a software engineer for another company so not like it would hurt very much by waiting but I do want to leave my company sometime soon.", "created_time": "2025-02-12T07:19:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 48.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "No_Value1340", "url": "https://reddit.com/r/microsoft/comments/1inlhlg/microsoft_team_matching_odds/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "<PERSON> says he wants to 'turn warfighters into technomancers' as <PERSON><PERSON><PERSON> takes over production of the US Army's IVAS AR headset from Microsoft", "content": "Microsoft will continue to support IVAS functionality with \"advanced cloud infrastructure and AI capabilities,\" but it's out of the hardware game.", "created_time": "2025-02-12T09:35:10", "platform": "reddit", "sentiment": "neutral", "engagement_score": 92.0, "upvotes": 88, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1innag4/palmer_luckey_says_he_wants_to_turn_warfighters/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Windows Upsell (Windows Update)", "content": "The amount of upselling after Windows Updates is getting ridiculous.\n\n[https://ibb.co/album/xm8CqX](https://ibb.co/album/xm8CqX) ", "created_time": "2025-02-12T14:24:17", "platform": "reddit", "sentiment": "bearish", "engagement_score": 64.0, "upvotes": 12, "num_comments": 0, "subreddit": "unknown", "author": "slfyst", "url": "https://reddit.com/r/microsoft/comments/1inryh9/windows_upsell_windows_update/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Question about <PERSON><PERSON><PERSON>", "content": "I do not subscribe to 365.  It doesn't make sense as to why I would subscribe to something when I can just buy it outright, knowing I'll constantly be using it...saves money in the long run.  I bout myself a Microsoft Office license key.\n\n  \nI know they're incorporating copilot into Word 365, but anyone know if it's also available if you bought a license key, rather then 365 subscription ?  After all, I did pay for it, I just went the less expensive option.", "created_time": "2025-02-12T16:37:59", "platform": "reddit", "sentiment": "bullish", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "fieldday1982", "url": "https://reddit.com/r/microsoft/comments/1inv4pu/question_about_copilot/", "ticker": "MSFT", "date": "2025-02-12"}, {"title": "Why do you not want me to like you?", "content": "Why is the Microsoft suite getting less useful with every iteration?  My three pet peeves for today are \n\n* the inability to drag and drop email attachments from Outlook directly to SharePoint online\n   * You wrote both sets of software.  At no time during the development did anyone raise the possibility that uses might want this functionality.  I refuse to believe that a company of this size did not see that possibility and be able to solve this simple task.\n   * Why are you making it so difficult?\n* what happened to Add to Dictionary when spell checking in Word or Outlook?\n   * There is no ability to do that while in the flow of the task.  You have to stop what you're doing and follow a convoluted process of copy/pasting the word into the dictionary?\n   * Why deprecate a feature used by millions and not replace it with something better\n   * Why?\n* In Windows 10 start menu you could mouse over the app icon, and it would show you the last X files opened with that app.\n   * This was great, file opened in one fluid motion.\n   * Windows 11 - GONE.  You need to open the app THE<PERSON> find the file from the recent dialogue. \n   * Recent may or may not include the file you had just saved before lunch.  Random recents?\n\nWhat else have we lost that makes our lives more difficult?", "created_time": "2025-02-12T21:18:23", "platform": "reddit", "sentiment": "bearish", "engagement_score": 28.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "CountryTraining", "url": "https://reddit.com/r/microsoft/comments/1io21tw/why_do_you_not_want_me_to_like_you/", "ticker": "MSFT", "date": "2025-02-12"}], "metadata": {"timestamp": "2025-07-06T21:09:03.134398", "end_date": "2025-02-19", "days_back": 7, "successful_dates": ["2025-02-19", "2025-02-18", "2025-02-17", "2025-02-16", "2025-02-15", "2025-02-14", "2025-02-13", "2025-02-12"], "failed_dates": [], "source": "local"}}}}