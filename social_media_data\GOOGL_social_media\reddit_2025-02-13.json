[{"platform": "reddit", "post_id": "reddit_1io82bm", "title": "Google a good buy", "content": "I have bought calls few days ago thinking that market will come up sanity. If you really look at their earnings and prospects; I feel that it is buy. \n\n\nWanted to see if I should sell this tomorrow or keep it", "author": "Potential-Heat-4336", "created_time": "2025-02-13T01:50:38", "url": "https://reddit.com/r/StockMarket/comments/1io82bm/google_a_good_buy/", "upvotes": 81, "comments_count": 88, "sentiment": "bullish", "engagement_score": 257.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1io8wj7", "title": "$GOOG “This year we’ll begin testing a machine learning-based age estimation model in the U.S.,” wrote <PERSON><PERSON>, SVP of Google’s “Core” Technology team, in the blog post.", "content": "The Core unit is responsible for building the technical foundation behind the company’s flagship products and for protecting users’ online safety.   \n  \n“This model helps us estimate whether a user is over or under 18 so that we can apply protections to help provide more age-appropriate experiences,<PERSON> <PERSON><PERSON><PERSON> wrote.  \n  \nThe latest AI move also comes as lawmakers pressure online platforms to create more provisions around child safety. The company said it will bring its AI-based age estimations to more countries over time. Meta rolled out similar features that uses AI to determine that someone may be lying about their age in September.", "author": "Alternative_East_597", "created_time": "2025-02-13T02:33:07", "url": "https://reddit.com/r/investing_discussion/comments/1io8wj7/goog_this_year_well_begin_testing_a_machine/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iodb1v", "title": "Google removed all reviews from the past month and disabled reviews on Gulf of Mexico", "content": "Reviewing has been disabled on the Gulf of Mexico, with a 'Details' link pointing to a generic information page. Additionally, it seems that reviews from the past month have been deleted in bulk, the last review being one by one '<PERSON><PERSON>' 1 month ago (cannot look up the specific date, thanks Google).\n\nWhat are reviews even for if they get scraped by Google when they decide they do not like them?\n\n[https://imgur.com/a/qmzVPC1](https://imgur.com/a/qmzVPC1)", "author": "alexionut05", "created_time": "2025-02-13T06:52:18", "url": "https://reddit.com/r/GoogleMaps/comments/1iodb1v/google_removed_all_reviews_from_the_past_month/", "upvotes": 5, "comments_count": 15, "sentiment": "neutral", "engagement_score": 35.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iohna9", "title": "Google DOES penalize AI content, period. Running a content marketing consultancy and have seen multiple LEGIT SaaS websites get penalized because of abusing AI e.g. mass-generating blog posts or template pages.", "content": "", "author": "highwayman6677", "created_time": "2025-02-13T12:09:24", "url": "https://reddit.com/r/marketing/comments/1iohna9/google_does_penalize_ai_content_period_running_a/", "upvotes": 57, "comments_count": 7, "sentiment": "neutral", "engagement_score": 71.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ioiqls", "title": "Game Ready Driver 572.42 FAQ/Discussion", "content": "# Game Ready Driver 572.42 has been released.\n\n**Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/avowed-indiana-jones-great-circle-geforce-game-ready-driver/)\n\n**Game Ready Driver Download Link**: [Link Here](https://us.download.nvidia.com/Windows/572.42/572.42-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n**New feature and fixes in driver 572.42:**\n\n**Game Ready** \\- This new Game Ready Driver provides the best gaming experience for the latest new games supporting DLSS 4 technology including Indiana Jones and the Great Circle. Further support for new titles leveraging DLSS technology includes Avowed and Wuthering Waves. In addition, this driver supports the launch of <PERSON>'s Civilization VII.\n\n**Gaming Technology** \\- Adds support for the GeForce RTX 5090 and GeForce RTX 5080 GPUs\n\n**Fixed Gaming Bugs**\n\n* \\[Valorant\\] Game may crash when starting game \\[4951583\\]\n* \\[Final Fantasy XVI\\] PC may freeze when exiting game \\[5083532\\]\n* \\[Delta Force\\] Some PC configurations may experience performance regression when Resizable BAR is enabled \\[5083758\\]\n\n**Fixed General Bugs**\n\n* \\[3DMark DXR Feature Test\\] Unusually low score for Blackwell GPUs \\[5062329\\]\n* Resolved a compatibility issue caused by version mismatches between current and updated dynamic link library files. \\[5081921\\]\n   * **Requires selecting \"Custom (Advanced)\" -> \"Perfor a clean installation\" during the driver installation process** \n*  \n\n**Open Issues**\n\n* Changing state of \"Display GPU Activity Icon in Notification Area\" does not take effect until PC is rebooted \\[4995658\\]\n* \\[VRay 6\\] Unexpected Low Performance on CUDA Vpath Tests for Blackwell GPUs \\[4915763\\]\n\n**Additional Open Issues from** [GeForce Forums](https://www.nvidia.com/en-us/geforce/forums/user/15//557200/geforce-grd-57242-feedback-thread-released-21325/)\n\n* Adobe Substance 3D Painter texture corruption in baking results from GPU raytracing \\[5091781\\] \n* After letting display go to sleep for an extended period, driver may crash when waking up monitor \\[5089560\\] \n* \\[SteamVR\\] Some apps may display stutter on GeForce RTX 50 series \\[5088118\\] \n* \\[Vulkan/DirectX\\] Some apps may display slight image corruption on pixelated 2D patterns \\[5071565\\] \n\n**Driver Downloads and Tools**\n\nDriver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n\nLatest Game Ready Driver: **572.42** WHQL\n\nLatest Studio Driver: **572.16** WHQL\n\nDDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n\nDDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n\n**DDU/WagnardSoft Patreon:** [**Link Here**](https://www.patreon.com/wagnardsoft)\n\nDocumentation: [Game Ready Driver 572.42 Release Notes](https://us.download.nvidia.com/Windows/572.42/572.42-win11-win10-release-notes.pdf) | [Studio Driver 572.16 Release Notes](https://us.download.nvidia.com/Windows/572.16/572.16-win10-win11-nsd-release-notes.pdf)\n\nNVIDIA Driver Forum for Feedback: [Link Here](https://www.nvidia.com/en-us/geforce/forums/user/15//557200/geforce-grd-57242-feedback-thread-released-21325/)\n\n**Submit driver feedback directly to NVIDIA**: [Link Here](https://forms.gle/kJ9Bqcaicvjb82SdA)\n\n[r/NVIDIA](https://new.reddit.com/r/NVIDIA/) Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\nHaving Issues with your driver? Read here!\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue**\n\nThere is only one real way for any of these problems to get solved, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows 10 or 11\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\nIf it still crashes, we have a few other troubleshooting steps but this is fairly involved and you should not do it if you do not feel comfortable. Proceed below at your own risk:\n\n* A lot of driver crashing is caused by Windows TDR issue. There is a huge post on GeForce forum about this [here](https://forums.geforce.com/default/topic/413110/the-nvlddmkm-error-what-is-it-an-fyi-for-those-seeing-this-issue/). This post dated back to 2009 (Thanks Microsoft) and it can affect both Nvidia and AMD cards.\n* Unfortunately this issue can be caused by many different things so it’s difficult to pin down. However, editing the [windows registry](https://www.reddit.com/r/battlefield_4/comments/1xzzn4/tdrdelay_10_fixed_my_crashes_since_last_patch/) might solve the problem.\n* Additionally, there is also a tool made by Wagnard (maker of DDU) that can be used to change this TDR value. [Download here](http://www.wagnardmobile.com/Tdr%20Manipulator/Tdr%20Manipulator%20v1.1.zip). Note that I have not personally tested this tool.\n\nIf you are still having issue at this point, visit [GeForce Forum for support](https://forums.geforce.com/default/board/33/geforce-drivers/) or contact your manufacturer for RMA.\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n\n**Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n* **What does the new Power Management option “Optimal Power” means? How does this differ from Adaptive?** *The new power management mode is related to what was said in the Geforce GTX 1080 keynote video. To further reduce power consumption while the computer is idle and nothing is changing on the screen, the driver will not make the GPU render a new frame; the driver will get the one (already rendered) frame from the framebuffer and output directly to monitor.*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations. The software will not be perfect and there will be issues for some people. For a more comprehensive list of open issues, please take a look at the Release Notes. Again, I encourage folks who installed the driver to post their experience here... good or bad.\n\n*Did you know NVIDIA has a Developer Program with 150+ free SDKs, state-of-the-art Deep Learning courses, certification, and access to expert help. Sound interesting?* [Learn more here](https://nvda.ws/3wCfH6X)*.*", "author": "Nestledrink", "created_time": "2025-02-13T13:12:46", "url": "https://reddit.com/r/nvidia/comments/1ioiqls/game_ready_driver_57242_faqdiscussion/", "upvotes": 219, "comments_count": 967, "sentiment": "bearish", "engagement_score": 2153.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iokgjq", "title": "1000+ Laptops ranked with 3DMark Time Spy (google docs)", "content": "[https://docs.google.com/spreadsheets/d/1iEKpYRElX\\_BUXuQ7a5Yk2jDvEpf7nzTyUjzKwuXuFAE/edit?gid=282014234#gid=282014234](https://docs.google.com/spreadsheets/d/1iEKpYRElX_BUXuQ7a5Yk2jDvEpf7nzTyUjzKwuXuFAE/edit?gid=282014234#gid=282014234)\n\nNow with more informations like specifications of displays, chassis type etc\n\nPage 1 is listing desktop gpus, the others pages are for laptops gpus. Also in the \"Extras\" page, you can easily compare between dekstop and mobile gpus.", "author": "aldeys", "created_time": "2025-02-13T14:39:11", "url": "https://reddit.com/r/nvidia/comments/1iokgjq/1000_laptops_ranked_with_3dmark_time_spy_google/", "upvotes": 2, "comments_count": 4, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iokolw", "title": "Google Maps blocks Gulf of America reviews after rename criticism", "content": "", "author": "Tracker-man", "created_time": "2025-02-13T14:49:07", "url": "https://reddit.com/r/news/comments/1iokolw/google_maps_blocks_gulf_of_america_reviews_after/", "upvotes": 31498, "comments_count": 1653, "sentiment": "neutral", "engagement_score": 34804.0, "source_subreddit": "news", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ioksnw", "title": "AI could be used for a 'bad biological attack from some evil person,' ex-Google CEO <PERSON> warns", "content": "", "author": "MetaKnowing", "created_time": "2025-02-13T14:54:19", "url": "https://reddit.com/r/artificial/comments/1ioksnw/ai_could_be_used_for_a_bad_biological_attack_from/", "upvotes": 59, "comments_count": 31, "sentiment": "neutral", "engagement_score": 121.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ionq6b", "title": "Nancy Pelosi NEW $GOOGL Position 🗞️", "content": "\n<PERSON>’s financial disclosure report shows she purchased 50 call options on Alphabet Inc. (GOOGL) with a $150 strike price, expiring in January 2026. The transaction, valued between $250,001 and $500,000, suggests a bullish stance on Google’s stock price.\n\nPossible Short-Term Impact on GOOGL:\n\t1.\tIncreased Market Attention: Large trades by high-profile figures, especially politicians, often attract retail and institutional investors. This could lead to short-term buying pressure on GOOGL.\n\t2.\tSpeculation on Future Catalysts: <PERSON><PERSON><PERSON>’s stock trades have historically sparked speculation about potential government policies or insider knowledge. Investors might anticipate upcoming favorable developments for Google (e.g., regulatory decisions, AI advancements).\n\t3.\tMomentum Effect: If traders interpret this move as a signal of confidence in Google’s future performance, the stock price could experience short-term upward momentum.", "author": "nerdy-nate", "created_time": "2025-02-13T16:59:58", "url": "https://reddit.com/r/wallstreetbets/comments/1ionq6b/nancy_pelosi_new_googl_position/", "upvotes": 4374, "comments_count": 484, "sentiment": "neutral", "engagement_score": 5342.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ior0do", "title": "Google Maps Won’t Let You Leave Negative Reviews on the Gulf of America", "content": "[https://gizmodo.com/google-maps-wont-let-you-leave-negative-reviews-on-the-gulf-of-america-2000563649](https://gizmodo.com/google-maps-wont-let-you-leave-negative-reviews-on-the-gulf-of-america-2000563649)", "author": "esporx", "created_time": "2025-02-13T19:17:15", "url": "https://reddit.com/r/GoogleMaps/comments/1ior0do/google_maps_wont_let_you_leave_negative_reviews/", "upvotes": 0, "comments_count": 34, "sentiment": "bearish", "engagement_score": 68.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iorpqh", "title": "Mexico’s <PERSON><PERSON><PERSON> to Sue Google Over ‘Gulf of America’ Maps Change", "content": "", "author": "Suspicious-Bad4703", "created_time": "2025-02-13T19:46:36", "url": "https://reddit.com/r/technology/comments/1iorpqh/mexicos_<PERSON><PERSON><PERSON>_threatens_to_sue_google_over/", "upvotes": 39159, "comments_count": 1684, "sentiment": "neutral", "engagement_score": 42527.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iovsmv", "title": "Google Maps has turned off the ability to review the Gulf of “America”", "content": "", "author": "homemade-fruit-salad", "created_time": "2025-02-13T22:43:16", "url": "https://reddit.com/r/google/comments/1iovsmv/google_maps_has_turned_off_the_ability_to_review/", "upvotes": 1049, "comments_count": 280, "sentiment": "neutral", "engagement_score": 1609.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iovtcr", "title": "Mexico threatens to sue Google over Gulf of Mexico name change", "content": "", "author": "ControlCAD", "created_time": "2025-02-13T22:44:10", "url": "https://reddit.com/r/google/comments/1iovtcr/mexico_threatens_to_sue_google_over_gulf_of/", "upvotes": 2456, "comments_count": 180, "sentiment": "neutral", "engagement_score": 2816.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iowbay", "title": "Mexico’s <PERSON><PERSON><PERSON> to Sue Google Over ‘Gulf of America’ Maps Change", "content": "[https://www.bloomberg.com/news/articles/2025-02-13/mexico-s-shein<PERSON>-threatens-to-sue-google-over-gulf-of-america-maps-change](https://www.bloomberg.com/news/articles/2025-02-13/mexico-s-shein<PERSON>-threatens-to-sue-google-over-gulf-of-america-maps-change)", "author": "Healthy_Block3036", "created_time": "2025-02-13T23:06:35", "url": "https://reddit.com/r/GoogleMaps/comments/1iowbay/mexicos_she<PERSON><PERSON>_threatens_to_sue_google_over/", "upvotes": 207, "comments_count": 21, "sentiment": "neutral", "engagement_score": 249.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ioxatq", "title": "[D] We built GenAI at Google and Apple, then left to build an open source AI lab, to enable the open community to collaborate and build the next DeepSeek. Ask us anything on Friday, Feb 14 from 9am-12pm PT!", "content": "Proof: [https://imgur.com/a/kxiTTXP](https://imgur.com/a/kxiTTXP)\n\nTL;DR: Hi 👋 we’re Oumi, an AI lab that believes in an unconditionally open source approach–code, weights, training data, infrastructure, and collaboration—so the entire community can collectively push AI forward. We built a platform for anyone to contribute research in AI. Ask us anything about open source, scaling large models, DeepSeek, and what it takes to build frontier models, both inside and outside of big tech companies. Tell us what is working well in open source AI or what challenges you are facing. What should we work on together to improve AI in the open?\n\n\\-------------\n\nFor years, we worked at big tech (Google, Apple, Microsoft) leading efforts on GenAI models like Google Cloud PaLM, Gemini, and Apple’s health foundation models. We were working in silos and knew there had to be a better way to develop these models openly and collaboratively. So, we built a truly open source AI platform that makes it possible for tens of thousands of AI researchers, scientists, and developers around the world to collaborate, working together to advance frontier AI in a collective way that leads to more efficient, transparent and responsible development. The Oumi platform (fully open-source, Apache 2.0 license) supports pre-training, tuning, data curation/synthesis, evaluation, and any other common utility, in a fully recordable and reproducible fashion, while being easily customizable to support novel approaches.\n\nDeepSeek showed us what open source can achieve by leveraging open-weight models like LLaMA. But we believe AI should be even more open: not just the weights, but also the training data, and the code–make it ALL open. Then go even further: make it easy for anyone to access and experiment, make it easy for the community to work together and collaborate. \n\nSome resources about Oumi if you’re interested:\n\nOur GitHub repo: [https://github.com/oumi-ai/oumi](https://github.com/oumi-ai/oumi)\n\nOur launch story: [https://venturebeat.com/ai/ex-google-apple-engineers-launch-unconditionally-open-source-oumi-ai-platform-that-could-help-to-build-the-next-deepseek/](https://venturebeat.com/ai/ex-google-apple-engineers-launch-unconditionally-open-source-oumi-ai-platform-that-could-help-to-build-the-next-deepseek/)\n\nOur site: [https://oumi.ai/](https://oumi.ai/) \n\nIf you want to collaborate and contribute to community research projects, regardless of where you get your compute, you can sign up at: [https://oumi.ai/community](https://oumi.ai/community). We will be starting with the post-training of existing open models, next, we will be collaboratively pursuing improvements to pre-training. We intend to publish the research with all contributors included as authors.\n\nWe’re here to answer questions about our open source approach, scaling large models, DeepSeek, what it takes to build frontier models both inside and outside of big tech companies, and anything else you all want to discuss.\n\nWe’ll be here Friday, February 14 from 9am-12pm PT / 12pm-3pm ET. Ask us anything.\n\n**Joining us in the AMA:**\n\n* (u/koukoumidis) **Manos Koukoumidis** \\- CEO and Co-founder, ex-Google (Cloud GenAI Lead)\n* (u/oelachqar) **Oussama Elachqar** \\- Co-founder, Engineering, ex-Apple (Health foundation models)\n* (u/MatthewPersons) **Matthew Persons** \\- Co-founder, Engineering, ex-Google (Cloud PaLM & NL Lead)\n* (u/jeremy\\_oumi) **Jeremy Greer** \\- Co-founder, Research, ex-Google (Gemini Alignment)", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-13T23:53:27", "url": "https://reddit.com/r/MachineLearning/comments/1ioxatq/d_we_built_genai_at_google_and_apple_then_left_to/", "upvotes": 162, "comments_count": 69, "sentiment": "neutral", "engagement_score": 300.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}]