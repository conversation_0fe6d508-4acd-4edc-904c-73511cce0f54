#!/usr/bin/env python3
"""
测试新添加的QingYun模型与回测系统的集成
使用call_llm函数验证模型能否正常处理金融分析任务
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.utils.llm import call_llm
from langchain_core.prompts import ChatPromptTemplate

# 加载环境变量
load_dotenv()

class TradingSignal(BaseModel):
    """交易信号模型"""
    signal: str  # "bullish", "bearish", "neutral"
    confidence: int  # 0-100
    reasoning: str

class IntegrationTester:
    """集成测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.api_key = os.getenv("QINGYUN_API_KEY")
        if not self.api_key:
            raise ValueError("❌ QINGYUN_API_KEY环境变量未设置")
        
        # 选择表现最好的模型进行集成测试
        self.test_models = [
            "qwen3-1.7b",      # 响应时间最快
            "ERNIE-Speed-128K", # 响应质量好
            "glm-4-flash"      # 已知在系统中工作良好
        ]
        
        # 创建金融分析提示模板
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", "你是一位专业的金融分析师。请分析给定的股票信息并提供交易建议。"),
            ("human", """请分析AAPL股票的当前情况并返回JSON格式的交易信号。

股票信息：
- 股票代码：AAPL
- 当前价格：$150.00
- 52周高点：$180.00
- 52周低点：$120.00
- 市盈率：25.5
- 成交量：正常

请严格按照以下JSON格式返回：
{{
  "signal": "bullish" or "bearish" or "neutral",
  "confidence": 数字(0-100),
  "reasoning": "详细的分析原因"
}}""")
        ])
        
        print(f"🚀 新QingYun模型集成测试")
        print(f"✅ API密钥已配置")
        print(f"📋 将测试 {len(self.test_models)} 个新模型的系统集成")
        print("-" * 60)
    
    def test_model_integration(self, model_name: str) -> dict:
        """测试单个模型的系统集成"""
        print(f"\n🧪 测试模型集成: {model_name}")
        print("-" * 40)
        
        try:
            start_time = time.time()
            
            # 创建提示
            prompt = self.prompt_template.invoke({})
            
            # 创建默认工厂函数
            def create_default():
                return TradingSignal(
                    signal="neutral",
                    confidence=50,
                    reasoning="系统默认响应：无法获取有效分析结果"
                )
            
            # 使用call_llm函数调用模型
            result = call_llm(
                prompt=prompt,
                model_name=model_name,
                model_provider="QingYun",
                pydantic_model=TradingSignal,
                agent_name="integration_test",
                default_factory=create_default
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # 验证结果
            success = (
                isinstance(result, TradingSignal) and
                result.signal in ["bullish", "bearish", "neutral"] and
                0 <= result.confidence <= 100 and
                len(result.reasoning.strip()) > 10
            )
            
            # 检查是否是默认响应
            is_default = result.reasoning.startswith("系统默认响应")
            
            test_result = {
                "model_name": model_name,
                "success": success and not is_default,
                "response_time": response_time,
                "signal": result.signal,
                "confidence": result.confidence,
                "reasoning_length": len(result.reasoning),
                "is_default": is_default,
                "error": None
            }
            
            if success and not is_default:
                print(f"  ✅ 集成成功 | 耗时: {response_time:.2f}s")
                print(f"  📊 信号: {result.signal} | 置信度: {result.confidence}%")
                print(f"  📝 分析长度: {len(result.reasoning)} 字符")
            elif is_default:
                print(f"  ⚠️  返回默认响应 | 可能存在JSON解析问题")
                print(f"  📝 响应: {result.reasoning[:100]}...")
            else:
                print(f"  ❌ 集成失败 | 响应格式不正确")
            
            return test_result
            
        except Exception as e:
            print(f"  ❌ 集成测试失败: {str(e)}")
            return {
                "model_name": model_name,
                "success": False,
                "response_time": 0,
                "signal": "error",
                "confidence": 0,
                "reasoning_length": 0,
                "is_default": False,
                "error": str(e)
            }
    
    def test_all_models(self):
        """测试所有模型的集成"""
        print(f"🔄 开始系统集成测试...")
        
        results = []
        success_count = 0
        
        for model_name in self.test_models:
            result = self.test_model_integration(model_name)
            results.append(result)
            if result["success"]:
                success_count += 1
            
            # 添加延迟避免API限制
            time.sleep(3)
        
        # 生成报告
        print(f"\n" + "=" * 60)
        print(f"📊 系统集成测试报告")
        print(f"=" * 60)
        print(f"总测试数: {len(results)}")
        print(f"成功数: {success_count}")
        print(f"失败数: {len(results) - success_count}")
        print(f"成功率: {success_count/len(results)*100:.1f}%")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for result in results:
            status = "✅" if result["success"] else "❌"
            default_flag = " (默认)" if result["is_default"] else ""
            print(f"{status} {result['model_name']}{default_flag}")
            print(f"   信号: {result['signal']} | 置信度: {result['confidence']}% | 耗时: {result['response_time']:.2f}s")
            if result["error"]:
                print(f"   错误: {result['error']}")
        
        return results

def main():
    """主函数"""
    try:
        tester = IntegrationTester()
        results = tester.test_all_models()
        
        # 检查集成测试结果
        success_count = sum(1 for r in results if r["success"])
        if success_count > 0:
            print(f"\n🎉 {success_count} 个模型系统集成测试成功！")
            print(f"✅ 新模型已成功集成到AI对冲基金回测系统中")
            print(f"🚀 可以在backtester.py中选择这些模型进行回测")
        else:
            print(f"\n⚠️  所有集成测试都失败了，可能需要检查模型配置或API连接")
            
    except Exception as e:
        print(f"❌ 测试器初始化失败: {e}")

if __name__ == "__main__":
    main()
