[{"platform": "reddit", "post_id": "reddit_1isumx1", "title": "[R] The Curse of Depth in Large Language Models: Are We Scaling in the Wrong Direction?", "content": "\"The Curse of Depth\" paper highlights a fundamental flaw in LLM scaling, past a certain depth, additional layers contribute almost nothing to effective learning.\n\nThe Problem:\n\n* Pre-Layer Normalization (Pre-LN) causes output variance to explode in deep layers.\n* The result? Deep layers lose effective learning capacity, essentially acting as identity functions.\n* This means we’re training deeper models than necessary, wasting compute with layers that aren’t meaningfully improving performance.\n\nIf this is true, it fundamentally challenges the “bigger is always better” assumption in LLM development.\n\nImplications for Model Scaling & Efficiency\n\nIf deep layers contribute diminishing returns, then:\n\nAre we overbuilding LLMs?\n\n* If deep layers aren’t meaningfully contributing, then models like GPT-4, DeepSeek, and Mistral could be significantly optimized without losing performance.\n* This aligns with empirical results showing pruned models maintaining competitive performance.\n\nLayerNorm Scaling Fix – A Simple Solution?\n\n* The paper proposes LayerNorm Scaling to control gradient variance and improve training efficiency.\n* This keeps deeper layers from becoming statistical dead weight.\n\nShould We Be Expanding Width Instead of Depth?\n\n* If deeper layers fail to contribute, then perhaps scaling width (e.g., Mixture of Experts) is the more efficient direction.\n* Transformer scaling laws may need revision to account for this bottleneck.\n\nThis suggests that current LLMs may be hitting architectural inefficiencies long before they reach theoretical parameter scaling limits.\n\n# What This Means for Emergent Behavior & AI Alignment\n\nThis also raises deep questions about where emergent properties arise.\n\nIf deep layers are functionally redundant, then:\n\n* Where is intelligence actually forming? If early and mid-layers are doing all the real work, emergence may be a function of gradient stability, not just scale.\n* Why do LLMs display unexpected reinforcement overrides? Could it be that certain mid-tier layers are forming persistent structures, even as deeper layers become inactive?\n\nIf deep models are just inflating parameter counts without meaningful gains, then the future of AI isn’t bigger, it’s smarter.\n\n# The Bigger Question: Are We Scaling in the Wrong Direction?\n\nThis paper suggests we rethink depth scaling as the default approach to improving AI capabilities.\n\n* If deep layers are underutilized, should we prioritize architectural refinement over raw scale?\n* What does this mean for efficient fine-tuning, pruning strategies, and next-gen transformer architectures?\n* Could this explain certain emergent behaviors as mid-tier layers take on unintended roles?\n\nThe idea that \"bigger models = better models\" has driven AI for years. But if this paper holds up, we may be at the point where just making models deeper is actively wasting resources.\n\n# Final Thought: This Changes Everything About Scaling\n\nIf layer depth scaling is fundamentally inefficient, then we’re already overdue for a shift in AI architecture.\n\n* What do you think? Should AI research move away from deep scaling and focus on better structured architectures?\n* Could this lead to new models that outperform current LLMs with far fewer parameters?\n\nCurious to hear what others think, is this the beginning of a post-scaling era?", "author": "pseud0nym", "created_time": "2025-02-19T02:29:59", "url": "https://reddit.com/r/MachineLearning/comments/1isumx1/r_the_curse_of_depth_in_large_language_models_are/", "upvotes": 9, "comments_count": 33, "sentiment": "bullish", "engagement_score": 75.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1isy62k", "title": "I’ve never really had a plan", "content": "28 y.o. Was making just over 100k for the past 6 years while living at home and invested just about every dollar into one company. I put 270k in and it is currently sitting a little over 500k. I also have a 401k with about 150k. My magic number has always seemed to be $4 million and I think I may hit that in 7-10 years. My question is what does that actually look like in the end? I’ve read about conservative withdrawal rates and I would have no issue living a somewhat frugal life. Would a 3.5% WD rate of $140k be something that is actually realistic? ", "author": "No-Rip-5084", "created_time": "2025-02-19T05:36:54", "url": "https://reddit.com/r/Fire/comments/1isy62k/ive_never_really_had_a_plan/", "upvotes": 60, "comments_count": 31, "sentiment": "neutral", "engagement_score": 122.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1it0mxs", "title": "Google’s new policy tracks all your devices with no opt-out", "content": "", "author": "mWo12", "created_time": "2025-02-19T08:21:53", "url": "https://reddit.com/r/privacy/comments/1it0mxs/googles_new_policy_tracks_all_your_devices_with/", "upvotes": 3419, "comments_count": 343, "sentiment": "neutral", "engagement_score": 4105.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1it5h9d", "title": "POTUS just seized absolute Executive Power. A very dark future for democracy in America.", "content": "The President just signed the following Executive Order:\n\nhttps://www.whitehouse.gov/presidential-actions/2025/02/ensuring-accountability-for-all-agencies/\n\n\"Therefore, in order to improve the administration of the executive branch and to increase regulatory officials’ accountability to the American people, it shall be the policy of the executive branch to ensure Presidential supervision and control of the entire executive branch. Moreover, all executive departments and agencies, including so-called independent agencies, shall submit for review all proposed and final significant regulatory actions to the Office of Information and Regulatory Affairs (OIRA) within the Executive Office of the President before publication in the Federal Register.\"\n\nThis is a power grab unlike any other:\n\"For the Federal Government to be truly accountable to the American people, officials who wield vast executive power must be supervised and controlled by the people’s elected President.\"\n\nThis is no doubt the collapse of the US democracy in real time. Everyone in America has \ngot front-row tickets to the end of the Empire. \n\nWhat does the future hold for the US democracy and the American people.\n\nThe founding fathers are rolling over in their graves. One by one the institutions in America will wither and fade away. In its place will be the remains of a once great power and a people who will look back and wonder \"what happened\"", "author": "chota-kaka", "created_time": "2025-02-19T13:28:58", "url": "https://reddit.com/r/Futurology/comments/1it5h9d/potus_just_seized_absolute_executive_power_a_very/", "upvotes": 66681, "comments_count": 4848, "sentiment": "neutral", "engagement_score": 76377.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1it7jr8", "title": "uBlock Origin no longer supported by Chrome?", "content": "When I opened up Chrome this morning, I was greeted with \"These extensions were turned off because they're no longer supported.\"\n\nuBlock Origin\n\nI absolutely LOVE this plugin. And honestly, this was bound to happen bc the ad blocker was too good, even blocked YouTube ads.\n\nIs there anyway to get this extension to work again? Install in developer mode?! I'm not familiar with Chrome in that way. Was hoping the r/chrome community had some suggestions.", "author": "sco-go", "created_time": "2025-02-19T15:04:23", "url": "https://reddit.com/r/chrome/comments/1it7jr8/ublock_origin_no_longer_supported_by_chrome/", "upvotes": 218, "comments_count": 203, "sentiment": "bullish", "engagement_score": 624.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1itaia1", "title": "MVIS DD BUY NOW!!", "content": "\nBuckle up, cause MicroVision (MVIS) isn’t just another ticker—it’s a ticking time bomb of potential. This is the company that’s been grinding in the shadows, developing cutting-edge LiDAR tech while the rest of the market sleeps. And when it wakes up? Goodbye penny stock, hello Wall Street darling.\n\nWhy MVIS Is a Monster in Disguise\n\t1.\tLiDAR for the Win – Autonomous vehicles are the future, and guess what powers them? LiDAR. And guess who’s been perfecting it while the big dogs fumble around? MVIS. Their long-range LiDAR flexes 250m+ detection range with insane resolution—basically giving self-driving cars superhuman vision. Tesla who?\n\t2.\tPartnerships Incoming? – MVIS has been playing it quiet, but big tech and auto giants are circling. Any sniff of a deal, and this stock goes from micro-cap to mega-cap overnight.\n\t3.\tShort Squeeze Potential – The shorts have been camping on MVIS like it’s their job, but joke’s on them. Low float + heavy short interest = volatility goldmine. If retail catches wind, this thing could go full GameStop 2.0.\n\t4.\tMicrosoft Connection – MVIS has a history with Microsoft (they supply tech for HoloLens). If a full buyout happens? BOOM. We’re flying.\n\t5.\tRetail Army Loves It – MVIS has a cult following that’s just waiting for ignition. The moment volume spikes, expect Twitter, Reddit, and Discord to turn this into the next meme-stock rocket.\n\nThe Risks? Yeah, They Exist, but Who Cares\n\t•\tCash Burn – Yeah, they’re still pre-profit, but that’s how disruptors roll. Amazon lost money for YEARS before taking over the world.\n\t•\tCompetition – Sure, there are bigger players in LiDAR, but MVIS’s tech is arguably better. Once the market wakes up to it, good luck getting in under $10.\n\t•\tPatience Required – This isn’t an overnight flip (unless volume surges), but if you’re looking for a high-risk, high-reward banger, this is it.\n\nThe Play\n\nMVIS under $3? Steal. Under $5? Still cheap. Above $10? Told you so.\n\nIf this gets picked up by the right crowd (retail, hedge funds, or an acquirer), this could be a 5-10x easy. The question isn’t if MVIS will blow up—it’s when. And when it does, you either have your shares, or you’re watching from the sidelines.\n\nTLDR : Strap in(no lesbo), load up(like LODE),and enjoy the ride .", "author": "<PERSON><PERSON><PERSON><PERSON>_", "created_time": "2025-02-19T17:04:37", "url": "https://reddit.com/r/pennystocks/comments/1itaia1/mvis_dd_buy_now/", "upvotes": 127, "comments_count": 93, "sentiment": "bullish", "engagement_score": 313.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1itckdd", "title": "In China, AI-driven robots are ‘evolving at an incredibly fast pace’", "content": "", "author": "MetaKnowing", "created_time": "2025-02-19T18:24:47", "url": "https://reddit.com/r/technews/comments/1itckdd/in_china_aidriven_robots_are_evolving_at_an/", "upvotes": 5, "comments_count": 9, "sentiment": "neutral", "engagement_score": 23.0, "source_subreddit": "technews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1itesj8", "title": "Ublock origin was just killed off on Chrome, just Switch to another browser.", "content": "May these greedy bastard burn in hell. Everything they do has made the internet worse, to the point that the internet is unusable without an add blocker, all to promote shit I cant even afford or AI slop mobile games. If they are willing to go this far I know that they do not intend to change and at this point I don't really care.\n\nI installed firefox and took about an hour to set everything up even better than I had it on chrome. Even if i could not do that I would sooner pay for a premium addblocker than give Google/Youtube any money. ", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-19T19:52:15", "url": "https://reddit.com/r/chrome/comments/1itesj8/ublock_origin_was_just_killed_off_on_chrome_just/", "upvotes": 521, "comments_count": 234, "sentiment": "neutral", "engagement_score": 989.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iti0ex", "title": "Getting the 5090 was genuinely impossible so I used some of the money to upgrade everything else and keep my 4090", "content": "New 9800X3D, TRYX Panorama AIO, Lian Li o11 Vision Compact ", "author": "HD4kAI", "created_time": "2025-02-19T22:03:25", "url": "https://reddit.com/r/nvidia/comments/1iti0ex/getting_the_5090_was_genuinely_impossible_so_i/", "upvotes": 7932, "comments_count": 1193, "sentiment": "neutral", "engagement_score": 10318.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1itil8n", "title": "Tesla Takedown | Take action at Tesla showrooms everywhere.", "content": "", "author": "jimmy_c_korn", "created_time": "2025-02-19T22:27:25", "url": "https://reddit.com/r/environment/comments/1itil8n/tesla_takedown_take_action_at_tesla_showrooms/", "upvotes": 390, "comments_count": 14, "sentiment": "neutral", "engagement_score": 418.0, "source_subreddit": "environment", "hashtags": null, "ticker": "TSLA"}]