[{"platform": "reddit", "post_id": "reddit_1h7ptwq", "title": "Google CEO <PERSON><PERSON> says the progress in AI is \"going to get harder\"", "content": "Google CEO <PERSON><PERSON> says the progress in AI is \"going to get harder\" because \"the low-hanging fruit is gone, the hill is steeper\" and \"you're definitely going to need deeper breakthroughs as we go to the next stage\"\n\n[https://x.com/tsarnick/status/1864474204864958642](https://x.com/tsarnick/status/1864474204864958642)\n\n  \nI saw this quote and it made me think of autonomous vehicles since we know they use AI to drive. It reminds me of what <PERSON><PERSON><PERSON><PERSON> said that it is relatively easy to do a self-driving demo with vision-only end-to-end but actually going from that to safe, reliable L4 is a lot harder. Can we think of the current autonomous driving capabilities as the \"low hanging fruit\" and getting AVs to the next level of safety and reliability will be harder? ", "author": "diplomat33", "created_time": "2024-12-06T01:36:07", "url": "https://reddit.com/r/SelfDrivingCars/comments/1h7ptwq/google_ceo_sun<PERSON>_p<PERSON><PERSON>_says_the_progress_in_ai/", "upvotes": 55, "comments_count": 27, "sentiment": "neutral", "engagement_score": 109.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h7sk9m", "title": "TIL of Wakaliwood, the Ugawood studio that made the 85$ Tarantinoesque hit YouTube film ‘Who Killed <PERSON><PERSON><PERSON>?’", "content": "", "author": "huninnuvna69", "created_time": "2024-12-06T03:56:10", "url": "https://reddit.com/r/todayilearned/comments/1h7sk9m/til_of_wakaliwood_the_ugawood_studio_that_made/", "upvotes": 0, "comments_count": 16, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "todayilearned", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h7sv0o", "title": "Self Running Water Pump, And Others", "content": "You Tube Video--   I make free water pump no need electric power new style;  \n  \nThe 2 side water levels are equal, therefore no siphoning self running effect.  I am assuming there is a Lithium powerful battery inside the DC low voltage pump/motor side housing that is connected to the 2 plugs. The end shaft is also turned ,- the 2 electrical contacts connect, for the plug on the positive side, running the water pump. (acts like a jumper connection)The Pump stops when plugs disconnected,- for open switch state. He uses the drill power to overcome initial inertia , as pump is off when first plugged.  There is also some momentum saved. It takes lots of electricity to run an electric motor Watts of power. This however, could really be a great invention after all. Ac 220v generated same time as pump motor runs.  \n  \n  \n Also, a DC permanent magnet 12v car heater fan motor can be used to run a transformer . Transformer will run on either AC or pulsing on + off DC, but not DC.  By opening housing, snip wire section on each side 180 degrees apart on armature. Put back together. Run on oscilloscope top see pulsing square waves or hook motor to transformer to get a secondary voltage output. There will be ohms resistance in series from the motor wire. Turning shaft will produce on/off signals from wires output- running backwards.  \n  \nBack in the 1980's I saw an air compressor running by itself and used for mowing grass by pushing piston of mower with long hose out the window of trailer.. Retired carpenter had air tank with balanced SPOKED TYPE flywheel on main shaft with reciprocating hydraulic cylinder on journal of compressor shaft. (no motor, wires, battery) A preadjusted vortex tube heat shielded, had increased pressure of heated air to the cylinder keeping crankshaft going. He had 3 machined parts from 3 different machine shops , so as no one knew what final assembly was. 40lbs initially filled tank by hand rotation flywheel, until self powered.  \n  \nMan from Georgia ran his riding lawn mower engine on salt water in carb 2008. See Youtube videos S1R9A9M9 . Hidden 300watt Inverter box in rear hole of cement block. Battery start. battery removed. Engine ran on hydrogen from spark plug electrolysis of salt water. Alternator under flywheel has 12 magnets powering the conversion .Bridge rectifier  about 11 amp draw for inverter to 110v DC and about 4 amps to electromagnet over plug wire to change to ATDC timing for hydrogen. About 15 amps total . Peak pulse current 7-10A, average current about 1A to plug. Self running Briggs 18HP at idle speed. . Tiny electrodes of zero ohms spark plug requires high volts.  \n  \nMr. John Keely of late 1800's machine shop Philadelphia, had demonstrated self running rotating motor mechanism that had flywheel and band saw sawing wood for visitors,  and investors. Hydo pneumatic vacu engine. It had rotating 4 way shock valve that created internal pressure 50 lbs of water hammer.  Table top model is on Youtube that was stolen and later returned to museum. The Patent application showed it's workings. Full Patent not issued because Investor's names not on manuscript submitted. S.V.P. sells copies of old Keely documents.", "author": "Putrid-Bet7299", "created_time": "2024-12-06T04:12:40", "url": "https://reddit.com/r/CleanEnergy/comments/1h7sv0o/self_running_water_pump_and_others/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h7wr4u", "title": "<PERSON><PERSON> drives straight through a car accident scene ", "content": "", "author": "coffeebeanie24", "created_time": "2024-12-06T08:21:37", "url": "https://reddit.com/r/SelfDrivingCars/comments/1h7wr4u/waymo_drives_straight_through_a_car_accident_scene/", "upvotes": 880, "comments_count": 235, "sentiment": "neutral", "engagement_score": 1350.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h7x5us", "title": "[D] Any OCR recommendations for illegible handwriting?", "content": "\nHas anyone had experience using an ML model to recognize handwriting like this? The notebook contains important information that could help me decode a puzzle I’m solving. I have a total of five notebooks, all from the same person, with consistent handwriting patterns. My goal is to use ML to recognize and extract the notes, then convert them into a digital format.\n\nI was considering Google API after knowing that Tesseract might not work well with illegible samples like this. However, I’m not sure if Google API will be able to read it either. I read somewhere that OCR+ CNN might work, so I’m here asking for suggestions. Thanks! Any advice/suggestions are welcomed! ", "author": "SpaceSheep23", "created_time": "2024-12-06T08:53:03", "url": "https://reddit.com/r/MachineLearning/comments/1h7x5us/d_any_ocr_recommendations_for_illegible/", "upvotes": 209, "comments_count": 172, "sentiment": "neutral", "engagement_score": 553.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h7zgvx", "title": "Oh my god is there actually NO WAY to copy coordinates?????", "content": "So due to my work i need to copy and send coordinates (android phone) that i literally just switched to from an iPhone and i realized unlike Google Maps for IOS, the android one does not give you any option whatsoever to copy the coordinates of any point. HILARIOUSLY HORRIBLE and i dont understand why thats available for ios but not on adroid, on their own damn software. Tried literally anything to get coordinates to copy, no option. It’s infuriating. Im about to return this samsung just purely for that single reason 😂\n\n", "author": "Monte666", "created_time": "2024-12-06T11:40:22", "url": "https://reddit.com/r/GoogleMaps/comments/1h7zgvx/oh_my_god_is_there_actually_no_way_to_copy/", "upvotes": 2, "comments_count": 11, "sentiment": "bullish", "engagement_score": 24.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h82jlz", "title": "Looking for the Best Training Resources for SEO, Google Ads, and Meta Ads", "content": "Hi all, I’m looking to become more proficient in digital marketing, particularly in SEO, Google Ads, and Meta (Facebook/Instagram) advertising. Can anyone recommend the best Udemy courses, YouTube channels, or other online platforms for these topics? I'm especially interested in learning about Google Ads Editor and Google Analytics. It would be great if the resources cover both beginner and advanced levels. Appreciate your suggestions and thank you in advance!", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-06T14:27:57", "url": "https://reddit.com/r/DigitalMarketing/comments/1h82jlz/looking_for_the_best_training_resources_for_seo/", "upvotes": 15, "comments_count": 47, "sentiment": "neutral", "engagement_score": 109.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h85nik", "title": "How long do you think <PERSON><PERSON> remains the richest man in the world? Honestly I don’t see anyone passing him for 15+ years", "content": "", "author": "ConstructionRare4123", "created_time": "2024-12-06T16:44:49", "url": "https://reddit.com/r/elonmusk/comments/1h85nik/how_long_do_you_think_elon_musk_remains_the/", "upvotes": 0, "comments_count": 107, "sentiment": "bullish", "engagement_score": 214.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h86rbs", "title": "The new \n@GoogleDeepMind\n model gemini-exp-1206 is crushing it, and the race is heating up. \n\nGoogle is back in the #1 spot 🏆overall and tied with O1 for the top coding model! \n", "content": "", "author": "<PERSON><PERSON><PERSON>", "created_time": "2024-12-06T17:31:32", "url": "https://reddit.com/r/singularity/comments/1h86rbs/the_new_googledeepmind_model_geminiexp1206_is/", "upvotes": 824, "comments_count": 275, "sentiment": "neutral", "engagement_score": 1374.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8a421", "title": "I don't really understand what Google is trying to do in terms of releasing AI to the public, it's the most confusing and clusterf*ck of an exercise in all of AI", "content": "They started with Bard, the worst chatbot ever which they renamed later to Gemini and which now comes with a free Gemini 1.0 version that's still probably the worst free model among all the big tech providers. They have Gemini advanced for paid users which is again arguably the worst paid service among all other providers. For the past year, they have been releasing all their best models on AIstudio. Just in past two months there have been three different models with different dates in their names and no official benchmark or post of any kind explaining what their difference is and why tf would anyone need this many models. Most of the general public don't even know about AIstudio and will never use it. Seems like they are just caught in some competition to game lmsys leaderboard and have no intention of releasing an actual product. Their AI search overview takes the crown as the absolute worst AI product ever and is regularly used as an example of AI slop. \n\nThey failed to capitalize on their only successful products Gemini flash and Notebooklm. The notebooklm leads have now left to form their own startups. Seems to me like a complete and utterly incompetent leadership who have no clue how to make an actual product.", "author": "obvithrowaway34434", "created_time": "2024-12-06T19:53:52", "url": "https://reddit.com/r/singularity/comments/1h8a421/i_dont_really_understand_what_google_is_trying_to/", "upvotes": 29, "comments_count": 61, "sentiment": "neutral", "engagement_score": 151.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h8dr3p", "title": "Need an explanation for this GOOGL call", "content": "The 200 strike 12/27/2024 expiry GOOGL call went down 88% today while the share price increased 1%z. I see the IV is being displayed as 0% and the vega is -8k+.  Is this a bug, or what? ", "author": "early-retirement-plz", "created_time": "2024-12-06T22:35:40", "url": "https://reddit.com/r/options/comments/1h8dr3p/need_an_explanation_for_this_googl_call/", "upvotes": 1, "comments_count": 7, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}]