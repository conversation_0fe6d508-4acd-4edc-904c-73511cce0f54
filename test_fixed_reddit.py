#!/usr/bin/env python3
"""
Reddit凭据测试脚本
使用修复后的凭据进行测试
"""

import os
from dotenv import load_dotenv
import praw

# 清除系统环境变量
reddit_vars = ['REDDIT_CLIENT_ID', 'REDDIT_CLIENT_SECRET', 'REDDIT_USER_AGENT', 'REDDIT_USERNAME', 'REDDIT_PASSWORD']
for var in reddit_vars:
    if var in os.environ:
        del os.environ[var]

# 强制从.env文件加载
load_dotenv(override=True)

# 创建Reddit客户端
reddit = praw.Reddit(
    client_id=os.getenv('REDDIT_CLIENT_ID'),
    client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
    user_agent=os.getenv('REDDIT_USER_AGENT'),
    username=os.getenv('REDDIT_USERNAME'),
    password=os.getenv('REDDIT_PASSWORD')
)

# 测试
try:
    user = reddit.user.me()
    print(f"✅ Reddit认证成功: {user.name}")
    
    # 测试获取帖子
    test_sub = reddit.subreddit('test')
    posts = list(test_sub.new(limit=1))
    print(f"✅ 成功获取 {len(posts)} 个帖子")
    
except Exception as e:
    print(f"❌ 认证失败: {e}")
