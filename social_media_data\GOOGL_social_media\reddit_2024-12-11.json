[{"platform": "reddit", "post_id": "reddit_1hbhr1o", "title": "Data Analytics worth pursuing in 2024 with no experience?", "content": "Hi everyone,\n\nI’m in a bit of a crossroads in life and looking for some honest advice. I’m a 30-year-old with a degree in Statistics from a university in the US, but due to personal struggles (including some mental health challenges I’ve worked hard to address), I’ve been out of the workforce since graduating 8 years ago. Unfortunately, I have no professional experience in my field—or any field, really.\n\nThat said, I’ve recently gotten serious about starting a career, and data analytics has always intrigued me. I enjoy problem-solving, working with numbers, and tools like Python make me feel confident and capable. However, I’ve been struggling with doubts because the job market is obviously brutal right now. Every time I hop on Reddit or LinkedIn, I see posts about people with years of experience and polished portfolios struggling to get interviews. It’s discouraging, to say the least.\n\nI’ve been researching and planning to relearn SQL, Python, Tableau, and Excel over the next few weeks. My goal is to build a strong portfolio, apply to small-to-mid-sized companies, and avoid big tech giants where competition seems overwhelming. But I can’t shake the fear that my lack of experience and the 8-year gap on my resume will make this a lost cause.\n\nSo here’s my question:\n\n* Is it realistic for someone in my situation to break into data analytics in the next year, especially in the current job market?\n* Would upskilling and building a portfolio be worthwhile, or should I pivot to another career path altogether?\n\nI’m open to honest feedback, even if it’s not what I want to hear. I don’t want to waste any more time if this path is a dead end. Thanks so much for taking the time to read this—I’d really appreciate your advice or any words of encouragement (or a reality check).\n\n**TL;DR:** 30, degree in Stats but no work experience, 8-year gap. Want to pursue data analytics but worried the market is too saturated. Is it worth it?", "author": "asols43", "created_time": "2024-12-11T01:13:47", "url": "https://reddit.com/r/analytics/comments/1hbhr1o/data_analytics_worth_pursuing_in_2024_with_no/", "upvotes": 47, "comments_count": 19, "sentiment": "bullish", "engagement_score": 85.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbhxyn", "title": "As a beginner should i straight up just do google ads and analytics certificate or should i do something else first?", "content": "Please dont tell me that you learn from getting a job. I know, as soon as i have a beginner level knowledge base I will start looking for internship actually learn skills and go for a job. However, I dont want to look like i have no idea about what i am doing so what should i do first?", "author": "sunher<PERSON>dke", "created_time": "2024-12-11T01:23:26", "url": "https://reddit.com/r/DigitalMarketing/comments/1hbhxyn/as_a_beginner_should_i_straight_up_just_do_google/", "upvotes": 21, "comments_count": 27, "sentiment": "neutral", "engagement_score": 75.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbnjq7", "title": "Google's <PERSON> Chip Breakthrough: A Game-Changer for Quantum Computing and a Stock Surge", "content": "Google recently made a significant announcement in the world of quantum computing—introducing its Willow chip. This development marks a major step toward the commercialization of quantum computing, a long-anticipated breakthrough in the tech industry. After years of research and exploration, Google appears to have achieved a major milestone that could shift the landscape of computing as we know it.\n\nThe stock market reaction has been swift and dramatic. Alphabet, Google's parent company, saw its stock price soar following the news of the Willow chip breakthrough. But why all the excitement? Quantum computing has the potential to revolutionize a wide range of industries, from artificial intelligence to data processing and cryptography. If Google’s Willow chip lives up to its promise, it could unlock unprecedented computational power, enabling advancements that are currently impossible with classical computing.\n\nFor investors, this is a moment to watch closely. Quantum computing could be the next big thing in tech, and Google is positioning itself at the forefront of this new era. The question is, will this breakthrough drive Alphabet’s growth in the long term, or is the current stock surge just a reflection of short-term hype?\n\nWhile it’s clear that commercial quantum computing is still in its early stages, Google’s success with the Willow chip could pave the way for broader adoption of the technology in the years to come. The company has already established itself as a leader in AI and cloud computing, and quantum computing could be the next big step in its growth strategy.\n\nFor those considering an investment in Alphabet, Google’s progress in quantum computing could represent a strong long-term opportunity. The potential applications of quantum technology are vast, and if Google continues to lead the charge, the company could see significant returns as the technology matures.\n\nAs always, however, there are risks to consider. Quantum computing’s commercial applications are still speculative, and while Google’s announcement is promising, there are no guarantees that the Willow chip will lead to immediate breakthroughs in revenue generation. Nevertheless, for investors with a long-term outlook, this could be the start of an exciting new chapter for Google and the broader tech industry.\n\nIn summary, Google's Willow chip marks an important step forward for quantum computing and has already caused a surge in Alphabet’s stock price. As this technology continues to develop, investors will need to weigh the potential for massive growth against the uncertainty that comes with pioneering a new, uncharted field. It’s definitely a space to watch closely.", "author": "roycheung0319", "created_time": "2024-12-11T06:38:50", "url": "https://reddit.com/r/investing_discussion/comments/1hbnjq7/googles_willow_chip_breakthrough_a_gamechanger/", "upvotes": 3, "comments_count": 2, "sentiment": "bullish", "engagement_score": 7.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbnlxz", "title": "2024 Favourites Chrome Extensions is Released. Do you use any one of them?", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-11T06:43:05", "url": "https://reddit.com/r/chrome/comments/1hbnlxz/2024_favourites_chrome_extensions_is_released_do/", "upvotes": 6, "comments_count": 17, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbnqfg", "title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Quantum Machines Deliver AI-Powered Quantum Computing", "content": "", "author": "MeltingHippos", "created_time": "2024-12-11T06:51:26", "url": "https://reddit.com/r/tech/comments/1hbnqfg/nvidia_rigetti_quantum_machines_deliver_aipowered/", "upvotes": 149, "comments_count": 7, "sentiment": "neutral", "engagement_score": 163.0, "source_subreddit": "tech", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbqx2u", "title": "<PERSON><PERSON><PERSON>, the founder and lead at Google Quantum AI, says Google's new Willow quantum chip is so fast it may be borrowing computational power from other universes in the multiverse.", "content": "", "author": "lughnasadh", "created_time": "2024-12-11T10:54:05", "url": "https://reddit.com/r/Futurology/comments/1hbqx2u/hartmut_neven_the_founder_and_lead_at_google/", "upvotes": 256, "comments_count": 299, "sentiment": "neutral", "engagement_score": 854.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbsxyx", "title": "On this day in 2008, the first non-beta version of Google Chrome was released.", "content": "", "author": "HelloitsWojan", "created_time": "2024-12-11T13:01:32", "url": "https://reddit.com/r/google/comments/1hbsxyx/on_this_day_in_2008_the_first_nonbeta_version_of/", "upvotes": 334, "comments_count": 34, "sentiment": "neutral", "engagement_score": 402.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbvtnd", "title": "Thanks GOOGL", "content": "Must include context.\n\n&nbsp;\n\nGoogle is amazing. Still undervalued.\n\n&nbsp;\n\nBought 160 $190 GOOGL 12/13 calls on 12/10 for  $0.58 premium.\n\nSold the for $4.00.\n\n\n&nbsp;\nIn: $9284.80; Out: $64,000", "author": "spellbreaker", "created_time": "2024-12-11T15:21:15", "url": "https://reddit.com/r/wallstreetbets/comments/1hbvtnd/thanks_googl/", "upvotes": 948, "comments_count": 105, "sentiment": "bullish", "engagement_score": 1158.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbx96v", "title": "Getting Assistant to use Shortcuts correctly...", "content": "I have a shortcut set to a simple Tasker task to activate the heating on my Tesla. The idea being I can say 'Hey <PERSON>, Warm up my car' 10 minutes or so before I leave for work and I get to sit in a nice warm car for the journey.\n\nThe shortcut and task work fine, but if I'm in my house, the assistant will default to responding on my google mini speaker in my office, which then tells me that the shortcut I've asked for 'only works if used on an android phone with that app'. This happens even if I speak the Hey Google into my phone to trigger the shortcut, and even if im out of earshot of the mini speaker. It seems like the phone is pushing the request into the speaker, which then can't activate the shortcut as it can't get hold of <PERSON><PERSON> on my phone. \n\nIs there any way to tell assistant to not push this shortcut trigger over to the mini speaker? I like that it does it for playing music etc, but not for this. \n\nAny help appreciated.", "author": "TKOS7", "created_time": "2024-12-11T16:23:21", "url": "https://reddit.com/r/google/comments/1hbx96v/getting_assistant_to_use_shortcuts_correctly/", "upvotes": 1, "comments_count": 0, "sentiment": "bearish", "engagement_score": 1.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hbxgm9", "title": "Director of Data Science & Analytics - AMA", "content": "I have worked at companies like LinkedIn, Pinterest, and Meta. Over the course of my career (15+ years) I've hired many dozens of candidates and reviewed or interviewed thousands more. I recently started a podcast with couple industry veterans to help people break in and thrive in the data profession. I'm happy to answer any questions you may have about the field or the industry.\n\nPS: Since many people are interested, the name of the podcast is Data Neighbor Podcast on YouTube", "author": "Shoddy-Still-5859", "created_time": "2024-12-11T16:32:07", "url": "https://reddit.com/r/analytics/comments/1hbxgm9/director_of_data_science_analytics_ama/", "upvotes": 585, "comments_count": 249, "sentiment": "neutral", "engagement_score": 1083.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hc0kit", "title": "<PERSON><PERSON> gets stuck in a roundabout loop ", "content": "looks like it’s having fun ", "author": "coffeebeanie24", "created_time": "2024-12-11T18:39:53", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hc0kit/waymo_gets_stuck_in_a_roundabout_loop/", "upvotes": 880, "comments_count": 150, "sentiment": "neutral", "engagement_score": 1180.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hc0pq8", "title": "GOOGL Call 180 expiring next week", "content": "Today Google is +5% and my call is +100% (700 USD profit). What would you do? I have the cash to buy the shares. Better to sell the call today, or let it expire next week and buy 100 shares of GOOGL at 180?", "author": "ebolognesi", "created_time": "2024-12-11T18:45:57", "url": "https://reddit.com/r/options/comments/1hc0pq8/googl_call_180_expiring_next_week/", "upvotes": 12, "comments_count": 47, "sentiment": "bullish", "engagement_score": 106.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hc5pg4", "title": "In contrast to OAI, the new Google model passes the analog clock test", "content": "", "author": "Jolly-Ground-3722", "created_time": "2024-12-11T22:16:14", "url": "https://reddit.com/r/singularity/comments/1hc5pg4/in_contrast_to_oai_the_new_google_model_passes/", "upvotes": 796, "comments_count": 137, "sentiment": "neutral", "engagement_score": 1070.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hc7oy8", "title": "GOOGL has room for further growth or wait for dip?", "content": "Ok GOOGL has been on a discount for too long. The new Quantum business segment gives room for nice things but Q business is debatable about when it’s going to boom (not if).\n\nThinking of opening a major position tomorrow or wait for some profit taking on Friday closure, since we had 2 straight green days?\n\nWhat do you think about it for the next year catalysts ? Legal and monopoly decompositions etc? ", "author": "mneymaker", "created_time": "2024-12-11T23:46:36", "url": "https://reddit.com/r/ValueInvesting/comments/1hc7oy8/googl_has_room_for_further_growth_or_wait_for_dip/", "upvotes": 0, "comments_count": 33, "sentiment": "bullish", "engagement_score": 66.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hc7q6k", "title": "Tixati - a closed-source torrent client for Linux and Windows, just released their \"very new and somewhat experimental\" build for Android devices", "content": "", "author": "foundfootagefan", "created_time": "2024-12-11T23:48:18", "url": "https://reddit.com/r/Android/comments/1hc7q6k/tixati_a_closedsource_torrent_client_for_linux/", "upvotes": 15, "comments_count": 13, "sentiment": "neutral", "engagement_score": 41.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}]