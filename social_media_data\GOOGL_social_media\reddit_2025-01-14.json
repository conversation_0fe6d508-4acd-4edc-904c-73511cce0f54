[{"platform": "reddit", "post_id": "reddit_1i0y4v2", "title": "Pixels just need iPhone like face unlock", "content": "I recently got a work iPhone for an internship, and the only feature that I think is better than my Pixel is Face ID. It's smoother, works in low light and doesn't need me to look right at it. Obviously, I know that iPhone's have dedicated hardware, whereas Google just uses the front camera and AI, but if Google adds dedicated hardware like on the Pixel 4, I'll be a happy camper. ", "author": "F-35Nerd", "created_time": "2025-01-14T04:34:31", "url": "https://reddit.com/r/GooglePixel/comments/1i0y4v2/pixels_just_need_iphone_like_face_unlock/", "upvotes": 16, "comments_count": 166, "sentiment": "neutral", "engagement_score": 348.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i12kr8", "title": "I just did the step: Chrome is no longer my browser", "content": "You may love Chrome, but you have to agree that during these past few years chrome has mistreated us, limiting what we can or we cannot do with OUR browser.\n\nRandomly changing its interface, limiting our customization, etc.\n\nI've stayed with chrome during all of those painful times since it's launch. And with forcing manifest V3, i've reached the point where it's just more convenient for me to just change browser, so this is my last post using chrome.\n\nI just wanted to express my pain for losing the app that i've been using since 2008 and i cannot longer recognice or love, and i'm certain some of you are in the same situation.\n\nThe promises of freedom and customization it brought on its release, and the thing it has become.\n\nCongratulatios, google, you made the planets of the apes ending real.", "author": "Cubitera", "created_time": "2025-01-14T09:45:16", "url": "https://reddit.com/r/chrome/comments/1i12kr8/i_just_did_the_step_chrome_is_no_longer_my_browser/", "upvotes": 455, "comments_count": 150, "sentiment": "bullish", "engagement_score": 755.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i13opp", "title": "I'm trying to start a software company, but Google won't let me in", "content": "Last month they closed my Google Play account with 9 apps. I think it was because I was competing with some publishers. None of my apps were deleted and I didn't get a warning. Google is really laundering money from this business. I am sure it has happened to many people here. Now we need to unite as developers. I won't stop until this is solved. What do you suggest? The best I could do was open an X post. I've listed some fair rules for the Play Store. ", "author": "bugrevealingbme", "created_time": "2025-01-14T11:04:31", "url": "https://reddit.com/r/startups/comments/1i13opp/im_trying_to_start_a_software_company_but_google/", "upvotes": 0, "comments_count": 12, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i17ydv", "title": "How did you get your site to rank #1 or 2 on Google?", "content": "I’m curious what worked to get a top ranking for your small business", "author": "tacosurfbike", "created_time": "2025-01-14T15:01:25", "url": "https://reddit.com/r/smallbusiness/comments/1i17ydv/how_did_you_get_your_site_to_rank_1_or_2_on_google/", "upvotes": 17, "comments_count": 59, "sentiment": "neutral", "engagement_score": 135.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i196lv", "title": "Frustrated as a Data Analyst: Are we just storytellers?", "content": "I’ve worked in five different roles in the data field, and across most companies, I’ve noticed a common trend: data analysts are primarily tasked with producing dashboards or generating figures based on very specific business requests. However, when it comes to tackling broader, more open-ended questions, things seem to get more challenging—especially in companies where Python isn’t part of the toolkit.\n\nIn my current company, for example, we’re expected to find new insights regularly, but everything is done using SQL and Tableau. While these tools are fine for certain tasks, doing deeper data exploration with them can feel tedious and limiting. We’re also not encouraged to use statistical knowledge at all, since no one on the team, including our boss, has a statistical background. It feels like there’s no understanding or value placed on applying more advanced techniques. We just need to have exceptional data storytelling skills + put up some nice figures which confirm already known intuitions.\n\nHonestly, I’m feeling a bit frustrated. I can’t help but wonder if this is common across the field or if it’s just the nature of certain industries or companies. Would things be different in a more tech-focused company or in a dedicated data science role?\n\nWhat’s your experience with this? Is this a frequent issue in your work as well, or does it vary depending on the company or team? I’d love to hear your thoughts.", "author": "Mountain_Sky_2419", "created_time": "2025-01-14T15:56:27", "url": "https://reddit.com/r/analytics/comments/1i196lv/frustrated_as_a_data_analyst_are_we_just/", "upvotes": 179, "comments_count": 96, "sentiment": "neutral", "engagement_score": 371.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}]