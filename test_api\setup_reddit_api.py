#!/usr/bin/env python3
"""
Reddit API配置设置工具

帮助用户设置Reddit API凭据并测试连接
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv, set_key
import praw
import prawcore

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("Reddit API配置设置工具")
    print("=" * 60)

def get_reddit_credentials():
    """获取Reddit API凭据"""
    print("\n📋 Reddit API凭据设置")
    print("请访问 https://www.reddit.com/prefs/apps 创建应用")
    print("选择 'script' 类型的应用\n")
    
    client_id = input("请输入Client ID: ").strip()
    client_secret = input("请输入Client Secret: ").strip()
    
    print("\n可选设置 (可直接回车跳过):")
    user_agent = input("User Agent (默认: AI-Hedge-Fund-Bot/1.0): ").strip()
    if not user_agent:
        user_agent = "AI-Hedge-Fund-Bot/1.0"
    
    username = input("Reddit用户名 (可选): ").strip()
    password = input("Reddit密码 (可选): ").strip()
    
    return {
        'REDDIT_CLIENT_ID': client_id,
        'REDDIT_CLIENT_SECRET': client_secret,
        'REDDIT_USER_AGENT': user_agent,
        'REDDIT_USERNAME': username if username else None,
        'REDDIT_PASSWORD': password if password else None
    }

def save_to_env_file(credentials):
    """保存凭据到.env文件"""
    env_file = Path('.env')
    
    print(f"\n💾 保存配置到 {env_file}")
    
    for key, value in credentials.items():
        if value:  # 只保存非空值
            set_key(env_file, key, value)
            print(f"✓ 已设置 {key}")
    
    print("✓ 配置已保存到.env文件")

def test_reddit_connection(credentials):
    """测试Reddit API连接"""
    print("\n🔍 测试Reddit API连接...")
    
    try:
        reddit = praw.Reddit(
            client_id=credentials['REDDIT_CLIENT_ID'],
            client_secret=credentials['REDDIT_CLIENT_SECRET'],
            user_agent=credentials['REDDIT_USER_AGENT'],
            username=credentials.get('REDDIT_USERNAME'),
            password=credentials.get('REDDIT_PASSWORD')
        )
        
        # 测试基本连接
        print("测试基本连接...")
        subreddit = reddit.subreddit('stocks')
        print(f"✓ 成功连接到 r/stocks (订阅者: {subreddit.subscribers:,})")
        
        # 测试获取帖子
        print("测试获取帖子...")
        posts = list(subreddit.hot(limit=5))
        print(f"✓ 成功获取 {len(posts)} 个热门帖子")
        
        # 显示示例帖子
        if posts:
            post = posts[0]
            print(f"\n📄 示例帖子:")
            print(f"标题: {post.title[:50]}...")
            print(f"作者: {post.author}")
            print(f"分数: {post.score}")
            print(f"评论数: {post.num_comments}")
        
        print("\n✅ Reddit API连接测试成功！")
        return True
        
    except prawcore.exceptions.ResponseException as e:
        print(f"❌ API响应错误: {e}")
        return False
    except prawcore.exceptions.OAuthException as e:
        print(f"❌ 认证错误: {e}")
        print("请检查Client ID和Client Secret是否正确")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n📚 使用示例:")
    print("="*40)
    
    examples = [
        "# 基本使用 - 收集2025年1-6月数据",
        "python reddit_live_collector.py",
        "",
        "# 指定股票和时间范围",
        "python reddit_live_collector.py --tickers AAPL MSFT NVDA --start-date 2025-01-01 --end-date 2025-03-31",
        "",
        "# 增量更新 - 收集昨天到今天的数据",
        "python reddit_live_collector.py --incremental",
        "",
        "# 指定子版块",
        "python reddit_live_collector.py --subreddits stocks investing wallstreetbets",
        "",
        "# 自定义输出目录",
        "python reddit_live_collector.py --output-dir my_reddit_data",
        "",
        "# 集成到AI对冲基金系统",
        "python src/backtester.py --tickers AAPL MSFT NVDA --use_local_social_media"
    ]
    
    for example in examples:
        print(example)

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")

    required_packages = ['praw', 'python-dotenv', 'tqdm']
    missing_packages = []

    # 检测是否在Poetry环境中
    poetry_env = os.getenv('POETRY_ACTIVE') or 'poetry' in sys.executable.lower()
    if poetry_env:
        print("🔍 检测到Poetry环境")

    for package in required_packages:
        try:
            # 尝试导入包
            if package == 'python-dotenv':
                import dotenv  # noqa: F401
            else:
                __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package} (缺失)")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n⚠️  需要安装缺失的包:")
        if poetry_env:
            print(f"poetry add {' '.join(missing_packages)}")
        else:
            print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ 所有依赖包已安装")
    return True

def main():
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺失的依赖包，然后重新运行此脚本")
        return 1
    
    # 加载现有环境变量
    load_dotenv()
    
    # 检查是否已有配置
    existing_client_id = os.getenv('REDDIT_CLIENT_ID')
    if existing_client_id:
        print(f"\n🔍 检测到现有Reddit配置")
        print(f"Client ID: {existing_client_id[:8]}...")
        
        choice = input("是否重新配置? (y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("使用现有配置进行测试...")
            credentials = {
                'REDDIT_CLIENT_ID': os.getenv('REDDIT_CLIENT_ID'),
                'REDDIT_CLIENT_SECRET': os.getenv('REDDIT_CLIENT_SECRET'),
                'REDDIT_USER_AGENT': os.getenv('REDDIT_USER_AGENT', 'AI-Hedge-Fund-Bot/1.0'),
                'REDDIT_USERNAME': os.getenv('REDDIT_USERNAME'),
                'REDDIT_PASSWORD': os.getenv('REDDIT_PASSWORD')
            }
            
            if test_reddit_connection(credentials):
                show_usage_examples()
                return 0
            else:
                print("现有配置测试失败，请重新配置")
    
    # 获取新凭据
    try:
        credentials = get_reddit_credentials()
        
        if not credentials['REDDIT_CLIENT_ID'] or not credentials['REDDIT_CLIENT_SECRET']:
            print("❌ Client ID和Client Secret是必需的")
            return 1
        
        # 保存到.env文件
        save_to_env_file(credentials)
        
        # 测试连接
        if test_reddit_connection(credentials):
            show_usage_examples()
            print("\n🎉 Reddit API配置完成！现在可以开始收集数据了")
            return 0
        else:
            print("\n❌ 配置测试失败，请检查凭据是否正确")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n配置被用户中断")
        return 1
    except Exception as e:
        print(f"\n配置过程中出错: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
