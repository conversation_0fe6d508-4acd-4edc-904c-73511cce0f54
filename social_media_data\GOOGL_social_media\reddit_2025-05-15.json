[{"platform": "reddit", "post_id": "reddit_1kn6mj0", "title": "Web Search is still Google's Moat & I bought some shares today", "content": "Been reading all GOOG & GOOGL posts here for months. Haven't placed an order simply because of uncertainties around Google like DoJ split, Waymo as well as LLM Threat to search business.\n\nI don't know anything about first two. But I run some tests on several LLMs to build something like AI Agent.\n\nThe task is to list each and every website related to a company. And rank the links so that official lists come on top of the list.\n\nTests used ChatGPT Deep Search, Perplexity Pro, Calude 3.7 Sonnet, Deepseek DeepThink R1, Grok Deeper Research, QWEN QWEN3-235B-A22B, Mistral Web Search, Manus High-Effort. And Gemini Deep Research with Flash 2.5.\n\nQuick conclusion is that Gemini performed way better than others. Brought approximately 60 links. All active. Some of them are relevant PDFs. Went above and beyond the task assigned.\n\nChatGPT came second with 16 links. All good. But nothing extraordinary. Also took much longer than Gemini run.\n\nThe rest of the results full of hallucinations, dead or broken links, missing critical links etc.\n\nSo I know Google has been disappointing with AI endeavors recently. \n\nYet their search know-how is so huge. And Google has the ability to use the latest and greatest data on web.\n\nI expect a \"Last mover advantage\" for Google when it comes to LLMs.\n\nI placed a small order of GOOG and GOOGL today. Because my little test convinces me they will be around for some time.\n\nThe question is, how much time?\n\nWell, that's personal. My investment horizon is 17 years or more. The shares are for my son who was born last year. The stocks go to my coffee can portfolio.\n\nIt is harder to predict short-term movements, do your homework before taking action.", "author": "conquistudor", "created_time": "2025-05-15T12:12:38", "url": "https://reddit.com/r/ValueInvesting/comments/1kn6mj0/web_search_is_still_googles_moat_i_bought_some/", "upvotes": 31, "comments_count": 54, "sentiment": "neutral", "engagement_score": 139.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kn7o5b", "title": "How can I retire early?", "content": "Hey 👋 I (m31) lurk the finance subs but never posted until now. I’d like some insight if you have it!\n\nI’m a software engineer with a 140k salary. I have ~245k in investment accounts (stocks, bonds), 26k in an HYSA, 65k in an IRA, and just bought a 590k house (230 down, mortgaged 360, 6.1% rate). Other than the mortgage, I have no other debt (own my car outright and never let a credit card balance roll over).\n\nI dream of retiring early (45ish?) but I’m not delusional - I know I’ll need to make some changes in order for that to happen. I’m looking at how I can use my $245k to start generating passive income.\n\nMy gf (f26) is entering a 4 year family med residency this summer. It’ll pay 65-70k yearly. After that her expected income will be a little north of 200k. I don’t mind leaning on her income to help, and neither does she, but I’d like to try to get as much done with my own cash as I can.\n\nShould I be looking into rentals? Dumping it all in dividends? YOLO’ing on Google (/s)?", "author": "itsyoboytroy", "created_time": "2025-05-15T13:03:23", "url": "https://reddit.com/r/FinancialPlanning/comments/1kn7o5b/how_can_i_retire_early/", "upvotes": 9, "comments_count": 21, "sentiment": "bearish", "engagement_score": 51.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1knc8n4", "title": "how to turn off \"search with google lens\" for image search when right-clicking?", "content": "i've disabled everything i could find in the flags using \"lens\", but still even after relaunching the \"search with google lens\" options shows instead of \"search images with google\" like how it used to before.", "author": "redditfanfan00", "created_time": "2025-05-15T16:15:35", "url": "https://reddit.com/r/chrome/comments/1knc8n4/how_to_turn_off_search_with_google_lens_for_image/", "upvotes": 10, "comments_count": 13, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1knf19b", "title": "With Google's AlphaEvolve, we have evidence that LLMs can discover novel & useful ideas", "content": "", "author": "MetaKnowing", "created_time": "2025-05-15T18:07:07", "url": "https://reddit.com/r/artificial/comments/1knf19b/with_googles_alphaevolve_we_have_evidence_that/", "upvotes": 51, "comments_count": 10, "sentiment": "neutral", "engagement_score": 71.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1knl8ab", "title": "Google hits 150 million users for subscription service with help of AI", "content": "https://www.reuters.com/business/google-hits-150-million-users-subscription-service-with-help-ai-2025-05-15/\n\n> SAN FRANCISCO, May 15 (Reuters) - Alphabet's Google One subscription service, which charges consumers for cloud storage and artificial intelligence features, recently crossed 150 million subscribers, the company told Reuters.\n\n> That represents a 50% increase since February 2024, when Google One crossed 100 million subscriptions nearly six years after the service launched.\n\n> The same month, Google introduced a $19.99 a month plan with access to AI capabilities unavailable for free users. The company continues to offer Google One subscription tiers for file storage, but without most AI features, at lower prices.\n\n> The new AI tier accounted for \"millions\" of subscriptions, according to <PERSON><PERSON><PERSON>, a vice president at Google in charge of the subscription service.\n\n> Google One is part of Alphabet's effort to diversify beyond advertising, which accounted for more than three-quarters of its $350 billion in overall 2024 revenue.\n\n> Alphabet's success with subscriptions could play a key role in its long-term financial outlook as it grapples with the threat of AI chatbots, like OpenAI's ChatGPT or Google's own Gemini, to its search engine stronghold.", "author": "joe4942", "created_time": "2025-05-15T22:24:49", "url": "https://reddit.com/r/stocks/comments/1knl8ab/google_hits_150_million_users_for_subscription/", "upvotes": 279, "comments_count": 68, "sentiment": "bullish", "engagement_score": 415.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}]