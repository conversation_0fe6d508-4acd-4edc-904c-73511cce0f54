[{"platform": "reddit", "post_id": "reddit_1koheq4", "title": "iOS Google Maps App Re-Routing Issue", "content": "Is anyone having an issue with iOS Google Maps app not re-routing after missing a turn? I am running the most updated version as of 5/16/2025. \n\nI haven’t seen a setting to turn re-routing on/off. Only quick fix it to exit the navigation mode, search for the location again and start navigation. \n\nTo get the re-routing back I have to delete the app and reinstall but then it goes back to no re-routing again. \n", "author": "ESDventures", "created_time": "2025-05-17T01:33:38", "url": "https://reddit.com/r/GoogleMaps/comments/1koheq4/ios_google_maps_app_rerouting_issue/", "upvotes": 50, "comments_count": 69, "sentiment": "neutral", "engagement_score": 188.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1koi17l", "title": "Google Ads and Meta are the safe channels but which channels do you use that others are sleeping on?", "content": "Ex. I have used Criteo, Microsoft ads, and Verizon ads with success depending on the vertical. ", "author": "Bboy486", "created_time": "2025-05-17T02:07:48", "url": "https://reddit.com/r/PPC/comments/1koi17l/google_ads_and_meta_are_the_safe_channels_but/", "upvotes": 162, "comments_count": 50, "sentiment": "neutral", "engagement_score": 262.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1koqmma", "title": "Are ACWI/FTSE Global funds really the best place to park your money for the next 10 years? They are based on market market-cap-weighted.", "content": "World Index funds prioritise the Mag7 plus other U.S. household juggernauts by their market capitalisation. These companies while very profitable are seemingly overpriced based on their high PE ratios. Many analysts have predicted the S&P growth to be low for the next decade.\n\n1. Do we really expect Apple, Google, Meta, Amazon, Microsoft, Visa to double/triple in size in the next ten years in an environment that is already saturated with ETF inflows over the last decade? Sure, they will continue to \"do well\"?\n2. Or are we banking on Ai wildly disrupting commerce. Eventually Ai will disrupt but will it be as wild as our imaginations?\n3. How are we expecting new companies to challenge the dominance of the large US companies with massive moats? Wouldn't these new companies have to get up to market capitalisation of the Mag7 then double/triple?\n4. Outside of the promise of Ai not sure U.S growth will be achieved organically but another way is for large companies to swallow small and mid-cap upstarts. Even these upstarts seem overpriced\n\nSo are ACWI/FTSE Global funds now flawed by the market-cap-weighted approach? Or is growth going to come by new upstarts working their way up S&P index charts? Should we be using these index funds but diluting their impact with by adding more weighting to other indexes, say Europe, Asia? IS it time to slide over to Factor-Based \n\n(disclaimer: not asking anyone to predict the future)", "author": "dropptop", "created_time": "2025-05-17T11:21:05", "url": "https://reddit.com/r/FinancialPlanning/comments/1koqmma/are_acwiftse_global_funds_really_the_best_place/", "upvotes": 0, "comments_count": 7, "sentiment": "bullish", "engagement_score": 14.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1koqsda", "title": "looks like someone at Google made an oopsie!", "content": "", "author": "samurai-coder", "created_time": "2025-05-17T11:31:00", "url": "https://reddit.com/r/youtube/comments/1koqsda/looks_like_someone_at_google_made_an_oopsie/", "upvotes": 236, "comments_count": 27, "sentiment": "neutral", "engagement_score": 290.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1korllj", "title": "Google Ads not making even impressions or going on/off randomly", "content": "I'm doing a search campaign for my independent financial advisor business.\n\nAge >35 (also tried with no limits or >35 with unknown enabled)\n\nRadious 20 Km around Rome (also tried 100 Km)\n\nSegments: financial planning, investment services and bank services. (also tried without but they're set on observing anyway)\n\nSchedule: Mon-Fri 06.00-00.00 (also tried 24/7)\n\nOffering: conversions (also tried max click but nothing changes)\n\nBudget: 50€/day. 10€ CPC/conversion cost (also tried with CPC/conversion cost 2€)\n\nAnnouncement quality: Google says \"excellent\"\n\nKeywords: \\[financial advisor\\], \\[independent financial advisor\\], \\[financial consulting\\], \"name surname\", \"website\" (i'm translating from my native language but you got it). Exact match on around 10 super related keywords.\n\nI had some good calls/mails the first 1-2 days, then DIED. Some days i don't make even impressions. Others i do for half of the budget. 90% of the time i don't make even impressions.\n\nIdeas?", "author": "Curious_Aerie_7016", "created_time": "2025-05-17T12:16:56", "url": "https://reddit.com/r/marketing/comments/1korllj/google_ads_not_making_even_impressions_or_going/", "upvotes": 1, "comments_count": 3, "sentiment": "bullish", "engagement_score": 7.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kow659", "title": "Fix Slow and Laggy Google Chrome on Mac: The Ultimate Guide", "content": "If Google Chrome is running super slow and laggy on your Mac, I’ve got you covered with a quick, easy guide to fix it. I’ve rounded up the best solutions that worked for me and many other users, pulling together tips from various posts into one place for you. \n\nIf there are more solutions, I’ll make sure to add them here.\n\n# 1. Enable Hardware Acceleration\n\n1. Open Chrome and go to **Settings** (click the three-dot menu in the top-right corner, then select **Settings**).\n2. Scroll down and click **System**.\n3. Ensure the toggle for **Use hardware acceleration when available** is **On**.\n4. Restart Chrome by clicking **Relaunch**.\n\n# 2. Disable All Extensions\n\n1. In Chrome, go to the three-dot menu and select **Extensions > Manage Extensions**.\n2. Toggle off all extensions.\n3. Use Chrome for a while to check if performance improves.\n4. If the lag is gone, re-enable extensions one by one, testing performance each time, to identify any problematic extensions.\n\n# 3. Optimize Chrome Flags for Performance\n\n1. In Chrome’s address bar, type `chrome://flags`.\n2. Search and enable the following flags:\n   * **GPU Rasterization**: Offloads rendering tasks to the GPU.\n   * **Override Software Rendering List**: Forces GPU acceleration.\n   * **Zero-copy Rasterizer**: Improves rasterization efficiency.\n3. Click **Relaunch** to apply changes.\n\n# 4. Reinstall Chrome with the .pkg Installer\n\n1. Quit Chrome completely.\n2. Go to **Applications**, drag Chrome to the **Trash**, and empty the Trash.\n3. Visit [https://chromeenterprise.google/download/?modal-id=download-chrome#mac-download](https://chromeenterprise.google/download/?modal-id=download-chrome#mac-download).\n4. Download the **.pkg** installer (not the .dmg).\n5. Run the .pkg file and follow the installation prompts.\n6. Open Chrome and sign in to restore your settings.\n\n", "author": "emilio_aat", "created_time": "2025-05-17T15:51:22", "url": "https://reddit.com/r/chrome/comments/1kow659/fix_slow_and_laggy_google_chrome_on_mac_the/", "upvotes": 31, "comments_count": 7, "sentiment": "neutral", "engagement_score": 45.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kp1l56", "title": "Google I/O next week - what to expect?", "content": "This was posted and deleted today by a googler, I’m really excited for next week. I’m also assuming other AI Labs will try to attend at one upping Google so at the end of the day, we (the users) are all winning 😂", "author": "KlutzyAnnual8594", "created_time": "2025-05-17T19:47:49", "url": "https://reddit.com/r/singularity/comments/1kp1l56/google_io_next_week_what_to_expect/", "upvotes": 877, "comments_count": 160, "sentiment": "neutral", "engagement_score": 1197.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kp3zaw", "title": "My first PC build ever", "content": "Lian Li 011D  Evo XL\nRTX 5090 Astral \n9800x3d \nHydroshift LCD 360tl \n32GB DDR5 RAM 6000Mhz 30CL\nAsus TUF B850 Plus wifi \n2 * 360 SL reverse blade fans \n1*120 SL fan \nAsus ROG Strix 1000w platinum psu(3.1 ATX)\n\n\nBeen researching for a gaming pc for a year. Both prebuilts and parts. Took the plunge last week with the 5090 and then ordered the rest of the parts shortly after. Went to stores like NBB, Cyberport and Caseking (in Germany) to have it assembled but they said they will only do it if I order the parts with them. It took me multiple days for all the parts to arrive, and watched a ton of YouTube videos but eventually, everything was in, I closed it up and plugged it in. I was super nervous as this is my first build and beyond basic google searches, if things go sideways, i would not know what to do. So I hit the power, everything lit up but bam, had an message on the screen \"New CPU installed, fTPM NV corrupted or fTPM NV structure changed\" error. <PERSON> skipped a couple of beats and a lot of googling and fiddling around with a couple of errors and issues but finally got it now full working. What a card! ", "author": "ConflictGrand6373", "created_time": "2025-05-17T21:35:48", "url": "https://reddit.com/r/nvidia/comments/1kp3zaw/my_first_pc_build_ever/", "upvotes": 2130, "comments_count": 378, "sentiment": "bearish", "engagement_score": 2886.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}]