[{"platform": "reddit", "post_id": "reddit_1k1rt92", "title": "Google rep called client trying to convince them to reactivate campaigns", "content": "A year and a half ago I worked with a client. They had a logistics and warehousing business and we had success in November and December with Black Friday, but things got slow in the new year, so we paused campaigns around March. No bad blood. It was just a slow part of the year and it didn't make sense to keep ads running. We both agreed we'd work together in the future.\n\nAnyways, we finally reconnected earlier this week to talk about a new project they're working on and they told me that shortly after we paused Google had called them and told them that we hadn't done a good job and they should reactivate.   \n  \nIn fairness, we're an old school agency and our approach is very antithetical to what Google recommends, often using all exact match keywords, fine-tuned audience targeting and manual CPC.   \n  \nEven so, we got results and the client told me they were really aggressive with them about turning their campaigns back on and letting Google manage them.\n\nI often suspected that things like this were happening, I had gotten calls before after pausing campaigns, but I didn't realize they were pushing such aggressive narratives to get clients to spend more money.   \n  \nI thought this was pretty devious and worth warning others about, both on the agency side and client side.\n\nWatch out folks! 👀", "author": "cole-interteam", "created_time": "2025-04-18T00:16:34", "url": "https://reddit.com/r/PPC/comments/1k1rt92/google_rep_called_client_trying_to_convince_them/", "upvotes": 25, "comments_count": 36, "sentiment": "neutral", "engagement_score": 97.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k1w71f", "title": "Google’s Gemini 2.5 Flash introduces ‘thinking budgets’ that cut AI costs by 600% when turned down", "content": "", "author": "PrincipleLevel4529", "created_time": "2025-04-18T04:13:57", "url": "https://reddit.com/r/artificial/comments/1k1w71f/googles_gemini_25_flash_introduces_thinking/", "upvotes": 112, "comments_count": 16, "sentiment": "neutral", "engagement_score": 144.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k21aw1", "title": "I keep hearing that if Google or Meta are forced to break up into separate smaller entities that would create shareholder value and that it would end up being good for investors. Is that true ? If so, why ?", "content": "Hello,\n\nOver the past few days I keep reading how Google and Meta might end up having to divest or break up into separate smaller independent companies. A lot of people kept saying that this might result in more shareholder value because the individual companies \"would be worth more separate than under the same umbrella\".\n\nCan someone more familiar with the topic share their view on this subject ?\n\nGiven that so many of Google's and Meta's products are so interconnected won't breaking the companies up be detrimental ?\n\nThanks !", "author": "iyankov96", "created_time": "2025-04-18T10:03:37", "url": "https://reddit.com/r/investing/comments/1k21aw1/i_keep_hearing_that_if_google_or_meta_are_forced/", "upvotes": 64, "comments_count": 43, "sentiment": "neutral", "engagement_score": 150.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k21mg3", "title": "Google's bad year just got worse", "content": "", "author": "AktienKopfi2025", "created_time": "2025-04-18T10:25:07", "url": "https://reddit.com/r/wallstreetbets/comments/1k21mg3/googles_bad_year_just_got_worse/", "upvotes": 1636, "comments_count": 288, "sentiment": "neutral", "engagement_score": 2212.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k22p74", "title": "arXiv moving from Cornell servers to Google Cloud", "content": "", "author": "sh_tomer", "created_time": "2025-04-18T11:31:56", "url": "https://reddit.com/r/MachineLearning/comments/1k22p74/arxiv_moving_from_cornell_servers_to_google_cloud/", "upvotes": 265, "comments_count": 21, "sentiment": "neutral", "engagement_score": 307.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k234ol", "title": "Live demo at TED2025, computer scientist <PERSON><PERSON> debuts Google’s prototype smart glasses, powered by the new Android XR system", "content": "[https://x.com/TEDTalks/status/1912890077094547494](https://x.com/TEDTalks/status/1912890077094547494)", "author": "Nunki08", "created_time": "2025-04-18T11:56:36", "url": "https://reddit.com/r/singularity/comments/1k234ol/live_demo_at_ted2025_computer_scientist_s<PERSON><PERSON><PERSON>/", "upvotes": 810, "comments_count": 166, "sentiment": "neutral", "engagement_score": 1142.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k256cs", "title": "If the market falls 0.20%, it'll be the worst market year in 45 years.", "content": "I've collected market data of the worst days in the market overall from 1980 (that's google's max limt) to 2025. These are overall worst market days since inception, so it includes dot com bubble, 2008, black monday, 2020 covid crash etc. Whatever days are worse it'll show that, the most minimum number of all the years. \n\nIt looks like if the market falls another .2%, it'll be the worst performance of the market in 45 years.  \n", "author": "Iwubinvesting", "created_time": "2025-04-18T13:39:25", "url": "https://reddit.com/r/StockMarket/comments/1k256cs/if_the_market_falls_020_itll_be_the_worst_market/", "upvotes": 19634, "comments_count": 1107, "sentiment": "bearish", "engagement_score": 21848.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k267bz", "title": "How do you price in the regulatory risk affecting big tech? (namely Google)", "content": "So I know there are 1 trillion posts on Google.\n\nI always thought it was undervalued below 2tn, and that future earnings would have been higher and higher.\n\nNow that it is back below 2tn I lost most of my gains, but I am not happy for the buying opportunity. While AI companies are both a partner and a competitor, I feel like the US (states) governament(s) and the DOJ are commited to harming the company.\n\nThe rulings are in my opinion unfair and regulators get emboldend by every court decision. It seems that it isn't about one or the other rules being violated, deep down they don't want tech giants to exist. There is also a risk of retaliation against big tech but thatìs another story.\n\nI think the company is amazing and could do great if they left it alone for 5 SECONDS. I do not plan on selling but I am a bit discouraged by recent developments.\n\n", "author": "APC2_19", "created_time": "2025-04-18T14:25:08", "url": "https://reddit.com/r/ValueInvesting/comments/1k267bz/how_do_you_price_in_the_regulatory_risk_affecting/", "upvotes": 19, "comments_count": 19, "sentiment": "neutral", "engagement_score": 57.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k2bef4", "title": "Should Google Be Forced To Sell YouTube/Make it Independent?", "content": "Currently, Google is being pressured to sell its Chrome Browser, and that makes me think, “What about YouTube?” Do you think Google should sell YouTube? Should it be an independent company? Why?", "author": "Falconator100", "created_time": "2025-04-18T18:03:55", "url": "https://reddit.com/r/youtube/comments/1k2bef4/should_google_be_forced_to_sell_youtubemake_it/", "upvotes": 26, "comments_count": 53, "sentiment": "bearish", "engagement_score": 132.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k2dlk8", "title": "<PERSON><PERSON><PERSON>'s alternative to tariffs is seriously brilliant (Import Certificates)", "content": "I'm honestly not sure how this hasn't been brought up more, but <PERSON><PERSON><PERSON> actually has a beautifully elegant alternative to tariffs that solves for the trade deficit (which is a very real problem, he said in 2006.... \"The U.S. trade deficit is a bigger threat to the domestic economy than either the federal budget deficit or consumer debt and could lead to political turmoil...\")\n\nHere's how Import Certificates work...\n\n* Every time a U.S. company exports goods, it receives \"Import Certificates\" equal to the dollar amount exported.\n* Foreign companies wanting to import into the U.S. must purchase these certificates from U.S. exporters.\n* These certificates trade freely in an open market, benefiting U.S. exporters with an extra revenue stream, and gently nudging up the price of imports.\n\nThe brilliance is that trade automatically balances itself out—exports must match imports. No government bureaucracy, no targeted trade wars, no crony capitalism, and no heavy-handed tariffs.\n\n<PERSON><PERSON><PERSON> was upfront: Import Certificates aren't perfect. Imported goods would become slightly pricier for American consumers, at least initially. But tariffs have that same drawback, with even more negative consequences like trade wars and global instability.\n\nThe clear advantages:\n\n* **Automatic balance:** Exports and imports stay equal, reducing America's dangerous trade deficit.\n* **More competitive exports:** U.S. businesses get a direct benefit, making them stronger in global markets.\n* **Job creation:** Higher exports mean more domestic production and, consequently, more American jobs.\n* **Market-driven:** No new bureaucracy or complex regulation—just supply and demand at work.\n\nI honestly don't know how this isn't being talked about more! Hell, we could rename them Trump Certificates if we need to, but I think this policy needs to get up to policymakers ASAP haha.\n\nEdit: removed ‘no new Bureaucracy’ as an explanation for market driven. It def does increase gov overhead, thanks for pointing that out!\n\nHere's the link to Buffett's original article: [https://www.berkshirehathaway.com/letters/growing.pdf](https://www.berkshirehathaway.com/letters/growing.pdf)\n\nWe also made a full video on this if you want to check it out: [https://www.youtube.com/watch?v=vzntbbbn4p4](https://www.youtube.com/watch?v=vzntbbbn4p4)\n", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-18T19:37:17", "url": "https://reddit.com/r/ValueInvesting/comments/1k2dlk8/buffetts_alternative_to_tariffs_is_seriously/", "upvotes": 1584, "comments_count": 425, "sentiment": "neutral", "engagement_score": 2434.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k2fh48", "title": "I’ve Worked at Google for Decades. I’m Sickened by What It’s Doing.", "content": "", "author": "1oarecare", "created_time": "2025-04-18T20:58:31", "url": "https://reddit.com/r/technology/comments/1k2fh48/ive_worked_at_google_for_decades_im_sickened_by/", "upvotes": 3583, "comments_count": 268, "sentiment": "neutral", "engagement_score": 4119.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k2ja17", "title": "How to not be a failure like Quibi, Juicero, and Google Glass.", "content": "I’ve spent the last few years diving into the wreckage of big product flops. Juicero ($120 M down the drain), Google Glass ($500 M loss), Quibi ($1.75 B loss)and noticed the same pattern every single time: companies build what they *think* customers want, without ever validating the *real* problems they face.\n\nHere’s what jumping into hundreds of customer conversations and mining thousands of social posts has taught me:\n\n**Most “must‑have” features end up sitting untouched.** Teams burn nights coding bells and whistles… only to hear crickets.  \n**Budget bleeds away on guesswork.** Without a clear target, your roadmap becomes a scattergun of half‑baked ideas.  \n**Research often feels like incomplete puzzle pieces.** Surveys ask the wrong questions; analytics miss the “why.”  \n**That nagging “will anyone actually pay for this?” is 100% valid.** If you’re not talking to real users, you’re flying blind.\n\n# So what really works?\n\n1. **Go to the source.** Talk one‑on‑one with your ideal users. compensate them for 20 minutes of their time and you’ll hear the stories that surveys never capture.\n2. **Follow threads, not features.** I’ve learned to skip “Would you use X?” and start with “Tell me about the last time you were stuck doing Y.” Pain points emerge naturally.\n3. **Leverage existing data.** Customer support logs, chat transcripts, social posts—these hold hidden clues about what’s really annoying people right now.\n4. **Stay curious about context.** Door‑to‑door UX walks, remote screen shares, even a quick coffee chat: sometimes you need to see the environment to understand the problem.\n\nEvery time I’ve put these steps into practice, ideas that once felt risky suddenly gained clarity, and pre‑launch interest.\n\n  \nBesides product builders, this works for sales, consultants, marketers, and anyone trying to sell anything. \n\nHow many businesses have failed for you because you didn't learn about pain points?\n\nTLDR: Figure out your audiences pain points before building. ", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-18T23:57:52", "url": "https://reddit.com/r/Entrepreneur/comments/1k2ja17/how_to_not_be_a_failure_like_quibi_juicero_and/", "upvotes": 0, "comments_count": 5, "sentiment": "bearish", "engagement_score": 10.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}]