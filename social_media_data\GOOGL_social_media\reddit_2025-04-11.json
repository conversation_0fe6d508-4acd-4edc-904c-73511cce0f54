[{"platform": "reddit", "post_id": "reddit_1jwd473", "title": "FedEx Stole My 4090 FE RMA Inbound to Nvidia - I'm having a panic attack", "content": "I am making this post to share my nightmare I'm dealing with to this subreddit and the world, and to just vent my frustration right now.\n\nIn October 2022, I bought the 4090 FE from Best Buy and used it for over 2 years just fine no problem. In January 2025 I bought a Corsair HX1500i PSU to replace my aging EVGA 850w T2.\n\n2 weeks later, while upgrading my case, I happened to catch the cable starting to melt in the GPU. I snapped photos, contacted Nvidia customer support, and got an RMA to have the connector/card replaced with the newer 12v-2x6 standard. They gave me a FedEx shipping label to send the card to their Omni RMA center in California.\n\nI was already feeling queasy about sending this card out there. I was googling around about Nvidia FE RMA stories and saw a bunch of horrible situations where users got back physically damaged cards, cards that didn't work, cards with people's hair intertwined in the fans and heatsink, etc. But no one seemed to have an exact situation like mine where the card was stolen in transit to the RMA department from the customer. \n\nMy card arrived on March 26th, Wednesday after a noticeable delay in New Mexico. A week went by and I didn't hear from anyone at Nvidia so I decided to email their customer support about getting a status for my RMA.\n\nI just received the reply tonight: my card wasn't in the box, and they claim no signs of tampering were present on the packaging.\n\nMy heart sank, my stomach is in knots, and I feel like I'm going to throw up.\n\nThey told me I will have to file a claim with FedEx. I've also seen reports in the past on this very subreddit where users were in a similar situation and they were told only Nvidia can make the claim since it was their paid shipping label.\n\nI don't know what to do at this point. I don't know who to contact, I don't know how to process this. I am devastated. <PERSON> help me.", "author": "KuraiS<PERSON>dos<PERSON>", "created_time": "2025-04-11T00:27:23", "url": "https://reddit.com/r/nvidia/comments/1jwd473/fedex_stole_my_4090_fe_rma_inbound_to_nvidia_im/", "upvotes": 1172, "comments_count": 309, "sentiment": "bullish", "engagement_score": 1790.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jwgjst", "title": "Looking for some help in Google Ads", "content": "Hi all,\n\nI have been running Google Ads for a while, but compared to Meta, my cost per conversion is very very unstable, which is why I have not been able to scale the budget.\n\nIs this a common problem everybody faces or just unique to my account?", "author": "head<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-11T03:31:00", "url": "https://reddit.com/r/adwords/comments/1jwgjst/looking_for_some_help_in_google_ads/", "upvotes": 2, "comments_count": 8, "sentiment": "neutral", "engagement_score": 18.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jwhyll", "title": "Google lays off hundreds of employees in Android, Pixel Group, The Information reports", "content": "", "author": "InitiativeWorth8953", "created_time": "2025-04-11T04:54:27", "url": "https://reddit.com/r/GooglePixel/comments/1jwhyll/google_lays_off_hundreds_of_employees_in_android/", "upvotes": 854, "comments_count": 107, "sentiment": "neutral", "engagement_score": 1068.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jwiktd", "title": "$GOOGL growth trajectory?", "content": "All you people that believe in Alphabets growth in the next 5 years. 52% of $GOOGLE revenue comes from advertising through searching on Google. How is this business not being complete cannibalized by Chat GPT, Grok, Mistral, Gemini etc. \nFor myself I can say that I almost never use Google search anymore. Please explain how $GOOGLE predicted growth trajectory makes sense?", "author": "Pretty-Spot-8197", "created_time": "2025-04-11T05:34:59", "url": "https://reddit.com/r/ValueInvesting/comments/1jwiktd/googl_growth_trajectory/", "upvotes": 0, "comments_count": 69, "sentiment": "bullish", "engagement_score": 138.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jwnpvs", "title": "$78K Wasted on Junk Leads from “Search Partners” Network in Google Ads", "content": "Thought this might be of interest to folks wondering if they should enable “Search Partners” in Google Ads. The short answer is no, but let me explain:\n\nI inherited a mid-market/enterprise B2B SaaS Google Ads account running paid search campaigns exclusively but with “Search Partners” enabled.\n\nThey were using HubSpot CRM and their native Google Ads integration, which is AWESOME because it automatically collects “First Page Seen” for all inbound leads, which is the landing page URL with lots of useful parameters, including network.\n\nA quick workflow in HubSpot let me populate a custom “Google Ads Network” field on all Contacts & Deals from Google Ads, which I then combined with their Contact Lead Status and Deal Stage/Amount fields to help quantify something I already knew to be true, which is that “Search Partners” is complete garbage.\n\nHere’s the data for 2024:\n\n|Data Source|Metric|Google Search|Search Partners|\n|:-|:-|:-|:-|\n|Google Ads|Spend|$259,367|$78,383|\n|Google Ads|Click Rate|7.5%|12.4%|\n|Google Ads|Conversion Rate|1.7%|3.1%|\n|Google Ads|Conversions|451|417|\n|Google Ads|Cost per Conversion|$575|$188|\n|HubSpot|Lead Status - Qualified|281|8|\n|HubSpot|Lead Status - Junk/Spam|86|380|\n|HubSpot|Lead Status - Unknown|124|51|\n|HubSpot|% Qualified|57%|2%|\n|HubSpot|% Junk or Unknown|43%|98%|\n|HubSpot|Opportunities Created|274 - $2,909,510|1 - $17,160|\n|HubSpot|Opportunities Closed-Won|52 - $727,325|1 - $17,160|\n\nIf you were just looking at Google Ads, you’d think “Search Partners” is a slam dunk. Better CTR, CRV and CPL. But looking at properly segmented data in HubSpot, you realize that it is a complete waste of money.\n\nWorth noting these are fairly normal campaigns - a healthy mix of client brand, competitor brand and higher-intent industry solution/software keywords.\n\nCan't speak to ecommerce, but for lead generation - my recommendation is always to turn off :)", "author": "aStormyKnight", "created_time": "2025-04-11T11:38:04", "url": "https://reddit.com/r/PPC/comments/1jwnpvs/78k_wasted_on_junk_leads_from_search_partners/", "upvotes": 80, "comments_count": 46, "sentiment": "bearish", "engagement_score": 172.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jwxxil", "title": "Google's Cos<PERSON>nti<PERSON> finds what took Researchers a Decade", "content": "The article at [https://www.techspot.com/news/106874-ai-accelerates-superbug-solution-completing-two-days-what.html](https://www.techspot.com/news/106874-ai-accelerates-superbug-solution-completing-two-days-what.html) highlights a Google AI CoScientist project featuring a multi-agent system that generates original hypotheses without any gradient-based training. It runs on base LLMs, Gemini 2.0, which engage in back-and-forth arguments. This shows how “test-time compute scaling” without RL can create genuinely creative ideas.\n\n**System overview**\nThe system starts with base LLMs that are not trained through gradient descent. Instead, multiple agents collaborate, challenge, and refine each other’s ideas. The process hinges on hypothesis creation, critical feedback, and iterative refinement.\n\n**Hypothesis Production and Feedback** \nAn agent first proposes a set of hypotheses. Another agent then critiques or reviews these hypotheses. The interplay between proposal and critique drives the early phase of exploration and ensures each idea receives scrutiny before moving forward.\n\n**Agent Tournaments**\nTo filter and refine the pool of ideas, the system conducts tournaments where two hypotheses go head-to-head, and the stronger one prevails. The selection is informed by the critiques and debates previously attached to each hypothesis.\n\n**Evolution and Refinement**\nA specialized evolution agent then takes the best hypothesis from a tournament and refines it using the critiques. This updated hypothesis is submitted once more to additional tournaments. The repeated loop of proposing, debating, selecting, and refining systematically sharpens each idea’s quality.\n\n**Meta-Review** \nA meta-review agent oversees all outputs, reviews, hypotheses, and debates. It draws on insights from each round of feedback and suggests broader or deeper improvements to guide the next generation of hypotheses.\n\n\n\nFuture Role of RL Though gradient-based training is absent in the current setup, the authors note that reinforcement learning might be integrated down the line to enhance the system’s capabilities. For now, the focus remains on agents’ ability to critique and refine one another’s ideas during inference.\n\nPower of LLM Judgment A standout aspect of the project is how effectively the language models serve as judges. Their capacity to generate creative theories appears to scale alongside their aptitude for evaluating and critiquing them. This result signals the value of “judgment-based” processes in pushing AI toward more powerful, reliable, and novel outputs.\n\nConclusion Through discussion, self-reflection, and iterative testing, Google AI CoScientist leverages multi-agent debates to produce innovative hypotheses—without further gradient-based training or RL. It underscores the potential of “test-time compute scaling” to cultivate not only effective but truly novel solutions, especially when LLMs play the role of critics and referees.", "author": "PianistWinter8293", "created_time": "2025-04-11T19:10:16", "url": "https://reddit.com/r/artificial/comments/1jwxxil/googles_coscientist_finds_what_took_researchers_a/", "upvotes": 20, "comments_count": 11, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jwydc0", "title": "Selling Manus AI Invitation", "content": "I have a few Manus AI invitations that I am selling.  This thing is crazy awesome btw.  In three hours it built an entire web application that allows me to manage my Google Ads API and the 100+ Google Ads accounts I manage.  That's just the tip of the iceberg.  DM me if you are interested.", "author": "jkal33", "created_time": "2025-04-11T19:29:13", "url": "https://reddit.com/r/adwords/comments/1jwydc0/selling_manus_ai_invitation/", "upvotes": 0, "comments_count": 13, "sentiment": "bearish", "engagement_score": 26.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jx1g9h", "title": "Anyone Finding Google Display Ads Just Drive Spam Traffic?", "content": "I manage a Google Ads account for a business in the finance industry. Keyword targeting for search ads is too expensive so we used html5 display ads but recently spam traffic is crazy! Anyone else noticing this?", "author": "Digital_Gorilla_", "created_time": "2025-04-11T21:42:25", "url": "https://reddit.com/r/adwords/comments/1jx1g9h/anyone_finding_google_display_ads_just_drive_spam/", "upvotes": 10, "comments_count": 13, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}]