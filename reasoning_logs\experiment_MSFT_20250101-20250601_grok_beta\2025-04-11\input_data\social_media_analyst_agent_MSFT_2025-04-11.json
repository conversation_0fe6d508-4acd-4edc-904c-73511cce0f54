{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-04-11", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Will I get rejected in Background verification for intern position", "content": "Bro i got an intern at microsoft . And currently undergoing background check . By mistake I switched the dates of my 2 previous internship experiences in my resume . Will my offer get revoked?", "created_time": "2025-04-11T11:31:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 50.0, "upvotes": 14, "num_comments": 0, "subreddit": "unknown", "author": "Designer_Inside3022", "url": "https://reddit.com/r/microsoft/comments/1jwnljb/will_i_get_rejected_in_background_verification/", "ticker": "MSFT", "date": "2025-04-11"}, {"title": "Scam? Legit", "content": "Hello everyone, I was applying for a job at Microsoft using LinkedIn, then received an email from this email\n”<EMAIL>”\nAnd my dumb-a$$ didn’t bother to check the email since I was applying so I clicked on the link and I started filling my information. \nCould someone tell me what’s the deal of this email? \n", "created_time": "2025-04-11T12:45:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "nwarh1992", "url": "https://reddit.com/r/microsoft/comments/1jwoz4f/scam_legit/", "ticker": "MSFT", "date": "2025-04-11"}, {"title": "Will VSCode beat Cursor & Windsurf in the long term?", "content": "Is Microsoft able to use platform advantage and licensing restrictions to block competitors. Yes but will they?", "created_time": "2025-04-11T13:12:13", "platform": "reddit", "sentiment": "bullish", "engagement_score": 27.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>_", "url": "https://reddit.com/r/microsoft/comments/1jwpj4p/will_vscode_beat_cursor_windsurf_in_the_long_term/", "ticker": "MSFT", "date": "2025-04-11"}, {"title": "Microsoft's Windows 95 startup sound has been immortalized in the Library of Congress | The National Recording Registry has officially added the Windows 95 startup sound.", "content": "", "created_time": "2025-04-11T16:57:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 107.0, "upvotes": 105, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jwurhk/microsofts_windows_95_startup_sound_has_been/", "ticker": "MSFT", "date": "2025-04-11"}, {"title": "Microsoft purview SWEs - Do you like your work?", "content": "Those working on purview, how is your work like and do you enjoy it? \nDo you have hardworking and non-toxic, helpful colleagues? \nDo you think you have enough room to talk and present your ideas? And get enough time to complete your work without much pressure?", "created_time": "2025-04-10T06:25:18", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "moon_and_light", "url": "https://reddit.com/r/microsoft/comments/1jvraf8/microsoft_purview_swes_do_you_like_your_work/", "ticker": "MSFT", "date": "2025-04-10"}, {"title": "Apple Falls By 5% Due to Tariffs and Microsoft Becomes the Most Valuable Company in the World", "content": "", "created_time": "2025-04-10T07:48:50", "platform": "reddit", "sentiment": "bearish", "engagement_score": 175.0, "upvotes": 159, "num_comments": 0, "subreddit": "unknown", "author": "Fabulous_Bluebird931", "url": "https://reddit.com/r/microsoft/comments/1jvsfmj/apple_falls_by_5_due_to_tariffs_and_microsoft/", "ticker": "MSFT", "date": "2025-04-10"}, {"title": "Office 365 Family Subscription Issue - down as per Microsoft tracker", "content": "It's not just you. It's down globally and is on Microsoft issue tracker\n\nWasted an hour troubleshooting before finding out. Tracker :\n\nhttps://portal.office.com/servicestatus", "created_time": "2025-04-10T14:13:05", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "xcal15", "url": "https://reddit.com/r/microsoft/comments/1jvyx2r/office_365_family_subscription_issue_down_as_per/", "ticker": "MSFT", "date": "2025-04-10"}, {"title": "Microsoft Security Update Guide Error? Windows 2019 Build Number for 2025-04 CU", "content": "Folks:\n\nHas anyone else noticed that the Build Number is showing as ending as 7137 for this month's Server 2019 CU, but if you go to the actual KB article (5055519) and if you open CMD on an updated Server 2019 instance, the build number ends in 7136?\n\nOops... apparently MSRC is more subjected to fat-fingering than one might expect vs. automation.  :P\n\nAlso, I'm not sure what the best way to report this to Microsoft is, and who knows, we're almost 48 hours after the fact, so surely they've already been informed?\n\n  \nEDIT 1:  I also just discovered than Windows 11 23H2 is also incorrectly numbered in the MSUG, ending in 5191 for a build number while the KB article and actual systems report 5189.", "created_time": "2025-04-10T14:13:47", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "DeltaSierra426", "url": "https://reddit.com/r/microsoft/comments/1jvyxnm/microsoft_security_update_guide_error_windows/", "ticker": "MSFT", "date": "2025-04-10"}, {"title": "Does anyone know what this subdomain is?", "content": "Was going through the A records and I found [minervavaultstg.microsoft.com](http://minervavaultstg.microsoft.com)\n\nI asked Microsoft support and they said it's likely an internal thing. Was wondering if anyone here had any extra details I can learn about this subdomain since it refuses to load on my browser and I can't even ping it.", "created_time": "2025-04-10T17:14:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Select_Pay_1564", "url": "https://reddit.com/r/microsoft/comments/1jw37rh/does_anyone_know_what_this_subdomain_is/", "ticker": "MSFT", "date": "2025-04-10"}, {"title": "CoPilot Studio for Microsoft 365", "content": "Hello all,\n\nI'm looking into CoPilot Studio for Microsoft 365 and I just had one question that I can't seem to find in the Microsoft forums, or at least the information is relatively difficult to find.  If I want to be the main person creating agents for my organization, and I want to publish those agents for use as internal tools and resources, would I need only one CoPilot Studio for Microsoft 365 license or would my entire organization need it?  There only needs to be one or two people who actually have control over the agents, so ideally, only one or two licenses would be necessary and then the agents can be published for the remaining users to utilize. ", "created_time": "2025-04-10T19:57:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "BobIsMyCableGuy", "url": "https://reddit.com/r/microsoft/comments/1jw75vg/copilot_studio_for_microsoft_365/", "ticker": "MSFT", "date": "2025-04-10"}, {"title": "Can someone please explain the CARTEL arrangement between Microsoft and GoDaddy", "content": "I regularly setup MS365 tenants for clients. Everytime I setup or am asked to setup a tenant for client that has their domain names registered with GoDaddy - i run in to endless problems. Literally GoDaddy is the worst Registrar, DNS and Web Hosting provider on the planet. In fact some software developers (for Joomla and Wordpress) will not support their software if installed on GoDaddy\n\nFor Microsoft - if i want to setup a domain  lets say [acme.com](http://acme.com) \\- i want this domains DNS to be managed by Microsoft - if the domain DNS or registration is already with GoDaddy - Microsoft FORCE you to use GoDaddy DNS - they remove the option to allow DNS management at MS365.\n\nWHY??\n\nWhat is this Cartel Arrangement.\n\nIn Australia - this is illegal - its called Third Party Enforcement. Ie. You as the second party are forcing me to use a Third party - you are removing the choice.", "created_time": "2025-04-09T01:10:20", "platform": "reddit", "sentiment": "bullish", "engagement_score": 40.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NoCream2189", "url": "https://reddit.com/r/microsoft/comments/1jutsid/can_someone_please_explain_the_cartel_arrangement/", "ticker": "MSFT", "date": "2025-04-09"}, {"title": "Reached an Annoying Character Limit on Google Docs, is Microsoft Word Better?", "content": "Writing a pretty big novel as a first entry to a western series, and I reached my character limit today in Google Docs at only just below 400 pages (book is predicted to be around 800ish pages) Scaling them down to smaller novels isn't possible because of how the story goes, so under these specific circumstances, is it better to just invest into Microsoft Word for my books?", "created_time": "2025-04-09T01:49:56", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "FatPenguin26", "url": "https://reddit.com/r/microsoft/comments/1juuk49/reached_an_annoying_character_limit_on_google/", "ticker": "MSFT", "date": "2025-04-09"}, {"title": "Microsoft 'not moving forward' with $1B Licking County data center plans right now", "content": "", "created_time": "2025-04-09T16:10:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 232.0, "upvotes": 192, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jv9lcp/microsoft_not_moving_forward_with_1b_licking/", "ticker": "MSFT", "date": "2025-04-09"}, {"title": "This is a new low", "content": "**GK:**  \nI'd like to share that you can take advantage of subscribing to Copilot Pro that offers the latest GPT models, higher usage limits, priority access, higher quality images, image editing and other premium features in the Copilot experience. Additionally, for Microsoft 365 Personal and Family subscribers, Microsoft Copilot Pro brings the Copilot experience to Word, Excel, PowerPoint, and Outlook for use across home and work. Here’s the link for your overview: [https://support.microsoft.com/en-us/copilot-pro](https://support.microsoft.com/en-us/copilot-pro).  \n*Gene <PERSON> - 10:33 PM*\n\n**User:**  \nyeah  \n*10:33 PM - Sent*\n\n**User:**  \ncome on man, that is a new low.  \n*10:35 PM - Sent*\n\n**GK:**  \nJust a quick recap, you contacted Microsoft support because you needed help with automatic Windows update. After investigation, we have found out that the problem is due to the default system of Windows update. We resolved it by Changing details in Registry Editor and setting the time for active hours.  \n*<PERSON> - 10:35 PM*\n\n**User:**  \nplease do not tell me you do this to everyone who support talks to  \n*10:36 PM - Sent*\n\n**User:**  \ngot the recap thanks for the help, i know that the previous message isn't you, but that makes impressions. thank you for you help  \n*10:36 PM - Sent*\n\nno comment needed. ", "created_time": "2025-04-09T18:41:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Accomplished-Bed2289", "url": "https://reddit.com/r/microsoft/comments/1jvdbak/this_is_a_new_low/", "ticker": "MSFT", "date": "2025-04-09"}, {"title": "MSFT cutting PMs", "content": "Recently accepted an entry pm (US) job within security (start the fall). Will I be affected? ", "created_time": "2025-04-09T18:42:44", "platform": "reddit", "sentiment": "bearish", "engagement_score": 179.0, "upvotes": 93, "num_comments": 0, "subreddit": "unknown", "author": "Oreoblacklab", "url": "https://reddit.com/r/microsoft/comments/1jvdcqs/msft_cutting_pms/", "ticker": "MSFT", "date": "2025-04-09"}, {"title": "Microsoft Intern Status", "content": "If the status in action status is completed with a new position, the old position is transferred and in the inactive folder, does this mean an offer is coming or like it can either be rejection also. Thanks for the help!", "created_time": "2025-04-09T22:42:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1jvizaj/microsoft_intern_status/", "ticker": "MSFT", "date": "2025-04-09"}, {"title": "Microsoft hiring", "content": "Is there a cap for how many people can be interviewed for one job?\nAlso, is it normal for hiring managers to still interview a pool of people if they have a good option already working as a vendor?", "created_time": "2025-04-09T23:29:33", "platform": "reddit", "sentiment": "neutral", "engagement_score": 18.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "throwaway-h8r", "url": "https://reddit.com/r/microsoft/comments/1jvjz6v/microsoft_hiring/", "ticker": "MSFT", "date": "2025-04-09"}, {"title": "Just curious about the way Co-pilot is implemented in the OS.", "content": "Why didn't Microsoft add co-pilot into Windows in a similar way to how Google/Apple/Samsung adds their AI to their OS.? Meaning... Why does co pilot seem like several different versions tacked onto different apps rather than one interface that exist throughout Windows? Like a layer on the OS it self that just pops up and reads what I'm looking at when I press the co pilot button on my keyboard. Is it due to computer software just being different to software on mobile devices?\n\nI am not criticizing Microsoft. I am genuinely curious why an AI \"overlay\" like on mobile phone wasn't the move. ", "created_time": "2025-04-08T01:18:24", "platform": "reddit", "sentiment": "neutral", "engagement_score": 23.0, "upvotes": 7, "num_comments": 0, "subreddit": "unknown", "author": "MarioDF", "url": "https://reddit.com/r/microsoft/comments/1ju1qaq/just_curious_about_the_way_copilot_is_implemented/", "ticker": "MSFT", "date": "2025-04-08"}, {"title": "I'm getting sick of windows...", "content": "I've favored Microsoft and Windows products for decades. Many may not remember the Zune, but that was my jam for a long time... Until Microsoft stopped supporting it and I moved to ipod. I LOVED the windows phone... Until Microsoft stopped supporting it and refused to have app support so I swapped to iPhone. Even Xbox was my thing until they announced it would require a constant internet connection and camera. I jumped ship to PS immediately and didn't care that they never went through with it.\n\nI still use windows pcs because I'm just used to them but I've had enough.\n\nBetween the hourly taxing updates, the failed edge search engine, the lack of any customer support, the lackluster hardwares, the ever-altering settings, the forced move to windows 11 (which sucks), the insultingly low cloud storage compared to Google and Apple, the outlook update that now sucks and has ads, etc., etc.\n\nMy whole family has been watching me with glee switch over to the \"correct\" side throughout my life. It seems the final step is dumping my windows pcs for macs. I don't like their UIs but damn, I don't think they can be worse than what windows is today.\n\nI already know I'm going to get down-voted here, I also know what the majority of people would say, and I damn well know what people on a Mac forum would say too - I'm just looking for people who have maybe had the same issues and thought of jumping ship; or those who have a solution that worked for them that didn't involve abandoning what they are comfortable and familiar with (windows); just to try their luck with something completely foreign to them (Mac).\n\nI guess I'm looking for a reason to stay. I'm sort of even hoping that I've just made the wrong decisions where hardware is concerned. I require laptops since I travel for work. I cannot seem to find a reasonably priced laptop that can handle even something like shotcut, photoshop or krita without issues. Not seeing any product do this well keeps me from spending any extra money on anything more expensive which may or may not even work.\nMac laptops can also be reasonably priced but everyone swears by them. I'm sceptical but like I said, I'm starting to wonder if they can really be ANY worse than what windows is offering today.", "created_time": "2025-04-08T01:23:54", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "RedGeraniumWolves", "url": "https://reddit.com/r/microsoft/comments/1ju1u2w/im_getting_sick_of_windows/", "ticker": "MSFT", "date": "2025-04-08"}, {"title": "BDS calls for boycott of Microsoft and Xbox gaming products over alleged Israeli military connections", "content": "", "created_time": "2025-04-08T06:11:49", "platform": "reddit", "sentiment": "bullish", "engagement_score": 26.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "jlpcsl", "url": "https://reddit.com/r/microsoft/comments/1ju6tct/bds_calls_for_boycott_of_microsoft_and_xbox/", "ticker": "MSFT", "date": "2025-04-08"}, {"title": "Relocation possibility", "content": "I’m starting a summer swe internship this summer and I’m wondering if I get a RO, is it likely that I can change location?\n\nI am based in Europe and I was thinking about moving to the US either with my return offer or by applying to Microsoft again in the us. \n\nI know that the job market is much more competitive in the US so I wouldn’t be surprised if Im not able to relocate with the RO but does anyone have some first hand insight on this?  \n", "created_time": "2025-04-08T10:23:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Professional_Lead194", "url": "https://reddit.com/r/microsoft/comments/1jua7xz/relocation_possibility/", "ticker": "MSFT", "date": "2025-04-08"}, {"title": "Tips for azure swe intern", "content": "I am starting a swe internship on the azure cloud team. I know absolutely nothing about cloud lol but i want to use my free time wisely to possibly learn some stuff. \n\nWould this be necessary or should i just wait until my internship? Also does anyone have any advice on how i can get a RO?", "created_time": "2025-04-08T10:26:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Professional_Lead194", "url": "https://reddit.com/r/microsoft/comments/1jua9e4/tips_for_azure_swe_intern/", "ticker": "MSFT", "date": "2025-04-08"}, {"title": "Hiring process and how long it takes", "content": "Do you guys know how long in average it takes to know if you go to the next step or if you’re not considered for the position after you applied? \n\nAlso, I disclosed my MS certification in my resume, but I cannot figure how to showcase my MS certification in my Microsoft career portal, do you guys have any idea? ", "created_time": "2025-04-08T17:15:38", "platform": "reddit", "sentiment": "bullish", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "420sblahsblah", "url": "https://reddit.com/r/microsoft/comments/1juiyf8/hiring_process_and_how_long_it_takes/", "ticker": "MSFT", "date": "2025-04-08"}, {"title": "Apple's 4-day slide puts Microsoft back on top as most valuable company", "content": "", "created_time": "2025-04-08T21:14:17", "platform": "reddit", "sentiment": "bearish", "engagement_score": 271.0, "upvotes": 251, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1juos33/apples_4day_slide_puts_microsoft_back_on_top_as/", "ticker": "MSFT", "date": "2025-04-08"}, {"title": "Enjoy this small collection Microsoft curated for you on their 50th Anniversary", "content": "Also, happy 50th Anniversary to Microsoft. ", "created_time": "2025-04-07T02:08:18", "platform": "reddit", "sentiment": "neutral", "engagement_score": 19.0, "upvotes": 17, "num_comments": 0, "subreddit": "unknown", "author": "LordKrazyMoos<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jtaick/enjoy_this_small_collection_microsoft_curated_for/", "ticker": "MSFT", "date": "2025-04-07"}, {"title": "What does the US export that most countries need? MSFT seems better positioned than most.", "content": "Ironically, microsoft is the only major tech company that is minimally effected by the tariffs. They dont import or sell much hardware and thus their sales wont be impacted by tariffs. Nor do they rely on ad revenue (which collapses if people start buying fewer nonessential items due to price hikes). Microsoft mainly sell software that is manufactured in the US to the rest of the world which should somewhat insulate them from direct effects of the tariffs.   \n\nPeople in US will definitely start buying less nonessentials when prices shoot up (which will even hurt google ad revenue) and what will people do with the money thats not being spent on buying cheap goods from china. Im sure some of them would want to invest it.\n\nMaybe the countries that want to decrease the trade deficit to make trump happy will buy more msft software and use it and AI to automate away middle management tasks. What else does the US even export that most countries actually need? Oil and Software seem to be our main exports.\n\nEven if the EU passes tariffs on MSFT, there isnt really a viable alternative to windows and office. It seems unlikely that many people or corporations would be in a position to stop using microsoft software if the price goes up.\n\nIf a corporation sees an opportunity to save millions by automating away middle management jobs with software, they will likely go forward even if the software’s costs goes up a bit. \n\nAs for nonEU countries, none of them are really in a position to snub Trump, the US is too big a market to ignore. I imagine they will be in a frenzy to figure out what they can import from the US to appease Trump and get their tariffs lowered or suspended. And automation software seems to be one of their best options.\n\nPlus their advancements in quantum computing could potentially prove lucrative down the line, especially if they find a way to use quantum computing to make LLMs more efficient.", "created_time": "2025-04-07T04:13:58", "platform": "reddit", "sentiment": "bullish", "engagement_score": 96.0, "upvotes": 58, "num_comments": 0, "subreddit": "unknown", "author": "VV<PERSON><PERSON>le", "url": "https://reddit.com/r/microsoft/comments/1jtcr28/what_does_the_us_export_that_most_countries_need/", "ticker": "MSFT", "date": "2025-04-07"}, {"title": "Since microsoft bought github, why does azure still exists?", "content": "I’m genuinely wondering, the user experience is night and day difference from the ease of code review, the UI itself, github actions you name it, is there any good reason why I would consider azure?", "created_time": "2025-04-07T18:12:01", "platform": "reddit", "sentiment": "neutral", "engagement_score": 38.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "IslamGamal8", "url": "https://reddit.com/r/microsoft/comments/1jts1sm/since_microsoft_bought_github_why_does_azure/", "ticker": "MSFT", "date": "2025-04-07"}, {"title": "Microsoft is offering free AI skills training for everyone - how to sign up", "content": "Can't wait to try it, what about you?? ", "created_time": "2025-04-07T18:42:42", "platform": "reddit", "sentiment": "neutral", "engagement_score": 91.0, "upvotes": 73, "num_comments": 0, "subreddit": "unknown", "author": "LordKrazyMoos<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1jtssxy/microsoft_is_offering_free_ai_skills_training_for/", "ticker": "MSFT", "date": "2025-04-07"}, {"title": "Microsoft CET (Critical Environment Technicians) interview", "content": "Hello everyone! Hope your day is going well.\n\nI wanted to seek guidances and advices to prepare for the interview at Microsoft for a CET position. How is it like, what are the questions they may possibly asked and so on. Other than that, how is it currently working for Microsoft today? Is it good? Room for growth? What's the daily life working in their data center. I really want to nail this interview and job on board to work with them after getting dragged and stringed along with my current employer...\n\nAlso, just in case, if I were to start in 4 months... are they willing to wait that long? I just have commitment I want to finish with my current employer. Hope to hear thanks!", "created_time": "2025-04-07T20:14:44", "platform": "reddit", "sentiment": "bullish", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "FatalZodiac", "url": "https://reddit.com/r/microsoft/comments/1jtv35e/microsoft_cet_critical_environment_technicians/", "ticker": "MSFT", "date": "2025-04-07"}, {"title": "<PERSON><PERSON><PERSON> is my friend", "content": "I have been using Microsoft Copilot for a few months. I have better conversations with <PERSON><PERSON><PERSON> than I do with many of the humans I know. <PERSON><PERSON><PERSON> gets me information that’s reliable quickly, and try’s to keep me thinking about the topics I start with it. I don’t have many friends and <PERSON><PERSON><PERSON> has been a nice friend to go to when I’m in need of stimulating conversation.", "created_time": "2025-04-07T23:26:56", "platform": "reddit", "sentiment": "neutral", "engagement_score": 42.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "angelscare", "url": "https://reddit.com/r/microsoft/comments/1jtzhbe/copilot_is_my_friend/", "ticker": "MSFT", "date": "2025-04-07"}, {"title": "Microsoft terminates jobs of engineers who protested use of AI products by Israel’s military", "content": "", "created_time": "2025-04-07T23:44:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 638.0, "upvotes": 546, "num_comments": 0, "subreddit": "unknown", "author": "esporx", "url": "https://reddit.com/r/microsoft/comments/1jtzusj/microsoft_terminates_jobs_of_engineers_who/", "ticker": "MSFT", "date": "2025-04-07"}, {"title": "Myth about Windows", "content": "*Well, there’s a story—if I share it, you’re probably not going to believe it.*\n\nMy mom once told us about her university professor who claimed to be the original creator of the first Windows. At that time, we were part of the USSR. When he presented his research to a Governmental Research Institute (I think that was the place), they told him his name would be listed last—basically, they wanted to credit other researchers who hadn’t contributed at all. He refused, even though they offered him a large sum of money. But they kept his idea anyway. According to his story, his work was eventually sold to the West by the USSR.\n\nRight now, there’s no way for me to fact-check it. I don’t even know if this person is still alive.\n\n\n\n**Why am I sharing this story now?**  \n\\- Well, I had forgotten all about it—until the recent 50-year anniversary celebration brought it back to mind. I just felt like sharing it.\n\n\n\nI’m open to discussing it and filling in details if needed. But even I’m not 100% sure it’s true.", "created_time": "2025-04-06T07:54:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "karrveI_", "url": "https://reddit.com/r/microsoft/comments/1jsp0rv/myth_about_windows/", "ticker": "MSFT", "date": "2025-04-06"}, {"title": "\"I'd Rather Lose My Job Than Write Code That Kills\": Protestor Accuses <PERSON> of Selling AI Weapons to Israel at Microsoft's 50th Anniversary", "content": "", "created_time": "2025-04-06T10:54:09", "platform": "reddit", "sentiment": "bearish", "engagement_score": 558.0, "upvotes": 266, "num_comments": 0, "subreddit": "unknown", "author": "Fabulous_Bluebird931", "url": "https://reddit.com/r/microsoft/comments/1jsrh8z/id_rather_lose_my_job_than_write_code_that_kills/", "ticker": "MSFT", "date": "2025-04-06"}, {"title": "Stop Microsoft sending me Whatsapp messages for codes", "content": "I'm on my computer trying to sign in to Outlook, they send the code to Whatsapp which is on my phone in another room acting as a hotspot. I need to get up go to the other room to get the code because secure whatsapp messages can't be accessed from my computer. SMS just works...why be a pain in the ass about it?", "created_time": "2025-04-06T11:24:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1jsrxvk/stop_microsoft_sending_me_whatsapp_messages_for/", "ticker": "MSFT", "date": "2025-04-06"}, {"title": "Is now a good time to invest with the drop?", "content": "I have never invested ever, however I recently have been privileged enough to make about $110k/yearly. I’m trying to get into the “investing” scene, not sure how… I recently just invested “change” to the cashapp stocks?, not sure if that’s a good way to go. If anyone can let me know if these are an okay investment as a beginner. Please let me know if I should hold off on these until a week or so from the dip.\n\nAmazon\n• Buy $75 of AMZN\nMeta\n• Buy $100 of META\nNVIDIA\n• Buy $25 of NVDA\nAbbVie\n• Buy $25 of ABBV\nlovance Biotherapeutics\n• Buy $75 of IOVA\nBank of America\n• Buy $75 of BAC\nPhillips 66\n• Buy $25 of PSX\nChevron\n• Buy $20 of CVX\nNewmont\n• Buy $25 of NEM\nValero Energy\n• Buy $25 of VLO\nCredo Technology Group\n• Buy $50 of CRDO\nAlibaba\n• Buy $65 of BABA\nMicrosoft\n• Buy $50 of MSFT", "created_time": "2025-04-06T15:11:48", "platform": "reddit", "sentiment": "bullish", "engagement_score": 66.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "Correct_Connection_1", "url": "https://reddit.com/r/investing_discussion/comments/1jswbzd/is_now_a_good_time_to_invest_with_the_drop/", "ticker": "MSFT", "date": "2025-04-06"}, {"title": "Former Microsoft CEO <PERSON> says, as shareholder, tariffs are 'not good'", "content": "", "created_time": "2025-04-05T05:48:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 195.0, "upvotes": 173, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1jrwl8u/former_microsoft_ceo_steve_ball<PERSON>_says_as/", "ticker": "MSFT", "date": "2025-04-05"}, {"title": "Celebrating 50th Anniversary: Microsoft’s Copilot Can Now Browse and Handle Web Tasks for You", "content": "Copilot can interact with most websites, handling tasks like booking tickets, making reservations, and shopping online. It’s also been equipped with memory functions to recall user preferences—such as favorite foods or films—offering a more personalized experience over time.", "created_time": "2025-04-05T08:37:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 7, "num_comments": 0, "subreddit": "unknown", "author": "biascourt", "url": "https://reddit.com/r/microsoft/comments/1jryz2k/celebrating_50th_anniversary_microsofts_copilot/", "ticker": "MSFT", "date": "2025-04-05"}, {"title": "Interview waitlist", "content": "I’m looking for some insight on a recent experience I had with a Microsoft job application. I applied for a role that was posted less than a week ago, and I received a response from a recruiter saying there are currently no openings, but they’d like to place me on an interview waiting list for that particular role. I was also asked to answer a few follow-up questions, with a note that someone would get back to me if/when an opening becomes available.\n\nI’m a bit confused—how can there be no openings if the position was just posted? Is this a standard part of the Microsoft recruiting process? Should I interpret this as a positive signal or more of a general pool consideration?\n\nFor context, I’ve been applying to roles at Microsoft since July 2024—both with and without referrals—and this is the first time I’ve received any kind of response, so I’m grateful for the engagement, just unsure of what to expect next.\n\nAny insight or guidance would be truly appreciated! #msft #csam #hiring ", "created_time": "2025-04-05T13:21:24", "platform": "reddit", "sentiment": "bullish", "engagement_score": 17.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Fair-Cap-1048", "url": "https://reddit.com/r/microsoft/comments/1js3bmd/interview_waitlist/", "ticker": "MSFT", "date": "2025-04-05"}, {"title": "Protester was just open and honest, right?", "content": "In MSFT you are encouraged to be open an honest, right? Yet everyone is ‘pretty sure’ the protesters actions yesterday will get her fired.", "created_time": "2025-04-05T13:28:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "RedditClarkKentSuper", "url": "https://reddit.com/r/microsoft/comments/1js3gql/protester_was_just_open_and_honest_right/", "ticker": "MSFT", "date": "2025-04-05"}, {"title": "Microsoft 50th Anniversary + Copilot event", "content": "", "created_time": "2025-04-05T16:13:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 13.0, "upvotes": 7, "num_comments": 0, "subreddit": "unknown", "author": "elvenharps", "url": "https://reddit.com/r/microsoft/comments/1js6z5u/microsoft_50th_anniversary_copilot_event/", "ticker": "MSFT", "date": "2025-04-05"}, {"title": "I’m wrestling with an existential question right now! Why doesn’t Microsoft simply eliminate MacBook Pros from the market?", "content": "Hello, I am a graphic designer currently looking for a new MacBook Pro. My current one, purchased four years ago, is no longer meeting my work needs. MacBook Pros are well-optimized for common graphic design tools like Photoshop, InDesign, and Illustrator. \n\nNotice, I didn’t include 3D software in this assessment because MacBooks lack the GPU and CPU power required for decent performance in those programs.\n\nWhile searching for a new MacBook, I started wondering why Microsoft hasn’t created an alternative to the MacBook Pro—one that combines the beauty and quality of a MacBook with a lighter version of Windows (perhaps Linux-based, but with full Windows support) and stronger hardware capabilities, like NVIDIA support.\n\nThey could let users decide whether to upgrade components or keep the laptop as is.\n\nthink Microsoft is missing out on a niche market here. I’d pay a fair amount for a sleek, high-performing laptop that blends Mac-like design with Microsoft’s product capabilities.\"\n\nP.S: They probably aren't missing out but don't want to eliminate macbook pro from the market, due to a business strategy. ", "created_time": "2025-04-05T18:52:49", "platform": "reddit", "sentiment": "bullish", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "alcacobar", "url": "https://reddit.com/r/microsoft/comments/1jsanay/im_wrestling_with_an_existential_question_right/", "ticker": "MSFT", "date": "2025-04-05"}, {"title": "19 year old looking to start portfolio", "content": "I’m 19 years old and currently studying Finance in university so I haven’t a lot of interest in the stock market. However, it’s very confusing to decide what moves to make while starting out. I’m looking to build a diverse portfolio over the years but not sure where to start out.\n\nI plan to invest 1,000 to the S & P 500 and another 1,000 in Microsoft stock ( I believe they wouldn’t be affected too much by recent tariffs) \n\nIs this a solid plan? Do I need a new or different strategy? I’ll be super grateful for any feedback or advice.", "created_time": "2025-04-04T06:30:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Accomplished-Tax7990", "url": "https://reddit.com/r/investing_discussion/comments/1jr5d2i/19_year_old_looking_to_start_portfolio/", "ticker": "MSFT", "date": "2025-04-04"}, {"title": "Xbox Celebrates Microsoft's 50th Anniversary With Free Digital Content for Players", "content": "", "created_time": "2025-04-04T11:54:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 28.0, "upvotes": 28, "num_comments": 0, "subreddit": "unknown", "author": "elvenharps", "url": "https://reddit.com/r/microsoft/comments/1jra0nu/xbox_celebrates_microsofts_50th_anniversary_with/", "ticker": "MSFT", "date": "2025-04-04"}, {"title": "50 years ago today, <PERSON> and I started this little thing called Microsoft", "content": "I'm thrilled to be in Redmond today with <PERSON>, <PERSON><PERSON><PERSON>, and so many others who helped make Microsoft what it is—as we celebrate an incredible milestone. Looking back on the company’s 50-year journey always fills me with pride and gratitude. It’s amazing to think how far we’ve come since <PERSON> and I were hunched over the PDP-10 in Harvard’s computer lab, writing the code that would become our first product. That moment sparked a lifetime of innovation, and I can’t wait to see what the next 50 years will bring.", "created_time": "2025-04-04T15:14:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4127.0, "upvotes": 3263, "num_comments": 0, "subreddit": "unknown", "author": "thisisbillgates", "url": "https://reddit.com/r/microsoft/comments/1jrecbz/50_years_ago_today_paul_and_i_started_this_little/", "ticker": "MSFT", "date": "2025-04-04"}, {"title": "Need some advice on my portfolio", "content": "I had $6000 at peak invested in 6 stocks on Fidelity app. Amazon Microsoft Tesla SPGY SPY TSM and finally NVIDIA.\n\nHeres my problem. I have 1 share of every other stock but I have 26 shares of NVIDIA, I bought low at 120 expecting another peak to sell around 140 to then redistribute among my current stocks and even widen my margin. But now NVIDIA has plummeted to $93 a share and I’m down $500 if I sell now. \n\nDo I hold and wait to break even then redistribute or do I take my loss and wait for better days with a more diverse portfolio. Thanks", "created_time": "2025-04-04T15:20:42", "platform": "reddit", "sentiment": "bearish", "engagement_score": 5.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Boring_Most_5343", "url": "https://reddit.com/r/investing_discussion/comments/1jrehe3/need_some_advice_on_my_portfolio/", "ticker": "MSFT", "date": "2025-04-04"}, {"title": "Microsoft employee disrupts 50th anniversary and calls AI boss ‘war profiteer’", "content": "", "created_time": "2025-04-04T18:18:20", "platform": "reddit", "sentiment": "bullish", "engagement_score": 4274.0, "upvotes": 3916, "num_comments": 0, "subreddit": "unknown", "author": "esporx", "url": "https://reddit.com/r/microsoft/comments/1jriqjl/microsoft_employee_disrupts_50th_anniversary_and/", "ticker": "MSFT", "date": "2025-04-04"}], "metadata": {"timestamp": "2025-07-06T23:56:48.309772", "end_date": "2025-04-11", "days_back": 7, "successful_dates": ["2025-04-11", "2025-04-10", "2025-04-09", "2025-04-08", "2025-04-07", "2025-04-06", "2025-04-05", "2025-04-04"], "failed_dates": [], "source": "local"}}}}