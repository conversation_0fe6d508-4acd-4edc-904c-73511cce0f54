[{"platform": "reddit", "post_id": "reddit_1j4tctg", "title": "Stop using chrome from now on", "content": "I've been using chrome for more than 10 years. Whenever I try to switch to another browser I never did it because of I have been using it for so long. But today they trigger the switch to uninstall chrome and use another browser. Bye bye chrome.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-06T11:25:25", "url": "https://reddit.com/r/chrome/comments/1j4tctg/stop_using_chrome_from_now_on/", "upvotes": 0, "comments_count": 37, "sentiment": "bullish", "engagement_score": 74.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j4v8li", "title": "Google tells <PERSON>’s DOJ that forcing a Chrome sale would harm national security", "content": "", "author": "AdSpecialist6598", "created_time": "2025-03-06T13:18:10", "url": "https://reddit.com/r/technology/comments/1j4v8li/google_tells_trumps_doj_that_forcing_a_chrome/", "upvotes": 8274, "comments_count": 428, "sentiment": "neutral", "engagement_score": 9130.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j4zz2l", "title": "No one asked for this Google...", "content": "I really like(ed) the GBoard, but broh... There are so many other things that needs updating on the GBoard, and all of sudden Google decided that the key borders will now be 'circles' what the heck...\n\nAnd no, you DON'T have an option to change it. It's either this or no borders at all...", "author": "synergy<PERSON>ie", "created_time": "2025-03-06T16:51:59", "url": "https://reddit.com/r/google/comments/1j4zz2l/no_one_asked_for_this_google/", "upvotes": 1100, "comments_count": 250, "sentiment": "neutral", "engagement_score": 1600.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j513rc", "title": "What Niches Spend 20K+/Month on Google Ads", "content": "I know for sure Lawyers, Financial Sector, Plastic Surgeons, B2B (Not sure what sub categories) Any other ideas ? ", "author": "Partizana", "created_time": "2025-03-06T17:38:22", "url": "https://reddit.com/r/PPC/comments/1j513rc/what_niches_spend_20kmonth_on_google_ads/", "upvotes": 9, "comments_count": 46, "sentiment": "bullish", "engagement_score": 101.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j521ug", "title": "AAPL is the second most expensive Mag 7 behind TSLA.", "content": "Not one to ever care about the MAG 7 or the usual \"I like Google\" comment, but how is this even remotely rational? Naturally stocks can differ on many different aspects, so I'm already quiet on how Costco can trade at 60x Earnings on a mid-single digit growth rate, but these companies are at least all somewhat interconnected (outside of Tesla, that's just fantasy).\n\nWith today's decline, Apple and Nvidia are on basically the same P/E ratio, yet Nvidia has almost 15x higher revenue growth. Even the next two lowest growth stocks (Google and Amazon) are still over double what Apple is doing. I'm sitting here and I'm questioning as to how this is even possible.\n\nI'd love to hear any kind of plausible explanation as to how Apple can demand such an insane valuation on growth that's just marginally beating inflation.\n\nSorry for the low effort post, but I'm seriously stunned by this nonsensical market.", "author": "DylanIE_", "created_time": "2025-03-06T18:16:51", "url": "https://reddit.com/r/ValueInvesting/comments/1j521ug/aapl_is_the_second_most_expensive_mag_7_behind/", "upvotes": 103, "comments_count": 153, "sentiment": "bullish", "engagement_score": 409.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j55x4z", "title": "Google's Imagen 3 Model is Insane", "content": "", "author": "na<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-06T20:57:53", "url": "https://reddit.com/r/singularity/comments/1j55x4z/googles_imagen_3_model_is_insane/", "upvotes": 1135, "comments_count": 132, "sentiment": "neutral", "engagement_score": 1399.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j598ac", "title": "Overclocking the ASUS TUF 5080 OC = Beastly gains", "content": "TLDR: This 5080 overclocks to 3.3GHz, pushing 4090 numbers, while maintaining low temps and relatively low power draws. Overall equating to 20-30% above 4080S and nearly double the performance of my former 7900 GRE.\n\nI recently upgraded to the ASUS TUF 5080 OC edition from a 7900 GRE. I also had a quick pit stop with a 4080S but sold it for the price I paid for it and moved on a 5080 once I could get my hands on one in stock.\n\nThe gains on this card are crazy, at stock levels producing anywhere from  50% to at times double the performance of my former 7900 GRE in raster. Plus, I can finally turn on Ray Tracing and see all the expensive pixels. While RT can at times be overrated and cost unnecessary performance, now I have a card that can run it I find myself turning on Ray Tracing and Path Tracing wherever it is available, and in games like Cyberpuink it is absolutely transformative!\n\nBut that was just the beginning, because I have since been tweaking with overclocks and have found that I can get a relatively stable overclock running +375 on the core clock and +1000MHz on the memory for insane gains of between 10-15% depending on the title I am running. I have also tested pushing it as high as +450 on the core and +2000 on the memory, and while that appears to be my cards stable limit, it only nets an additional 1-2%.\n\nThe best thing about this card is that even when pushing it to its limits, seeing the core clock hit 3.3GHz, the card remains under 64c (the highest I've seen it go), the power still hovering around 300W and barely exceeding it, and the fans are no louder than what they are at any given time. Heck, my case fans actually run louder than my GPU fans as I haven't heard them spinning at all.\n\nFor all the flack the 50 series launch gets (which is understandable), these cards can be seriously impressive. Especially when overclocking to the point that in my  gaming tests, I'm achieving close or equal to 4090 numbers.\n\nThe screenshot you will see is from Steel Nomad running at +450 on the core and +2000 on the memory, and while it is not the highest score out there, it's seriously impressive considering at stock my score is about 81 FPS, resulting in a +13% gain in this benchmark, which is pretty consistent with my improvments in game v stock settings.\n\nAs noted earlier, I did also own a 4080S for a couple of weeks while I was waiting to get my hands on the 5080, and my overall performance gain v the 4080S across both games and benchmarks is more than 20%, including the fact that the 4080S I owned was OC'd to run about 10% above stock 4080S levels.\n\nI know the generational uplift wasn't there, the stock situation hasn't been great, and this launch has been plagued by drama. However, I make this post ultimately to say this, the 5080 is a great card. I am impressed by its performance, it is a beast when overclocked and I am extremely happy with my purchase.\n\nSo what about you guys, how has your experience been and what numbers have you all been able to push overclocking your 50 series cards?\n\n\\*Edit, screenshot didn't post so it is below in the comments.", "author": "Electrical_Good_4903", "created_time": "2025-03-06T23:20:12", "url": "https://reddit.com/r/nvidia/comments/1j598ac/overclocking_the_asus_tuf_5080_oc_beastly_gains/", "upvotes": 10, "comments_count": 156, "sentiment": "bearish", "engagement_score": 322.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j59huo", "title": "Googles ai is so smart. Lkmnop", "content": "", "author": "Previous-Working7184", "created_time": "2025-03-06T23:32:22", "url": "https://reddit.com/r/google/comments/1j59huo/googles_ai_is_so_smart_lkmnop/", "upvotes": 1, "comments_count": 19, "sentiment": "neutral", "engagement_score": 39.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j59tiq", "title": "Just got a pension", "content": "I just got onto a pension plan (YAY) that (according to the numbers) should equal about 45k in today's dollars per year and it will be adjusted for inflation. I have been saving but now I don't know what I'm supposed to do. I honestly never thought I'd see a pension in my line of work so I'm just gobsmacked. I have about 20 years of working left until freedom 55 and the 45k is what it would be if I retired today at 55 (added for clarity). My question is for people who have pensions, do you still save 40% of your income or no? Sorry if this  is the wrong community! I'm just looking at the possibility of retiring even earlier if I keep saving aggressively and take an earlier pension. Thoughts? Help? ", "author": "fabulousfrugalfemm", "created_time": "2025-03-06T23:47:33", "url": "https://reddit.com/r/Fire/comments/1j59tiq/just_got_a_pension/", "upvotes": 16, "comments_count": 34, "sentiment": "neutral", "engagement_score": 84.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j59tq6", "title": "Just use Ublock Origin Lite", "content": "That's it. Stop complaining about Chrome disabling the original addon every hour, the Ublock developers have been working on Ublock Origin Lite for almost 1 year now (and users were made aware of it). \n\nIt does have tree different permissions options, and even the basic one works perfectly fine. If you want to have even more control, use one of the other options, that works for 99% of people.", "author": "edu4rdshl", "created_time": "2025-03-06T23:47:49", "url": "https://reddit.com/r/chrome/comments/1j59tq6/just_use_ublock_origin_lite/", "upvotes": 0, "comments_count": 84, "sentiment": "neutral", "engagement_score": 168.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}]