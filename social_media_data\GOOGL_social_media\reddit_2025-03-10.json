[{"platform": "reddit", "post_id": "reddit_1j7tg55", "title": "Google Deleted Local Timeline Data on Users Devices", "content": "Yes, it was not planned but it still happened. The Google One App update on February 22 was the primary cause.\n\nUnfortunately for someone like me the data is now permanently lost. I migrated to my device last year after the notice of the change. My backups occurred automatically so once I saw this was happening it was too late, the recent backup only had a few days. I lost close to 12 years of data.\n\nNothing will happen to Google because of this, financially or legally. It is a good reminder that no matter the size and value of a company it is run by people. \n\nAnd people are frequently incompetent as demonstrated by this situation. ", "author": "me<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-10T07:48:26", "url": "https://reddit.com/r/GoogleMaps/comments/1j7tg55/google_deleted_local_timeline_data_on_users/", "upvotes": 130, "comments_count": 46, "sentiment": "neutral", "engagement_score": 222.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j7v6h9", "title": "New small business owner. Is it crazy I want to manage Google PPC myself?", "content": "I have a small business that used to be a franchise but branching out to do it myself. So we are starting all the way at the bottom again since the franchise used to manage all our advertising. I’ve started a campaign just by stumbling along the Google recommendations, I know this is probably not the best preparation but had to get something going. Now I have a bit more time I’m wanting to learn some basics and maybe if my brain can manage it, some more advanced skills for managing my own PPC campaigns effectively. Any online courses or resources would be greatly appreciated. Thank you ", "author": "Fit-Cheetah-7513", "created_time": "2025-03-10T10:01:55", "url": "https://reddit.com/r/PPC/comments/1j7v6h9/new_small_business_owner_is_it_crazy_i_want_to/", "upvotes": 16, "comments_count": 52, "sentiment": "bullish", "engagement_score": 120.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j7xv0r", "title": "Google Maps Timeline Data Wiped - recovered lost Timeline with another account", "content": "Similar to a lot of other threads on this topic, my timeline data was wiped a few days ago. Things I tried after the data was wiped that didn't fix it: \n\n* Installed an update for Google Maps that just came out March 7\n* Recovered last automatic backup from 2 days prior\n* Force Stopped the App and cleared cache\n* Turned Timeline off (don't delete data) and back on\n\nNone of this worked, trips prior to a few days ago were wiped and my history previously went back for years.\n\nWhat did work - I added another Google account to my phone, clicked on Timeline, answered all the prompts, and all my travel history is visible again.\n\nEdit: I should clarify a few points:\n\n* The second account I added was another Google account that I have that I don't believe ever used Maps. When I logged into that account in Maps it asked me a pile of questions like I was a new user.\n* To reiterate the first point: The travel data does not belong to the new account, it belonged to the old account.\n* As soon as I added this fresh account, boom, all my trips were immediately visible so it felt like the data was there, just not accessible until the second account was added. As in, it was on the phone just hidden and the new account reactivated it.\n* The original account is still missing all the travel data. The new account now has it all.", "author": "ctrl77nf", "created_time": "2025-03-10T12:50:59", "url": "https://reddit.com/r/GoogleMaps/comments/1j7xv0r/google_maps_timeline_data_wiped_recovered_lost/", "upvotes": 12, "comments_count": 42, "sentiment": "bullish", "engagement_score": 96.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j7yer7", "title": "I suck at marketing and I need help with google Ads.....", "content": "So we spent 3K so far on marketing over two months and had several meetings with are ads manager who has not done anything to help. With that we have only had two leads that didn't even fully convert. I started with CPA ads, but after getting an $87 click and we were told there was no way to minimize that with CPA ads so I changed to max CPC. With CPC and only google search it wasn't getting many clicks and we had to hit the promo level so I was told to turn on search partners which got lots of clicks at a decent cost per click, but it looks like most of it was garbage and still no legit conversions. \n\nFor context we are a SAAS business that specializes in software to manage the back end of service businesses and we likely still have to optimize our home page and other pages, but at this point I know marketing is one of the things I am weakest at and definitely need a partner to help us improve this as I can't keep spending that much on marketing that does not convert. I have another meeting today with my ad manager, but honestly they keep telling me to keep waiting and to trust the algorithm, but none of the advice they have given has seemed to make an impact or goes directly against most of the things I have read in this sub and the algorithm seems like it just randomly jumbles things and has no clue what it is doing other than maximizing my ad spend. ", "author": "pachinkopunk", "created_time": "2025-03-10T13:19:10", "url": "https://reddit.com/r/PPC/comments/1j7yer7/i_suck_at_marketing_and_i_need_help_with_google/", "upvotes": 2, "comments_count": 93, "sentiment": "bearish", "engagement_score": 188.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j7ze1r", "title": "Best European alternatives to AWS/GCP for AI workloads?", "content": "I'm looking for cloud GPU providers based in Europe. AWS, GCP, and Azure are expensive, and I'm also dealing with annoying latency when connecting to US servers. Ideally, I want something with on demand access and transparent pricing.\n\nI recently came across [Compute with Hivenet ](https://compute.hivenet.com), which offers on-demand RTX 4090s at way lower prices than AWS A100s. The performance has been solid, and there’s no waiting in queues or dealing with spot instance interruptions. it's also kinda nice to use a provider that’s actually in Europe thats as reliable as the big american names even if its a pretty basic platform for now.\n\nWhat other good European cloud GPU services are out there? Looking for options that won’t destroy my budget.", "author": "Brinley-berry", "created_time": "2025-03-10T14:05:42", "url": "https://reddit.com/r/cloudcomputing/comments/1j7ze1r/best_european_alternatives_to_awsgcp_for_ai/", "upvotes": 22, "comments_count": 11, "sentiment": "neutral", "engagement_score": 44.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j805p2", "title": "How many google ads do you currently manage? I'm at 98 right now and feeling overwhelmed", "content": "I work for an agency and am looking after 98 different google ad accounts. A fair amount of them don't need much work and run themselves, and not many big spenders. It still feels like too much to me. I wanted to get an insight to how many accounts other people manage", "author": "TheScreamingLord", "created_time": "2025-03-10T14:40:39", "url": "https://reddit.com/r/PPC/comments/1j805p2/how_many_google_ads_do_you_currently_manage_im_at/", "upvotes": 62, "comments_count": 126, "sentiment": "neutral", "engagement_score": 314.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j855jx", "title": "Buying the dip - What is on your watchlist?📈", "content": "So i’ve been buying some stocks like google and amazon during this market dip but I have some more cash I want to invest and I am curios what stocks are on your watchlists? Doesn’t matter if it is a dividend stock or just a growth stock.", "author": "Regular_Newspaper990", "created_time": "2025-03-10T18:09:00", "url": "https://reddit.com/r/dividends/comments/1j855jx/buying_the_dip_what_is_on_your_watchlist/", "upvotes": 156, "comments_count": 169, "sentiment": "bullish", "engagement_score": 494.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j8bzvh", "title": "If you bought $VZ in October 2023 near the low ($31), you have outperforms S&P, MSFT, TSLA, AMZN, GOOG and most other tech stocks", "content": "When VZ hit a low of $31 in late 2023, there were lots of posts about it in this forum. The vast majority were repelled by the idea citing the balance sheet and other liabilities that were in the headlines. By any measure, the stock was at record low valuations on any metric one might use. The VZ business just does not change that much as it is a core service business at the backbone of all communications in this country. \n\nWhen companies like this are universally despised by investors, you should hold your nose and buy. \n\nThere was one stock that has outperformed VZ by over 20% annually over this period: AT&T which was even more hated and left for dead than Verizon.", "author": "mrmrmrj", "created_time": "2025-03-10T22:54:07", "url": "https://reddit.com/r/ValueInvesting/comments/1j8bzvh/if_you_bought_vz_in_october_2023_near_the_low_31/", "upvotes": 0, "comments_count": 26, "sentiment": "bullish", "engagement_score": 52.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}]