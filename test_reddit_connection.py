#!/usr/bin/env python3
"""
Reddit连接诊断工具

用于诊断和测试Reddit API连接问题，包括：
- 网络连接测试
- SSL证书验证
- Reddit API认证测试
- 子版块访问测试
- 错误详细分析
"""

import os
import sys
import time
import socket
import ssl
import requests
import logging
from datetime import datetime
from pathlib import Path

import praw
import prawcore
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class RedditConnectionDiagnostic:
    """Reddit连接诊断工具"""
    
    def __init__(self):
        self.setup_logging()
        self.results = []
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('reddit_diagnostic.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log_result(self, test_name: str, success: bool, message: str):
        """记录测试结果"""
        status = "SUCCESS" if success else "FAILED"
        result = f"{test_name}: {status} - {message}"
        self.results.append((test_name, success, message))
        print(result)
        # 避免Unicode编码问题
        try:
            if success:
                self.logger.info(f"{test_name}: SUCCESS - {message}")
            else:
                self.logger.error(f"{test_name}: FAILED - {message}")
        except UnicodeEncodeError:
            pass  # 忽略编码错误
    
    def test_basic_network(self):
        """测试基本网络连接"""
        print("\n=== 基本网络连接测试 ===")
        
        # 测试DNS解析
        try:
            import socket
            socket.gethostbyname('www.reddit.com')
            self.log_result("DNS解析", True, "www.reddit.com 解析成功")
        except Exception as e:
            self.log_result("DNS解析", False, f"DNS解析失败: {e}")
            return False
        
        # 测试TCP连接
        try:
            sock = socket.create_connection(("www.reddit.com", 443), timeout=10)
            sock.close()
            self.log_result("TCP连接", True, "www.reddit.com:443 连接成功")
        except Exception as e:
            self.log_result("TCP连接", False, f"TCP连接失败: {e}")
            return False
        
        # 测试HTTPS连接
        try:
            response = requests.get("https://www.reddit.com", timeout=10)
            self.log_result("HTTPS连接", True, f"状态码: {response.status_code}")
        except Exception as e:
            self.log_result("HTTPS连接", False, f"HTTPS连接失败: {e}")
            return False
        
        return True
    
    def test_ssl_certificate(self):
        """测试SSL证书"""
        print("\n=== SSL证书测试 ===")
        
        try:
            # 获取SSL证书信息
            context = ssl.create_default_context()
            with socket.create_connection(("www.reddit.com", 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname="www.reddit.com") as ssock:
                    cert = ssock.getpeercert()
                    self.log_result("SSL证书", True, f"证书主题: {cert.get('subject', 'N/A')}")
        except Exception as e:
            self.log_result("SSL证书", False, f"SSL证书验证失败: {e}")
            return False
        
        return True
    
    def test_reddit_api_endpoints(self):
        """测试Reddit API端点"""
        print("\n=== Reddit API端点测试 ===")
        
        endpoints = [
            "https://www.reddit.com/api/v1/me",
            "https://oauth.reddit.com/api/v1/me",
            "https://www.reddit.com/r/test.json",
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, timeout=10)
                self.log_result(f"API端点 {endpoint}", True, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_result(f"API端点 {endpoint}", False, f"请求失败: {e}")
    
    def test_reddit_authentication(self):
        """测试Reddit API认证"""
        print("\n=== Reddit API认证测试 ===")
        
        # 检查环境变量
        client_id = os.getenv('REDDIT_CLIENT_ID')
        client_secret = os.getenv('REDDIT_CLIENT_SECRET')
        user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-diagnostic/1.0')
        
        if not client_id or not client_secret:
            self.log_result("环境变量", False, "REDDIT_CLIENT_ID 或 REDDIT_CLIENT_SECRET 未设置")
            return False
        
        self.log_result("环境变量", True, "Reddit API凭据已配置")
        
        # 测试认证
        try:
            reddit = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent
            )
            
            # 测试基本访问
            test_subreddit = reddit.subreddit('test')
            _ = test_subreddit.display_name
            self.log_result("Reddit认证", True, "认证成功，可以访问子版块")
            
            return reddit
            
        except prawcore.exceptions.ResponseException as e:
            if e.response.status_code == 401:
                self.log_result("Reddit认证", False, "401 Unauthorized - 认证凭据无效")
            elif e.response.status_code == 403:
                self.log_result("Reddit认证", False, "403 Forbidden - 访问被禁止")
            else:
                self.log_result("Reddit认证", False, f"HTTP {e.response.status_code}: {e}")
        except Exception as e:
            self.log_result("Reddit认证", False, f"认证失败: {e}")
        
        return None
    
    def test_subreddit_access(self, reddit):
        """测试子版块访问"""
        if not reddit:
            return
        
        print("\n=== 子版块访问测试 ===")
        
        test_subreddits = [
            'test', 'stocks', 'investing', 'wallstreetbets',
            'technology', 'business', 'finance'
        ]
        
        for subreddit_name in test_subreddits:
            try:
                subreddit = reddit.subreddit(subreddit_name)
                _ = subreddit.display_name
                
                # 尝试获取一个帖子
                try:
                    posts = list(subreddit.new(limit=1))
                    self.log_result(f"r/{subreddit_name}", True, f"访问成功，可获取帖子")
                except Exception as e:
                    self.log_result(f"r/{subreddit_name}", False, f"可访问但无法获取帖子: {e}")
                
            except prawcore.exceptions.Forbidden:
                self.log_result(f"r/{subreddit_name}", False, "403 Forbidden - 访问被禁止")
            except prawcore.exceptions.NotFound:
                self.log_result(f"r/{subreddit_name}", False, "404 Not Found - 子版块不存在")
            except Exception as e:
                self.log_result(f"r/{subreddit_name}", False, f"访问失败: {e}")
            
            # 添加延迟避免限制
            time.sleep(1)
    
    def test_rate_limits(self, reddit):
        """测试API限制"""
        if not reddit:
            return
        
        print("\n=== API限制测试 ===")
        
        try:
            # 快速连续请求测试限制
            subreddit = reddit.subreddit('test')
            for i in range(5):
                _ = subreddit.display_name
                time.sleep(0.1)
            
            self.log_result("API限制", True, "未触发明显的API限制")
            
        except prawcore.exceptions.TooManyRequests:
            self.log_result("API限制", False, "触发了API限制 (429 Too Many Requests)")
        except Exception as e:
            self.log_result("API限制", False, f"测试失败: {e}")
    
    def run_full_diagnostic(self):
        """运行完整诊断"""
        print("Reddit连接诊断工具")
        print("=" * 50)
        print(f"开始时间: {datetime.now()}")
        
        # 运行所有测试
        if not self.test_basic_network():
            print("\n基本网络连接失败，跳过后续测试")
            return
        
        self.test_ssl_certificate()
        self.test_reddit_api_endpoints()
        reddit = self.test_reddit_authentication()
        self.test_subreddit_access(reddit)
        self.test_rate_limits(reddit)
        
        # 总结结果
        print("\n" + "=" * 50)
        print("诊断结果总结:")
        print("=" * 50)
        
        success_count = sum(1 for _, success, _ in self.results if success)
        total_count = len(self.results)
        
        print(f"总测试数: {total_count}")
        print(f"成功: {success_count}")
        print(f"失败: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        # 显示失败的测试
        failed_tests = [(name, msg) for name, success, msg in self.results if not success]
        if failed_tests:
            print("\n失败的测试:")
            for name, msg in failed_tests:
                print(f"  - {name}: {msg}")
        
        # 提供建议
        self.provide_recommendations()
    
    def provide_recommendations(self):
        """提供修复建议"""
        print("\n修复建议:")
        print("-" * 30)
        
        failed_tests = [name for name, success, _ in self.results if not success]
        
        if "DNS解析" in failed_tests:
            print("• 检查网络连接和DNS设置")
        
        if "TCP连接" in failed_tests or "HTTPS连接" in failed_tests:
            print("• 检查防火墙设置")
            print("• 检查代理设置")
        
        if "SSL证书" in failed_tests:
            print("• 更新系统证书")
            print("• 检查系统时间是否正确")
        
        if "Reddit认证" in failed_tests:
            print("• 检查Reddit API凭据")
            print("• 确认应用类型为'script'")
            print("• 检查User-Agent格式")
        
        if any("r/" in name for name in failed_tests):
            print("• Reddit可能对您的IP进行了临时限制")
            print("• 尝试更换网络或使用VPN")
            print("• 减少请求频率")

def main():
    diagnostic = RedditConnectionDiagnostic()
    diagnostic.run_full_diagnostic()

if __name__ == '__main__':
    main()
