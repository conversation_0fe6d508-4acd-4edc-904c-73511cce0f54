#!/usr/bin/env python3
"""
Reddit历史转储文件处理器

该脚本处理Reddit历史转储文件，提取与特定股票代码相关的帖子和评论，
并将其转换为与现有AI对冲基金回测系统兼容的JSON格式。

功能：
- 处理Reddit历史转储文件（JSONL格式）
- 按日期范围过滤（2025-01-01 到 2025-06-01）
- 按股票代码过滤（AAPL, MSFT, NVDA等）
- 搜索相关金融关键词
- 输出结构化JSON数据
- 按日期组织文件（TICKER_YYYY-MM-DD.json）
- 高效处理大文件
"""

import json
import os
import re
import gzip
import bz2
import lzma
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any, Optional, Set, Union, Tuple
import argparse
import logging
from pathlib import Path
from collections import defaultdict
import hashlib

# 导入配置
try:
    from reddit_dump_config import (
        get_ticker_mapping, get_finance_subreddits, get_financial_keywords,
        get_sentiment_keywords, get_processing_config, get_output_config
    )
    TICKER_TO_COMPANY = get_ticker_mapping()
    FINANCE_SUBREDDITS = get_finance_subreddits()
    FINANCIAL_KEYWORDS = get_financial_keywords()
    SENTIMENT_KEYWORDS = get_sentiment_keywords()
    PROCESSING_CONFIG = get_processing_config()
    OUTPUT_CONFIG = get_output_config()
except ImportError:
    # 如果配置文件不存在，使用默认配置
    TICKER_TO_COMPANY = {
        'AAPL': ['Apple', 'AAPL', 'Apple Inc', 'iPhone', 'iPad', 'Mac', 'iOS'],
        'MSFT': ['Microsoft', 'MSFT', 'Microsoft Corp', 'Windows', 'Azure', 'Office', 'Xbox'],
        'NVDA': ['NVIDIA', 'NVDA', 'Nvidia Corp', 'GeForce', 'RTX', 'GPU', 'AI chip', 'CUDA'],
    }
    FINANCE_SUBREDDITS = {
        'investing', 'stocks', 'SecurityAnalysis', 'ValueInvesting', 'financialindependence',
        'StockMarket', 'wallstreetbets', 'options', 'dividends', 'pennystocks'
    }
    FINANCIAL_KEYWORDS = {
        'earnings', 'revenue', 'profit', 'loss', 'stock', 'share', 'dividend'
    }
    SENTIMENT_KEYWORDS = {
        'positive': ['bullish', 'buy', 'long', 'up', 'rise', 'gain'],
        'negative': ['bearish', 'sell', 'short', 'down', 'fall', 'loss']
    }
    PROCESSING_CONFIG = {'batch_size': 10000, 'progress_interval': 10000}
    OUTPUT_CONFIG = {'indent': 2, 'ensure_ascii': False}

# 设置日志
log_level_str = str(PROCESSING_CONFIG.get('log_level', 'INFO'))
log_level = getattr(logging, log_level_str)
log_file = str(PROCESSING_CONFIG.get('log_file', 'reddit_dump_processor.log'))

logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RedditDumpProcessor:
    def __init__(self, dump_dir: str, output_dir: str, start_date: str, end_date: str):
        """
        初始化Reddit转储处理器
        
        Args:
            dump_dir: Reddit转储文件目录
            output_dir: 输出目录
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
        """
        self.dump_dir = Path(dump_dir)
        self.output_dir = Path(output_dir)
        self.start_date = datetime.strptime(start_date, '%Y-%m-%d')
        self.end_date = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 为每个股票代码创建子目录
        for ticker in TICKER_TO_COMPANY.keys():
            ticker_dir = self.output_dir / f"{ticker}_social_media"
            ticker_dir.mkdir(parents=True, exist_ok=True)
        
        # 统计信息
        self.stats = {
            'files_processed': 0,
            'posts_found': 0,
            'posts_filtered': 0,
            'posts_saved': defaultdict(int)
        }
        
        logger.info(f"初始化Reddit转储处理器")
        logger.info(f"转储目录: {self.dump_dir}")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"日期范围: {start_date} 到 {end_date}")

    def open_file(self, file_path: Path):
        """
        根据文件扩展名打开文件（支持压缩格式）
        """
        if file_path.suffix == '.gz':
            return gzip.open(file_path, 'rt', encoding='utf-8')
        elif file_path.suffix == '.bz2':
            return bz2.open(file_path, 'rt', encoding='utf-8')
        elif file_path.suffix == '.xz':
            return lzma.open(file_path, 'rt', encoding='utf-8')
        else:
            return open(file_path, 'r', encoding='utf-8')

    def is_relevant_subreddit(self, subreddit: str) -> bool:
        """
        检查子版块是否与金融相关
        """
        if not subreddit:
            return False
        return subreddit.lower() in FINANCE_SUBREDDITS

    def extract_tickers_from_text(self, text: str) -> Set[str]:
        """
        从文本中提取相关的股票代码
        """
        if not text:
            return set()
        
        text_lower = text.lower()
        found_tickers = set()
        
        for ticker, keywords in TICKER_TO_COMPANY.items():
            for keyword in keywords:
                if re.search(r'\b' + re.escape(keyword.lower()) + r'\b', text_lower):
                    found_tickers.add(ticker)
                    break
        
        return found_tickers

    def has_financial_content(self, text: str) -> bool:
        """
        检查文本是否包含金融相关内容
        """
        if not text:
            return False
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in FINANCIAL_KEYWORDS)

    def calculate_engagement_score(self, upvotes: int, comments: int) -> float:
        """
        计算参与度分数
        """
        return float(upvotes + (comments * 1.5))

    def determine_sentiment(self, title: str, content: str) -> str:
        """
        简单的情感分析（基于关键词）
        """
        text = f"{title} {content}".lower()

        positive_words = SENTIMENT_KEYWORDS.get('positive', ['bullish', 'buy', 'long', 'up', 'rise', 'gain'])
        negative_words = SENTIMENT_KEYWORDS.get('negative', ['bearish', 'sell', 'short', 'down', 'fall', 'loss'])

        positive_count = sum(1 for word in positive_words if word.lower() in text)
        negative_count = sum(1 for word in negative_words if word.lower() in text)

        if positive_count > negative_count:
            return 'bullish'
        elif negative_count > positive_count:
            return 'bearish'
        else:
            return 'neutral'

    def process_post(self, post_data: Dict[str, Any]) -> Optional[List[Tuple[str, Dict[str, Any]]]]:
        """
        处理单个Reddit帖子
        """
        try:
            # 检查必需字段
            if 'created_utc' not in post_data:
                return None
            
            # 转换时间戳
            post_date = datetime.fromtimestamp(post_data['created_utc'], tz=timezone.utc).replace(tzinfo=None)
            
            # 检查日期范围
            if not (self.start_date <= post_date <= self.end_date):
                return None
            
            # 获取子版块
            subreddit = post_data.get('subreddit', '')
            
            # 检查是否为相关子版块
            if not self.is_relevant_subreddit(subreddit):
                return None
            
            # 获取文本内容
            title = post_data.get('title', '')
            content = post_data.get('selftext', '') or post_data.get('body', '')
            
            # 检查是否包含股票相关内容
            found_tickers = self.extract_tickers_from_text(f"{title} {content}")
            
            # 如果没有找到相关股票代码，检查是否有金融内容
            if not found_tickers and not self.has_financial_content(f"{title} {content}"):
                return None
            
            # 如果没有找到特定股票代码但有金融内容，跳过（我们只关注特定股票）
            if not found_tickers:
                return None
            
            # 获取其他字段
            upvotes = post_data.get('ups', 0) or post_data.get('score', 0)
            comments_count = post_data.get('num_comments', 0)
            post_id = post_data.get('id', '')
            author = post_data.get('author', 'unknown')
            url = post_data.get('url', '') or f"https://reddit.com/r/{subreddit}/comments/{post_id}"
            
            # 计算参与度分数
            engagement_score = self.calculate_engagement_score(upvotes, comments_count)
            
            # 确定情感
            sentiment = self.determine_sentiment(title, content)
            
            # 为每个找到的股票代码创建记录
            results = []
            for ticker in found_tickers:
                post_record = {
                    "platform": "reddit",
                    "post_id": f"reddit_{post_date.strftime('%Y-%m-%d')}_{post_id}",
                    "title": title,
                    "content": content,
                    "author": author,
                    "created_time": post_date.strftime('%Y-%m-%dT%H:%M:%S'),
                    "url": url,
                    "upvotes": upvotes,
                    "comments_count": comments_count,
                    "ticker": ticker,
                    "sentiment": sentiment,
                    "engagement_score": engagement_score,
                    "source_subreddit": subreddit,
                    "hashtags": None
                }
                results.append((ticker, post_record))
            
            return results
            
        except Exception as e:
            logger.warning(f"处理帖子时出错: {e}")
            return None

    def process_dump_file(self, file_path: Path) -> Dict[str, List[Dict[str, Any]]]:
        """
        处理单个转储文件
        """
        logger.info(f"处理文件: {file_path}")
        
        posts_by_ticker = defaultdict(list)
        line_count = 0
        processed_count = 0
        
        try:
            with self.open_file(file_path) as f:
                for line in f:
                    line_count += 1
                    
                    if line_count % 10000 == 0:
                        logger.info(f"已处理 {line_count} 行，找到 {processed_count} 个相关帖子")
                    
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        post_data = json.loads(line)
                        results = self.process_post(post_data)
                        
                        if results:
                            for ticker, post_record in results:
                                posts_by_ticker[ticker].append(post_record)
                                processed_count += 1
                                
                    except json.JSONDecodeError:
                        continue
                    except Exception as e:
                        logger.warning(f"处理行时出错: {e}")
                        continue
        
        except Exception as e:
            logger.error(f"读取文件 {file_path} 时出错: {e}")
            return {}
        
        logger.info(f"文件 {file_path} 处理完成: {line_count} 行，{processed_count} 个相关帖子")
        self.stats['files_processed'] += 1
        self.stats['posts_found'] += processed_count
        
        return posts_by_ticker

    def save_posts_by_date(self, posts_by_ticker: Dict[str, List[Dict[str, Any]]]):
        """
        按日期保存帖子
        """
        for ticker, posts in posts_by_ticker.items():
            # 按日期分组
            posts_by_date = defaultdict(list)
            
            for post in posts:
                date_str = post['created_time'][:10]  # 提取YYYY-MM-DD
                posts_by_date[date_str].append(post)
            
            # 保存每个日期的文件
            for date_str, date_posts in posts_by_date.items():
                output_file = self.output_dir / f"{ticker}_social_media" / f"reddit_{date_str}.json"
                
                # 如果文件已存在，合并数据
                existing_posts = []
                if output_file.exists():
                    try:
                        with open(output_file, 'r', encoding='utf-8') as f:
                            existing_posts = json.load(f)
                    except Exception as e:
                        logger.warning(f"读取现有文件 {output_file} 时出错: {e}")
                
                # 合并并去重（基于post_id）
                all_posts = existing_posts + date_posts
                unique_posts = {}
                for post in all_posts:
                    unique_posts[post['post_id']] = post
                
                final_posts = list(unique_posts.values())
                
                # 按时间排序
                final_posts.sort(key=lambda x: x['created_time'], reverse=True)
                
                # 保存文件
                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(final_posts, f, ensure_ascii=False, indent=2)
                    
                    self.stats['posts_saved'][ticker] += len(date_posts)
                    logger.info(f"保存 {ticker} {date_str}: {len(final_posts)} 个帖子")
                    
                except Exception as e:
                    logger.error(f"保存文件 {output_file} 时出错: {e}")

    def process_all_dumps(self):
        """
        处理所有转储文件
        """
        logger.info("开始处理所有Reddit转储文件...")
        
        # 查找所有转储文件
        dump_files = []
        for pattern in ['*.jsonl', '*.json', '*.jsonl.gz', '*.json.gz', '*.jsonl.bz2', '*.json.bz2', '*.jsonl.xz', '*.json.xz']:
            dump_files.extend(self.dump_dir.glob(pattern))
        
        if not dump_files:
            logger.error(f"在目录 {self.dump_dir} 中未找到转储文件")
            return
        
        logger.info(f"找到 {len(dump_files)} 个转储文件")
        
        # 处理每个文件
        for dump_file in sorted(dump_files):
            posts_by_ticker = self.process_dump_file(dump_file)
            if posts_by_ticker:
                self.save_posts_by_date(posts_by_ticker)
        
        # 打印统计信息
        self.print_stats()

    def print_stats(self):
        """
        打印处理统计信息
        """
        logger.info("=" * 50)
        logger.info("处理完成统计:")
        logger.info(f"处理文件数: {self.stats['files_processed']}")
        logger.info(f"找到相关帖子数: {self.stats['posts_found']}")
        logger.info("按股票代码保存的帖子数:")
        for ticker, count in self.stats['posts_saved'].items():
            logger.info(f"  {ticker}: {count}")
        logger.info("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='Reddit历史转储文件处理器')
    parser.add_argument('--dump-dir', required=True, help='Reddit转储文件目录')
    parser.add_argument('--output-dir', default='social_media_data', help='输出目录 (默认: social_media_data)')
    parser.add_argument('--start-date', default='2025-01-01', help='开始日期 (YYYY-MM-DD, 默认: 2025-01-01)')
    parser.add_argument('--end-date', default='2025-06-01', help='结束日期 (YYYY-MM-DD, 默认: 2025-06-01)')
    parser.add_argument('--tickers', nargs='+', default=list(TICKER_TO_COMPANY.keys()), 
                       help='要处理的股票代码列表 (默认: 所有支持的股票)')
    
    args = parser.parse_args()
    
    # 验证日期格式
    try:
        datetime.strptime(args.start_date, '%Y-%m-%d')
        datetime.strptime(args.end_date, '%Y-%m-%d')
    except ValueError:
        logger.error("日期格式错误，请使用 YYYY-MM-DD 格式")
        return
    
    # 过滤股票代码
    if args.tickers != list(TICKER_TO_COMPANY.keys()):
        filtered_tickers = {k: v for k, v in TICKER_TO_COMPANY.items() if k in args.tickers}
        TICKER_TO_COMPANY.clear()
        TICKER_TO_COMPANY.update(filtered_tickers)
    
    # 创建处理器并运行
    processor = RedditDumpProcessor(
        dump_dir=args.dump_dir,
        output_dir=args.output_dir,
        start_date=args.start_date,
        end_date=args.end_date
    )
    
    processor.process_all_dumps()


if __name__ == '__main__':
    main()
