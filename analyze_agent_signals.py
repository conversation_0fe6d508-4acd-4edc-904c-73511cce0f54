#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对冲基金系统代理信号分析和可视化工具

该脚本用于分析reasoning_logs目录下的实验数据，提取各个代理的交易信号和置信度，
并生成综合可视化图表。

使用方法:
    python analyze_agent_signals.py --experiment_path reasoning_logs/experiment_AAPL_20250101-20250601_gemini2.0_flash
    python analyze_agent_signals.py --experiment_path reasoning_logs/experiment_MSFT_20250101-20250601_gpt3.5 --output_dir ./charts
"""

import os
import json
import argparse
import pandas as pd
from datetime import datetime
import numpy as np
from collections import defaultdict
from pathlib import Path
import warnings
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

# 忽略警告
warnings.filterwarnings('ignore')

class AgentSignalAnalyzer:
    """代理信号分析器"""
    
    def __init__(self, experiment_path):
        self.experiment_path = Path(experiment_path)
        self.excluded_agents = {
            'portfolio_manager_agent', 
            'risk_management_agent', 
            'reflection_analyst'
        }
        
        # 信号映射
        self.signal_mapping = {
            'bullish': 'BUY',
            'bearish': 'SELL', 
            'neutral': 'HOLD',
            'buy': 'BUY',
            'sell': 'SELL',
            'hold': 'HOLD'
        }
        
        # 颜色映射
        self.signal_colors = {
            'BUY': '#2E8B57',    # 绿色
            'SELL': '#DC143C',   # 红色  
            'HOLD': '#808080'    # 灰色
        }
        
        # 代理名称映射（中文显示）
        self.agent_name_mapping = {
            'warren_buffett_agent': 'Warren Buffett',
            'ben_graham_agent': 'Ben Graham',
            'peter_lynch_agent': 'Peter Lynch',
            'bill_ackman_agent': 'Bill Ackman',
            'cathie_wood_agent': 'Cathie Wood',
            'charlie_munger_agent': 'Charlie Munger',
            'phil_fisher_agent': 'Phil Fisher',
            'michael_burry_agent': 'Michael Burry',
            'stanley_druckenmiller_agent': 'Stanley Druckenmiller',
            'aswath_damodaran_agent': 'Aswath Damodaran',
            'technical_analyst_agent': '技术分析师',
            'fundamentals_agent': '基本面分析师',
            'sentiment_agent': '情绪分析师',
            'valuation_agent': '估值分析师',
            'factual_news_agent': '事实新闻分析师',
            'subjective_news_agent': '主观新闻分析师',
            'news_analyst_agent': '新闻分析师',
            'social_media_analyst_agent': '社交媒体分析师',
            'fundamentals_analyst_agent': '基本面分析师',
            'market_analyst_agent': '市场分析师',
            # 处理文件名解析出的简化代理名称
            'buffett_agent': 'Warren Buffett',
            'graham_agent': 'Ben Graham',
            'lynch_agent': 'Peter Lynch',
            'ackman_agent': 'Bill Ackman',
            'wood_agent': 'Cathie Wood',
            'munger_agent': 'Charlie Munger',
            'fisher_agent': 'Phil Fisher',
            'burry_agent': 'Michael Burry',
            'druckenmiller_agent': 'Stanley Druckenmiller',
            'damodaran_agent': 'Aswath Damodaran',
            'analyst_agent': '市场分析师',
            'news_agent': '新闻分析师',
            'management_agent': '风险管理',
            'manager_agent': '投资组合经理',
            # 处理带股票代码前缀的代理名称
            'AAPL_fundamentals_agent': '基本面分析师',
            'AAPL_sentiment_agent': '情绪分析师',
            'AAPL_valuation_agent': '估值分析师',
            'AAPL_technical_analyst_agent': '技术分析师',
            'MSFT_fundamentals_agent': '基本面分析师',
            'MSFT_sentiment_agent': '情绪分析师',
            'MSFT_valuation_agent': '估值分析师',
            'MSFT_technical_analyst_agent': '技术分析师',
            'NVDA_fundamentals_agent': '基本面分析师',
            'NVDA_sentiment_agent': '情绪分析师',
            'NVDA_valuation_agent': '估值分析师',
            'NVDA_technical_analyst_agent': '技术分析师',
            'GOOGL_fundamentals_agent': '基本面分析师',
            'GOOGL_sentiment_agent': '情绪分析师',
            'GOOGL_valuation_agent': '估值分析师',
            'GOOGL_technical_analyst_agent': '技术分析师',
            'TSLA_fundamentals_agent': '基本面分析师',
            'TSLA_sentiment_agent': '情绪分析师',
            'TSLA_valuation_agent': '估值分析师',
            'TSLA_technical_analyst_agent': '技术分析师'
        }
        
    def extract_experiment_info(self):
        """从实验路径中提取股票代码、日期范围和模型信息"""
        path_name = self.experiment_path.name
        parts = path_name.replace('experiment_', '').split('_')
        
        if len(parts) >= 3:
            ticker = parts[0]
            date_range = parts[1]
            model = '_'.join(parts[2:])
            return ticker, date_range, model
        else:
            return "UNKNOWN", "UNKNOWN", "UNKNOWN"
    
    def load_agent_data(self):
        """加载所有代理的数据"""
        data = defaultdict(lambda: defaultdict(dict))
        
        if not self.experiment_path.exists():
            print(f"错误：实验路径不存在: {self.experiment_path}")
            return data
            
        # 遍历所有日期目录
        for date_dir in sorted(self.experiment_path.iterdir()):
            if not date_dir.is_dir() or date_dir.name == 'input_data':
                continue
                
            date_str = date_dir.name
            
            # 遍历该日期下的所有代理文件
            for json_file in date_dir.glob("*.json"):
                try:
                    # 解析文件名获取代理名称
                    filename = json_file.stem
                    parts = filename.split('_')

                    # 改进的代理名称解析逻辑
                    agent_name = None

                    # 方法1: 寻找以_agent结尾的部分
                    for i, part in enumerate(parts):
                        if part.endswith('_agent'):
                            agent_name = part
                            break
                        elif part == 'agent' and i > 0:
                            # 处理 "fundamentals_analyst_agent" 类型
                            if i >= 2:
                                agent_name = '_'.join(parts[i-2:i+1])
                            else:
                                agent_name = '_'.join(parts[i-1:i+1])
                            break

                    # 方法2: 如果没找到，尝试寻找包含agent的连续部分
                    if not agent_name:
                        for i in range(len(parts)):
                            if 'agent' in parts[i]:
                                # 向前查找可能的代理名称组合
                                start_idx = max(0, i-2)
                                agent_name = '_'.join(parts[start_idx:i+1])
                                break

                    # 方法3: 最后的备选方案，寻找已知的代理名称模式
                    if not agent_name:
                        known_patterns = ['buffett', 'graham', 'lynch', 'ackman', 'wood', 'munger',
                                        'fisher', 'burry', 'druckenmiller', 'damodaran', 'technical',
                                        'fundamentals', 'sentiment', 'valuation', 'news', 'analyst']
                        for pattern in known_patterns:
                            if pattern in filename:
                                agent_name = pattern + '_agent'
                                break

                    # 方法4: 处理包含股票代码的代理名称（如 NVDA_fundamentals_agent）
                    if not agent_name:
                        # 移除股票代码前缀，寻找代理类型
                        clean_filename = filename
                        for ticker_code in ['AAPL', 'MSFT', 'NVDA', 'GOOGL', 'TSLA']:
                            if filename.startswith(f'{ticker_code}_'):
                                clean_filename = filename[len(ticker_code)+1:]
                                break

                        # 在清理后的文件名中寻找代理名称
                        if '_agent' in clean_filename:
                            agent_name = clean_filename
                        elif 'agent' in clean_filename:
                            agent_name = clean_filename

                    if not agent_name or agent_name in self.excluded_agents:
                        continue
                        
                    # 读取JSON文件
                    with open(json_file, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    
                    # 提取信号和置信度
                    signal, confidence = self.extract_signal_confidence(json_data)
                    
                    if signal and confidence is not None:
                        data[date_str][agent_name] = {
                            'signal': signal,
                            'confidence': confidence,
                            'raw_data': json_data
                        }
                        
                except Exception as e:
                    print(f"处理文件 {json_file} 时出错: {e}")
                    continue
                    
        return data
    
    def extract_signal_confidence(self, json_data):
        """从JSON数据中提取信号和置信度"""
        try:
            reasoning = json_data.get('reasoning', {})
            
            # 获取信号
            signal = reasoning.get('signal', '').lower()
            signal = self.signal_mapping.get(signal, signal.upper() if signal else 'HOLD')
            
            # 获取置信度
            confidence = reasoning.get('confidence')
            
            # 处理置信度的不同格式
            if confidence is not None:
                if isinstance(confidence, (int, float)):
                    # 如果置信度大于1，假设是百分比形式
                    if confidence > 1:
                        confidence = confidence / 100.0
                elif isinstance(confidence, str):
                    try:
                        confidence = float(confidence.replace('%', '')) / 100.0
                    except:
                        confidence = 0.5  # 默认值
                else:
                    confidence = 0.5
            else:
                confidence = 0.5
                
            return signal, confidence
            
        except Exception as e:
            print(f"提取信号和置信度时出错: {e}")
            return 'HOLD', 0.5
    
    def create_comprehensive_visualization(self, data, output_dir='./charts'):
        """创建综合可视化HTML报告"""
        if not data:
            print("没有数据可供可视化")
            return

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # 获取实验信息
        ticker, date_range, model = self.extract_experiment_info()

        # 准备数据
        dates = sorted(data.keys())
        agents = set()
        for date_data in data.values():
            agents.update(date_data.keys())
        agents = sorted(list(agents))

        # 创建交互式热力图
        html_content = self.create_interactive_heatmap(data, dates, agents, ticker, model, date_range)

        # 保存HTML文件
        filename = f"agent_analysis_{ticker}_{model}_{date_range}.html"
        filepath = output_path / filename

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"交互式HTML报告已保存至: {filepath}")

        # 创建详细统计报告
        self.create_statistics_report(data, output_path, ticker, model, date_range)
    
    def create_interactive_heatmap(self, data, dates, agents, ticker, model, date_range):
        """创建交互式热力图HTML报告"""
        # 准备数据
        z_values = []  # 用于热力图的数值矩阵
        hover_text = []  # 悬停文本矩阵
        colors = []  # 颜色矩阵

        # 为每个代理创建一行数据
        for agent in agents:
            agent_row = []
            hover_row = []
            color_row = []

            for date in dates:
                if date in data and agent in data[date]:
                    signal = data[date][agent]['signal']
                    confidence = data[date][agent]['confidence']

                    # 数值编码：BUY=1, SELL=-1, HOLD=0
                    if signal == 'BUY':
                        value = 1
                        signal_text = 'Bullish'
                        base_color = 'green'
                    elif signal == 'SELL':
                        value = -1
                        signal_text = 'Bearish'
                        base_color = 'red'
                    else:  # HOLD
                        value = 0
                        signal_text = 'Neutral'
                        base_color = 'gray'

                    agent_row.append(confidence)  # 使用置信度作为热力图数值

                    # 创建悬停文本
                    agent_display = self.agent_name_mapping.get(agent, agent)
                    hover_text_item = (
                        f"<b>代理:</b> {agent_display}<br>"
                        f"<b>日期:</b> {date}<br>"
                        f"<b>信号:</b> {signal_text}<br>"
                        f"<b>置信度:</b> {confidence:.2f}"
                    )
                    hover_row.append(hover_text_item)

                    # 根据信号类型和置信度确定颜色强度
                    color_row.append(value * confidence)  # 正值=绿色，负值=红色，0=灰色

                else:
                    agent_row.append(None)
                    hover_row.append("无数据")
                    color_row.append(None)

            z_values.append(agent_row)
            hover_text.append(hover_row)
            colors.append(color_row)

        # 创建自定义颜色映射
        # 使用RdYlGn颜色映射的反向版本：红色(负值) -> 灰色(0) -> 绿色(正值)
        colorscale = [
            [0.0, '#DC143C'],    # 深红色 (Bearish, 高置信度)
            [0.25, '#FF6B6B'],   # 浅红色 (Bearish, 低置信度)
            [0.45, '#D3D3D3'],   # 浅灰色 (Neutral, 低置信度)
            [0.55, '#808080'],   # 深灰色 (Neutral, 高置信度)
            [0.75, '#90EE90'],   # 浅绿色 (Bullish, 低置信度)
            [1.0, '#2E8B57']     # 深绿色 (Bullish, 高置信度)
        ]

        # 获取代理显示名称
        agent_display_names = [self.agent_name_mapping.get(agent, agent) for agent in agents]

        # 创建plotly热力图
        fig = go.Figure(data=go.Heatmap(
            z=colors,
            x=dates,
            y=agent_display_names,
            hovertemplate='%{customdata}<extra></extra>',
            customdata=hover_text,
            colorscale=colorscale,
            zmid=0,  # 设置中点为0（灰色）
            showscale=True,
            colorbar=dict(
                title=dict(text="信号强度", side="right"),
                tickmode="array",
                tickvals=[-1, -0.5, 0, 0.5, 1],
                ticktext=["强烈看跌", "轻微看跌", "中性", "轻微看涨", "强烈看涨"],
                len=0.7
            )
        ))

        # 添加置信度数值标注
        annotations = []
        for i, agent in enumerate(agents):
            for j, date in enumerate(dates):
                if date in data and agent in data[date]:
                    confidence = data[date][agent]['confidence']
                    signal = data[date][agent]['signal']

                    # 根据背景颜色选择文字颜色
                    if signal == 'BUY':
                        text_color = 'white' if confidence > 0.6 else 'black'
                    elif signal == 'SELL':
                        text_color = 'white' if confidence > 0.6 else 'black'
                    else:  # HOLD
                        text_color = 'white' if confidence > 0.5 else 'black'

                    # 格式化置信度显示
                    if confidence >= 0.995:
                        display_text = '1.00'
                    elif confidence <= 0.005:
                        display_text = '0.00'
                    else:
                        display_text = f'{confidence:.2f}'

                    annotations.append(
                        dict(
                            x=date,
                            y=agent_display_names[i],
                            text=display_text,
                            showarrow=False,
                            font=dict(color=text_color, size=10, family="Arial Black"),
                            bgcolor="rgba(255,255,255,0.3)",
                            bordercolor="rgba(255,255,255,0.5)",
                            borderwidth=1
                        )
                    )

        # 设置图表布局
        fig.update_layout(
            title={
                'text': f'AI对冲基金系统代理交易信号与置信度分析 - {ticker} ({model})',
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'family': 'Arial Black'}
            },
            xaxis_title="日期",
            yaxis_title="代理",
            font=dict(family="Arial", size=12),
            plot_bgcolor='white',
            paper_bgcolor='white',
            width=max(1200, len(dates) * 40),
            height=max(800, len(agents) * 50),
            annotations=annotations,
            xaxis=dict(
                tickangle=45,
                tickfont=dict(size=10),
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray'
            ),
            yaxis=dict(
                tickfont=dict(size=11),
                showgrid=True,
                gridwidth=1,
                gridcolor='lightgray'
            )
        )

        # 生成HTML内容
        html_content = self.generate_html_report(fig, ticker, model, date_range, data)
        return html_content

    def generate_html_report(self, fig, ticker, model, date_range, data):
        """生成完整的HTML报告"""
        # 将plotly图表转换为HTML
        plot_html = pyo.plot(fig, output_type='div', include_plotlyjs=True)

        # 计算统计信息
        stats = self.calculate_statistics(data)

        # 生成HTML模板
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对冲基金代理信号分析报告 - {ticker}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .content {{
            padding: 30px;
        }}
        .chart-container {{
            margin: 20px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        .stat-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }}
        .stat-card h3 {{
            margin: 0 0 10px 0;
            color: #667eea;
            font-size: 1.1em;
        }}
        .stat-card p {{
            margin: 5px 0;
            font-size: 0.95em;
        }}
        .legend {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }}
        .legend h3 {{
            margin: 0 0 15px 0;
            color: #333;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            margin: 8px 0;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 10px;
        }}
        .bullish {{ background-color: #2E8B57; }}
        .bearish {{ background-color: #DC143C; }}
        .neutral {{ background-color: #808080; }}
        .footer {{
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI对冲基金代理信号分析报告</h1>
            <p>{ticker} | {model} | {date_range}</p>
        </div>

        <div class="content">
            <div class="legend">
                <h3>📊 图表说明</h3>
                <div class="legend-item">
                    <div class="legend-color bullish"></div>
                    <span><strong>绿色 (Bullish):</strong> 看涨信号，颜色越深置信度越高</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color bearish"></div>
                    <span><strong>红色 (Bearish):</strong> 看跌信号，颜色越深置信度越高</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color neutral"></div>
                    <span><strong>灰色 (Neutral):</strong> 中性信号，颜色越深置信度越高</span>
                </div>
                <p><strong>💡 使用提示:</strong> 将鼠标悬停在热力图上查看详细信息，包括代理名称、日期、信号类型和置信度值。</p>
            </div>

            <div class="chart-container">
                {plot_html}
            </div>

            <div class="stats-grid">
                {self.generate_stats_cards(stats)}
            </div>
        </div>

        <div class="footer">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | AI对冲基金系统</p>
        </div>
    </div>
</body>
</html>
        """

        return html_template

    def calculate_statistics(self, data):
        """计算统计信息"""
        stats = {
            'total_signals': 0,
            'bullish_count': 0,
            'bearish_count': 0,
            'neutral_count': 0,
            'avg_confidence': 0,
            'agent_stats': {},
            'date_coverage': len(data)
        }

        total_confidence = 0

        for date, agents_data in data.items():
            for agent, signal_data in agents_data.items():
                stats['total_signals'] += 1
                signal = signal_data['signal']
                confidence = signal_data['confidence']

                # 统计信号类型
                if signal == 'BUY':
                    stats['bullish_count'] += 1
                elif signal == 'SELL':
                    stats['bearish_count'] += 1
                else:
                    stats['neutral_count'] += 1

                # 累计置信度
                total_confidence += confidence

                # 代理统计
                if agent not in stats['agent_stats']:
                    stats['agent_stats'][agent] = {
                        'total': 0, 'bullish': 0, 'bearish': 0, 'neutral': 0,
                        'avg_confidence': 0, 'total_confidence': 0
                    }

                agent_stat = stats['agent_stats'][agent]
                agent_stat['total'] += 1
                agent_stat['total_confidence'] += confidence

                if signal == 'BUY':
                    agent_stat['bullish'] += 1
                elif signal == 'SELL':
                    agent_stat['bearish'] += 1
                else:
                    agent_stat['neutral'] += 1

        # 计算平均置信度
        if stats['total_signals'] > 0:
            stats['avg_confidence'] = total_confidence / stats['total_signals']

        # 计算每个代理的平均置信度
        for agent_stat in stats['agent_stats'].values():
            if agent_stat['total'] > 0:
                agent_stat['avg_confidence'] = agent_stat['total_confidence'] / agent_stat['total']

        return stats

    def generate_stats_cards(self, stats):
        """生成统计卡片HTML"""
        cards_html = f"""
            <div class="stat-card">
                <h3>📈 总体统计</h3>
                <p>总信号数: <strong>{stats['total_signals']}</strong></p>
                <p>平均置信度: <strong>{stats['avg_confidence']:.2f}</strong></p>
                <p>覆盖日期: <strong>{stats['date_coverage']}</strong> 天</p>
            </div>

            <div class="stat-card">
                <h3>🎯 信号分布</h3>
                <p>看涨信号: <strong>{stats['bullish_count']}</strong> ({stats['bullish_count']/max(stats['total_signals'],1)*100:.1f}%)</p>
                <p>看跌信号: <strong>{stats['bearish_count']}</strong> ({stats['bearish_count']/max(stats['total_signals'],1)*100:.1f}%)</p>
                <p>中性信号: <strong>{stats['neutral_count']}</strong> ({stats['neutral_count']/max(stats['total_signals'],1)*100:.1f}%)</p>
            </div>
        """

        # 找出最活跃的代理
        if stats['agent_stats']:
            most_active_agent = max(stats['agent_stats'].items(), key=lambda x: x[1]['total'])
            agent_name = self.agent_name_mapping.get(most_active_agent[0], most_active_agent[0])
            agent_data = most_active_agent[1]

            cards_html += f"""
            <div class="stat-card">
                <h3>🏆 最活跃代理</h3>
                <p>代理: <strong>{agent_name}</strong></p>
                <p>信号数: <strong>{agent_data['total']}</strong></p>
                <p>平均置信度: <strong>{agent_data['avg_confidence']:.2f}</strong></p>
            </div>
            """

        return cards_html

    def create_statistics_report(self, data, output_path, ticker, model, date_range):
        """创建统计报告"""
        stats = defaultdict(lambda: {'BUY': 0, 'SELL': 0, 'HOLD': 0, 'avg_confidence': 0, 'total_signals': 0})
        
        for date_data in data.values():
            for agent, agent_data in date_data.items():
                signal = agent_data['signal']
                confidence = agent_data['confidence']
                
                stats[agent][signal] += 1
                stats[agent]['total_signals'] += 1
                stats[agent]['avg_confidence'] += confidence
        
        # 计算平均置信度
        for agent in stats:
            if stats[agent]['total_signals'] > 0:
                stats[agent]['avg_confidence'] /= stats[agent]['total_signals']
        
        # 创建统计报告
        report_lines = [
            f"AI对冲基金系统代理统计报告",
            f"股票: {ticker}",
            f"模型: {model}",
            f"日期范围: {date_range}",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "=" * 60,
            ""
        ]
        
        for agent in sorted(stats.keys()):
            display_name = self.agent_name_mapping.get(agent, agent)
            agent_stats = stats[agent]
            
            report_lines.extend([
                f"代理: {display_name}",
                f"  总信号数: {agent_stats['total_signals']}",
                f"  BUY信号: {agent_stats['BUY']} ({agent_stats['BUY']/agent_stats['total_signals']*100:.1f}%)",
                f"  SELL信号: {agent_stats['SELL']} ({agent_stats['SELL']/agent_stats['total_signals']*100:.1f}%)",
                f"  HOLD信号: {agent_stats['HOLD']} ({agent_stats['HOLD']/agent_stats['total_signals']*100:.1f}%)",
                f"  平均置信度: {agent_stats['avg_confidence']:.3f}",
                ""
            ])
        
        # 保存报告
        report_filename = f"agent_statistics_{ticker}_{model}_{date_range}.txt"
        report_path = output_path / report_filename
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"统计报告已保存至: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='AI对冲基金系统代理信号分析工具')
    parser.add_argument('--experiment_path', required=True, 
                       help='实验数据路径，例如: reasoning_logs/experiment_AAPL_20250101-20250601_gemini2.0_flash')
    parser.add_argument('--output_dir', default='./charts',
                       help='输出目录 (默认: ./charts)')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = AgentSignalAnalyzer(args.experiment_path)
    
    print(f"正在分析实验数据: {args.experiment_path}")
    
    # 加载数据
    data = analyzer.load_agent_data()
    
    if not data:
        print("未找到有效数据，请检查实验路径是否正确")
        return
    
    print(f"成功加载 {len(data)} 天的数据")
    
    # 创建可视化
    analyzer.create_comprehensive_visualization(data, args.output_dir)
    
    print("分析完成！")

if __name__ == "__main__":
    main()
