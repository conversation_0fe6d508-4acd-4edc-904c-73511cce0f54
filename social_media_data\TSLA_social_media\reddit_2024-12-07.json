[{"platform": "reddit", "post_id": "reddit_1h8fqvz", "title": "Tesla to lead the way on the shift to 48-volt electrical architecture", "content": "", "author": "wewewawa", "created_time": "2024-12-07T00:10:42", "url": "https://reddit.com/r/electriccars/comments/1h8fqvz/tesla_to_lead_the_way_on_the_shift_to_48volt/", "upvotes": 5, "comments_count": 49, "sentiment": "neutral", "engagement_score": 103.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8h8fk", "title": "Done well this year, time to ramp it up in '25 🚀📈", "content": "I'm putting a majority of my money in pennystocks for 2025. I believe these ones will do well and have the potential to at least double in share price.\nKULR, LODE, D-WAVE, RGTI and BBAI 🚀📈\nAnd still hodling 20%  PLTR at a $23 cost basis.\nWhat do you think of my picks?\nPortfolio size is $40,000ish\n@RemindMe1year\n\n\n\nEdit: Going to add SERV on Monday 🤖🚀📈", "author": "JediRebel79", "created_time": "2024-12-07T01:25:01", "url": "https://reddit.com/r/pennystocks/comments/1h8h8fk/done_well_this_year_time_to_ramp_it_up_in_25/", "upvotes": 141, "comments_count": 104, "sentiment": "neutral", "engagement_score": 349.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8ikd7", "title": "Managed to save 50K at 23", "content": "Hey y’all. Just checked my portfolio and noticed I crossed the 50k mark, woohoo!! Here’s how it’s split up:\n\nRoth IRA - 17.3k\nRoth 401k - 14.1k\nRobinhood - 14.5k\nHSA - 2.5k\nHYSA - 7.5k\n\nStudent Loans - 4.5k\n\nCurrently putting 16% of my paycheck into my Roth 401k, and have maxed out my ROTH IRA in FZROX for the past 2 years. Robinhood is mainly NVDA and a tiny bit TSLA. \n\nGot a OK paying SWE job in June 2023, made 71k and was fortunate enough to stay at home with my parents. I paid for groceries and renovated the home a bit, got them new furniture and re-painted our house so it didn’t feel like I was leeching off them haha. Should receive a promotion here at years end that should put me at 95k, and I plan to move out next year. Can’t tell my parents how much I have saved or they’ll start asking me for loans and I don’t have the heart to say no to them :(. \n \nI wanna try to hit 80k invested/saved total by the end of next year.\n\nI plan to get a used car, I don’t need any fancy new vehicle that puts me in debt, so should I start putting more towards my HYSA? Also any other general advice that could help me out? Thanks in advance 🤝", "author": "Remote_Confidence_26", "created_time": "2024-12-07T02:35:07", "url": "https://reddit.com/r/Fire/comments/1h8ikd7/managed_to_save_50k_at_23/", "upvotes": 9, "comments_count": 6, "sentiment": "bearish", "engagement_score": 21.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8iwbv", "title": "Veteran - thinking about transitioning to data analytics but hesitant if oversaturated", "content": "Ok my fellow redditians. I is a veteran. I is a smarty (not really lol). I gots me 2 bachelors degrees, one in Pyschology (useless), and one in Information Technology. I never got really far in IT, because basically my school sucked, I had the Comptia A+ cert and let it expire because with all the studying I did I could never get a job that wasn't a call center/help desk. I can't do those jobs well bc I is also a deafy boi from big boom boom in sand land. I have some somewhat relevant SQL and Excel experience, and have reviewed a few Tubers talking about blah blah, you need excel, sql and tableau and you can get a job if you do my course and network blah blah. I am trying to see if I actually put the time into this, make my resume look shiny, don't list my crappy employment hx bc of my disabilabuddies from the military if I stand a chance after 6 months of study and maybe that google cert. I think I can be a shiny turd on paper, but looking for opinions from those that have tried, those that have failed and those that are lucky enough to have succeeded plz. Thankee. ", "author": "gritsofblasphemy", "created_time": "2024-12-07T02:53:07", "url": "https://reddit.com/r/analytics/comments/1h8iwbv/veteran_thinking_about_transitioning_to_data/", "upvotes": 0, "comments_count": 34, "sentiment": "neutral", "engagement_score": 68.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8jy6k", "title": "<PERSON> @ DealBook Summit: \"Let's be clear. He's truly one of the great entrepreneurs of our lifetime. No ifs, no ands, no buts. He runs Tesla, he runs SpaceX at a level of excellence that very few companies can even start to relate to. <...>\"", "content": "", "author": "twinbee", "created_time": "2024-12-07T03:51:48", "url": "https://reddit.com/r/elonmusk/comments/1h8jy6k/ken_griffin_dealbook_summit_lets_be_clear_hes/", "upvotes": 50, "comments_count": 196, "sentiment": "neutral", "engagement_score": 442.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8kgck", "title": "What age should I start saving for retirement?", "content": "Just planning on paying away my debt first, heres my situation\n\nIncome: 15k a month (still have to pay tax) sometimes on a good month I make 20k\n\nDebt: 200K\n\nMonthly expenses: 6K a month (includes monthly debt payments, groceries, rent, etc)\n\nCurrently only 30 years old. I don’t know where my timeline is or what age I should be retiring. Can i even afford to go on vacations and stuff? Im single with no kids wither\n\nEdit: since people asking about the nature of the debt,\n50K with zero interest, paying about $550 a month\n150K with about 9%, paying under 3k a month", "author": "bammie6969", "created_time": "2024-12-07T04:20:42", "url": "https://reddit.com/r/FinancialPlanning/comments/1h8kgck/what_age_should_i_start_saving_for_retirement/", "upvotes": 1, "comments_count": 69, "sentiment": "neutral", "engagement_score": 139.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8lm2n", "title": "What Project helped you land your first Analysis Job?", "content": "Hello all,\n\nI want to transition careers into Data Analytics. My background is in teaching Mathematics. I have a degree in math and have programming knowledge from college however i am rusty as i graduate 8 years ago. \n\nI’ve lurked this sub for a while now and understand that having a portfolio with projects is the best way to develop skills and showcase them. \n\nI currently am almost complete with google data analytics on coursera and i am starting to think about how to develop a well rounded project that is interesting but i feel like I don’t have enough business sense to make a complete project.\n\nI feel like my a lack of business acumen is making it difficult for me to plan out a project. I know the fundamentals of sql and excel and I can play around with the data but is there a process to follow that will guide me towards proper analysis. I am not interested in following YouTube tutorials because i learned better from diving straight into data with tasks and questions to guide me. What are common analytics task you all do at work? \n\nWith that being said, how did you all decide your beginner projects? What type of analysis did you do? How do you come up with questions that are important and interesting? How can i showcase data cleaning? \n\nIt would be cool if y’all can share a link to your projects that helped you land an entry level job. I am curious to see what original projects look like.\n\nThanks for the help!", "author": "ignorant_monky", "created_time": "2024-12-07T05:31:09", "url": "https://reddit.com/r/analytics/comments/1h8lm2n/what_project_helped_you_land_your_first_analysis/", "upvotes": 50, "comments_count": 23, "sentiment": "neutral", "engagement_score": 96.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8lmp4", "title": "Biden’s $1 trillion investment in clean energy, semiconductors and infrastructure is a model for economic growth. It is stunning that the most successful private-public collaboration in history — one that is transforming cities, states and regions — has gotten so little coverage in the media. ", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-07T05:32:15", "url": "https://reddit.com/r/sustainability/comments/1h8lmp4/bidens_1_trillion_investment_in_clean_energy/", "upvotes": 3917, "comments_count": 100, "sentiment": "bullish", "engagement_score": 4117.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8ocb2", "title": "My 3 Month SEO Progress", "content": "On September 8, 2024, I posted on this subreddit asking for advice on how to improve my website's SEO and the best approach to reach a wider audience.\n\nThe feedback and support from this community were incredible, and I’m truly thankful for all the tips and encouragement I received.\n\nSince then, I’ve been consistently posting 1-2 blogs per week, and I’m really happy with the progress I’ve made so far. There’s still a lot for me to learn, but I’m glad I stayed persistent and didn’t give up.\n\nI have worked on internal linking, keywords, updating images with proper alt descriptions and updating my blogs with user friendly structure and content. I started to see results from November 3, and in the last 1 month, I have hit 10.6K clicks and 339k Total Impressions.\n\nI’ll continue sharing my progress here in the future, and I look forward to learning even more from this amazing community!", "author": "Competitive-Gate3285", "created_time": "2024-12-07T08:41:54", "url": "https://reddit.com/r/SEO/comments/1h8ocb2/my_3_month_seo_progress/", "upvotes": 102, "comments_count": 65, "sentiment": "neutral", "engagement_score": 232.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8rp5z", "title": "Best Colleges for Master's in Renewable Energy Engineering in the USA?", "content": "Hi everyone,\n\nI’m looking to pursue a Master's in Renewable Energy Engineering and am wondering which universities in the USA currently offer the best programs. I’m interested in factors like program structure, research opportunities, industry connections, and overall reputation in the field.\n\nAny recommendations or insights would be greatly appreciated!\n\nThanks in advance!", "author": "Exciting_Albatross94", "created_time": "2024-12-07T12:43:25", "url": "https://reddit.com/r/Renewable/comments/1h8rp5z/best_colleges_for_masters_in_renewable_energy/", "upvotes": 13, "comments_count": 19, "sentiment": "neutral", "engagement_score": 51.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8s8f7", "title": " 🛩️ $SRFM (Backed by Palantir) Could Be the Tesla of Regional Air Travel - Electric Planes + Palantir AI", "content": "Sup y'all. Last and final post of today. I've been doing a crazy amount of research so wanted to share.\n\nSo, check this out.\n\n# Palantir Technologies (yes, that Palantir) just dropped $1.27M buying shares at $1.25 and now own 10 percent of $SRFM\n\n***Their CEO (<PERSON>) built Mokulele Airlines into Hawaii's biggest commuter airline before joining SRFM.*** \n\nTLDR 🎯\n\n* $4.66 penny stock that's actually doing something real\n* Palantir keeps buying (1.27M shares recently) 🐋\n* 33.59 percent insider buying last month (that's huge btw)\n* Just got $50M in fresh cash\n* Electric planes + Palantir AI = 🚀\n\nThe Juicy Stuff 💰\n\n* $118M revenue and growing fas\n* Got exclusive deals with Cessna's parent company\n* Already signing up airlines worldwide\n* Think Tesla but for small planes\n\nWhy I'm Watching 👀\n\n* Palantir isn't stupid - they see something here\n* Everyone's talking EVs, but nobody's talking electric planes yet\n* They've got real revenue, real partners, real planes\n* Stock's still cheap compared to other EV plays\n\nThe \"But Wait...\" Part ⚠️\n\n* Yeah, they're losing money (like early Tesla)\n* Aviation stuff takes forever to approve\n* It's still early days\n* Don't yeet your rent money at this\n\n*Not financial advice. Don't own any shares (yet).*\n\nFun Fact: They just started getting their first batch of planes delivered from Cessna this month. Things are moving. 🤷‍♂️", "author": "ilovechicken37", "created_time": "2024-12-07T13:14:08", "url": "https://reddit.com/r/pennystocks/comments/1h8s8f7/srfm_backed_by_palantir_could_be_the_tesla_of/", "upvotes": 17, "comments_count": 20, "sentiment": "bullish", "engagement_score": 57.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8ttef", "title": "My first useful project as a web dev 🎉", "content": "", "author": "Nols05", "created_time": "2024-12-07T14:39:18", "url": "https://reddit.com/r/webdev/comments/1h8ttef/my_first_useful_project_as_a_web_dev/", "upvotes": 1497, "comments_count": 206, "sentiment": "neutral", "engagement_score": 1909.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8z3t7", "title": "Wind farm would be a great renewable Energy source in the united kingdom", "content": "", "author": "LyndaLyndaLyn<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-07T18:42:45", "url": "https://reddit.com/r/Renewable/comments/1h8z3t7/wind_farm_would_be_a_great_renewable_energy/", "upvotes": 4, "comments_count": 2, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h919th", "title": "How close is AI to human-level intelligence? - Large language models such as OpenAI’s o1 have electrified the debate over achieving artificial general intelligence, or AGI. But they are unlikely to reach this milestone on their own.", "content": "", "author": "Gari_305", "created_time": "2024-12-07T20:20:38", "url": "https://reddit.com/r/Futurology/comments/1h919th/how_close_is_ai_to_humanlevel_intelligence_large/", "upvotes": 9, "comments_count": 117, "sentiment": "neutral", "engagement_score": 243.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h92fbb", "title": "Could lower birthrates lead to happier families?", "content": "My logic is that only couples who truly want to raise families and have the means to are currently doing so. Everyone else is essentially removing themselves from the gene pool. It sounds cruel in a way but is that really what is happening? Are we seeing the evolution of more stable families? Or is the reality less optimistic than that?", "author": "originmsd", "created_time": "2024-12-07T21:14:03", "url": "https://reddit.com/r/Futurology/comments/1h92fbb/could_lower_birthrates_lead_to_happier_families/", "upvotes": 6, "comments_count": 123, "sentiment": "bullish", "engagement_score": 252.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}]