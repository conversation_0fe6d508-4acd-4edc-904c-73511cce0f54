#!/usr/bin/env python3
"""
测试新添加的QingYun API模型
验证6个新模型的API调用功能
"""

import os
import sys
import time
from dotenv import load_dotenv
from openai import OpenAI

# 加载环境变量
load_dotenv()

class NewQingYunModelTester:
    """新QingYun模型测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.api_key = os.getenv("QINGYUN_API_KEY")
        if not self.api_key:
            raise ValueError("❌ QINGYUN_API_KEY环境变量未设置。请在.env文件中设置您的QingYun API密钥。")
        
        # 新添加的6个模型
        self.new_models = [
            "claude-3-haiku-20240307",
            "ERNIE-Speed-128K",
            "qwen3-1.7b", 
            "glm-4-flash",
            "deepseek-v3",
            "llama-2-70b"
        ]
        
        # 初始化OpenAI客户端
        self.client = OpenAI(
            base_url="https://api.qingyuntop.top/v1",
            api_key=self.api_key,
            timeout=120
        )
        
        print(f"🚀 新QingYun模型测试器")
        print(f"✅ API密钥已配置: {self.api_key[:10]}...")
        print(f"📋 将测试 {len(self.new_models)} 个新添加的模型")
        print("-" * 60)
    
    def test_model(self, model_name: str) -> dict:
        """测试单个模型"""
        print(f"\n🔍 测试模型: {model_name}")
        
        # 简单的测试消息
        test_messages = [
            {"role": "system", "content": "You are a helpful financial analyst."},
            {"role": "user", "content": "What is the current market sentiment? Please provide a brief analysis in JSON format with fields: sentiment, confidence, reasoning."}
        ]
        
        try:
            start_time = time.time()
            
            # 发送API请求
            response = self.client.chat.completions.create(
                model=model_name,
                messages=test_messages,
                temperature=0.1,
                max_tokens=500
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # 提取响应内容
            content = response.choices[0].message.content
            
            result = {
                "status": "success",
                "model": model_name,
                "response_time": round(response_time, 2),
                "content_length": len(content),
                "content_preview": content[:200] + "..." if len(content) > 200 else content,
                "usage": {
                    "prompt_tokens": getattr(response.usage, 'prompt_tokens', 0),
                    "completion_tokens": getattr(response.usage, 'completion_tokens', 0),
                    "total_tokens": getattr(response.usage, 'total_tokens', 0)
                }
            }
            
            print(f"✅ 成功 - 响应时间: {response_time:.2f}s")
            print(f"📝 内容长度: {len(content)} 字符")
            print(f"🔢 Token使用: {result['usage']['total_tokens']} total")
            print(f"📄 内容预览: {content[:100]}...")
            
            return result
            
        except Exception as e:
            error_msg = str(e)
            result = {
                "status": "error",
                "model": model_name,
                "error": error_msg,
                "error_type": type(e).__name__
            }
            
            print(f"❌ 失败 - {type(e).__name__}: {error_msg}")
            return result
    
    def test_all_models(self):
        """测试所有新模型"""
        print(f"\n🎯 开始测试所有新添加的模型...")
        
        results = []
        successful_models = []
        failed_models = []
        
        for i, model in enumerate(self.new_models, 1):
            print(f"\n{'='*60}")
            print(f"📊 进度: {i}/{len(self.new_models)}")
            
            result = self.test_model(model)
            results.append(result)
            
            if result["status"] == "success":
                successful_models.append(model)
            else:
                failed_models.append(model)
            
            # 在测试之间添加短暂延迟，避免速率限制
            if i < len(self.new_models):
                print("⏳ 等待3秒避免速率限制...")
                time.sleep(3)
        
        # 打印总结
        self.print_summary(results, successful_models, failed_models)
        
        return results
    
    def print_summary(self, results, successful_models, failed_models):
        """打印测试总结"""
        print(f"\n{'='*60}")
        print(f"📊 测试总结")
        print(f"{'='*60}")
        
        print(f"✅ 成功模型 ({len(successful_models)}/{len(self.new_models)}):")
        for model in successful_models:
            result = next(r for r in results if r["model"] == model)
            print(f"   • {model} - {result['response_time']}s")
        
        if failed_models:
            print(f"\n❌ 失败模型 ({len(failed_models)}/{len(self.new_models)}):")
            for model in failed_models:
                result = next(r for r in results if r["model"] == model)
                print(f"   • {model} - {result['error_type']}: {result['error'][:100]}...")
        
        success_rate = len(successful_models) / len(self.new_models) * 100
        print(f"\n🎯 总体成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 测试结果良好！大部分新模型工作正常。")
        elif success_rate >= 50:
            print("⚠️  测试结果一般，部分模型可能需要调整。")
        else:
            print("🚨 测试结果不佳，需要检查模型配置或API状态。")

def main():
    """主函数"""
    try:
        tester = NewQingYunModelTester()
        results = tester.test_all_models()
        
        # 保存结果到文件
        import json
        with open("new_qinyun_models_test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: new_qinyun_models_test_results.json")
        
    except Exception as e:
        print(f"❌ 测试器初始化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
