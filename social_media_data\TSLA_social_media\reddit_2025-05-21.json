[{"platform": "reddit", "post_id": "reddit_1krofyy", "title": "New model Y spotted under cover on the Nurbrugring track", "content": "https://www.carscoops.com/2025/05/new-tesla-model-y-performance-breaks-cover-plaid-wheels-and-all/", "author": "ConfidentImage4266", "created_time": "2025-05-21T03:45:32", "url": "https://reddit.com/r/teslamotors/comments/1krofyy/new_model_y_spotted_under_cover_on_the/", "upvotes": 881, "comments_count": 111, "sentiment": "neutral", "engagement_score": 1103.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krpan5", "title": "This is Exactly How We Nailed Both Google Call & SPY Short Today !", "content": "1. Google : signaled & bought 172.5 call since yesterday , sold all by 10am PT I/O event today , for 70% return !\n\n*Processing img h2a8za3w912f1...*\n\n*Processing img 2zq6cpyea12f1...*\n\n*Processing img 2lpvki3ga12f1...*\n\n2. SPY : short 591 put at 592.5,  dumped to 590 for 100% return.\n\n*Processing img lpec9nqe912f1...*\n\n*Processing img ii9f4hxf912f1...*\n\n*Processing img p898u6gg912f1...*\n\n**Join #1 AI Trading Community of the world :**\n\n*Processing img 5skqn6tl912f1...*\n\n", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-21T04:35:13", "url": "https://reddit.com/r/investing_discussion/comments/1krpan5/this_is_exactly_how_we_nailed_both_google_call/", "upvotes": 0, "comments_count": 0, "sentiment": "bearish", "engagement_score": 0.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krq8qo", "title": "yolo every dollar I have on GOOGLE", "content": "Thesis: Google will follow <PERSON>sla and NVDA. Thats all. Here are some photos. (please dont do what im doing.. unless it makes sense which im unsure of)\n\nHere is my position so you know I have 3 brain cells left in me. Also my DD is a singular indicator.\n\nalso wtf is even DD or thesis people throw around these words on here and I think it just means \"why I'm buying this\".\n\n[PLEASE DO NOT DO WHAT IM DOING IM NOT VERY SMART](https://preview.redd.it/d2z8ii3jl22f1.png?width=1611&format=png&auto=webp&s=e504419f7c1069eb49d78ab79a845ca1ac26fc2b)\n\n[nvda weekly MACD cross scrumptious](https://preview.redd.it/dkowv19ml22f1.png?width=2689&format=png&auto=webp&s=633c789cbcf83b5b4d789fd2b29faa8bc92be044)\n\n[Tesla also weekly MACD cross YUMMY](https://preview.redd.it/smsjphjol22f1.png?width=2691&format=png&auto=webp&s=ddfc208fc1d80ad24766be0e152b7874958e2926)\n\n[Google wants to eat but its just taking a lil bit](https://preview.redd.it/8ohzw3mql22f1.png?width=2697&format=png&auto=webp&s=abe52b498f1de127f136be32e3305bbfe40afc74)\n\nI'm not gonna lie as dumb as this is I lowkey think it might work. Like I'm no genius but I don't see why I cant at least catch a 5 percent move. ", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-05-21T05:33:15", "url": "https://reddit.com/r/wallstreetbets/comments/1krq8qo/yolo_every_dollar_i_have_on_google/", "upvotes": 1585, "comments_count": 400, "sentiment": "bullish", "engagement_score": 2385.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krqbbk", "title": "The aftershock: google I/O 2025", "content": "I don't know about you, but I'm still ruminating over what was shown today.  Just amazing. I'm dumbfounded.  All the different tools and applications... I can't think about anything else since the events concluded.  <PERSON><PERSON><PERSON>'s sweatshirt said \"AGI wen?\" on it.  Are we not here? is this not general?\n\n<PERSON><PERSON> is quite the mindfuck. And it works flawlessly in the glasses too?! I mean come on man. What the fuck?!!\n\nTheir dev segment was amazing too. They're putting some very intuitive AI apis into chrome.  That simple photo site, that she submitted a sketch to refactor the site's aesthetics into - was perfect.  Unbelievable.   I signed up for the api access and read through the docs a little bit, but a few hours after the show, I got stuck in this mental rut of \"well if I really learn this stuff, it's all going to change within 4-6 months. Is there a point in learning these things anymore?\" and \"surely the end is near with development.... shit, entire companies will not survive - their lifeblood is a few iterations away\".  And if people begin creating all these apps, there will be so many it wont matter.  If not immediately overtaken, it will be eventually.\n\nIf you think about all the AI wrappers that have been built in the last yr and some change, could you not imagine cloning it fairly easily with some of these tools?\n\nKind of all over the place here, like I said I'm stupefied.  I'm having a hard time finding the point.  It's hard to even see this as an 'intermediate step' in the grand scheme of things.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-21T05:38:05", "url": "https://reddit.com/r/singularity/comments/1krqbbk/the_aftershock_google_io_2025/", "upvotes": 327, "comments_count": 133, "sentiment": "neutral", "engagement_score": 593.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krqfe5", "title": "Top Selling Car in China is the Geely Geome (Xingyuan)", "content": "In April 2025, the <PERSON>ly Geome outsold every other car in China.  This vehicle starts at $9540 USD.  It’s no wonder the world is afraid of Chinese vehicles.\n\nThe Geome weighs 2833 pounds.  Most American weigh well over 4000 pounds, with a few just under.  The vehicle has a LFP battery which will probably last for 500,000 miles.  It’s safer than NCM batteries, and it charges faster.\n\nSign me up Geely.  I’ll be glad to pay the 35% tariff.\n\nLinks:\n\nhttps://fastestlaps.com/models/geely-geome-xingyuan#google_vignette\n\nhttps://www.redwayess.com/what-are-catls-new-lifepo4-batteries-and-their-impact/\n\nhttps://carnewschina.com/2025/05/19/best-selling-passenger-vehicles-in-china-geely-geome-xingyuan-geely-xingyue-l-denza-d9-april-2025/", "author": "NetZeroDude", "created_time": "2025-05-21T05:45:32", "url": "https://reddit.com/r/electriccars/comments/1krqfe5/top_selling_car_in_china_is_the_geely_geome/", "upvotes": 20, "comments_count": 46, "sentiment": "bearish", "engagement_score": 112.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kru42z", "title": "Tesla owners install DIY rip cords to avoid being trapped behind all-electric doors | Other EVs just unlock if you forcefully pull on the interior handle, or pull twice", "content": "", "author": "chrisdh79", "created_time": "2025-05-21T10:05:15", "url": "https://reddit.com/r/technology/comments/1kru42z/tesla_owners_install_diy_rip_cords_to_avoid_being/", "upvotes": 4820, "comments_count": 592, "sentiment": "neutral", "engagement_score": 6004.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krvmp7", "title": "MQ Summit 2025 CFP is open!", "content": "If you're building cloud-native or event-driven systems and working with messaging tech like Kafka, RabbitMQ, Pulsar, NATS, LavinMQ, or cloud services like SQS and Pub/Sub — consider submitting a talk.\n\nWe're looking for real-world stories, scaling tips, cloud-to-edge patterns, and innovations in messaging infrastructure.\n\n**CFP deadline: June 15, 2025**  \nSubmit here: [https://mqsummit.com/#cft](https://mqsummit.com/#cft)", "author": "Code_Sync", "created_time": "2025-05-21T11:37:54", "url": "https://reddit.com/r/cloudcomputing/comments/1krvmp7/mq_summit_2025_cfp_is_open/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krvqc6", "title": "Tesla Optimus the humanoid robot is learning household chores", "content": "", "author": "Distinct-Question-16", "created_time": "2025-05-21T11:43:51", "url": "https://reddit.com/r/singularity/comments/1krvqc6/tesla_optimus_the_humanoid_robot_is_learning/", "upvotes": 49, "comments_count": 42, "sentiment": "neutral", "engagement_score": 133.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krvszd", "title": "What’s the most trusted company for removal of a bad Google review?", "content": "The customer mentions the business owner and his son in the disparaging ,negative review. He does not want to attempt to remove on his own because in the past that prior attempt had put him in jeopardy with another review company. I’m writing this on behalf of a neighboring business owner. Who’s not very astute online, and has seen a decline in business due to this review. Just trying to help a neighbor out with his small bricks and mortar business! Thank you in advance.", "author": "Acrobatic-Parsley-53", "created_time": "2025-05-21T11:48:00", "url": "https://reddit.com/r/smallbusiness/comments/1krvszd/whats_the_most_trusted_company_for_removal_of_a/", "upvotes": 1, "comments_count": 19, "sentiment": "bearish", "engagement_score": 39.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krw9j8", "title": "<PERSON><PERSON> reaffirms role as Tesla CEO, says no concerns with demand", "content": "", "author": "chrisdh79", "created_time": "2025-05-21T12:12:22", "url": "https://reddit.com/r/teslamotors/comments/1krw9j8/elon_musk_reaffirms_role_as_tesla_ceo_says_no/", "upvotes": 630, "comments_count": 345, "sentiment": "neutral", "engagement_score": 1320.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krwlab", "title": "China: The Global Leader in EV Adoption", "content": "China has firmly positioned itself as the global leader in EV adoption, dominating both production and sales.\n\nBetween 2020 and 2024, annual EV sales in China jumped from 1.14 million to 11.3 million — a tenfold increase in just five years.\n\nI’m writing a free EV ebook with key insights from IEA sales data I analysed myself. It’s a complete guide for anyone looking to understand EVs and buy with confidence. Techwheel.co subscribers will get it first.", "author": "scienceguy0077", "created_time": "2025-05-21T12:29:07", "url": "https://reddit.com/r/sustainability/comments/1krwlab/china_the_global_leader_in_ev_adoption/", "upvotes": 30, "comments_count": 5, "sentiment": "bullish", "engagement_score": 40.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1krxe7z", "title": "Elon Musk Is The Cause, Not The Solution To Tesla’s Deep Problems", "content": "", "author": "mafco", "created_time": "2025-05-21T13:08:05", "url": "https://reddit.com/r/electricvehicles/comments/1krxe7z/elon_musk_is_the_cause_not_the_solution_to_teslas/", "upvotes": 2994, "comments_count": 515, "sentiment": "neutral", "engagement_score": 4024.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks05i7", "title": "<PERSON> says SpaceX will 10x by 2035—Elon thinks it’ll 30x 😳", "content": "I own a couple thousand shares of SpaceX.\n\n<PERSON> recently said he expects SpaceX to 10x by 2035, and that Elon believes it could 30x.\n\nWith a current valuation of ~$350 billion, that would imply a future valuation of $3.5 trillion to $10+ trillion.\n\nHere’s the interview clip:\nhttps://youtu.be/Q3Fk7WtqTMk?si=ceTwzaFj1P_4LHgw\n\nDoes that seem realistic over the next 10 years? Would love to hear how people are thinking about the upside—especially with Starlink and Starship coming online.\n", "author": "JimFiNantz", "created_time": "2025-05-21T15:06:43", "url": "https://reddit.com/r/investing_discussion/comments/1ks05i7/ron_baron_says_spacex_will_10x_by_2035elon_thinks/", "upvotes": 1, "comments_count": 12, "sentiment": "neutral", "engagement_score": 25.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks0hfs", "title": "36, 4 million. I'm terrified to FIRE and keep increasing my number for a larger cushion. How do people pull the trigger?", "content": "37, married, 1 kid and 1 on the way. We have 4 million in investments and our home is paid off (high property tax state though). Our *current average* monthly spend is 5.5k. Since we're in our 30s i'm aiming for a 3% withdrawal rate. \n\nOn paper, we should be FIRE but I'm just so uncomfortable with the risks and don't know how people do it on 1-2 million like I see here so frequently. My biggest concerns are:\n\n  \n\\- Health insurance. I'm afraid to rely on the ACA and even with it most of the plans in my area have a max 20k OOP. \n\n\\- Emergencies. Basement floods, HVA<PERSON> dies, hail destroys roof, car accident, health issues etc. Unexpected significant costs. How are people planning for these?\n\n\\- Cost of children. My kid is 1 and we have another on the way. Other than diapers he's pretty cheap right now. I have no idea how much they're going to cost 10 years from now. I know they *could* be cheap but if they want to play travel sports or anything like that, I don't want to deprive them. There's also a decent chance 10-15 years from now the education system funding will be different and we'll need private schools.\n\n  \n\\- Caring for parents/in-laws as they age. Who knows if they'll have issues and need special care that they can't afford on their own.\n\n\\- If I FIRE now i'm confident there will be major issues rejoining the job market years from now, so once I quit i'm committing. \n\n\n\nNone of these concerns should be too unique to me. How are people getting over them? I can't help but feel like I need an insane number to be totally secure.", "author": "Throw_away_FIRE_act", "created_time": "2025-05-21T15:19:45", "url": "https://reddit.com/r/Fire/comments/1ks0hfs/36_4_million_im_terrified_to_fire_and_keep/", "upvotes": 0, "comments_count": 94, "sentiment": "neutral", "engagement_score": 188.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks1t3f", "title": "PPC for E-commerce: Are Amazon Ads Still Underrated Compared to Google/Meta?", "content": "I have been a part of amazon ads for the last 8 years. I think that they are the best to see immediate roi from spend, as everything is first party data. Curious to know what other folks think about amazon ads vs google and meta. ", "author": "Pitiful-Extent9596", "created_time": "2025-05-21T16:12:33", "url": "https://reddit.com/r/DigitalMarketing/comments/1ks1t3f/ppc_for_ecommerce_are_amazon_ads_still_underrated/", "upvotes": 7, "comments_count": 13, "sentiment": "neutral", "engagement_score": 33.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks3n5w", "title": "Status check 39 single male", "content": "Posted before but had no responses, hoping to get a sanity check. I’ve been a set and forget person but have been much more aware of my finances as I stalk this sub the past year or so. Looking back, I’ve made a lot of mistakes but still in good shape I think? Please tear me down if not. Would love some honest feedback and advice going forward.\n\nWage -$160k\n\nRoth - $170k \nTrad 401k- $20k \nCrypto - $40k (maybe a lil more in some illiquid tokens) \nHouse equity - $550k ($525k left at a 2.65%. So lucky to buy in 2019 and refi 2020 but it is a fixer upper I haven’t been able to fix up) I want to move to a lower cost of living if I can and buy a place all cash if I can. \nCash - $40k (buying a Tesla model 3 soon. I’ll get financing but will put most of this in MM to earn interest while pay down 0% interest Tesla loan) \nMM - $30k \nMisc - $10k\n\nNet worth - $860k. Or closer to $840k after a vacay and car purchase coming up.\n\nAs of now, I’m also in line to inherit 1/2 my parents place (so probably about another $500k in the future).\n\nConsidering house(s) and NW and cash position, I’m thinking I should start focusing on liquid asset growth (equities mostly)?\n\nAny comments or suggestions would be appreciated. Thank you.", "author": "Glass_Specialist44", "created_time": "2025-05-21T17:25:23", "url": "https://reddit.com/r/Fire/comments/1ks3n5w/status_check_39_single_male/", "upvotes": 0, "comments_count": 27, "sentiment": "bullish", "engagement_score": 54.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks5gvd", "title": "<PERSON><PERSON>’s head of self-driving admits ‘lagging a couple years’ behind <PERSON><PERSON>", "content": "", "author": "walky<PERSON><PERSON><PERSON>", "created_time": "2025-05-21T18:37:45", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ks5gvd/teslas_head_of_selfdriving_admits_lagging_a/", "upvotes": 509, "comments_count": 197, "sentiment": "neutral", "engagement_score": 903.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks8prx", "title": "Why does it feel like Google is the most underappreciated tech giant while Apple and Microsoft get all the love?", "content": "Over the past few years, I’ve noticed a consistent trend across tech media, investor sentiment, and general public discourse:\n\nApple is widely admired and treated as a gold standard of innovation—even when its recent contributions are mostly iterative.\n\nMicrosoft is praised for strategic excellence, especially in B2B and cloud infrastructure.\n\nYet Google—despite its profound impact across multiple industries—is often criticized, underestimated, or dismissed.\n\n\nWhat makes this more surprising is that even on Google’s own platform, YouTube, the prevailing narrative tends to spotlight Apple and Microsoft positively, while Google frequently becomes a target for criticism. This raises an important question:\nIs Google actually underappreciated despite arguably being one of the most impactful and ambitious tech companies of our time?\n\n\nA Comprehensive Look at Google’s Contributions\n\nUnlike Apple, whose innovation slowed notably after the passing of <PERSON>, and whose key advancements revolve around hardware polish (AirPods, Apple Watch, service bundling), Google has made deep, foundational contributions across both consumer and enterprise technology:\n\nArtificial Intelligence: With DeepMind, Gemini, and TPUs, Google is developing a complete AI stack—from hardware to models to deployment. It's getting better and gaining traction and actually getting ahead of others as we saw in I/O 2025.\n\nSearch: Continues to be the world’s most used and reliable search engine, even in the age of AI-based alternatives.\n\nCloud Computing: Google Cloud is now a major player, serving high-demand clients and growing fast.\n\nYouTube: The most influential platform for education, entertainment, marketing, and content creation.\n\nAndroid: The world’s most widely used mobile operating system.\n\nAutonomous Driving: Waymo is one of the most advanced efforts globally in self-driving technology.\n\nCybersecurity: With investments like Mandiant and Wiz, Google is becoming a significant player in this space.\n\n\nThis level of influence spans more verticals than either Apple or Microsoft in many respects.\n\n\nApple’s Limitations\n\nWhile Apple excels in design, branding, and product refinement, its innovation track record in recent years is relatively conservative:\n\nThe AI efforts (e.g., Siri) have fallen behind.\n\nThe autonomous vehicle project was discontinued after a decade of development and investment.\n\nApple Intelligence is a failure for now by not delivering what's promised.\n\nRecent “innovations” largely center around ecosystem integration, camera,not foundational technology.\n\n\nIn contrast, Samsung and other OEMs are pushing the envelope further in hardware and manufacturing.\n\n\nMicrosoft’s Position\n\nMicrosoft deserves immense credit for:\n\nStrategic investments (e.g., OpenAI)\n\nDominance in enterprise tools (Office, Azure, LinkedIn, GitHub)\n\nEffective AI integration into its suite (Copilot, Bing Chat)\n\nHowever, it must be noted that Microsoft leverages external breakthroughs (like OpenAI), rather than building its AI foundation internally, as Google does. And unlike Google, Microsoft lacks major consumer-facing ecosystems like YouTube or Android.\n\n\nSo Why the Gap in Perception?\n\nThere are several reasons Google remains underappreciated:\n\nAn engineering-first culture that prioritizes substance over storytelling.\n\nInconsistent branding and product messaging, leading to confusion (e.g., Duo, Meet, Chat, Hangouts).\n\nFrequent product shutdowns that affect trust and public perception.\n\nA deliberate avoidance of hype—Google rarely overmarkets its work, even when it’s pioneering.\n\n\nA Modern-Day Tesla Analogy\n\nIn many ways, Google resembles Nikola Tesla in the classic Edison-Tesla dichotomy:\n\nApple is Edison: charismatic, commercially polished, beloved by the public.\n\nMicrosoft is Rockefeller: strategic, business-focused, dominant in enterprise.\n\nGoogle is Tesla: visionary, experimental, often misunderstood, and focused on deeper innovation.\n\n\nGoogle may not always be the first to enter a space—but it often becomes the best. This has been the case with Search, Gmail, Chrome, Android, Maps, and now increasingly with AI.\n\nFinal Thought\n\nGoogle’s impact on the modern digital landscape is difficult to overstate, and yet its understated approach often leaves its achievements overshadowed. As we look forward to the next decade, it may become more evident just how foundational Google has been in shaping the technological infrastructure of the modern world.\n\nWould love to hear thoughtful perspectives—do others see this imbalance too?\n\n", "author": "Infinity100b", "created_time": "2025-05-21T20:48:49", "url": "https://reddit.com/r/google/comments/1ks8prx/why_does_it_feel_like_google_is_the_most/", "upvotes": 81, "comments_count": 80, "sentiment": "neutral", "engagement_score": 241.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks97jm", "title": "How to move forward in current market conditions", "content": "At 31 Years old working in the medical device industry finally reached $350,000 in my individual brokerage account. This does not include my 401 (K)holdings it is only the money I grew by investing long term. Initially I invested into Apple, Microsoft, Amazon, Google, Tesla, Meta Berkshire Hathaway, NVIDIA, AMD, Waste Management, Republic Services, SPY, QQQ, Bitcoin, Ethereum. Sold all my positions I only hold & DCA into S&P 500 Index & T Bills. I wanted to practice risk management by selling and indexing and holding bonds.\n\nQuestion: how should I move forward should I add VT Vanguard World Index ETF, IBIT Bitcoin, IAU Gold ETF? Should I also add QQQ or FSPGX? ", "author": "Elegant-Estate4537", "created_time": "2025-05-21T21:08:42", "url": "https://reddit.com/r/Fire/comments/1ks97jm/how_to_move_forward_in_current_market_conditions/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ks9xsk", "title": "Need help with my google campaigns for landscaping services", "content": "If someone can spend 1 hour or even 30 mins with me on a call just to take a look at the marketing structure i have so far i would really appreciate it. Ill even pay for that time. \n\nI just was seeking guidance on a few things with google ads. I am currently running a campaign and the budget is relatively low (50 cad) daily budget generating leads for a landscaping, epoxy and drywalling company all tied together by one guy who owns the drywalling business himself and is the owner along with a few others in the landscaping and epoxy flooring companies, so they are all pooling their money together . one issue I am currently facing is I originally had a pmax campaign running but then realized that with the budget I was working with I was not receiving the results I was expecting. I expected the learning phase of the new campaign also had a part to play into it. After a few days only 2 leads were generated and $75 spent and our cost per lead was $75 which I thought was way too high\n\nOut of haste I started a regular search campaign as I believed it would be a better and quicker option than pmax with a lower budget. It did not do well either  i think after like 4 days only 1 lead was generated. I don't think it was the assets themselves that led to this performance. I made sure they were good but for some reason I couldn't see any data on the search campaign itself not too sure if conversion tracking was due to this? but I did not have that on Pmax either and I could still see the data. Everything was just 0 but I did get a lead from it so i knew it Was doing something\n\nI just switched it back to Pmax because at least I could see the impressions and data. I was also getting more leads with it. It also had to restart the learning phase which says it is 5 days so I might be cooked. Is there something that I'm missing here like conversion tracking or something like that? Or is it just the limitation of the budget I have? I have gotten the consent of the company to increase the daily budget and I was thinking maybe something like 75-100 daily budget with Pmax. surely that would be more viable. Technically it is 3 different companies one in landscaping, epoxy flooring and drywalling so they would ideally want to assests individually under 3 ad groups in one campaign. previously i had them grouped together all in one ad set  so i would assume i should create 3 separate ad sets under the campaign now that i have a bigger budget. Do you think I should just leave the Pmax campaigns and let it run now under 3 ad sets and a higher budget or am I missing something here?", "author": "Artistic_Drummer_894", "created_time": "2025-05-21T21:38:56", "url": "https://reddit.com/r/adwords/comments/1ks9xsk/need_help_with_my_google_campaigns_for/", "upvotes": 2, "comments_count": 3, "sentiment": "bullish", "engagement_score": 8.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ksclxk", "title": "GOOGL Calls YOLO - $1 million in gains", "content": "I bought $2 million worth of GOOGL calls two weeks ago on the big dip to $148. I've been scaling out of these calls the last week, and closed my remaining contracts this morning when GOOGL hit $172. I did a bit of scalping along the way. All in all a 50% gain, which isn't the biggest percentage gain since I de-risked by selling as it was going up.\n\nI might jump back in if there's a big market correction. I still believe in Google and think the whole narrative around the death of search is overstated, especially because Gemini is one of the best AI models, and they are in a better position to monetize AI than their competitors.\n\nGOOGL 8/15 $150 Calls - $478,528.66 - [https://imgur.com/a/77nPsVC](https://imgur.com/a/77nPsVC)\n\nGOOGL 8/15 $155 Calls - $557,290.99 - [https://imgur.com/a/0PR72gj](https://imgur.com/a/0PR72gj)\n\n**Total Gains: $1,035,819.65**\n\nSee my previous posts for my initial buy in prices.", "author": "Solid-Sock-1794", "created_time": "2025-05-21T23:39:50", "url": "https://reddit.com/r/wallstreetbets/comments/1ksclxk/googl_calls_yolo_1_million_in_gains/", "upvotes": 1191, "comments_count": 137, "sentiment": "bullish", "engagement_score": 1465.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}]