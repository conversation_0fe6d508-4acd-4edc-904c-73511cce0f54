#!/usr/bin/env python3
"""
测试社交媒体数据与回测系统的集成
验证Reddit数据格式与social_media_analyst的兼容性
"""

import json
import os
from pathlib import Path
from datetime import datetime
import sys

def test_social_media_data_format():
    """测试社交媒体数据格式"""
    print("测试社交媒体数据格式...")
    
    social_media_dir = Path("social_media_data")
    if not social_media_dir.exists():
        print("❌ social_media_data 目录不存在")
        return False
    
    # 查找AAPL数据文件
    aapl_dir = social_media_dir / "AAPL_social_media"
    if not aapl_dir.exists():
        print("❌ AAPL_social_media 目录不存在")
        return False
    
    # 获取最新的Reddit数据文件
    reddit_files = list(aapl_dir.glob("reddit_*.json"))
    if not reddit_files:
        print("❌ 没有找到Reddit数据文件")
        return False
    
    # 测试最新文件
    latest_file = max(reddit_files, key=lambda x: x.name)
    print(f"测试文件: {latest_file}")
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print("❌ 数据格式错误：应该是列表")
            return False
        
        if len(data) == 0:
            print("⚠️  数据文件为空")
            return True
        
        # 检查第一个帖子的格式
        post = data[0]
        required_fields = [
            'platform', 'post_id', 'title', 'content', 'author',
            'created_time', 'url', 'upvotes', 'comments_count',
            'sentiment', 'engagement_score', 'source_subreddit', 'ticker'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in post:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
            return False
        
        # 验证数据类型
        if not isinstance(post['upvotes'], int):
            print("❌ upvotes 应该是整数")
            return False
        
        if not isinstance(post['comments_count'], int):
            print("❌ comments_count 应该是整数")
            return False
        
        if not isinstance(post['engagement_score'], (int, float)):
            print("❌ engagement_score 应该是数字")
            return False
        
        if post['sentiment'] not in ['bullish', 'bearish', 'neutral']:
            print(f"❌ sentiment 值无效: {post['sentiment']}")
            return False
        
        # 验证时间格式
        try:
            datetime.fromisoformat(post['created_time'])
        except ValueError:
            print(f"❌ created_time 格式无效: {post['created_time']}")
            return False
        
        print(f"✅ 数据格式验证通过")
        print(f"   - 文件包含 {len(data)} 个帖子")
        print(f"   - 示例帖子标题: {post['title'][:50]}...")
        print(f"   - 情感分析: {post['sentiment']}")
        print(f"   - 参与度分数: {post['engagement_score']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")
        return False

def test_backtester_compatibility():
    """测试与回测系统的兼容性"""
    print("\n测试与回测系统的兼容性...")

    # 检查是否存在social_media_analyst
    analyst_file = Path("src/agents/social_media_analyst.py")
    if not analyst_file.exists():
        print("❌ social_media_analyst.py 不存在")
        return False

    try:
        # 尝试导入social_media_analyst
        sys.path.append("src/agents")
        from social_media_analyst import social_media_analyst_agent

        print("✅ 成功导入 social_media_analyst_agent")

        # 检查函数是否可调用
        if callable(social_media_analyst_agent):
            print("✅ social_media_analyst_agent 是可调用的函数")
        else:
            print("❌ social_media_analyst_agent 不是可调用的函数")
            return False

        return True

    except ImportError as e:
        print(f"❌ 导入 social_media_analyst_agent 失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试 social_media_analyst_agent 失败: {e}")
        return False

def test_data_loading():
    """测试数据加载功能"""
    print("\n测试数据加载功能...")

    try:
        # 测试直接读取社交媒体数据文件
        test_date = "2025-07-01"
        ticker = "AAPL"

        social_media_dir = Path("social_media_data")
        ticker_dir = social_media_dir / f"{ticker}_social_media"
        data_file = ticker_dir / f"reddit_{test_date}.json"

        if data_file.exists():
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            print(f"✅ 成功加载 {ticker} {test_date} 的社交媒体数据")
            print(f"   - 加载了 {len(data)} 个帖子")

            # 验证数据结构与social_media_analyst兼容
            if data and isinstance(data, list):
                sample_post = data[0]
                required_fields = ['platform', 'sentiment', 'engagement_score', 'created_time']
                missing_fields = [field for field in required_fields if field not in sample_post]

                if not missing_fields:
                    print("✅ 数据结构与social_media_analyst兼容")
                else:
                    print(f"⚠️  数据缺少字段: {missing_fields}")

            return True
        else:
            print(f"⚠️  没有找到 {ticker} {test_date} 的数据文件: {data_file}")
            return True  # 这不是错误，只是没有数据

    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Reddit社交媒体数据集成测试")
    print("=" * 50)
    
    tests = [
        ("数据格式测试", test_social_media_data_format),
        ("回测系统兼容性测试", test_backtester_compatibility),
        ("数据加载测试", test_data_loading),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Reddit数据收集器已成功集成。")
        print("\n下一步:")
        print("1. 运行完整的回测测试:")
        print("   python src/backtester.py --tickers AAPL --use_local_social_media --start-date 2025-07-01 --end-date 2025-07-01")
        print("2. 收集更多历史数据:")
        print("   python reddit_live_collector.py --start-date 2024-01-01 --end-date 2025-07-02")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
    
    return passed == len(results)

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
