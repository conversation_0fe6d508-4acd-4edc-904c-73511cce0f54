[{"platform": "reddit", "post_id": "reddit_1hdqiio", "title": "We raised the first phase of dual-axis trackers over the Athens, GA. library today. Crane day is always stressful but very rewarding in the end", "content": "We raised 4 of 7 dual-axis trackers today.once complete, they will provide 133kw of power for the library, plus any little extra the bi-facials add. I love building these. I'll build these everyday if it keeps me off rooftops.", "author": "dragonflyfoto", "created_time": "2024-12-14T00:23:51", "url": "https://reddit.com/r/solar/comments/1hdqiio/we_raised_the_first_phase_of_dualaxis_trackers/", "upvotes": 151, "comments_count": 68, "sentiment": "neutral", "engagement_score": 287.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdqkv2", "title": "Is it time to sell TSLA?", "content": "Basically the title. I bought after the 10% dip at the beginning of October. Entry price ~$211 PE ~60x. I was really happy to get a decent entry price given the multiples the stock has historically traded at. I love Elon, autonomous driving, the tsla semis, the new Q model I believe it is ($25k car), the robots, and his future thinking view of the company.\n\nUnfortunately / fortunately the stock has doubled in the last 2 months mostly due to non-fundamental reasons. Financially, I want to split with the stock (sold half my holdings around $360), but I am too much of a fanboy to do it yet.\n\nWould love to hear everyone’s thoughts. Thanks!", "author": "Pershing_Circle", "created_time": "2024-12-14T00:27:11", "url": "https://reddit.com/r/ValueInvesting/comments/1hdqkv2/is_it_time_to_sell_tsla/", "upvotes": 0, "comments_count": 146, "sentiment": "bearish", "engagement_score": 292.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdr2dc", "title": "Things that are worth doing...", "content": "Tl:Dr; Options trading is a legitimate path to creating wealth. That said, it in no way is easy. To be successful as a trader you by definition need to be an outlier. Just like those who achieve outlier performance in other disciplines, it requires a significant investment of sweat equity and delayed gratification. I actually hate writing and am not good at it but this post is long because there is a lot of nuance. This post combines elements of my other posts into one comprehensive spot.\n\nThings that are worth doing are rarely easy. The goal of this post is to share information regarding my path as a trader to hopefully provide some context for other options traders trying to find their way. I outline how I would prioritize my time as a new trader and what I would focus on. One of the messages here is that we all will have different opportunities cross our paths, but being receptive to and willing to take advantage of them WHEN they do is vital.\n\nI’m 33 now and have been trading for nearly 17 years. I have well over 30,000 hours in markets and below is exactly how I would approach trading if starting again. First, trading is absolutely viable as a way to build wealth. It’s extremely misleading however. Most really high earnings jobs have strict prerequisites,degrees, experience, tests, etc. Trading requires none of this. The barrier to entry is as low as it can be: open a brokerage account, transfer money. DON’T LET THIS DECEIVE YOU.\n\n\\-Skip this section-\n\nMy background. Recommend skipping this because it literally doesn’t matter but including because it’s a common follow up question. All this summarizes to I had a fairly regular upbringing that had basic advantages of growing up in America but not much beyond that. I am very average in terms of intellect but have overcome this through discipline and consistency.\n\nI grew up with a low income single parent that was a public school contractor as a occupational therapist. My mom worked hard but was unwise with money and didn’t make much of it. I did not grow up without food or a roof over my head. I grew up in a violent area and walked through metal detectors daily for school. I was stabbed in my hand while defending against getting jumped in the bathroom. I did well in school because for some reason (still don’t know actually why) I really cared about doing well at what I was working. So it was a competition with myself. I started working young doing odd jobs: shoveling snow, taking trash out, splitting wood, moving shale, worked at a bowling alley, sold Christmas trees, etc. I knew how to work hard from my moms example but didn’t know what to do with my money. I just knew that I didn’t want to have the lifestyle she had to work through. I really respected and revered the military, so I was planning to enlist in the Marine Corps like my brother. I went into JROTC to start my prep and one of the instructors there changed my life. I would spend hours picking his brain and he introduced me to the concept of investing. This advice altered the trajectory of my life. In 2007 while in high school I started investing and around 3 months in I started trying to actively trade. I earned a Marine Corps scholarship (college was completely out of the question otherwise) and went to RIT (because they also covered housing which I couldn’t afford). When I started trading, like all of us, I had no fucking clue what I was doing and thought the more involved I was, the more I’d make. Stemming from my background, I was extremely risk averse and uncomfortable with losing money (looking back, I can clearly see the impact of a scarcity mindset at play, be aware of this if you have a similar background. for those that grow up with more, they have their own issue, typically not paying enough attention to risk - the market will ALWAYS provide conditions for your weaknesses to show).\n\n\\-Back to the stuff that matters-\n\n**General overview**\n\n1. Create goals. This becomes the point we can reverse plan from. Be specific and be thoughtful. Superficial answers of “Millionaire” are fine to start but they break down as you progress.\n2. There is ZERO substitute for saving and growing your income. As far as I see it, there are (3) wealth levers: save, grow your income, invest. Saving is the most accessible lever that can make a BIG difference early on. However, you can only cut so much. Earning MORE money should be paired with savings and this combination is much more effective than cutting your way to wealth. Think of the skillsets you have and ways to monetize it. Labor is always a source of money. Doordash or uber if you have a car. For me, I used to buy cheap broken cars, fix them, and sell them (I did this in college along with a few other things. I learned to fix cars because we couldn’t afford to take my mom’s into the shop when needed and she drove a lot). I got into flipping motorcycles because they’re cheap, I enjoyed riding, and cheap bikes were liquid. You can also fit more motorcycles into a space than cars.\n3. For the third lever, investing. The reality is the overwhelming majority of traders will fail. This isn’t because trading is insanely difficult but because the barrier to entry is so low is leads to an abysmal lack of preparation. For context, before Marine Officers commission, they are screened through a program called OCS. We all train before we go to the program to increase our chances of succeeding. If we approached OCS like trading, the failure rate would absolutely sky rocket. Trading is the same. My approach here would be to hedge my bets. I would DCA into an index ETF as soon as humanly possible while papertrading to see if trading is in my wheelhouse. The probability of trading a small account into your future wealth is VERY low. In contrast, the probability that you blow your account or have a massive drawdown is VERY HIGH.\n4. Create a learning plan and a schedule. Like ANY course you’ve taken in school, there is typically a syllabus of some sort with a progression of topics, homework, quizzes, tests, projects, etc. Yet, for trading, we all start slanging positions magically thinking we’re just going to figure it out. For those that are really smart, they might be able to. I wasn’t one of those people and spend a significant amount of time very inefficiently wandering around topics. When I started, there weren’t that many choices to learn from. Today you have the opposite problem with information overload - making this step even more important to stay efficient. To create a learning plan, I believe for options traders the very first thing that I’d do is read: Options as a Strategic Investment and Option Volatility and Pricing. Followed by Positional Options Trading. “But Erik, that’s a LOT of reading!” No shit. Again, take a giant step back and try to appreciate what you’re trying to do as a trader. How arrogant is it to think you have access to fast easy money with no effort? Welcome to the real world - that’s not how shit works. Embrace the suck and get to work. (The beautiful part, is ONCE you learn how to trade, it really is very easy. ALL of the work is done during learning and building the approach).\n5. Those books will give you an idea of a natural progression. Don’t reinvent the wheel. You also have access to dope accelerators like ChatGPT. As I was reading those books, I would start a trading plan word doc to initially take notes. I would have ChatGPT quiz me. For a rough outline of priorities see below.\n6. Trading plan, strategy outlines, and trade logs. As you’re learning and developing ideas that you think might work, outline the rules in a strategy outline, dump it into your trading plan for a reference. Create a lot to track your performance. This is the ONLY WAY to objectively analyze what you’re doing and make logical adjustments. Without this process, it’s literally a matter of luck. Remember, markets can show you one thing for a long time. In my trading tenure, I’ve seen just (3) bear markets, (2) have been in the last (4) years. If you don’t test your ideas in varying market conditions ahead of time because you think you have it figured out, the market will eventually show you otherwise.\n\n**Strategy Development Process**\n\nTl:Dr; most of us dive into what option structures or strategies we think are “best”. However, the true initial focus HAS to be on identifying profit mechanisms, which is the underlying force that yields profit from a trade. It doesn’t matter what deltas or DTE you pick for your long call if the underlying goes down. This post covers #1 and #2. Nothing here is novel or unique. This is simply a framework I’ve build for myself to streamline the trading process.\n\nThere are (4) main steps I built for myself in building strategies: 1. Profit Mechanism, 2. Profit Mechanism Type & Signals, 3. Structure Fitting, 4. Strategy Creation.\n\nAdmin note. You need to start a trading plan and trade log to track and analyze this stuff. If you’re too lazy to do that, you’re going to get lazy results. It’s up to you.\n\n1. Profit mechanism. This is the most critical step in the process. Positively attributing HOW a trade makes money. Price direction (up or down), volatility (up, down, variance), dividends, stock buybacks, correlation (pairs). It sounds simple but you’d be amazed how most traders think little about the implications for this step. For example, if I’m extremely bullish on a stock, it might make more sense to buy a call for the uncapped profit potential vs selling a put. Yet most of us get stuck into defaulting to something that might not be optimal.\n2. Profit mechanism signal. This is where we test and track different signals that help us identify the profit mechanism and better understand the behavior. This is a game of matching things that are relevant. For example, if I’m testing a breakout price mechanism, that based on initial observation tends to last 3-9 days on average, using a 5 year, weekly chart is likely useless as is the 252D MA. Maybe we test things like volume relative to a short term average, or shorter term MAs, etc. After step 1, you should be logically refining what makes sense to test for signals. You can accelerate your testing by: eyeballing first (this is just visually reviewing a chart and see what stands out as common themes to give initial testing ideas, this CANNOT be trusted but is a reasonable starting point), then backtesting, then forward testing. Reminder, we’re not testing strategies here, JUST signals. Why the emphasis on profit mechanism and signals? Because if you don’t get this right, it doesn’t matter if you sell a put or buy a call and the stock goes down - you’re still wrong. We’re building the initial inputs to track expected return. Win rate, loss rate, average win $ and average loss $. Remember, options simply amplify things. So you can track your average sizes based solely on price movement to start, it’s completely fine.\n3. Outline structures. NOW is when we introduce base structures we thing might make sense. Going back to the price direction up, breakout profit mechanism. We know we’re trading something that is going up, so buying stock, buying calls, selling puts all fit. This is a fine starting point. Once we get comfortable, we can get a bit more complex with our structure outlines. Here we can explore basic ideas via an eyeball test, backtest, and forward test. This step helps us refine what deserves to become a strategy based on step 1 and 2.\n4. Build strategy. Finally, we can take the best idea or two and build strategies around them. This is where we test tons of variations to find an optimal set up (reminder, optimal doesn’t mean best performing inherently, that might just show an overfit configuration. robustness matters). Back to the breakout example. Maybe we found defining a tight exit below recent consolidation has a manageable loss rate (say 40%) with an average loss of 5% of entry price. Win rate was 60% with an average win of 25% of entry price. While the short puts might work fine, long calls seem to be a better fit based on these metrics, since there’s a stop involved that doesn’t allow us to fully benefit from the larger profit window of a short puts and the upside has larger potential which the short puts sacrifice. To test the long call strategy, we need to test some key inputs: DTE, theta, and delta. We’ll want to pay attention to gamma as well. Remember, greeks give us tremendous insight into HOW a position will behave - that’s their purpose. We can backtest and forward test here to test all different configurations of DTE and deltas (while tracking theta and gamma). In this way, it’s not a guessing game, it shouldn’t be. We might find that mid-duration options greater than 30DTE balances theta decay and gamma but going too far out might decrease liquidity and add unnecessary expense if our average total holding duration (identified from step 1 and 2) is <40DTE.\n5. If this sounds like a lot of steps and work, it is. See the first thing I said. Do not allow the low barrier to entry into trading deceive you, especially options which add complexity. To achieve long-term success you will need to work as hard at this as any other high performing career with far more pitfalls and less support. As a trader you will wear many hats: researcher, analysis, risk manager, psychologist, planner, etc. The cool part though, is your destiny is entire in your own hands.\n\n**Generalized syllabus for a new options trader**\n\n*Of note, I wouldn't even worry about placing a live trade for the first year. While this sounds insanely unappealing, the probability of making any true positive progress trading within your first year is wildly small. Even if a trader makes money, they likely are building in countless bad habits that will harm them in the long run.*\n\n1. Defining Realistic Goals\n2. Understanding common trader shortfalls. (SSRN: The Courage of Misguided Convictions: The Trading Behavior of Individual Investors)\n3. Market function & basic economics - how markets work. MIT OCW can help here\n4. Derivatives - overview options and futures\n5. Options - Review their history, use, and general theory\n6. Types of options (Book above)\n7. Components of an options contract & settlement\n8. Basic structures: long and short single options to start\n9. How to read options chains\n10. Option pricing and volatility\n11. First and second order greeks\n12. Portfolio management\n13. Analysis (Fundamental and Technical)\n14. Here I'd keep things as simple as possible and relevant to the timeframe you're trying to trade. It's okay to learn and experiment but WAY too easy to get completely stuck with the bazillion analysis tools out there.\n15. Organizing your trading: creating trading plans, trading logs, strategy outlines\n16. Option Structures: Here, I'd explore everything you can find but I'd clearly define a required use case that you're filling. For me, it's having 1-2 for long and short directional and volatility thesis.\n17. Long direction: covered strangles, ratio call diagonals\n18. Short direction: ratio put diagonals, short calls\n19. Long vol: long straddles or strangles\n20. Short vol: short straddles or strangles\n21. *Of note, all of the individual option components from above can be traded. Things can also have combined purpose: aka if I'm short vol but also have a short bias, short calls fit well, etc.*\n22. Testing & Optimization - here we outline how we can codify testing our ideas, analyzing results, and integrating into our approach\n23. Basic understanding of statistic. MIT OCW also helpful here.\n24. How to backtest, forward test, and live test\n25. Process to review our trading logs & update our trading plans\n\nHow do we assess our competence as a trader? Before we start actively trading, we can papertrade for 6months to make all the stupid mistakes we all make, track our performance, and learn the basics. Papertrading will never fully replace trading, but for those that argue \"it's not the same thing, so it's not worth it\" I always say - if you're unable to take papertrading seriously, trading is likely not for you. Moreover, we can learn a LOT papertrading: aka that we all fat finger and enter the wrong orders and need to double check, that we need a pre-trade checklist to make sure we're checking all the key components until we know them cold (which is only realized after you enter to see earnings is in a week, etc). It can be difficult to embody, but sometimes going slower actually leads to much faster performance - this applies heavily to trading.\n\nEdit 1. Someone in the comments asked for a longer reading list, here’s 10 to start.\n\n1. Options as a Strategic Investment\n2. Option Volatility and Pricing\n3. Positional Options Trading\n4. Volatility Trading\n5. Option Trading\n6. Expected Returns\n7. What Works on Wall Street (this is useful more as a model of how to approach practically testing ideas and provides interesting market datapoints)\n8. How to Make Money in Stocks (useful for directional analysis)\n9. The Beginners Guide to Stoicism (weird I know, but once you have the technical proficiency as a trader, the game turns to self regulation which is a beast entirely to itself)\n10. SSRN - search the terms “options” “options trading” “trading” “investor” “investing” “stock market”. I read off SSRN weekly and it’s extremely useful to supplement my own research.\n\n**My primary strategies** are designed to let me trade: price trends (both up and down), volatility (expansion and contraction), and structural volatility (think different risk premiums). This approach allows me to continue feeding the account regardless of the current market regime, maintaining broad exposure to the primary market theme while still holding non-beta correlated positions.\n\n1. Covered strangles in index ETFs: Buying shares, selling calls at a ratio against the shares, and selling cash-secured puts to capture elevated put IV.\n2. Ratio diagonals (calls for upside, puts for downside): I buy in-the-money (ITM) options with at least 60 DTE, now favoring 90-180 DTE. This forms the base position. I then sometimes sell options with less than 30 DTE against the longs at a very light ratio to maintain upside potential while capturing some upfront premium to offset theta decay on the longs. Often, I'll enter the long positions without the shorts and phase them in over time (if at all).\n3. Short straddles/strangles: In the past five years, strangles have outperformed straddles in my approach to trading variance risk premiums. These are typically 0 and about 40 DTE, with shorts ranging from 0.15 to 0.35 delta.\n4. Long straddles: To capture expanding IV, typically buying about two weeks before a stock reports earnings to trade the run-up. Exits occur by the day before earnings at the latest.\n5. Momentum trades in futures: I employ a \"dumb\" momentum strategy in futures where I buy the outperforming quartile and fade the bottom-performing one, rotating monthly. I often deviate from this to amplify returns through discretionary management of stronger and weaker performers.\n6. I’ve also moved my larger positions into Section 1256 products for 60/40 tax treatment along with electing Day Trader (stupid terminology) status with the IRS.\n7. My job is to do my absolute best to analyze the current market theme and construct a portfolio that fits. As the market theme changes, so does the portfolio. This is completely different that my original expectation but has worked really well.\n8. The process is simple. I target a certain return each year that keeps me on a solid growth trajectory. I withdraw what we need from the account each month tracking the distributions so I can analyze trend and make sure I’m maintaining future growth (I’m 33 years old now, no kids yet). Each years’ profit cover post tax distributions for the current year.\n\nGood luck!", "author": "esInvests", "created_time": "2024-12-14T00:51:58", "url": "https://reddit.com/r/options/comments/1hdr2dc/things_that_are_worth_doing/", "upvotes": 304, "comments_count": 45, "sentiment": "bullish", "engagement_score": 394.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdr85s", "title": "20/10 Vision with AI: The Singularity of Sight Is Here", "content": "12 years ago, I decided not to go for LASIK or ReLEx SMILE. I thought, “What if something better comes along?” Now, it finally feels like it has.\n\nThere’s this new AI-powered laser surgery called “Eyevatar.” It builds a digital twin of your eye, runs thousands of simulations, and figures out the best way to reshape your cornea. The results? People are getting 20/10 vision. That means seeing at 20 feet what most people need to be 10 feet away to see.\n\nLooking back, I’m glad I waited. LASIK always felt like it had too many side effects—halos, glare, or vision that didn’t quite hit the mark for some people. This new tech seems way more precise. I’m planning to try it in the next year or two.\n\nWould you wait for this, or do you think LASIK is still good enough? Let’s hear your thoughts.", "author": "qubitser", "created_time": "2024-12-14T01:00:21", "url": "https://reddit.com/r/singularity/comments/1hdr85s/2010_vision_with_ai_the_singularity_of_sight_is/", "upvotes": 2006, "comments_count": 309, "sentiment": "bullish", "engagement_score": 2624.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdragl", "title": "TIL that Nickelodeon almost cancelled <PERSON>’s Christmas episode because they thought kids wouldn't care about its portrayal of the Vietnam War. An executive revoked the decision when her nine-year-old son saw a rough cut of the episode and asked '<PERSON>, is that what Vietnam was all about?'.", "content": "", "author": "deleted", "created_time": "2024-12-14T01:03:27", "url": "https://reddit.com/r/todayilearned/comments/1hdragl/til_that_nickelodeon_almost_cancelled_hey_arnolds/", "upvotes": 63372, "comments_count": 1265, "sentiment": "neutral", "engagement_score": 65902.0, "source_subreddit": "todayilearned", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdsky9", "title": "Tesla FSD turns into the wrong lane", "content": "", "author": "ThotPoppa", "created_time": "2024-12-14T02:11:56", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hdsky9/tesla_fsd_turns_into_the_wrong_lane/", "upvotes": 258, "comments_count": 199, "sentiment": "neutral", "engagement_score": 656.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdvlv6", "title": "How and why does <PERSON><PERSON> succeed in almost all of his ventures even when the wind is against him ?", "content": "So I saw this post somewhere and decided to ask this here too. <PERSON><PERSON> has probably been involved, initiated and successfully executes more ventures in his life than any other tech-billionaire CEO. SpaceX, Tesla, Twitter, PayPal (his company was acquired by PayPal after which it boomed), Neuralink, OpenAI etc. How is it that one single person can find time to do this much in life and succeed in each and every one of them ? It almost seems unreal. \n\nAnd in most of his ventures, he was always very close to being done once and for all and then somehow always finds a way to climb back up ? The SpaceX's 3 failed launches (back to back), <PERSON><PERSON>'s near death condition when he took over, Twitter's entire drama with lawsuits and big companies leaving the hands etc. These are all examples of near death experiences he must've had during his journey. \n\nBut how does he always climb back out of it ? Is it because of his \"failure's not an option\" and \"never give up\" attitude ? Or does he have some very foolproof strategies ?\n\nAlso how does one single person do so much in such a short span and yet still have time to shitpost on twitter ?\n\nAlso just to be clear I am not a Musk fanboy. I respect him for some of his work and at the same time criticize for many of his actions.", "author": "FreeBirdy00", "created_time": "2024-12-14T05:06:36", "url": "https://reddit.com/r/Entrepreneur/comments/1hdvlv6/how_and_why_does_musk_succeed_in_almost_all_of/", "upvotes": 396, "comments_count": 619, "sentiment": "bearish", "engagement_score": 1634.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdwdg4", "title": "US jury finds Vegas police fabricated evidence in 2001 killing, awards $34M to exonerated woman", "content": "", "author": "gnomegnat", "created_time": "2024-12-14T05:55:08", "url": "https://reddit.com/r/UpliftingNews/comments/1hdwdg4/us_jury_finds_vegas_police_fabricated_evidence_in/", "upvotes": 14619, "comments_count": 262, "sentiment": "neutral", "engagement_score": 15143.0, "source_subreddit": "UpliftingNews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdxoi4", "title": "Canoo Shares Skyrocket 96%, Hits Highest Volume Day Since IPO - EV", "content": "", "author": "wewewawa", "created_time": "2024-12-14T07:25:34", "url": "https://reddit.com/r/electriccars/comments/1hdxoi4/canoo_shares_skyrocket_96_hits_highest_volume_day/", "upvotes": 3, "comments_count": 3, "sentiment": "bullish", "engagement_score": 9.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdy7zg", "title": "Maximum Jobs in LINKEDIN are fake!!!!?????", "content": "idk but i feel like jobs in LINKEDIN are SCAM maximum times! Their logo and everything but idk is it fr", "author": "Classic_Profile_891", "created_time": "2024-12-14T08:05:45", "url": "https://reddit.com/r/DigitalMarketing/comments/1hdy7zg/maximum_jobs_in_linkedin_are_fake/", "upvotes": 1, "comments_count": 19, "sentiment": "neutral", "engagement_score": 39.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1he0kq8", "title": "Have I almost achieved FIRE?", "content": "I’m 22 and have 24k invested and going to hold till I’m about 62. Ive gone the bogle head route of investing just no bonds yet because I’m still young. Hopefully I’ll average 7-15% interest. I realized tho at 8% I should have just above 800k in 40 years, at 10% it could be 1.6M. I’ve heard you need about 1M to retire, would that mean I technically might not have to invest another dime and I would probably be able to retire? Of course I’ll continue to invest but I’m curious if this is somewhat true. It would bring a lot of peace of mind haha", "author": "S_H_R_O_O_M_S999", "created_time": "2024-12-14T10:56:37", "url": "https://reddit.com/r/Fire/comments/1he0kq8/have_i_almost_achieved_fire/", "upvotes": 0, "comments_count": 82, "sentiment": "neutral", "engagement_score": 164.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1he35h4", "title": "Xmas or no Xmas for the kids", "content": "After a rough year and almost losing everything and ready to put a bullet in my head, would you put a few items on a cc or just explain to the kids that there is no Xmas this year? \n\nI won’t get into all the backstory about the failure but business was a partnership for 14 years. Going from success to the very bottom has been extremely difficult for me. ", "author": "innerpeace_labrynth", "created_time": "2024-12-14T13:44:07", "url": "https://reddit.com/r/smallbusiness/comments/1he35h4/xmas_or_no_xmas_for_the_kids/", "upvotes": 0, "comments_count": 44, "sentiment": "bullish", "engagement_score": 88.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1he36pq", "title": "Idea of EV is awesome but why do they need to look so bad", "content": "I like the idea of ev and like seeing more in my town and local dealers.I just seen the EV Silverado and it a Chevy Avalanche but very futuristic.I feel that with most EVs.Why can't more EVs just be the body style most Americans are use to,like the F150 lighting,looks like a F150 but EV.Ive seen conversions with older cars like corvettes and pickups.Do body styles need to be completely different for a battery and the motors?I'm just not very knowledgeable on EV yet.", "author": "Dusk011506", "created_time": "2024-12-14T13:46:00", "url": "https://reddit.com/r/electriccars/comments/1he36pq/idea_of_ev_is_awesome_but_why_do_they_need_to/", "upvotes": 0, "comments_count": 83, "sentiment": "neutral", "engagement_score": 166.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1he59i4", "title": "The phony comforts of AI skepticism - It’s fun to say that artificial intelligence is fake and sucks — but evidence is mounting that it’s real and dangerous", "content": "", "author": "katxwoods", "created_time": "2024-12-14T15:29:26", "url": "https://reddit.com/r/Futurology/comments/1he59i4/the_phony_comforts_of_ai_skepticism_its_fun_to/", "upvotes": 0, "comments_count": 16, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1he7m33", "title": "Why are 10 minute \"Fresh Food\" delivery apps becoming a thing in India?", "content": "They're trying to \"solve a problem\" that never really existed.\nSwish, Zepto Cafe, <PERSON><PERSON><PERSON>lt, etc. promising fresh food in 10 mins when we know it's either shit or just frozen food/fast food reheated or whatever. There have been so many complaints and people saying that they felt uneasy and sick after having Swish food.\nAll they're riding on is gimmicky investor money, burning cash and running fake PR on LinkedIn and otherwise.\nRecently came across some tweet alleging Zepto  Cafe using all kinds of dirty tricks and undercuts to fulfill their orders.\n\nMy question is, WHY?\nIf you really have only 10 mins to get food, step down and get something(which most people do btw). I feel all these startups are so gimmicky, 10 min delivery was already something which required so much exploitation but I'll not get into that. Fresh food in 10 min seems like a joke, which no one wants to hear btw. Every person who I have talked to has only scoffed at the idea, calling it unnecessary.\n\nMy question is, we live in a world full of real issues, why are we supporting shitty ideas like Swish and Zepto Cafe just because they cater to like 0.1% of the people. Why not put it in places where it actually contributes to society.\n\nWe all know that these people running promotions initially will show numbers and revenue, but there's no way they can sustain. Plus the exploitation of labour is unmatched, there is no justification to that, and most importantly, no need for it.\n\nMight be wrong, would like to hear more opinions.", "author": "suffer-surfer", "created_time": "2024-12-14T17:20:00", "url": "https://reddit.com/r/startups/comments/1he7m33/why_are_10_minute_fresh_food_delivery_apps/", "upvotes": 3, "comments_count": 35, "sentiment": "neutral", "engagement_score": 73.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1he841t", "title": "Free supercharging for life now being offered on new model S", "content": "", "author": "KM3134", "created_time": "2024-12-14T17:43:41", "url": "https://reddit.com/r/teslamotors/comments/1he841t/free_supercharging_for_life_now_being_offered_on/", "upvotes": 1075, "comments_count": 267, "sentiment": "neutral", "engagement_score": 1609.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1heai3r", "title": "4-min shower hotel game", "content": "Saw this in the hotel I was staying at in Barcelona. Even my husband played the game, and he doesn’t consider sustainability often. He specifically said that the hour glass was what made it appealing to him. He said it has to be analog. If it was digital he wouldn’t participate.", "author": "isthisgaslighting", "created_time": "2024-12-14T19:33:39", "url": "https://reddit.com/r/sustainability/comments/1heai3r/4min_shower_hotel_game/", "upvotes": 8444, "comments_count": 541, "sentiment": "neutral", "engagement_score": 9526.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hearhv", "title": "Dropped power bank on the floor", "content": "I dropped my powerbank on the floor it’s still working fine and is not heating up however it does have a gap now:/ is it still safe?? ", "author": "Iamsocool19", "created_time": "2024-12-14T19:45:24", "url": "https://reddit.com/r/batteries/comments/1hearhv/dropped_power_bank_on_the_floor/", "upvotes": 2, "comments_count": 18, "sentiment": "bearish", "engagement_score": 38.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hebfri", "title": "Importing datasets in carla", "content": "Hi everyone,\n\nI’m working on a project involving traffic sign object detection in the CARLA simulator, and I’ve hit a roadblock. CARLA doesn’t seem to have a rich set of traffic signs pre-placed in the maps, and they’re not available in the blueprint library either.\n\nFrom what I’ve read, the only realistic way to integrate traffic signs into CARLA is by customizing the map using **Unreal Engine**, where I’d have to place the assets manually or edit the OpenDRIVE map.\n\nThat said, I’m wondering:\n\n1. Is this **really the only way** to add traffic signs to CARLA?\n2. **What about pre-made datasets of traffic sign images**? Can they be imported into CARLA, and if so, how can I integrate them seamlessly into the simulation?\n3. Are there any simpler workflows, tools, or methods that I might be missing?\n\nIf anyone has worked on adding traffic signs to CARLA or has experience using external datasets for this, I’d really appreciate your insights. I’m trying to figure out whether this process is inherently this complex or if I’m overcomplicating it.\n\nThanks in advance for your help!", "author": "Master-Ebb593", "created_time": "2024-12-14T20:15:50", "url": "https://reddit.com/r/AutonomousVehicles/comments/1hebfri/importing_datasets_in_carla/", "upvotes": 3, "comments_count": 3, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hebo0r", "title": "Is EcoVadis greenwashing?", "content": "Thousands of companies have paid for their evaluation. It costs 18 000€ for 3 years for a company of 1000+ employees. There's nearly zero information about EcoVadis company in the media.\n\nIs this somewhat similar to [ethispehere.com](http://ethispehere.com) ?\n\n[https://ecovadis.com/plans-pricing](https://ecovadis.com/plans-pricing)", "author": "SchruteFarms82", "created_time": "2024-12-14T20:26:37", "url": "https://reddit.com/r/Renewable/comments/1hebo0r/is_ecovadis_greenwashing/", "upvotes": 7, "comments_count": 10, "sentiment": "neutral", "engagement_score": 27.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hecmt3", "title": "What is the most important investing lesson you’ve learned that everyone should know?", "content": "Whether you’ve been investing for decades or just started, we’ve all picked up lessons—some learned the hard way, some through success.\n\nWhat’s the one lesson that completely changed the way you approach investing and that you think everyone should know? It could be a simple tip, a mindset shift, or a strategy.\n\nCurious to hear your thoughts and wisdom!", "author": "Tyty11519", "created_time": "2024-12-14T21:11:42", "url": "https://reddit.com/r/investing/comments/1hecmt3/what_is_the_most_important_investing_lesson_youve/", "upvotes": 327, "comments_count": 338, "sentiment": "neutral", "engagement_score": 1003.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1heeutc", "title": "Going electric for the first time due to a new commute. Would love opinions/advice!", "content": "Update: I test drove the EX30 yesterday and think its the winner. As much as I tried to love the Ioniq 5, I just couldn't get excited about it. I enjoyed the driving experience of the EV6 but not the looks (both interior or exterior). The EX30 drives like the EV6 but with better aesthetics IMO.\n\nI'm starting a new job next year in which I'll be commuting 45mi each way, 2xs a week. I expect traffic to be pretty hellish (SF Bay Area) and looking to buy an electric vehicle. My head is spinning with all the options and the pros/cons of each, including whether to go new or lightly used.\n\nMy most important criteria is a smooth, quiet, comfortable ride, good highway driving assists (adaptive cruise control, lane centering, etc.) for the inevitable stop and go I'll be in and easy controls/navigation when driving. I've been driving a Honda Fit for 15 years, so I want something more substantial but don't need a large vehicle. If Honda made an electric CR-V that would basically be my dream car but alas...\n\nWWYD?\n\n1. **Hyundai Ioniq 5 SEL.** This feels like the most practical option as it gets top ratings in on all the review sites. All the controls feel pretty intuitive and easy to use. But something in me is not in love with it? Might have been my experience with the sales guys tho.\n2. **Volvo EX40, C40** (or perhaps the EX30 once its available in US). I don't like that I have to get the most expensive trim to get the Pilot Assist but do love the voice activated Google Assistant since I hate futzing with buttons and screens. Obvs more expensive than the Ioniq but the interior feels more premium. Unfortunately, would need to get new to get the 297mi range in the RWD; all used ones are AWD with a range of 220.\n3. **Kia EV6.** I haven't test driven this one yet but hear good things; have an appointment Monday to give it a try.\n\nOnes I test drove but are out include:  \nSubaru Solterra - bad reviews all around  \nHonda Prologue - too much car for me  \nKira Niro - felt like a go-cart  \nTesla - I just can't support rn", "author": "BirdieJean545", "created_time": "2024-12-14T22:56:58", "url": "https://reddit.com/r/electriccars/comments/1heeutc/going_electric_for_the_first_time_due_to_a_new/", "upvotes": 10, "comments_count": 106, "sentiment": "bullish", "engagement_score": 222.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}]