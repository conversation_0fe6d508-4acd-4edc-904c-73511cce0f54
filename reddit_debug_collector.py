#!/usr/bin/env python3
"""
Reddit数据收集调试工具
专门用于调试为什么收集到的相关帖子数量为0
"""

import os
import argparse
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 清除系统环境变量
reddit_vars = ['REDDIT_CLIENT_ID', 'REDDIT_CLIENT_SECRET', 'REDDIT_USER_AGENT', 'REDDIT_USERNAME', 'REDDIT_PASSWORD']
for var in reddit_vars:
    if var in os.environ:
        del os.environ[var]

# 强制从.env文件加载
load_dotenv(override=True)

from reddit_live_collector import RedditLiveCollector, load_reddit_config

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Reddit数据收集调试工具')
    parser.add_argument('--subreddits', nargs='+', default=['stocks'], 
                       help='目标子版块列表')
    parser.add_argument('--tickers', nargs='+', default=['GOOGL', 'TSLA'], 
                       help='目标股票代码')
    parser.add_argument('--limit-per-subreddit', type=int, default=25,
                       help='每个子版块的帖子数量限制')
    parser.add_argument('--start-date', type=str, 
                       default=(datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, 
                       default=datetime.now().strftime('%Y-%m-%d'),
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--verbose', action='store_true',
                       help='显示详细调试信息')
    
    args = parser.parse_args()
    
    print("🔍 Reddit数据收集调试工具")
    print("="*60)
    print(f"目标子版块: {args.subreddits}")
    print(f"目标股票: {args.tickers}")
    print(f"每个子版块限制: {args.limit_per_subreddit} 个帖子")
    print(f"时间范围: {args.start_date} 到 {args.end_date}")
    print(f"详细模式: {'开启' if args.verbose else '关闭'}")
    print("="*60)
    
    try:
        # 创建收集器
        # 加载Reddit配置
        config = load_reddit_config()
        collector = RedditLiveCollector(config)
        
        # 设置日志级别
        if args.verbose:
            import logging
            collector.logger.setLevel(logging.DEBUG)
            handler = logging.StreamHandler()
            handler.setLevel(logging.DEBUG)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            collector.logger.addHandler(handler)
        
        # 开始收集
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        
        print(f"\n🚀 开始收集数据...")
        
        stats = collector.collect_data(
            subreddits=args.subreddits,
            tickers=args.tickers,
            start_date=start_date,
            end_date=end_date,
            limit_per_subreddit=args.limit_per_subreddit
        )
        
        print(f"\n📊 收集完成!")
        print("="*60)
        print(f"处理子版块数: {stats.get('subreddits_processed', 0)}")
        print(f"总处理帖子数: {stats.get('total_posts', 0)}")
        print(f"相关帖子数: {stats.get('relevant_posts', 0)}")
        
        if stats.get('total_posts', 0) > 0:
            match_rate = stats.get('relevant_posts', 0) / stats.get('total_posts', 0) * 100
            print(f"匹配率: {match_rate:.1f}%")
        
        # 分析结果
        print(f"\n🔍 分析结果:")
        if stats.get('relevant_posts', 0) == 0:
            print("❌ 没有找到相关帖子")
            print("\n💡 可能的原因:")
            print("1. 当前时间段内确实没有相关股票的讨论")
            print("2. 关键词匹配过于严格")
            print("3. 目标子版块不是讨论这些股票的主要场所")
            print("\n🛠️ 建议解决方案:")
            print("1. 扩大时间范围 (--start-date 更早的日期)")
            print("2. 增加更多子版块 (--subreddits stocks investing wallstreetbets)")
            print("3. 尝试其他热门股票 (--tickers AAPL MSFT NVDA)")
            print("4. 增加每个子版块的帖子数量 (--limit-per-subreddit 50)")
        else:
            print(f"✅ 成功找到 {stats.get('relevant_posts', 0)} 个相关帖子")
            print("数据已保存到 social_media_data 目录")
        
        # 提供进一步的调试建议
        if not args.verbose:
            print(f"\n💡 如需查看详细处理过程，请使用 --verbose 参数")
        
        print(f"\n🔗 查看保存的数据:")
        print(f"ls social_media_data/")
        
    except Exception as e:
        print(f"❌ 调试工具运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
