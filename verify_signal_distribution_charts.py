#!/usr/bin/env python3
"""
验证信号分布图表更新脚本
检查生成的信号分布图表是否成功包含了价格走势图
"""

import os
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from datetime import datetime

def verify_chart_files():
    """验证图表文件是否存在并显示基本信息"""
    stocks = ['AAPL', 'MSFT', 'NVDA']
    chart_info = {}
    
    print("📊 信号分布图表验证报告")
    print("=" * 60)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    for stock in stocks:
        signal_chart = f"{stock}_signal_distribution.png"
        accuracy_chart = f"{stock}_accuracy_comparison.png"
        
        print(f"🔍 检查 {stock} 股票图表:")
        
        # 检查信号分布图
        if os.path.exists(signal_chart):
            file_size = os.path.getsize(signal_chart) / 1024  # KB
            mod_time = datetime.fromtimestamp(os.path.getmtime(signal_chart))
            print(f"  ✅ 信号分布图: {signal_chart}")
            print(f"     文件大小: {file_size:.1f} KB")
            print(f"     修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            chart_info[f"{stock}_signal"] = {
                "exists": True,
                "size_kb": file_size,
                "modified": mod_time
            }
        else:
            print(f"  ❌ 信号分布图: {signal_chart} 不存在")
            chart_info[f"{stock}_signal"] = {"exists": False}
        
        # 检查准确率对比图
        if os.path.exists(accuracy_chart):
            file_size = os.path.getsize(accuracy_chart) / 1024  # KB
            mod_time = datetime.fromtimestamp(os.path.getmtime(accuracy_chart))
            print(f"  ✅ 准确率对比图: {accuracy_chart}")
            print(f"     文件大小: {file_size:.1f} KB")
            print(f"     修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            chart_info[f"{stock}_accuracy"] = {
                "exists": True,
                "size_kb": file_size,
                "modified": mod_time
            }
        else:
            print(f"  ❌ 准确率对比图: {accuracy_chart} 不存在")
            chart_info[f"{stock}_accuracy"] = {"exists": False}
        
        print()
    
    return chart_info

def display_chart_preview():
    """显示图表预览信息"""
    print("📈 图表内容说明:")
    print("-" * 40)
    print("信号分布图表布局 (2x2):")
    print("  ┌─────────────────┬─────────────────┐")
    print("  │  Gemini2.0      │  GPT-3.5        │")
    print("  │  信号分布       │  信号分布       │")
    print("  ├─────────────────┼─────────────────┤")
    print("  │  Grok Beta      │  股票价格走势   │")
    print("  │  信号分布       │  (2025年)       │")
    print("  └─────────────────┴─────────────────┘")
    print()
    print("✨ 更新内容:")
    print("  • 在右下角空白位置添加了股票价格走势图")
    print("  • 价格数据来源: 本地financial_data_offline目录")
    print("  • 时间范围: 2025年1月1日 - 2025年6月1日")
    print("  • 包含收盘价走势和20日移动平均线")
    print("  • 保持中文字体支持和一致的图表风格")
    print("  • 使用回测系统相同的价格数据源，确保一致性")
    print()

def generate_summary_report(chart_info):
    """生成总结报告"""
    print("📋 总结报告:")
    print("-" * 40)
    
    total_charts = len([k for k in chart_info.keys() if chart_info[k].get("exists", False)])
    expected_charts = 6  # 3 stocks × 2 chart types
    
    print(f"图表生成状态: {total_charts}/{expected_charts} 个图表成功生成")
    
    if total_charts == expected_charts:
        print("🎉 所有图表都已成功生成并更新！")
    else:
        print("⚠️  部分图表可能存在问题，请检查上述详细信息")
    
    print()
    print("🔧 技术实现:")
    print("  • 修改了 analyze_llm_performance.py 脚本")
    print("  • 添加了 add_price_trend_chart() 方法")
    print("  • 使用本地JSON文件获取股票价格数据")
    print("  • 解析字符串格式的价格数据并转换为DataFrame")
    print("  • 在 2x2 布局的第四个位置添加价格走势图")
    print("  • 处理了文件不存在和数据解析异常的错误处理")
    
    print()
    print("📁 生成的文件:")
    for stock in ['AAPL', 'MSFT', 'NVDA']:
        signal_exists = chart_info.get(f"{stock}_signal", {}).get("exists", False)
        accuracy_exists = chart_info.get(f"{stock}_accuracy", {}).get("exists", False)
        print(f"  {stock}: 信号分布图 {'✅' if signal_exists else '❌'} | 准确率对比图 {'✅' if accuracy_exists else '❌'}")

def main():
    """主函数"""
    print()
    chart_info = verify_chart_files()
    display_chart_preview()
    generate_summary_report(chart_info)
    
    print()
    print("🎯 下一步建议:")
    print("  1. 查看生成的信号分布图表，确认价格走势图显示正常")
    print("  2. 如需调整价格图表样式，可修改 add_price_trend_chart() 方法")
    print("  3. 如遇网络问题导致价格数据获取失败，图表会显示相应提示信息")
    print("  4. 可以根据需要调整时间范围或添加更多技术指标")
    print()

if __name__ == "__main__":
    main()
