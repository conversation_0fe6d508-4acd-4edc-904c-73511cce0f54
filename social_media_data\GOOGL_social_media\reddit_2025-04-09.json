[{"platform": "reddit", "post_id": "reddit_1juuz19", "title": "Google Banned Me from Local Guides After 10+ Years and 100M Photo Views — No Warning, No Explanation", "content": "***So happy to report that after some great connections, advice and appeal to Google they have effectively reinstated my Google Local Guides account and status. Appreciate the comments and support. What a ride that was...SKOL!***\n\n\n\nI’ve been contributing to Google Maps for over a decade—uploading photos, videos, reviews, and corrections to help improve the platform. I hit **Level 10** as a Local Guide with over **45,000 contributions** and nearly **100 million views**.\n\nOut of nowhere, I noticed:\n\n* My Local Guide badge and profile were gone\n* My uploads were stuck in “upload pending”\n* My view count had completely frozen\n\nThen I tried rejoining the Local Guides program and got this message:  \n**“You’re not eligible to become a Local Guide.”**\n\nFinally, I found a hidden message saying I’d been banned for violating the rules—but they never sent me a warning, a reason, or anything. Just silently nuked my status, my visibility, and 10+ years of contributions.\n\nSupport? Nothing. Forums? Banned there too. No way to appeal.\n\nSo that’s it: 100 million views, thousands of hours, all gone with no explanation.  \nThanks for the transparency, Google.\n\nhttps://preview.redd.it/2qzvy9oywpte1.png?width=3456&format=png&auto=webp&s=090f5b32b9b8a5128ffc0d68d33f773be5f1467e\n\nhttps://preview.redd.it/nzbwe9oywpte1.png?width=3456&format=png&auto=webp&s=1441bfc44fc09a56e0ae041e06165ffac1c8ad4d\n\nhttps://preview.redd.it/j02zjnvzwpte1.png?width=3456&format=png&auto=webp&s=f7017ecf04c3f46ab32b8475efbb27744d404281\n\nhttps://preview.redd.it/mjxqwlj0xpte1.png?width=1179&format=png&auto=webp&s=568379b87b13b667999e06ee5ce7c8101caede9b", "author": "vikingsfanben", "created_time": "2025-04-09T02:11:06", "url": "https://reddit.com/r/GoogleMaps/comments/1juuz19/google_banned_me_from_local_guides_after_10_years/", "upvotes": 103, "comments_count": 65, "sentiment": "neutral", "engagement_score": 233.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jv1bj7", "title": "Google keeps directing traffic to Reddit", "content": "I'm trying to decide between using Replit, Bolt or Lovable  \n  \nThe natural thing to do was google it  \n  \nThe first results are:  \n\\> Youtube Videos (Google owns Youtube)   \n\\> Reddit thread (Google has a $60M/y deal with Reddit)  \n  \nIt's becoming obvious where Google wants you to look - and where people are actually making buying decisions ", "author": "notomarsol", "created_time": "2025-04-09T09:05:14", "url": "https://reddit.com/r/marketing/comments/1jv1bj7/google_keeps_directing_traffic_to_reddit/", "upvotes": 0, "comments_count": 15, "sentiment": "bullish", "engagement_score": 30.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jv5ygz", "title": "Self-Driving Cars: Future Revolution or Risky Gamble?", "content": "The age of self-driving cars is here, but are we ready for it? Will autonomous vehicles make our roads safer, or are they a technological gamble with unknown risks?  \n\nAutonomous vehicles are no longer just a dream—they are being tested on roads worldwide. Companies like Tesla, Waymo, and major automakers are investing billions to develop self-driving technology. These cars rely on artificial intelligence, sensors, and cameras to navigate roads without human input. But how well do they really work?  ", "author": "Es<PERSON><PERSON><PERSON>", "created_time": "2025-04-09T13:36:25", "url": "https://reddit.com/r/youtube/comments/1jv5ygz/selfdriving_cars_future_revolution_or_risky_gamble/", "upvotes": 0, "comments_count": 0, "sentiment": "bullish", "engagement_score": 0.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jv7g60", "title": "Big tech’s new datacenters will take water from the world’s driest areas | Amazon, Google and Microsoft are building datacenters in water-scarce parts of five continents", "content": "", "author": "chrisdh79", "created_time": "2025-04-09T14:41:57", "url": "https://reddit.com/r/environment/comments/1jv7g60/big_techs_new_datacenters_will_take_water_from/", "upvotes": 36, "comments_count": 2, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "environment", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jv87fp", "title": "Can we actually report <PERSON> for market manipulation over his tweets?", "content": "Like… real question.\n\nDude drops tariffs, tanks the market, then tweets “**THIS IS A GREAT TIME TO BUY**!!!” right after — and magically, his own stock pops premarket like a toaster pastry. Isn’t that textbook market manipulation? Or does shouting in all caps make it legal now?\n\nIf any other CEO did this, the SEC would’ve already parked a van outside their house. But when <PERSON> does it, it’s just another Tuesday on Twitter.\n\nIs there a hotline? A Google Form? A carrier pigeon I can send to the SEC?\n\nNot saying I expect anything to happen… just wondering how much more obvious it needs to be before someone goes, “Hey, wait a minute…”", "author": "ToothNo6373", "created_time": "2025-04-09T15:13:24", "url": "https://reddit.com/r/stocks/comments/1jv87fp/can_we_actually_report_trump_for_market/", "upvotes": 7451, "comments_count": 895, "sentiment": "neutral", "engagement_score": 9241.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jv8wm4", "title": "Now available on YouTube, stream course lectures from Stanford AA228V Validation of Safety Critical Systems", "content": "", "author": "Stanford_Online", "created_time": "2025-04-09T15:42:22", "url": "https://reddit.com/r/AutonomousVehicles/comments/1jv8wm4/now_available_on_youtube_stream_course_lectures/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jvf7he", "title": "I am a Software Developer and I am tired and I never want to sit in front of a computer again. A rant", "content": "I know this is might be a little unjustified because I have a job that is well-paying, high demand and in a field with lots of opportunities. I am a web developer with some knowledge in NLP, meaning I've been working on AI things too.\n\nBut. I simply cannot do it anymore. I don't ever want to hear the word \"agile\" again. I don't ever want to play Planning Poker again. I don't ever want to wake up to find out that my most recent implementation is outdated because another super hot LLM has dropped overnight. I don't ever want to pretend to be proficient in yet another framework because the one I've been using is not cool anymore. I don't ever want to google how to revert a commit after pushing to remote again. I don't want to update oh-my-zsh every other day!!!!!!!!! I don't want to say \"I'm still working on it but I've made a lot of progress\" when in reality I haven't opened VSCode in three days because I'm sick of it. I don't want to discuss which IDE is best, I don't want to be stuck on a customer's API just to find out their documentation is completely wrong, I don't want to run into issue after issue until I can't remember what the actual task was anymore, I don't ever want to run out of GPU in Colab again. I don't want to have to check 5 different browsers to see if a margin is applied correctly. I don't ever want to compare model cards on huggingface again, I don't ever want to adjust parameters again, I don't ever want to refactor a single line of code again, I don't want to read another completely redundant comment other people's code because it was created by ChatGPT or Copilot. I don't want to see another component that is illegible because it is stuffed with tailwind. I don't want to discuss UX with stakeholders who apparently have never used an application in their lives. I don't want to be automatically labelled as  frontend and UX expert simply because I am a woman. I don't want to have to explain that the problem isn't the AI but the badly maintained data. I don't want to write a single Readme .md again. I don't want to write another prompt in my life. I don't want to restart another jupyter notebook ever again. I don't ever want to npm install again, I don't ever want to pip install -r requirements.txt just to run into dependency hell, and I don't want to take minutes every time I look for a previous message because I can't remember if it's in slack, teams, or discord. I don't want to write another word on a sticky note in miro and I don't want to look for \"the gif that best describes my mood\" either. I don't want to read another sentence on the world wide web that contains any of the words \"enhance\", \"leverage\", \"delve\". I don't want to \"embark\" or \"indulge\".\n\nI hate the internet. I have completely lost the ability to concentrate for longer than a couple of minutes. I have two monitors in addition to my laptop, I swipe between multiple desktops and it's still not enough for showing my emails, calendar, slack, teams, chatgpt, my IDE which in itself is separated into the main view and three different terminal tabs, the mongodb compass, postman, a browser window for googling, a browser window for compiling, a million other browser windows for github, jira, confluence, gcp or aws, and MY NOTES APP BECAUSE I DON'T REMEMBER A SINGLE THING ANYMORE.\n\nI know that a lot of these issues are directly related to my workplace, but I have tried all kinds of setups and also working independently, and I am done. Open for any job suggestions that do not involve any of the above. Also open for any additions to this list.\n\n Edit: UPDATE\n\nPeople of reddit, you are incredible! I did not expect this to be read and commented on by so many people. And I am honestly touched by the sympathy, concern and advice in your responses. I will try to reply to as many as possible in the next couple of days. Not sure whether to be happy or sad to see that so many people feel the same, but I am glad that some of your were able to improve their situation, be it in a new position or a completely new field of work.\n\nMost of you have suggested burnout, and I agree that it is time for a break for me (as soon as I can afford it). In the long run, I am still considering changing profession. I feel like my brain is just not suitable for doing all these things at once. I started programming because I did enjoy solving problems and the abstract thinking that is needed. But the IT world just seems too fast-paced for me. The jobs I had before, where I had to physically do something (mostly service and hospitality industry) were exhausting and at times it was hard not to hate people, but they weren’t frying my brain in the way that is is being fried now. It came with a different kind of satisfaction, and I guess this is something that differs from person to person. \n\nI also appreciate the people who took the time to tell me to suck it up. There was no need to be rude, but sometimes such comments put things into perspective again.\n\nMy offline hobby is cycling and taking longer bike trips, but I might try some of the things you suggested too, especially the ones that are about creating things. \n\nAgain, thank you very much for sharing your own stories and your thoughts!\n\nPS: I am a woman, but happy to be your bro. Also, I’m European.", "author": "Realistic_Shoulder13", "created_time": "2025-04-09T19:59:18", "url": "https://reddit.com/r/webdev/comments/1jvf7he/i_am_a_software_developer_and_i_am_tired_and_i/", "upvotes": 1443, "comments_count": 457, "sentiment": "bullish", "engagement_score": 2357.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}]