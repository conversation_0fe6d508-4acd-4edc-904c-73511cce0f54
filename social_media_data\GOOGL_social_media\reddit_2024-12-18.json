[{"platform": "reddit", "post_id": "reddit_1hgot8d", "title": "What should my next move be for my bidding strategy? (Performance Max)", "content": "I started running Google Ads for a small-scale e-commerce store three months ago. Below, I will provide a brief review as well as some stats. I think this review and the responses you guys provide can help lots of vulnerable people in this space.\n\nAs of right now, I am using a $100.00 USD per day budget on Performance Max. My bidding strategy is set to maximize conversions, fully automated with no target CPA set. **Last month (November) I had a 271% ROAS, 0.58% CTR, 92 Purchases, 4.54% Conv. Rate.** Now I know those stats don't mean much without context of my business model. **This month (December) which is a bit more than halfway finished, I had a 340% ROAS, 0.69% CTR, 89 purchases, 6.17% Conv. Rate.** I know I know, CTR is not great.\n\nNow, I have made attempts at Target CPA previously, and Target ROAS as well, but had no success. Why do I think I was unsuccessful?\n\n1. I did not have enough conversion data\n2. I perhaps didn't set the correct Target CPA/ROAS\n\nMy business model is a little different than most stores. We sell multiple versions of the same product. 95% of our entire inventory, is priced at $19.99. Which makes me think, is the \"conversion value, target ROAS\" route not for me? I hear everyone talking about how just about every e-commerce store ends up running some sort of value-based strategy, but I've also heard that value-based bidding is meant for stores with higher ticket items, which we do not have or plan on having.\n\n**My plan for January 1st:** as of right now, my plan is to let this thing run on \"maximize conversions\" automated bidding until the end of the year. Then on January 1st, I will be evaluating my campaign and maybe try out \"conversion value\" once again. Now that we have more conversion data, maybe it will do better. Maximize conversions did great during the Holidays, definitely let us find some new customers. But... without the holiday traffic, it was very hard to push past 300% ROAS. If it wasn't for the holidays, I believe I would be stuck in the 200% range again. Let me know of any opinions, advice, criticism. Anything helps more than you guys know. Have a great rest of your year!", "author": "ProfessionalGoat99", "created_time": "2024-12-18T00:07:50", "url": "https://reddit.com/r/adwords/comments/1hgot8d/what_should_my_next_move_be_for_my_bidding/", "upvotes": 4, "comments_count": 7, "sentiment": "bearish", "engagement_score": 18.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hgr937", "title": "Microsoft CEO <PERSON><PERSON><PERSON> says \"Google makes more money on Windows than all of Microsoft\" due to its dominance in search and distribution", "content": "Google makes more money on Windows than Microsoft, says CEO <PERSON><PERSON><PERSON>.", "author": "ControlCAD", "created_time": "2024-12-18T02:11:28", "url": "https://reddit.com/r/microsoft/comments/1hgr937/microsoft_ceo_satya_nadella_says_google_makes/", "upvotes": 390, "comments_count": 58, "sentiment": "neutral", "engagement_score": 506.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hgs1my", "title": "Google Delivered an empty box to my home, now Im down $1,378.75", "content": "During the black friday sales, I purchased a **Pixel 9 Pro XL Obsidian 512GB (Unlocked)** from google, the box was delayed and arrived a day later than expected.\n\nWhen It arrived I noticed on a NEST video the delivery driver doing something weird, after placing the box, he turned it around, in hindsight this was to allow to hide the package was already open.\n\nHe took his picture [https://imgur.com/qCeB8Hw](https://imgur.com/qCeB8Hw) You can see the damaged box on the bottom.\n\nWhen bringing the box in it looked bad, [https://imgur.com/CmhK4Vq](https://imgur.com/CmhK4Vq) the content of my pixel pro had been pilfered, only delivering the case, glass and hand in kit. [https://imgur.com/mgknAKg](https://imgur.com/mgknAKg) .\n\nI contacted Google Support, their \"specialists\" have determined I don't have a case, they delivered a 100% of the products.\n\nIm down a 1378.5 dollars on this, and have been stollen from, by FEDEX, and google. I used to believe google not being evil was just a stance. I've found out the hard way, they will steal from you and not even think twice. No matter how many videos or photos you send you are now their wage slave, and you have to like it.\n\nIf anyone has ideas or a way to get to someone that can actually help feel free to post.", "author": "Antiokloodun", "created_time": "2024-12-18T02:54:12", "url": "https://reddit.com/r/google/comments/1hgs1my/google_delivered_an_empty_box_to_my_home_now_im/", "upvotes": 885, "comments_count": 215, "sentiment": "neutral", "engagement_score": 1315.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hgto7k", "title": "Can someone explain this gain on my GOOG call options?", "content": "Body:\nI bought GOOG call options and I don’t understand why I am seeing a profit even though the price has not yet touched the breakeven point.\n\nHere are the key details of my position:\n\nContracts: +5\nAverage Cost: $0.04\nCurrent Price of the Option: $0.31\nMarket Value: $155.00\nDate Bought: 12/17\nExpiration Date: 12/20\nGOOG Breakeven Price: $245.04\nCurrent GOOG Price: $197.36\nToday’s Return: +$135.00 (+675.00%)\nTotal Return: +$135.00 (+675.00%)\nThe Greeks (Stats):\nDelta: 0.0355\nGamma: 0.0034\nTheta: -0.3328\nVega: 0.0132\nRho: 0.0005\nOther Stats:\n\nIV (Implied Volatility): 135.41%\nBid: $0.01 x 50\nAsk: $0.61 x 416\nMark Price: $0.31\nLast Trade Price: $0.01\nPrevious Close: $0.04\nVolume: 6\nOpen Interest: 537\nQuestion:\nI assumed that profit would only occur if the price of GOOG rises above the breakeven price ($245.04). Can someone explain why I am seeing a gain even though the price is at $197.36?\n\nIs this related to the IV (implied volatility) or some other factor in the options pricing?\nAny insights would be greatly appreciated!", "author": "satyam0795", "created_time": "2024-12-18T04:23:46", "url": "https://reddit.com/r/options/comments/1hgto7k/can_someone_explain_this_gain_on_my_goog_call/", "upvotes": 0, "comments_count": 20, "sentiment": "bullish", "engagement_score": 40.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hgwpn4", "title": "Is youtube dumb?", "content": "", "author": "oskiozki", "created_time": "2024-12-18T07:44:38", "url": "https://reddit.com/r/youtube/comments/1hgwpn4/is_youtube_dumb/", "upvotes": 16, "comments_count": 46, "sentiment": "neutral", "engagement_score": 108.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hgzxlb", "title": "Google's Waymo self driving taxis succeeding in San Francisco, and expanding", "content": "According to Reuters: \"When I visited San Francisco last month and rode in Waymo robotaxis, I ordered them via the Uber app and Waymo’s own. Waymo seems to be not only succeeding in the city by the Bay but expanding: it announced it would start service in Miami in 2026 two weeks ago; shares in Uber and Lyft slumped upon the news. Just before Cruise announced its demise, Uber publicized a new partnership with the Chinese autonomous vehicle maker WeRide in Abu Dhabi. WeRide makes the cars, Uber sends them your way.\"\n\nSo GM is shutting down Cruise. But the mainly software company Google is succeeding in self driving taxis. It shows how important software has become for the automotive companies, along with microchips. With robotaxis, automobiles will become high tech. It is unfair to highlight single events with self driving companies. Instead look at the overall impact on safety by metrics such as accidents per mile driven or otherwise, and compare it with human driven miles or otherwise.\n\nIndia would be a more difficult territory for robitaxis like Waymo. Uber and Indian OLA, are market leaders in app based taxi services at least where I live in New Delhi NCR. Most of the problems are not technical, but based on poor customer service. But drivers in India, can be faced with not following the traffic rules, and narrow streets. Thus implementing robotaxis will remove the driver and poor customer service, but AI software will have to be trained in Indian driving and road conditions. So I would be glad if Waymo entered the market, at least in the largest cities, where it will be easier to implement robotaxis. Or Uber partnered with a Chinese automaker, and introducing robotaxis to large metros in India.\n\nReference: https://www.theguardian.com/technology/2024/dec/16/robotaxis-general-motors-self-driving", "author": "fool49", "created_time": "2024-12-18T11:52:37", "url": "https://reddit.com/r/economy/comments/1hgzxlb/googles_waymo_self_driving_taxis_succeeding_in/", "upvotes": 0, "comments_count": 0, "sentiment": "bullish", "engagement_score": 0.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh03ri", "title": "Google is winning the ai race", "content": "Gemini 2.0 - top of the LLM arena, cheapest model per token if we account for quality it's way cheaper than competitors, 1/0100th the cost of o1\n\nTPU - <PERSON><PERSON> said, more compute and better compute infra than all their competitors and it isn't close\n\nNotebookLM - amazing product\n\nVeo 2/imagen 3 - SOTA, massively ahead of competition with a gap similar to that of gpt 3.5 vs gpt 4\n\nWaymo - only company I'm aware of offering driverless taxi services\n\nNot even mentioning DeepMind, alpha fold, alpha geometry, etc. what else have I missed?\n\nRemember: consensus on this sub was that Google is too big to innovate..now sentiment is shifting to \"they were always going to win anyway\"", "author": "AverageUnited3237", "created_time": "2024-12-18T12:03:09", "url": "https://reddit.com/r/singularity/comments/1hh03ri/google_is_winning_the_ai_race/", "upvotes": 1241, "comments_count": 382, "sentiment": "neutral", "engagement_score": 2005.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh0ovm", "title": "Income question ", "content": "When filling out an application for a credit card, loan or similar, what do you generally put down for income?\n\nWe get about $85k/yr social security and I have our “bank” send us $10k/month. They also pay our mortgage and property taxes and insurance directly and a few other minor things. So that’s about $160k/yr plus the $85k mentioned earlier \n\nWe have a nest egg of about $7M so in reality our declared “income” could be a lot more but we are really only drawing what we spend.   So, would you write down $245k or maybe round up to $300k? Or something different?  \n\nA couple years ago we were drawing less (actual expenses were less) and I applied for a different credit card and kept running into the limit each month  I also intend to buy a new car this year and will probably fill out a loan app for ~$100k and want lowest possible rate \n\nI never really know what to put down so it’s never consistent ", "author": "Effyew4t5", "created_time": "2024-12-18T12:39:17", "url": "https://reddit.com/r/financialindependence/comments/1hh0ovm/income_question/", "upvotes": 0, "comments_count": 95, "sentiment": "bullish", "engagement_score": 190.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh0p46", "title": "Google's <PERSON><PERSON> is really next gen. These are all videos made by people who had preview access and posted them on X. You can easily generate intros and design that are animated. It can even do anime. Especially crazy because it can generate them in 4k. ", "content": "", "author": "GodEmperor23", "created_time": "2024-12-18T12:39:43", "url": "https://reddit.com/r/singularity/comments/1hh0p46/googles_veo_is_really_next_gen_these_are_all/", "upvotes": 1128, "comments_count": 213, "sentiment": "neutral", "engagement_score": 1554.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh33nd", "title": "Killer captured by Google Maps while moving dismembered body in northern Spanish village", "content": "A picture is worth a thousand words. A red car and a man bent over and putting a large white bag in the boot. It's the image from Google Maps Street View application that helped National Police officers solve a murder in Soria, in north-central Spain, which they had started investigating more than a year ago.\n\nA vehicle from the search engine giant equipped with numerous cameras on its roof photographed every corner of Tajueco in Soria (Castilla y León). It was a routine visit as part of its route to take images for Google street view until it arrived at Calle El Norte.\n\nThere, an old red car was parked like any other day and next to it, a man was putting a big bundle in the boot. It could be a normal everyday scene, but it was not, and it proved to be the key piece that solved the puzzle National Police had been trying to put together since November 2023.\n\nSince that date, officers together with Guardia Civil had been looking for a 40-year-old Cuban man. The young man had come to Spain looking for his wife and been missing since November last year. Details were scarce and nobody knew his whereabouts.\n\nAfter a long period of investigation, a strange coincidence set off alarm bells: the Google Street View image of a man putting a bulky white plastic bag in the boot of a car in Calle El Norte de Tajueco.\n\nPolice officers discovered who the car belonged to and tapped the suspect's phone. They found out that he lived with the Cuban wife of the missing man. After months of listening to the couple's conversations, both were arrested and charged in connection with the crime.\n\nAfter ten months of investigation, the case has been solved. The remains of the young Cuban man were found dismembered in the cemetery of the municipality of Andaluz, just 12 minutes from the location where the vital image that solved the crime was taken.", "author": "Swissdanielle", "created_time": "2024-12-18T14:46:12", "url": "https://reddit.com/r/nottheonion/comments/1hh33nd/killer_captured_by_google_maps_while_moving/", "upvotes": 9126, "comments_count": 150, "sentiment": "bullish", "engagement_score": 9426.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh470m", "title": "Obviously reddit app knows who am I ", "content": "So I was flagged for ban evasion while using data on my app (which is very fortunate because it eliminates the possibility of my Wi-Fi IP exposing me)\n\nSo with a new account , obviously reddit knows my other banned alt account as well.\n\nIs there some Google setting to stop all apps from knowing who am I, not just reddit \n\n\n", "author": "Blk925ChickenRice", "created_time": "2024-12-18T15:37:27", "url": "https://reddit.com/r/privacy/comments/1hh470m/obviously_reddit_app_knows_who_am_i/", "upvotes": 0, "comments_count": 21, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh68zk", "title": "Waymo did 4M+ trips in 2024", "content": "", "author": "diplomat33", "created_time": "2024-12-18T17:08:05", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hh68zk/waymo_did_4m_trips_in_2024/", "upvotes": 196, "comments_count": 50, "sentiment": "neutral", "engagement_score": 296.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh835f", "title": "Google docs to blog!", "content": "Hi all, \n\nThe other day I heard some people complaining about setting up a blog using wordpress or other cms is a bit of a hustle. And they said that really wished they had something similar to google docs to write. I have the same feeling. Is there any saas that brings this google docs to blog feeling? ", "author": "R4nd0mm1l4n3s4", "created_time": "2024-12-18T18:27:22", "url": "https://reddit.com/r/startups/comments/1hh835f/google_docs_to_blog/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hh99b7", "title": "I Lost My Entire Timeline. If You Did Too, Let’s Work Together!", "content": "Hi everyone!\n\nMany of us, including myself, recently discovered that our Google Maps Timeline data has disappeared due to the migration from the cloud-based Timeline system to the on-device version. If you’ve been affected by this, I want to say how sorry I am that this happened to you. This is incredibly frustrating, and for so many of us, that data represented years of cherished memories.\n\nI wanted to share what I’ve done so far to try and get Google’s attention and encourage all of you to join in. If enough of us come together, we might just make a difference. I’ll go over everything in this post, but in an attempt to organize as much data as possible, I’ve also created a website to collect and organize this information. It allows users to submit their stories or share links to other websites where they’ve posted their story. You can check it out at [https://SaveGoogleTimeline.com](https://SaveGoogleTimeline.com) (backup available at https://SaveOurTimeline.com)\n\n# What I've Done So Far\n\n* **Contacted Google Account Support** After explaining the issue, I was advised to post in the Community Support forums & submit feedback through the Maps app.\n* **Made a Post in the Google Maps Community Forum** Unfortunately, it’s not public yet, but when it is, you’ll be able to view it [here](https://support.google.com/maps/thread/*********?hl=en). In the meantime, I can share the contents of that post in a comment if anyone wants to see it. I think posting here will be an important part of getting Google's attention. My post is slightly different from others I’ve seen in that I directly ask for my request to be forwarded to the Maps team for investigation.\n* **Sent a Certified Letter to Google Headquarters** Based on conversations with a handful of friends who work at Google and advice I received in the forums, I wrote a detailed letter explaining the issue, shared my personal story, and requested an investigation into possible recovery options. I sent the letters via certified mail, but I know this can be cost-prohibitive for some people, so standard mail is probably fine.\n   * I sent a total of three letters: one to Google LLC, one to the Google Maps Team, and one directly to Sundar Pichai. Again, this might be overkill, a single letter to Google LLC or the Google Maps Team is likely enough.\n\n# How You Can Help\n\nThis is a widespread issue, and the more people who raise their voices, the more likely Google is to notice and take action. Here’s how you can contribute:\n\n**1. Share Your Story and Raise Awareness**  \nThere are several ways you can share your experience and help amplify this issue:\n\n* **Submit Your Story on** [**SaveGoogleTimeline.com**](https://SaveGoogleTimeline.com)**:** If you don’t want to share a full story, you can simply add your name to the count of affected users or share links to external websites where you’ve posted your story. This will help me keep track of all public posts and build a comprehensive view of the issue. If you prefer not to use the website, [here is a Google form](https://forms.gle/tPkTcnroyM21L3BG6) that can be used as an alternative.\n* **Share Your Story in the Comments Here:** Feel free to share your story below! Every story helps build awareness and strengthens our collective voice. If you do, please consider submitting a link to your comment on the website to help keep everything organized.\n* **Post Publicly on Social Media:**\n   * Share your experience on platforms like Twitter, Facebook, or Instagram.\n   * Use hashtags like **#SaveGoogleTimeline** (We can workshop something better if you don't like this one) and tag Google or Google Maps in your posts to increase visibility. Here are their official handles:\n      * **Reddit:** u/Google\n      * **Twitter:** [Google](https://twitter.com/Google), [GoogleMaps](https://twitter.com/GoogleMaps)\n      * **Instagram:** [Google](https://instagram.com/Google), [GoogleMaps](https://instagram.com/GoogleMaps)\n      * **Facebook**: [Google](https://www.facebook.com/Google), [GoogleMaps](https://www.facebook.com/GoogleMaps)\n      * **TikTok**: [Google](https://www.tiktok.com/@google)\n\n**2. Post in the Community Support Forums**\n\n* Share your story and ask for help escalating the issue to the Maps team.\n* Tell them your Maps Timeline data is gone, explain how devastated you are, and ask them to investigate.\n* Be respectful and request that your issue be forwarded to the Maps team for review.\n\nSome product experts may say it’s your fault or that nothing can be done. **DON’T LET THEM GET UNDER YOUR SKIN**. Thank them for their response, and calmly reiterate your request to escalate the issue to the Maps team.\n\nIf you’d like, I can draft a template for Community Support posts or letters to Google, but personal stories are likely to be more impactful. Try to include details about your experience and how your data was lost.\n\n**3. Send a Letter to Google**\n\nExplain your situation in a respectful letter and mail it to **Google LLC**. I’m not posting the full address here to comply with Rule #1, but it’s easy to find via a quick Google search. If you’re having trouble locating it, you can also find it listed on [SaveGoogleTimeline.com](http://SaveGoogleTimeline.com)\n\nTell them your Timeline data is gone, explain how important the data is to you, tell them how devastating this is, and ask them to investigate.\n\nThis may seem like a small step, but Google does notice these letters. If enough of us take action, it can demonstrate the scope of the issue and encourage them to respond.\n\n**4. Submit Feedback Through the Maps App**\n\nWhile I don’t know how effective this will be, it was recommended by a Google employee, so it’s still worth doing. The more reports Google receives, the better. Instructions from Google on how to submit feedback can be found [here](https://support.google.com/maps/answer/3094045)\n\n# Why This Matters\n\nGoogle Maps Timeline has been an essential tool for so many of us, whether for tracking trips, revisiting memories, or keeping a personal log of life events. Losing this data is more than inconvenient; it’s heartbreaking.\n\nIf we can show Google how widespread and important this issue is, we have a much better chance of convincing them to take action.\n\n# What’s Next?\n\nOnce I have some stats on how many people have been affected by this, I plan to reach out to tech journalists and writers to ask them to cover the issue. Getting media coverage will help put additional pressure on Google to address this problem, but I need to hear from all of you first! Your stories and support are crucial to making this happen.\n\n# Let’s Do This Together\n\nThis is a massive issue, but it’s important we stay respectful and organized in our efforts. Together, we can make our voices heard and push for a solution.\n\nIf you have any questions, ideas or comments, feel free to share them here.\n\nIf you know someone who has lost their Timeline data or would be interested in supporting this cause, please share this post with them. The more people we can bring together, the stronger our voice will be.", "author": "lost_in_timelines", "created_time": "2024-12-18T19:17:08", "url": "https://reddit.com/r/GoogleMaps/comments/1hh99b7/i_lost_my_entire_timeline_if_you_did_too_lets/", "upvotes": 81, "comments_count": 46, "sentiment": "bullish", "engagement_score": 173.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hhes7t", "title": "I own a small family owned coffee drive thru & Dunkin moved in its 3rd locaton right next to me...", "content": "I am honestly a little shook up and angry. Does anyone have any advice on how I should approach this or what I should be feeling?", "author": "OctoIsaac", "created_time": "2024-12-18T23:19:38", "url": "https://reddit.com/r/smallbusiness/comments/1hhes7t/i_own_a_small_family_owned_coffee_drive_thru/", "upvotes": 940, "comments_count": 1040, "sentiment": "neutral", "engagement_score": 3020.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}]