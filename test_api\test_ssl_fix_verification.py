#!/usr/bin/env python3
"""
SSL修复验证测试脚本
验证 api.financialdatasets.ai 的SSL连接问题是否已完全解决
"""

import os
import sys
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_financial_metrics_api():
    """测试财务指标API"""
    print("=== 测试财务指标API ===")
    
    try:
        from src.tools.api import get_financial_metrics
        
        # 测试不同的股票代码
        test_cases = [
            ("AAPL", "2025-06-01", 5),
            ("MSFT", "2025-06-01", 3),
            ("GOOGL", "2025-06-01", 2),
        ]
        
        results = []
        
        for ticker, end_date, limit in test_cases:
            print(f"\n--- 测试 {ticker} ---")
            try:
                start_time = time.time()
                result = get_financial_metrics(ticker, end_date, limit=limit)
                end_time = time.time()
                
                if result:
                    print(f"✓ 成功获取 {len(result)} 条记录")
                    print(f"  最新记录日期: {result[0].report_period}")
                    print(f"  市值: {result[0].market_cap:,.0f}" if result[0].market_cap else "  市值: N/A")
                    print(f"  请求耗时: {end_time - start_time:.2f}秒")
                    results.append((ticker, True, len(result)))
                else:
                    print(f"✗ 返回空结果")
                    results.append((ticker, False, 0))
                    
            except Exception as e:
                print(f"✗ 错误: {e}")
                results.append((ticker, False, 0))
        
        return results
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return []

def test_other_api_functions():
    """测试其他API函数"""
    print("\n=== 测试其他API函数 ===")
    
    try:
        from src.tools.api import get_prices, get_market_cap
        
        # 测试价格数据
        print("\n--- 测试价格数据 ---")
        try:
            prices = get_prices("AAPL", "2025-05-01", "2025-06-01")
            if prices:
                print(f"✓ 获取到 {len(prices)} 条价格数据")
                print(f"  最新价格: ${prices[-1].close:.2f}")
            else:
                print("✗ 价格数据为空")
        except Exception as e:
            print(f"✗ 价格数据错误: {e}")
        
        # 测试市值数据
        print("\n--- 测试市值数据 ---")
        try:
            market_cap = get_market_cap("AAPL", "2025-06-01")
            if market_cap:
                print(f"✓ 获取市值: ${market_cap:,.0f}")
            else:
                print("✗ 市值数据为空")
        except Exception as e:
            print(f"✗ 市值数据错误: {e}")
            
    except ImportError as e:
        print(f"✗ 导入错误: {e}")

def test_ssl_robustness():
    """测试SSL连接的稳定性"""
    print("\n=== 测试SSL连接稳定性 ===")
    
    try:
        from src.tools.api import get_financial_metrics
        
        # 连续多次请求测试稳定性
        success_count = 0
        total_requests = 5
        
        for i in range(total_requests):
            try:
                print(f"请求 {i+1}/{total_requests}...", end=" ")
                result = get_financial_metrics("AAPL", "2025-06-01", limit=1)
                if result:
                    print("✓")
                    success_count += 1
                else:
                    print("✗ (空结果)")
            except Exception as e:
                print(f"✗ ({str(e)[:50]}...)")
            
            # 短暂延迟避免过于频繁的请求
            time.sleep(1)
        
        success_rate = (success_count / total_requests) * 100
        print(f"\n稳定性测试结果: {success_count}/{total_requests} ({success_rate:.1f}%)")
        
        return success_rate >= 80  # 80%以上成功率认为稳定
        
    except Exception as e:
        print(f"✗ 稳定性测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("\n=== 测试错误处理机制 ===")
    
    try:
        from src.tools.api import get_financial_metrics
        
        # 测试无效股票代码
        print("--- 测试无效股票代码 ---")
        try:
            result = get_financial_metrics("INVALID_TICKER", "2025-06-01", limit=1)
            if not result:
                print("✓ 正确处理无效股票代码（返回空结果）")
            else:
                print("⚠ 无效股票代码返回了数据")
        except Exception as e:
            print(f"✓ 正确抛出异常: {str(e)[:100]}...")
        
        # 测试无效日期
        print("\n--- 测试无效日期 ---")
        try:
            result = get_financial_metrics("AAPL", "2030-12-31", limit=1)
            if not result:
                print("✓ 正确处理未来日期（返回空结果）")
            else:
                print("⚠ 未来日期返回了数据")
        except Exception as e:
            print(f"✓ 正确抛出异常: {str(e)[:100]}...")
            
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")

def main():
    """主测试函数"""
    print("开始SSL修复验证测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # 检查环境
    if os.environ.get("FINANCIAL_DATASETS_API_KEY"):
        print("✓ API密钥已配置")
    else:
        print("⚠ 未找到API密钥，某些测试可能失败")
    
    # 运行各项测试
    test_results = []
    
    # 1. 财务指标API测试
    financial_results = test_financial_metrics_api()
    if financial_results:
        success_count = sum(1 for _, success, _ in financial_results if success)
        test_results.append(("财务指标API", success_count == len(financial_results)))
    else:
        test_results.append(("财务指标API", False))
    
    # 2. 其他API函数测试
    test_other_api_functions()
    
    # 3. SSL稳定性测试
    stability_result = test_ssl_robustness()
    test_results.append(("SSL连接稳定性", stability_result))
    
    # 4. 错误处理测试
    test_error_handling()
    
    # 输出总结
    print("\n" + "="*60)
    print("测试结果总结:")
    print("="*60)
    
    for test_name, success in test_results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:<20} {status}")
    
    success_count = sum(1 for _, success in test_results if success)
    print(f"\n总计: {success_count}/{len(test_results)} 项核心测试通过")
    
    if success_count == len(test_results):
        print("\n🎉 所有核心测试都通过了！")
        print("✅ SSL连接问题已完全解决")
        print("✅ API函数工作正常")
        print("✅ 错误处理机制完善")
        print("\n建议:")
        print("- 可以正常使用财务数据API")
        print("- 系统已具备良好的错误恢复能力")
        print("- SSL连接稳定可靠")
    elif success_count > 0:
        print("\n⚠ 部分测试通过，系统基本可用")
        print("建议检查失败的测试项目")
    else:
        print("\n❌ 测试失败，需要进一步调试")
    
    print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
