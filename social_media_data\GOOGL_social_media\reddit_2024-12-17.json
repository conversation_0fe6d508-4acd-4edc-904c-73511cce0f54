[{"platform": "reddit", "post_id": "reddit_1hfyb28", "title": "Waymo to begin testing in Tokyo, its first international destination", "content": "", "author": "<PERSON><PERSON><PERSON>", "created_time": "2024-12-17T00:38:09", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hfyb28/waymo_to_begin_testing_in_tokyo_its_first/", "upvotes": 332, "comments_count": 36, "sentiment": "neutral", "engagement_score": 404.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hfz4ne", "title": "<PERSON><PERSON> and <PERSON> jointly create 3D immersive world, Google/WiMi innovation leads holographic virtuality - Newstrail", "content": "", "author": "Gas<PERSON>den", "created_time": "2024-12-17T01:17:35", "url": "https://reddit.com/r/Economics/comments/1hfz4ne/meta_and_cameron_jointly_create_3d_immersive/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "Economics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hg179q", "title": "Everything here is 100% generated w/ Google Veo 2", "content": "", "author": "SharpCartographer831", "created_time": "2024-12-17T03:05:33", "url": "https://reddit.com/r/singularity/comments/1hg179q/everything_here_is_100_generated_w_google_veo_2/", "upvotes": 1393, "comments_count": 242, "sentiment": "neutral", "engagement_score": 1877.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hg553u", "title": "Can I do web development on a low-spec work PC using a USB setup?", "content": "\nSo, I have a lot of free time at my current job (receptionist). Honestly, the pay is great, and since I’m still in college, I feel really lucky to have a job that’s both chill and well-paid. This is pretty rare in my country (Argentina).\n\nI do web development, but I haven’t been able to find anything in the field yet. I was thinking of using my free time at work to code, but the work PC has really low specs, and it’s even worse with the job’s programs running in the background.\n\nThe specs are:\n\t•\tIntel Core i3 7020U (2.3GHz)\n\t•\t4GB RAM (usually at 90% usage due to work programs).\n\nI was wondering if it’s possible to set up everything on a USB drive using portable versions of the tools I need. Do you think I could work on some MERN stack projects or any kind of web development with this setup?", "author": "<PERSON><PERSON>", "created_time": "2024-12-17T07:04:25", "url": "https://reddit.com/r/webdev/comments/1hg553u/can_i_do_web_development_on_a_lowspec_work_pc/", "upvotes": 0, "comments_count": 45, "sentiment": "neutral", "engagement_score": 90.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hg5yop", "title": "Advice for learning hands-on Meta/Google Ads management while managing a £10K/month budget", "content": "Hi Everyone! I’m a freelance consultant with 10+ years in eCommerce and digital marketing based in the UK, primarily focused on strategy, project management, and KPIs. I recently finished a contract for a brand I love as Ecommerce manager. Now, they need me to directly manage and execute their Meta and Google Ads campaigns on a tight £10K/month budget so doable, considering we operate in the Luxury fashion space.\n\nWhile I have a strong understanding of Meta and Google Ads from a strategic perspective (working alongside agencies and freelancers), I lack deep operational experience. I need to quickly learn the specialist skills:\n\n* Launching and optimizing campaigns.\n* Building/editing audiences.\n* Testing new creatives and strategies.\n\nMy goal is to deliver results as both a hands-on specialist and strategic advisor.\n\nWhat’s the best way to upskill quickly on execution tasks? Are there tools, resources, or workflows you’d recommend to get operationally strong in Meta and Google Ads?\n\nAny advice would be hugely appreciated, thanks!", "author": "Previous_Delivery_41", "created_time": "2024-12-17T08:05:19", "url": "https://reddit.com/r/DigitalMarketing/comments/1hg5yop/advice_for_learning_handson_metagoogle_ads/", "upvotes": 23, "comments_count": 26, "sentiment": "bullish", "engagement_score": 75.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hg6kb0", "title": "Question for Data Analytics and Industry Professionals RE: \"The User Experience\" (From: a user)", "content": "So, serious question - do any of you work, or know anyone who works on UIs/Databases/User Experience designs specifically for one of the various streaming giants: Netflix, MAX, Prime, Disney+, Hulu?  \n\n\nI apologize first-hand if this sounds overly-angry, but this problem has gotten EXPONENTIALLY WORSE over the past 7 years or so, in particular and will only be exacerbated by B<PERSON>LLSHIT AI TRENDS.   \n\n\nBecause I HAVE A NITPICK.   \n\n\nWhy do you make your service impossible to index properly to the average user? Occasionally, I may be able to sort a genre or sub-genre alphabetically, such as with MAX; however, the default sort is ALWAYS a smattered, mish-mash of \"here's shit you're likely to click on\" or \"more recent/promoted\" results...JUST LIKE THAT HORSE-SHIT THAT GOOGLE'S been pulling for the past five years.   \n  \nLook, I just want to know exactly WHAT IS THERE, and I DO NOT want it appearing in multiple categories thereby increasing the time it takes to scroll through each category, NOR do I want a NEVER ENDING category based on some bullshit, algorithm that tries to generate results on the fly via applied \\[TAGS\\], popularity, or god KNOWS however other \"hidden metrics,\" that we as users cannot see, nor control. (as is the current trend)  \n  \nFrom a design/user-experience standpoint, IT'S A GODDAMNED NIGHTMARE. IT'S FUCKING STUPID.    \n  \nI realize this may save the labor of having to curate and sanitize your records, BUT IT IS MASSIVELY UNFRIENDLY TO YOUR ENTIRE USERBASE.   \n  \nLook, here's what I'm talking about. I walk into a Library Today, or a Blockbuster Video a decade ago, titles aren't spontaneously being shuffled from one shelf to the other, aside from NEW RELEASE to OLD SHIT. The STATIC LOCATION of said item helps ME, the USER, keep track of what's available and what's no longer available. It makes BROWSINGS a MILLION TIMES EASIER, not to mention more satisfying.   \n  \nThis is WHY people LOVED GOING TO THE STORE. The sort MADE SENSE, instead of whatever logarithmic HORSE SHIT you software developers are fucking with today.   \n  \nAnd it's not just limited to the Streaming services. This is what Amazon does, this is what STEAM does, hell - this is what FACEBOOK has been doing with your posts, instead of just giving a \"TRULY\" chronological timeline of your contacts.   \n  \nI   \nDO  \nNOT  \nNEED/WANT    \nYOU   \nMAKING   \nDECISIONS   \nFOR   \nME   \n  \nFUCKING FIX IT. The amount of autonomy that's been STOLEN from users by the modern data informatics and analytics paradigm because of current data trends is FUCKING CRIMINAL.   \n  \nIt intentionally obfuscates \"what is actually going on\" to ANYONE who doesn't have direct access to the individual data-records, and is ethically OPENLY HOSTILE TO THE END USER.   \n  \nIn other words, YOU NEED TO MAKE IT POSSIBLE FOR ME (THE USER and YOUR CUSTOMER) TO DO A \"REAL\" not \"LOGARITHMIC\" search query of your records.   ", "author": "divine_shadow", "created_time": "2024-12-17T08:53:09", "url": "https://reddit.com/r/analytics/comments/1hg6kb0/question_for_data_analytics_and_industry/", "upvotes": 2, "comments_count": 11, "sentiment": "bullish", "engagement_score": 24.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hg7nck", "title": "What is a best way to start with Google Ads?", "content": "Hey! I'm a SaaS founder, and I want to try Google Ads. How do you recommend starting? Should I request a consultation from Google? As I understand, they provide that service. What is the best way to get free credits? Thank you!\n\n", "author": "One_Jump_6678", "created_time": "2024-12-17T10:16:26", "url": "https://reddit.com/r/adwords/comments/1hg7nck/what_is_a_best_way_to_start_with_google_ads/", "upvotes": 8, "comments_count": 15, "sentiment": "neutral", "engagement_score": 38.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hgch78", "title": "Is Mr <PERSON> still the bad guy?", "content": "The other day my brother was watching Mr beast and i was like dont watch he does shady stuff and he was like wasnt it all cleared ?\n\nThen i said still dont watch him he is a bad youtuber ,  then he asked me why and i didnt have an answer for that i have stopped watching mr beast for like 4 months and i ponder why i did it like i watched a bunch of vids and now hate him but idk if he is still bad is he ? if so why not?\n\n  \nfor context like how dont consume nestle products since they loot north west african countries gave women green baby powder for their children till the couldnt produce their own milk and raised the prices while hogging over a spring for bottled water while i belive california was in drought i am not sure which one it was.\n\nSo for Mr beast it could be what?", "author": "Netscapevo01", "created_time": "2024-12-17T15:00:55", "url": "https://reddit.com/r/youtube/comments/1hgch78/is_mr_beast_still_the_bad_guy/", "upvotes": 1, "comments_count": 50, "sentiment": "neutral", "engagement_score": 101.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hghf1a", "title": "Discovered 'SOS' signal on GoogleMaps Randomly", "content": "Randomly discovered a SOS signal on GoogleMaps when searching for some vacation spots. I don't know if it was intentional for the purpose of people using Google Earth but it's very interesting.\n\n0°39'34.9\"S 73°12'57.1\"E - Cordinates  \n[https://www.google.com/maps/place/0%C2%B039'34.9%22S+73%C2%B012'57.1%22E/@-0.6597017,73.2152133,284m/data=!3m2!1e3!4b1!4m4!3m3!8m2!3d-0.659703!4d73.215857?entry=ttu&g\\_ep=EgoyMDI0MTIxMS4wIKXMDSoASAFQAw%3D%3D](https://www.google.com/maps/place/0%C2%B039'34.9%22S+73%C2%B012'57.1%22E/@-0.6597017,73.2152133,284m/data=!3m2!1e3!4b1!4m4!3m3!8m2!3d-0.659703!4d73.215857?entry=ttu&g_ep=EgoyMDI0MTIxMS4wIKXMDSoASAFQAw%3D%3D)", "author": "Lost-Hat8728", "created_time": "2024-12-17T18:36:26", "url": "https://reddit.com/r/GoogleMaps/comments/1hghf1a/discovered_sos_signal_on_googlemaps_randomly/", "upvotes": 87, "comments_count": 5, "sentiment": "neutral", "engagement_score": 97.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}]