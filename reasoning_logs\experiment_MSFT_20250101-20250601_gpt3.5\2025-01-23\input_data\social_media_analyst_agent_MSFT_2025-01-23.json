{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-01-23", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Microsoft's business development chief <PERSON> resigns", "content": "", "created_time": "2025-01-23T03:18:45", "platform": "reddit", "sentiment": "neutral", "engagement_score": 445.0, "upvotes": 387, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1i7u3c0/microsofts_business_development_chief_ch<PERSON>_young/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Remote Positions - 50 Mile Hub Radius?", "content": "Straight to the point question I have here. I live in southern FL and was looking at quite a few Microsoft positions that state up to 100% remote, but sometimes in the job descriptions I'm seeing you have to be within a 50 mile radius of a hub or it just doesn't say it at all. The jobs I'm gearing towards (Procurement/Supply Chain) are stating relocation will be supported to be within 50 mile radius. I have a family and pretty much have no intention of wanting to move anywhere, is it even worth me applying to these positions if this is actually true? I'm seeing on Reddit some people aren't even actually within that radius and have negotiated with the hiring manager? ", "created_time": "2025-01-23T04:43:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "TrumpBrahs", "url": "https://reddit.com/r/microsoft/comments/1i7vmsn/remote_positions_50_mile_hub_radius/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Copilot integration with Microsoft word is now free?", "content": "I have notice when Im using microsoft word, the copilot icon is already popping up. But i havent used it yet. Does anyone is using this feature in microsoft word?", "created_time": "2025-01-23T05:30:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 13.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "SilentAdvocate2023", "url": "https://reddit.com/r/microsoft/comments/1i7webz/copilot_integration_with_microsoft_word_is_now/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Remote employee question", "content": "Hi! I recently was offered a job at Microsoft and my background check just completed, so I haven’t started yet. I’m not located in WA and my job is 100% remote. Im just curious—if there is a Microsoft office near me, can I go in and use it on random days if I want? Or is it only for those assigned to teams that work out of those buildings?\n\nI’ve been remote since 2018 and it can get a little isolating/stir crazy, so would love the option to go somewhere, even for a few hours.\n\nCheers! ", "created_time": "2025-01-23T07:02:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 56.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "Katinthehat02", "url": "https://reddit.com/r/microsoft/comments/1i7xrxw/remote_employee_question/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Cheaper hidden Office offer without Copilot", "content": "I am (was, actually) a Microsoft 365 Family subscriber. A few days ago Copilot started showing up integrated into my Office apps. I am not a big fan of Microsoft shoving and trying to upsell Copilot everywhere, but I have grown accostumed to it and I thought no more of the subject. However, a few days after that, when browsing through my Microsoft account settings, I accidentally discovered that for the \"privilege\" of having Copilot unwillingly shoved into my face, my subscription is now 30% (!!!) more expensive. Fortunately I was on an annual subscription and thus rebilling was still a few months away!\n\nAfter pondering for a while, I decided to eradicate the problem at the source and canceled my subscription. I discovered, and want to share with potential users facing the same dilemma, that when you are canceling your subscription, you are presented with a hidden, 30% cheaper M365 subscription without Copilot. That offer is not accessible anywhere outside the cancelation screen. \n\n This \"hidden offer when canceling\" seems right out of the playbook of unreputable adult content merchants rather than reputable software vendors. It was a bit too shady for me and so I've moved on to a non-Microsoft office product, but for those interested in getting the non-Copilot version of Office at the previous price, take note of the existence of this hidden offer.  \n\n In my opinion, Microsoft COULD have been user friendly when selling these features to users. For example: \n\n* \"We have integrated Copilot into Office. Do you want to try it out? Click here to activate a 15-day trial. If you like it, you can enable the Copilot add-on for your subscription for a 30% price increase. You can also disable this add-on in the future at any time and it will take effect at the next time your subscription renews.\" \n* New subscribers of M365 can choose to subscribe with or without the Copilot add-on. \n\nWhat  Microsoft actually chose to do: \n\n* Everyone gets a silent 30% price increase. Users do not get notified of the price increase. There is no explanation of the reason behind the price increase (Copilot integration). \n* Copilot gets forcefully shoved in everyone's face inside Office. (The OneNote integration is specially disgusting, the Copilot icon literally follows your cursor around everywhere.) There is no way to disable it except for using a global setting for \"connected experiences\" - which disables a lot more than just Copilot. \n* They implemented a hidden offer without Copilot integration and the associated price increase that is kept secret and only presented to users when canceling their subscription as a last resort customer retention tactic - because they KNEW perfectly well that some subscribers would cancel after such a gigantic price increase. ", "created_time": "2025-01-23T08:54:19", "platform": "reddit", "sentiment": "bearish", "engagement_score": 119.0, "upvotes": 35, "num_comments": 0, "subreddit": "unknown", "author": "forger-eight", "url": "https://reddit.com/r/microsoft/comments/1i7z7wu/cheaper_hidden_office_offer_without_copilot/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Amazon UK Boss Says Mr <PERSON> Vs The Post Office Wouldn't Work On Prime - Deadline", "content": "", "created_time": "2025-01-23T14:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1i83ysc/amazon_uk_boss_says_mr_bates_vs_the_post_office/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Quality of Microsoft Designer", "content": "Who at Microsoft is responsible for product quality standards? The generated images in Microsoft Designer unfortunately remain unusable and there has been no improvement in the last two months.\n\nFor example, the PR16 is unable to draw an even line, let alone handle text. Lighting is also handled incorrectly: in some scenes, characters' eyes are barely visible, and the overall colour contrast causes visual discomfort. The generation of objects, such as the moon, leaves much to be desired.\n\nNevertheless, the home page continues to showcase the quality of the images, which appear to be only available on the PR13 model. I would like to know who is responsible for quality control of this product, and are there any fixes or updates planned in the near future?", "created_time": "2025-01-22T06:43:32", "platform": "reddit", "sentiment": "bullish", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "MINIVV", "url": "https://reddit.com/r/microsoft/comments/1i7565v/quality_of_microsoft_designer/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "Custom hotkey/keyboard shortcut that returns a text string", "content": "I'm making this post for anyone in the future looking for a solution to this feature. If you already know about the solution please ignore the following but it took me days of research to finally find it with a lot of forums leading to convoluted dead ends.\n\n\nIn short if you want to set up a keyboard shortcut combination that inserts a predetermined string of characters (eg. \"@gmail.com\") you should install from the Microsoft Store \"Microsoft PowerToys\" & set up the combinations in the Keyboard Manager>Remap a shortcut.\n", "created_time": "2025-01-22T09:35:05", "platform": "reddit", "sentiment": "bearish", "engagement_score": 6.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Boflator", "url": "https://reddit.com/r/microsoft/comments/1i77ge6/custom_hotkeykeyboard_shortcut_that_returns_a/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "I swear, MS Word is some kind of eldritch rage-energy feeding device.", "content": "Trying to accomplish the simplest of tasks (in this instance, separate page numbering from front matter to the actual body of work) yields anguish, despair, and little else. \n\nI've been poring over no less than five separate help articles for the past three hours. As frustration grows a solution seemingly presents itself only to yield more issues. \n\nI FINALLY was able to start numbering the main body with separate numbers only to have the \"Chapter One\" 1st page be labeled as \"3.\" Wat. \n\nSo I spent 45 minutes trying to find the appropriate place to insert the \"Section Break\" and eventually got the Chapter One 1st page to be labeled \"one\"...but now page two is \"3\"!!!!! From then on it continues normally. \n\nTell me that Microsoft DOESN'T have some kind of deal worked out with book formatting peeps as they've made this thing so incredibly fucking frustrating to use - and I play like, REALLY intricate games. Well, Rimworld. and Factorio. and Terra Invicta. Semi-complicated. ", "created_time": "2025-01-22T09:48:08", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "DescriptionOwn6184", "url": "https://reddit.com/r/microsoft/comments/1i77mh5/i_swear_ms_word_is_some_kind_of_eldritch/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "Rewrite windows", "content": "From win 7 and forward Windows has been more and more \"fat\", eats more ram, uses more cpu...you guys need to rethink the Windows code...build an ai to help you 👌. I have several computers and some of them is on older hardware..if i put any distro of Linux on them they are snappy again. But i need Windows. I believe that there are billions of older computers that would need a new version of windows that is secure and up to date, that would work on older hardware.", "created_time": "2025-01-22T11:43:24", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "kallstrom_74", "url": "https://reddit.com/r/microsoft/comments/1i7991o/rewrite_windows/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "Remove Copilot nonsense", "content": "Hi,\n\nI was trying to quickly respond to an email and get on with my day. Unfortunately, it seems to be impossible to do so without MS jamming Copilot nonsense into my face at the start of every line.\n\nStop shoving new \"features\" down my throat, please, and simply add an obvious way to disable new features. I have zero interest in AI, and would rather cancel my Office 365 (or is it Copilot, now?) membership than continue to pay for the constant inconvenience of having half-baked Copilot junk shoved in my face when trying to send a single-paragraph email to a family member.\n\nI guess the fact that I'm unable to include an image displaying the issue is a testament to how beloved MS is over here?\n\nGoing into system settings and unticking something under the taskbar settings, as I saw mentioned somewhere, didn't do it. Has anyone been able to disable these Copilot annoyances, particularly for Outlook/Office? And am I the only one annoyed by the way it's jammed into software that worked fine for decades without it?", "created_time": "2025-01-22T11:50:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 44.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "straef", "url": "https://reddit.com/r/microsoft/comments/1i79cvv/remove_copilot_nonsense/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "Software Engineer II - AI/ML", "content": "Hi,\nI’ve just received an email with a Codility coding assignment (1 question - 40 minutes) for the role above. It has no expiration date, and it’s been a while since I did leetcode. For Microsoft, which patterns should I focus on preparing? And how much time should I give myself at minimum?", "created_time": "2025-01-22T14:43:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 21.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Adventurous-Fee3087", "url": "https://reddit.com/r/microsoft/comments/1i7cmm6/software_engineer_ii_aiml/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "M365: I do not understand anything", "content": "Hi, \n\nI am trying to buy a M365 and, unfortunately, I do not understand anything from the limitless pages with plans and FAQs. \n\nEnterprise is too much for me, business standard or personal seem alright. \n\nI’ve talked to two Microsoft agents, the first conversation being abysmal (no direct responses, bad copilot replies; just horrendous). \n\nA. General\n\nOne of the agents told me only certain Enterprise accounts can opt out of training the model (through EDP—Enterprise Data Protection).\n\nA.1. Is that information correct? \n\nA.1.1. If not, what other plans would allow me to do that?\n\nIf this is correct, I would be forced to buy a Google subscription. \n\nB. M365 Personal vs. M365 Business Standard \n\nB.1. Designer is only available on M365 Personal?\n\nB.2. What are the Copilot differences between the two plans? \n\nB.3. Am I protected against a phishing, ransomware and other cyber threats in any of these 2 plans? \n\nB.4. Do I get a custom e-mail address in any of the plans? \n\nB.5. Are my messages and work encrypted in any of these plans? \n\nB.6. If I buy M364 Business Standard WITHOUT Teams, are there any other differences?\n\nC. Copilot Features in Personal and Business Plans vs. separate license for M365 Copilot ($30)\n\nC.1. What are the difference between Copilot M365 Personal and Copilot M365 Business Standard? \n\nC.2. What are the differences between Copilot M364 Personal and M365 Copilot ($30)?\n\nC.3. What are the differences between Copilot M364 Business Standard and M365 Copilot ($30)?\n\nD. Other questions\n\nD.1. Am I able to opt out from the Copilot preinstalled in Windows? I do not have a M365 account, but I have a W11 Pro license. \n\nI really don’t understand anything. \n\nThank you! ", "created_time": "2025-01-22T17:37:54", "platform": "reddit", "sentiment": "bullish", "engagement_score": 44.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "xYoKx", "url": "https://reddit.com/r/microsoft/comments/1i7gsxt/m365_i_do_not_understand_anything/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "Microsoft will automatically keep you signed in to your account starting in February", "content": "", "created_time": "2025-01-22T18:42:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 211.0, "upvotes": 171, "num_comments": 0, "subreddit": "unknown", "author": "BippityBoppityWhoops", "url": "https://reddit.com/r/microsoft/comments/1i7ieir/microsoft_will_automatically_keep_you_signed_in/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "Microsoft 365 Personal goes up in price by $30", "content": "Just received this email...\n\n*Thank you for being a valued Microsoft 365 subscriber. To reflect the value we’ve added over the past decade, address rising costs, and enable us to continue delivering new innovations, we’re increasing the price of your subscription.*\n\n*Effective February 14, 2025, the price for* ***Microsoft 365 Personal*** *subscriptions will increase from* ***USD 69.99******^(\\*)*** ***per year*** *to* ***USD 99.99******^(\\*)*** ***per year****.* *To continue with the new price, no action is needed—your payment method on file will be automatically charged. To make changes to your subscription plan or turn off recurring billing, visit your* [*Microsoft account*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********) *at least two days before your next billing date.*\n\n*By maintaining your subscription, you’ll enjoy secure cloud storage, advanced security for your data and devices, and cutting-edge AI-powered features, along with all your other* [*subscription benefits*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********)*. Thank you for choosing Microsoft.*\n\n*Learn more about how to manage your subscription, including* [*how to cancel*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********) *and* [*switch your subscription*](https://t2.infomails.microsoft.com/r/?id=hec627d5,********,********)*.*", "created_time": "2025-01-22T19:35:30", "platform": "reddit", "sentiment": "bullish", "engagement_score": 70.0, "upvotes": 26, "num_comments": 0, "subreddit": "unknown", "author": "Perry7609", "url": "https://reddit.com/r/microsoft/comments/1i7jrek/microsoft_365_personal_goes_up_in_price_by_30/", "ticker": "MSFT", "date": "2025-01-22"}, {"title": "What is the cooling period to reapply for a position in Microsoft?", "content": "I had applied for a position in Microsoft a couple of months ago and my application was rejected. Since then, I've upskilled myself and noticed that the position is still open. When I tried to apply to it I get a message \"you've already applied for this job\"\n\nIs there a way I can reapply to that job with an updated resume? If not, how long do I have to wait to reapply?", "created_time": "2025-01-21T00:47:10", "platform": "reddit", "sentiment": "bullish", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "prv<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i667b9/what_is_the_cooling_period_to_reapply_for_a/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Microsoft brand pocket knife passed down from generation to generation, yes that exists ", "content": "Today, January 20, 2025, I received a gift from my father, passed from father to son, yes, I received a Microsoft brand pocket knife, I didn't understand why it was Microsoft brand, I searched and nothing was found, no There are records, but it is very suspicious ", "created_time": "2025-01-21T02:33:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i68d6n/microsoft_brand_pocket_knife_passed_down_from/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Xbox sees red with latest Cipher Special Edition wireless controller Revealed", "content": "", "created_time": "2025-01-21T17:55:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "great_maccc", "url": "https://reddit.com/r/microsoft/comments/1i6om5g/xbox_sees_red_with_latest_cipher_special_edition/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Supplier Emissions Reduction Planning PAIN POINTS", "content": "Any Microsoft Suppliers gaining traction with pushback on Microsoft's SCOC initiative of carbon emissions reduction planning at the service line level?\n\nOur organization is emissions resilient without operational control over our building leases/energy usage, and only provide staffing or consulting individuals with an unimaged laptop, without operational control over commute, remote work, or business travel. \n\nThey have refused time and again, to install a reasonable threshold by which to measure relevance and impact of those suppliers with annual emissions totals, comparable to a day or two of Microsoft operations.\n\nQUESTION: How are you and your organization, if similar, working to meet this new SCOC requirement?", "created_time": "2025-01-21T18:35:44", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Successful_Charge_73", "url": "https://reddit.com/r/microsoft/comments/1i6pmlz/supplier_emissions_reduction_planning_pain_points/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Question about Outlook for Windows and Copilot", "content": "After finally being forced off the old Mail app, I was begrudgingly looking at the new Outlook for Windows and saw it had Copilot features. I don't want Microsoft training Copilot on my emails but as far as I am aware the free version of outlook doesn't use Copilot, can I assume that it will not be active when using the Outlook app as well?", "created_time": "2025-01-21T19:40:31", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "DogWith<PERSON>a<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i6r7va/question_about_outlook_for_windows_and_copilot/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "CSAM Microsoft", "content": "Hi everyone, I was suppose to interview for for a CSAM role at Microsoft but I got an email today saying \n\nWe hope you are doing well. We wanted to reach out and inform you that, due to business needs, we will need to cancel the screen event 1/22 - 1/24/2025, Microsoft Customer Success Account Manager - First Round Teams Interview for the () location that you are a part of this week. We apologize for the inconvenience of this change and appreciate you considering Microsoft for your career next step.\nWe will get back to you as soon as we have clarity regarding timing and next steps. Thank you for your time and consideration.\n\nDid this happen to anyone else\n\n", "created_time": "2025-01-21T22:29:33", "platform": "reddit", "sentiment": "neutral", "engagement_score": 61.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Horror-Context-237", "url": "https://reddit.com/r/microsoft/comments/1i6vbgd/csam_microsoft/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Stargate without Microsoft?", "content": "President trump is announcing with <PERSON> @ OpenAI and <PERSON>@Oracle as well as <PERSON><PERSON> @Softbank.\n\nWhere is Microsoft here? Seems strange Oracle is leading a charge here", "created_time": "2025-01-21T22:39:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 32.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "WittinglyWombat", "url": "https://reddit.com/r/microsoft/comments/1i6vk11/stargate_without_microsoft/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Updates?", "content": "What is Microsoft doing with the updates? I just had a pop-up saying would you like to restart now to finish updating. I click no. Started watching a movie and my computer started updating...\n\nMicrosoft really lost its touch with update lately. What's going on??", "created_time": "2025-01-21T22:44:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 26.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "mlandry2011", "url": "https://reddit.com/r/microsoft/comments/1i6vnyb/updates/", "ticker": "MSFT", "date": "2025-01-21"}, {"title": "Hot Take: Copilot shine as a search engine tool.", "content": "Copilot become really powerful if you treat it like a search engine tool. Because it does bing searches for you, you can ask something and it will compile, and summarized the result\n\nIt help alot when I do plenty of tinkering. It also help that it doesn't' hallucinate as much like ChatGPT, so you can reasonably rely on the answer being decently accurate.\n\n  \nBecause of that, I would not be oppose for Copilot to be integrated to Office, in all case, it would be a much more powerful Clippy, that actually talks when you want to talk to them.", "created_time": "2025-01-20T02:07:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 105.0, "upvotes": 51, "num_comments": 0, "subreddit": "unknown", "author": "AsrielPlay52", "url": "https://reddit.com/r/microsoft/comments/1i5fjfa/hot_take_copilot_shine_as_a_search_engine_tool/", "ticker": "MSFT", "date": "2025-01-20"}, {"title": "What are the benefits for Microsoft Canada?", "content": "Just got an offer to work at Microsoft Canada. <PERSON><PERSON><PERSON><PERSON> only gave some information about health plan and vacation policy. \n\nFolks who work at Microsoft Canada, what are the benefits you would highlight for me? I am too excited but I don’t want to shoot a lot of questions about benefits until I am officially in lol (doing background check rn). I found a lot of info about US benefits, but not a lot about Canada.\n\nSome questions I have:\nDo all Microsoft employees get xbox game pass, or just the gaming employees?\nHow’s the pat/mat leave? How much top-up, for how long?\nIs there any childcare benefits for Canada?\nIs there any gym/well being benefits or an access to gym?", "created_time": "2025-01-20T04:45:49", "platform": "reddit", "sentiment": "bullish", "engagement_score": 28.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Emergency-Cup1360", "url": "https://reddit.com/r/microsoft/comments/1i5idys/what_are_the_benefits_for_microsoft_canada/", "ticker": "MSFT", "date": "2025-01-20"}, {"title": "HireRight is a nightmare", "content": "I’m onboarding at Microsoft, and HireRight flagged my education as **“Unable to Verify”** even though I provided official transcripts and directed them to NSCH (the only available source since the school is closed) for verification.\n\nI didn’t graduate from the Art Institute (and never claimed to—even on HireRight’s initial questionnaire). Education wasn’t discussed during the interview process, and the job posting states that “equivalent experience” is accepted for the role.\n\nDespite this, HireRight marked my background check as **“Completed”** with a yellow flag under the education section. Everything else came back clear, but this one issue has me worried about how it might affect my onboarding.\n\nHas anyone dealt with a similar situation? Did it cause any delays or issues? Would love to hear your experiences.", "created_time": "2025-01-20T17:22:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 166.0, "upvotes": 106, "num_comments": 0, "subreddit": "unknown", "author": "Whole-Finish-6905", "url": "https://reddit.com/r/microsoft/comments/1i5vhf0/hireright_is_a_nightmare/", "ticker": "MSFT", "date": "2025-01-20"}, {"title": "Entry level business analyst", "content": "Hey guys, looking to see if anyone knows how the interview process is for the entry level BA position at Microsoft (final round specifics)? Any experiences, things I should prepare for, anything of the sort? Would love any advice I can get, thanks.\n", "created_time": "2025-01-19T03:23:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "b<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i4ozyv/entry_level_business_analyst/", "ticker": "MSFT", "date": "2025-01-19"}, {"title": "Reduced-rate Microsoft 365 without Copilot", "content": "Hey Microsoft. It's cool and all that you're playing with AI, but I DON'T WANT IT, and I resent that you're increasing the annual cost of Microsoft 365 to pay for the obscene investments into massive world-killing server farms. Please... give us a cheaper subscription option that locks out the AI functions. I don't want them. ", "created_time": "2025-01-19T04:22:07", "platform": "reddit", "sentiment": "neutral", "engagement_score": 252.0, "upvotes": 138, "num_comments": 0, "subreddit": "unknown", "author": "Phlucious", "url": "https://reddit.com/r/microsoft/comments/1i4q5ej/reducedrate_microsoft_365_without_copilot/", "ticker": "MSFT", "date": "2025-01-19"}, {"title": "Microsoft Relocation", "content": "Hi there, \n\nDo Microsoft offer Relocation fees/bonus when you are hired and need to relocate to another town ?\nOne of the HR screeening questions was if I would be able to relocate on my own. I said YES but Im wondering if it’s common for Microsoft new hires.\n\nThank you", "created_time": "2025-01-18T05:12:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 71.0, "upvotes": 35, "num_comments": 0, "subreddit": "unknown", "author": "Ok_Present_8445", "url": "https://reddit.com/r/microsoft/comments/1i40czw/microsoft_relocation/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "What to do with vested RSUs?", "content": "Are you holding onto those individual stocks? Or are you selling and diversifying in some ETF like VOO, VTI QQQ. \n\nI feel like if you were to invest in ETFs while still holding onto $MSFT or GOOG AMZN etc it would be redundant. Thoughts on how others have carried this situation out? I’m still holding onto my vested RSUs and thinking if I should diversify into my VOO portfolio? ", "created_time": "2025-01-18T14:11:15", "platform": "reddit", "sentiment": "bearish", "engagement_score": 130.0, "upvotes": 48, "num_comments": 0, "subreddit": "unknown", "author": "Ok-Intention-384", "url": "https://reddit.com/r/microsoft/comments/1i48bp2/what_to_do_with_vested_rsus/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Microsoft AutoGen v0.4: A turning point toward more intelligent AI agents for enterprise developers", "content": "", "created_time": "2025-01-18T19:06:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Ethan<PERSON>ill<PERSON>s_TG", "url": "https://reddit.com/r/microsoft/comments/1i4eraf/microsoft_autogen_v04_a_turning_point_toward_more/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Your sign-in experience is changing", "content": "\"The web browser sign-in experience is changing when you sign in to any product or service using your Microsoft account. Starting in February 2025, ***you will stay signed in automatically*** unless you sign out or use private browsing.\" [Avoid staying signed in on a public computer](https://support.microsoft.com/en-us/account-billing/avoid-staying-signed-in-on-a-public-computer-d3f1448b-64b9-4b35-89d0-ce56715c6756)\n\nAm I the only one that thinks this change is moronic and will probably end badly for quite a few people?  ", "created_time": "2025-01-18T22:17:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 70.0, "upvotes": 20, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i4iyq1/your_signin_experience_is_changing/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Microsoft 365 Business code stack", "content": "Does anyone know how long M365 Business Standard product keys can be stacked for?\n\n(I'm aware you can stack up to 5 years of M365 personal subscriptions using product keys, but do not want to assume the same for business as it's a different platform)", "created_time": "2025-01-18T22:29:04", "platform": "reddit", "sentiment": "bullish", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "StormB2", "url": "https://reddit.com/r/microsoft/comments/1i4j7h7/microsoft_365_business_code_stack/", "ticker": "MSFT", "date": "2025-01-18"}, {"title": "Should I be worried about applying for too many jobs?", "content": "I've been applying for jobs at Microsoft for the past few months. I have 20+ inactive applications, and currently have 10 pending. I recently completely overhauled my resume and want to apply for more positions, but I am getting worried that it looks like I am spamming them. I only apply for positions where I meet all the required criteria. Am I worried about nothing? I am tempted to make another account for a fresh start, but honestly that feels shady.", "created_time": "2025-01-17T00:54:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 47.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "ItWorkedLastTime", "url": "https://reddit.com/r/microsoft/comments/1i34bua/should_i_be_worried_about_applying_for_too_many/", "ticker": "MSFT", "date": "2025-01-17"}, {"title": "technical program manager - interview prep", "content": "Hi, got a question - TPM role at Microsoft, how much coding experience is required? I've read that coding / programming experience is very light, but on the other hand, I heard there are coding questions in the interview. \n\n  \nCan anyone shine a light on that?\n\n  \nThanks!", "created_time": "2025-01-17T01:51:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Clay20222", "url": "https://reddit.com/r/microsoft/comments/1i35gt1/technical_program_manager_interview_prep/", "ticker": "MSFT", "date": "2025-01-17"}, {"title": "Contract role on hold for a month due to funding?", "content": "Hey everyone I was told I was hired for a contract role as a program manager in ux for Microsoft through a vendor company but now it's on hold. I interviewed with the internal managers, established pay, was told I was hired and that it would most likely be ready to onboard end of January. I just came back from vacation and now the vendor company is telling me the budget for the contract length is still pending with the PO they sent and most likely won't be approved till near end of February. I was never told this up front by anyone and this is the first time this has happened to me as a contractor of 5 years. I like the pay and the role but wondering if I should just keep looking in the mean time. Is this poor timing due to budget submissions for 2025 fiscal?", "created_time": "2025-01-17T23:57:26", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "iceetoomuch", "url": "https://reddit.com/r/microsoft/comments/1i3uibk/contract_role_on_hold_for_a_month_due_to_funding/", "ticker": "MSFT", "date": "2025-01-17"}, {"title": "Is MS 900 and Pl 300 worth it as a aspiring developer?", "content": "I'm getting a 50% off coursera discount voucher for pl 300 and ms 900 upon completion of its respective courses. Is it worth getting or should I just stick with getting JUST the azure developer cert?", "created_time": "2025-01-16T08:25:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "AmazingInflation58", "url": "https://reddit.com/r/microsoft/comments/1i2kexj/is_ms_900_and_pl_300_worth_it_as_a_aspiring/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "Microsoft 365 users still on Windows 10 will be out of luck when Windows 10 is retired in October", "content": "", "created_time": "2025-01-16T12:54:14", "platform": "reddit", "sentiment": "neutral", "engagement_score": 119.0, "upvotes": 49, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1i2o8yq/microsoft_365_users_still_on_windows_10_will_be/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "How is Microsoft still alive?", "content": "Genuine question. Let’s go over it:\n\n- Microsoft accounts: log-in issues of every type, going from your personal accounts to work accounts. One time I went to a Microsoft store and even the cashier was having log in problems. If you log in and connect your account to another (let’s say Microsoft-PlayStation for Minecraft) you’re stuck with it because MICROSOFT DOES NOT HAVE A WAY TO UNLINK TWO ACCOUNTS. \n\n- Surface products: been using surfaces for ages, overall decent but overpriced. However: literally every complementary object has a high chance of dying after the 3 year mark. Keyboard died in one year, pen in 3 and a half. They made a pen which can change tips where the tip connector breaks before the interchangeable tip. Genius work.\n\n- Office: anyone that ever had to deal with Office Notes knows it’s as comfortable as lying down on anti-homeless architecture. Possibly the least seamless software suite anyone has ever created.\n\n- Xbox: I feel so sorry for y’all. \n\n- AI: Bought shares in Open AI to collaborate with it only for Open AI to give the same privilege to Apple without them having to pay a dime. Another management masterclass. \n\nNot saying Microsoft hasn’t done any good work, but so far I had problems with anything I’ve ever used made by Microsoft. How does a company survive like this? Are we all just too afraid of switching to competitors? Is it on Us? I’m genuinely curious.", "created_time": "2025-01-16T15:46:30", "platform": "reddit", "sentiment": "bullish", "engagement_score": 36.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>_", "url": "https://reddit.com/r/microsoft/comments/1i2rtom/how_is_microsoft_still_alive/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "Grrrr I'm so over it. Microsoft or bust ", "content": "\"or bust\"? I'm just being a little dramatic but what gives? I've been actively applying to Microsoft roles since September 2024, and even had FTE friends submit referrals on my behalf. However, when I logged in today, I saw that I wasn’t considered for three of the six positions I applied for. Are these ghosts jobs? Based on the listed requirements, I meet the qualifications, have the necessary certifications, and relevant experience. Yet, I haven’t even been offered an interview, and it’s incredibly frustrating. I’m at a loss trying to understand what’s going wrong. I've written and written/tailored my resume countless times w/ AI and I've even broken down and included cover letters.  What steps can I take to improve my chances? ", "created_time": "2025-01-16T19:50:40", "platform": "reddit", "sentiment": "bearish", "engagement_score": 104.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Fair-Cap-1048", "url": "https://reddit.com/r/microsoft/comments/1i2xlm9/grrrr_im_so_over_it_microsoft_or_bust/", "ticker": "MSFT", "date": "2025-01-16"}, {"title": "Microsoft raises price of consumer Microsoft 365 first time since 2013", "content": "", "created_time": "2025-01-16T20:18:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 470.0, "upvotes": 336, "num_comments": 0, "subreddit": "unknown", "author": "Novel_Negotiation224", "url": "https://reddit.com/r/microsoft/comments/1i2y8v3/microsoft_raises_price_of_consumer_microsoft_365/", "ticker": "MSFT", "date": "2025-01-16"}], "metadata": {"timestamp": "2025-07-06T19:57:04.103278", "end_date": "2025-01-23", "days_back": 7, "successful_dates": ["2025-01-23", "2025-01-22", "2025-01-21", "2025-01-20", "2025-01-19", "2025-01-18", "2025-01-17", "2025-01-16"], "failed_dates": [], "source": "local"}}}}