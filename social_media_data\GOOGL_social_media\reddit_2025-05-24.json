[{"platform": "reddit", "post_id": "reddit_1ktz6po", "title": "[25] Looking for Feedback on My Financial Plan – Loans, Investing, Emergency Fund, and Big Life Changes Ahead", "content": "Hi r/personalfinance! I’d really appreciate feedback on my current financial plan and whether I’m on the right track. I’ve been trying to stay intentional with my money while juggling student loans, investing, and preparing for some big life milestones.\n\nInvestments & Accounts\n\t•\tRoth IRA – $32.1k (opened 3 years ago, maxed this year). Asset mix: ~67% domestic stocks, 19% international, 12% bonds, 2% short-term. I have less than 20% in individual stocks (I got lucky and purchased some while everything was recovering after COVID, and they exploded upwards since). The holdings are spread across various funds I picked early on when I was still learning. I’ve since become more educated and am working to consolidate into a cleaner, Boglehead-esque portfolio with 4 core funds (can’t mentally commit to ONLY 3). I have no plans to sell the stocks anytime soon but will see what happens long-term. \nCurrent holdings: AAPL, AMZN, BRKB, DIS, FBGRX, FFRHX, FSELX, FSKAX, FTBFX, FTIHX, FXAIX, FXNAX, FZILX, FZROX, GOOGL, SCHD, SCHG, TSLA\n\t•\t403(b) – $4.3k (ROTH contributions at 7% = ~$220/paycheck). My employer matches 50% of the first 4%, and it’s vested in 3 years. \n\t•\tBrokerage (Fidelity) – $5.6k in SPAXX. Currently being used as a quasi HYSA but I like that it’s liquid, can be used for investments without additional movement later on (don’t need to open another account), and I can throw it into short term CDs if higher rates are available. It came with a debit card\n\t•\tI have a TreasuryDirect account that my family has given me bonds/bills as gifts in. I’ve made no contributions as of yet, but it contains $900 in T-bills, $800 in I-bonds face value, and $2,450 face value in EE bonds (maturing around 2035)\n\t•\tI also have a $600 CD that will be cashed and moved into another account come November. It was a gift with a bank and I want to consolidate\n\nDebts\n\t•\tCredit cards – Always paid in full each month\n\t•\tStudent loans – All undergrad loans paid off. Grad loans currently in forbearance until 2026 (no required payments), but I’m still paying them down aggressively using the avalanche method:\n\t•\t$19,692 @ 6.54%\n\t•\t$19,448 @ 5.28%\n\t•\tPaying at least $500/month, often more (some months $1k+). I’m anticipating this will be near my monthly payment amount once I’m required to start paying, so I want to adequately budget for this as is. \n\nIncome\n\t•\tMain job (healthcare) – ~$2,050 biweekly take-home\n\t•\tStarting in July: plan to pick up an extra 10 hours/month of bonus pay (~$450 post-tax)\n\t•\tSide gigs: I referee rugby, making ~$3750/year, and earn $600/year as a student supervisor in a pro bono PT clinic\n\t•\tOne time “gifts” expected this year still include $1,600 post-tax from a sign-on bonus (used the other half to max out the IRA) and $3,000 expected from a family gift\n\nExpenses\n\t•\tRent + utilities (incl. electric/internet): ~$2k/month\n\t•\tTotal average monthly spending (Including loan payments): ~$3,100–$3,300\n\t•\tHealth insurance and other benefits deducted pre-tax\n\nPartner\n\t•\tLives with me, pays me back for  ~$500/month as I use my account to pay all the bills and rent\n\t•\tRecently started a $44k salary job\n\t•\tHas a strong emergency fund (~10 months) but is in the preparation stages to start contributing to a Roth IRA/401K\n\nGoals (in order of personal priority):\n\t1.\tRebuild emergency fund to $10k (aiming for July/August)\n\t2.\tSave ~$2.5k for an engagement ring (late summer/fall proposal)\n\t3.\tSave $5k+ for a car down payment by August (likely need to replace current car)\n\t4.\tAggressively pay down student loans (goal: payoff in 3–5 years)\n\t5.\tIncrease 403(b) contributions when able (as goals are met vs as I tighten up the budget, hope to at least increase to 10%)\n\nFuture Planning\n\t•\tLikely life events: marriage → house → kids (3+ years out)\n\t•\tPlan to stay in current area until 403(b) match vests and I try for an internal promotion/job transition (~$10–15k salary bump, likely into home health)\n\t•\tGirlfriend and I are aligned on priorities and flexible if budget cuts are needed, but I think we are in okay shape. Just want to be mindful of our lifestyle creep as she just went from part-time work to this full-time job. \n\nWould love your thoughts on:\n\t•\tConsolidating my Roth IRA into fewer funds\n\t•\tWhether I’m allocating wisely between debt payoff vs. investments\n\t•\tAnything I might be overlooking or underestimating\n\nThanks so much in advance!\n", "author": "Historical_Sugar_270", "created_time": "2025-05-24T00:44:45", "url": "https://reddit.com/r/personalfinance/comments/1ktz6po/25_looking_for_feedback_on_my_financial_plan/", "upvotes": 1, "comments_count": 1, "sentiment": "bearish", "engagement_score": 3.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ktzsyw", "title": "These are all AI videos generated with Google Veo 3", "content": "", "author": "Aware_Situation_868", "created_time": "2025-05-24T01:17:28", "url": "https://reddit.com/r/google/comments/1ktzsyw/these_are_all_ai_videos_generated_with_google_veo/", "upvotes": 490, "comments_count": 146, "sentiment": "neutral", "engagement_score": 782.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kubhrs", "title": "How can I make Google maps show me what lane I need to be in before I turn?", "content": "Its really annoying how many u-turns I have to do because once I turn then I basically guess the lane i need to be in and 9 times out of 10 im in the wrong one and miss my turn because sometimes I need to turn again shortly after or Noone let's me over so I can make my turn. ", "author": "Reverseflash202", "created_time": "2025-05-24T13:22:19", "url": "https://reddit.com/r/GoogleMaps/comments/1kubhrs/how_can_i_make_google_maps_show_me_what_lane_i/", "upvotes": 2, "comments_count": 19, "sentiment": "bearish", "engagement_score": 40.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kucnah", "title": "My Google Ads Search Campaign Tanked After Years of Success – Any Insight?", "content": "Hey everyone,\n\nI run a service-based business in NYC focused on indoor air quality testing (mold, VOCs, etc.), and my entire business has been built on Google Ads search campaigns. I don’t have a storefront – just a couple of employees, a solid service, and a phone number.\n\nHere’s the rundown:\n\n**The Backstory**\n\nWhen I started a few years ago, I knew very little – learned from YouTube, tried things out. Somehow I created a search campaign that worked.\n\n* 6–7 clicks a day\n* $6–$8 CPC\n* 1–3 phone calls a day\n* Booked 2–4 jobs a week\n\nThis kept my business running smoothly for two years. I hired people. Life was good.\n\n**The Problem**\n\nIn 2025, everything fell apart. Without any major changes, impressions vanished, CPC shot up to **$42 per click**, and conversions died.\n\nI paused that campaign, created a brand new one from scratch – same targeting, new ads – and the exact same thing happened:\n\n* Barely any impressions\n* CPC still sky-high\n* Leads dried up\n\nI rely almost entirely on inbound search traffic. Referrals help, but they’re not reliable – this is a one-and-done type service. You don’t need a mold test every week. My market is NYC, so demand should always exist.\n\n# What I’ve Tried\n\n* Rebuilt campaign from scratch\n* Tested new keywords and ad copy\n* Adjusted bidding strategies (Maximize Conversions)\n* Monitored quality scores and ad relevance (everything looked fine)\n\n# What I Don’t Understand\n\n* Why would a historically consistent campaign suddenly stop delivering?\n* Why would a new campaign, in a massive market like NYC, get almost no traffic?\n* Has something changed in Google’s system recently that favors big-budget or lead form campaigns?\n\nI’m honestly at a loss. This is how I feed my employees and pay rent. If anyone’s experienced this drop-off recently or has thoughts, I’d really appreciate some guidance or just to know I’m not going crazy.\n\nThanks in advance.\n\n\n\n**Update:** \n\n\n\nFirst of all—massive thanks to everyone who commented on my original post. The advice, sympathy, and even just the “yeah dude, Google Ads is a black box now” validation helped more than you know.\n\nSo after a week of *nothing*—no calls, no leads, just CPCs spiking to $54 and me paying my crew out of pure delusion—I was cooked. Burnt. Done. Sitting at my desk like a monkey staring at a glowing rectangle wondering why my life is now entirely dependent on an algorithm I don’t understand.\n\nThen I remembered I have ChatGPT Pro. And this thing called *Operator*. I was like, “You know what? I’m already getting zero calls so before i pay an agency let’s see what happens if I just let the AI do it.  This campaign is already wrecked anyway.\n\nSo I copy-pasted this prompt I built using GPT-4.5 and Reddit threads and deep reaserchj based on this, logged in through Operator, guide it to log in gave it my Google Ads credentials (yes, I know, probably insane), and told it:\n\n“Do whatever you want. Break shit. Edit anything. I literally do not care anymore.”\n\nAnd this thing *went to town*.\n\nFor 27 straight minutes it was like watching a hacker movie in real-time. It removed 47 negative keywords, added new keywords, changed some to phrase some to broad match, adjusted targeting, restructured some ad groups, and scrolled through settings I forgot even existed. Every 30 seconds it would ask something like “Do you want me to change this?” and I finally just said:\n\n“STOP ASKING. YOU ARE GOD NOW.”\n\nThen it stopped. Said “all done.”\n\nI figured it was about to get my account banned or implode my credit card.\n\n**Next day, I get 4 phone calls.**\n\nThree scheduled jobs. One from a luxury retail store in SoHo. Another from a hotel needing 12 rooms tested. A few solid residentials. CPC dropped from $42 to **$7.96**. And it’s stayed there all week.\n\n\n\nThe week before? **$0**.\n\nThis week? **Booked $17K.**\n\nWhat even is reality anymore?\n\nAnyway, I’m working on diversifying channels now because I’m not trying to let one algorithm decide whether I eat next month. But for now—holy shit. We’re back. ", "author": "Reasonable_Mark_747", "created_time": "2025-05-24T14:16:14", "url": "https://reddit.com/r/PPC/comments/1kucnah/my_google_ads_search_campaign_tanked_after_years/", "upvotes": 135, "comments_count": 33, "sentiment": "bullish", "engagement_score": 201.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kucv55", "title": "[D] Is Google Colab Pro worth for my project?", "content": "Hey guys, I'm currently dealing with my bachelor degree's final project. My title is “Grayscale Image Colorization Using Deep Learning”. I have datasets of 10000 images i guess. And it took quite a long time to train it.\n\nSo my question is, does purchasing colab pro makes the training faster or not? And does it worth the money if i just want to focus on developing my project using colab pro? \n\nThanks for you guys input, I’ll be waiting for it.", "author": "Few-Criticism9249", "created_time": "2025-05-24T14:26:22", "url": "https://reddit.com/r/MachineLearning/comments/1kucv55/d_is_google_colab_pro_worth_for_my_project/", "upvotes": 5, "comments_count": 36, "sentiment": "bullish", "engagement_score": 77.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kugymb", "title": "Realistically what is going to happen with TSLA robotaxi launch", "content": "Basically the headline.\n\nHonestly i haven’t seen a lot of quality discussion about the planned robotaxi launch in June. Otherthan bulls and bears yelling on each other and random meme shit. \nLet’s collect all the facts and try to find what’s gonna happen in few weeks. \n\nHere are some facts we know so far and i will try to be impartial as i can be and just state facts and not my opinion. \n- Elon announced robotaxi planned launch in Austin in June. \n- TSLA stock has rallied 40+ % since the announcement. \n- TSLA sales are being disastrous worldwide and on going sales news are negative. Hence making the stock rally purely based on hopium on robotaxi launch. \n- TSLA FSD is currently at level-2 autonomy. \n- Full autonomy with driver less operation is level-5. \n- Tsla uses a complete vision based system. vs competitors multiple sensors including LIDAR and Radars. \n- We haven’t seen any test vehicles or any tests happening around robotaxi despite launch date being just few weeks away. (Waymo did years of testing before making their service publicly available)\n- On the otherside, tsla fans seems to be seeing all the current fsd being driven by consumers are actual test and that is good enough for fsd launch. \n\nThat’s all for now. Let’s discuss and as we go we can add more points and if possible we can keep this as a long thread to keep track the planned event. \n", "author": "ldmonko", "created_time": "2025-05-24T17:25:41", "url": "https://reddit.com/r/wallstreetbets/comments/1kugymb/realistically_what_is_going_to_happen_with_tsla/", "upvotes": 432, "comments_count": 810, "sentiment": "bullish", "engagement_score": 2052.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kuizns", "title": "No Shot Google's Pixel 10 Pro Leaked This Way", "content": "", "author": "Maxwellxoxo_", "created_time": "2025-05-24T18:55:14", "url": "https://reddit.com/r/GooglePixel/comments/1kuizns/no_shot_googles_pixel_10_pro_leaked_this_way/", "upvotes": 419, "comments_count": 122, "sentiment": "neutral", "engagement_score": 663.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kun0n8", "title": "Great Summary of Google Marketing Live 2025", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-24T22:01:18", "url": "https://reddit.com/r/marketing/comments/1kun0n8/great_summary_of_google_marketing_live_2025/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}]