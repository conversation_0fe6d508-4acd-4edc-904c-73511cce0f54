[{"platform": "reddit", "post_id": "reddit_1hw61fo", "title": "Google CEO says over 25% of new Google code is generated by AI", "content": "", "author": "katxwoods", "created_time": "2025-01-08T00:00:06", "url": "https://reddit.com/r/artificial/comments/1hw61fo/google_ceo_says_over_25_of_new_google_code_is/", "upvotes": 132, "comments_count": 61, "sentiment": "neutral", "engagement_score": 254.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hw8mlv", "title": "Microsoft disguises Bing as Google to fool inattentive searchers", "content": "", "author": "LookAtThatBacon", "created_time": "2025-01-08T02:03:24", "url": "https://reddit.com/r/nottheonion/comments/1hw8mlv/microsoft_disguises_bing_as_google_to_fool/", "upvotes": 975, "comments_count": 120, "sentiment": "neutral", "engagement_score": 1215.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwa8be", "title": "<PERSON><PERSON><PERSON> just pulled off the greatest SEO hack of 2025. (So far)", "content": "He recently posted on Instagram, sharing his dissatisfaction with adidas that their site comes before his when you google his site Yeezy.\n\nNow because of a few million fans typing it in unison on Google, testing out what he was saying, his site now comes before adidas. Incredible.\n\nThis was in real time within 20 minutes.\n\nI was reading the other day about how marketers need to move beyond relying on google searches for their business because of the up rise of \"Zero Click Results\" from users thanks to Googles built in AI feature.\n\nDo you think <PERSON><PERSON><PERSON>'s organic post prove that SEO is still king?", "author": "JparkerMarketer", "created_time": "2025-01-08T03:24:48", "url": "https://reddit.com/r/Entrepreneur/comments/1hwa8be/kanye_just_pulled_off_the_greatest_seo_hack_of/", "upvotes": 2422, "comments_count": 206, "sentiment": "neutral", "engagement_score": 2834.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwbp2p", "title": "Has anyone seen this ad? It ended before I could click on anything to screenshot it more.", "content": "The ad is just a video of this woman masterbating, that’s it. Someone from YouTube reviewed this advertisement and said, “yeah that’s fine.”", "author": "USAirsoft", "created_time": "2025-01-08T04:44:56", "url": "https://reddit.com/r/youtube/comments/1hwbp2p/has_anyone_seen_this_ad_it_ended_before_i_could/", "upvotes": 1, "comments_count": 20, "sentiment": "neutral", "engagement_score": 41.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwc9ji", "title": "Nvidia CEO <PERSON> today on Tesla’s approach to self-driving.", "content": "Full interview: https://www.youtube.com/watch?v=yXKyH4NuqEU", "author": "InformalSky8443", "created_time": "2025-01-08T05:17:29", "url": "https://reddit.com/r/teslainvestorsclub/comments/1hwc9ji/nvidia_ceo_j<PERSON><PERSON>_huang_today_on_teslas_approach/", "upvotes": 104, "comments_count": 218, "sentiment": "neutral", "engagement_score": 540.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwffmp", "title": "is micro management unethical for startups? (be honest..seeking honest answers from experienced founders)", "content": "In small startups, every penny truly counts, and sometimes it feels like success depends on every team member going above and beyond to get things off the ground. But this raises some tough questions. Is it fair—or even ethical—to expect employees to work beyond what was agreed upon?\n\nI’ve also been thinking about micro-managing as a way to ensure everyone is being as productive as possible. It’s not about distrust of what has been agreed on but rather understanding whether the team’s efforts are worth future investment on. At the same time, I realize that constant oversight could backfire, damaging trust and morale.\n\nFor those who’ve been through this, how do you strike a balance? How do you manage limited resources and high stakes without crossing ethical lines?", "author": "<PERSON>en<PERSON><PERSON><PERSON>", "created_time": "2025-01-08T08:38:01", "url": "https://reddit.com/r/startups/comments/1hwffmp/is_micro_management_unethical_for_startups_be/", "upvotes": 0, "comments_count": 70, "sentiment": "neutral", "engagement_score": 140.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwj6kq", "title": "Please can u take a minute to answer this short survey on ICE v EV for my school project. Thanks ", "content": "https://docs.google.com/forms/d/e/1FAIpQLSeSe1Tri1ZrtXxwpVeXhnPd94XrlAt-4LcYvnUHpq7eG78Eig/viewform?usp=sf_link ", "author": "Ashamed-Dog-9346", "created_time": "2025-01-08T12:33:21", "url": "https://reddit.com/r/electriccars/comments/1hwj6kq/please_can_u_take_a_minute_to_answer_this_short/", "upvotes": 8, "comments_count": 18, "sentiment": "bearish", "engagement_score": 44.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwj7m9", "title": "Don’t <PERSON><PERSON>sh Google for Being Good at What It Does", "content": "", "author": "franciscarter06", "created_time": "2025-01-08T12:34:58", "url": "https://reddit.com/r/google/comments/1hwj7m9/dont_punish_google_for_being_good_at_what_it_does/", "upvotes": 0, "comments_count": 27, "sentiment": "neutral", "engagement_score": 54.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwkdkc", "title": "What are you thoughts on using Google Trends to follow interest in stocks? ", "content": "", "author": "Aware-Designer2505", "created_time": "2025-01-08T13:35:34", "url": "https://reddit.com/r/StockMarket/comments/1hwkdkc/what_are_you_thoughts_on_using_google_trends_to/", "upvotes": 0, "comments_count": 17, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwkt4t", "title": "The \"fake frame\" hate is hypocritical when you take a step back.", "content": "I'm seeing a ton of \"fake frame\" hate and I don't understand it to be honest.  Posts about how the 5090 is getting 29fps and only 25% faster than the 4090 when comparing it to 4k, path traced, etc.  People whining about DLSS, lazy devs, hacks, etc.\n\nThe hardcore facts are that this has been going on forever and the only people complaining are the ones that forget how we got here and where we came from.\n\n**Traditional Compute Limitations**\n\nI won't go into rasterization, pixel shading, and the 3D pipeline.  Tbh, I'm not qualified to speak on it and don't fully understand it.  However, all you need to know is that the way 3D images get shown to you as a series of colored 2D pixels has changed over the years.  Sometimes there are big changes to how this is done and sometimes there are small changes.\n\nHowever, most importantly, if you don't know what Moore's Law is and why it's technically dead, then you need to start there.\n\n[https://cap.csail.mit.edu/death-moores-law-what-it-means-and-what-might-fill-gap-going-forward](https://cap.csail.mit.edu/death-moores-law-what-it-means-and-what-might-fill-gap-going-forward)\n\nTL;DR - The traditional \"brute force\" methods of *all* chip computing cannot just keep getting better and better.  GPUs *and CPUs* must rely on innovative ways to get better performance.  AMD's X3D cache is a GREAT example for CPUs while DLSS is a great example for GPUs.\n\n**Gaming and the 3 Primary Ways to Tweak Them**\n\nWhen it comes to people making real time, interactive, games work *for them*, there have always been 3 primary \"levers to pull\" to get the right mix of:\n\n1. Fidelity.  How good does the game look?\n2. Latency.  How quickly does the game respond to my input?\n3. Fluidity. How fast / smooth does the game run?\n\nHardware makers, engine makers, and game makers have found creative ways over the years to get better results in all 3 of these areas.  And sometimes, compromises in 1 area are made to get better results in another area.\n\n***The most undeniable and common example of making a compromise is \"turning down your graphics settings to get better framerates\".  If you've ever done this and you are complaining about \"fake frames\", you are a hypocrite.***\n\nI really hope you aren't too insulted to read the rest.\n\n**AI, Ray/Path Tracing, and Frame Gen... And Why It Is No Different Than What You've Been Doing Forever**\n\nDLSS: +fluidity, -fidelity\n\nReflex: +latency, -fluidity (by capping it)\n\nDLSS: +fluidity, -fidelity\n\nRay Tracing: +fidelity, -fluidity\n\nFrame Generation: +fluidity, -latency\n\nVSync/GSync: Strange mix of manipulating fluidity and latency to reduce screen tearing (fidelity)\n\nThe point is.... all of these \"tricks\" are just options so that *you* can figure out the right combination of things that are *right for you*.  And it turns out, the most popular and well-received \"hacks\" are the ones that have really good benefits with very little compromises.\n\nWhen it first came out, DLSS compromised too much and provided too little (generally speaking).  But over the years, it has gotten better.  And the latest DLSS 4 looks to swing things even more positively in the direction of more gains / less compromises.\n\nMulti frame-generation is similarly moving frame generation towards more gains and less compromises (being able to do a 2nd or 3rd inserted frame for a 10th of the latency cost of the first frame!).\n\nAnd *all of this* is primarily in support of being able to do real time ray / path tracing which is a HUGE impact to fidelity thanks to realistic lighting which is quite arguably the most important aspect of anything visually... from photography, to making videos, to real time graphics.\n\nMoore's Law has been dead.  All advancements in computing have come in the form of these \"hacks\".  The best way to combine various options of these hacks is subjective and will change depending on the game, user, their hardware, etc.  If you don't like that, then I suggest you figure out a way to bend physics to your will.\n\n\\*EDIT\\*  \nSeems like most people are sort of hung up on the \"hating fake frames\".  Thats fair because that is the title.  But the post is meant to really be non-traditional rendering techniques (including DLSS) and how they are required (unless something changes) to achieve better \"perceived performance\".  I also think its fair to say Nvidia is not being honest about some of the marketing claims and they need to do a better job of educating their users on how these tricks impact other things and the compromises made to achieve them.", "author": "a-m<PERSON><PERSON>ey", "created_time": "2025-01-08T13:57:32", "url": "https://reddit.com/r/nvidia/comments/1hwkt4t/the_fake_frame_hate_is_hypocritical_when_you_take/", "upvotes": 0, "comments_count": 330, "sentiment": "bullish", "engagement_score": 660.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwnspz", "title": "Is <PERSON> Huang Too Conservative About Quantum Computing?", "content": "Most of you have probably seen what <PERSON> said about quantum computing. He thinks it’s going to take **20+ years** before we see \"very useful quantum computers.\" After that, stocks like $IONQ, $RGTI, and $QUBT tanked over 30%.\n\nBut is he being too pessimistic?\n\nlike if you look at tech history, people have been very wrong about timelines before. Back in 1995, <PERSON> wrote a memo about how the internet was going to change everything, but a lot of people thought it would take decades to go mainstream. By 2005, over a billion people were online, and it had already changed how we lived and worked.\n\nSame thing with AI. In 2012, it was mostly academic stuff with no real-world impact. But by the 2020s, it was everywhere. Tools like ChatGPT and advancements in industries like healthcare and finance made it clear that the shift happened way faster than most people expected.\n\nFrom what I’ve read, quantum computing is obviously still in its early days, but there are signs of progress that make me wonder if 20 years might be too long. Companies are starting to use quantum systems for things like optimizing supply chains or improving financial modeling. While these applications are limited and still rely heavily on classical computers to assist, it feels like a stepping stone toward something bigger. Hybrid systems, where quantum and classical work together, are already showing practical value in solving specific problems today, even if we’re not yet at the \"very useful\" stage <PERSON> is talking about.\n\nI realize <PERSON> is <PERSON><PERSON> more informed on this than I am, but with how quickly innovation keeps speeding up, it’s hard to believe it’ll take that long. What do yall think?", "author": "WadsoMarkets", "created_time": "2025-01-08T16:12:55", "url": "https://reddit.com/r/StockMarket/comments/1hwnspz/is_jensen_huang_too_conservative_about_quantum/", "upvotes": 8, "comments_count": 116, "sentiment": "bullish", "engagement_score": 240.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwosqn", "title": "Why the $750,000 Bet on ELTP", "content": "When is a stock “worth” a certain price? Let’s say a certain electric car company was producing 25% of all electric cars, and the price per share was $100 per share, but you had clear knowledge that based on existing orders, this electric car company would hit 50% market share once those cars were done being built? The cars aren’t technically built yet, but the orders are there, and we know the approximate market  penetration this company would have. In this scenario, I would value the stock at $200. It’s clear as day that this company is actually worth twice as much, but other investors haven’t priced in the basically guaranteed income and market share as long as a meteor doesn’t smash in to the factory. \n\nWhat’s this have to do with ELTP? All of the EXISTING approvals, orders, and ingredients are in place to make a reasonable assumption of the value TODAY. It’s grade school math to calculate.\n\nMarket penetration of drugs by the sales team. The sales team is SO good, that they did more sales in 1 month than <PERSON><PERSON><PERSON> did in 12 months. Lannette used to be a $3.1 billion drug company. \n\nNew drugs don’t have an uphill path for the sales team. They get to call their existing monster contracts with some of the largest drug retailers in the country and say, “hi, I’ve got <PERSON>y<PERSON><PERSON>, Percocet, hydrocodone now. How much of each do you want?” It’s a HOT sales call. Anyone in sales knows the value of this.\n\nProfit margin. Pull the margin from last 4 quarters. Remove last quarter due to one time repurchase of old drugs.\n\nAvg PE ratio of a company in this space. I use 26. You can go as high as 40 and as low as 20. \n\nNumber if shares about 1 billion.\n\nRun all the numbers. I’ve run 30 scenarios plus or minus and they group up in general price ranges. I’ve had ChatGPT run them as well to see what it thinks. I’ve had Gemini Deep Research spend 2 days researching background info to run against my numbers. Both of them tend to be a little conservative and “don’t want to offer financial advice”. \n\nMy highest range hits $10 per share, but it’s honestly the absolute perfect storm of factors including who buys us and then having some less immediately obvious financial benefits. My lowest range, including every bad turn that could happen without assuming a cataclysmic event is $1.60 per share.\n\nThe rest of my analysis groups the share price at $3.60, $4.60, and $7.20. My CURRENT evaluation of this stock, knowing the amount of orders they are able to start filling now, is between the $3.60 to $4.60 range.\n\nThis doesn’t include a single other drug getting approved. \n\nThis doesn’t include what I estimate to be as $1 per share in value for their unfinished Anti Opioid Abuse technology.\n\nThis doesn’t include the continued Research and Development money ELTP spends every quarter. \n\nThis doesn’t include the Purdue Pharma legal loss with Oxy.\n\nThis doesn’t include the high likelihood that we are the first to get our Oxy ANDA approved. \n\nThis doesn’t include continued expansion internationally.\n\nAnd this doesn’t include any additional factories we build.\n\nNow, the real question is - how much more should I buy?", "author": "<PERSON>ol<PERSON><PERSON><PERSON>", "created_time": "2025-01-08T16:53:12", "url": "https://reddit.com/r/pennystocks/comments/1hwosqn/why_the_750000_bet_on_eltp/", "upvotes": 109, "comments_count": 107, "sentiment": "bullish", "engagement_score": 323.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwsuxb", "title": "XPeng 5.5 Autonomous Driving Update: Parking Spot to Parking Spot TEST!", "content": "", "author": "Recoil42", "created_time": "2025-01-08T19:39:03", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hwsuxb/xpeng_55_autonomous_driving_update_parking_spot/", "upvotes": 10, "comments_count": 9, "sentiment": "neutral", "engagement_score": 28.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hwy12m", "title": "Hot take: please don't join a pre-PMF startup", "content": "Hi all, I got inspired on the topic by <PERSON><PERSON>'s (Cofounder of Maven & Udemy) recent post—it is a read worth your time! (will add links in the comments due to r/startups' policy)\n\nI wanted to share my perspective on the question since I wish I had read this back when I was making pivotal career choices. I have nothing to sell, and I will speak my truth as if I were talking to a dear younger cousin.\n\n**My take is simple**: If you're smart-hardworking-ambitious, I beg you: never work for a pre-product-market-fit startup.  \n**Why**: most pre-PMF startups fail, and even if they succeed it's unclear you'll fairly benefit from it.  \n**Instead**: work for post-PMF companies, whatever stage you're comfortable with, rake in money + xp + brand + quality lifestyle -> then climb the corporate ladder and/or start your own startup.\n\n**Why?**\n\n(about me: multiple years in pre-PMF startup, in very successful high-growth post-PMF startup, and as co-founder)\n\n**1. Odds of success are extremely low.**\n\nWe all wish we'd join the next big thing. But startup life is cruel, only a handful of companies actually become successful. A few reach PMF. And among them, even fewer reach a couple of million in revenue.\n\nPre-PMF, it’s close to impossible to tell which will succeed—so much so that even professional investors fail all the time at this game.\n\nWhy?\n\n**A.** Fundamentally most startup folks (founders or employees) are optimistic gold diggers—we *want* to believe in the story.\n\n**B.** Founders are professional liars - they need to paint a good story to investors and employees to hype you up, and they will never admit they're just faking it until they make it. They're probably better at bullshitting than you are at cutting through the BS (otherwise you'd be an investor or a founder :). The best founders even exert a \"reality distorsion field\": they will push the right levers within you and convince you of pretty much anything – even though the data can tell a very different story (stagnant growth / no revenue / no avenue for profitability). Success will always be *around the corner*, and weeks, months, years can go by like this, without you realizing you're tied to a zombie startup.\n\nYet here you are, early-stage startup employees, busting your ass, for a fleeting odd of success.\n\n**2. You will be exploited**\n\nFrom experience, founders are very intense and selfish sons of b\\*\\*ches (I've been one). They have decided to commit 100% to this thing. And they will push you to do the same. The upside is life-changing for them – most likely, not for you.\n\nThey will promote toxic work culture: working 10+ hrs a day, at night, on weekends, taking close to no vacations. They will hype this as \"we go hard\", \"we're navy seals\", \"we're like a family\", \"unlimited PTO policy\", and other shallow bullshit. They will indeed *lead by example*: working all sorts of hours, not respecting your personal boundaries, texting/calling you 24/7, taking no days off. Actively or passively, you'll feel guilt-tripped to try to have a regular work schedule. How could you not? Everybody in the company is an enthused cult member.\n\nTruth be told, they're just sucking out your soul like f\\*\\*ing Dementors. You're losing more than you're winning from living like this. Startups are built atop the corpses of smart and loyal employees.\n\nYour friends in bigger orgs are making more money than you, growing their scope / salaries faster, all this while working a peaceful 9-to-5, enjoying hobbies and traveling on the weekends.\n\nBut you're probably thinking: \"no bro, I'm learning a sh\\*tton! I'm becoming a machine!\"  \nNot really.\n\n**3. You will not learn as much as they dangle**\n\nBy working that much, you're indeed producing stuff.  \nBut are you really learning to do it following industry *best practices*?  \nOdds are, you're \"moving fast and breaking things\", which is often a glorified way of saying you're *half-assing*. Since you're pushed to go faster and faster, it's the only way around. All the rest is considered a waste of time by the founders.\n\nIn theory, it's not a big problem. The book goes like: startups go fast, reach PMF, then clean technical/org debt and become more structured, reach profitability and then exit.\n\nHere's the problem **for you** though: even if your startup reaches PMF (rare occurrence), founders will most likely bring *adult supervision* for the next stage of the company – people from FAANG-like companies, with managerial experience.\n\nYou thought you were a family, that you'd grow alongside the company, and that your efforts would be fairly rewarded when success finally happened - by becoming the lead, head of, CxO. But in reality, you were just the simp they were exploiting, and now they will give that sweet position and total comp to someone they actually look up to and think they have something to learn from: your normie college roommate who has the Google/McKinsey stamp on their resume. Founders like to pretend they're unfazed by these credentials, but when push comes to shove, they often choose this type of people, with a pat on the back from their normie investors.\n\nPlease don't think it's a personal vendetta: I'm not only speaking from personal experience, I've seen this happen too many times for me to count, both for my FAANG friends happy to \"exit to an early stage startup\", and to my early-stage fellows pissed to now have to report to a sophisticated schmoozer they usually have no respect for. I'm happy to admit there are counterexamples, some first guys at Facebook, Uber, Slack - who climbed the ladder and managed to FIRE (achieve Financial Independence and Retire Early) post-IPO. These guys are a statistical error - I urge you to not make your life's most important decisions based on their stories.\n\nHow did you get there???  \n**A. No coaching**  \nIn general in pre-PMF, no one is available to **actively coach** you, which is imho the best way to grow.  \nCofounders are way too busy hustling - and they might not even have the skillset to teach you your craft (e.g., you're the first designer or ML person in the org).  \n**B. Diffuse role**  \nJacks-of-all-trades are valued in startups. You will sign for a ML role, but actually you'll also do data engineering, MLops, and probably some software engineering. It's all fine and dandy – but you're not becoming the best at anything. Meaning you're not competitive to rise to a leadership role in your current org, let alone aim for a senior position in a FAANG. I get you, you might find it boring to be put in a box by a large corp, but that's what they need, and your main skill of *being a generalist who goes fast but in a non-clean way* is a no-no for these large corps.  \nIt's kind of ironic - not only will the \"ex-Google/McKinsey/...\" get the best job in your startup, but you won't be able to join Google either.  \n**C. Result: you're not the best**  \nIt's kind of sad, but if we're being honest, the FAANG guy is probably better suited than you to actually run the show. You've worked hard - but for the Zimbabwe army (no disrespect 🇿🇼). They've worked less hard - but for the special forces.\n\nPersonally, I feel that in a pre-PMF startup I mostly unlearned all the best practices I had invested efforts to learn in larger orgs, all that for dubious results.\n\n**4. You won't make a lot of money (even in case of success)**\n\nThis one is pretty straightforward.\n\nEarly stage startups generally pay low in cash and somewhat liberally in stock options (\"hope-money\"). But if the stock never skyrockets, your options are worth nil.\n\nI think it's kind of cruel, but even if the company's valuation actually skyrockets, you're not likely to substantially benefit from it. You probably have <3% equity pre-Series A. Not only will it take 5-10 years to mature to a potential cash exit for you, but these 3% will melt faster than butter on a hot pan.\n\nPeople who know what they're doing—investors, and sometimes repeat founders who learned their lesson the hard way the first time—have all sorts of contractual provisions to get preferential equity treatment: they can sell secondary shares during fundraising rounds, get their cash back first in case of acquisition, have anti-dilution protections, etc. Meanwhile, you're naively signing the standard ESOP piece of crap your co-founder handed your way like a second-hand car salesman closing a deal.\n\nMind you, that's the *success* scenario.\n\nMeanwhile, your FAANG friends – whose base salary is already higher – get RSUs (they don't have to pay to purchase the stocks, but you pre-PMF peasant will have to purchase your stock options if you ever want to activate them) and yearly refreshers, in an almost-guaranteed-to-grow equity.\n\nLet's not even touch on the benefits they're getting but you're not – 401(k) matching, bonuses, awesome health insurance, actual pto & parental leave, and even more than you can think of.\n\nIt means that while you're busting your ass off to stay broke - your friends are quietly building their net worth to escape the rat race.\n\nTo add insult to injury – it might very well be the case that by waiting for a pre-PMF company to reach PMF, and then joining it post-PMF (less risk) from a brand-name company, you'll have a way better total comp & equity package than the sucker who was here since day 1.\n\n**5. When you leave, you will be relatively undesirable on the job market**\n\nI think it's honestly the saddest part. When you leave this type of company, no employer will care about this no-name startup on your resume and the inordinate amount of effort you invested in it.\n\nTrust me, I've hired so many times both for small and big companies, and most people (co-founders, execs, peers) will prefer the candidate with a brand-name on their resume. It's unfair, but the success of the brand brushes off on them. You might've been a phenomenal crew member, but no one wants to hire an expert in paddleboats.\n\nEven if you find yourself launching a VC-backed venture, you will find out that VCs, the very guys pretending they're friendly with early stage startups, will actually favor the entrepreneurs coming from the brand-name company.\n\n**Conclusion: by and large, you're better off not joining any pre-PMF company**.\n\n**My recommendation**: work for post-PMF companies, whatever stage you're comfortable with, rake in money + xp + brand + quality lifestyle -> then climb the corporate ladder and/or start your own company.\n\nI honestly wish someone would've broken this down for me a few years back. But YC and other propagandists were too good at sexifying the pre-PMF and I fell for it.\n\nI know some of you will brush this post off as coming from a hater/loser. Honestly, it's not even about me. I've seen too many bright and very hard-working friends making the wrong career choices and, 5-10 years later, be way behind financially/career-wise compared to the guys who went post-PMF. This has to stop.  \nI just wish fewer good-willed employees would wake up after years only to realize they've been stolen of their youth and fortune.", "author": "Intelovri", "created_time": "2025-01-08T23:14:53", "url": "https://reddit.com/r/startups/comments/1hwy12m/hot_take_please_dont_join_a_prepmf_startup/", "upvotes": 343, "comments_count": 147, "sentiment": "bullish", "engagement_score": 637.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}]