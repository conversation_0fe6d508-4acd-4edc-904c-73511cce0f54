[{"platform": "reddit", "post_id": "reddit_1haopv2", "title": "What charger do I need for this Braun 9 in 1 Trimmer?", "content": "I’ve been looking everywhere and ordered many different chargers off of Amazon but none have worked. Does anyone know what charger I should buy for this razor?", "author": "NoBus6850", "created_time": "2024-12-10T00:16:41", "url": "https://reddit.com/r/batteries/comments/1haopv2/what_charger_do_i_need_for_this_braun_9_in_1/", "upvotes": 0, "comments_count": 2, "sentiment": "bullish", "engagement_score": 4.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1haqgqp", "title": "Lithium batteries and extreme desert heat. Why don’t lithium car batteries explode ?", "content": "So I have a Apple AirTag in my car and usually take it out during summers as the internet says it can explode or start a fire under extreme heat and I live in Arizona so it can get up to 117F outside. But what about lithium car batteries, shouldn’t that be a concern then? I’ve never heard of anyone concerned about that.", "author": "garbageaxount", "created_time": "2024-12-10T01:41:43", "url": "https://reddit.com/r/batteries/comments/1haqgqp/lithium_batteries_and_extreme_desert_heat_why/", "upvotes": 0, "comments_count": 22, "sentiment": "neutral", "engagement_score": 44.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hasiio", "title": "Just cut off a customer ", "content": "Sometimes… enough is enough\n\nHad a customer chew us out before he ordered the wrong item (AGAIN). He’s ordered a handful of items from us over the years and always emails to ask for a “bulk” discount (even tho he barely orders over our $50 free shipping minimum).\n\nHe ordered the wrong items and chewed out the shipping manager and then emailed me directly with the emails he’d sent to her and I said “look, I don’t think we’re a good fit for you. Please check out these other companies” and listed a couple places that I think are good.\n\nHe emailed me back, “I expected no less from you.” And I didn’t respond to that.\n\nI’m thinking I’ll just cancel any future orders from him moving forward, though I doubt he will order from us again.\n\nAt some point, you gotta fire the customer.", "author": "behem<PERSON><PERSON>", "created_time": "2024-12-10T03:27:55", "url": "https://reddit.com/r/smallbusiness/comments/1hasiio/just_cut_off_a_customer/", "upvotes": 606, "comments_count": 87, "sentiment": "neutral", "engagement_score": 780.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1haurl6", "title": "Return of the king! ROG Phone 9 Pro Edition review!", "content": "", "author": "Antonis_32", "created_time": "2024-12-10T05:34:12", "url": "https://reddit.com/r/Android/comments/1haurl6/return_of_the_king_rog_phone_9_pro_edition_review/", "upvotes": 20, "comments_count": 36, "sentiment": "neutral", "engagement_score": 92.0, "source_subreddit": "Android", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1havmov", "title": "I[30] have financially ruined myself", "content": "I work full time and make about $54k a year. I am looking for another job that I can work overnight but the market is terrible right now. I'm so embarrassed typing this but I need help.\n\n-I have about $54k in student loans for a degree I cannot use. I will have my BSN but I have a pinched nerve that has rendered me more immobile than my weight ever has.\n\n-I have $20k in credit card debt from overspending, trying to upkeep a car that I should've junked, etc.\n\n-I have a car note of $475/month for a Camry. I needed a car to get to clinicals and Facebook marketplace was trash. However, I'm upside down in the loan because I've only recently purchased it. Carvana/Carmax etc will only give me about $23k and I owe $27k. Should I eat the $4k and get out the loan?\n\nI'm actively paying my private student loan back so $600/month goes towards that. $200 for insurance. Most of the rest goes towards my debt and that feels useless. I care for my mom so our house is paid for and bills are minimal.\n\nI need help. I fucked myself over and it's wrecking my mental health.", "author": "deleted", "created_time": "2024-12-10T06:29:22", "url": "https://reddit.com/r/personalfinance/comments/1havmov/i30_have_financially_ruined_myself/", "upvotes": 1084, "comments_count": 335, "sentiment": "neutral", "engagement_score": 1754.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1havzwp", "title": "Is fiverr dead? The freelancers there suck. I had a bad experience of hiring some who gave me a bad website", "content": "Is Fiverr no longer as good as it used to be? I recently had a really bad experience hiring a freelancer there, and I’m wondering if this is a common problem. It feels like the quality of work has gone downhill.\n\nI needed a website for a travel-related project, so I hired someone who seemed qualified based on their profile. They said “yes” to the job right away, which seemed promising, but it all went downhill from there. Over the next seven days, they didn’t ask me a single question about the project or what I wanted. They didn’t clarify any details, offer suggestions, or check in at any point.\n\nThen, right before the deadline, they submitted the final website. Unfortunately, it was a mess. It clearly didn’t meet the requirements, and it was obvious they hadn’t put much effort into understanding what I needed.\n\nIs this kind of experience common on Fiverr, or was I just unlucky with this particular freelancer? Have others noticed a decline in quality?", "author": "Professional_Dog_371", "created_time": "2024-12-10T06:54:20", "url": "https://reddit.com/r/startups/comments/1havzwp/is_fiverr_dead_the_freelancers_there_suck_i_had_a/", "upvotes": 208, "comments_count": 162, "sentiment": "neutral", "engagement_score": 532.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1haw3pb", "title": "Google steps in after McDonald's gets ‘review bombed’ over arrest in UnitedHealth CEO's murder", "content": "", "author": "wizardofthefuture", "created_time": "2024-12-10T07:01:22", "url": "https://reddit.com/r/technology/comments/1haw3pb/google_steps_in_after_mcdon<PERSON><PERSON>_gets_review/", "upvotes": 29906, "comments_count": 2088, "sentiment": "neutral", "engagement_score": 34082.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hax9po", "title": "Clip Champ is the worst application I have ever used.", "content": "Clip Champ is a free Microsoft Application that has been accessible via freely for years by now. I have been having issues with this application for said years now. I have never given the time to write a poor review about an application in my entire live until now. I can't do it. Clip Champ is by far THE WORST application I have ever used in my entire life. I have, for years, been on a mindless goal of combining all of my video by date into one video. I strategically separate EACH video by year given the previous history of Clip Champ, knowing it will crash/give up at any given moment. I recently started my post deployment life having FINALLY successfully combined all my previous video, and decided to make a montage of my deployment video. Not more than 30 minutes of combined video, I go to name it and finish it. The final video began to compress and finalize and the app crashes completely demoralizing any chance I had of this fucking app to have fixed itself since when I was gone. For the love of God is there a video editor for free that I can use to just COMPILE AND SQUISH short/medium sized video together to make a single video no longer than 30 minutes long. I am at a loss of words for such a ridiculous application that fucking MICROSOFT has provided.\n\n  \nPS:\n\n  \nBy the love of God if it's cheap I'll pay.", "author": "SoulSeeker14", "created_time": "2024-12-10T08:27:34", "url": "https://reddit.com/r/microsoft/comments/1hax9po/clip_champ_is_the_worst_application_i_have_ever/", "upvotes": 4, "comments_count": 22, "sentiment": "bearish", "engagement_score": 48.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hayvdu", "title": "Is Costco (COST) overvalued?", "content": "I am building a portfolio of mostly recession-proof stocks (WMT, ABBV, MCD, KO, PG, VZ, JNJ, etc.) and I m thinking of adding COST as well. However, it seems way to overpriced, especially for a consumer staples shop. What would be a reasonable price for this stock in your opinion?\n\n\nEDIT: Thanks everyone for your opinion. All I want for now is some tasty b̶u̶r̶g̶e̶r̶ hotdog.", "author": "ashm1987", "created_time": "2024-12-10T10:31:29", "url": "https://reddit.com/r/dividends/comments/1hayvdu/is_costco_cost_overvalued/", "upvotes": 27, "comments_count": 40, "sentiment": "bearish", "engagement_score": 107.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1haz3jm", "title": "The world’s 280 million electric bikes and mopeds are cutting demand for oil far more than electric cars", "content": "", "author": "chilladipa", "created_time": "2024-12-10T10:47:39", "url": "https://reddit.com/r/electricvehicles/comments/1haz3jm/the_worlds_280_million_electric_bikes_and_mopeds/", "upvotes": 1626, "comments_count": 199, "sentiment": "neutral", "engagement_score": 2024.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb0m51", "title": "How Does Google Know Who Will Convert?", "content": "There is little doubt that Google conversion based bid strategies are good at what they say they do.  Getting conversions is what they do well, but how do they do it?\n\nRetargeting previous site visitors is an easy win. Someone who has visited your website five times is more likely to convert than someone who is on their first visit. So, the algorithm bids higher for these—that makes sense. However, what about websites that convert on their first visit?\n\nIf it's not about the number of website visits, other data must be used. If the buyers convert on the first visit, you need a high bid to win the click over competitors. This will also put the ad in a high position. But when running target impression share absolute top, the conversion rate is much lower compared to tROAS/tCPA.  This is comparing the same keywords and ads getting the same number of clicks.\n\nSo, it's not about ad position, number of site visits, or bid. None of these factors contribute to a higher conversion rate. The only other data is the users' profile, e.g. age, sex, job, location, device, audience group, plus whatever else Google knows about the user. \n\nIs it this black box of information that now makes the difference, and it's not possible to compete with this with manual campaigns?", "author": "Different-Goose-8367", "created_time": "2024-12-10T12:26:52", "url": "https://reddit.com/r/PPC/comments/1hb0m51/how_does_google_know_who_will_convert/", "upvotes": 27, "comments_count": 81, "sentiment": "bullish", "engagement_score": 189.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb2a1o", "title": "$KULR Xero Vibe Solution Launches on NVIDIA Jetson Edge AI Platform", "content": "HOUSTON, Dec. 10, 2024 (GLOBE NEWSWIRE) -- [**KULR Technology Group, Inc.**](https://www.globenewswire.com/Tracker?data=IRoPF3LyS4j5JzNkWgxPQ0HEvBtyjfxqhmv-ytzz-lpjNuV7EYl-GK7QqN28mXtn2ft1wt2PxSsTdsevZUdoHdI-pMB6augAj6oeNmxMCJg=) (NYSE American: KULR) (the \"Company\" or \"KULR\"), a global leader in energy management and vibration reduction solutions, today announced the launch of its innovative KULR Xero Vibe™ (“KXV”) solution integrated with the [NVIDIA Jetson edge AI platform](https://www.globenewswire.com/Tracker?data=jrFjhoqibUg_JCJMJ_GTir46fZrvAmSXAASEC0MQhRurD7gWHaa6nstPAAA2maqLA-0dQ18_cUvzY6Pbp1IVro6gLGjGP3KP4KFvjhNcjmP13is6jNov3S0JzttKkgo9MwFqf8IOA8eWgwrVxxIQVc_jSZiSFjQndGOSppaGmvZFqO6TeDrmK-WTRCySeZxs). This new rollout combines superior vibration mitigation with artificial intelligence capabilities to enable high-performance, reliable operation in edge AI environments.\n\nThe NVIDIA Jetson platform, known for its powerful edge AI computing capabilities, offers unparalleled performance for edge applications such as robotics, autonomous machines, industrial IoT, and smart cities. KULR’s Xero Vibe™ solution complements the Jetson platform by addressing key operational challenges such as vibration suppression, ensuring optimal cooling system performance, reduced energy consumption, and extended mechanical lifespans.\n\nKULR CEO Michael Mo highlighted, \"The Jetson platform is NVIDIA's Industrial AI-at-the-edge solution to connect the physical world to the Omniverse through AI agents for the Industrial Revolution 4.0. It's the perfect platform for KULR to integrate our KXV technology and provide our customers a future proof AI-agent powered energy management edge device solution for data centers, renewable energy, electric mobility and industrial cooling applications. We are very excited to embark on this new era of AI-agent powered future with the NVIDIA platform.\"\n\nThe edge AI market size is [projected](https://www.globenewswire.com/Tracker?data=3hx__9Wze3zc0EK9Nht60bhZ6siILSWI8JbHXp5C_CSoMaDKEIUWz18Bv26E1IcA0cwRUsfUMsncZgQSmloFX00d_qea8ixx4RBhHxedmcs=) to grow from $24.05 billion in 2024 to $356.84 billion by 2035, representing a CAGR of 27.786% during the forecast period 2024-2035.\n\n**Key Features of the KULR Xero Vibe™ Solution:**\n\n* **Advanced Vibration Mitigation**: KULR Xero Vibe™ utilizes proprietary vibration reduction technology to minimize mechanical stress, enhancing the reliability and longevity of AI edge devices.\n* **Seamless AI Integration**: While reducing vibration to virtually zero, KULR Xero Vibe™ is enhanced by NVIDIA Jetson platform’s real-time data processing and machine learning at the edge, unlocking new possibilities for AI-driven operations.\n* **Durability in Harsh Environments**: Designed for rugged and mission-critical use cases, the KXV solution supports operations in extreme conditions, making it ideal for industrial, aerospace, and defense applications.\n\n**Applications Across Industries:**\n\nThe KULR Xero Vibe™ solution unlocks transformative opportunities across various sectors, including:\n\n* **Data Centers**: Enables data center fan cooling systems to run more efficiently and environmentally friendly which lowers operational and capex costs.\n* **Wind-Powered Turbines**: Diminished mechanical breakdown extends system lifespan leading to increased energy efficiency.\n* **Bitcoin**: Lowers energy consumption by generating less noise and reduced mechanical wear and tear in proof-of-work mining applications.\n* **Robotics**: Ensures seamless operation in precision robotics for industrial automation.\n* **Aerospace, Defense, and Electric Aviation**: Enables robust performance in mission-critical applications requiring ruggedized systems.\n\nFor more information about the KULR Xero Vibe™ solution, visit [www.kulrtechnology.com](https://www.globenewswire.com/Tracker?data=dhhecN5Dfx2hJEtQMbVSFss0FsadPHy3NSYLPT9QTcucRsHnhbxAiaxgJfIVPfFc91r6fPorOQFQIVWPdayPJQRtu9ZWfhUBoKWGrI9cy-M=).\n\n**About KULR Technology Group Inc.**  \nKULR Technology Group Inc. (NYSE American: KULR) delivers cutting edge energy storage solutions for space, aerospace, and defense by leveraging a foundation of in-house battery design expertise, comprehensive cell and battery testing suite, and battery fabrication and production capabilities. The Company’s holistic offering allows delivery of commercial-off-the-shelf and custom next generation energy storage systems in rapid timelines for a fraction of the cost compared to traditional programs. For more information, please visit [www.kulrtechnology.com](https://www.globenewswire.com/Tracker?data=dhhecN5Dfx2hJEtQMbVSFss0FsadPHy3NSYLPT9QTcsiOzWHEvFHHQ1u-BpTm4LBgv-eEdt-KHWEstP4XoX0e3qIp2QWKzypaIK357T7-58=).\n\n**About NVIDIA Jetson**  \nThe NVIDIA Jetson platform is the leading AI-at-the-edge computing platform with over a million developers. It offers high performance, energy efficiency, and scalability, enabling the development of smart devices and systems across diverse industries.\n\nThoughts ?", "author": "GodMyShield777", "created_time": "2024-12-10T13:55:39", "url": "https://reddit.com/r/pennystocks/comments/1hb2a1o/kulr_xero_vibe_solution_launches_on_nvidia_jetson/", "upvotes": 96, "comments_count": 60, "sentiment": "bullish", "engagement_score": 216.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb2nxu", "title": "Can low birthrates be compared (to an extent) by a more child-friendly overall infrastructure l?", "content": "So, I've been thinking about the difficult.parts of being a parent. \n\nEven for people with stable and sufficient income, there is still one hurdle: that of having to sacrifice so much time. \n\nWhat scares.me the most about becoming a parent is that it will be very difficult to maintain my identity as a person: my hobbies and interests. \n\nHowever, there might be hope. \n\nWhen I was a kid, I didn't want to be with my parents for too.long. I preferred playing with other kids, reading, doing homework, playing video games. \n\nTherefore, my question is; **can the issue of low birth rates be combated to an extent by a child-friendly infrastructure?**\n\nWhat.is a child friendly infrastructure?\nA city that has many indoor playgrounds, kindergartens, etc, wherein you can safely leave your child to play/read while you go and do your thing. \n\nFor example, if you are a tennis player, to have enough availability of indoor playgrounds that it is easy to take them there (with adult supervision). \n\nThoughts? ", "author": "AmbassadorAdept9713", "created_time": "2024-12-10T14:13:45", "url": "https://reddit.com/r/Futurology/comments/1hb2nxu/can_low_birthrates_be_compared_to_an_extent_by_a/", "upvotes": 2, "comments_count": 58, "sentiment": "bullish", "engagement_score": 118.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb2znn", "title": "<PERSON><PERSON> is not going to be delisted", "content": "0.0023 post dilution, no limit on euronext. 67 billion shares, meaning if it got to €1 the market cap would be normal. Above €2 would be the time for a reverse stock split. No point right now.\n\nIt is part of so much vital infrastructure and hires 90,000 people it also controls the NHS cybersecurity and systems and also the nuclear sector in many European countries. France CANNOT let it fail. \n\nI have 105,000 shares right now hoping for €1 maybe 2. If they don't stock split a bull run could mean millions.\n\nThoughts?", "author": "aaaabbbbbccccccccc", "created_time": "2024-12-10T14:29:25", "url": "https://reddit.com/r/pennystocks/comments/1hb2znn/atos_is_not_going_to_be_delisted/", "upvotes": 2, "comments_count": 37, "sentiment": "bullish", "engagement_score": 76.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb39my", "title": "Google shares rise on new quantum chip breakthrough", "content": "\n\nShares of Google (NASDAQ:GOOGL) saw a 4% increase in pre-open trading Tuesday, reflecting investor confidence following the company's unveiling of its new quantum computing chip, Willow<PERSON> <PERSON><PERSON>, CEO of Google, introduced the chip on Monday, highlighting its ability to significantly reduce computational errors and its performance in benchmark tests.\n\nPichai emphasized the chip's potential in practical applications, stating, \"We see <PERSON> as an important step in our journey to build a useful quantum computer with practical applications in areas like drug discovery, fusion energy, battery design + more.\"\n\nThe new chip, according to <PERSON><PERSON><PERSON>, has achieved two major milestones. It can exponentially decrease errors with the addition of more qubits, addressing a quantum error correction challenge that has persisted for nearly three decades. In addition, <PERSON> completed a standard benchmark computation in less than five minutes, a task that would take one of the fastest supercomputers today more than 10 septillion years.\nThis development is part of Google's long-term commitment to quantum computing, a vision that began over a decade ago when <PERSON><PERSON><PERSON> founded Google Quantum (NASDAQ:QMCO) AI in 2012. \n\nThe goal is to build a quantum computer that leverages quantum mechanics to advance scientific discovery and address societal challenges.\n\nThe announcement drew a simple yet telling reaction from Tesla (NASDAQ:TSLA) CEO <PERSON><PERSON>, who expressed his astonishment with a single word, \"wow,\" on social media platform X.\n\nThe Willow chip represents a significant advancement for Google Quantum AI and positions the company closer to achieving commercially viable quantum computing applications.", "author": "Guy_PCS", "created_time": "2024-12-10T14:42:37", "url": "https://reddit.com/r/stocks/comments/1hb39my/google_shares_rise_on_new_quantum_chip/", "upvotes": 497, "comments_count": 186, "sentiment": "bullish", "engagement_score": 869.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb3mzn", "title": "I employ 40 people, here's my top 5 pieces of advice for business owners", "content": "I employ 40 people across two different businesses. These range from mostly employees in my painting business, to about a 50% split between employees and contractors in my SAAS...\n\nAs a leader of so many with very little upper management leadership (I am the only upper management in my painting business) I rely heavily on people making sound decisions without the need for constant input from me. I host very little meetings and collaborate mostly via slack.\n\nHere's my top 5 pieces of advice for new business owners when it comes to management and leadership.\n\n1. **Create space for people to make mistakes** \\- in my painting business, there's plenty of mistakes that happen, of course, more so in my painting business than SAAS specifically because we are working people's homes.\n\nI rarely dwell on mistakes, instead, I've created a culture of solution driven crew leaders that if they call me, they're already prepared with a few solutions - at that point, my only job is to help guide them on which one makes the most sense from my perspective (even though i'm not on the job)\n\nDoing this overtime, I've probably limited the \"what do I do\" texts, calls, and messages by 70% because my team members know they're not going to lose their job if a mistake happens.\n\n2. **Stop outsourcing leaders at early stages** \\- I tried to hire in \"managers\" for certain roles, and although they have their place, for me, my experience is to give people an opportunity to manage. I know in some sectors it requires experience, but for me, I like to leverage loyalty. I think loyalty drives growth more than anything and the best way to garnish loyalty is to give opportunity to people.\n\nIn my painting business, all 4 of my crew leaders have never managed anything in their lives - however, I saw something in them when they were painters that prompted me to give them a chance at managing.\n\nFrom there, two of those crew leaders are now project managers.\n\nThis fosters a culture of growth that allows my team to see that growth is achievable. Honestly, if I were to bring someone in to manage a crew it would probably hurt morale.\n\n3. **It's Cliche, but hire for character, train for skill.**\n\nOn average, my customer success applications for my SAAS get 450 applications on LinkedIn within the first 3 days - it's literally impossible to filter these applicants with the linked in tools, so we funnel them into an internal form - the form is built entirely off of about 80% situational questions that help us see how the applicants will handle tough situations...\n\n\"A customer is threatening to cancel their account because no one answered their request for help on the intercom messenger - how do you handle this?\"\n\nSure, a little experience in customer success helps, but you can really learn a lot by letting people answer these types of questions.\n\n4. **Be accessible - get off your podium**\n\nI used to work for a bank that had about 400 employees - the CEO was this illusive ghost that only showed his face at corporate events. He wasn't a \"team guy\" he was just a guy that made high level decisions - people kind of feared him.\n\nI remember at an awards event, he had an assistant raddle off the awards, and like a puppet he would hand the awards to the winners and shake their hands. He didn't even know the people he was handing awards to. It was awkward.\n\nI vowed to never be that way. I don't care if you are QA or you are a prepper on a paint job. I want to shake your hand, or get to know you, or have a conversation with you - i know at scale this is hard, but in meetings, at the end of a meeting (especially a big one) i always say \"I'm an open book, send me a message, don't be a stranger. Even if it's just to say what's up\"\n\nI don't really know the impact because I don't ask, I just know that if i were an employee it would take the pressure down a notch.\n\n5. **Care about people**\n\nI can't teach you how to care about people. It's an innate thing. Possess empathy. I've had people in some of the most crucial times call out of work because of a life thing that happened. Dog died, family member issue, kid got in trouble at school, mental health day... idk all sorts of things.\n\nThere's no paint emergencies, and there's no SAAS emergencies.\n\nLife happens - everything can be fixed tomorrow.\n\nI always have my team's back, in some situations, i'd even step in to go paint, or fulfill a role in customer success, do a demo, or whatever is required to help the team. Again, i haven't polled people on this, but I will say that if i were an employee, i'd appreciate this\n\nTLDR: 5 things to help you lead a better organization and create a culture of happiness", "author": "Byobcoach", "created_time": "2024-12-10T14:59:28", "url": "https://reddit.com/r/Entrepreneur/comments/1hb3mzn/i_employ_40_people_heres_my_top_5_pieces_of/", "upvotes": 752, "comments_count": 93, "sentiment": "bullish", "engagement_score": 938.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb3yfb", "title": "As Clean Tech Industry Gains Influence, Can Climate Law Survive Trump?", "content": "With billions in investment flowing to GOP districts, <PERSON>’s plans to repeal clean energy tax credits face Republican resistance. In an interview with Yale E360, political scientist <PERSON> talks about the new politics of renewable energy. [Read more](https://e360.yale.edu/features/leah-stokes-interview).\n\nhttps://preview.redd.it/ynhkwa5ve16e1.jpg?width=1600&format=pjpg&auto=webp&s=bb71ec8f1b09de1658353343091b898b103d7ca1", "author": "YaleE360", "created_time": "2024-12-10T15:13:15", "url": "https://reddit.com/r/Renewable/comments/1hb3yfb/as_clean_tech_industry_gains_influence_can/", "upvotes": 5, "comments_count": 0, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb3yjv", "title": "As Clean Tech Industry Gains Influence, Can Climate Law Survive Trump?", "content": "With billions in investment flowing to GOP districts, <PERSON>’s plans to repeal clean energy tax credits face Republican resistance. In an interview with Yale E360, political scientist <PERSON> talks about the new politics of renewable energy. [Read more](https://e360.yale.edu/features/leah-stokes-interview).\n\nhttps://preview.redd.it/pd1duz0we16e1.jpg?width=1600&format=pjpg&auto=webp&s=536770e499c5dbeab344c6883a674f36f325a9f3", "author": "YaleE360", "created_time": "2024-12-10T15:13:23", "url": "https://reddit.com/r/CleanEnergy/comments/1hb3yjv/as_clean_tech_industry_gains_influence_can/", "upvotes": 3, "comments_count": 4, "sentiment": "neutral", "engagement_score": 11.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb6haw", "title": "I made my clients grow, I have good results, but I feel stagnant...", "content": "I was recently analyzing my clients' advertising accounts and checking the results and I noticed very good results. For example, clients who started with me earning less than 1k/day and now earn more than 40k, among other smaller and less eye-catching cases.\n\n\n\nBut the point is, even with all these results, my earnings are not growing. I have tried to renegotiate several times and even so, there is always resistance, especially because of the entire structure of campaigns and audiences that I have already set up, which the client can simply \"fire\" me and continue to maintain them with someone who charges less.\n\n\n\nAnd this is becoming an extremely frustrating situation. To give you an idea, one of my clients currently earns more than 6M/month including website, marketplaces and internal orders and what I receive does not even reach 20k. (These values ​​are in <PERSON><PERSON>, he is from Brazil. If you convert to dollars, this client pays me less than 4k dollars/month).\n\n\n\nI've tried to find new clients on sites like Upwork, Workana and others, but people don't even respond to me.\n\n\n\nI send screenshots of my Google Ads and Facebook Ads Dashboards, showing the results I have in real time and I feel really marginalized, because people think they are fake results.\n\n\n\nI've tried posting videos in groups and the admins don't approve the publication of the videos, even though I show the Dashboards being updated in real time on my monitor, recording directly with my cell phone.\n\n\n\nI haven't done and don't do such extraordinary work, but even so, I feel like I'm treated like a fake all the time or undervalued by the market where I work.\n\n\n\nHow do you deal with this type of situation?\n\n\n\nIn short, I have good results and only my clients grow and I don't. I try to attract new clients and I'm ignored, the same when I try to interact with others in my field.\n\n\n\n\\*P.S: I've tried boosting my Instagram posts with videos from the GA-4 dashboard, Google Ads and Facebook/Meta Ads, and I've gotten zero results. Only people who are skeptical or businesses that are too small and have no growth potential are looking for me.", "author": "Savage_M4chine", "created_time": "2024-12-10T17:02:32", "url": "https://reddit.com/r/PPC/comments/1hb6haw/i_made_my_clients_grow_i_have_good_results_but_i/", "upvotes": 558, "comments_count": 84, "sentiment": "neutral", "engagement_score": 726.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb6v3c", "title": "Save or Buy? Advice please", "content": "Quick context: I have been doing lots of pros and cons and asking around but it doesn’t hurt to get more opinions. In short, I am deciding to buy a new car or save my money and buy a car later on. \n\nI am trying to move out with my girlfriend by January of 2026 (a year on out) and I have been saving about $2k a month for a down payment (expected to have about 37k-40k by that time) for a condo in Las Vegas, Nevada. I make about 76k a year and the remainder of my expenses look like:\n\n$4.5k a month \n$1k towards rent and utilities\n$2k towards home savings\n$300 towards expenses\n$450 towards investing\n$700 remainder\n\nI drive a 2007 dodge charger and it usually runs pretty fine. Requires the occasional oil change and isn’t the most fuel efficient but it gets me around fine. I don’t find it breaking down often unless it is for things such as spark plugs and other things that were just due for change due to it being used up for its life. It has about 167k miles and I know it will soon be time for a new car. This current one is paid off and drivers insurance is super low. I’m debating to buy a used tesla 2023 model 3 because of the ev tax credit for about 25k and lower insurance costs with Tesla insurance. I would be financing this at a 5.7% interest rate and at a 60-72 month loan period. With $6k down, It gets me a low monthly payment now but I will be able to use my bonus and tax returns to put more of a lump sum, then once the condo is bought I will have more money freed up to pay the car off much sooner. There are no fees to pay my loan sooner.  \n\nMy question is I wonder if it is the right time to make the move on this or should I wait till closer to the middle of next year for potentially more depreciation, save for a larger down payment? Are there any other flaws in my plan. I have looked into other vehicles and the insurance costs in the Vegas area are insanely high. It may be because I am pretty young (21). The Tesla insurance was the most affordable. \n\nYikes… that wasn’t that short but I appreciate anyones input and maybe help me get on the right track. \nThank you. \n\n", "author": "Traditional_Sale_355", "created_time": "2024-12-10T17:18:37", "url": "https://reddit.com/r/FinancialPlanning/comments/1hb6v3c/save_or_buy_advice_please/", "upvotes": 1, "comments_count": 2, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb77xu", "title": "34 trying to leave an abusive situation but have no savings and in debt", "content": "34m\n\nI’ll skip the relationship part and just say that I was cheated on, but have nowhere to go so I’m still here with her.\n\nI’ve been paying bills solo for the last 4 months since she lost her job.  This has put me in a huge financial bind, as I have $3000 in loans and credit cards, 0$ saved, and nothing of major value to sell.  My dad doesn’t have room for me, my mom passed away last year, and my sister is living in a 2 bedroom house with herself, her husband, 1 toddler and 1 infant.  Only 1 living grandparent and she’s in a nursing home.  Not close enough with aunts or uncles to impose on them.  All friends are married with children and staying with them is unrealistic.\n\nHere is my current financial situation:\n60,000/year salary working remotely\nRoughly 3400/month after deductions \nMonthly bills at home are roughly 3000/month \nDebt: 1900 in a predatory, extremely high interest rate credit line, 1350 in maxed out credit cards \nCredit score is about 650\n\n\nTo answer some obvious questions, I’m not kicking her out because of her child.  The kid doesn’t deserve to suffer for her actions.  There’s a lot to this, but this isn’t the relationship subreddit so I’ll just say I can’t kick her out.\n\nYes I have learned my lesson, and it sucks.\n\nI applied for a consolation loan via my bank and got declined.  Realistically, I’ll need roughly 3000$ to pay off debt and another 2000-2500$ to move into my own place.  That’s not even including buying a bed or furniture.  That’s just a blanket on the floor until I can buy a bed.\n\nAt this point, I am pretty desperate to figure this out.  Just looking for any advice.  Thank you.", "author": "whole_bear1991", "created_time": "2024-12-10T17:33:37", "url": "https://reddit.com/r/personalfinance/comments/1hb77xu/34_trying_to_leave_an_abusive_situation_but_have/", "upvotes": 4, "comments_count": 52, "sentiment": "neutral", "engagement_score": 108.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb78yl", "title": "Help with SS Taxes + TSLA Stock Gains", "content": "State: New York\n\nMy dad is 77 years old, retired, and single. His SS payment is $4200/month. His only assets are a condo (paid in full), about $20k in savings, a car (I gifted it to him, maybe worth $10k), and 30 shares of TSLA (~$12,000). Idk the cost basis of the TSLA shares, it’s probably $100/share so about $3,000.\n\nRight now his income tax liability is $0. I’ve done his taxes with FTUSA two years in a row and it came out to $0 each time so he no longer files. \n\nMy question is how can he sell the TSLA shares without causing tax Armageddon? I talked to ChatGPT and it seemed like if he sold all of his shares, he would end up paying income tax on his SS so he would potentially owe more than he sells the shares for.\n\nObviously he can’t gift me the shares and I give him $12k cash, that’s illegal. What if he gifts me the shares and I make him an AU on one of my CCs and I let him use it however he likes for the rest of his life? Or are there other things he could do to reduce his tax liability? Not trying to do anything shady, just understand the options. \n\nIdeally he can do this next year. TSLA shares are so overvalued it’s comical. And it’s the only real asset he has left other than the small cash account. ", "author": "mrlazyboy", "created_time": "2024-12-10T17:34:51", "url": "https://reddit.com/r/personalfinance/comments/1hb78yl/help_with_ss_taxes_tsla_stock_gains/", "upvotes": 0, "comments_count": 14, "sentiment": "bearish", "engagement_score": 28.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb7lxr", "title": "Tesla Model Y & Model 3 Take Gold & Silver in California Auto Sales (22 Charts) - CleanTechnica", "content": "", "author": "SPorterBridges", "created_time": "2024-12-10T17:49:59", "url": "https://reddit.com/r/electricvehicles/comments/1hb7lxr/tesla_model_y_model_3_take_gold_silver_in/", "upvotes": 1, "comments_count": 13, "sentiment": "neutral", "engagement_score": 27.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hb7ur1", "title": "[D] What’s stopping you from using foundation models for time series forecasting?", "content": "I’ve been experimenting with foundation models like [<PERSON><PERSON>](https://github.com/wearesulie/sulie), [Granite TTM](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r1), and [Amazon Chronos](https://github.com/amazon-science/chronos-forecasting), and each one has its own strengths. What’s really fascinating is how much faster you can get accurate forecasts with a zero-shot approach. However, as much as these models improve forecasting, explainability remains a major challenge compared to more traditional methods like ARIMA, which are simpler to interpret.\n\nI’m curious—do you think explainability is a dealbreaker, or is there another reason why foundation models for forecasting aren’t gaining wider adoption? Would love to hear what’s been your biggest blocker or challenge in using these models.", "author": "Queasy_Emphasis_5441", "created_time": "2024-12-10T18:00:23", "url": "https://reddit.com/r/MachineLearning/comments/1hb7ur1/d_whats_stopping_you_from_using_foundation_models/", "upvotes": 3, "comments_count": 27, "sentiment": "neutral", "engagement_score": 57.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbblg2", "title": "Help me escape from months of back-and-forth with Google Ads support?", "content": "After 8 weeks of suffering through 50+ Google Ad support emails and at least 10 Google Ad support phone calls, I am desperate for tips on how I might find \\*anyone\\* on the Google Ads team who is human and technical enough to understand why an iframe embed does **not** inherently equal \"compromised\" status. \n\nI've outlined my saga in an [excruciatingly long blog post](https://bill.harding.blog/2024/12/05/google-ads-a-2024-glimpse-into-life-as-a-google-customer/) about how our company has been suspended almost two months now in spite of no wrongdoing. We are currently suspended for what they have labeled a policy violation of \"Compromised site.\" In final section of my blog post (which I'm continuing to amend as new updates follow), [I provide a request-by-request analysis of why the page they flagged is **not** evidence of a compromised site](https://bill.harding.blog/2024/12/05/google-ads-a-2024-glimpse-into-life-as-a-google-customer/#Analyzing_whether_Quick_Start:_Commit_Activity_Browser_is_dangerous). (There is also Google's own Web Console tool, which [confirms that neither our site nor the one we link to is \"compromised\"](https://bill.harding.blog/2024/12/05/google-ads-a-2024-glimpse-into-life-as-a-google-customer#Part_II:_Suspended_for__Compromised_site__(3_weeks))) \n\nI recognize that this isn't Google Ads support, but that's sort of the point of posting here. I've been corresponding with their professional support for months now without finding anyone who can explain why they would brand us a \"compromised site\" when **there is no tool anywhere on the web that substantiates their designation of \"compromised site.\"** \n\nIf some hero on this subreddit can suggest to me a course of action that allows us to finallllllly return to posting ads on Google (as we had w/o issue for 4+ years, until this debacle began in October), I would happily purchase and allocate to them whatever reddit-swag-token-stuff $25 could buy (I am not a reddit expert, can you tell? 😜). \n\nThank you for considering the pathetic plight of an earnest small business owner. 🙏", "author": "wbharding", "created_time": "2024-12-10T20:36:49", "url": "https://reddit.com/r/adwords/comments/1hbblg2/help_me_escape_from_months_of_backandforth_with/", "upvotes": 1, "comments_count": 4, "sentiment": "bullish", "engagement_score": 9.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbc2my", "title": "How have you immunized your portfolio?", "content": "So, I'm mostly retired and have spent most of this year fretting about the increasingly expensive US stock market:\n\n* CAPE has risen from 32 at the start of the year to over 38 now\n* TTM PE on S&P 500 has reached 31\n\nI started the year with a modest equity position of about 40%.  Throughout the year I have been performing mental gymnastics trying to find the right bond ETF's, while selling equities and dollar cost averaging back into them.  Last week, I finally decided I need a new plan.  The equity anxiety and randomness of my bond purchases was getting to me.  \n\nI sat down and revised my asset allocation model.  I developed new \"risk-on\", \"neutral\", and \"risk-off\" weightings for each asset class.  Then I designated up to two of my accounts (401k, taxable, traditional IRA for me and wife, Roth-IRA for me and wife) for each asset class.\n\nNow that I reduced my equity exposure to under 20%, I find I'm more relaxed.  I put the rest in a variety of bond ETF's to get decent yield with reasonable risk.\n\nWhat have you done to reduce your risk and/or investment stress?", "author": "CA2NJ2MA", "created_time": "2024-12-10T20:56:54", "url": "https://reddit.com/r/investing/comments/1hbc2my/how_have_you_immunized_your_portfolio/", "upvotes": 3, "comments_count": 46, "sentiment": "bearish", "engagement_score": 95.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbcke8", "title": "GM will no longer fund Cruise’s robotaxi development work", "content": "", "author": "walky<PERSON><PERSON><PERSON>", "created_time": "2024-12-10T21:17:22", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hbcke8/gm_will_no_longer_fund_cruises_robotaxi/", "upvotes": 498, "comments_count": 528, "sentiment": "bullish", "engagement_score": 1554.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbdz5s", "title": "$5.. forever? 😏", "content": "👋🏼 Over the past year, I’ve been diving into software development and product management. Most of my projects have been ambitious and complex (read: nowhere near finished), so I decided to tackle something smaller to gain practical experience.\n\nRecently, I needed to organize my finances for an upcoming move. Instead of creating yet another Google Sheet, I thought, Why not build a simple tool for myself? 🙃\n\nWhat began as a quick personal project quickly escalated. In just a few days, I developed a full app, complete with a licensing system and a (barebones) marketing site. It’s been a fun learning journey, and it feels great to have something tangible out there instead of endlessly tinkering.\n\nThe app is straightforward—it’s an offline finance tool that stores data locally and helps plan finances without relying on bank integrations. While it’s not groundbreaking, it serves my needs and avoids the hassle of dealing with miscategorized transactions.\n\nHere’s where I deviated from the norm: I opted for a $5 lifetime license instead of the typical subscription model. I understand that subscriptions are standard in SaaS, and this approach likely won’t make me rich. However, I wanted to experiment with simplicity and see if a one-time price could still attract interest.\n\nSo far, a few sales have come in, boosting my confidence. But I’m curious: Does this kind of pricing make sense for small, low-maintenance tools like this? Or am I missing the mark by not adopting the subscription model?\n\nI’d love to hear your thoughts on this pricing experiment and any similar experiences you’ve had. Thanks for reading!", "author": "brody<PERSON>ie", "created_time": "2024-12-10T22:18:11", "url": "https://reddit.com/r/startups/comments/1hbdz5s/5_forever/", "upvotes": 0, "comments_count": 21, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbec7c", "title": "Need Advice where to park a few 100k to protect from inflation. ", "content": "53 year old male. Looking for advice on what I can do to protect my retirement savings from inflation. Great if possible to beat the rate of inflation. Buy Have next to zero risk tolerance. \n\nCurrently unemployed, but have a few   thousand a month in side gig that covers monthly expenses. \n\nHave about 800k looking to protect from inflation. \n\nHave close to zero risk tolerance in losing any principle. \n\nHave zero debt.\n\nOwn house car everything.\n\nHave very little bills. Only HOA, insurance, food.  Live very frugal. Eat out rarely.  Only spending is maybe 15k a year on travel. But most all is covered so very rare to ever break into any savings.\n\nHave about 800k that I don't touch. Just looking for some place to park it to protect from inflation.  Currently most is locked up in CD all expiring in 2025.\n\n", "author": "lostandalone2023", "created_time": "2024-12-10T22:34:32", "url": "https://reddit.com/r/investing/comments/1hbec7c/need_advice_where_to_park_a_few_100k_to_protect/", "upvotes": 1, "comments_count": 74, "sentiment": "bullish", "engagement_score": 149.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hber75", "title": "Audited by IRS for Energy Credit on 2022 tax return", "content": "Be sure and keep all your records, but even that might not be enough.  I have a 'correspondence audit' challenging my form 5695 credit on a full rooftop install in 2022.  Must furnish normal things like contract and proof of payment, but also a copy of the city permit, a utility bill, and copies of manufacturers' certifications showing \"the product qualifies for the credit.\"  Luckly, for REC and Enphase I found those certifications online, but insist your installar provide when you pay.  What a pain .. I don't expect to have problems, but it's never fun to get an audit notice from the IRS.  Wonder how many of our billionaire oligarchs get audited for paying nothing? (rant off)", "author": "liberte49", "created_time": "2024-12-10T22:53:12", "url": "https://reddit.com/r/solar/comments/1hber75/audited_by_irs_for_energy_credit_on_2022_tax/", "upvotes": 170, "comments_count": 120, "sentiment": "neutral", "engagement_score": 410.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbess6", "title": "Nitrogen hydroxide fuel ? ", "content": "Generated with regular low efficiency electrolysis ,when neutral air is pumped under the ions solution of electrolyte.  Used in the Outback  by Australian solders returning from WW2. They brought the data back with them after finding the Germans were experimenting with vehicle engines in motor pools. With no power lines in the back lands, the engines powering the generators would self run on this increased energy content fuel gases. Check it out. Nitrogen Hydroxide as an aid for Hydrogen + Oxygen water cells. \n\n<PERSON>, retired engineer of New Zealand factory for electroplating of scissors, had, in the past, US Patent on discs cell of porous aluminum electrodes electrolysis cell with air pumped in from bottom. Dual type fuel gases evolve with the other increased efficiency of \"clean sweep\". Some of the neutral air wipes the stuck ions clean off , so as the maximum efficiency is achieved.  He converted 3 cars to run on water. ", "author": "Putrid-Bet7299", "created_time": "2024-12-10T22:55:12", "url": "https://reddit.com/r/CleanEnergy/comments/1hbess6/nitrogen_hydroxide_fuel/", "upvotes": 1, "comments_count": 2, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbf8vi", "title": "Have I underestimated healthcare costs in retirement by focusing on OOP max?", "content": "Up until now, my method for calculating my healthcare costs in retirement was to basically take my premium at my planned income from [the kff calculator](https://www.kff.org/interactive/subsidy-calculator/#state=ga&zip=30328&income-type=percent&income=199&employer-coverage=0&people=1&alternate-plan-family=&adult-count=1&adults%5B0%5D%5Bage%5D=45&adults%5B0%5D%5Btobacco%5D=0&adults%5B1%5D%5Bage%5D=53&adults%5B1%5D%5Btobacco%5D=0&adults%5B2%5D%5Bage%5D=52&adults%5B2%5D%5Btobacco%5D=0&child-count=0&children%5B0%5D%5Bage%5D=19&children%5B0%5D%5Btobacco%5D=0&children%5B1%5D%5Bage%5D=19&children%5B1%5D%5Btobacco%5D=0&children%5B2%5D%5Bage%5D=17&children%5B2%5D%5Btobacco%5D=0), add in the OOP max and simply assume I'll hit that every year.  Simple right?\n\nOnly, I had a health issue earlier this year, and I've had multiple claims denied.  I'd heard that insurance companies were increasingly doing this, but I had no idea how widespread it was until recent events got everyone talking about *their* denials for things that **should** have been covered.\n\nI used to hear that 2/3rds of bankruptcies were related to medical expenses, and I used to think 'they should have had insurance'.  This was before I realized that **most actually have insurance**.\n\nHonestly, as someone with a disability, and higher than average healthcare costs, this is kind of terrifying to me.  I don't know how I'm supposed to have the confidence to FIRE when an insurance company can simply *decide not to pay* and the patient has little recourse.", "author": "alpacaMyToothbrush", "created_time": "2024-12-10T23:15:11", "url": "https://reddit.com/r/financialindependence/comments/1hbf8vi/have_i_underestimated_healthcare_costs_in/", "upvotes": 247, "comments_count": 165, "sentiment": "neutral", "engagement_score": 577.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hbfgxs", "title": "<PERSON><PERSON><PERSON>. Will Stop Developing Cuise Self-Driving Taxis", "content": "", "author": "EchoStash", "created_time": "2024-12-10T23:25:28", "url": "https://reddit.com/r/AutonomousVehicles/comments/1hbfgxs/gm_will_stop_developing_cuise_selfdriving_taxis/", "upvotes": 10, "comments_count": 1, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}]