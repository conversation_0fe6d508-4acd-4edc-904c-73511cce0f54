[{"platform": "reddit", "post_id": "reddit_1h686ei", "title": "My thoughts on the future. (People in the past loved to predict the future, I wanna do that in these modern times)", "content": "Its about to be 2025, and the acceleration of technological progression has reached insanely new heights. We are getting to the point of there almost not being any original thoughts, is there even a point of predictions if you're not a genius in some field?\n\nWhat even is life anymore? Where am I? WHY WAS I BORN IN TO THE FUTURE? Why wasn't I born 1437 years ago or 2561 years ago or 333 years ago?\n\nWhy I already human in a past life back then? Is this a matrix and we just keep respawning as new human or non-human sperm in present time instantly? Are we able to reincarnate as plants? Is there reincarnation at all?\n\nWill there ever be great philosophers again?\n\nWe are in a whole new time, I will call this the Acceleration Era. Everything is getting faster and faster. WHAT HAPPENS WHEN IT GETS TOO FAST? Do we crash? Theres a boom of AI, whats next quantum AI? How will future humans earn money with less jobs?\n\nIn this day in age theres not much to predict, someone has already predicted or thought of it. You truly have to think outside of the box, and theres not much outside of it\n\nPredictions:\n\nJARVIS. Middle and upper class homes will have a real life JARVIS. AI will get to the point where it can monitor every human in the house from any room in the house and aid them in a variety of ways. Without pressing any buttons or picking anything up you can yell out loud wake me up at 6 pm, and your AI house assistant will do just that.\n\nSurveillance will be so tight that crime will heavily reduce in the future of 1st world countries. There will be AI surveillance set up almost everywhere once it gets cheap enough, though humans will be human and it would obviously\n\nPeople will look to AI for advice on how to handle crime so the people who are brave enough to still commit crime are dealt with\n\nFashion: We will get to a point where the world is so boring that fashion become incredibly exotic, expressing fashion in creative ways will be how far future humans keep their humanity and remember their roots. We could see humans wearing fashion pieces from ancient history as normal fashion pieces as a desperate way of being creative in a world that has done and seen everything", "author": "Commercial-Source568", "created_time": "2024-12-04T04:54:53", "url": "https://reddit.com/r/Futurology/comments/1h686ei/my_thoughts_on_the_future_people_in_the_past/", "upvotes": 0, "comments_count": 14, "sentiment": "bearish", "engagement_score": 28.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h68802", "title": "For those that think the market is overvalued based on historical norms...", "content": "There have been some huge structural changes in our society that make simply comparing basic indicators like historical P/E ratios insufficient in determining whether the market is overvalued or not, the biggest being:\n\n1. We've had over a decade of the fed injecting unprecedented amount of liquidity into the financial system - its not a coincidence that we've seen the biggest bull run in our lifetimes beginning after the 2008 financial crisis, because that's precisely when this started happening with QE. Then this got turbocharged again during the 2020 pandemic. Look at this graph of M2 money supply: [https://fred.stlouisfed.org/series/M2SL](https://fred.stlouisfed.org/series/M2SL) \\- **investable money has nearly 3x since the start of QE**. All that money has to go somewhere, which is why we see the current \"everything\" bubble with stocks and RE all at all time highs. \n\n2. Wealth inequality in our economy has accelerated over the past few decades with the middle class being increasingly hollowed out and companies placing shareholders interests first and employees interests last. The [top 10% of earners own 93% of the stock market](https://finance.yahoo.com/news/wealthiest-10-americans-own-93-033623827.html), these folks don't need the money they invest to fund their day to day lives and are just looking for the best return on their money. \n\nThis is basic supply and demand problem --> there's been a huge increase in demand for investments with the increased money supply (M2 graph shown above) but the supply of investment opportunities have not kept pace and could argue even decreased: \n\n\\- limited increase in housing supply even as total populations grow and economic opportunities get increasingly  concentrated in a small number of metro areas\n\n\\- a digital economy that makes it easier than ever for huge companies to dominate and further entrench their dominance by unlocking economies of scale, leading to fewer total winners - why invest in 5 different specialty retailers when amazon sells everything for cheaper and delivers to you faster?\n\n  \nThe market may very well be overvalued - who knows - but coming to that judgement purely by looking at historical norms is comparing apples to oranges. The fact is that a smaller number of people hold the vast majority of the investable wealth in the US and all that money is looking to make a return somewhere - so with real estate also at all time highs and in many ways even more overvalued than the stock market when comparing the cost to buy vs rent, combined with interest rates coming down, *where else is all that money going to go?*\n\n  \n", "author": "budabudabudabudabuda", "created_time": "2024-12-04T04:57:21", "url": "https://reddit.com/r/ValueInvesting/comments/1h68802/for_those_that_think_the_market_is_overvalued/", "upvotes": 223, "comments_count": 211, "sentiment": "bearish", "engagement_score": 645.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h68jnk", "title": "A friendly reminder to take care of your phone battery.", "content": "I thought you might find this interesting. This is the battery wear chart on an Android phone made with the accubattery pro app. When I just bought the phone I decided to limit the maximum charge level to 80% and auto turn on stamina at 30%. With the intent to charge it between those two values. After a while I got tired of only having 50% of usable battery capacity and got rid of it. On the graph provided you may see exactly when that happened. Now, two and a half years later I have just replaced the battery as is had 82% of usable capacity (A tiny bit early for a replacement if you ask me, but it didn't last with me through the day and I had the opportunity to do it). Could have gone a solid extra 6-12 months if not for giving up early. Definitely going back to that practice after this. ", "author": "AggravatingFun9692", "created_time": "2024-12-04T05:15:05", "url": "https://reddit.com/r/batteries/comments/1h68jnk/a_friendly_reminder_to_take_care_of_your_phone/", "upvotes": 0, "comments_count": 24, "sentiment": "neutral", "engagement_score": 48.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h68wnv", "title": "Do people hate prompting? Or do they just not know how to use AI?", "content": "I am an avid user of AI and have found it to be extremely helpful in several areas of my life. I even decided to start a Saas business providing users access to tailored prompts for people to use to help them generate content, come up with ideas, analyze reports etc. However, I am noticing that a lot of the products people are raving about are products that obfuscate the prompt and/or use the API to do all of the prompting on the backend so the user just gets the output after filling out a few text fields.\n\nIs this you all's experience? Do you prefer having someone present a form rather than just plugging in a prompt into ChatGPT or is it that you think people just don't really know how to use LLMs or prompt? I'm genuinely curious what you all's take on this is.\n\nThanks in advance", "author": "Alternative_End_5295", "created_time": "2024-12-04T05:35:56", "url": "https://reddit.com/r/artificial/comments/1h68wnv/do_people_hate_prompting_or_do_they_just_not_know/", "upvotes": 0, "comments_count": 16, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6clhw", "title": "Opinion | Saving Electric Vehicles From <PERSON> (Gift Article)", "content": "", "author": "nytopinion", "created_time": "2024-12-04T09:59:32", "url": "https://reddit.com/r/environment/comments/1h6clhw/opinion_saving_electric_vehicles_from_donald/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "environment", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6earu", "title": "Even ExxonMobil is telling Trump to tone it down on fossil fuels", "content": "", "author": "chrisdh79", "created_time": "2024-12-04T11:56:40", "url": "https://reddit.com/r/environment/comments/1h6earu/even_exxonmobil_is_telling_trump_to_tone_it_down/", "upvotes": 1962, "comments_count": 37, "sentiment": "neutral", "engagement_score": 2036.0, "source_subreddit": "environment", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6hxbv", "title": "A universal ‘Plug and Charge’ protocol for EV charging is coming in 2025 | No more fobs, no more app signups. Just plug in and start charging.", "content": "", "author": "chrisdh79", "created_time": "2024-12-04T14:55:40", "url": "https://reddit.com/r/electricvehicles/comments/1h6hxbv/a_universal_plug_and_charge_protocol_for_ev/", "upvotes": 1121, "comments_count": 207, "sentiment": "neutral", "engagement_score": 1535.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6kbvn", "title": "Cloud Solution for a non-profit", "content": "Hi Everyone,\n\nMy company is a non-profit currently using Afinety (which provides managed IT solutions for the legal industry). Afinety provisions Amazon Workspaces for all of our employees, as well as Microsoft 365 and a few other managed programs. We would like to get away from using Afinety, as reaching them for support can be such a headache, and the services are in the range of $30k a month (we have about 250 users). \n\n  \nWhat are some viable alternatives I can look at? I have a computer science degree but do not have any particular cloud expertise other than the basics (I am a data analyst). Would this be something I can do myself, or are there other companies that can do similar which you recommend? Thank you.", "author": "dba_cooper", "created_time": "2024-12-04T16:32:54", "url": "https://reddit.com/r/cloudcomputing/comments/1h6kbvn/cloud_solution_for_a_nonprofit/", "upvotes": 1, "comments_count": 0, "sentiment": "bullish", "engagement_score": 1.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6leff", "title": "Intellicheck, Inc. (NasdaqGM:IDN)", "content": "IDN is a \\~$50mm company securing contracts with some of the world’s largest corporations, all while being surrounded by a moat wider than the Grand Canyon. With 90%+ margins and a clear path for reaccelerated growth, it’s hard to see how this stock won’t exceed expectations. But let me first introduce you to the idea.  \n  \nData breaches are surging. The recent United Healthcare breach exposed data on roughly a third of all Americans. For \\~$20, this stolen data is available on the dark web, and for just \\~$40 more, you can get a visually undetectable by law enforcement fake ID. But who cares about manual checks anymore, right? Surely computers can catch it all.  \n  \nWrong. Every competitor relies on OCR templating, a method with detection rates ranging from 65% to 75%. Claims of higher accuracy? Don’t trust them. In contrast, IDN has a \\~99.9% detection rate, thanks to its longstanding relationships with the AAMVA and DMVs.  \n  \nSo, why does this opportunity exist? Growth has decelerated, but I've examined the causes and uncovered a relationship that dictates an imminent reversal. Importantly, there is a hard catalyst too. During onboarding, strict NDAs prevent IDN from disclosing the identity of new clients, but sometimes the company gives some clues.  \n  \nTake 2020, for example. IDN secured a contract with a multinational financial services company that \"provides innovative payment, travel, and expense management solutions for individuals and businesses of all sizes.\" A quick copy-paste into Google revealed it was American Express. By April 2021, the stock had quadrupled.  \n  \nNow, the multinational company IDN signed a contract with is not a card issuer, but \"one of the largest social media platforms in the world.\" The setup looks familiar, but will history repeat?\n\nhttps://preview.redd.it/be7i3qc67v4e1.png?width=1700&format=png&auto=webp&s=ddc5907dc647cc63d9a39f8bb85a1548ab76eea0", "author": "realLigerCub", "created_time": "2024-12-04T17:15:21", "url": "https://reddit.com/r/SecurityAnalysis/comments/1h6leff/intellicheck_inc_nasdaqgmidn/", "upvotes": 19, "comments_count": 1, "sentiment": "bullish", "engagement_score": 21.0, "source_subreddit": "SecurityAnalysis", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6libp", "title": "Tesla Named 'Deadliest Car Brand in America' in Bombshell Car Safety Study", "content": "", "author": "PostHeraldTimes", "created_time": "2024-12-04T17:19:37", "url": "https://reddit.com/r/economy/comments/1h6libp/tesla_named_deadliest_car_brand_in_america_in/", "upvotes": 1074, "comments_count": 98, "sentiment": "neutral", "engagement_score": 1270.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6ls72", "title": "Best cloud options for web \"scraping\"?", "content": "I'm a self-taught hobbyist programmer new to the cloud. My job is not in software. I wrote a web scraping script to automate the most tedious aspect of my job. I run it locally 19 hours/day every day. It doesn't download or upload any data, hence why I put scraping in quotes. It's more about automation. What it does:  \n1) Login to company portal  \n2) Click the appropriate buttons based on what's on the screen  \n3) Refresh screen.  \n4) Go to step 2 or step 5 depending on if there's new data on the screen.  \n5) sleep for up to a minute.  \n6) Go to step 3.  \nRight now, I run this script only for myself, but I'm sure I could get some customers from people who use the same company portal for their job. I looked into AWS, but it seems prohibitively expensive. I'd like to learn about the best options for my use case. Can anyone help me out with this? Thanks!\n\n", "author": "mor<PERSON>ter", "created_time": "2024-12-04T17:30:27", "url": "https://reddit.com/r/cloudcomputing/comments/1h6ls72/best_cloud_options_for_web_scraping/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6m4ng", "title": "Tesla suspends Cybertruck production. Who could have predicted this?", "content": "", "author": "Legitimate_Hand2867", "created_time": "2024-12-04T17:43:51", "url": "https://reddit.com/r/technews/comments/1h6m4ng/tesla_suspends_cybertruck_production_who_could/", "upvotes": 3247, "comments_count": 247, "sentiment": "neutral", "engagement_score": 3741.0, "source_subreddit": "technews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6mf12", "title": "Got an interview with JXM Enterprise, anyone know them? ", "content": "They offered me a business sales associate position in their Lawndale office.\n\nI tried to do some research but noting came up, I'm just checking for red flags mainly. I don't know if it's commission based or what. \n\nBut I want to know before we meet tomorrow. \n\nI'm weary cause I've almost fallen for MLMs before and devil corps but I'm checking with you guys. You are the experts. \n\nThank you! ", "author": "Armandocubes", "created_time": "2024-12-04T17:54:56", "url": "https://reddit.com/r/business/comments/1h6mf12/got_an_interview_with_jxm_enterprise_anyone_know/", "upvotes": 0, "comments_count": 12, "sentiment": "bearish", "engagement_score": 24.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6q2q9", "title": "How to prevent climate deniers from getting in the way of decarbonization ", "content": "Here are the two key methods \n\n1. Mention that fossil fuels impede energy security and can cause geopolitical instability \n\n2. Advocate for non-intermittent alternative energy sources \n\nClimate change deniers emotional fetish for fossil fuels is why they cannot understand how climate change works. You cannot use logic to reason with someone who does not think logically in the first place. Climate change denial is a much more serious issue than many people think because the people who deny anthropological climate change will always have the ability to vote for politicians who also deny anthropological climate change. \n\nIf someone is mentally incapable of understand one reason for something then using another reason would is the logical course of action. ", "author": "Live_Alarm3041", "created_time": "2024-12-04T20:20:37", "url": "https://reddit.com/r/CleanEnergy/comments/1h6q2q9/how_to_prevent_climate_deniers_from_getting_in/", "upvotes": 11, "comments_count": 4, "sentiment": "neutral", "engagement_score": 19.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6qtps", "title": "[D] Packaging a Pytorch model to an exe. What is the best method?", "content": "I have Pytorch models that are designed to run locally, both training and inference on a local machine.\n\nThe GUI is being created using another language, and the plan is to package all the Python aspects into an executable and run it via the Python equivalent of subprocess (and Pipe very basic data between the two). I will be running cross platform on both Windows and Mac\n\nThere are multiple auxiliary scripts which read in data, and process it (data extraction + feature engineering). While I have extensively used vectorised functions, I have used a cythonized approach for some code, and I am compiling the underlying scripts using Cython(so pretty much everything is a compiled binary, except an entry point, say, main.py).\n\nMy ancillary libraries are the usual suspects, Pandas, Numpy (1.x), SciKit learn.\n\nMy question is this, what is the most reliable packaging approach at the moment? I know that both PyInstaller and cx\\_freeze are options that I have used before. My preference is PyInstaller, but previously I encountered issues with it (and Pytorch).\n\nHas anyone completed a similar project recently, and do you have any advice?\n\nnb. I've checked the old posts, there are a few on this topic. However, there have been a number of changes to Pytorch, particularly with some of the runtime compiled elements (which can be a nightmare on <PERSON> with its notarisation process) - and I know Pyinstaller has a very active user base.\n\n ", "author": "Solid_Company_8717", "created_time": "2024-12-04T20:51:34", "url": "https://reddit.com/r/MachineLearning/comments/1h6qtps/d_packaging_a_pytorch_model_to_an_exe_what_is_the/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6qy2d", "title": "Google ad suspension help", "content": "Hey there,\n\nWas looking at getting some advice, as I tried doing some searches and cannot find anything that goes over my current situation.\n\nMy google ads were suspended for circumventing policy. I believe because I had linked to same website as other offices.\n\nWhen I try appealing, I am having issues.\n\nI registered my company in \"ABC 123 Inc.\" name. But I operate my business kind of like a franchise model were I operate as \"Company - Main Branch\". I setup my google business account as \"Company - Main Branch\" due to advertising regulation in the industry I am in. I am licensed to \"Company - Main Branch\" and have a business contract with them.\n\nBecause my articles of incorporation are under \"ABC 123 Inc.\" they wont remove my suspension.\n\nNot sure if there is any work around this or if I just likely won't be able to use google ads to market my business. Due to nature of industry I work in, I cannot advertise for my incorporated company while working with \"Company - Main Branch\"\n\nHoping to get any suggestions or personal experiences.", "author": "Ill-List6707", "created_time": "2024-12-04T20:56:25", "url": "https://reddit.com/r/adwords/comments/1h6qy2d/google_ad_suspension_help/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6r360", "title": "I feel like 'life expectancy' for anyone under 50 is inaccurate for first world countries ", "content": "When I hear people saying \"life expectancy is 80\" etc is only applicable for people close to that age.\n\n10-20 years from now I think that could change drastically, maybe even sooner and as more time goes on it will continue to change. The higher the life expectancy gets, the more time we have for it to go higher.\n\nI hear people talk alot about the future tense being similar conditions to present, I dont hear anyone consider things like this.\n\nOr am I secluded in this thinking?", "author": "ZapppppBrannigan", "created_time": "2024-12-04T21:02:06", "url": "https://reddit.com/r/Futurology/comments/1h6r360/i_feel_like_life_expectancy_for_anyone_under_50/", "upvotes": 0, "comments_count": 57, "sentiment": "neutral", "engagement_score": 114.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6r3hy", "title": "Full remarks from <PERSON><PERSON> at Mar-a-Lago, on November 14, 2024.", "content": "[Source](https://x.com/ElonClipsX/status/1864332495375778070):\n\n> “I guess someone's got to be the <PERSON> on the right. I mean, I'd call myself <PERSON> of the center, I think\n\n> We obviously need to have just the basics. As President <PERSON> rightly said, what we need is the restoration of common sense.\n\n> We want safe cities, we want secure borders, sensible spending, freedom of speech, respect for the Constitution.\n\n> If that's <PERSON> of the right, that's me.\n\n> I'm incredibly excited about the future. The public has given us a mandate that could not be more clear. It's the clearest mandate.\n\n> Winning the popular vote, clearly, the House, the Senate, the Electoral College by a mile, a majority of governorships, a majority of House legislatures, all Republican.\n\n> The people have spoken. The people want change. And we're going to give it to them. They will get what they asked for.\n\n> I think this is going to be the most transformative presidency, perhaps, since the founding of the country.\n\n> And President <PERSON> is picking the Cabinet to do it.\n\n> You can see that the choices are people who are actually going to make change. It's not going to be business as usual.\n\n> We're going to shake things up. It's going to be a revolution, and we're headed towards, I think, a fantastic future.\n\n> I'm super excited. I look forward to experiencing that with everyone. Thank you.”", "author": "twinbee", "created_time": "2024-12-04T21:02:28", "url": "https://reddit.com/r/elonmusk/comments/1h6r3hy/full_remarks_from_elon_musk_at_maralago_on/", "upvotes": 71, "comments_count": 377, "sentiment": "neutral", "engagement_score": 825.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h6rnay", "title": "People are sleeping on Block Inc (SQ)", "content": "I am not going to do a full deep dive here but my main points would be:\n\n1. Business optionality (Square, CashApp, Bitcoin initiatives) - its rare for a company trading at this valuation to have so many possible businesses that could take off.\n2. Forward multiple is very cheap (forward P/E of less than 20).\n3. Healthy balance sheet (net cash of more than $4billion.\n4. High level of insider ownership, and founder guided company.\n5. Consistent revenue growth.\n\nDisclaimer: I am long SQ and bought around \\~$65. I still think it is cheap and may look to add to my position over the next few months.", "author": "Snight", "created_time": "2024-12-04T21:24:59", "url": "https://reddit.com/r/stocks/comments/1h6rnay/people_are_sleeping_on_block_inc_sq/", "upvotes": 10, "comments_count": 44, "sentiment": "bullish", "engagement_score": 98.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}]