{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-03-10", "api_calls": {"load_local_social_media_data": {"data": [{"title": "I've read through many posts in this sub but I still can't make up my mind on office 365. Please guide me.", "content": "I recently purchased a surface laptop for personal use (studying and entertainment). When I say study I mean ACCA, not with a university/school so no edu email.  Now, I mostly use OneNote because that's where I have all my notes separated by subject etc. I very rarely use word and excel but i still want them there incase I randomly end up needing them. \n\nNow my question... Based on the above, should I just buy the the one time purchase of office 2024 or should I still consider 365 personal?  I see most people recommend purchasing 365 in the posts I saw but I'm not sure how it wouldn't be a waste of money in my particular case. I wish I had access to copilot but I don't think I would be using it enough in MS office to justify $99 per year.  \n\nWhat do y'all think?\n", "created_time": "2025-03-10T02:25:11", "platform": "reddit", "sentiment": "bullish", "engagement_score": 50.0, "upvotes": 32, "num_comments": 0, "subreddit": "unknown", "author": "MarioDF", "url": "https://reddit.com/r/microsoft/comments/1j7odkm/ive_read_through_many_posts_in_this_sub_but_i/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Rejected or not?", "content": "<PERSON> did microsoft loop interview for Technology Specialist Internship last week about 6 days ago.  Interview I think went well, interviewers gave me quite a good feedback after each round.  But i an starting to get worried as it has been quite long, do I reach out to recruiters or my interviewer on linkedin? Is it normal or I am getting too paranoid. Thank you very much guys", "created_time": "2025-03-10T13:11:52", "platform": "reddit", "sentiment": "bullish", "engagement_score": 88.0, "upvotes": 24, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1j7y9jm/rejected_or_not/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "I am using windows 8.1 pro WMC. If i dont want to upgrade can i still use it for my daily use?", "content": "Please an advice is needed.", "created_time": "2025-03-10T13:41:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Turbulent-Ad415", "url": "https://reddit.com/r/microsoft/comments/1j7yv0k/i_am_using_windows_81_pro_wmc_if_i_dont_want_to/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Storm-0558", "content": "Is there any way the Storm-0558 attack on Microsoft could have affected my iPhone?  Other than having a personal Hotmail account, I don’t see there’s a connection.  But the timing of the attack on Microsoft and what I saw happening on my iPhone were exactly the same, and I doubt that was a coincidence. ", "created_time": "2025-03-10T15:14:38", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "OneToughTexan2", "url": "https://reddit.com/r/microsoft/comments/1j80xqx/storm0558/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Microsoft is holding a 50th anniversary and Copilot event in April", "content": "", "created_time": "2025-03-10T19:04:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 64.0, "upvotes": 58, "num_comments": 0, "subreddit": "unknown", "author": "samiy2k", "url": "https://reddit.com/r/microsoft/comments/1j86je3/microsoft_is_holding_a_50th_anniversary_and/", "ticker": "MSFT", "date": "2025-03-10"}, {"title": "Microsoft discontinuing Publisher is a shame", "content": "I know Publisher is not as powerful as InDesign. I know Publisher is not as easy as Canva. However, Publisher is a fairly robust desktop publishing app for people who need it, with a relatively gentle learning curve. \n\nI used Publisher regularly for over 20 years. I only recently started using InDesign out if necessity. But Publisher is still what I show people at work who need more flexibility than Word and more control than programs like Canva and Adobe Express. \n\nI have not used Microsoft Designer yet, but it seems to be going after Canva's market rather than the professional designer. ", "created_time": "2025-03-08T01:43:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 126.0, "upvotes": 46, "num_comments": 0, "subreddit": "unknown", "author": "movieguy95453", "url": "https://reddit.com/r/microsoft/comments/1j667ex/microsoft_discontinuing_publisher_is_a_shame/", "ticker": "MSFT", "date": "2025-03-08"}, {"title": "Nearly 1 million Windows devices targeted in advanced “malvertising” spree | Malware stole login credentials, cryptocurrency, and more from infected machines.", "content": "", "created_time": "2025-03-08T03:59:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 29.0, "upvotes": 27, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j68oqa/nearly_1_million_windows_devices_targeted_in/", "ticker": "MSFT", "date": "2025-03-08"}, {"title": "What do you guys think of Microsoft shutting down Skype in May this year?", "content": "I find it kinda sad but I get the point. I remember always calling my friends on Skype right after school to play some Roblox. I haven't used it for a few years now but was still kinda shocked that it is going.\n\nWhat do you think? I am curious what others think.\n\n[https://www.microsoft.com/en-us/microsoft-365/blog/2025/02/28/the-next-chapter-moving-from-skype-to-microsoft-teams/](https://www.microsoft.com/en-us/microsoft-365/blog/2025/02/28/the-next-chapter-moving-from-skype-to-microsoft-teams/)", "created_time": "2025-03-07T16:32:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 306.0, "upvotes": 46, "num_comments": 0, "subreddit": "unknown", "author": "RoytjePoytjeGamez", "url": "https://reddit.com/r/microsoft/comments/1j5sqs5/what_do_you_guys_think_of_microsoft_shutting_down/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "Microsoft Explores AI Partnerships Beyond OpenAI: Testing xAI, Meta, and DeepSeek Models for Copilot Innovation", "content": "", "created_time": "2025-03-07T17:39:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Common_Sleep_5777", "url": "https://reddit.com/r/microsoft/comments/1j5up1b/microsoft_explores_ai_partnerships_beyond_openai/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "New iOS OneDrive is terrible.", "content": "Just recently updated the OneDrive app on my phone. The new UI is not appealing and I’m pretty sure they got rid of the option to take photos to add to a folder. I use OneDrive for work. It allows me to share on-site photos with my managers. Before the update it was super simple. Create folder > share folder > open camera > take pics and have them immediately uploaded. It was great because it kept my work photos separate from my own personal photos. Since the update I have to take the pics using my iPhone’s camera app, then go to OD and select the pics that I just took (because I’m not allowing full access to Photos). Why does Microsoft always find a way to make things more complicated??", "created_time": "2025-03-07T18:06:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 45.0, "upvotes": 23, "num_comments": 0, "subreddit": "unknown", "author": "FY00Z", "url": "https://reddit.com/r/microsoft/comments/1j5vgl5/new_ios_onedrive_is_terrible/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "It is so nice being able to just walk away from a job", "content": "My organization is moving to 5 days in office starting at the beginning of next month.  I'm currently applying for other jobs but if I don't find anything by then, I'll simply quit. I will not pointlessly put up with a 3 hour round trip commute and office politics for 5 days a week.  It is a waste of gas, time, and will make me miserable.  WFH has tremendously boosted my quality of life.  Giving it up just because some pinhead in the ivory tower said so is stupid and I just will not do it.  That is why it is so nice to have enough money to be able to just walk away from a job when the amount of BS gets to be too much.   ", "created_time": "2025-03-07T18:16:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1373.0, "upvotes": 1147, "num_comments": 0, "subreddit": "unknown", "author": "stan<PERSON>", "url": "https://reddit.com/r/financialindependence/comments/1j5vpyf/it_is_so_nice_being_able_to_just_walk_away_from_a/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "Why is Microsoft so slow to innovate on Windows and Surface Pro?", "content": "\nHey everyone,\n\nFIRST OF ALL, it’s my own opinion. If you think that I’m wrong let me know why ! \n\nI use a Surface Pro 11, and when comparing it to iPads, I can’t help but wonder: Why doesn’t Microsoft release more impactful and innovative updates for Windows and Surface Pro?\n\nI mean, of course it is not easy to innovate everytime but isn’t Microsoft supposed to be Apple’s rival ? \n\nApple releases a new iPadOS every 2-3 years with major new features (e.g., Stage Manager, Pencil improvements, UI redesigns).\n\nMicrosoft, on the other hand, mostly pushes minor Windows 11 updates, often limited to stability fixes and small adjustments (except for Copilot recently, but that’s more AI-focused than a real UI/features revolution).\n\nEven Surface Pro devices receive very few updates that enhance the touch experience, multitasking, or UI. While Surface Pro have so much potential imo ! \n\n\n\nDoes Microsoft simply not want to push innovation on Windows and Surface like Apple does with iPadOS ?\nOr is it because Windows has to remain compatible with too many different devices?\n\nI’d like to know if other users also feel this stagnation and whether they hope Microsoft will speed up its innovation pace. What do you guys think ?\n\n\n\n", "created_time": "2025-03-07T18:51:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 106.0, "upvotes": 12, "num_comments": 0, "subreddit": "unknown", "author": "Diablo1511", "url": "https://reddit.com/r/microsoft/comments/1j5wnfu/why_is_microsoft_so_slow_to_innovate_on_windows/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "Former Xbox boss admits the company once \"encouraged\" the console wars, which he believes \"were healthy for the industry\" as \"a rising tide that lifted all ships\" | But it's not \"the old days\" anymore", "content": "", "created_time": "2025-03-07T20:52:09", "platform": "reddit", "sentiment": "neutral", "engagement_score": 267.0, "upvotes": 245, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j6026m/former_xbox_boss_admits_the_company_once/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "IC4 Level 63 salary range for NYC metro - should I push for 64?", "content": "I just got an offer for an IC4, level 63. The offer is in the mid $180k range, the position put the NYC metro range between $155k and $220k.  I have 21 years' experience, 6 and a half in Azure engineering. No manager roles, 3 years as a senior Azure engineer, and in the past 3 years as a senior sys admin. The recruiter mentioned a sign-on bonus split between first paycheck and 2nd anniversary paycheck, RSUs, stock awards, company bonus, and team bonus. I've got that on verbal and am awaiting the written offer.\n\nDo I have any leverage to push for higher? The recruiter said this was the max for level 63 and anything higher would need approval, but I'm not sure if I qualify for higher levels or what the approval looks like. \n\nIt's definitely a decent step up for me but I'm a bit worried they're lowballing. I didn't see anything close to this title or team on levels.fyi. I don't know if my background in non-tech companies is an issue or what else, so any guidance would be appreciated. ", "created_time": "2025-03-07T23:49:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 67.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "MohnJaddenPowers", "url": "https://reddit.com/r/microsoft/comments/1j63xa7/ic4_level_63_salary_range_for_nyc_metro_should_i/", "ticker": "MSFT", "date": "2025-03-07"}, {"title": "MS licensing questions for the legacy program, and some partner doubts!", "content": "Guys, my buddy at an smb has some questions about MS licensing.\n\nI did my reading and digging through MS portals to give him some ideas, then I thought you guys would love this topic since everyone loves to talk about MS licensing here so much .. ./s\n\nPlease could yall help me out a lil to make sense of these questions for us and share some \"behind the scenes\" info not mentioned in kb articles about this MS licensing stuff? Also some gotcha moments with MS licensing from your experience?\n\n**He's not really a redditor so here I am posting for him:**\n\n1. The Microsoft Legacy program is ending soon and with it some included and free Microsoft licenses to server and desktop Microsoft applications. What cost effective options are out there for a current Microsoft Legacy program partner to get the similar level of free server and desktop Microsoft licenses without the ability to get a lot of new certifications and sales\n2. If you have Microsoft Server Datacenter 2022 installed, and then you create 5 virtual server environments running Microsoft Server 2022 Standard, then do you need to purchases licenses for the virtual server environments if they are running Microsoft Server 2022 or 2019 Standard?\n3. Can you buy all three Partner Launch, Partner Success Core, and Partner Success Expanded for a single partner global account? Can you confirm that this would effectively allow the partner to have 24 Microsoft Visual Studio Professional Subscription as an example. Can you confirm by sending a link that shows this is possible\n4. Confirm that, with the Partner Success packages, the licensing allows us to use any older version of the software?\n5. Confirm that the Partner Success packages include server software?\n\n  \n**UPDATE:**\n\n  \nAfter a bit of digging we have this info so far, could anyone take a look and confirm this?\n\n**1. The Microsoft Legacy program is ending soon and with it some included and free Microsoft licenses to server and desktop Microsoft applications. What cost effective options are out there for a current Microsoft Legacy program partner to get the similar level of free server and desktop Microsoft licenses without the ability to get a lot of new certifications and sales.**\n\n* We can't find any docs which refers to this \"legacy program\" is this referring to \"Microsoft Action Pack subscription\"? This just stopped in Jan 2025.\n* So the only way is to move to one of the launch, success core, or success expanded packages?\n\n  \n**2. If you have Microsoft Server Datacenter 2022 installed, and then you create 5 virtual server environments running Microsoft Server 2022 Standard, then do you need to purchases licenses for the virtual server environments if they are running Microsoft Server 2022 or 2019 Standard?**\n\n* Licensing is based on the host not on the VMs if you have DC 2022 license you can run unlimited VMs on it the min requirements are you need to license all 16 cores per server even if you have fewer cores?\n\n**3. Can you buy all three Partner Launch, Partner Success Core, and Partner Success Expanded for a single partner global account? Can you confirm that this would effectively allow the partner to have 24 Microsoft Visual Studio Professional Subscription as an example. Can you confirm by sending a link that shows this is possible?**\n\n* Yes we can buy all 3 launch, success core, success expanded packages but **ONLY 1 OF EACH INDIVIDUALLY** \\- so we cannot buy 2 of launch/SC/SE - I can't find any doc which confirms that yes we can buy all 3 but the part about buying only 1 of each is well documented in the [FAQ on every partner benefit page](https://learn.microsoft.com/en-us/partner-center/membership/partner-launch-benefits#can-i-buy-more-than-one-partner-launch-benefits).\n* And ones we buy all 3 can we use all 24 of the visual studio professional licenses? We can't find any info which confirms if we can merge benefits like these, is this against TOS?\n\n**4. Confirm that, with the Partner Success packages, the licensing allows us to use any older version of the software?**\n\n* The answer is we can only use the version of the software until the license expires and then we are expected to upgrade, but then once we upgrade do we again buy licenses?\n* Found this in the \"[terms of participation](https://assetsprod.microsoft.com/mpn-maps-product-usage-guide.pdf)\" pdf:\n   * Each partner is responsible for tracking their own consumption and entitlement of product benefits. If a partner organization is selected for a compliance audit, that organization is responsible for presenting records regarding the active program licenses used and compliance with the terms of use. **Licenses do not provide downgrade rights or any other Software Assurance services.** For deployment, management, and other similar services, learn more about Software Assurance.\n* What type of software does this even apply to? SCCM? SCEP?\n\n**5. Confirm that the Partner Success packages include server software?**\n\n* Yes it includes server management tools like:\n   * Windows 365 Enterprise – 8 vCPU, 32 GB RAM, 512 GB Storage\n   * System Center Client Management Suite (2022)\n   * System Center Endpoint Protection (2019)\n   * Windows Server CALs (not edition-specific)\n   * Windows Server Datacenter – Per core (2022)\n   * Windows Server Standard – Per core (2022)", "created_time": "2025-03-06T01:01:18", "platform": "reddit", "sentiment": "bullish", "engagement_score": 53.0, "upvotes": 47, "num_comments": 0, "subreddit": "unknown", "author": "masterofrants", "url": "https://reddit.com/r/microsoft/comments/1j4jcbn/ms_licensing_questions_for_the_legacy_program_and/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "How Can I Get Involved in Volunteer Opportunities with Microsoft?", "content": "Hello, Microsoft community!\n\nI’m interested in gaining experience by volunteering in roles related to customer service, moderation, or support for Microsoft products and services. I’m eager to help out and contribute my time and skills without the expectation of payment—just looking to gain hands-on experience.\n\nAre there any volunteer opportunities with Microsoft, or ways to get involved in these areas remotely? Any guidance or suggestions would be greatly appreciated!\n\nThank you in advance!", "created_time": "2025-03-06T01:31:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "IdeaSprout22", "url": "https://reddit.com/r/microsoft/comments/1j4jyv2/how_can_i_get_involved_in_volunteer_opportunities/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "AA Round or not?", "content": "Hi recently i went through an interview loop. At the final round it was a super senior person with 25 years of experience in microsoft (not sure his title). Is this the as appropriate round? The interview was quite nice and relaxed, basically a try to get to know me, whats your learning process, questions about my internship, any client facing roles you have etc Thank you everyone!!!!!  ", "created_time": "2025-03-06T02:24:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 26.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1j4l1j7/aa_round_or_not/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Microsoft 365 Family without AI for $99.99 a year is still available", "content": "\\[This is for US subscriptions\\] \n\nI forgot I had OneDrive full of data and had to go back and signup for another month of Microsoft 365 Family at $12.99. I had canceled my subscription two weeks ago. I renewed for a month, went back to cancel the rebill and it offered M365 Family with AI for $99.99/year. Altogether it billed me another $107.49.\n\n  \nI went back to cancel that rebill to avoid paying $129.99/yr + taxes next year. Clicked 'Cancel Subscription' and when the page loaded it offered to switch to monthly at $12.99 but right below, highlighted in bright yellow, was the 'LOWER COST WITHOUT AI' option. M365 Family Classic $99.99/year for 6 people, and a link to 'Buy at $9.99/month'\n\nElsewhere someone said switching like that will tack a year of M365 Family, so in March 2026 I would get billed $99.99+taxes for service without AI until March 2027.", "created_time": "2025-03-06T21:26:07", "platform": "reddit", "sentiment": "bullish", "engagement_score": 40.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "brozelam", "url": "https://reddit.com/r/microsoft/comments/1j56ld1/microsoft_365_family_without_ai_for_9999_a_year/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Xbox update resetting consoles to factory settings, Insiders say", "content": "", "created_time": "2025-03-06T21:45:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 81.0, "upvotes": 59, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j5725h/xbox_update_resetting_consoles_to_factory/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Audit tool against Best Practices", "content": "Hi All,\n\nI’m currently working for a MSP that’s looking after a few clients that consume a whole host of Microsoft 365 products and services, such as: exchange online, m365 apps, intune, teams, SharePoint, OneDrive, entra, security etc. \n\nI was wondering if there’s a tool out there - whether it’s 1 tool or a few tools combined, that can provide me with a host of recommendations to update/check against a tenant? \n\nThis would be ideal if it also generated a report that had all the good/bad/ugly in the tenant. \n\nI am thinking along the lines of ORCA but more feature and service rich.\n\nKeen to hear how you guys do it. \n\nThanks. ", "created_time": "2025-03-05T03:39:19", "platform": "reddit", "sentiment": "bullish", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "fungusfromamongus", "url": "https://reddit.com/r/microsoft/comments/1j3u0y6/audit_tool_against_best_practices/", "ticker": "MSFT", "date": "2025-03-05"}, {"title": "Who is responsible for the Hiring Decision?", "content": "Hi everybo<PERSON>, I went to interview loop with Microsoft for Technology Specialist Intern, went for two interviews back to back, I think feedback was great. I think the last interview the person was significantly more senior. But who actually makes the hiring decision, is it <PERSON><PERSON> or the last guy in the interview loop? Thank you everyone", "created_time": "2025-03-05T03:41:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 32.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1j3u2en/who_is_responsible_for_the_hiring_decision/", "ticker": "MSFT", "date": "2025-03-05"}, {"title": "Microsoft 365 is so bad", "content": "Shifted to a new company that uses Microsoft 365.\nEverything in the suite is a hot pile of garbage.\n\n- Teams doesn’t send mobile notifications\n- outlook is extremely unintuitive and search sucks\n- I can’t manage separate mails according to custom tags, need to follow the stupid coloring system. If there is a way it’s hidden somewhere and couldn’t find out where even after googling it\n- one note is horrible, almost never pastes images on one attempt, syncing always has issues.\n- SharePoint is its own UI made for people who hate finding files.\n- copilot is absolutely useless, unable to hold context\n- calendar feels like a Nokia era calendar, doesn’t seem to integrate with personal accounts or other providers\n\nBasically everything sucks and yet they keep shipping new features without addressing any of the complaints in the existing software.", "created_time": "2025-03-05T21:57:09", "platform": "reddit", "sentiment": "neutral", "engagement_score": 36.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "No-Equipment5090", "url": "https://reddit.com/r/microsoft/comments/1j4f8kl/microsoft_365_is_so_bad/", "ticker": "MSFT", "date": "2025-03-05"}, {"title": "Microsoft has introduced Dragon Copilot, designed to assist with clinical workflows.", "content": "[Meet Microsoft Dragon Copilot: Your new AI assistant for clinical workflow - Microsoft Industry Blogs](https://www.microsoft.com/en-us/industry/blog/healthcare/2025/03/03/meet-microsoft-dragon-copilot-your-new-ai-assistant-for-clinical-workflow/)", "created_time": "2025-03-04T04:18:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 16, "num_comments": 0, "subreddit": "unknown", "author": "Inevitable-Rub8969", "url": "https://reddit.com/r/microsoft/comments/1j32i5f/microsoft_has_introduced_dragon_copilot_designed/", "ticker": "MSFT", "date": "2025-03-04"}, {"title": "New Microsoft 365 outage impacts Teams, causes call failures", "content": "", "created_time": "2025-03-04T04:55:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 126.0, "upvotes": 94, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j335or/new_microsoft_365_outage_impacts_teams_causes/", "ticker": "MSFT", "date": "2025-03-04"}, {"title": "Microsoft sunsetting Skype on May 2025", "content": "A huge respect and a humble tribute to <PERSON><PERSON>. Only millennials would know the importance. This was our “Teams”, “Slack”, “discord”, “WhatsApp”", "created_time": "2025-03-03T05:10:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 81.0, "upvotes": 51, "num_comments": 0, "subreddit": "unknown", "author": "gyardgain", "url": "https://reddit.com/r/microsoft/comments/1j2b84u/microsoft_sunsetting_skype_on_may_2025/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Microsoft: Official Support Thread", "content": "This thread was created in order to facilitate easy-to-access support for our Reddit subscribers. We will make a best effort to support you. We may also need to redirect you to a specialized team when it would best serve your particular situation. Also, we may need to collect certain personal information from you when you use this service, but don't worry -- you won't provide it on Reddit. Instead, we will private message you as we take data privacy seriously.\n\n### Here are some of the types of issues we can help with in this thread:\n\n* Microsoft Support: Needing assistance with specific Microsoft products (Windows, Office, etc..)\n\n* Microsoft Accounts: Lockouts, suspensions, inability to gain access\n\n* Microsoft Devices: Issues with your Microsoft device (Surface, Xbox)\n\n* Microsoft Retail: Needing to find support on a product or purchase, assistance with activating online product keys or media, assistance with issues raised from liaising with colleagues in the Microsoft Store.\n\nThis list is not all inclusive, so if you're unsure, simply ask.\n\n### When requesting help from us, you may be requested to provide Microsoft with the following information (you'll be asked via private message from the MSModerator account):\n\n* Your full name (First, Last)\n\n* Your interactions with support thus far, including any existing service request numbers\n\n* An email address that we can use to contact you\n\nThank you for being a valued Microsoft customer.\n\n*For previous Support Threads, please use the [Support Thread](https://msft.it/61699qDDXf) flair.*", "created_time": "2025-03-03T12:25:25", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10027.0, "upvotes": 43, "num_comments": 0, "subreddit": "unknown", "author": "MSModerator", "url": "https://reddit.com/r/microsoft/comments/1j2hi7e/microsoft_official_support_thread/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Has any US based MSFT employee (who is not an EU citizen) been able to get an internal role/sponsorship for an EU role?", "content": "I’m looking at a few internal roles in Dublin and Barcelona and curious what the odds are for a current US based worker (and US citizen) to actually get sponsored to move internally abroad. \n\nI know I have to interview and still get the role but was curious if anyone has done this and had success or if anyone had any tips. \n\n", "created_time": "2025-03-03T13:48:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 48.0, "upvotes": 16, "num_comments": 0, "subreddit": "unknown", "author": "The_Federal", "url": "https://reddit.com/r/microsoft/comments/1j2j15j/has_any_us_based_msft_employee_who_is_not_an_eu/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Azure M365 Outage", "content": "Anyone feeling the effects of a wide area outage?", "created_time": "2025-03-03T17:13:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 60.0, "upvotes": 30, "num_comments": 0, "subreddit": "unknown", "author": "swlci", "url": "https://reddit.com/r/microsoft/comments/1j2nqfu/azure_m365_outage/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Microsoft Offer Timeline - Action Center Status Change", "content": "Hey everyone, I was just curious—if you’ve received an offer from Microsoft, how long did it take for your application status in the Action Center to change from Scheduled to Completed? I had finished 3 rounds of interview last Thursday so it’s only been 2 business days.\n\n", "created_time": "2025-03-03T17:16:08", "platform": "reddit", "sentiment": "bullish", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1j2nspo/microsoft_offer_timeline_action_center_status/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Am I the only one struggling with Microsoft's UI, or is everyone on the same page?", "content": "I've been using Google Workspace for a while but recently decided to shift to the Microsoft ecosystem mainly because it's cheaper. Thought it wouldn't be a big deal, but from yesterday, I’m just completely fucked up.\n\nEverything feels unnecessarily complicated, settings are all over the place, and the UI just doesn't feel intuitive. Outlook, Teams, OneDrive—nothing feels as smooth as Google's ecosystem. Am I missing something, or is this just how it is?\n\nHas anyone else gone through this transition? How long does it take to get used to it?\n\n4o", "created_time": "2025-03-03T20:35:32", "platform": "reddit", "sentiment": "bullish", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ishibam97", "url": "https://reddit.com/r/microsoft/comments/1j2spjy/am_i_the_only_one_struggling_with_microsofts_ui/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Microsoft Product Design Intern Status Update", "content": "Has anyone heard back after the Feb 11th interview for the Product Design Intern position? I know some people got offers for the Azure and BIC teams, but was wondering if anyone heard back for other teams. If not, has your status changed to \"completed\" in the Microsoft Career portal or does it still say \"scheduling\"? Freaking out.", "created_time": "2025-03-03T21:50:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ListenExtension1620", "url": "https://reddit.com/r/microsoft/comments/1j2ui2b/microsoft_product_design_intern_status_update/", "ticker": "MSFT", "date": "2025-03-03"}], "metadata": {"timestamp": "2025-07-06T21:15:03.570840", "end_date": "2025-03-10", "days_back": 7, "successful_dates": ["2025-03-10", "2025-03-08", "2025-03-07", "2025-03-06", "2025-03-05", "2025-03-04", "2025-03-03"], "failed_dates": ["2025-03-09"], "source": "local"}}}}