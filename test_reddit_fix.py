#!/usr/bin/env python3
"""
测试Reddit API修复
简化版本用于验证401错误修复
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv

# 导入修复后的Reddit收集器
from reddit_live_collector import RedditLiveCollector, load_reddit_config

def setup_logging():
    """设置简单的日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )

def test_reddit_collection():
    """测试Reddit数据收集"""
    print("=== 测试Reddit API修复 ===\n")
    
    try:
        # 加载配置
        config = load_reddit_config()
        print("✓ Reddit配置加载成功")
        
        # 创建收集器
        collector = RedditLiveCollector(config, "test_social_media_data")
        print("✓ Reddit收集器创建成功")
        
        # 测试单个子版块
        print("\n测试收集 r/stocks 数据...")
        
        # 设置较短的时间范围进行测试
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)  # 只收集最近1天的数据
        
        posts = collector.collect_from_subreddit(
            'stocks', 
            start_date, 
            end_date, 
            limit=5  # 只获取5个帖子进行测试
        )
        
        if posts:
            print(f"✓ 成功收集到 {len(posts)} 个帖子")
            for i, post in enumerate(posts[:3], 1):  # 显示前3个帖子
                print(f"  {i}. {post['title'][:50]}...")
                print(f"     作者: {post['author']}, 分数: {post['upvotes']}")
                print(f"     股票: {post.get('tickers', [])}")
        else:
            print("⚠️ 没有收集到相关帖子（可能是时间范围内没有相关内容）")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    setup_logging()
    
    print("开始测试Reddit API修复...")
    success = test_reddit_collection()
    
    if success:
        print("\n🎉 Reddit API修复测试成功！")
        print("现在可以运行完整的数据收集脚本了。")
        return 0
    else:
        print("\n❌ Reddit API修复测试失败！")
        print("请检查错误信息并进行进一步调试。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
