[{"platform": "reddit", "post_id": "reddit_1iv9xtm", "title": "What can I do to distance my Tesla from Elon ?", "content": "I feel extremely guilty that my $$$ have gone towards funding this Narcissist. But unfortunately I cannot get rid of this vehicle as of now. Everytime I enter it, a feeling of disgust enters me. A true feel of violation. \n\nI'm working my butt off to be able to get rid of this Tesla and get a Kia. In the meanwhile what can I do to put distance between the Tesla I drive and that awful person. I've already bought a few \"I bought this car before <PERSON>on went nuts\" sticker, anything else I can do to \"clean\" my image ?", "author": "bbrk9845", "created_time": "2025-02-22T03:55:04", "url": "https://reddit.com/r/electricvehicles/comments/1iv9xtm/what_can_i_do_to_distance_my_tesla_from_elon/", "upvotes": 506, "comments_count": 1419, "sentiment": "neutral", "engagement_score": 3344.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivep05", "title": "Political posts are currently not allowed", "content": "Governments have the authority to assign place names based on their own conventions, whether for domestic or international locations. These changes are reflected in mapping services, including Google Maps, which follows the naming policies of the country where the map is rendered. Google Maps itself does not make these decisions, but simply mirrors the official names as designated by the relevant authorities.\n\nWe understand that name changes like this can be sensitive, and we appreciate that people may have strong feelings about the matter. If you wish to provide feedback to Google regarding this change, you can do so at the following link:  \n\n[Google Maps Feedback](https://support.google.com/maps/answer/3094045)\n\nAll further posts that are politically motivated complaints will be removed. There are simply too many duplicate posts voicing the same issue. Leave feedback to Google at the link above, or ideally complain to your government representatives who are allowing these changes on your behalf.\n\nThank you for your cooperation and understanding.", "author": "Empyrealist", "created_time": "2025-02-22T09:02:17", "url": "https://reddit.com/r/GoogleMaps/comments/1ivep05/political_posts_are_currently_not_allowed/", "upvotes": 1, "comments_count": 16, "sentiment": "bullish", "engagement_score": 33.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iveywv", "title": "Scientists spent 10 years on a superbug mystery - Google's AI solved it in 48 hours | The co-scientist model came up with several other plausible solutions as well", "content": "", "author": "chrisdh79", "created_time": "2025-02-22T09:22:52", "url": "https://reddit.com/r/Futurology/comments/1iveywv/scientists_spent_10_years_on_a_superbug_mystery/", "upvotes": 1436, "comments_count": 126, "sentiment": "neutral", "engagement_score": 1688.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivf6i7", "title": "Am I right to assume that google is listening to my microphone?", "content": "hi everyone,\n\nlong story short, I was talking to a classmate of mine and he told me that he bought some product. I looked said product up on the school computer. it was a robot vacuum but thats not what this is about lol. school computer (running linux btw) ofc is not signed into my accounts, or anything that would allow tracking that leads back to me.\n\nmy phone was in my pocket during this conversation. It was online using cellular. i have google assistant disabled. i have my microphone permissions very locked down, basically only allowing calling apps to access it when needed.\n\nthis morning i got an ad on reddit for the exact same product he was talking about. \n \ni never searched for anything similar before. i didn't look it up on my phone.\n\nmy only assumption why i got this ad, that's from a totally different category of products I usually get ads for, is that my phone listened in on this conversation. \n\nam i imagining this or is this what actually happened? i know it's absolutely possible from a technical perspective.\n\nhow can i prevent this from happening? apparently opt-out doesn't work, locking down permissions doesn't work. i'm guessing the only thing I can do is not carry my phone around anymore?\n\nwould love to hear your experience with this.  \n", "author": "True_Tumbleweed_3740", "created_time": "2025-02-22T09:38:21", "url": "https://reddit.com/r/privacy/comments/1ivf6i7/am_i_right_to_assume_that_google_is_listening_to/", "upvotes": 314, "comments_count": 195, "sentiment": "neutral", "engagement_score": 704.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivi9vm", "title": "Million dollar idea, no funds, where do I start? I will not promote", "content": "I feel so stuck, I have had an idea for well over a year now, and part of me wants to just drop it, but something in me will not allow me to do so.\n\nI found a huge gap in a certain service space that I am very confident will get traction. It would be web/app based. I have no idea where to start, I have a family and bills like everyone else and lack of extra money to hire people etc.\n\nThe problem is I know for a fact as soon as it comes to to life there will be clones shortly after, I also know there is a huge hole that can be capitalized with the world completely lacking it.\n\nI will not promote\n\nEdit: I can see the downvotes pouring in. That is absolutely okay, I have gotten a boat load of information, and it has very much helped me map some of my next steps.\n\nThank you to everyone who has contributed actual information and along with advice/motivation instead of slandering. The reality is that we all need to start somewhere, and this is one of the places I started and do not regret it one bit.  ", "author": "ScoutTheStankDog", "created_time": "2025-02-22T13:04:00", "url": "https://reddit.com/r/startups/comments/1ivi9vm/million_dollar_idea_no_funds_where_do_i_start_i/", "upvotes": 92, "comments_count": 268, "sentiment": "bearish", "engagement_score": 628.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivm232", "title": "Google's Alleged Discrimination: Economic Consequences of Corporate Unaccountability", "content": "https://www.indiatoday.in/law/supreme-court/story/supreme-court-ex-google-employees-plea-religious-discrimination-2603751-2024-09-20\n\nAs a former Google worker, I've witnessed firsthand the economic consequences of corporate unaccountability. Despite being a high-performing Googler, I faced discrimination and retaliation due to my identity as a kashmiri muslim ,ultimately leading to my exit from the company.\n\nThis experience raises important questions about the economic implications of corporate wrongdoing:\n\n- How do discriminatory practices affect employee productivity and retention?\n- What are the economic costs of corporate unaccountability, and who bears the burden?\n- How can we promote greater transparency and accountability in corporate governance to prevent such incidents?\n\nShare your thoughts on the economic consequences of corporate unaccountability and how we can work towards creating a more equitable and just economy.\"", "author": "Beneficial_Apple9506", "created_time": "2025-02-22T16:07:13", "url": "https://reddit.com/r/economy/comments/1ivm232/googles_alleged_discrimination_economic/", "upvotes": 0, "comments_count": 0, "sentiment": "bearish", "engagement_score": 0.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivqest", "title": "Have my first lawsuit hearing Monday.", "content": "I own a business that custom paints high end bicycles. These paint jobs start at $1000 and I've done them all the way up to $12,000.\n\nLast June a customer of mine was able to use UPS to steal their completed frame and ghost me. \n\nThis customer sent in their bike frame, a 3T Exploro gravel bike frame. He wanted an American flag paint job. (In hindsight, this felt appropriate (s)). The paint job was completed some time in May of 24. We reached out to the customer to let them know the bike was completed and sent them a link for payment. The customer said they were out of the country and would pay when they returned. Odd but whatever. When our jobs are completed we box them up and put a shipping label on them. This is so we can include shipping to the customer. We are next door to a UPS drop location so when a customer pays we walk their box over and off it goes. \n\nA few weeks ago by and this frame is still in the shop, annoying but not crazy uncommon. I am out of the state at an event my business sponsors and I get a notice that the frame has been picked up by UPS. I quickly call my shop and ask about it. My employee said that UPS came in saying they had a scheduled pick up and at that moment we only had the one box waiting. My employee assumed the customer had paid so she let UPS take it. Now I don't blame my employee. This isn't how things normally work, she was alone in the shop, and assumed I had set this up. I was annoyed but was not too concerned. I contacted the customer and asked if they had scheduled a pick up, they denied knowing anything about it. At that time I believed them but now I suspect they orchestrated this whole thing. After that email the customer cut off all communication and blocked me. Through tracking I saw the frame was delivered to his stated address in Memphis but that was all. For the next few weeks UPS would come in asking where the pick up was that was scheduled. When looking at the requests they all had some version of my name as the customer. I told UPS to never pick up from my store, that I would always go next door to drop off. \n\nAt the time this happened I assumed the customer would end up paying. Over the 11 years I've owned my business my customers often have felt like friends, so it took me a while to realize I have been stolen from. I even paid for back ground checks to see if the customer had died (has happened before) or was in jail. I also went to local Facebook groups in Memphis asking if anyone knew this person. \n\nFinally this fall I decided it was time to take this customer to court. I am in St Louis County MO and all the filing and paperwork was really easy. I am sueing him in small claims so now lawyer. I think I'm in for $75.00 and a few hours of work. \n\nI expect the customer won't show up so I'm ready to file a writ of execution to have property seazed to pay the debts owed. \n\nSo far I feel like the effort and cost has been worth it. I don't want to just roll over and accept this kind of treatment and am willing to do it just for the principle of it. Never been to court other than for a couple basic traffic tickets but I have all the documentation and conversations in text. I rarely talk with customers on the phone. So I think it should be fairly straight forward. \n\n\n\nEDIT 1:\n\nHad court today and defendant didn't show. Not surprised. Won the judgement. \n\nI then went downstairs and filed a civil levy (what they call it in St Louis County.) Once that is completed that will go to the Shelby County Sheriff's levy division. I need to call them to go through the steps they require. In Shelby county they seem to call it a writ of execution. In St Louis they said they use that term for landlords??? 🤷\n\nSpent 30 min in court and 30 min filing the levy. \n\nTo answer some comments that were being asked or stated often. \nWe have started taking 50% down payments on work. This event wasn't the top reason why, but it was part of the reason. Cash flow was the biggest reason. \n\nNo I still print out shipping labels. I wear lots of hats and it is just easier to do this once than to quote the shipping and then come back and do it again. This way of theft cannot happen again. It has been dealt with. \n\nI didn't get mad at my employee because in the end it is my fault. I did not create processes to handle something like this. That has been taken care of. \n\nSo far the time and money spent on this is worth it to me because fuck this dude. He's the one who is in the wrong. Sure there were mis-steps by others but he is the thief. \n\nI'll update again when I know more. Might be a couple months though. ", "author": "illinihand", "created_time": "2025-02-22T19:09:10", "url": "https://reddit.com/r/smallbusiness/comments/1ivqest/have_my_first_lawsuit_hearing_monday/", "upvotes": 793, "comments_count": 211, "sentiment": "bearish", "engagement_score": 1215.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivru30", "title": "Position update with charts/levels & prediction for $SB", "content": "I have added to my position since my last post, Im now up to 225 contracts at an average price of 0.072 per con. Also added a couple crayon drawings, red line is the exp date of the options. $5.50 or higher by 2nd week of June", "author": "GMEVISIONARY", "created_time": "2025-02-22T20:10:51", "url": "https://reddit.com/r/pennystocks/comments/1ivru30/position_update_with_chartslevels_prediction_for/", "upvotes": 11, "comments_count": 23, "sentiment": "neutral", "engagement_score": 57.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivslyf", "title": "autonomous race car", "content": "I wanna build an autonomous race car for my graduation project, any tips on where to start? it would be a mini race car and its reactive kinda like a tesla u can say", "author": "maralq29", "created_time": "2025-02-22T20:45:13", "url": "https://reddit.com/r/AutonomousVehicles/comments/1ivslyf/autonomous_race_car/", "upvotes": 1, "comments_count": 5, "sentiment": "neutral", "engagement_score": 11.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ivssn2", "title": "I Built a 100% free, GDPR-Compliant cookie consent alternative for Devs! Sick of all the paid options being so pricey.", "content": "", "author": "LordS<PERSON>uts", "created_time": "2025-02-22T20:53:42", "url": "https://reddit.com/r/webdev/comments/1ivssn2/i_built_a_100_free_gdprcompliant_cookie_consent/", "upvotes": 9, "comments_count": 18, "sentiment": "neutral", "engagement_score": 45.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}]