[{"platform": "reddit", "post_id": "reddit_1is3ocv", "title": "I just raised $1.5M - I will not promote", "content": "The purpose of this post is not meant to brag, but to seriously get some feedback, insight and advice. \n\nI’ve been working on a startup for a few years, have a co-founder, now a small team (8 employees). We bootstrapped to $250k in ARR and closed a round in 4Q24 that included a few VC funds and angels. We will likely grow to $1M or $2M in ARR over the next 12mo. So very early, still figuring things out, but for the most part I’m very grateful for things. \n\nBut if I’m being honest, I have no idea what I’m doing and I constantly feel a sense of falling behind. We never have enough capital, never enough people, product is always behind, something is always breaking, i always want more revenue, and I feel as if it’s an endless cycle of figuring things out or biting more than we can chew. \n\nMeanwhile every day I see headlines and other founders at our stage acting as if they have it all figured out. As if each day is calculated, planned, and executed to perfection.\n\nDoes anyone else feel this way? Am I crazy? Is this just part of the “founder journey?”\n", "author": "Extension-Cold2635", "created_time": "2025-02-18T03:52:26", "url": "https://reddit.com/r/startups/comments/1is3ocv/i_just_raised_15m_i_will_not_promote/", "upvotes": 580, "comments_count": 165, "sentiment": "bearish", "engagement_score": 910.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1is7ytm", "title": "How do I prevent negative reviews on Google Business Profile?", "content": "Hey all- I run a small business in United States and currently the process to collect review is, I blast out an email to all my customers to leave a review.\n\nThis works quite well but except sometimes some angry customer leaves a 5 star review. Often times it’s something we could have easily fixed but they never talk to us. Instead they directly leave a review. What are some ways to prevent this?\n\n**Edit**: Took the advice to use a review management system to gate negative reviewed and signed up for visihero. Thanks everyone", "author": "Particular-Will1833", "created_time": "2025-02-18T08:21:53", "url": "https://reddit.com/r/smallbusiness/comments/1is7ytm/how_do_i_prevent_negative_reviews_on_google/", "upvotes": 29, "comments_count": 49, "sentiment": "bearish", "engagement_score": 127.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isa5ao", "title": "Mexico threatens lawsuit against Google over \"Gulf of America\" name on Google Maps | Mexico's president suggested North America be renamed América Mexicana", "content": "", "author": "chrisdh79", "created_time": "2025-02-18T11:00:23", "url": "https://reddit.com/r/technology/comments/1isa5ao/mexico_threatens_lawsuit_against_google_over_gulf/", "upvotes": 5679, "comments_count": 325, "sentiment": "neutral", "engagement_score": 6329.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isbnbj", "title": "Google Maps used to be so good...What happened?", "content": "I remember when Google Maps reached its peak performance around 2-3 years ago. Now it's a complete mess.\n\nSome key points:\n\n- \"Search along route\" feature is gone (why?)\n\n- If I look for a business/shop whatever, it will zoom out to a 500km+ range and show me businesses or shops that are extrmely far away. To find the one that is nearby, I have to zoom in again and pick it\n\n- Speed camera feature: Nice idea, but terrible implementation. The whole bottom screen is covered, until the timer to confirm that there's a speed camera is gone. \n\n- Routing feature: If I select an alternative route, it will still try to re-route me onto the initial route. Even if I actively chose to avoid tolls etc.\n\n- Language feature: I used to be able to choose English, but only for the voice guidance. Now I have to choose English for the entire app, and while using it, it will change my entire phone to English.", "author": "mark01254", "created_time": "2025-02-18T12:33:32", "url": "https://reddit.com/r/GoogleMaps/comments/1isbnbj/google_maps_used_to_be_so_goodwhat_happened/", "upvotes": 203, "comments_count": 83, "sentiment": "bullish", "engagement_score": 369.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isdsh2", "title": "Google Starts Tracking All Your Devices As Chrome Changes", "content": "", "author": "lurker_bee", "created_time": "2025-02-18T14:23:22", "url": "https://reddit.com/r/technology/comments/1isdsh2/google_starts_tracking_all_your_devices_as_chrome/", "upvotes": 2874, "comments_count": 463, "sentiment": "neutral", "engagement_score": 3800.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iseeqz", "title": "Google DeepMind CEO says for AGI to go well, humanity needs 1) a \"CERN for AGI\" for international coordination on safety research, 2) an \"IAEA for AGI\" to monitor unsafe projects, and 3) a \"technical UN\" for governance", "content": "", "author": "MetaKnowing", "created_time": "2025-02-18T14:52:06", "url": "https://reddit.com/r/singularity/comments/1iseeqz/google_deepmind_ceo_says_for_agi_to_go_well/", "upvotes": 771, "comments_count": 156, "sentiment": "neutral", "engagement_score": 1083.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isfoo4", "title": "After 5 years in consulting, I believe AI Data Analyst will be there to end junior consultant suffering", "content": "After half a decade in data consulting, I’ve reached a conclusion: **AI could (and should) replace 90% of the grunt work I did as a junior consultant**\n\nHere’s my rant, my lessons, and what I think needs to happen next\n\nMy rant:\n\n* As junior consultants, we were essentially workhorses doing repetitive tasks like writing queries, building slides, and handling hundreds of ad hoc requests—especially before client meetings. However, with\n* We had limited domain knowledge and often guessed which data to analyze when receiving business questions. In 90% of cases, business rules were hidden in the clients' legacy queries\n* Our clients and project managers often lacked awareness of available data because they rarely examined the database or didn't have technical backgrounds\n* I spent most of my time on back-and-forth communications and rewriting similar queries with different filters or aggregate functions\n* Dashboards weren't an option unless clients were willing to invest\n* I sometimes had to take over work from other consultants who had no time for proper handovers\n\nMy lessons:\n\n* Business owners typically need simple aggregation analysis to make decisions\n* Machine learning models don't need to be complex to be effective. Simple solutions like random forests often suffice\n* A communication gap exists between business owners and junior analysts because project managers are overwhelmed managing multiple projects\n* Projects usually ended just as I was beginning to understand the industry\n\nWhat I wished for is a tool that can help me:\n\n* Break down business questions into smaller data questions\n* Store and quickly access reusable queries without writing excessive code\n* Write those simple queries for me\n* Answer ad hoc questions from business people\n* Get familiar with the situation more quickly\n* Guide me through the database schema of the client company\n\nThese are my personal observations. While there's ongoing debate about AI replacing analysts, I've simply shared my perspective based on my humble experience in the field.", "author": "jhnl_wp", "created_time": "2025-02-18T15:46:56", "url": "https://reddit.com/r/analytics/comments/1isfoo4/after_5_years_in_consulting_i_believe_ai_data/", "upvotes": 6, "comments_count": 30, "sentiment": "neutral", "engagement_score": 66.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isfqk8", "title": "Why you need to turn off\nGoogle ads auto-apply recommendations right now", "content": "First, it s\\*cks\n\nYou need to take control over your campaigns, not Google.\n\nWhen you turn on auto-apply recommendations you are giving full control to Google for making any changes that google machine learning algorithm thinks that are right or useful for your campaigns\n\n**But the thing is google is not always right, most of the time these recommendations are only beneficial to Google**\n\nBid and budget adjustments by recommendations can kill your Google ads account\n\nFor instance, in case of a target CPA bid strategy that is losing click volume, Google will increase CPA which will burn your budget\n\nGoogle will add dozens of unnecessary keywords in phrase match or broad match that will potentially burn your budget without seeing any improvement in sales\n\nAlso, Big Campaign Moves Require tons of research and  planning\n\nFor instance;\n\nThe automated recommendations would suggest you switch to smart shopping\n\nSmart Shopping campaigns automatically take product data from a feed and use it to create Shopping ads designed for your customers.\n\nYou can’t simply switch to Smart Shopping campaigns in one click. There are so many things you need to consider before making a big campaign move like this\n\nAnother major issue here is Google suggests these recommendations by machine learning,\n\nThe lack of human intervention here can be a bit unsettling.\n\nSo always make sure to take control of your campaigns without letting  a computer code  take control over your campaigns", "author": "Valuable-Rip6922", "created_time": "2025-02-18T15:49:13", "url": "https://reddit.com/r/adwords/comments/1isfqk8/why_you_need_to_turn_off_google_ads_autoapply/", "upvotes": 10, "comments_count": 10, "sentiment": "neutral", "engagement_score": 30.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isg5m2", "title": "Google Sheets/Excel tracker", "content": "Anyone have a good looking google sheets/excel tracker. \n\nI don't need it to automate (since it can't connect to webull per my understanding) - i'm happy to plug stuff in manually. \n\nI just don't want to build it :)\n\nI found a couple on google but they have a ton of automation and I'm not sure i wanna dig around and play in that.", "author": "Kleptos18", "created_time": "2025-02-18T16:06:27", "url": "https://reddit.com/r/dividends/comments/1isg5m2/google_sheetsexcel_tracker/", "upvotes": 2, "comments_count": 2, "sentiment": "bullish", "engagement_score": 6.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1islhii", "title": "Mexican president threatens to sue Google over ‘Gulf of America’ name change", "content": "", "author": "Forward-Answer-4407", "created_time": "2025-02-18T19:37:59", "url": "https://reddit.com/r/worldnews/comments/1islhii/mexican_president_threatens_to_sue_google_over/", "upvotes": 7567, "comments_count": 311, "sentiment": "neutral", "engagement_score": 8189.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ispeoi", "title": "Business listing services that aren't run by support-free corporations? Google, Apple, Yelp, Mapquest, and Bing's verification processes are literally broken. There is no resolution.", "content": "Any business search providers that don't hate new business owners?  \nWhere do I get a magic phone number that Google et al think is valid for a business?  \nI know how these systems and processes are supposed to work.. and they're broken.  \nI'm an IT systems engineer. I'm not technically inept. These are basic tasks.  \n  \nHave a business phone number that their verification systems won't accept?  \nMay as well go curl up and die, because support cannot and will not fix that.  \n*Whoops our agent has mysteriously disconnected, after repeating our website error.*  \n  \nHave to make a video of your place of business, to get verified?  \nMay as well light yourself on fire, because their QR code link doesn't even scan.  \n  \nHave to update your business address on our review site?  \nHey there friendo, our login process just repeats in an endless loop!  \n  \nWhat the hell?", "author": "rrab", "created_time": "2025-02-18T22:39:49", "url": "https://reddit.com/r/business/comments/1ispeoi/business_listing_services_that_arent_run_by/", "upvotes": 5, "comments_count": 15, "sentiment": "bullish", "engagement_score": 35.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isqe82", "title": "I have 50k to spend, government grant money that I need to deposit / spend immediately", "content": "I have 50k to spend, government grant money that I need to deposit / spend immediately to support women owned businesses in California, and I cannot seem to get an English speaking Google Ads rep? Any advice on how I would go about contacting an actual real human at Google Ads? (not a outsourced teenager in Philippines reading from a script?) ", "author": "mywise<PERSON>y", "created_time": "2025-02-18T23:18:57", "url": "https://reddit.com/r/adwords/comments/1isqe82/i_have_50k_to_spend_government_grant_money_that_i/", "upvotes": 1, "comments_count": 12, "sentiment": "neutral", "engagement_score": 25.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1isr2lw", "title": "Take the L on Google or roll over position?", "content": "I have a Google call with a srirke of $195 expiring 3/7. I bought it after earnings with the belief that the 7% drop was overdone given their P/E ratio, their free cash flow generation, and position as a huperscaler. Then the stock went down another 5%, some of which I attribute to the NFP report causing sector allocation changes.\n\nI continue to believe that the market's reaction to Google was overly pessimistic and that the stock has the potential to rebound. However, I think my option is probably going to expire worthless. \n\nI bought the option at $4.45 and now it's about $1.22. I meant to do a bull spread but hadn't been approved on Robinhood so that ended up not happening. I will need to get approved to do that. \n\nAnyway, I'm thinking of two options: 1. Taking the loss, or 2. Take the loss and enter a bull spread at $190/$195 expiring 4/17. At present prices, this will cost me about $200 to enter, if I sell my option at current prices I'll basically add another $80 to my already-losing bet. ", "author": "Shapen361", "created_time": "2025-02-18T23:48:09", "url": "https://reddit.com/r/options/comments/1isr2lw/take_the_l_on_google_or_roll_over_position/", "upvotes": 8, "comments_count": 11, "sentiment": "bearish", "engagement_score": 30.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}]