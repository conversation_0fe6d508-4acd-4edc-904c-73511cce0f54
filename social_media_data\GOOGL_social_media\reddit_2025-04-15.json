[{"platform": "reddit", "post_id": "reddit_1jzerr9", "title": "could someone dumb it down and explain to me what the dividend % means?", "content": "say i google a company and it says it pays 7% dividends, is that 7% of the initial investiment monthly or yearly? \n\nif i spend 100$ on a stock, and it pays a 5% dividend, do i get 5$ back every year or every month?\n\nthanks in advance.", "author": "M4ldarc", "created_time": "2025-04-15T00:41:11", "url": "https://reddit.com/r/dividends/comments/1jzerr9/could_someone_dumb_it_down_and_explain_to_me_what/", "upvotes": 2, "comments_count": 19, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzhipc", "title": "How bad is this for TSLA?", "content": "Tesla is facing significant pressure to address a major shortfall in its Full Self-Driving (FSD) technology.\nApproximately 4 million vehicles equipped with the Hardware 3 (HW3) computer- installed in Teslas produced from April 2019 through late 2023-are unable to achieve the promised unsupervised autonomous driving capabilities. This revelation contradicts Tesla's earlier assurances that all vehicles produced since 2016 had \"all the hardware necessary for full self-driving capability.\"\n\nIn January 2025, CEO <PERSON><PERSON> acknowledged that HW3 lacks the necessary processing power for full autonomy. He stated that Tesla would need to upgrade the computers in vehicles of customers who purchased the FSD package. This admission has sparked discussions about potential compensation or hardware upgrades for affected owners.\n\nThe situation is further complicated by (HW4) computers. In early 2025, Tesla recalled over 200,000 vehicles due to HW4 units short-circuiting, leading to failures in safety features like rearview cameras. The company is addressing these problems through over-the-air software updates and, when necessary, hardware replacements.\n\nGiven the scale of the HW3 issue and the costs associated with potential retrofits or compensation, this could become one of the most expensive recalls in automotive history. Tesla has not yet detailed a comprehensive plan for addressing the HW3 limitations across its fleet. \n\nFor more detailed information, you can read the full article on Electrek:\n\nhttps://electrek.co/2025/04/14/tesla-tsla-replace-computer-4-million-cars-or-compensate-their-owners/", "author": "Much-Dealer3525", "created_time": "2025-04-15T03:00:20", "url": "https://reddit.com/r/stocks/comments/1jzhipc/how_bad_is_this_for_tsla/", "upvotes": 518, "comments_count": 204, "sentiment": "bearish", "engagement_score": 926.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzqnpk", "title": "In a first, Japan issues cease-and-desist order against Google", "content": "", "author": "superanth", "created_time": "2025-04-15T12:30:06", "url": "https://reddit.com/r/news/comments/1jzqnpk/in_a_first_japan_issues_ceaseanddesist_order/", "upvotes": 3837, "comments_count": 93, "sentiment": "neutral", "engagement_score": 4023.0, "source_subreddit": "news", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzuycv", "title": "{Repeat SEO Myth} Google Again Says Structured Data Does Not Make Your Site Rank Better", "content": "This is always mentioned in these silly SEO posts and checklists and infographics of \"everything you need to rank in Google\" that spam this sub, and other SEO, Marketing and Content subs, here and on LinkedIn and X\n\nSchema just helps Google know where data starts and ends - its a delimiter - like CSV files, like a table\n\nBut \"Schema\" doesnt make your site \"rank better\" or \"rank higher\"\n\nIt's maybe a rank signal but its NOT a rank factor\n\nIt's fine to use it for other things in [schema.org](http://schema.org), that won't cause problems, but you're unlikely to see any visible change from it in Google Search. (I know some people take the \"unlikely\" & \"visible change\" to mean they should optimize for it regardless - knock yourself out; others move faster)\n\nSo please stop posting this, please stop telling people this is why they're not ranking and lets improve our SEO standards here.\n\n[https://www.seroundtable.com/google-structured-data-ranking-39232.html](https://www.seroundtable.com/google-structured-data-ranking-39232.html)", "author": "WebLinkr", "created_time": "2025-04-15T15:35:53", "url": "https://reddit.com/r/SEO/comments/1jzuycv/repeat_seo_myth_google_again_says_structured_data/", "upvotes": 19, "comments_count": 88, "sentiment": "neutral", "engagement_score": 195.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzw1z8", "title": "Google DeepMind's new AI used RL to create its own RL algorithms: \"It went meta and learned how to build its own RL system. And, incredibly, it outperformed all the RL algorithms we'd come up with ourselves over many years\"", "content": "", "author": "MetaKnowing", "created_time": "2025-04-15T16:20:15", "url": "https://reddit.com/r/singularity/comments/1jzw1z8/google_deepminds_new_ai_used_rl_to_create_its_own/", "upvotes": 1060, "comments_count": 112, "sentiment": "neutral", "engagement_score": 1284.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzw54p", "title": "Google DeepMind's new AI used RL to discover its own RL algorithms: \"It went meta and learned how to build its own RL system. And, incredibly, it outperformed all the RL algorithms we'd come up with ourselves over many years.\"", "content": "", "author": "MetaKnowing", "created_time": "2025-04-15T16:23:53", "url": "https://reddit.com/r/artificial/comments/1jzw54p/google_deepminds_new_ai_used_rl_to_discover_its/", "upvotes": 70, "comments_count": 20, "sentiment": "neutral", "engagement_score": 110.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzwsvf", "title": "Want to Learn How to Run Google Ads - Any Recommendations on a Detailed Course, Tutorials, Youtube channel, Ebook, etc.", "content": "I am getting overwhelmed with the fluffy scammed stuff that is out there. Trying to filter through what is the real deal, from where I can truly learn and immediately put into action vs theory and no actionable follow-up.\n\nI looked at the pinned list for this sub.\n\nAny feedback on relevent Current resources or recommendations for 2025 would be much appreciated!!", "author": "els1107", "created_time": "2025-04-15T16:50:24", "url": "https://reddit.com/r/adwords/comments/1jzwsvf/want_to_learn_how_to_run_google_ads_any/", "upvotes": 3, "comments_count": 8, "sentiment": "neutral", "engagement_score": 19.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzxfdf", "title": "I started investing in the middle of a market crash 5 years ago. Here’s what I wish I knew.", "content": "I started investing in 2020, when the market was chaotic and uncertain. I started watch YouTube videos about investing and trying to figure it all out. What I got was that I basically needed to stay on top of every market, every news piece, frantically following all of it for hours a day. This is what I thought the experts did, and hence what would bring me success.\n\nI thought I needed as much information as possible. I went on to read every book, every finance textbook, studied all of it in university. Until I realised that while I had immense knowledge now, I was just as lost and overwhelmed, not knowing where to begin or where to go from there.\n\nMy mistake was the following: I didn't need more information, I needed a solid, timeless but simple mental model of fundamental investing principles to guide me. This would allow me to focus only on what matters, giving me 90% of the results I was looking for. This would allow me to IGNORE the noise and stay calm in any market environment.\n\nI realised that all of investing wisdom can be summed up in around 10 principles, that if followed completely will allow me to be successful, while spending a fraction of the time I thought was needed.\n\nA stock is a part of a business, if the business does well, your stock does well. Based on how well a business is doing, and how good its prospects looks, it has an intrinsic value that can be calculated simply or more complexly. If you underpay compared to that value, you will do well. If you over pay, you will not do well. Most stock moves are emotional, ignore them and be greedy when others are fearful.\n\nThese are some of those principles. Thanks to them, I now am completely calm in a chaotic market, and with clarity I see massive opportunity to make lots of money in such an uncertain time. All because I am able to be grounded in the ironclad principles, and consider nothing else. As <PERSON> <PERSON> said, the simpler it is, the better I like it. That's exactly my philosophy.\n\nWhat makes you feel calm and clear in this chaotic market?", "author": "_TheLongGame_", "created_time": "2025-04-15T17:15:23", "url": "https://reddit.com/r/investing_discussion/comments/1jzxfdf/i_started_investing_in_the_middle_of_a_market/", "upvotes": 0, "comments_count": 30, "sentiment": "bearish", "engagement_score": 60.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jzyzgw", "title": "Gemini now works in google sheets", "content": "", "author": "ibo<PERSON><PERSON><PERSON>", "created_time": "2025-04-15T18:17:31", "url": "https://reddit.com/r/singularity/comments/1jzyzgw/gemini_now_works_in_google_sheets/", "upvotes": 5217, "comments_count": 267, "sentiment": "neutral", "engagement_score": 5751.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}]