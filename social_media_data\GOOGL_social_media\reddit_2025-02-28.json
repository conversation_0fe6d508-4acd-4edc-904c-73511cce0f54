[{"platform": "reddit", "post_id": "reddit_1j03jd8", "title": "anybody else prefer copilot over Google Gemini AI and chat gpt on their phones?", "content": "I've been using copilot since it released in beta and I'm genuinely impressed. I really prefer it over chat gpt or Google Gemini AI. Honestly the thing i like most about it is that it's like actually talking to a human whereas <PERSON> feels like it's just basing it's answers off of Google searches. I also like the fact that my questions on copilot on my pc carry over to the copilot app on my phone.", "author": "Legitimate_Pea_143", "created_time": "2025-02-28T09:03:48", "url": "https://reddit.com/r/microsoft/comments/1j03jd8/anybody_else_prefer_copilot_over_google_gemini_ai/", "upvotes": 8, "comments_count": 11, "sentiment": "neutral", "engagement_score": 30.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j051y2", "title": "My dividend excel sheet", "content": "I didn't feel like paying a monthly fee to track my dividends. So I found a decent excel template and built out all the formulas to automate almost everything. The most difficult thing with my excel sheet is to download and copy and paste an excel sheet from your brokerage of dividends paid out if you want to track an accurate amount of dividends received throughout the years.\n\n[https://docs.google.com/spreadsheets/d/1yuCv\\_3Oel1fnuSr8em\\_WERZTpHIds-oGVh6cmezNj6Y/edit?usp=sharing](https://docs.google.com/spreadsheets/d/1yuCv_3Oel1fnuSr8em_WERZTpHIds-oGVh6cmezNj6Y/edit?usp=sharing)\n\nSome were asking about wanting a real time stock price. This sheet uses a different formula to update the price every minute. This is as close as real time as I can figure out.\n\n[https://docs.google.com/spreadsheets/d/1WElRRRPH8D--eKIo7SwV7\\_tCcE2UZgkDp\\_p07wTsMbQ/edit?usp=sharing](https://docs.google.com/spreadsheets/d/1WElRRRPH8D--eKIo7SwV7_tCcE2UZgkDp_p07wTsMbQ/edit?usp=sharing) ", "author": "usaf_photog", "created_time": "2025-02-28T10:55:31", "url": "https://reddit.com/r/dividends/comments/1j051y2/my_dividend_excel_sheet/", "upvotes": 280, "comments_count": 64, "sentiment": "neutral", "engagement_score": 408.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j09wxl", "title": "Google announces layoffs in its HR, cloud units as part of ongoing cost cuts", "content": "", "author": "ControlCAD", "created_time": "2025-02-28T15:13:14", "url": "https://reddit.com/r/business/comments/1j09wxl/google_announces_layoffs_in_its_hr_cloud_units_as/", "upvotes": 146, "comments_count": 10, "sentiment": "neutral", "engagement_score": 166.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0b6dh", "title": "Google removing 'state' designation from Canadian government buildings. No word from Microsoft", "content": "", "author": "TheArcticBeyond", "created_time": "2025-02-28T16:06:31", "url": "https://reddit.com/r/worldnews/comments/1j0b6dh/google_removing_state_designation_from_canadian/", "upvotes": 1524, "comments_count": 112, "sentiment": "neutral", "engagement_score": 1748.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0cik8", "title": "Why is <PERSON><PERSON> astral a lot more expensive than other cards?", "content": "As the title says, I'm wondering why the astral 5090 is about $1,000 more than other 5090s? I did a quick Google search and couldn't find anything other than it is overclocked. Is that it? Is the overclock really worth than extra thousand bucks?", "author": "Sorktastic", "created_time": "2025-02-28T17:00:52", "url": "https://reddit.com/r/nvidia/comments/1j0cik8/why_is_asus_astral_a_lot_more_expensive_than/", "upvotes": 218, "comments_count": 301, "sentiment": "neutral", "engagement_score": 820.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0e3ey", "title": "Had my first unwanted Assignment today. GOOGL ate my baby.", "content": "Sold Credit Spread for $60 x10  \nClosed Credit Spread for $495 x10  \nLoss = $4,350\n\nI thought I was clever, and sold a 10x Bull Put Credit Spread on GOOGL last week with strike of $180 and a 5 point spread, expiring the next Friday, Feb 28th.\n\nI figured, it's not likely to go down much more than it already did after Earnings. The earnings were fine. The market overreacted, as it does. I expected the stock to climb slowly back up over the the following week (this week).\n\nAnd it probably would have, if the market as a whole wasn't shitting itself non stop this week.\n\nSo my credit spread was WAY in the money now, but I wasn't too worried. I planned to roll it out a month when the market opened today. But I was immediately assigned, on margin. I don't have $180,000!\n\nThen I remembered, \"Oh yeah, I have that blessed Long Put, for defined risk!\"\n\nSo I sold my puts, my freshly assigned shares, and a few other things, just to make sure I had the liquidity to make Fidelity happy.\n\nI learned a few lessons from this:\n\n1: I'm no longer running credit spreads a week or two out. I'm sticking to the 45DTE/21DTE method.\n\n2: I'm never again opening a credit spread close to the money, thinking it can't go lower. It always seems to. No matter how good the company is.\n\n3: I'm never waiting until the last day to roll. Which shouldn't be a problem if I hold to rule #1.\n\nThis one hurt. I need to run some numbers, but it looks like it might have wiped out all my profits for the month. :(\n\n::EDIT::\n\nTurns out I'm still up $1,736 on the month! So, not a complete loss.", "author": "PinkyPowers", "created_time": "2025-02-28T18:05:48", "url": "https://reddit.com/r/options/comments/1j0e3ey/had_my_first_unwanted_assignment_today_googl_ate/", "upvotes": 107, "comments_count": 64, "sentiment": "bullish", "engagement_score": 235.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0gf9m", "title": "Google's <PERSON> Engineers Should Work 60-Hour Weeks in Office to Build AI That Could Replace Them", "content": "", "author": "lurker_bee", "created_time": "2025-02-28T19:44:59", "url": "https://reddit.com/r/technology/comments/1j0gf9m/googles_sergey_brin_says_engineers_should_work/", "upvotes": 3621, "comments_count": 437, "sentiment": "neutral", "engagement_score": 4495.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0i3xg", "title": "Review my Educational Content on Google AI Studio", "content": "Hey, Anyone willing to review my educational course on Google AI Studio on Udemy? It is a 4 hours content with all features, capabilities and settings explained in detail (theory + examples). I'll provide you with a coupon to take the course for no cost, and share feedback.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-28T20:57:19", "url": "https://reddit.com/r/analytics/comments/1j0i3xg/review_my_educational_content_on_google_ai_studio/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0if81", "title": "Google’s <PERSON>s Engineers Should Work 60-Hour Weeks in Office to Build AI That Could Replace Them", "content": "", "author": "ControlCAD", "created_time": "2025-02-28T21:10:32", "url": "https://reddit.com/r/google/comments/1j0if81/googles_sergey_brin_says_engineers_should_work/", "upvotes": 2431, "comments_count": 163, "sentiment": "neutral", "engagement_score": 2757.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0jbg3", "title": "Google's <PERSON> Engineers Should Work 60-Hour Weeks in Office to Build AI That Could Replace Them", "content": "", "author": "waIIstr33tb3ts", "created_time": "2025-02-28T21:49:50", "url": "https://reddit.com/r/wallstreetbets/comments/1j0jbg3/googles_sergey_brin_says_engineers_should_work/", "upvotes": 6195, "comments_count": 613, "sentiment": "neutral", "engagement_score": 7421.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j0kigr", "title": "In leaked memo to Google’s AI workers, <PERSON> says 60 hours a week is the ‘sweet spot’ and doing the bare minimum can demoralize peers - (Fortune)", "content": "In an internal memo to employees who work on Gemini, <PERSON> recommended being in the office at least every weekday and said 60 hours is the \"sweet spot\" for productivity, according to the New York Times ---- He added that competition to develop artificial general intelligence has ramped up but maintained his belief that Google can come out on top if the company can \"turbocharge\" its efforts.\n\n[https://www.msn.com/en-us/news/technology/in-leaked-memo-to-google-s-ai-workers-sergey-brin-says-60-hours-a-week-is-the-sweet-spot-and-doing-the-bare-minimum-can-demoralize-peers/ar-AA1A04mu?ocid=msedgntp&pc=HCTS&cvid=93f9dc8d54874c96b0400e2d98a65eb3&ei=15](https://www.msn.com/en-us/news/technology/in-leaked-memo-to-google-s-ai-workers-sergey-brin-says-60-hours-a-week-is-the-sweet-spot-and-doing-the-bare-minimum-can-demoralize-peers/ar-AA1A04mu?ocid=msedgntp&pc=HCTS&cvid=93f9dc8d54874c96b0400e2d98a65eb3&ei=15)\n\n\n\n", "author": "Next-Particular1476", "created_time": "2025-02-28T22:43:16", "url": "https://reddit.com/r/business/comments/1j0kigr/in_leaked_memo_to_googles_ai_workers_sergey_brin/", "upvotes": 484, "comments_count": 152, "sentiment": "neutral", "engagement_score": 788.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}]