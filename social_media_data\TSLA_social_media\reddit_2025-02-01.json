[{"platform": "reddit", "post_id": "reddit_1iev66u", "title": "Looking for financial advice!! 19y/o", "content": "I am 19 working full time, already maxed out 2 years of my retirement account have 14k in my ROTH IRA, have a emergency fund abt 15k and no debt, l've tried crypto and lost some money, done stocks and made about 2k from Tesla and apple. I have some money that I'm ok with risking, was wondering if there is any smart investing advice or tips you guys could give me to make more passive income? I already know about CDS and 401k.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-01T01:31:37", "url": "https://reddit.com/r/Fire/comments/1iev66u/looking_for_financial_advice_19yo/", "upvotes": 4, "comments_count": 11, "sentiment": "neutral", "engagement_score": 26.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1if1i5b", "title": "<PERSON><PERSON><PERSON> Master 5090", "content": "Hello, some people had questions and comments on my previous post so I thought I’d post an update. \n\nIn regards to coil and fan noise I have to say I’m impressed. Temps stayed at a cool mid 70s peaking at about 74c. The thing was almost silent\n\nUnfortunately I was unable to run several benchmarks as they refused to start up at all. \nCinabench 4.3,4.2 & Blender all failed to start up. \nOther programs such as MSI afterburner are also affected. Maybe the comments can help me out on this one \n\nI’ve also used the auto clocking feature in the NVIDIA app and was able to get a +100 to both clocks (this is on top of gigabytes own tuning which is 2655MHZ, reference cards being 2407MZH) \n\nI’d like to get bench marks running and MSI AFTERBURNER so we can see how far this card can be pushed. \n\nFor trouble shooting reference, BIOS is up to date as well as NVIDIA drivers of course. \n\n9800x3d\nX680e\n1200w PSU", "author": "VintageAutomatics", "created_time": "2025-02-01T07:52:33", "url": "https://reddit.com/r/nvidia/comments/1if1i5b/aorus_master_5090/", "upvotes": 9, "comments_count": 103, "sentiment": "neutral", "engagement_score": 215.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1if2xeq", "title": "Apple's So Worried About Losing $20 Bilion every year, They're Demanding the Court to Not Block Their Google search Deal.", "content": "https://www.theverge.com/news/603998/apple-google-search-remedies-monopoly-trial-stay-request", "author": "Yazzdevoleps", "created_time": "2025-02-01T09:39:45", "url": "https://reddit.com/r/google/comments/1if2xeq/apples_so_worried_about_losing_20_bilion_every/", "upvotes": 354, "comments_count": 36, "sentiment": "neutral", "engagement_score": 426.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1if3n1a", "title": "Simply script to check for RTX 5090 FE NVIDIA stock (on NVIDIA Marketplace)", "content": "So, you missed out on the (paper) launch due to bots or slow F5'ing? The best way to increase your chances is to optimize your search and automate stock monitoring for GPUs. Luckily for you, I have a free, simple script that you can run in Google Chrome without any additional downloads! Forget those complicated headless software setups—this requires only two things.\n\n**Setting Up the Script in Google Chrome**\n\n**Step 1:** Bookmark the following script. It injects the latest jQuery into your current webpage.\n\n    javascript:(function(e,s){e.src=s;e.onload=function(){jQuery.noConflict();console.log('jQuery injected')};document.head.appendChild(e);})(document.createElement('script'),'//code.jquery.com/jquery-latest.min.js')\n\n**Step 2:** In Google Chrome, press **F12** and click on the **\"Sources\"** tab. Navigate to **\"Snippets\"**, then click **\"New snippet\"**.\n\n**Step 3:** Copy and paste the following code, then press **CTRL+S** to save the snippet. Be sure to change `\"locale=COUNTRY\"` to the area you want to monitor (e.g., `\"be\"` for Belgium, `\"nl\"` for the Netherlands, `\"de\"` for Germany, etc.).\n\n`//inject Jquery first`\n\n`function myTimer() {`\n\n`jQuery.getJSON('https://api.store.nvidia.com/partner/v1/feinventory?status=1&skus=PROGFTNV590&locale=NL', function(data) {`\n\n`// JSON result in \\`data\\` variable\\`\n\n`var text0 = data.listMap[0].is_active`\n\n`var url0 = data.listMap[0].product_url`\n\n`console.log(text0);`\n\n`if (text0 == 'true') {`\n\n`console.log('5090 is available!');`\n\n`window.open(url0);`\n\n`for (var i = 1; i < 9999; i++) clearInterval(i);`\n\n`}`\n\n`else{`\n\n`console.log('5090 not available!');`\n\n`}`\n\n`});}`\n\n`setInterval(myTimer, 2000);`\n\n**How to Use the Script (video example in Notes)**\n\n**Step 1:** Navigate to any webpage. (I usually use a blank page to monitor network activity, but you can also go to the NVIDIA marketplace webpage.)\n\n**Step 2:** Open the developer console by pressing **F12** and navigating to the **\"Console\"** tab.\n\n**Step 3:** Click on the bookmarked page (with the jQuery script). This will inject jQuery into the website. The console should confirm that jQuery has been injected.\n\n**Step 4:** Go to the snippet (**Sources → Snippets**), click on the snippet, and press **CTRL+Enter** to run it. The script will check the NVIDIA stock API every second. When stock is available (`is_active = true`), it will open the `product_url` in a new tab. Once stock is detected and a tab is opened, the script will stop checking to prevent opening a new tab every second.\n\n*Notes*\n\n*You can remove the console.log statements to reduce memory usage.*\n\n*Increase the timer interval to at to avoid a temporary ban. Using a higher value is recommended for long-term monitoring.*\n\n*Looking for RTX5080, change the SKU to PRO580GFTNV*\n\n*Useage video:* \n\nhttps://reddit.com/link/1if3n1a/video/qbdquh08lige1/player\n\n", "author": "B<PERSON>bjair", "created_time": "2025-02-01T10:34:22", "url": "https://reddit.com/r/nvidia/comments/1if3n1a/simply_script_to_check_for_rtx_5090_fe_nvidia/", "upvotes": 235, "comments_count": 175, "sentiment": "bullish", "engagement_score": 585.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1if7p41", "title": "Tesla Long Call", "content": "I bought 1 Call $415 on TSLA on 1/23 with expiration of 1/31.  I was betting it would have a spike on earning.   As you saw TSLA dropped under 400 going into earning.  On Friday though I went to sell my call when it was over $416.25 and I only had $334 of the value. Ended up losing.  Is there a good way to determine what price at expiration would need to be to make money or break even?  Also, if some experts see what went wrong would like to know.  Is this all the result of time decay?   Honestly thought the quick run up from the $388 would have turn good for me. \n\n", "author": "AmericaIsBack110524", "created_time": "2025-02-01T14:41:04", "url": "https://reddit.com/r/options/comments/1if7p41/tesla_long_call/", "upvotes": 0, "comments_count": 42, "sentiment": "bearish", "engagement_score": 84.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ifa2bh", "title": "My idea for decarbonizing Sasol's Secunda Coal to liquid fuel plant in South Africa", "content": "Sasols Secunda CTL plant is the single largest emitter of CO2 in the world. Coal derived liquid fuels have a large climate impact because coal contains the most carbon out of all fossil fuels. Coal is not only used as production feedstock but it is also used to produce the energy needed the thermochemical conversion process. I have an idea for how to decarbonize this facility.\n\n[This facility was built in the early 1980s by the former Apartheid regime to reduce dependence on imported oil](https://preview.redd.it/lly0uymfvjge1.png?width=2560&format=png&auto=webp&s=c343c8db9cf838333261ae79742ba4e0ab691578)\n\nThe Secunda facility works by using gasification to convert coal into syngas (CO and H2 mixture) which is then cleaned before being converted into liquid fuels using a Fischer-Tropsch (FT) reactor. The mixture of substances produced in the FT reactor is then separated using fractional distillation like in oil refineries. Coal is also burned to produce the heat needed to power the gasifier.\n\n[This diagram visualizes the process which I described above](https://preview.redd.it/fwkiua12wjge1.png?width=1650&format=png&auto=webp&s=31bccfe2f5082929fb5f8381c4641f0a22953591)\n\nThis is my idea to decarbonize the Secunda facility\n\n1. Replace coal with synthetic coal made from biomass as the production feedstock. The South African sugar industry produces large amounts of sugarcane baggage every year which is currently a waste product that is piling up at sugar mills in South Africa - [https://www.intechopen.com/chapters/84298#](https://www.intechopen.com/chapters/84298#)  . This sugarcane baggage can be turned into artifical coal using this technology - [https://www.science.org/doi/10.1126/sciadv.aay0748](https://www.science.org/doi/10.1126/sciadv.aay0748) . This artifical coal should be compatible with the existing equipment at Secunda because it is chemically identical.\n2. Use the ethylene and propylene produced by the process (shown in the diagram above) as energy sources to produce the heat needed for the gasifier. Retrofit the facilities power plant to burn ethylene and propylene. This will require new pipes to connect the distillation facility to the power plant. Bio-based biodegradable plastics and plastic alternatives combined with recycling of these materials will eliminate the value in using ethylene and propylenes as chemical production feedstock.\n3. Distribute the biochar produced by the artificial coal production process to local farmers so that they can use it to improve soil health and remove atmospheric CO2.\n\nUnfourtenetly I do not think my idea or any similar idea will get implemented by Sasol anytime soon given the incompetence of the South African government and Sasol's economic troubles. Sasol will need money to decarbonize the Secunda plant in the manner that I described. Sasol does not have this money nor is the South African government willing or able to give this sort of funding to Sasol. The potential to decarbonize the Secunda facility will remain un-utilized until both Sasol the South African government get themselves together again. The future of the Secunda plant will remain uncertain as long as Sasol and the government of Sasols home country remain troubled.", "author": "Live_Alarm3041", "created_time": "2025-02-01T16:31:19", "url": "https://reddit.com/r/CleanEnergy/comments/1ifa2bh/my_idea_for_decarbonizing_sasols_secunda_coal_to/", "upvotes": 4, "comments_count": 3, "sentiment": "bullish", "engagement_score": 10.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ifbyxk", "title": "NVPI Revamped - Tool Release: Override DLSS4 & More Globally", "content": "The tool has been shared here for awhile now in comments & posts, but I thought I'd make a dedicated post on it.\n\nIts a fork of NVPI AIO, which was a fork of the original NVPI except with a ton of enhancements to it regarding load times, search functionality, & exposing additional hidden CVars.\n\nMy fork is a continuation of that with support for the latest NVIDIA drivers *(the AIO version of NVPI stopped working)* and also for the latest NVIDIA app DLSS overrides *(except on a global scale rather than a per game basis, making it a stronger override)*\n\nI recommend not having the NVIDIA App installed due to the fact when you launch a game that's not officially supported NVIDIA automatically changed the overrides to off, uninstalling the app removes that check so it works better. \n\n**Disclaimer:** The app will be marked as a virus by Windows, you are free to compile the code yourself. This is due to something called Wacatac which is a commonly well known false positive & is often marked as a Trojan. If you want to know why its marked as such you can use Google or ask an AI assistant.\n\n[Source / Download](https://github.com/xHybred/NvidiaProfileInspectorRevamped)\n\n[Screenshot](https://i.imgur.com/maRjZDi.png)", "author": "OptimizedGamingHQ", "created_time": "2025-02-01T17:54:06", "url": "https://reddit.com/r/nvidia/comments/1ifbyxk/nvpi_revamped_tool_release_override_dlss4_more/", "upvotes": 231, "comments_count": 182, "sentiment": "bullish", "engagement_score": 595.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ife62q", "title": "$BHAT the most undervalued stock on the market as of January 31st", "content": "Summary\n\n$BHAT is trading 0,0579 after hours on January 31st. I personally believe This stock is worth $0.30+,\n\nI am just a young female student I did my own research,fact checked everything. I encourage you to do your own research.\n\nIntroduction\n\nBlue Hat Interactive Entertainment Inc. ($BHAT) is currently trading at an all-time low, yet recent developments indicate that it is severely undervalued. The stock recently saw a slight price increase following significant news, but it has yet to reflect its true value. In this DD, I will break down why I believe $BHAT could see a substantial run-up next week, with a personal price target of $0.30+ per share based on its latest acquisition and financial restructuring.\n\nKey Catalyst: $90M in Gold Acquisition\n\nOn 31 January 2025, $BHAT announced that it has acquired a substantial quantity of gold worth approximately $90 million. Prior to this announcement, the stock was trading at just $0.035 per share, with a total market cap of roughly $2 million based on its 58 million outstanding shares. Even after factoring in the dilution from an upcoming share offering (which I'll discuss below), the stock remains significantly undervalued. Effectively, investors are buying shares in a company now backed by gold assets at a massive discount.\n\nUnderstanding the Dilution Factor\n\nA common concern for investors is dilution. $BHAT has an agreement with Golden Alpha Strategy, which includes an issuance of 245 million shares at an average price of $0.1365 per share to settle outstanding debts. Once these shares are fully issued, the total share count will rise to 303 million shares. However, even at this new share count, the math supports a much higher valuation:\n\nCurrent Price Per Share: $0.0569\n\nPost-Dilution Market Cap (303M shares at $0.0569): $17.24 million\n\n$BHAT made it known that they wanted to acquire $66.000.000 dollars worth of gold on August 29th, 2024.\n\nThey announced on January 31st, 2025 that they completed the acquisition. Since then, that gold has now become worth 90 million. Their completed gold transaction made them (with the risen value of gold) an astonishing profit of 24 million dollars.\n\nWith their net worth of 90 Million dollars of gold (which they now own debt free).\n\nLet's recalculate with that in mind: ($90.000.000 :300.000000 shares=$0.30 a share) Which amounts to an adjusted share price of $0.30 based on gold assets\n\nThis means that even after dilution, the current stock price is trading at just one-fifth of its actual value.\n\n\n\nFair Value Based on Gold Reserves → $90M : 303M shares = $0.30 per share\n\nValue per share right now with the 58 Million shares that are trading now\n\n90.000.000: 58.000.000=$ 1,552 \n\n the shares are trading at 0,0569 right now\n\nThey are 27 times undervalued.\n\nThis means that at the current price, investors are essentially getting shares at 1/27th of their real value based on the gold acquisition alone. And after they will bring the 250 M shares acquired by Golden Alpha Strategy at 0,1365 a share the value per share is $0.30\n\nFinal Thoughts\n\n\n$BHAT has completely re-structured into the trading of gold commodity and with possible acquisition of GTCFX and use of ai. driven financial services, they'll have an enormous ground to stand on.\n\nThis is not financial advise do your own research. ", "author": "Subject-Necessary713", "created_time": "2025-02-01T19:28:20", "url": "https://reddit.com/r/pennystocks/comments/1ife62q/bhat_the_most_undervalued_stock_on_the_market_as/", "upvotes": 0, "comments_count": 67, "sentiment": "bullish", "engagement_score": 134.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ifgzhw", "title": "Teslas now drive themselves from the factory to loading docks without human intervention", "content": "", "author": "matroosoft", "created_time": "2025-02-01T21:33:06", "url": "https://reddit.com/r/singularity/comments/1ifgzhw/teslas_now_drive_themselves_from_the_factory_to/", "upvotes": 9, "comments_count": 28, "sentiment": "neutral", "engagement_score": 65.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ifhn7p", "title": "<PERSON><PERSON><PERSON><PERSON> says Canada should target Elon Musk’s Tesla in a tariff fight", "content": "", "author": "plz-let-me-in", "created_time": "2025-02-01T22:02:33", "url": "https://reddit.com/r/politics/comments/1ifhn7p/chrystia_freeland_says_canada_should_target_elon/", "upvotes": 4494, "comments_count": 204, "sentiment": "neutral", "engagement_score": 4902.0, "source_subreddit": "politics", "hashtags": null, "ticker": "TSLA"}]