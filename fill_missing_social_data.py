#!/usr/bin/env python3
"""
填补AAPL和NVDA缺失的社交媒体数据

使用现有的reddit_live_collector.py脚本来收集缺失的社交媒体数据
专门针对AAPL和NVDA股票，填补2024-12-01到2025-06-01期间的数据空缺
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Set
import argparse

# 添加当前目录到Python路径，以便导入reddit_live_collector
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from reddit_live_collector import RedditLiveCollector, load_reddit_config

def setup_logging():
    """设置日志配置"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "fill_missing_data.log"),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def get_existing_dates(ticker: str, data_dir: str = "social_media_data") -> Set[str]:
    """获取指定股票已有的数据日期"""
    ticker_dir = Path(data_dir) / f"{ticker}_social_media"
    existing_dates = set()
    
    if ticker_dir.exists():
        for file_path in ticker_dir.glob("reddit_*.json"):
            # 从文件名提取日期 (reddit_YYYY-MM-DD.json)
            filename = file_path.stem
            if filename.startswith("reddit_"):
                date_str = filename[7:]  # 移除 "reddit_" 前缀
                existing_dates.add(date_str)
    
    return existing_dates

def generate_date_range(start_date: str, end_date: str) -> List[str]:
    """生成日期范围内的所有日期字符串"""
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    dates = []
    current = start
    while current <= end:
        dates.append(current.strftime('%Y-%m-%d'))
        current += timedelta(days=1)
    
    return dates

def find_missing_dates(ticker: str, start_date: str, end_date: str, data_dir: str = "social_media_data") -> List[str]:
    """找出指定时间范围内缺失的数据日期"""
    existing_dates = get_existing_dates(ticker, data_dir)
    all_dates = generate_date_range(start_date, end_date)
    
    missing_dates = [date for date in all_dates if date not in existing_dates]
    return sorted(missing_dates)

def collect_data_for_date_range(collector: RedditLiveCollector, ticker: str, 
                               start_date: str, end_date: str, logger) -> dict:
    """为指定日期范围收集数据"""
    logger.info(f"开始为 {ticker} 收集 {start_date} 到 {end_date} 的数据")
    
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')
    
    # 使用收集器收集数据
    stats = collector.collect_data(
        start_date=start_dt,
        end_date=end_dt,
        tickers=[ticker],
        limit_per_subreddit=2000  # 增加限制以获得更好的覆盖
    )
    
    return stats

def main():
    parser = argparse.ArgumentParser(description='填补AAPL和NVDA缺失的社交媒体数据')
    parser.add_argument('--tickers', nargs='+', default=['AAPL', 'NVDA'],
                       help='要处理的股票代码 (默认: AAPL NVDA)')
    parser.add_argument('--start-date', default='2024-12-01',
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', default='2025-06-01',
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--data-dir', default='social_media_data',
                       help='数据目录')
    parser.add_argument('--batch-size', type=int, default=7,
                       help='批处理大小（天数）')
    parser.add_argument('--dry-run', action='store_true',
                       help='仅显示缺失的日期，不实际收集数据')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("="*60)
    logger.info("开始填补AAPL和NVDA缺失的社交媒体数据")
    logger.info("="*60)
    
    try:
        # 加载Reddit配置
        config = load_reddit_config()
        
        # 创建收集器
        collector = RedditLiveCollector(config, args.data_dir)
        
        total_missing = 0
        total_collected = 0
        
        for ticker in args.tickers:
            logger.info(f"\n处理股票: {ticker}")
            logger.info("-" * 40)
            
            # 找出缺失的日期
            missing_dates = find_missing_dates(ticker, args.start_date, args.end_date, args.data_dir)
            
            if not missing_dates:
                logger.info(f"{ticker}: 没有缺失的数据")
                continue
            
            logger.info(f"{ticker}: 发现 {len(missing_dates)} 个缺失日期")
            total_missing += len(missing_dates)
            
            if args.dry_run:
                logger.info(f"{ticker} 缺失日期:")
                for i, date in enumerate(missing_dates):
                    logger.info(f"  {i+1:3d}. {date}")
                continue
            
            # 按批次处理缺失的日期
            for i in range(0, len(missing_dates), args.batch_size):
                batch = missing_dates[i:i + args.batch_size]
                batch_start = batch[0]
                batch_end = batch[-1]
                
                logger.info(f"{ticker}: 处理批次 {batch_start} 到 {batch_end} ({len(batch)} 天)")
                
                try:
                    # 收集这个批次的数据
                    stats = collect_data_for_date_range(
                        collector, ticker, batch_start, batch_end, logger
                    )
                    
                    logger.info(f"{ticker}: 批次完成 - 处理了 {stats.get('subreddits_processed', 0)} 个子版块，"
                              f"获得 {stats.get('relevant_posts', 0)} 个相关帖子")
                    
                    total_collected += stats.get('relevant_posts', 0)
                    
                except Exception as e:
                    logger.error(f"{ticker}: 批次处理失败 {batch_start} 到 {batch_end}: {e}")
                    continue
        
        # 显示总结
        logger.info("\n" + "="*60)
        logger.info("数据填补完成")
        logger.info("="*60)
        
        if args.dry_run:
            logger.info(f"总缺失日期数: {total_missing}")
            logger.info("这是试运行，没有实际收集数据")
        else:
            logger.info(f"总缺失日期数: {total_missing}")
            logger.info(f"总收集帖子数: {total_collected}")
            logger.info(f"数据保存在: {args.data_dir}")
        
        return 0
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
