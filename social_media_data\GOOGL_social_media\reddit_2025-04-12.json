[{"platform": "reddit", "post_id": "reddit_1jx5ckf", "title": "Google AI in search", "content": "Google treating AI generated content as a negative for SEO while simultaneously blocking search results to show their own AI generated content that was lifted off our sites is ultimate gangster.", "author": "feech1970", "created_time": "2025-04-12T00:52:41", "url": "https://reddit.com/r/marketing/comments/1jx5ckf/google_ai_in_search/", "upvotes": 11, "comments_count": 11, "sentiment": "bearish", "engagement_score": 33.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jxin3q", "title": "[N] Google Open to let entreprises self host SOTA models", "content": "From a major player, this sounds like a big shift and would mostly offer enterprises an interesting perspective on data privacy. Mistral is already doing this a lot while OpenAI and Anthropic maintain more closed offerings or through partners.\n\n[https://www.cnbc.com/2025/04/09/google-will-let-companies-run-gemini-models-in-their-own-data-centers.html](https://www.cnbc.com/2025/04/09/google-will-let-companies-run-gemini-models-in-their-own-data-centers.html)", "author": "coding_workflow", "created_time": "2025-04-12T14:33:00", "url": "https://reddit.com/r/MachineLearning/comments/1jxin3q/n_google_open_to_let_entreprises_self_host_sota/", "upvotes": 54, "comments_count": 3, "sentiment": "neutral", "engagement_score": 60.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jxmyhg", "title": "I have my first click and it's 23.63$", "content": "Hello everybody,\n\nLike the title says, I got my first click and it's 23.63$. Should I panick? How can a click cost that much? The average cost-per-click was 0.71$ if I'm not wrong. I chose 4 keywords. It's my first time doing Google Ads.  \nIn about 4-5 days, the keywords got 206 impressions and 1 click. ", "author": "Way2MuchPride", "created_time": "2025-04-12T17:45:13", "url": "https://reddit.com/r/PPC/comments/1jxmyhg/i_have_my_first_click_and_its_2363/", "upvotes": 0, "comments_count": 22, "sentiment": "neutral", "engagement_score": 44.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jxp4g2", "title": "Trying to size up the current state of major AI products or players.", "content": "A+: Gemini 2.5 and <PERSON><PERSON> are currently best in class and Google Deepmind is one of the best research publishers and most innovative in niche/scientific fields. Clear leaders overall, but only just barely in the most competitive spaces. Their long history and deep pockets and platforms and data offer major advantages.\n\nA: Clear leaders ahead of the rest.\n- <PERSON>throp<PERSON> is the clear leader in interpretability and publishes constantly. <PERSON> is also a powerful if somewhat narrow model. They have a very dense pool of talent and a really good strategy.\n- OpenAI doesn't need introduction; they also lead the pack in productization, market and brand position, talent, reputation, fundraising, and seem to constantly be evolving forward. Anthropic and SSI and many other firms are themselves just the children of this lab.\n\nB: High potential but not yet leading. Both models have low content filtering (good) but high political propaganda (bad). Either of these teams could find themselves in the lead with one solid release but haven't done that yet.\n- Deepseek isn't leading in anything besides cost efficiency and minimal content filtering but with its critical gov backing and strong start, it has huge potential to keep the A-tier on their toes and make sure no moat forms. Deepseek also has strong partnerships in the Chinese space, which is a growing titan in the field and major research publication region. There is vast talent at their disposal as well.\n- Xai has a powerful model, good hardware, deep pockets. and solid talent on their hands. However, it's still playing catch-up. Love him or hate him, <PERSON><PERSON> has been an early investor and planner in AI, including with <PERSON><PERSON>, and will likely be a top player soon at the rate he's going. It still does have significant ground to cover, though. Xai also has a major platform and data advantage (x, tesla, spacex) and potential priority for government contracts which is very valuable.\n\nC: Many of these are solid non-leading players in the space or just partnered with leaders in the space but all have major advantages.\n- Huggingface is critical for the powerful open source side of the AI field and is the single most valuable concentration of AI tools that exists for independent and funded researchers alike. This is the true fulcrum of the AI community, however it's not itself an AI lab so it can't be a leader in the space itself. \n- Nvidia is a leader in some less visible AI spaces and the company selling the shovels to the miners. No matter who wins the race, Nvidia also wins. They aren't dominating the AI field on the product side, but they are the top players on the hardware side and are among the top on the research side. However, their hardware dominance will weaken in time. \n- Microsoft has a ton of great tools, a great platform, some decent talent, deep pockets, and great partnerships, and solid leadership. However, they're not very agile and have a culture that has somewhat ossified. Despite this, Azure, Windows, VSCode, and Github are massive platform and data advantages and their early partnership with OpenAI has been very valuable.\n- Meta has a lot of great talent but they seem to be struggling. Despite deep pockets, early experience in the field, and a commitment to the Llama models having open weights, they continue to struggle and seem to have some major leadership issues. Still, Llama is a best in class open weight LLM and that's no trivial matter. Meta also has a very powerful platform and data advantage.\n\nF: Falling behind or showing up late, these players still show promise but currently have little to show in this highly competitive space.\n\n- Amazon Q has big boots to fill. With the advantage of deep pockets, a partnership with Anthropic, AWS, and Alexa as a platform, they have the potential to lead in this space. Despite this they seem to be struggling to catch up. They have a strong data and tool advantage in various niches.\n\n- Mistral has a strong commitment to specific ethics, a great pool of European talent, solid funding, and the core of a great model. Despite this, they are hamstrung by regional braindrain and strict regulations. They have the potential to lead as well as dominate their massive and wealthy region if they can figure out how to navigate these burdens.\n\n- Apple Intelligence is currently a failure. Late to the game and struggling to catch up, they have vast resources, a massive commitment of funding, a rich history of showing up late and winning, a top tier platform (the iphone), solid commitment to some key ideals (privacy), and a solid pool of talent. Currently not doing much but don't count them out yet, they have a massively funded full-stack plan and a dedication to product excellence that has often proven itself.\n\n- Perplexity is a slowing leader in productization but I suspect they are running out of steam. I think they're still in the game for now, though. Time will tell whether they evolve or fall down like Stability AI did after Stable Diffusion.", "author": "outerspaceisalie", "created_time": "2025-04-12T19:21:06", "url": "https://reddit.com/r/singularity/comments/1jxp4g2/trying_to_size_up_the_current_state_of_major_ai/", "upvotes": 1347, "comments_count": 501, "sentiment": "bullish", "engagement_score": 2349.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jxqfsv", "title": "About Running Google Ads w/Small Budget (750/month)", "content": "Hey guys , I am selling my google ads services as an agency and want to run ads to get clients. As of now my budget is 750/month. I know this is a small budget. How do I effectively run ads and get bookings with this budget? So far my main problem is that the keywords that are high intent like google ads advertising/google ads management/ google ads agency all have high CPC for top of page bids. I have seen from youtube videos that I should aim for at least 10 clicks a day. Now with the average CPC of these keywords, 10 clicks a day is out of my budget. What should I do to run my ads to get a booking with the budget I have? Any recommendation is welcome no matter how small or big.   \n  \nMy business currently : I have 1 landing page that I am running the ad to. I have one call to action on the page which is a “book an appointment” button for a calendly appointment which I am taking as conversions.", "author": "Forward_Scratch_4923", "created_time": "2025-04-12T20:20:48", "url": "https://reddit.com/r/adwords/comments/1jxqfsv/about_running_google_ads_wsmall_budget_750month/", "upvotes": 7, "comments_count": 21, "sentiment": "bearish", "engagement_score": 49.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}]