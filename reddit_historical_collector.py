#!/usr/bin/env python3
"""
Reddit历史数据收集器
专门收集2025.01.01-2025.06.01期间的GOOGL和TSLA相关数据
"""

import os
import json
import time
import logging
import random
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
import argparse

import praw
import prawcore
from dotenv import load_dotenv
from tqdm import tqdm

# 加载环境变量
load_dotenv()

class HistoricalRedditCollector:
    """历史Reddit数据收集器"""
    
    def __init__(self, output_dir: str = "social_media_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 目标日期范围
        self.start_date = datetime(2025, 1, 1, tzinfo=timezone.utc)
        self.end_date = datetime(2025, 6, 1, tzinfo=timezone.utc)
        
        self.setup_logging()
        self.reddit = self.create_reddit_client()
        
        # 扩展的股票关键词，提高匹配准确性
        self.ticker_keywords = {
            'GOOGL': [
                'Google', 'GOOGL', 'GOOG', 'Alphabet', 'YouTube', 'Android', 
                'Chrome', 'Gmail', 'Google Search', 'Google Cloud', 'GCP',
                'Waymo', 'DeepMind', 'Pixel', 'Google Pay', 'AdWords',
                'Google Maps', 'Google Drive', 'Chromebook', 'Nest'
            ],
            'TSLA': [
                'Tesla', 'TSLA', 'Elon Musk', 'Model S', 'Model 3', 'Model Y', 
                'Model X', 'Cybertruck', 'Roadster', 'Semi', 'Supercharger',
                'Autopilot', 'FSD', 'Full Self Driving', 'Gigafactory',
                'Powerwall', 'Solar Roof', 'SpaceX', 'Neuralink'
            ]
        }
        
        # 目标子版块 - 包含更多金融和科技相关版块
        self.target_subreddits = [
            # 投资和金融版块
            'investing', 'stocks', 'SecurityAnalysis', 'ValueInvesting',
            'financialindependence', 'StockMarket', 'investing_discussion',
            
            # 科技版块
            'technology', 'tech', 'gadgets', 'apple', 'android',
            
            # 汽车和电动车版块
            'cars', 'electricvehicles', 'teslamotors', 'SelfDrivingCars',
            
            # 商业版块
            'business', 'entrepreneur', 'startups',
            
            # 新闻版块
            'news', 'worldnews', 'technews'
        ]
        
        # 处理过的帖子
        self.processed_posts: Set[str] = set()
        self.load_processed_posts()
        
        # 请求控制
        self.request_count = 0
        self.max_requests_per_hour = 60
        self.start_time = time.time()
        
        # 统计信息
        self.stats = {
            'total_posts_processed': 0,
            'relevant_posts_found': 0,
            'googl_posts': 0,
            'tsla_posts': 0,
            'subreddits_processed': 0,
            'date_range_posts': 0
        }
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "reddit_historical.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_reddit_client(self):
        """创建Reddit客户端"""
        client_id = os.getenv('REDDIT_CLIENT_ID')
        client_secret = os.getenv('REDDIT_CLIENT_SECRET')
        user_agent = os.getenv('REDDIT_USER_AGENT', 'historical-data-collector/1.0')
        
        if not client_id or not client_secret:
            raise ValueError("Reddit API凭据未配置")
        
        return praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
    
    def load_processed_posts(self):
        """加载已处理的帖子"""
        cache_file = self.output_dir / "processed_posts_historical.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.processed_posts = set(data.get('processed_posts', []))
                self.logger.info(f"加载了 {len(self.processed_posts)} 个已处理帖子ID")
            except Exception as e:
                self.logger.warning(f"加载缓存失败: {e}")
    
    def save_processed_posts(self):
        """保存已处理的帖子"""
        cache_file = self.output_dir / "processed_posts_historical.json"
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'processed_posts': list(self.processed_posts),
                    'last_updated': datetime.now().isoformat(),
                    'date_range': f"{self.start_date.date()} to {self.end_date.date()}",
                    'stats': self.stats
                }, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def check_rate_limit(self):
        """检查请求频率限制"""
        elapsed_time = time.time() - self.start_time
        if elapsed_time < 3600:  # 一小时内
            if self.request_count >= self.max_requests_per_hour:
                wait_time = 3600 - elapsed_time
                self.logger.info(f"达到请求限制，等待 {wait_time:.0f} 秒...")
                time.sleep(wait_time)
                self.request_count = 0
                self.start_time = time.time()
    
    def safe_request(self, func, *args, **kwargs):
        """安全执行请求"""
        self.check_rate_limit()
        self.request_count += 1
        
        # 随机延迟
        delay = random.uniform(3, 8)
        time.sleep(delay)
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 403:
                    wait_time = (attempt + 1) * 30
                    self.logger.warning(f"403错误，等待 {wait_time} 秒后重试")
                    time.sleep(wait_time)
                    continue
                elif e.response.status_code == 429:
                    wait_time = (attempt + 1) * 60
                    self.logger.warning(f"429限制错误，等待 {wait_time} 秒后重试")
                    time.sleep(wait_time)
                    continue
                else:
                    raise
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 15
                    self.logger.warning(f"请求失败，等待 {wait_time} 秒后重试: {e}")
                    time.sleep(wait_time)
                    continue
                else:
                    raise
        
        return None
    
    def extract_tickers_from_text(self, text: str) -> List[str]:
        """从文本中提取股票代码"""
        if not text:
            return []
        
        text_lower = text.lower()
        found_tickers = []
        
        for ticker, keywords in self.ticker_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    found_tickers.append(ticker)
                    break
        
        return list(set(found_tickers))
    
    def is_in_date_range(self, timestamp: float) -> bool:
        """检查时间戳是否在目标日期范围内"""
        post_date = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        return self.start_date <= post_date <= self.end_date
    
    def get_subreddit_posts(self, subreddit_name: str, limit: int = 50) -> List:
        """获取子版块帖子"""
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            
            # 尝试不同的排序方式获取更多历史数据
            all_posts = []
            
            # 获取热门帖子
            def get_hot_posts():
                return list(subreddit.hot(limit=limit))
            
            # 获取新帖子
            def get_new_posts():
                return list(subreddit.new(limit=limit))
            
            # 获取top帖子（不同时间范围）
            def get_top_posts(time_filter):
                return list(subreddit.top(time_filter=time_filter, limit=limit))
            
            # 尝试获取热门帖子
            try:
                hot_posts = self.safe_request(get_hot_posts)
                if hot_posts:
                    all_posts.extend(hot_posts)
                    self.logger.info(f"r/{subreddit_name}: 获取到 {len(hot_posts)} 个热门帖子")
            except Exception as e:
                self.logger.warning(f"获取热门帖子失败: {e}")
            
            # 尝试获取新帖子
            try:
                new_posts = self.safe_request(get_new_posts)
                if new_posts:
                    # 去重
                    existing_ids = {post.id for post in all_posts}
                    unique_new_posts = [post for post in new_posts if post.id not in existing_ids]
                    all_posts.extend(unique_new_posts)
                    self.logger.info(f"r/{subreddit_name}: 获取到 {len(unique_new_posts)} 个新帖子")
            except Exception as e:
                self.logger.warning(f"获取新帖子失败: {e}")
            
            # 尝试获取top帖子
            for time_filter in ['month', 'year']:
                try:
                    top_posts = self.safe_request(get_top_posts, time_filter)
                    if top_posts:
                        existing_ids = {post.id for post in all_posts}
                        unique_top_posts = [post for post in top_posts if post.id not in existing_ids]
                        all_posts.extend(unique_top_posts)
                        self.logger.info(f"r/{subreddit_name}: 获取到 {len(unique_top_posts)} 个{time_filter}热门帖子")
                except Exception as e:
                    self.logger.warning(f"获取{time_filter}热门帖子失败: {e}")
            
            return all_posts
            
        except Exception as e:
            self.logger.error(f"获取 r/{subreddit_name} 帖子失败: {e}")
            return []
    
    def process_submission(self, submission) -> Optional[Dict[str, Any]]:
        """处理单个提交"""
        try:
            if submission.id in self.processed_posts:
                return None
            
            # 检查日期范围
            if not self.is_in_date_range(submission.created_utc):
                return None
            
            self.stats['date_range_posts'] += 1
            
            # 获取文本内容
            title = getattr(submission, 'title', '') or ""
            content = getattr(submission, 'selftext', '') or ""
            full_text = f"{title} {content}"
            
            # 提取相关股票代码
            tickers = self.extract_tickers_from_text(full_text)
            if not tickers:
                return None
            
            # 创建帖子数据
            created_time = datetime.fromtimestamp(
                submission.created_utc, tz=timezone.utc
            ).replace(tzinfo=None)
            
            post_data = {
                'platform': 'reddit',
                'post_id': f"reddit_{submission.id}",
                'title': title,
                'content': content,
                'author': str(submission.author) if submission.author else 'deleted',
                'created_time': created_time.isoformat(),
                'url': f"https://reddit.com{submission.permalink}",
                'upvotes': getattr(submission, 'score', 0),
                'comments_count': getattr(submission, 'num_comments', 0),
                'tickers': tickers,
                'sentiment': 'neutral',
                'engagement_score': getattr(submission, 'score', 0) * 1.0 + getattr(submission, 'num_comments', 0) * 2.0,
                'source_subreddit': submission.subreddit.display_name,
                'hashtags': None
            }
            
            # 标记为已处理
            self.processed_posts.add(submission.id)
            self.stats['total_posts_processed'] += 1
            self.stats['relevant_posts_found'] += 1
            
            # 更新股票统计
            if 'GOOGL' in tickers:
                self.stats['googl_posts'] += 1
            if 'TSLA' in tickers:
                self.stats['tsla_posts'] += 1
            
            return post_data
            
        except Exception as e:
            self.logger.error(f"处理提交失败 {submission.id}: {e}")
            return None
    
    def save_posts_by_ticker_and_date(self, posts: List[Dict[str, Any]]):
        """按股票代码和日期保存帖子"""
        if not posts:
            return
        
        posts_by_ticker = {}
        
        for post in posts:
            for ticker in post['tickers']:
                if ticker not in posts_by_ticker:
                    posts_by_ticker[ticker] = []
                
                ticker_post = post.copy()
                ticker_post['ticker'] = ticker
                del ticker_post['tickers']
                
                posts_by_ticker[ticker].append(ticker_post)
        
        # 按股票代码保存到对应目录
        for ticker, ticker_posts in posts_by_ticker.items():
            ticker_dir = self.output_dir / f"{ticker}_social_media"
            ticker_dir.mkdir(parents=True, exist_ok=True)
            
            # 按日期分组保存
            posts_by_date = {}
            for post in ticker_posts:
                post_date = datetime.fromisoformat(post['created_time']).date()
                date_str = post_date.strftime('%Y-%m-%d')
                
                if date_str not in posts_by_date:
                    posts_by_date[date_str] = []
                posts_by_date[date_str].append(post)
            
            # 保存每个日期的数据
            for date_str, date_posts in posts_by_date.items():
                file_path = ticker_dir / f"reddit_{date_str}.json"
                
                # 合并现有数据
                existing_posts = []
                if file_path.exists():
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            existing_posts = json.load(f)
                    except Exception as e:
                        self.logger.warning(f"读取现有文件失败: {e}")
                
                # 去重并保存
                existing_ids = {post.get('post_id') for post in existing_posts}
                new_posts = [post for post in date_posts 
                            if post.get('post_id') not in existing_ids]
                
                if new_posts:
                    all_posts = existing_posts + new_posts
                    all_posts.sort(key=lambda x: x.get('created_time', ''))
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(all_posts, f, indent=2, ensure_ascii=False)
                    
                    self.logger.info(f"保存 {ticker} {date_str}: {len(new_posts)} 个新帖子")
    
    def collect_from_subreddit(self, subreddit_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """从子版块收集数据"""
        posts = []
        
        try:
            self.logger.info(f"开始收集 r/{subreddit_name} 数据...")
            
            submissions = self.get_subreddit_posts(subreddit_name, limit)
            
            if not submissions:
                self.logger.warning(f"r/{subreddit_name} 未获取到任何帖子")
                return posts
            
            self.logger.info(f"r/{subreddit_name} 获取到 {len(submissions)} 个帖子，开始筛选...")
            
            # 处理提交
            for submission in tqdm(submissions, desc=f"处理 r/{subreddit_name}"):
                try:
                    post_data = self.process_submission(submission)
                    if post_data:
                        posts.append(post_data)
                        self.logger.info(f"找到相关帖子: {post_data['title'][:50]}... (日期: {post_data['created_time'][:10]})")
                    
                except Exception as e:
                    self.logger.error(f"处理提交失败: {e}")
                    continue
            
            self.logger.info(f"r/{subreddit_name} 收集到 {len(posts)} 个相关帖子")
            self.stats['subreddits_processed'] += 1
            
        except Exception as e:
            self.logger.error(f"收集 r/{subreddit_name} 失败: {e}")
        
        return posts
    
    def collect_historical_data(self, limit_per_subreddit: int = 50) -> Dict[str, int]:
        """收集历史数据"""
        self.logger.info("开始收集Reddit历史数据")
        self.logger.info(f"日期范围: {self.start_date.date()} 到 {self.end_date.date()}")
        self.logger.info(f"目标股票: {list(self.ticker_keywords.keys())}")
        self.logger.info(f"目标子版块: {len(self.target_subreddits)} 个")
        
        all_posts = []
        
        for i, subreddit_name in enumerate(self.target_subreddits):
            try:
                self.logger.info(f"进度: {i+1}/{len(self.target_subreddits)} - r/{subreddit_name}")
                
                posts = self.collect_from_subreddit(subreddit_name, limit_per_subreddit)
                all_posts.extend(posts)
                
                # 定期保存数据
                if len(all_posts) >= 20 or i == len(self.target_subreddits) - 1:
                    self.save_posts_by_ticker_and_date(all_posts)
                    all_posts = []
                    self.save_processed_posts()
                
                # 子版块间添加延迟
                if i < len(self.target_subreddits) - 1:
                    delay = random.uniform(15, 30)
                    self.logger.info(f"等待 {delay:.1f} 秒后处理下一个子版块...")
                    time.sleep(delay)
                
            except Exception as e:
                self.logger.error(f"处理 r/{subreddit_name} 失败: {e}")
                continue
        
        # 保存剩余数据
        if all_posts:
            self.save_posts_by_ticker_and_date(all_posts)
        
        self.save_processed_posts()
        
        return self.stats

def main():
    parser = argparse.ArgumentParser(description='Reddit历史数据收集器 (2025.01.01-2025.06.01)')
    parser.add_argument('--output-dir', default='social_media_data', help='输出目录')
    parser.add_argument('--limit-per-subreddit', type=int, default=50, help='每个子版块的帖子限制')
    
    args = parser.parse_args()
    
    try:
        collector = HistoricalRedditCollector(args.output_dir)
        
        print("🚀 Reddit历史数据收集器启动")
        print(f"📅 目标日期范围: 2025-01-01 到 2025-06-01")
        print(f"📈 目标股票: GOOGL, TSLA")
        print(f"📂 输出目录: {args.output_dir}")
        print(f"🎯 子版块数量: {len(collector.target_subreddits)}")
        print(f"⏱️  预计完成时间: 2-4小时")
        print()
        
        stats = collector.collect_historical_data(args.limit_per_subreddit)
        
        print("\n" + "="*60)
        print("📊 Reddit历史数据收集完成")
        print("="*60)
        print(f"处理子版块数: {stats['subreddits_processed']}")
        print(f"总处理帖子数: {stats['total_posts_processed']}")
        print(f"日期范围内帖子: {stats['date_range_posts']}")
        print(f"相关帖子数: {stats['relevant_posts_found']}")
        print(f"GOOGL相关帖子: {stats['googl_posts']}")
        print(f"TSLA相关帖子: {stats['tsla_posts']}")
        print(f"数据保存在: {args.output_dir}/")
        print(f"  - GOOGL_social_media/")
        print(f"  - TSLA_social_media/")
        
    except Exception as e:
        print(f"收集失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
