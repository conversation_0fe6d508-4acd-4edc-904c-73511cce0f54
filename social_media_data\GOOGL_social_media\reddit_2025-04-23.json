[{"platform": "reddit", "post_id": "reddit_1k5on9o", "title": "OpenAI tells judge it would buy Chrome from Google", "content": "Your opinions on OpenAI owning Chrome?? ", "author": "LordKrazyMoos<PERSON>", "created_time": "2025-04-23T02:40:43", "url": "https://reddit.com/r/google/comments/1k5on9o/openai_tells_judge_it_would_buy_chrome_from_google/", "upvotes": 484, "comments_count": 105, "sentiment": "bullish", "engagement_score": 694.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k5p75l", "title": "OpenAI tells judge it would buy Chrome from Google", "content": "", "author": "mecha_power", "created_time": "2025-04-23T03:10:04", "url": "https://reddit.com/r/chrome/comments/1k5p75l/openai_tells_judge_it_would_buy_chrome_from_google/", "upvotes": 67, "comments_count": 33, "sentiment": "bullish", "engagement_score": 133.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k5rkqw", "title": "People Keep Asking Me: How Are AI Tools Like ChatGPT, Perplexity & DeepSeek Changing SEO in 2025? Here’s What’s Really Happening.", "content": "SEO in 2025 isn’t just about ranking #1 on Google. It’s about being visible *wherever* people are searching, whether that’s ChatGPT, Perplexity, or traditional search engines. AI is a tool, not a threat, if you know how to use it. \n\nSo here’s a breakdown of how these tools are impacting search engine optimization.\n\n**1. AI Is Changing** ***Search Behavior***  \nMore people are using AI chatbots instead of Google to get answers. This means fewer clicks to websites, aka *zero-click searches*.  \n**What it means for SEO:** We’re not just optimizing for Google anymore, we need to be visible *inside* AI responses too.\n\n**2. Search Engines Are Using AI to Rewrite Rankings**  \nGoogle’s AI-driven algorithms now look deeper into *content quality* using things like semantic relevance and *topical authority*.  \n**In simple terms:** It's not about stuffing keywords anymore. Google cares if your content is truly helpful, well-organized, and written by someone who knows what they’re talking about.\n\n**3. AI Is Helping SEOs Work Way Faster**  \nTools like DeepSeek and ChatGPT are doing tasks like:\n\n* Generating *keyword clusters* (grouping related search terms)\n* Creating *content briefs* (outlines for blog posts)\n* Running quick *technical SEO audits* (checking site health)\n\n**Takeaway:** AI handles the boring stuff, so humans can focus on strategy and creativity.\n\n**4. Less Traffic, But More Qualified Leads**  \nWith AI giving instant answers, raw *organic traffic* might drop. But the traffic you do get is more targeted people who are really interested.  \n**Your goal now:** Optimize for *conversion*, not just clicks.\n\n**5. AI-First SEO Means New Tactics**  \nWe’re now optimizing for:\n\n* *Featured snippets* (the answer boxes in Google)\n* *AI citations* (getting your content mentioned inside AI-generated answers)\n* *Entity SEO* (making your brand or product recognizable by AI systems)\n\nIn 2025, SEO is evolving faster than ever, and a lot of that is because of AI tools like ChatGPT, Perplexity, DeepSeek, Claude, and more.", "author": "atyychos_33", "created_time": "2025-04-23T05:30:41", "url": "https://reddit.com/r/DigitalMarketing/comments/1k5rkqw/people_keep_asking_me_how_are_ai_tools_like/", "upvotes": 6, "comments_count": 21, "sentiment": "bearish", "engagement_score": 48.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k5zubd", "title": "is Google AI snippet destroying SEO?", "content": "AI Snippet gives alot of the relevant info to the user.  I dont think people are gonna scroll down as much anymore and look at other info.   This is a drastic change in the business of SEO I think.   Are top 10 positions going to get less clicks now? I think so....", "author": "tonycarlo16", "created_time": "2025-04-23T13:58:03", "url": "https://reddit.com/r/SEO/comments/1k5zubd/is_google_ai_snippet_destroying_seo/", "upvotes": 101, "comments_count": 92, "sentiment": "neutral", "engagement_score": 285.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k63oed", "title": "How can I build some Google Ads experience?", "content": "Hi all,\n\nI’m currently in a position where I am looking for a new job. I work for a *very* small company and handle all of their SEO in house. Unfortunately, they are so small that they do not have a budget for paid ads. \n\nI’ve done 3 interviews so far, and all of them have turned me down due to my lack of Google Ads experience. \n\nWhat are some ways I can build real world experience with that? I already have the Google Ads and YouTube Ads certifications. I’d really like to avoid having to do another entry level position. And bonus points if it’s anything that I can do in the next few weeks-months. ", "author": "NateGman1", "created_time": "2025-04-23T16:34:43", "url": "https://reddit.com/r/DigitalMarketing/comments/1k63oed/how_can_i_build_some_google_ads_experience/", "upvotes": 11, "comments_count": 19, "sentiment": "neutral", "engagement_score": 49.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k652wg", "title": "Why does this sub hate the 5th largest global asset?", "content": "Very curious why mentioning this always leads to massive downvoting. Is it cope? Envy? Ignorance? Genuinely curious. \n\nhttps://www.coindesk.com/markets/2025/04/23/bitcoin-becomes-fifth-largest-global-asset-surpasses-google-s-market-cap", "author": "santiagotheboy", "created_time": "2025-04-23T17:30:57", "url": "https://reddit.com/r/Fire/comments/1k652wg/why_does_this_sub_hate_the_5th_largest_global/", "upvotes": 0, "comments_count": 71, "sentiment": "neutral", "engagement_score": 142.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k68zcy", "title": "Google forcing some remote workers to come back 3 days a week or lose their jobs", "content": "", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-04-23T20:07:38", "url": "https://reddit.com/r/technology/comments/1k68zcy/google_forcing_some_remote_workers_to_come_back_3/", "upvotes": 3322, "comments_count": 286, "sentiment": "neutral", "engagement_score": 3894.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k69d8w", "title": "Is aggressive dividend investing the quickest and most passive way for most people to reach work optional status?", "content": "Aside from rare examples like large inheritance, winning the lottery, becoming famous, being an owner/entrepreneur of a top company (Apple, Google, Tesla) etc? ", "author": "da<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-23T20:23:31", "url": "https://reddit.com/r/dividends/comments/1k69d8w/is_aggressive_dividend_investing_the_quickest_and/", "upvotes": 114, "comments_count": 71, "sentiment": "neutral", "engagement_score": 256.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k6djlu", "title": "My considerations over Google", "content": "Google is my biggest position in [portfolio](https://app.mecompounding.com/reference/53616c7465645f5f20c5cd096e20892c1db5765dc25a5b41276cc2a55245f5d9a8da462890eb87a2b5f546cd08d60bea) and I would like to share some thoughts on it before an upcoming earnings.  \n  \n**1. Every** segment of [Google's business is growing](https://app.mecompounding.com/tickers/GOOGL/financials) and creating an overall revenue growth rate 12-15% Y/Y.  \n2. The financial foundation is really strong. The long term debt is 3 times lower than yearly free cash flow, assets is 4 times bigger than liabilities, the margins are improving almost quarterly.  \n3. Everyone is running is screaming that Google Search is dead and ChatGPT would kill it, but the search revenue has grown 26% since the launch of ChatGPT in November of 2022. Moreover, Google's Gemeni is really, really strong. I use it for work and analysis.  \n4. DOJ scrutiny? Yes, it hearts, but Google will service and went out stronger (look how Microsoft, Visa and other giants can live with it for years). Moreover new Google investments will surpass the damage.  \n5. When I look on new Google's investments and tragectory, I think they are safe for now. The fastest growing cloud, Waymo is getting new licenses, best AI in class, improvements in search.\n\nThe company that is growing revenue, earnings, rewarding investors with buybacks and dividends are trading at 19P/E.   \nThis is the multiple of [NKE](https://app.mecompounding.com/tickers/NKE/summary) and [PEP](https://app.mecompounding.com/tickers/PEP/summary). Let me remind you that NKE has decreasing margins, decreasing market share, EPS and revenue are falling for 2 years almost every quarter, and still the valuation is 19P/E. PEP is lowering their forecast and has almost no growth neither in revenue nor EPS. Additionally, they have significant dividend burden. And still 20P/E.\n\nI just don't think that GOOGL really deserves such treatment. The company is really strong and is doing great strategic investments. I think right now might be a good time to add some GOOGL shares to your portfolio. WDYT?", "author": "Ok-Championship4945", "created_time": "2025-04-23T23:22:17", "url": "https://reddit.com/r/investing/comments/1k6djlu/my_considerations_over_google/", "upvotes": 33, "comments_count": 51, "sentiment": "bullish", "engagement_score": 135.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k6dt53", "title": "To everyone who advertises on both Meta and Google - what gives you better results?", "content": "Have you noticed a dip in conversions of both  in March and April? \n\n", "author": "alphaevil", "created_time": "2025-04-23T23:34:30", "url": "https://reddit.com/r/PPC/comments/1k6dt53/to_everyone_who_advertises_on_both_meta_and/", "upvotes": 27, "comments_count": 40, "sentiment": "neutral", "engagement_score": 107.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}]