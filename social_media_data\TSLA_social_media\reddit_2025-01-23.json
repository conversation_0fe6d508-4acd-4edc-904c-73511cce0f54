[{"platform": "reddit", "post_id": "reddit_1i7w14t", "title": "Looking for a Partner to Build an Analytics Startup", "content": "\nI’m a skilled financial and data analyst with experience working for Fortune 500 companies across various industries, including oil & gas and REITs. With an MBA and a Bachelor’s in Finance from the University of Texas at San Antonio , I specialize in Excel, Power BI, financial modeling, business intelligence, and data-driven decision-making.\n\nI’m currently looking for a like-minded partner interested in launching an analytics startup focused on delivering data-driven solutions for small  to medium businesses. My strengths lie in financial analysis, data visualization, and automation using tools like Power BI, Power Automate, and Excel. I’m also well-versed in working with APIs and building scalable reporting solutions.\n\nIf you’re passionate about data, business strategy, and helping companies make better decisions through analytics—whether you have technical expertise, a business development mindset, or industry-specific experience—let’s connect and explore how we can build something impactful together.\n\nLet’s discuss ideas and collaborate to turn insights into actionable value.\nMessage me ! \n\nThanks ", "author": "Unfair-Pause2166", "created_time": "2025-01-23T05:07:17", "url": "https://reddit.com/r/analytics/comments/1i7w14t/looking_for_a_partner_to_build_an_analytics/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i7x2kx", "title": "My kingdom for a compostable floss pick…", "content": "I don’t understand why this doesn’t exist. Compostable floss exists. Compostable plastic substitutes exist. Has anyone ever heard of one? Someone in product development, get me a BPI certified floss pick, and I will give you so much money.", "author": "this_is_nunya", "created_time": "2025-01-23T06:14:17", "url": "https://reddit.com/r/sustainability/comments/1i7x2kx/my_kingdom_for_a_compostable_floss_pick/", "upvotes": 70, "comments_count": 31, "sentiment": "bearish", "engagement_score": 132.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i847g6", "title": "The RTX 5090 - Our Biggest Review Ever", "content": "", "author": "Nestledrink", "created_time": "2025-01-23T14:11:06", "url": "https://reddit.com/r/nvidia/comments/1i847g6/the_rtx_5090_our_biggest_review_ever/", "upvotes": 0, "comments_count": 41, "sentiment": "neutral", "engagement_score": 82.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i84usn", "title": "NVIDIA GeForce RTX 5090 review: Pure AI excess for 2,000 Dollars", "content": "", "author": "a_Ninja_b0y", "created_time": "2025-01-23T14:42:02", "url": "https://reddit.com/r/gadgets/comments/1i84usn/nvidia_geforce_rtx_5090_review_pure_ai_excess_for/", "upvotes": 908, "comments_count": 277, "sentiment": "neutral", "engagement_score": 1462.0, "source_subreddit": "gadgets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i8ca4q", "title": "[JO<PERSON> O<PERSON>] Senior Simulation Robotics Engineer, Symbotic.", "content": "**Who we are**  \nWith its A.I.-powered robotic technology platform, Symbotic is changing the way consumer goods move through the supply chain. Intelligent software orchestrates advanced robots in a high-density, end-to-end system – reinventing warehouse automation for increased efficiency, speed and flexibility.\n\n**What we need**  \nAs a Senior Robotics Engineer, you will play a key role in the development of simulation systems and tools responsible for supporting the design, development, qualification, and deployment of large scale integrated robotic systems for our customers. We are looking for people who thrive in a creative, collaborative, and agile development environment.\n\n**What you'll do** Develop faster than real-time and optimized simulation environments for driving key design decisions for new systems and products Analyze and evaluate existing simulation tools and drive improvements that enable faster, more scalable, efficient, and low-cost solutions for use across the Symbotic development teams Lead the design and implementation of a hierarchy of simulation capabilities to support the development and analysis of both component and integrated systems-level digital twins.\n\nTake responsibility for all aspects of the simulation tools used throughout the organization including individual robots, perception, controls, fleet-level operations, routing, and system-wide simulations used during testing and design of new features Drive project scoping and requirement specification providing senior leadership with deep technical insight needed to create long-term technical roadmaps\n\nAmplify impact through other team members by serving as a technical mentor to guide the team toward innovative solutions and increased productivity.\n\nTo learn more & apply please visit: [https://www.simulationengineerjobs.com/](https://www.simulationengineerjobs.com/)", "author": "Gold_Worry_3188", "created_time": "2025-01-23T19:53:08", "url": "https://reddit.com/r/AutonomousVehicles/comments/1i8ca4q/job_opening_senior_simulation_robotics_engineer/", "upvotes": 1, "comments_count": 0, "sentiment": "bullish", "engagement_score": 1.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i8d3eb", "title": "I just dropped $69k on 1DTE $TSLA $415 calls, Model Y refresh goes on sale in the US tonight!", "content": "", "author": "RealMatthewDR", "created_time": "2025-01-23T20:27:21", "url": "https://reddit.com/r/wallstreetbets/comments/1i8d3eb/i_just_dropped_69k_on_1dte_tsla_415_calls_model_y/", "upvotes": 990, "comments_count": 1255, "sentiment": "neutral", "engagement_score": 3500.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i8hp9v", "title": "How to Start a Data Analyst Career With No Degree or Certificates?", "content": "Hey everyone,\n\nI’m really interested in starting a career as a data analyst, but I don’t have a college degree or any certifications. I’m starting completely from scratch with zero experience. I know it’s a competitive field, but I’m ready to put in the work.\n\nCould you help me figure out:\n\n1. What skills I need to learn to get started?\n2. Which certifications (if any) are worth pursuing to build credibility?\n3. How I can gain experience when I don’t have any professional background in data analysis?\n\nHere’s what I know so far:\n\n* Data analysts work a lot with tools like Excel, SQL, Tableau, and Python/R.\n* I need to understand concepts like data cleaning, visualization, and reporting.\n* Communication skills are important to present findings.\n\nBut what would be the best path for someone like me? Should I dive into free resources online? Are there specific entry-level jobs that can help me transition into data analysis? How can I start building a portfolio to showcase my skills without professional experience?\n\nI’m also wondering about certifications like Google Data Analytics, Microsoft Power BI, or even Coursera/edX courses—are they really necessary, or can I get by with just self-study and practice?\n\nAny advice, personal stories, or resources would be super helpful! Thanks in advance!", "author": "shomeeee", "created_time": "2025-01-23T23:45:43", "url": "https://reddit.com/r/analytics/comments/1i8hp9v/how_to_start_a_data_analyst_career_with_no_degree/", "upvotes": 0, "comments_count": 41, "sentiment": "neutral", "engagement_score": 82.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1i8ht62", "title": "General Investing Advice", "content": "Hey there, I am relatively new to Reddit, and this is my first post. A lot of you seem to really know what you’re doing, I would like some advice. \n\nI rolled over my 401k into an IRA. It was not a large amount, only $3,081. I did some research and ended up buying 7 shares of Google, and 7 shares of NVDA yesterday. I plan on buying more of those, and also buying Microsoft and Apple. I have a lot of catching up to do, I will need to max out my IRA every year. \n\nI have $677.41 left over. I left that there for cheap stocks or coins that I can pump and dump. What stocks or coins would you recommend for short term gain? What information sources do you use to get this knowledge? I want to be in the loop so to speak. I wasn’t aware of the Trump or Melania coin that went crazy. Perfect pump and dump opportunity that I missed out on. Basically, I don’t want to miss any more big opportunities like that. I’m open to any and all suggestions. Thank you for reading this. ", "author": "qwerty7769", "created_time": "2025-01-23T23:50:52", "url": "https://reddit.com/r/investing_discussion/comments/1i8ht62/general_investing_advice/", "upvotes": 1, "comments_count": 6, "sentiment": "bullish", "engagement_score": 13.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}]