[{"platform": "reddit", "post_id": "reddit_1ibp6s4", "title": "Google says it will change Gulf of Mexico to 'Gulf of America' in Maps after government updates", "content": "", "author": "HighwayWilderness", "created_time": "2025-01-28T00:45:45", "url": "https://reddit.com/r/worldnews/comments/1ibp6s4/google_says_it_will_change_gulf_of_mexico_to_gulf/", "upvotes": 27128, "comments_count": 4584, "sentiment": "neutral", "engagement_score": 36296.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibtz4v", "title": "Google says it will change Gulf of Mexico to 'Gulf of America' in Maps app after government updates", "content": "", "author": "GravelySilly", "created_time": "2025-01-28T04:54:43", "url": "https://reddit.com/r/nottheonion/comments/1ibtz4v/google_says_it_will_change_gulf_of_mexico_to_gulf/", "upvotes": 5472, "comments_count": 902, "sentiment": "neutral", "engagement_score": 7276.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibwmv4", "title": "deepseek is now more popular than chatgpt in google searches", "content": "", "author": "eternviking", "created_time": "2025-01-28T07:53:58", "url": "https://reddit.com/r/artificial/comments/1ibwmv4/deepseek_is_now_more_popular_than_chatgpt_in/", "upvotes": 46, "comments_count": 19, "sentiment": "neutral", "engagement_score": 84.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibwr6u", "title": "Has China FSD caught up?", "content": "If BYD has FSD \"V13+\" already in China, what's Tesla's MOAT?  \n  \nWatching this video of BYD's [FSD ](https://youtu.be/Qjl3r3D2tG4?si=D6MxhMtUHGWmdKzl&t=340)in action, I'm shook. Never imagined FSD in China has caught up or surpassed Tesla FSD.  \nJust one intervention at [05:40](https://youtu.be/Qjl3r3D2tG4?si=D6MxhMtUHGWmdKzl&t=340) mark in 30 minute drive with hundreds of scooters and jaywalkers rampant at every turn.\n\nDo I start selling my TSLA shares and looking into Chinese stocks?\n\n\\-\n\nHere's a brief synopsis of the video (ChatGPT)\n\n* **Introduction and Setup**:\n   * The challenge involves testing BYD’s autonomous driving capabilities under extreme conditions in a crowded, rural Chinese city at night, with a mix of people and scooters on the roads.\n   * The test vehicle is the Denza G9 GT, capable of urban autonomous driving but not yet fully updated for parking features.\n* **Initial Observations**:\n   * The car adjusts smoothly to dynamic situations like people walking onto the road, scooters changing lanes unexpectedly, and non-standard traffic patterns.\n   * It handles missing lane markings and unusual left-turn signals well, demonstrating reliable lane-changing and speed adjustments.\n* **Complex Traffic Scenarios**:\n   * Encounters included scooters suddenly appearing, pedestrians jaywalking, and erratically parked vehicles.\n   * The AI adjusts speed, yields to pedestrians, and navigates intersections effectively, though it struggles with areas lacking traffic signals or clear road markings.\n* **Challenges with Local Traffic Norms**:\n   * In some areas, straight and left-turn signals work simultaneously, leading to chaos.\n   * The car successfully handles these situations, adhering to traffic rules while ensuring safety for nearby scooters and pedestrians.\n* **Specific Difficulties**:\n   * In a school zone, the car yielded to crossing students, causing a delay that led to a violation notification for obstructing traffic.\n   * This highlighted differences in local driving expectations and challenges faced by autonomous systems in adhering to nuanced human behaviors.\n* **Performance in Crowded Areas**:\n   * The car safely navigated through congested areas like shopping districts with heavy foot and scooter traffic.\n   * Despite tight spaces and unpredictable movements, the AI avoided collisions and maintained a smooth ride.\n* **Critiques and Reflections**:\n   * Observations on China’s traffic system pointed out inefficiencies like conflicting signals and reckless driving behaviors.\n   * The narrator expressed frustration over receiving a traffic violation for prioritizing pedestrian safety.\n* **Conclusion**:\n   * The test showcased the potential and limitations of the BYD vehicle’s autonomous driving in extreme real-world conditions.\n   * The system’s reliance on LIDAR and its ability to handle chaotic traffic were impressive, but legal and cultural challenges remain significant barriers.\n   * Questions were raised about whether similar autonomous features would be released in other markets like Korea.", "author": "Upset-Apartment1959", "created_time": "2025-01-28T08:03:11", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ibwr6u/has_china_fsd_caught_up/", "upvotes": 19, "comments_count": 84, "sentiment": "bearish", "engagement_score": 187.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibxbfz", "title": "Desperately need help with lead generation", "content": "My business is experiencing a pretty rough time. We're a B2B SaaS provider of automation solutions for small enterprises. Over the last few months, we've been losing MRR quickly due to high monthly churn.\n\nThere are two issues:\n\n1. **Churn:** We're losing our current clients due to (what I believe to be) a significant discrepancy between how our leads understand our service and what we deliver.\n2. **Lead generation:** Our lead gen tactics, like Google Ads and outbound email campaigns, no longer yield the same ROI. We're paying a lot for ads, and our organic channels have plateaued. Basically, our lead quality has plummeted.\n\nOur team is small but competent and operates on a minimal monthly budget. Does anyone know how to improve our situation, or has anyone been in a similar spot?\n\nPlease let me know what you found effective in finding amazing leads.\n\n**Edit:** Thanks for all the help, folks! I tried <PERSON><PERSON>'s free trial, and it's decent if you just need a handful of emails to test out. It pulls verified emails from LinkedIn Sales Navigator but the free credits run out fast. ", "author": "Forsaken-Spell8853", "created_time": "2025-01-28T08:47:58", "url": "https://reddit.com/r/DigitalMarketing/comments/1ibxbfz/desperately_need_help_with_lead_generation/", "upvotes": 82, "comments_count": 49, "sentiment": "bullish", "engagement_score": 180.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ic004x", "title": "Pebble’s founder wants to relaunch the e-paper smartwatch for its fans | With help from Google, a focused team wants to make sustainable watches.", "content": "", "author": "chrisdh79", "created_time": "2025-01-28T12:02:20", "url": "https://reddit.com/r/gadgets/comments/1ic004x/pebbles_founder_wants_to_relaunch_the_epaper/", "upvotes": 1854, "comments_count": 147, "sentiment": "neutral", "engagement_score": 2148.0, "source_subreddit": "gadgets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ic20nn", "title": "What are the best free marketing courses for 2025?", "content": "I've curated a list of 100% free marketing courses. From SEO and content marketing to social media strategies, these courses will help you build the skills needed to get started with digital marketing in 2025.\n\n**Any other good courses missing from the list?**\n\n**#General Marketing Courses**\n\n1. Digital Marketing Course For Beginners (Reliablesoft)\n\n2. Fundamentals of Digital Marketing (Google)\n\n3. Digital Marketing Associate (Meta)\n\n4. Google Analytics Certification (Google)\n\n**#SEO Courses**\n\n5. Free SEO Course for Beginners (Reliablesoft)\n\n6. The One-Hour Guide to SEO (Moz)\n\n7. Free SEO training: SEO for Beginners (Yoast)\n\n8. SEO Course For Beginners (Ahrefs)\n\n**#PPC Marketing Courses**\n\n9. Google Ads Certification (Google)\n\n10. PPC Fundamentals Certification (Semrush)\n\n**#Affiliate Marketing Courses**\n\n11. Free Affiliate Marketing Course for Beginners (Reliablesoft)\n\n12. Affiliate Marketing Course (Udemy)\n\n13. Affiliate Marketing Course (Ahrefs)\n\n**#Social Media Marketing Courses**\n\n14. Social Media Mastery (Canva)\n\n15. Diploma in Social Media Strategy (Alison)\n\n16. How To Build Your Social Media Marketing Strategy (Udemy)\n\n**#Content Marketing Courses**\n\n17. Content Marketing Certification Course (Hubspot)\n\n18. Advanced Content Marketing with <PERSON> (Semrush)\n\n**#Email Marketing Courses**\n\n19. Connect Through Email (Google)\n\n20. Email Marketing Masterclass for Beginners (WishPond)", "author": "myln", "created_time": "2025-01-28T13:52:50", "url": "https://reddit.com/r/DigitalMarketing/comments/1ic20nn/what_are_the_best_free_marketing_courses_for_2025/", "upvotes": 145, "comments_count": 57, "sentiment": "neutral", "engagement_score": 259.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ic6sne", "title": "Google Maps to rename Gulf of Mexico, Denali for US users", "content": "", "author": "Redd24_7", "created_time": "2025-01-28T17:21:59", "url": "https://reddit.com/r/GoogleMaps/comments/1ic6sne/google_maps_to_rename_gulf_of_mexico_denali_for/", "upvotes": 53, "comments_count": 43, "sentiment": "neutral", "engagement_score": 139.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ic8rn8", "title": "As someone who hires digital marketing roles...", "content": "The quality of your resume matters. I am the director of digital marketing, marketing analytics, and marketing operations for a mid-size company. I hire a hand-full of people every year and go through literally thousands of resumes per position. Our positions are fully remote and potential candidates can be anywhere in the US or Canada so we received a lot of applicants. The current digital marketing manager role I am hiring pays up to $155K and I have received 2172 resumes for the position. Of those, I have moved 13 candidates through to my hiring manager for an initial phone interview.\n\nFor context, for those familiar with it, we use Greenhouse as our HR platform. I open and look at every single resume that comes through. I can tell in about 10 seconds if someone is a hard pass for me. It doesn't mean that they might not be qualified, it just means the resume is so underwhelming that I am moving on to the next one. \n\nI understand this is my personal perspective and others will vary. That said, here is what I am looking for:\n\n* Your resume needs to stand out! I am hiring for marketing positions. If you cannot market yourself, how can I trust you managing a $5m budget?\n* If you are not good at building a resume, go to Etsy and pay $20 for a well designed resume that is aesthetically pleasing and is formatted in a way that you can highlight your experience.\n* I know not everyone agrees but use (some) color in your resume. When I am going through 30 resumes and I am getting hit with all black text only brick of text resumes one after another, they rarely catch my eye. Even better, match the color scheme (or color) to include the company's color pallet. It's a subconscious trick that will resonate with people who review a lot of resumes.\n* Keep it under 2 pages. I don't care how much experience you have, I am only looking at your last couple of positions as my focus.\n* Do not highlight your freelance experience as the focus of your resume. Since I am hiring a fully remote role, I will be concerned that you are going to be working two gigs if your resume focus is freelance work. You can include it, but don't make that a focus of your work history.\n* Absolutely list all of the platforms and tools that you have experience with. I always look at those when they are listed. If you list Google Ads, Meta Ads, Bing Ads, Marketo, Salesforce, Tableau, SEMRush, and other platforms that we use, I am going to give your resume more attention.\n* Do the small things. If I am hiring for a digital marketing manager position, indicate that you are looking for a digital marketing manager role. Don't say you are a \"digital expert\" or that you are seeking a \"senior digital role\". I want someone who identifies as seeking the role for which I am hiring.\n* If you include a cover letter, make sure it is personalized for the company and written specifically to communicate why this particular role is interesting to you and why our company seems like a good fit for you. If you are sending generic cover letters, you might as well not send it.\n* Imbed a link to your LinkedIn profile. Imbed a link to your portfolio if you have one. It's a small thing but I am more likely to look at them if I don't have to copy and paste links into my browser.\n* Lastly, for the love of all that is holy, do not write your resume or cover letter in third person. I will immediately think you are a narcissistic lunatic and hit the reject button without reading another word.\n\nHopefully this is helpful for someone. I go through a lot of resumes and most of of them are bad. If you are sending out dozens (or hundreds) of resumes and not getting any hits, change your resume. It can be as simple as downloading a resume from Etsy and sending something out with a little character. Market yourself. Happy hunting!", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-28T18:42:13", "url": "https://reddit.com/r/DigitalMarketing/comments/1ic8rn8/as_someone_who_hires_digital_marketing_roles/", "upvotes": 152, "comments_count": 73, "sentiment": "bullish", "engagement_score": 298.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ic9khi", "title": "Google is Capitulating to Trump Absurdities", "content": "", "author": "K-tel", "created_time": "2025-01-28T19:14:21", "url": "https://reddit.com/r/technology/comments/1ic9khi/google_is_capitulating_to_trump_absurdities/", "upvotes": 3975, "comments_count": 375, "sentiment": "neutral", "engagement_score": 4725.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1icaqm2", "title": "Reddit just killed a significant portion of Google's search traffic with its upcoming inbuilt feature \"reddit answers\" - currently in beta.", "content": "", "author": "eternviking", "created_time": "2025-01-28T20:00:47", "url": "https://reddit.com/r/artificial/comments/1icaqm2/reddit_just_killed_a_significant_portion_of/", "upvotes": 176, "comments_count": 50, "sentiment": "neutral", "engagement_score": 276.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1icczqe", "title": "Marketing guy claims he needs Primary Ownership of Google Business listing. Why?", "content": "We've already given an ownership role to him, and he now claims he needs Primary Ownership to do his \"SEO magic\". I've only heard this second hand so I don't know the exact explanation. The quote is not his, just my snarky summary. \n\nIs there any possible reason he would need this? It seems very sketch and everything I've read says even the Manager role has all the same permissions other than user management. He has ownership already, why would he need more? And if this is malicious, what would be his angle?", "author": "DuckenHahnchen", "created_time": "2025-01-28T21:33:31", "url": "https://reddit.com/r/marketing/comments/1icczqe/marketing_guy_claims_he_needs_primary_ownership/", "upvotes": 19, "comments_count": 33, "sentiment": "neutral", "engagement_score": 85.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1icd3tr", "title": "Google Ruined My Wife's Pixel 4a, Offered Compensation, and Then Made It Useless… Here's the Story", "content": "Hey r/GooglePixel,\n\nI’m not super Reddit-savvy, but I don’t know where else to go. I need to share my wife's frustrating experience with Google's recent Pixel 4a \"Battery Performance\" update. You know, the one where Google forced an update that bricked her perfectly good phone and then gave her $100 credit as \"compensation\"? Well, buckle up, because it gets worse.\n\nHere’s what happened:\n\n1. The Issue: Like many others, my wife's Pixel 4a was essentially ruined after Google's mandatory forced update. Google’s handling of this update has created waves of frustration across its user base. Battery life? Toast. Usability? Gone. Her once-reliable phone was rendered useless. \n2. The “Fix”: Google offered $100 store credit to use toward a new Pixel device. Fine, we thought—not ideal, but at least it's something, right?\n3. The Catch: We went to the Google Store to buy a Pixel 8a that was already on sale. But when we tried to redeem the $100 credit, the system wouldn’t let us… because the phone was \"already discounted.\"\n\nLet me say that again: Google gave my wife $100 credit because they broke her phone… but won’t let her use it to buy the replacement device because it’s on sale. The sale price has nothing to do with the situation—this is supposed to be compensation, not a coupon!\n\n1. Google’s Support Response: We reached out to customer support (full transcript here: [https://imgur.com/a/w3uBueZ](https://imgur.com/a/w3uBueZ) ). We explained that this isn’t a typical promo code—it’s a voucher to make up for their forced update ruining her phone. After being bounced around and asked to \"try incognito mode\" (seriously?), they ultimately said they couldn’t help us. When we attempted to further explain the situation, they abruptly disconnected the chat without resolving anything.\n\nThe excuse? \"Promo codes cannot be combined with other promotions.\" But nowhere in the terms does it say that this credit can’t be used on a sale item. Even worse, others have apparently been able to use their vouchers on discounted phones. Why the inconsistency?\n\n1. Adding Insult to Injury: If she had chosen the $50 cash option instead of the $100 store credit, she wouldn’t have been restricted like this. The $100 store credit is essentially Google saying, \"We’re sorry for ruining your phone, but please consider buying a Pixel again.\" Yet when we try to, they make it as difficult as possible.\n2. Even Worse: To add fuel to the fire, we’re now seeing reports from others who, after being refused the ability to use their store credit, are also being denied the option to go back and choose the $50 cash alternative. So not only is the credit worthless in many cases, but once you’ve picked it, you’re stuck with it.\n\nMy Takeaway: This \"compensation\" is essentially worthless if my wife can’t use it to buy the replacement phone it’s meant for. Google’s update broke her phone, and now they’re nickel-and-diming us on their so-called solution. It’s insulting.\n\nHere’s the simple solution Google should offer:\n\n* Option 1: Let her uninstall the update and return her phone to its previous, fully functional state.\n* Option 2: If that’s not possible, honor your commitment and compensate her properly for her broken phone—whether that means unrestricted store credit, cash, or a direct replacement.\n\nI wanted to share this here because:\n\n* Awareness: If you’re dealing with the same issue, you’re not alone.\n* Advice: If anyone’s successfully navigated this nonsense, I’d love to hear how you did it.\n* Accountability: Google needs to know this isn’t okay. Their lack of support is unacceptable, and they need to do better.\n\nLet’s make some noise. If you’ve dealt with this or have thoughts, drop them in the comments. Google, if you’re reading this: Fix this mess.\n\nWe’ve fallen in love with Pixel devices over the years because they’ve been so reliable. We’ve purchased several Pixels, trusting Google to deliver great products. But now, Google has destroyed that trust with how they’re handling this issue. It’s heartbreaking to see a brand we believed in let its loyal customers down.", "author": "Kunzite_", "created_time": "2025-01-28T21:38:14", "url": "https://reddit.com/r/GooglePixel/comments/1icd3tr/google_ruined_my_wifes_pixel_4a_offered/", "upvotes": 719, "comments_count": 183, "sentiment": "bullish", "engagement_score": 1085.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1icera6", "title": "My Google-fu sucks. I'm trying to understand how much power is exporting to the grid at any given time", "content": "So this is saying I'm producing 4kw with my panels, I get that. But is that the hourly \"rate\"? Clear sky for an hour I get 4kw? If that's the case, what happens if there's 50 minutes of generation at 4kw but then the last 10 minutes drops to 1.5kw because magically the sky turned cloudy? Does the energy exporting in this picture mean I'm sending 3.1kw **per hour** to the grid?\n\nI'm sorry if these questions are basic and easy to find online answers to, again I suck at asking Google the right questions, and I didn't know what questions to ask my installer before. \n\nThanks for any help.", "author": "stevozip", "created_time": "2025-01-28T22:48:20", "url": "https://reddit.com/r/solar/comments/1icera6/my_googlefu_sucks_im_trying_to_understand_how/", "upvotes": 2, "comments_count": 8, "sentiment": "bearish", "engagement_score": 18.0, "source_subreddit": "solar", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1icfmvq", "title": "Google declares U.S. ‘sensitive country’ like China, Russia after <PERSON>'s map changes", "content": "", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-01-28T23:26:56", "url": "https://reddit.com/r/technology/comments/1icfmvq/google_declares_us_sensitive_country_like_china/", "upvotes": 51217, "comments_count": 1711, "sentiment": "neutral", "engagement_score": 54639.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}]