[{"platform": "reddit", "post_id": "reddit_1if2xeq", "title": "Apple's So Worried About Losing $20 Bilion every year, They're Demanding the Court to Not Block Their Google search Deal.", "content": "https://www.theverge.com/news/603998/apple-google-search-remedies-monopoly-trial-stay-request", "author": "Yazzdevoleps", "created_time": "2025-02-01T09:39:45", "url": "https://reddit.com/r/google/comments/1if2xeq/apples_so_worried_about_losing_20_bilion_every/", "upvotes": 354, "comments_count": 36, "sentiment": "neutral", "engagement_score": 426.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1if3n1a", "title": "Simply script to check for RTX 5090 FE NVIDIA stock (on NVIDIA Marketplace)", "content": "So, you missed out on the (paper) launch due to bots or slow F5'ing? The best way to increase your chances is to optimize your search and automate stock monitoring for GPUs. Luckily for you, I have a free, simple script that you can run in Google Chrome without any additional downloads! Forget those complicated headless software setups—this requires only two things.\n\n**Setting Up the Script in Google Chrome**\n\n**Step 1:** Bookmark the following script. It injects the latest jQuery into your current webpage.\n\n    javascript:(function(e,s){e.src=s;e.onload=function(){jQuery.noConflict();console.log('jQuery injected')};document.head.appendChild(e);})(document.createElement('script'),'//code.jquery.com/jquery-latest.min.js')\n\n**Step 2:** In Google Chrome, press **F12** and click on the **\"Sources\"** tab. Navigate to **\"Snippets\"**, then click **\"New snippet\"**.\n\n**Step 3:** Copy and paste the following code, then press **CTRL+S** to save the snippet. Be sure to change `\"locale=COUNTRY\"` to the area you want to monitor (e.g., `\"be\"` for Belgium, `\"nl\"` for the Netherlands, `\"de\"` for Germany, etc.).\n\n`//inject Jquery first`\n\n`function myTimer() {`\n\n`jQuery.getJSON('https://api.store.nvidia.com/partner/v1/feinventory?status=1&skus=PROGFTNV590&locale=NL', function(data) {`\n\n`// JSON result in \\`data\\` variable\\`\n\n`var text0 = data.listMap[0].is_active`\n\n`var url0 = data.listMap[0].product_url`\n\n`console.log(text0);`\n\n`if (text0 == 'true') {`\n\n`console.log('5090 is available!');`\n\n`window.open(url0);`\n\n`for (var i = 1; i < 9999; i++) clearInterval(i);`\n\n`}`\n\n`else{`\n\n`console.log('5090 not available!');`\n\n`}`\n\n`});}`\n\n`setInterval(myTimer, 2000);`\n\n**How to Use the Script (video example in Notes)**\n\n**Step 1:** Navigate to any webpage. (I usually use a blank page to monitor network activity, but you can also go to the NVIDIA marketplace webpage.)\n\n**Step 2:** Open the developer console by pressing **F12** and navigating to the **\"Console\"** tab.\n\n**Step 3:** Click on the bookmarked page (with the jQuery script). This will inject jQuery into the website. The console should confirm that jQuery has been injected.\n\n**Step 4:** Go to the snippet (**Sources → Snippets**), click on the snippet, and press **CTRL+Enter** to run it. The script will check the NVIDIA stock API every second. When stock is available (`is_active = true`), it will open the `product_url` in a new tab. Once stock is detected and a tab is opened, the script will stop checking to prevent opening a new tab every second.\n\n*Notes*\n\n*You can remove the console.log statements to reduce memory usage.*\n\n*Increase the timer interval to at to avoid a temporary ban. Using a higher value is recommended for long-term monitoring.*\n\n*Looking for RTX5080, change the SKU to PRO580GFTNV*\n\n*Useage video:* \n\nhttps://reddit.com/link/1if3n1a/video/qbdquh08lige1/player\n\n", "author": "B<PERSON>bjair", "created_time": "2025-02-01T10:34:22", "url": "https://reddit.com/r/nvidia/comments/1if3n1a/simply_script_to_check_for_rtx_5090_fe_nvidia/", "upvotes": 235, "comments_count": 175, "sentiment": "bullish", "engagement_score": 585.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1if7p41", "title": "Tesla Long Call", "content": "I bought 1 Call $415 on TSLA on 1/23 with expiration of 1/31.  I was betting it would have a spike on earning.   As you saw TSLA dropped under 400 going into earning.  On Friday though I went to sell my call when it was over $416.25 and I only had $334 of the value. Ended up losing.  Is there a good way to determine what price at expiration would need to be to make money or break even?  Also, if some experts see what went wrong would like to know.  Is this all the result of time decay?   Honestly thought the quick run up from the $388 would have turn good for me. \n\n", "author": "AmericaIsBack110524", "created_time": "2025-02-01T14:41:04", "url": "https://reddit.com/r/options/comments/1if7p41/tesla_long_call/", "upvotes": 0, "comments_count": 42, "sentiment": "bearish", "engagement_score": 84.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ifbyxk", "title": "NVPI Revamped - Tool Release: Override DLSS4 & More Globally", "content": "The tool has been shared here for awhile now in comments & posts, but I thought I'd make a dedicated post on it.\n\nIts a fork of NVPI AIO, which was a fork of the original NVPI except with a ton of enhancements to it regarding load times, search functionality, & exposing additional hidden CVars.\n\nMy fork is a continuation of that with support for the latest NVIDIA drivers *(the AIO version of NVPI stopped working)* and also for the latest NVIDIA app DLSS overrides *(except on a global scale rather than a per game basis, making it a stronger override)*\n\nI recommend not having the NVIDIA App installed due to the fact when you launch a game that's not officially supported NVIDIA automatically changed the overrides to off, uninstalling the app removes that check so it works better. \n\n**Disclaimer:** The app will be marked as a virus by Windows, you are free to compile the code yourself. This is due to something called Wacatac which is a commonly well known false positive & is often marked as a Trojan. If you want to know why its marked as such you can use Google or ask an AI assistant.\n\n[Source / Download](https://github.com/xHybred/NvidiaProfileInspectorRevamped)\n\n[Screenshot](https://i.imgur.com/maRjZDi.png)", "author": "OptimizedGamingHQ", "created_time": "2025-02-01T17:54:06", "url": "https://reddit.com/r/nvidia/comments/1ifbyxk/nvpi_revamped_tool_release_override_dlss4_more/", "upvotes": 231, "comments_count": 182, "sentiment": "bullish", "engagement_score": 595.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ifgt14", "title": "“Just give me the f***ing links!”—Cursing disables Google’s AI overviews | The latest trick to stop those annoying AI answers is also the most cathartic.", "content": "", "author": "ControlCAD", "created_time": "2025-02-01T21:25:05", "url": "https://reddit.com/r/technews/comments/1ifgt14/just_give_me_the_fing_linkscursing_disables/", "upvotes": 1946, "comments_count": 116, "sentiment": "neutral", "engagement_score": 2178.0, "source_subreddit": "technews", "hashtags": null, "ticker": "GOOGL"}]