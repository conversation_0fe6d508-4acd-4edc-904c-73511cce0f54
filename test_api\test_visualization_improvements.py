#!/usr/bin/env python3
"""
测试可视化改进效果的脚本
"""

import subprocess
import sys
from pathlib import Path
import os

def run_analysis(ticker, date_range=None, no_filter=False):
    """运行分析并生成图表"""
    cmd = f"python llm_accuracy_analyzer.py --ticker {ticker}"
    
    if no_filter:
        cmd += " --no-date-filter"
    elif date_range:
        cmd += f" --date-range {date_range}"
    
    print(f"运行命令: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8', errors='ignore')
    
    if result.returncode == 0:
        print(f"✅ {ticker} 分析成功完成")
        return True
    else:
        print(f"❌ {ticker} 分析失败: {result.stderr}")
        return False

def check_output_files():
    """检查输出文件"""
    output_dir = Path("accuracy_analysis_output")
    if not output_dir.exists():
        print("❌ 输出目录不存在")
        return False
    
    png_files = list(output_dir.glob("*.png"))
    print(f"\n📊 生成的图表文件:")
    for png_file in png_files:
        file_size = png_file.stat().st_size / 1024  # KB
        print(f"  - {png_file.name} ({file_size:.1f} KB)")
    
    return len(png_files) > 0

def main():
    """主函数"""
    print("=== 测试可视化改进效果 ===\n")
    
    # 检查脚本是否存在
    if not Path("llm_accuracy_analyzer.py").exists():
        print("❌ 找不到 llm_accuracy_analyzer.py 文件")
        sys.exit(1)
    
    # 测试不同的数据集
    test_cases = [
        ("AAPL", "20250101-20250601", "2025年数据 (3个模型)"),
        ("NVDA", None, "无过滤 (5个模型)"),
        ("MSFT", "20240102-20241231", "2024年数据 (2个模型)")
    ]
    
    success_count = 0
    
    for ticker, date_range, description in test_cases:
        print(f"\n测试 {ticker} - {description}")
        print("-" * 50)
        
        if date_range:
            success = run_analysis(ticker, date_range=date_range)
        else:
            success = run_analysis(ticker, no_filter=True)
        
        if success:
            success_count += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"成功: {success_count}/{len(test_cases)}")
    
    # 检查输出文件
    if check_output_files():
        print("\n✅ 图表文件生成成功")
        print("\n📝 可视化改进说明:")
        print("  - 增加了图表大小 (18x12)")
        print("  - 动态调整柱子间距")
        print("  - 智能标签显示策略")
        print("  - 根据模型数量调整字体大小")
        print("  - 优化了标签位置避免重叠")
        
        print("\n🔍 请检查生成的图片文件:")
        output_dir = Path("accuracy_analysis_output")
        for png_file in output_dir.glob("*.png"):
            print(f"  - {png_file}")
    else:
        print("\n❌ 未找到图表文件")

if __name__ == "__main__":
    main()
