#!/usr/bin/env python3
"""
Reddit API认证测试
测试不同的认证方式来解决403错误
"""

import os
import praw
import prawcore
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_user_authentication():
    """测试用户认证模式"""
    
    print("=== 测试用户认证模式 ===")
    
    # 配置
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    print(f"User-Agent: {user_agent}")
    print(f"Username: {username}")
    print(f"Password: {'已设置' if password else '未设置'}")
    
    if not username or not password:
        print("❌ 用户名或密码未设置，跳过用户认证测试")
        return False
    
    try:
        # 创建用户认证的Reddit实例
        print("\n步骤1: 创建用户认证Reddit实例...")
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent,
            username=username,
            password=password
        )
        print("✓ Reddit实例创建成功")
        
        # 验证用户认证
        print("\n步骤2: 验证用户认证...")
        try:
            user = reddit.user.me()
            print(f"✓ 用户认证成功: {user.name}")
            print(f"✓ 用户karma: {user.link_karma + user.comment_karma}")
        except Exception as e:
            print(f"✗ 用户认证失败: {e}")
            return False
        
        # 测试获取帖子
        print("\n步骤3: 测试获取帖子...")
        test_subreddits = ['test', 'stocks', 'investing']
        
        for sub_name in test_subreddits:
            print(f"\n  测试 r/{sub_name}:")
            try:
                subreddit = reddit.subreddit(sub_name)
                
                # 测试获取帖子
                submissions = list(subreddit.new(limit=1))
                if submissions:
                    post = submissions[0]
                    print(f"    ✓ 成功获取帖子: {post.title[:40]}...")
                    print(f"    ✓ 作者: {post.author}")
                    print(f"    ✓ 分数: {post.score}")
                    return True  # 至少一个成功就返回True
                else:
                    print(f"    ⚠ 没有找到帖子")
                    
            except Exception as e:
                print(f"    ✗ 失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"✗ 用户认证模式失败: {e}")
        return False

def test_different_endpoints():
    """测试不同的API端点"""
    
    print("\n=== 测试不同API端点 ===")
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    try:
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent,
            username=username,
            password=password
        )
        
        subreddit = reddit.subreddit('stocks')
        
        # 测试不同的获取方式
        methods = [
            ('hot', lambda s: s.hot(limit=1)),
            ('new', lambda s: s.new(limit=1)),
            ('top', lambda s: s.top(limit=1, time_filter='day')),
            ('rising', lambda s: s.rising(limit=1))
        ]
        
        for method_name, method_func in methods:
            print(f"\n测试 {method_name} 方法:")
            try:
                submissions = list(method_func(subreddit))
                if submissions:
                    post = submissions[0]
                    print(f"  ✓ 成功: {post.title[:40]}...")
                else:
                    print(f"  ⚠ 没有帖子")
            except Exception as e:
                print(f"  ✗ 失败: {e}")
        
    except Exception as e:
        print(f"✗ 测试不同端点失败: {e}")

def create_fixed_reddit_collector():
    """创建修复后的Reddit收集器配置"""
    
    print("\n=== 创建修复配置 ===")
    
    # 基于测试结果提供修复建议
    print("基于测试结果，建议的修复方案:")
    print("1. 使用用户认证模式而不是只读模式")
    print("2. 确保Reddit应用类型设置为'script'")
    print("3. 使用简单的User-Agent格式")
    print("4. 添加适当的错误处理和重试机制")
    
    # 生成修复后的配置代码
    config_code = '''
# 修复后的Reddit配置
def create_reddit_instance():
    reddit = praw.Reddit(
        client_id=os.getenv('REDDIT_CLIENT_ID'),
        client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
        user_agent='reddit-data-collector/1.0',
        username=os.getenv('REDDIT_USERNAME'),
        password=os.getenv('REDDIT_PASSWORD')
    )
    return reddit

# 带重试的帖子获取
def get_posts_with_retry(subreddit, limit=10, max_retries=3):
    for attempt in range(max_retries):
        try:
            return list(subreddit.new(limit=limit))
        except prawcore.exceptions.ResponseException as e:
            if e.response.status_code == 403:
                print(f"403错误，尝试 {attempt + 1}/{max_retries}")
                time.sleep(2 ** attempt)  # 指数退避
            else:
                raise
    return []
'''
    
    print("修复代码示例:")
    print(config_code)

if __name__ == '__main__':
    # 测试用户认证
    auth_success = test_user_authentication()
    
    if auth_success:
        print("\n✅ 用户认证模式成功！")
        test_different_endpoints()
    else:
        print("\n❌ 用户认证模式也失败")
        print("可能的原因:")
        print("1. Reddit密码设置问题 (Google登录用户需要设置Reddit密码)")
        print("2. Reddit应用权限不足")
        print("3. Reddit API政策限制")
    
    create_fixed_reddit_collector()
