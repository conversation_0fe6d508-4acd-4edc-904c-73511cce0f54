[{"platform": "reddit", "post_id": "reddit_1hn4nt1", "title": "Want to create personal clous server for books", "content": "Hi,\n\nI have been perusing all over for a \"explain it to me as if I am an idiot\" guide to build my own cloud server. I want it to be a online \"library\" for my epubs , that I may access from anywhere. I want to make this for myself and my companion. So far best I have seen is <PERSON>'s youtube video and he even gets a little too far ahead of a basic user like myself. Can anyone assist or point me in the right direction?", "author": "cpeters1965", "created_time": "2024-12-27T02:26:34", "url": "https://reddit.com/r/cloudcomputing/comments/1hn4nt1/want_to_create_personal_clous_server_for_books/", "upvotes": 2, "comments_count": 4, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hn59an", "title": "I Need A Marketing Strategy For A Travel Agency That's Burning Money", "content": "My friend owns a travel agency and she spends 30k usd a month of Google Ads (pay per click). This is her entire marketing strategy, aside from posting on social media every few days. \n\nThe problem is, the bounce rate on her website is almost 90%. So out of roughly 4000 monthly visitors, 3600 of them leave within seconds. I think this is because the website navigation is terrible, CTAs are weak, and there are lots of mistakes with the English grammar.\n\nI'm not a developer, so I can't change the website. However, I'm an experienced English teacher and copywriter. I want to convince my friend to use 10% of her marketing budget (3000 usd) on hiring me instead, seeing as she's throwing her money away at the moment. \n\nMy strategy would be to use Google analytics to find out which pages are visited most and which holiday packages generate the most revenue. Then build an entire marketing campaign around that - clean up website copy, emails, social media posts, ads, etc. \n\nDoes this sound like a decent strategy? Do you have any other suggestions?", "author": "SBCopywriter", "created_time": "2024-12-27T02:58:48", "url": "https://reddit.com/r/DigitalMarketing/comments/1hn59an/i_need_a_marketing_strategy_for_a_travel_agency/", "upvotes": 48, "comments_count": 137, "sentiment": "bearish", "engagement_score": 322.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hn8azc", "title": "Renting an electric car sucks", "content": "To start off and ward off a few downvotes, I freely admit that I did very little research before deciding to rent a Kia Niro electric vehicle from <PERSON><PERSON>, and that was a boneheaded move on my part. I have never driven an electric car or looked deeply into the differences between various charging stations and methods. However, I don’t think that my experience would have been much different even if I had done that research. Here is the Christmas vacation experience of a complete electric vehicle newbie.\n\nI earnestly assumed that the Polestar which I reserved and for which I completed the early check in questionnaire would be ready; it was not. (The Kia was offered as a substitute.)\n\nI blithely assumed that <PERSON><PERSON> would provide a charging cable so I could charge at my mother’s house, because isn’t that how most people charge their vehicles, at home? I didn’t even think to ask after a long day of travel. No, no cable.\n\nI reasonably assumed that the car I was given would have a full charge. No, 60 miles of range (and I had 35 to travel). Guess I’ll have to go back to the counter to get a different car.\n\nI foolishly assumed that public charging stations would be reasonably distributed and available in the Phoenix metropolitan area. I quickly learned that most advertised electric charging stations on the map are private, part of gated communities, or require some ridiculous $5 per hour parking fee.\n\nI further foolishly assumed that charging would be a relatively quick affair (an hour at most). After taking 5 minutes to sign up for yet another app, I plugged in at the Tempe public library and was quickly disabused of that notion when I saw “6 hours and 40 minutes remaining for full charge”. Rather than wasting time on Christmas Eve, I drove slowly back to my mother’s house, quickly learning the meaning of range anxiety as I watched the miles remaining tick down slowly.\n\nToday, December 26, I had to drive 50 miles to visit other family with 65 miles of range remaining. I left my mom’s house early so I could have an extra 30 minutes to look for charging stations around the area I was meeting them. All the chargers that were available when the day started quickly filled up as shoppers swarmed the malls and shopping centers. I finally found a slow Shell charger 2 miles away from where I was meeting them. After signing up for Yet Another App, I plugged in the car. 10 hours remaining for full charge and 2 hours parking. And the app doesn’t show you how much electricity the car is drawing the entire time it is charging, leaving us wondering if it is actually working. Oh well - we piled into a Lyft with the kids and drove 2 miles in a gas guzzling car to get to where we needed to be. I got dropped off two hours later and the car had 45 miles more range. At this point I just had to laugh at the absurdity of the situation.\n\nI’m going to return this thing with whatever range it has remaining and accept their stupid fees for recharging it. There is no fast charger within 5 miles of where we are staying (on the footstep of Camelback Mountain), and I have wasted way too much mental energy on trying to figure out how to get this vehicle charged. I enjoy the car, it drives well and I like the concept of electric cars, but this will be the last time I rent an electric vehicle until there is a sea change in infrastructure in this country.  \n\n", "author": "bar<PERSON><PERSON>", "created_time": "2024-12-27T05:53:17", "url": "https://reddit.com/r/electricvehicles/comments/1hn8azc/renting_an_electric_car_sucks/", "upvotes": 0, "comments_count": 93, "sentiment": "neutral", "engagement_score": 186.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hn94ng", "title": "Pixel Support Used To Be Great, But Is Now Useless ", "content": "tl;dr <PERSON>xel support used to replace manufacturing defects outside of warranty, but now refuse to do so.\n\nI've been using Pixel phones for the better part of 6 years. The phones themselves are fantastic, but every model I've used has developed some kind of manufactiring defect, always outside of warranty. Previously, this was no issue: I'd contact Pixel support, tell them the issue, and they'd respond like \"yup, this is a known issue with this model, and we will gladly cover the replacement\" (e.g. my Pixel 3 XL battery became swollen and lifted the screen off; phone batteries are designed to last longer than the 3 years I'd been using the phone, and the support acknowledged that this issue was an unreasonable timeframe to face such an issue).\n\nFast forward to today, I notice my Pixel 7 camera glass has been shattered, which is odd because I haven't left the house for days due to illness, and the phone has been sitting unused on my desk. I look into how this could've happened, and wouldn't you know it, this same issue has happened to hundreds of others with their camera glass spontaniously shattering.\n\nI reach out to Pixel support, and am being told that, because the device is out of warranty, they will not cover the damage. I ask why they have covered manufacturing defects in the past outside of warranty but are no longer doing so, only to be greeted with the same copy-pasted response \"because the device warranty is no longer valid, the damage will not be covered\".\n\nLuckily the damage is isolated to the wide angle lens which I rarely use, so I can save the \\~$250 CAD they quoted to have it fixed, but I am completely appauled by the decline in customer-oriented service Pixel support has provided, especially having had excellent service in the past.\n\nIf anyone has any tips on how to have devices with manufacturing defects replaced outside of warranty, or just want to share your own stories about dealing with Pixel support, please feel free to share so I can feel a little better about my situation :)", "author": "<PERSON><PERSON><PERSON>", "created_time": "2024-12-27T06:47:38", "url": "https://reddit.com/r/GooglePixel/comments/1hn94ng/pixel_support_used_to_be_great_but_is_now_useless/", "upvotes": 2, "comments_count": 4, "sentiment": "bullish", "engagement_score": 10.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnb9hw", "title": "Any tips on getting more Google Business reviews as a local business owner?", "content": "I'm running a veterinary hospital and want more Google reviews.\n\nAny ideas that I can do apart from showing the QR code on the cashier?", "author": "ciaodaniel", "created_time": "2024-12-27T09:24:14", "url": "https://reddit.com/r/smallbusiness/comments/1hnb9hw/any_tips_on_getting_more_google_business_reviews/", "upvotes": 2, "comments_count": 33, "sentiment": "neutral", "engagement_score": 68.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnc915", "title": "Is My Retirement Plan too Simple?", "content": "The best advice my dad gave me was to start contributing to my 401k the day I started working.   I've done that and at 57 I've got about $900k in my 401k, we have no debt and about $80k in an emergency fund.\n\nHonestly I've not put much thought into a retirement plan until recently, I enjoy working and enjoy my job and expect I'll work another 10 years.  Through continued contributions I can see a path to a $2,000,000 401k and maintaining zero debt at 67.\n\nMy thought is at 67 move my 401k into funds that allow for a guaranteed 5% growth netting me $100,000 a year, this in addition to SS could be $150,000 per year which would be more than enough income for us, less than we spend now. I would leave the $2,000,000 to my next generation to give them a leg up.\n\nI understand there are some variables but as a basic plan is this too simple?", "author": "bart1218", "created_time": "2024-12-27T10:37:25", "url": "https://reddit.com/r/FinancialPlanning/comments/1hnc915/is_my_retirement_plan_too_simple/", "upvotes": 67, "comments_count": 63, "sentiment": "bullish", "engagement_score": 193.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnfebn", "title": "Quantum Computing is crazy man… here’s a more in-depth DD on the next stock to watch", "content": "I definitely think that these low-priced quantum computing stocks are going to be staring us in the face on the S&P 500 someday. The power behind the concept is ridiculous. Here’s a full DD on $SCPCF Scope Technologies Corp. - a relatively new player in the field that is looking to be ready for the quantum computing wave.\n\nScope Technologies Corp. claims to be at the forefront of developing quantum-resistant encryption solutions to address the emerging threats posed by quantum computing to data security.\n\nOne noteworthy highlight on the horizon from $SCPCF is their quantum-proof cloud storage. Scope Technologies offers a secure cloud solution that safeguards data against quantum computer and ransomware attacks by utilizing uncrackable encryption keys. This ensures clients' data remains protected even if their primary systems are compromised.\n\nScope also rides on their GEM AI software as well. The company’s AI-driven tools assist clients in optimizing advertisements, predicting customer behavior, and enhancing gaming experiences through the generation of dynamic content and intelligent characters. These diversified offerings create multiple revenue streams, positioning Scope Technologies as a leader in cybersecurity and AI innovation.\n\nWith the cybersecurity market projected to reach $1.5 to $2 trillion annually, growing at 12.4% per year, $SCPCF is well-positioned to capitalize on the increasing demand for advanced security solutions. The rise in cyberattacks has driven companies and governments to invest heavily in cybersecurity, creating a substantial market for innovative solutions like those offered by Scope Technologies.\n\nThe cybersecurity sector has seen significant acquisition activity, with larger firms acquiring smaller competitors to enhance their security capabilities. Scope Tech’s advancements in quantum-resistant encryption make it an attractive candidate for potential acquisition, especially as the threat of quantum computing grows.\n\nWith a float of 48 million shares and minimal restricted shares, increased investor interest could significantly impact the stock's performance. $SCPCF is approaching its third attempt to break a resistance trendline with increasing volume, indicating potential bullish momentum.\n\nIn recent developments, Scope Technologies announced a development update for the QSE Mobile App, designed for quantum-resistant encrypted communication and file sharing, further expanding its suite of security solutions.\n\nCould this new app give Scope the bolster they need to be a key player in the world of quantum computing/cybersecurity? I’ve been shocked to find so many of these companies in this price range, but quantum computing is also a very new concept that will revolutionize technology as we know it… it’s hard to imagine that there are already companies looking to combat the power behind the potential.\n\nCommunicated Disclaimer: NFA\n\nSources [1](https://www.scopetechnologies.io/) [2](https://finance.yahoo.com/quote/SCPCF/) [3](https://stockresearchtoday.com/quantum-security/) ", "author": "Patient-Craft-1944", "created_time": "2024-12-27T13:57:07", "url": "https://reddit.com/r/pennystocks/comments/1hnfebn/quantum_computing_is_crazy_man_heres_a_more/", "upvotes": 161, "comments_count": 124, "sentiment": "bullish", "engagement_score": 409.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnh0f9", "title": "First month of pre-registration numbers for my Google Play app", "content": "I wanted to share what the (nearly) first month of my apps Pre-registration numbers on Google Play look like. Tomorrow the pre-register page for my game Tower Farm will have been up for the first month - You may know that once you launch pre-register on a Google Play app you have 90 days to then release it, and so every person you can reach is incredibly important to a successful launch. I'm a solo dev doing this as a hobby, so I don't have any kind of marketing budget or any advertisements running anywhere. With 1 more day to go in the first month the game has been pre-registered by 584 people so far! That may not sound great for some devs, but for me it the biggest numbers I've reached yet doing this and I'm excited for launch day in 2 months or so!\n\nHere is the graph where you can see the first day spike, and also a couple other spikes where screenshots of the game drove some traffic! You can also check out Tower Farm here if interested, it is a roguelite Tower Defense game where Farming is your key to gaining resources. https://play.google.com/store/apps/details?id=com.genetix.towerfarm\n\nhttps://preview.redd.it/r7fifqu7re9e1.png?width=3812&format=png&auto=webp&s=077ad30dffe5f434cdc4607d2c778c1e71ad569b", "author": "NotFamous307", "created_time": "2024-12-27T15:16:50", "url": "https://reddit.com/r/Android/comments/1hnh0f9/first_month_of_preregistration_numbers_for_my/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnibdh", "title": "is there something called N-E-E-A-T in SEO?", "content": "I know that google follows E-E-A-T format for high-quality content but is there something like N-E-E-A-T? ", "author": "se<PERSON><PERSON><PERSON>", "created_time": "2024-12-27T16:15:36", "url": "https://reddit.com/r/SEO/comments/1hnibdh/is_there_something_called_neeat_in_seo/", "upvotes": 1, "comments_count": 16, "sentiment": "neutral", "engagement_score": 33.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnna0y", "title": "What to do with $5 million?", "content": "Hi, obvious throwaway from my primary account. \n\n  \nWell, I'm not really sure where to go from here. I've officially hit $5 million in my investment accounts after some dumb luck over the last 10 years in my industry and I'm wondering where to go from here. I have a financial advisor, but honestly not sure what type of value I'm really getting from them. They have about $3 million of mine to work with while I am holding onto $2 million spread amongst emergency account, checking account, high interest money market and high interest savings accounts.   I own my own home (worth roughly $1.3 with $900k outstanding loan just under 4%) as well as a few other rental properties (worth roughly 1 million in total and all break even or are slightly profitable). \n\n  \nI'm 33 and have been single for the majority of my adult life so my expenses have always been fairly low and I've been able to save the majority of my earnings. This has been great from a financial perspective, but has definitely had its downsides from a personal standpoint. I have a limited friend group and not much social interaction besides them and my family so changing that is definitely one of my high priorities. My one big worry about not working is that I don't know where I would continue to have those social interactions or meet new people. I'm not a guy to sit at a bar and strike up conversation, most of the friendships I have either came from childhood friends or started in the professional world. \n\nI guess I'm just wondering where I go from here. I spent so much time working to get here, that I never really figured out what I would do once I did. Ideally I would say my biggest goals are to:\n\n1. See my investments continue to grow and to be able to support a family\n\n2. Grow more socially (that's probably outside of this thread)\n\n3. Find something to occupy my time and be productive. I don't think I'll ever be able to just sit back idle, but I'm really struggling on what should I actually do. \n\n", "author": "NW-Throwaway", "created_time": "2024-12-27T19:51:49", "url": "https://reddit.com/r/Fire/comments/1hnna0y/what_to_do_with_5_million/", "upvotes": 0, "comments_count": 87, "sentiment": "bullish", "engagement_score": 174.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}]