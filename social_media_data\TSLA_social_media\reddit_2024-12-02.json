[{"platform": "reddit", "post_id": "reddit_1h4hxxx", "title": "<PERSON>: \"<PERSON>on Musk is right. The Pentagon, with a budget of $886 billion, just failed its 7th audit in a row. It’s lost track of billions. Last year, only 13 senators voted against the Military Industrial Complex and a defense budget full of waste and fraud. That must change.\"", "content": "", "author": "twinbee", "created_time": "2024-12-02T00:21:06", "url": "https://reddit.com/r/elonmusk/comments/1h4hxxx/bernie_sanders_elon_musk_is_right_the_pentagon/", "upvotes": 4864, "comments_count": 759, "sentiment": "neutral", "engagement_score": 6382.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4i15b", "title": "I Have This Crazy Idea: Think Airbnb for Toilets — What Do You Think?", "content": "I’ve been brainstorming this startup idea called **\"Find My Loo\"**, and I’d love your honest feedback.\n\nThe problem: Finding clean public toilets can be a nightmare, and sometimes you’re forced to buy something at a cafe or mall just to use theirs.\n\nThe solution: An app where users can book clean toilets at nearby partner businesses (like cafes or shops) for a small fee. Businesses can list their unused toilet stalls on my app, and my company will ensure they meet high hygiene standards. In return, businesses earn a commission for every use, creating a win-win model: users get reliable, clean toilets, and businesses earn extra income from a resource they weren’t monetizing before.\n\nI’m curious:\n\n* Do you think this idea is viable?\n* Would you use an app like this?\n* Would businesses actually partner for something like this?\n\nLooking forward to your thoughts (be as brutal as you guys can be, like is it even viable or nah?)", "author": "Alone_Calendar1117", "created_time": "2024-12-02T00:25:33", "url": "https://reddit.com/r/Entrepreneur/comments/1h4i15b/i_have_this_crazy_idea_think_airbnb_for_toilets/", "upvotes": 176, "comments_count": 309, "sentiment": "bullish", "engagement_score": 794.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4i1n6", "title": "FSD v13 does an Austin Powers style 8 point turn", "content": "", "author": "UsernameINotRegret", "created_time": "2024-12-02T00:26:11", "url": "https://reddit.com/r/SelfDrivingCars/comments/1h4i1n6/fsd_v13_does_an_austin_powers_style_8_point_turn/", "upvotes": 303, "comments_count": 155, "sentiment": "neutral", "engagement_score": 613.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4ka0k", "title": "Best 2024 Meta Ads campaign structure for scale?", "content": "Hi all, \n\nI've been running Meta Ads for my business (in the car spare parts space) for about three years now. However, I've never really been able to successfully scale my ad spend past around $100 per day. \n\n**My current structure is:**\n\n**1x Advantage Plus campaign** (3-5 creatives on rotation)  - $100 Per Day\n\nROAS sits around 2.5-3x depending on time of month/year.\n\nI have my campaign optimised to Add To Cart as when I initially tried optimising for Purchase I didn't see great results on the platform. \n\n  \nMy products are floor mats that are sold for specific makes/models (EG: Floor Mats for 2020 Tesla Model 3)\n\nI'm wondering if anybody has any advice for a good base account campaign structure that is working in 2024 for me to test and try out so I can start to scale the account?\n\n  \n", "author": "gigasoftaus", "created_time": "2024-12-02T02:16:03", "url": "https://reddit.com/r/PPC/comments/1h4ka0k/best_2024_meta_ads_campaign_structure_for_scale/", "upvotes": 2, "comments_count": 4, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4kbsr", "title": "Convince me not to put $100k into CABA on Monday.", "content": "It’s at $3.8 now but analysts are projecting a 12-month avg of $22 and a high of $30+.\n\nSeven analysts are marking this as a strong buy. \n\nIt’s seeing exceedingly high trade volume and earlier this year the stock had a big correction. Seems like prime time it swung back the other way?\n\nCons: It’s biotech…\n\nhttps://ebbow.com/2-penny-stocks-in-wall-street/\n\nEDIT: Thanks all for helping me step off the edge. You’re right. I’m not going to put in 100k. I’ll put in 50k instead. 😅 \n\n\nUPDATE: I’ve lost $13k of my 50k investment so far 😅. That’ll teach me not to buy at Monday market open….(so glad I didn’t put in $100k. Thanks you guys for slapping me with at least a little bit of reason, otherwise I’d be looking at a $26k loss right now. But gonna hold and see if Dec 4 changes anything.)", "author": "Deadelevators", "created_time": "2024-12-02T02:18:36", "url": "https://reddit.com/r/pennystocks/comments/1h4kbsr/convince_me_not_to_put_100k_into_caba_on_monday/", "upvotes": 135, "comments_count": 151, "sentiment": "bullish", "engagement_score": 437.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4kh9b", "title": "12.6.3 FSD and snow storm", "content": "Update: change version in the body to 12.5.6.3 (not the latest 13.2).\n\n——— \n\n12.5.6.3 has been flawless on highway (0 intervention) for thousand of miles.\n\nAll came to a crash after it tried to navigate in the heavy snow storm:\n\n1. <PERSON><PERSON> challenges determining the lanes in the fully snow-covered highway.\n\n2. Even in Chill mode, it still tried to jump to 65+ miles per hour during the heavy storm (FSD degraded message popping up). Even after I manually reduced the max speed, system automatically reset back to the max speed within a few minutes. \n\n3. FSD is great for experience driver -> not ready anytime for senior in the winter.\n\nNo fault for FSD though, it was a tough drive: many cars crashed on the side due to black ice, highway changes to 1 lane, extremely low visibility due heavy snow.\n\nI’m looking forward to the new 13.2 version but I’m sure it will not be able to address snow storm anytime soon. \n\nStill lots of work to be done before it’s fully functional in the North.", "author": "CycleOfLove", "created_time": "2024-12-02T02:26:23", "url": "https://reddit.com/r/SelfDrivingCars/comments/1h4kh9b/1263_fsd_and_snow_storm/", "upvotes": 1, "comments_count": 16, "sentiment": "bearish", "engagement_score": 33.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4ltro", "title": "Google Maps ruined navigation with non-stop unhelpful \"alerts\"", "content": "I'm in disbelief at this.\n\nI recently took a long road trip, about 16 hours round trip, and started to get annoyed by the extremely frequent \"police are ahead,\" and \"there's a stalled vehicle ahead\" and other similar alerts. Naturally, I opened the settings to disable them, like I would do with any other feature that isn't relevant for me. it turns out, there is no way to disable these alerts.\n\nThe alerts are not only unhelpful 99% of the time, they are actively distracting while trying to drive and also interrupt whatever music or podcasts you're trying to listen to on your road trip. Every 3 minutes I would hear \"ping ping! There's police up ahead\" followed by a dialog box asking me to confirm if what it just told me is even true or not \\*facepalm\\*. On shorter drives to work and what not, this feature didn't bother me that much but on longer drives, where navigation is more often needed, it was nothing short of maddening after a while.\n\nWhy these alerts are unhelpful to me personally:\n\n\"Police up ahead\" - I'm not trying to evade the law - and even if I was I don't need your help, thanks. I choose not to speed to the point of getting a ticket,  therefore I'm not afraid of getting one when I pass a \"speed trap.\" Also, I believe in common sense rules of the road like speed limits since speeding causes accidents and deaths, and therefore don't really support Google trying to help people endanger others lives without being caught.\n\n\"Stalled vehicle ahead\" - These vehicles are always on the shoulder of the road. I've never once found it to be something I needed warning of while driving before these alerts came about. If you're watching the road, which you generally should while driving, then this should not be an issue.\n\nSo, I guess I'm just posting this in the vague hope that someone from Google will see this and realize the product has been tanked.\n\nThe worst part? I tried switching to the other major maps provider as a result of this, and they also have the same feature that also can't be disabled! As I said, I'm in complete disbelief at this decision by both major companies to force the same annoying feature on something so critical as Navigation.\n\nWould be interested to know if others agree, or what your thoughts are.", "author": "JigsawExternal", "created_time": "2024-12-02T03:37:13", "url": "https://reddit.com/r/GoogleMaps/comments/1h4ltro/google_maps_ruined_navigation_with_nonstop/", "upvotes": 69, "comments_count": 31, "sentiment": "bullish", "engagement_score": 131.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4nlq7", "title": "If you're ever feeling down, just remember that even Google is pay-to-win.", "content": "", "author": "VerdantSeamanJL", "created_time": "2024-12-02T05:16:30", "url": "https://reddit.com/r/google/comments/1h4nlq7/if_youre_ever_feeling_down_just_remember_that/", "upvotes": 1, "comments_count": 11, "sentiment": "neutral", "engagement_score": 23.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4ofqd", "title": "Any Tips for Bypassing AI Detectors Like Copyleaks?", "content": "Hey everyone, I’ve been generating a lot of content using ChatGPT, where I add references and articles and usually work section by section while developing my blog. Despite this, whenever I run my content through Copyleaks or QuillBot AI detection, I always end up with a high AI score.\n\nI’ve tried using multiple prompts and advice from GPT to avoid certain phrases, write in simpler language, and even intentionally add wrong punctuation or grammatical errors to bypass detection. Still, nothing seems to lower the AI score significantly. Ideally, I want my content either not to be detected or, if it is, for the score to be as low as possible. Since I’m looking to automate this process and don’t have time for manual tweaks, I wonder if anyone has found a solution that works. Paid tools are fine, too. Any tips would be greatly appreciated. Honestly, I just want to get rid of this problem :p", "author": "AromaticRange8948", "created_time": "2024-12-02T06:08:06", "url": "https://reddit.com/r/SEO/comments/1h4ofqd/any_tips_for_bypassing_ai_detectors_like_copyleaks/", "upvotes": 2, "comments_count": 78, "sentiment": "bearish", "engagement_score": 158.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4p4ps", "title": "5 Health care stocks for the next 5 years", "content": "# 1. $NVO - Novo Nordisk\n\nDenmark-based pharmaceutical company that produces weight loss blood sugar/diabetes drugs as the main revenue streams. Growing Intrinsic about 20% / year\n\n# 2. UTHR - United Therapeutics\n\nBiotech for chronic illness. 20-25% Intrinsic growth\n\n# 3. $SOBI:OMX - Swedish Orphan Biovitrum\n\nSwedish rare disease biotech company, 20% Intrinsic growth  ( U.S. ticker ADR is $SWDBY foreign ordinary U.S. is $SDWBF )\n\n# 4. NBIX - Neocrine Biosciences\n\nNeurologicaal disease/disorder pharma. 25% Intrinsic growth\n\n# 5. PODD - Insulet\n\nBiotech devices, Insulin pump. Growing Intrinsic 25% / year", "author": "Sugamaballz69", "created_time": "2024-12-02T06:53:41", "url": "https://reddit.com/r/stocks/comments/1h4p4ps/5_health_care_stocks_for_the_next_5_years/", "upvotes": 3, "comments_count": 51, "sentiment": "bullish", "engagement_score": 105.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4parr", "title": "<PERSON> and <PERSON><PERSON> on the same page for anti-corruption. This is good for the U.S. economy.", "content": "", "author": "wakeup2019", "created_time": "2024-12-02T07:04:46", "url": "https://reddit.com/r/economy/comments/1h4parr/bernie_sanders_and_elon_musk_on_the_same_page_for/", "upvotes": 1161, "comments_count": 136, "sentiment": "neutral", "engagement_score": 1433.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4rbf2", "title": "Google losing ground to TikTok and Instagram for searches", "content": "I came across an interesting observation recently:\n\nPeople over 30 still start their searches on Google and confirm their findings on YouTube.\n\nMeanwhile, those under 30 are increasingly starting searches on TikTok and validating them on Instagram.\n\nThis trend feels like a seismic shift in how we access information, especially for marketers, educators, and anyone who creates online content. It makes me wonder:\n\nAre younger generations moving away from traditional search engines because social platforms feel more engaging or personalized?\n\nWhat does this mean for businesses that have traditionally relied on SEO to drive traffic?\n\nHow do we, as creators or marketers, adapt to this new landscape?\n\nLet’s open this up:\n\nIf you’re over 30, do you still default to Google for everything?\n\nIf you’re under 30, do TikTok and Instagram feel more “searchable” or useful than Google?\n\nAnd for everyone: How does this impact how we present or consume information online?\n\nWould love to hear your thoughts. Is this the beginning of Google’s decline or just the evolution of search behavior?", "author": "Elegant-Fix8085", "created_time": "2024-12-02T09:36:13", "url": "https://reddit.com/r/marketing/comments/1h4rbf2/google_losing_ground_to_tiktok_and_instagram_for/", "upvotes": 91, "comments_count": 63, "sentiment": "bearish", "engagement_score": 217.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4s5l7", "title": "Which tool do you use to prevent fake click on your campaign? Clickcease or Clickguard and why?", "content": "Our campaign is receiving a lot of fake clicks and we want to implement a tool to maximize our spending. Pls provide your valuable insights.", "author": "Blue-Gamora-2305", "created_time": "2024-12-02T10:39:06", "url": "https://reddit.com/r/adwords/comments/1h4s5l7/which_tool_do_you_use_to_prevent_fake_click_on/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4smhz", "title": "How I made a high tech salary in my first selling month", "content": "For over 7 years I worked as a full-stack developer, helping other companies bring their ideas to life. But one day, I thought *“Why not try making my own dream come true?”*. That’s when I decided to quit my job and start my own journey to becoming an entrepreneur.\n\nAt first, it wasn’t easy. I didn’t make any money for months and had no idea where to start. I felt lost. Then, I decided to focus on something popular and trending. AI was everywhere, and ChatGPT was the most used AI platform. So I looked into it and I found the OpenAI community forum where people had been asking for features that weren’t being added.\n\nThat gave me an idea. Why not build those features myself? I created a Chrome extension and I worked on some of the most requested features, like:\n\n* Downloading the advanced voice mode and messages as MP3\n* Adding folders to organize chats\n* Saving and reusing prompts\n* Pinning important chats\n* Exporting chats to TXT/JSON files\n* Deleting or archiving multiple chats at once\n* Making chat history searches faster and better\n\nIt took me about a week to build the first version, and when I published it, the response was incredible. People loved it! Some even said things like, *“You’re a lifesaver!”* That’s when I realized I had something that could not only help people but also turn into a real business.\n\nI kept the first version free to see how people would respond. Many users have been downloading my extension, which prompted Chrome to review it to determine if it qualified for the featured badge. I received the badge, and it has significantly boosted traffic to my extension ever since.\n\nAfter all the positive feedback, I launched a paid version one month ago. A few minutes after publishing it, I made my first sale! That moment was so exciting, and it motivated me to keep going.\n\nI already have over 4,000 users and have made more than $4,500 in my first selling month. I’ve decided to release 1-2 new features every month to keep improving the extension based on what users ask for.\n\nI also created the same extension for Firefox and Edge users because many people have been asking for it!\n\nI also started a Reddit community, where I share updates, sales, discount codes, and ideas for new features. It’s been awesome to connect with users directly and get their feedback.\n\nAdditionally, I’ve started working on another extension for Claude, which I’m hoping will be as successful as this one.\n\n**My message to you is this: never give up on your dreams. It might feel impossible at first, but with patience, hard work, and some creativity, you can make it happen.**\n\nI hope this inspires you to go after what you want. Good luck to all of us!\n\n", "author": "Ok_Negotiation_2587", "created_time": "2024-12-02T11:11:27", "url": "https://reddit.com/r/startups/comments/1h4smhz/how_i_made_a_high_tech_salary_in_my_first_selling/", "upvotes": 297, "comments_count": 70, "sentiment": "bearish", "engagement_score": 437.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4tb5o", "title": "FSD 13 near crash video", "content": "https://youtu.be/7FTyQ-XM7PI?feature=shared&t=660\n\nJust wanted to post this situation. FSD 13.2 is barely out and out of the 3 main FSD testing channels I found a near crash situation from DirtyTeslas video already. At slow speeds in a tight spot it nearly crashed another car, hadn't the driver intercepted in the last second - at least that's how it seemed to me. The driver downplayed it a bit but this is pretty bad, isn't it? \n\nIt also refused to do a three point turn and just went stuck just seconds before at a dead end. \n\nThis may give a more balanced picture about the state of FSD. If you were to believe the hype around FSD12 already and now the hype how much better FSD13 is, you may think this was Level 3 ready yet with backwards driving ability etc now added. Yet it still seems to screw up on the most basic scenarios at low speed.\n\n\n", "author": "Apophis22", "created_time": "2024-12-02T11:56:31", "url": "https://reddit.com/r/SelfDrivingCars/comments/1h4tb5o/fsd_13_near_crash_video/", "upvotes": 2, "comments_count": 47, "sentiment": "bearish", "engagement_score": 96.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4vtsj", "title": "Tried to gamble my way out of debt - Didn’t work :(", "content": "Well here it goes. \n\nAbout a year ago I dumped about 5k into a company that I may or may not have had private knowledge of. I thought I was so smart, turns out that less than a month later all my shares are worthless because the company filed for bankruptcy. \n\nFast forward a couple months and my position was eliminated and I have been searching for jobs but been unemployed ever since. That 5k was a big chunk of my savings and with being unemployed and having bills to pay I quickly ran out of cash. I racked up about 12,000 in credit card debt and needed money fast. \n\nSo naturally, I started gambling. I gambled by using this same credit card on some sketchy online casino, I didn’t deposit much at once, usually only $100 or so, but man I realized I had a gambling addiction once I spent over 4k on this site. I was making the money back but then I was chasing my losses and lost it all. \n\nThen autopay resumed on my card and tried to charge my entire balance which I didn’t have and Amex canceled my card. I was still making my monthly payments before that. \n\nSo now I’m 16k in debt on this card, 3k on another, and have about $100 to my name. I sold my computers and guns and anything worth money but now I don’t know how I am going to pay my bills this month. \n\nGuys, please be careful, be honest with yourselves, casino gambling and options gambling are both just as dangerous. You have to limit yourself or else you are a few bad days away from being like me. \n\nI don’t know what I’m gonna do anymore but I thought I’d leave a warning for other degenerate gamblers like me. \n\n", "author": "whitelightning096", "created_time": "2024-12-02T14:11:49", "url": "https://reddit.com/r/wallstreetbets/comments/1h4vtsj/tried_to_gamble_my_way_out_of_debt_didnt_work/", "upvotes": 26242, "comments_count": 2471, "sentiment": "bearish", "engagement_score": 31184.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4wcj1", "title": "I think Optimus touring worldwide now", "content": "Last month Germany, This month Japan.\nWhere is next?", "author": "TakkyongHan", "created_time": "2024-12-02T14:36:37", "url": "https://reddit.com/r/teslamotors/comments/1h4wcj1/i_think_optimus_touring_worldwide_now/", "upvotes": 15, "comments_count": 14, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4wpez", "title": "Is it okay if I hate the pixel 8", "content": "Like I've got a pixel for the first time and was excited about all the good stuff I heard about it but just absolutely hated it after 6 hours which is why I am returning it (I made sure it looked as untouched as possible in the packaging).\n\nLike somehow I feel like it is bad that I completely hate everything on the Pixel 8 (except for the bubbly clock on the lockscreen, that one is soo CUTE). I hate the sound it makes, I hate the ui the font the way a protective glass doesn't really stick on it. I find the camera powerful but I actually hate the images it does (and since I own real cameras, the smartphone one doesn't have to be natural or accurate, I want it to feel flashy).\n\nLike my lifelong I feel while I saw many people use Samsung, I knew allot of friends who hated the device and called them bad and bloated but to my surprise I seem to love them and find them pretty much perfect in most regards, I also used to enjoy Sony a big while back and people seem to be negatively against those too (except my brother who still rocks this brand).\n\nIt seems to me that the brands people hate or recommend really matter little when it comes to your personal choice. But I feel kinda guilty since I know many people LOVE the pixel to say that I hate it.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-02T14:53:20", "url": "https://reddit.com/r/GooglePixel/comments/1h4wpez/is_it_okay_if_i_hate_the_pixel_8/", "upvotes": 0, "comments_count": 20, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4wu89", "title": "Google Ads cannot verify \"University\" or \"College\" as organisation/business account", "content": "This is both hilarious and frustrating. (sorry, mostly just a rant)\n\nWe are running our own ads for our international BSc environmental science programme on Google Ads. We had to verify ourselves as business due to new Google Ad policy this year.\n\nWhen we got to the question \"What's your industry (or industries)?\", there is a huge list of options, but it does not include \"university\", \"education\", \"school\" or \"college\"! The Google Ad team somehow forget that these seem to exist and can run ads as well...\n\nEventually I contacted a Google Ad helpdesk employee and they could not solve the issue. I simply selected \"government agency\" as that seemed to be the best match.\n\nBut seriously, how can they forget about this?", "author": "ESSETavans", "created_time": "2024-12-02T14:59:23", "url": "https://reddit.com/r/adwords/comments/1h4wu89/google_ads_cannot_verify_university_or_college_as/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4x9sv", "title": "Seeking your thoughts on my Google investment", "content": "Hey fellow investors,\n\nI’d like to hear your opinions on my current investment. Two weeks ago, I bought approximately 600 shares of GOOG at an average price of around $168, which now makes up 80% of my portfolio.\n\nI’m fully aware that this is not an ideal diversification strategy, but I struggle to see where this investment could go wrong. In my view, the downside risk might be around 10%. If the stock were to drop by 10%, we’d be looking at a P/E ratio of 20 and a forward P/E of 17 - something that has rarely happened with Google.\n\nSeveral factors keep me optimistic:\n\n* **Ad revenue** is likely to be strong in January due to the holidays and election-related spending.\n* **Google Cloud** is experiencing rapid growth.\n* **Waymo** is expanding quickly, and user feedback has been outstanding.\n* The company’s vast data resources and well-integrated ecosystem provide a solid foundation for continued success.\n\nOn top of that, Google is set to pay a dividend next week, which reinforces my confidence in the stock. And massive buybacks. \n\nWhat are your thoughts on this?\n\nLooking forward to hearing from you!", "author": "Former_Drawer6732", "created_time": "2024-12-02T15:17:49", "url": "https://reddit.com/r/ValueInvesting/comments/1h4x9sv/seeking_your_thoughts_on_my_google_investment/", "upvotes": 61, "comments_count": 89, "sentiment": "bullish", "engagement_score": 239.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4xwmo", "title": "Every time you use ChatGPT, half a litre of water goes to waste", "content": "", "author": "MetaKnowing", "created_time": "2024-12-02T15:45:22", "url": "https://reddit.com/r/technology/comments/1h4xwmo/every_time_you_use_chatgpt_half_a_litre_of_water/", "upvotes": 2045, "comments_count": 764, "sentiment": "neutral", "engagement_score": 3573.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4xx6c", "title": "Every time you use ChatGPT, half a litre of water goes to waste", "content": "", "author": "MetaKnowing", "created_time": "2024-12-02T15:46:03", "url": "https://reddit.com/r/environment/comments/1h4xx6c/every_time_you_use_chatgpt_half_a_litre_of_water/", "upvotes": 321, "comments_count": 57, "sentiment": "neutral", "engagement_score": 435.0, "source_subreddit": "environment", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h4zxe6", "title": "Math & Statistics in Data Analytics", "content": "I've been doing a bit of researching when it comes to moving into a data analytics The usual 3 things you are told to learn is: Excel, SQL and a data visualization tool (which I'm going to work on). But one thing I've been seeing mixed responses is needing to know math and/or statistics.\n\nSo I'm here to ask how much math/statistics should someone dive into if you are looking to aim for a entry level to mid analytics role? I've seen others say it varies from job to job. But I'm thinking it might not hurt to learn some of it. I was looking at taking an intro to statistics course (took a stats course back in grad school but that was many years and never used it) and maybe a basics/fundamentals algebra course.  I'm not looking to get into data science or engineering right now.\n\nWould love to know others thoughts/ideas. Also if you have suggestions on courses/books? Something relatable as I'm not good at math at all and it can take me awhile (along with repetition) to understand things. ", "author": "define_yourself72", "created_time": "2024-12-02T17:09:17", "url": "https://reddit.com/r/analytics/comments/1h4zxe6/math_statistics_in_data_analytics/", "upvotes": 65, "comments_count": 22, "sentiment": "bullish", "engagement_score": 109.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h513bc", "title": "How can I deploy my web app (django+react)? ", "content": "For the past few months, I’ve been working on a web app—a Reddit clone—using Django for the backend and React for the frontend. The app focuses on stock market tickers, allowing users to post and discuss specific securities, similar to how Reddit functions.\n\nThis is my first time building something like this, and I don’t have a background in computer science. Now, I’m ready to take the next step and deploy my app, but I have no idea where to start.\n\nI’ve heard about AWS, Azure, and other hosting platforms, but I’m not sure which one would be best for a beginner like me. I’d really appreciate any guidance, resources, or tutorials (e.g., YouTube videos, step-by-step guides) that can help me with deployment.\n\nThanks in advance for your help!\n\nhttps://preview.redd.it/79mm15wu4h4e1.png?width=3014&format=png&auto=webp&s=e676281a8c0e8b23515a573c5bb25c1b9bed50cc\n\nhttps://preview.redd.it/mjqay3wu4h4e1.png?width=3018&format=png&auto=webp&s=df0a450bd57ecfe705b28d6ec4f78ccd3aaf7a7a\n\nhttps://preview.redd.it/wx849twu4h4e1.png?width=3014&format=png&auto=webp&s=d308b2f51c97e8d1755c6bd8ac4252b983274d39\n\nhttps://preview.redd.it/630pa4wu4h4e1.png?width=3022&format=png&auto=webp&s=6725a184cba40323e957a0c06ff5f45c55fd9273\n\nhttps://preview.redd.it/ybuvi4wu4h4e1.png?width=3016&format=png&auto=webp&s=3533b390dc533d67bd19599896c39bb47e5f184d\n\n", "author": "pussydestroyerSPY", "created_time": "2024-12-02T17:56:09", "url": "https://reddit.com/r/webdev/comments/1h513bc/how_can_i_deploy_my_web_app_djangoreact/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h51q3l", "title": "GOOGLE OVERVALUED", "content": "This is my first ever stock analysis, so I want as much feedback and constructive criticism as possible. Please also tell me if I'm wrong about anything. Numbers are taken between 2016 and 2023 from TIKR stock screener.\n\nGoogle had an average growth of revenue of 19% yearly from 2016-2023, as well as a pretty stable profit margin of 60% every year. The revenue growth was good but the profit margin is outstanding as expected of a company that doesn't have to produce anything physical as a main form of income.\n\nGoogle also had a mildly growing operating margin starting at 26% in 2016 and being 32% in 2023, which again is an amazing margin. They have also had a strong growth in earnings from continuing operations, which ends up being the same as their net income.\n\nTheir CapEx is also growing, though not excessively, but I do not know what kind of ventures this includes.\n\nThey are also paying of more and more debt which of course is good, and they could as of 2023 pay of their long term debt over 10 times just using their Cash & Short-term Investments (They also have a Current Ratio of almost 2 which again is great). This, combined with the fact that their retained earnings were growing at an average of 10% over these years shows that their financial health is excellent. Their Cash & Short-term Investments is also growing yearly, and they have started paying dividends this year as a result, which also is good.\n\nGoogle has also bought back more and more stock each year, which shows that they believe in themselves, and their Total Assets are growing both strongly and evenly. The CEO also has a lot of stock himself, meaning that it's in his best interest that the company does well and gives some insurance of good leadership.\n\nIt also shouldn't have to be mentioned that they have a great moat as they essentially have a monopoly on browsing.\n\nThey also have 25 dollars earned per share, giving them a 25-1 return on cash ratio which is excellent, and their debt to equity ratio of around 0.3 is also great.\n\nOne thing that can be seen as a problem is that Googles Cash from Operations is growing at a slower rate than their Net Income, which means that they aren't actually collecting all the payments from the sales they record. They also have a low Cash Return of around 3.5%.\n\nOver to the intrinsic value, my calculations (which might very well be wrong as this is my first analysis) tell me that Googles terminal value in 2030 will be 829,168MM (679,886MM discounted at 15%) assuming their Free Cash Flow continues at an average of 15% per year. This means that the intrinsic value I've calculated comes out at 1,359,772MM. I made an intrinsic value range from 90% to 110% of this number and added Cash & Short Term Marketable Securities to the values, giving the intrinsic value of Google a range between 1,334,794MM and 1,606,794MM. Adding a margin of safety of 20% (as I assume nothing will go too wrong for Google) the range becomes 1,067,835MM to 1,285,435MM. The total Enterprise Value of Google divided by the Intrinsic Value on either side of the range gives us a ratio of 1.92-2.32, meaning that people are currently paying around twice as much as they should for google stock. This can kinda be confirmed by its current P/E ratio of 22.4 compared to its earnings growth of 19% on average, meaning that their earnings aren't keeping up with their stock price.\n\nAs stated, this is my first analysis so I want as much feedback as you guys can give me so I improve further :)\n\n  \nEdit: This is also an industry I don't know much about, I just needed a company to start practicing my analyzing skills, so there also might be outside factors and company plans I'm unaware of.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-02T18:21:14", "url": "https://reddit.com/r/ValueInvesting/comments/1h51q3l/google_overvalued/", "upvotes": 0, "comments_count": 34, "sentiment": "bullish", "engagement_score": 68.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h52wf9", "title": "Does it make sense to contribute to my Roth IRA if I didn't earn any money this year?", "content": "Please forgive my ignorance... my understanding is that the benefit of a Roth IRA is tax free gains. Well, sadly I haven't earned much of anything the last 4 years due to illness. But, I do have a decent amount of savings (currently in treasury bills). Plus, my mom owes me $10k. I'm wondering if it even makes sense to look into whether I could put some of my own savings into my Roth and/or some or all of the $10k my mom owes into my Roth. The alternative would be to keep buying short term Tbills. Thanks.", "author": "Rola66", "created_time": "2024-12-02T19:08:53", "url": "https://reddit.com/r/FinancialPlanning/comments/1h52wf9/does_it_make_sense_to_contribute_to_my_roth_ira/", "upvotes": 0, "comments_count": 21, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h55nks", "title": "November at Tesla - Monthly Recap", "content": "", "author": "Nakatomi2010", "created_time": "2024-12-02T21:00:53", "url": "https://reddit.com/r/teslamotors/comments/1h55nks/november_at_tesla_monthly_recap/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h56aak", "title": "Tesla 152.5 ", "content": "Someone wave their wand. Made a few hundred on calls on the way up this morning, saw something and bought a 152.5 put for expiration 12/6. \n\nRight now down $200 (still up $150 for the day, but if I’d not taken this I’d been happy with the day. \n", "author": "Honorbet", "created_time": "2024-12-02T21:26:38", "url": "https://reddit.com/r/options/comments/1h56aak/tesla_1525/", "upvotes": 0, "comments_count": 15, "sentiment": "bullish", "engagement_score": 30.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h571ec", "title": "Musk Record Tesla Pay Package Rejected Again by Delaware Judge", "content": "", "author": "MeowTheMixer", "created_time": "2024-12-02T21:57:20", "url": "https://reddit.com/r/wallstreetbets/comments/1h571ec/musk_record_tesla_pay_package_rejected_again_by/", "upvotes": 5014, "comments_count": 756, "sentiment": "neutral", "engagement_score": 6526.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h57cx2", "title": "Tesla CEO <PERSON><PERSON> loses bid to get $56 billion pay package reinstated", "content": "A Delaware judge ruled on Monday that <PERSON><PERSON>, CEO <PERSON><PERSON> still is not entitled to receive a $56 billion compensation package despite shareholders of the electric vehicle company voting to reinstate it.The ruling by the judge, Chancellor <PERSON><PERSON><PERSON> of the Court of Chancery, follows her January decision that called the pay package excessive and rescinded it, surprising investors, and cast uncertainty over Musk's future at the world's most valuable carmaker.\n\nTesla has said in court filings that the judge should recognize a subsequent June vote by its shareholders in favor of the pay package for Musk, the company's driving force who is responsible for many of its advances, and reinstate his compensation.<PERSON> also ordered <PERSON>sla to pay the attorneys who brought the case $345 million, well short of the billions they initially requested.\n\nSources:\n\n[https://www.reuters.com/legal/delaware-judge-rejects-request-restore-elon-musks-56-billion-tesla-compensation-2024-12-02/](https://www.reuters.com/legal/delaware-judge-rejects-request-restore-elon-musks-56-billion-tesla-compensation-2024-12-02/)\n\n[https://www.cnbc.com/2024/12/02/tesla-ceo-elon-musk-loses-bid-to-get-56-billion-pay-package-reinstated.html](https://www.cnbc.com/2024/12/02/tesla-ceo-elon-musk-loses-bid-to-get-56-billion-pay-package-reinstated.html)", "author": "<PERSON><PERSON><PERSON>", "created_time": "2024-12-02T22:10:38", "url": "https://reddit.com/r/stocks/comments/1h57cx2/tesla_ceo_elon_musk_loses_bid_to_get_56_billion/", "upvotes": 5568, "comments_count": 756, "sentiment": "bearish", "engagement_score": 7080.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h57ihb", "title": "Musk Record Tesla Pay Plan Rejected Again by Delaware Judge (1)", "content": "Here we go again", "author": "Salategnohc16", "created_time": "2024-12-02T22:17:08", "url": "https://reddit.com/r/teslainvestorsclub/comments/1h57ihb/musk_record_tesla_pay_plan_rejected_again_by/", "upvotes": 392, "comments_count": 236, "sentiment": "neutral", "engagement_score": 864.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h57kd5", "title": "Tesla CEO <PERSON><PERSON> loses bid to get $56 billion pay package reinstated", "content": "", "author": "OneArmedBrain", "created_time": "2024-12-02T22:19:22", "url": "https://reddit.com/r/news/comments/1h57kd5/tesla_ceo_elon_musk_loses_bid_to_get_56_billion/", "upvotes": 53582, "comments_count": 2901, "sentiment": "neutral", "engagement_score": 59384.0, "source_subreddit": "news", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h57lw9", "title": "Tesla loses bid to restore Elon Musk's $56bn pay package", "content": "", "author": "djoxo", "created_time": "2024-12-02T22:21:07", "url": "https://reddit.com/r/teslamotors/comments/1h57lw9/tesla_loses_bid_to_restore_elon_musks_56bn_pay/", "upvotes": 1008, "comments_count": 566, "sentiment": "neutral", "engagement_score": 2140.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h57mck", "title": "Tesla loses bid to restore Elon Musk's $56bn pay package", "content": "", "author": "djoxo", "created_time": "2024-12-02T22:21:38", "url": "https://reddit.com/r/StockMarket/comments/1h57mck/tesla_loses_bid_to_restore_elon_musks_56bn_pay/", "upvotes": 915, "comments_count": 561, "sentiment": "neutral", "engagement_score": 2037.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h57qeq", "title": "Tesla CEO <PERSON><PERSON> loses bid to get $56 billion pay package reinstated", "content": "", "author": "Kidd_Funkadelic", "created_time": "2024-12-02T22:26:25", "url": "https://reddit.com/r/teslamotors/comments/1h57qeq/tesla_ceo_elon_musk_loses_bid_to_get_56_billion/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h59b2y", "title": "Adgroups: Automated Bidding, Will this Work?", "content": "I have a TROAS Bidding Strategy, currently with a single campaign with thousands of products, across different brands. I am thinking of the following, but will this do anything with an automated strategy? \n\n**Campaign - Google Shopping, TROAS Bidding Strategy**\n\n**AdGroups**\n\nAll Products\n\nBrand Name 1\n\nBrand Name 2\n\nBrand Name 3\n\n  \nMy strategy is to run a script on the brand Ad Groups, let's say \"Tesla\".  Search terms that do not contain the name of the Ad Group (non branded) would become negatives of that Ad Group. \n\nThe idea is that over time, the Ad Groups should perform better as the non branded terms are taken out. However they can still get action in the Ad Group with All Products. \n\nMy question, will this even matter or would the automated bidding strategy take this into consideration? if the Adgroups start performing better wouldn't it funnel that brand traffic? \n\nFull disclosure I know this can be accomplished with Campaign Priorities and have done it in the past, but it seems with automated strategies it just doesn't work like it used to.", "author": "Antique-Cartoonist-5", "created_time": "2024-12-02T23:34:48", "url": "https://reddit.com/r/PPC/comments/1h59b2y/adgroups_automated_bidding_will_this_work/", "upvotes": 1, "comments_count": 2, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}]