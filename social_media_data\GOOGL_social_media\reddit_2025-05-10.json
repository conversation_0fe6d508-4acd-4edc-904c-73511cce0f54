[{"platform": "reddit", "post_id": "reddit_1kix8yd", "title": "Google agrees to pay Texas $1.4 billion data privacy settlement", "content": "", "author": "Force_Hammer", "created_time": "2025-05-10T00:10:33", "url": "https://reddit.com/r/StockMarket/comments/1kix8yd/google_agrees_to_pay_texas_14_billion_data/", "upvotes": 130, "comments_count": 17, "sentiment": "neutral", "engagement_score": 164.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kj2xu5", "title": "Was anything in particular hard for y’all learning digital marketing such as SEO, anything social media related or email? I’m considering learning Google analytics and Google, digital marketing and e-commerce certificate.", "content": "^", "author": "Bomberman2112", "created_time": "2025-05-10T05:33:04", "url": "https://reddit.com/r/DigitalMarketing/comments/1kj2xu5/was_anything_in_particular_hard_for_yall_learning/", "upvotes": 2, "comments_count": 10, "sentiment": "neutral", "engagement_score": 22.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kj8uv7", "title": "Mexico sues Google for renaming ‘Gulf of America’ to appease Trump", "content": "", "author": "Saltedline", "created_time": "2025-05-10T12:13:51", "url": "https://reddit.com/r/technology/comments/1kj8uv7/mexico_sues_google_for_renaming_gulf_of_america/", "upvotes": 4935, "comments_count": 255, "sentiment": "neutral", "engagement_score": 5445.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kja0xl", "title": "Grad project about investor sentiment, please help by completing the form", "content": "https://docs.google.com/forms/d/1jT8CcN5yv1uflJvig6hnrOmL9Ns6AsFyDyrJE_fw8ac/edit#responses", "author": "<PERSON><PERSON><PERSON><PERSON>_", "created_time": "2025-05-10T13:14:18", "url": "https://reddit.com/r/investing_discussion/comments/1kja0xl/grad_project_about_investor_sentiment_please_help/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kje7uh", "title": "The Fall of Google’s Ad Tech Empire: DOJ Wins Landmark Antitrust Case – Who Benefits?", "content": "Hey everyone - sharing an article that the team at Olympus Investing wrote regarding the DOJ's antitrust victory against Google. \n\nhere’s the summary of the artcile:\n\n* **Google rigged the game.** They snapped up rivals like DoubleClick and AdMeld, ran secret auction schemes (code names “Bernanke” and “Poirot”), and locked publishers into their system.\n* **Fees were insane.** While everyone else hovered around 10–15%, Google was taking 20–40% of every ad dollar.\n* **Complete chokehold.** Over 85% of open-web ad auctions went through Google’s black box, squeezing out competition.\n\nNow that the court’s called foul, billions could flow back to independent players. **Magnite (MGNI)** and **PubMatic** are set to steal the show on the sell-side, and DSPs like **The Trade Desk** stand to gain massively on the buy-side.\n\nWhat really determines the winners? Whether regulators opt for a full breakup or just slap on behavioral fixes—and how fast these smaller platforms can scale.\n\nIf you thought this was interesting, check out the full breakdown on our [Blog](https://olympus-trade.com/blog) \\- 100% free and feedback welcomed. We do these writeups and allow you to track Superinvestors 100% free, check us out there if interested :).", "author": "Rich_Okra5720", "created_time": "2025-05-10T16:27:49", "url": "https://reddit.com/r/ValueInvesting/comments/1kje7uh/the_fall_of_googles_ad_tech_empire_doj_wins/", "upvotes": 3, "comments_count": 14, "sentiment": "bearish", "engagement_score": 31.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kjg7bf", "title": "What to do with inherited IRA", "content": "Hi! I feel like this comes up a lot but I still could use some advice. My dad recently passed and us kids now all have about $48k in an inherited IRA thru Schwab. I have tried to educate myself as best I can about the rules for this type of account, and I will be taxed 22% after withdrawal when April rolls around. \n\nThe money is primarily invested in stock such as Apple, Google and Nvidia. I do not know anything about stocks or investments. I also am 47 with no financial planning of my own, as I only recently got my shit together as an adult. I have about $10k in checking and no 401k or IRA. My job is in hospitality and company unfortunately does not have a 401k program. I do not own property and make about $75k a year living in Washington State.\n\nShould I leave this $$ in the IRA as my sort of safety net until the ten years is up? Would it be more advantageous to take it out and put it in a HYSA? My dad's financial advisor recommended taking it out in increments (as you would for RMD's) over time, so do I open a ROTH or do I start a HYSA?\n\nHonestly the stock market part stresses me out for obvious reasons, but I don't immediately need the money for anything. It's also a weird amount of money--irs nice to have, but not life changing.\n\nWho else has been in this scenario? How did the various options play out?\n\n Thank you in advance!  ", "author": "mindycity", "created_time": "2025-05-10T17:57:15", "url": "https://reddit.com/r/FinancialPlanning/comments/1kjg7bf/what_to_do_with_inherited_ira/", "upvotes": 9, "comments_count": 9, "sentiment": "neutral", "engagement_score": 27.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kjhtgm", "title": "How can I avoid frequent re-authentication when using Google Cloud Platform (GCP) (e.g., auto-renew, increase token expiry, another auth method)?", "content": "I use Google Cloud Platform (GCP) to access the Vertex AI API. I run:\n\n```\ngcloud auth application-default login --no-launch-browser\n```\n\nto get an authorization code:\n\nhttps://ia903401.us.archive.org/19/items/images-for-questions/65RR4vYB.png\n\nHowever, it expires after 1 or 2 hours, so I need to re-authenticate constantly. How can I avoid that? E.g., increase the expiry time, authenticate automatically, or authenticate differently in such a way I don't need an authorization code.", "author": "<PERSON><PERSON><PERSON>_<PERSON>", "created_time": "2025-05-10T19:09:10", "url": "https://reddit.com/r/cloudcomputing/comments/1kjhtgm/how_can_i_avoid_frequent_reauthentication_when/", "upvotes": 4, "comments_count": 0, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kjingq", "title": "Help please malware is messing up my google!", "content": "Something's wrong please help", "author": "Funny_Board_6560", "created_time": "2025-05-10T19:48:00", "url": "https://reddit.com/r/chrome/comments/1kjingq/help_please_malware_is_messing_up_my_google/", "upvotes": 2, "comments_count": 17, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kjjrb0", "title": "Google Pays $1.4 Billion to Texas Over Unauthorized Tracking and Biometric Data Collection", "content": "", "author": "Consistent-Age5347", "created_time": "2025-05-10T20:38:18", "url": "https://reddit.com/r/privacy/comments/1kjjrb0/google_pays_14_billion_to_texas_over_unauthorized/", "upvotes": 873, "comments_count": 24, "sentiment": "neutral", "engagement_score": 921.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kjkohk", "title": "A+ Certificate or Google Data Analytics Course?", "content": "I am a recent graduate with some data analytics classes taken but biomedical related, in my coursework i used SQL, statistics with R program, python, etc. I dont have any internships but just a capstone project related to clinical data analytics. I have been applying to positions for 5 months almost and have yet to hear back from a Data Analyst position or an entry-level IT helpdesk/support position. The only call backs ive gotten are for back office jobs at a school / research positions, which I was denied after interview. I am desperate now as it’s about to be 6 months, I am wondering would it be better to do the A+ certificate or the google data analytics course. I can’t decide which field to pursue and put most of my time towards and it’s very stressful. Everyday I try to apply to data analyst jobs and entry level IT, but honestly it’s hard to do both. Any advice is appreciated, thank you", "author": "Ill_Pause_9264", "created_time": "2025-05-10T21:20:56", "url": "https://reddit.com/r/analytics/comments/1kjkohk/a_certificate_or_google_data_analytics_course/", "upvotes": 7, "comments_count": 4, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kjnc22", "title": "Is ChatGPT the End of Google Search?", "content": "Hey everyone, I know GOOG stock is pretty beaten to death on Reddit, but I wanted to share my take on it and provide more of a comprehensive numbers backed outlook on it than I have seen posted previously.\n\nGoogle’s P/E is 16.2. TTM Free cash flow is $75B. This is not priced like the company building AI infrastructure.\n\nThere’s a growing consensus with Alphabet that AI is threatening its dominance. But if you look past the parroting crowd on CNBC, the numbers show that AI is not only improving Google's numbers today, but it may help it expand drastically in the future.\n\nQ1 2025 results:\n\n•\t⁠Revenue: $90.2B (+12% YoY)\n•\t⁠Net income: $34.5B (+46% YoY)\n•\t⁠EPS: $2.81\n•\t⁠Operating margin: 34%\n•\t⁠Free cash flow: $19B for the quarter\n•\t⁠TTM FCF: $74.9B\n•\t⁠CapEx planned for 2025: $75B, primarily for AI infrastructure\n•\t⁠Dividend: $0.21 per share\n•\t⁠Buyback authorization: $70B\n\nForward P/E: 16.2\nMarket cap: $1.86T\n\nNow compare this to:\n\n•\t⁠Meta: P/E 23.4\n•\t⁠Amazon: P/E 29.5\n\nIf Alphabet traded at Meta’s multiple, it would be worth $2.08T. At Amazon’s, $2.61T. That’s 12 to 40 percent upside with no multiple expansion beyond peers.\n\nSearch and Other revenue:\n$50.7B last quarter. That’s up 10% YoY. Gemini now powers over 100M AI-enhanced searches daily. Mobile query volume is still climbing. Ad targeting is improving. This is not a dying product; it's changing and likely for the better long term.\n\nPeople also don't consider the decades of data and analytics advantage that Google has over competitors to both train and implement its models.\n\nYouTube:\n$8.93B in Q1 ad revenue, +10.3 percent YoY\n70B daily Shorts views\n12 percent share of U.S. TV viewership\nPremium subs over 100M\nEstimated standalone value: $475B to $550B (MoffettNathanson)\n\nCloud:\n$12.26B in revenue, +28 percent YoY\nSustainably profitable\nEnterprise demand rising for AI-native tools (Vertex, BigQuery, Security AI Workbench)\n\nWaymo:\n250,000+ paid autonomous rides per week\nOperating in Phoenix, SF, LA, and Austin\nValued at $45B in its October 2024 round (expected 2030 valuation between 300-800B\nTargeting long-term platform economics across mobility, data, and fleet infrastructure.\n\nWaymo isn't just a robotaxi, it also allows google to implement internal UX that promotes local business, ads, and youtube (among other products) while continuing to grow its data advantage across its business segments.\n\nWhat’s mispriced?\n\n•\t⁠Search is growing and more monetizable with Gemini\n•\t⁠YouTube could be worth over 25 percent of Alphabet’s total value\n•\t⁠Cloud is scaling into profitability\n•\t⁠Waymo, DeepMind, and other moonshots provide embedded optionality\n•\t⁠Massive CapEx advantage ($75B vs. peers raising capital)\n•\t⁠Alphabet’s balance sheet is a war chest, not a safety net\n\nThis is not a story about one product. It's a behemoth that’s being priced like a dying ad business, despite deep infrastructure leverage and unmatched free cash flow.\n\nld love to hear counterarguments. But it looks like the market is still valuing 2019 Google, not the one building the foundation for AI and cloud-native platforms with a massive balance sheet and data advantage.\n\nHere's the full article if anyone's interested:\n\nhttps://northwiseproject.com/is-google-stock-a-buy/", "author": "TyNads", "created_time": "2025-05-10T23:29:12", "url": "https://reddit.com/r/ValueInvesting/comments/1kjnc22/is_chatgpt_the_end_of_google_search/", "upvotes": 101, "comments_count": 194, "sentiment": "bullish", "engagement_score": 489.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}]