[{"platform": "reddit", "post_id": "reddit_1iaxrco", "title": "The deepseek glazing is a little tiring here is why I think its not the miracle people think it is", "content": "So lets give credit were it is do. They trained a really great model. That's it. We can't verify the true costs, we can't verify how many \"spare GPU's\" that could be 100m worth of hardware, etc.\n\nFine lets take the economic implications out for a second here:\n\"BUT IT'S A THINKER! OH MY GOOOD GOLLY GOSH!\"\n\nyeah you can make any model a thinker with consumer level fine tuning:https://www.youtube.com/watch?v=Fkj1OuWZrrI\n\nchill out broski, 01 was the first thinking model so we already had this and again its not that impressive.\n\n\n\"BUT IT COSTS SO MUCH LESS\": yeah it was some unregulated project built on the foundations of everything we have learned about machine learning to that point. Even if we choose to believe that 5mm number, it probably doesn't account for the GPU hardware, the hardware those GPU's sit on, staff training costs, data acquisition costs, electricity. For all we know its just some psyops shit.\n\n\"BUT BUT, SAM ALTMAN\": Yeah i get it you dont' like billionaires, that doesn't make some random model that performs worse than 7 month old claude 3.5 in coding is THAT worthy of constant praise and wonderment. \n\n\nIf you choose to be impressed, fine, just know its NOT that credible of a claim to begin with and even if it was, they managed to get to 90 percent of the performance of models of almost a year ago with hundreds of thousands of \"spare gpus\".\n\n\nI think the part that has FASCINATED the laymen that populate this sub is the political slap to US companies more than any actual achievements. deep down everyone is resentful about American corporations and the billionaires that own them and so you WANT them to be put in their places rather than actually believing the bullshit you tell yourself about how much you love China.\n\n", "author": "OriginalPlayerHater", "created_time": "2025-01-27T02:45:53", "url": "https://reddit.com/r/singularity/comments/1iaxrco/the_deepseek_glazing_is_a_little_tiring_here_is/", "upvotes": 5, "comments_count": 53, "sentiment": "bullish", "engagement_score": 111.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iayb7e", "title": "Rate my investment allocations. Open to advice.", "content": "30M \n\n401k Roth: \n80% FXAIX\n10% FFLEX\n10% RNPGX  \n\nTraditional Investment account -\nGoogle, Amazon, Apple, Lowes, Tesla, Coke and Spy\n\nRoth -\nVT, VTI, VOO, SCHD\n\nLooking for a general consensus on whether this is a decent allocation of investments. I’d rather handle everything myself and not rely on a financial advisor. ", "author": "NoloveRNG", "created_time": "2025-01-27T03:14:04", "url": "https://reddit.com/r/FinancialPlanning/comments/1iayb7e/rate_my_investment_allocations_open_to_advice/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ib48gf", "title": "What are these ads? 💀", "content": "A video of <PERSON> Gaiden had an ad of a chick putting a pickle between her meat curtains. Nice one YouTube you’re really setting the bar high. ", "author": "AirTraditional6311", "created_time": "2025-01-27T09:18:39", "url": "https://reddit.com/r/youtube/comments/1ib48gf/what_are_these_ads/", "upvotes": 0, "comments_count": 46, "sentiment": "neutral", "engagement_score": 92.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibdy5y", "title": "(JOB OPENING) Platform Simulation Engineer, Waymo.", "content": "Waymo is an autonomous driving technology company with the mission to be the most trusted driver. Since its start as the Google Self-Driving Car Project in 2009, Waymo has focused on building the Waymo Driver—The World's Most Experienced Driver™—to improve access to mobility while saving thousands of lives now lost to traffic crashes. The Waymo Driver powers Waymo One, a fully autonomous ride-hailing service, and can also be applied to a range of vehicle platforms and product use cases. The Waymo Driver has provided over one million rider-only trips, enabled by its experience autonomously driving tens of millions of miles on public roads and tens of billions in simulation across 13+ U.S. states.  \n  \nSoftware Engineering builds the brains of Waymo's fully autonomous driving technology. Our software allows the Waymo Driver to perceive the world around it, make the right decision for every situation, and deliver people safely to their destinations. We think deeply and solve complex technical challenges in areas like robotics, perception, decision-making and deep learning, while collaborating with hardware and systems engineers. If you’re a software engineer or researcher who’s curious and passionate about Level 4 autonomous driving, we'd like to meet you.  \n  \nIn this hybrid role, you will report to a Software Engineering Manager.  \n  \n**You will:**  \n  \nAdvance our platform emulators, simulators and associated tools &  infrastructure to scale Waymo to more vehicles, faster.  \nImplement hardware in the loop (HIL) software features for new vehicle platforms  \nSoftware development and integration for off-the-shelf devices (simulators, adaptors, power supplies, etc.) and custom interface hardware  \nDevelop test automation frameworks: resource management and scheduling infrastructure, dashboard, user and admin utilities  \nIntegrate software models into the HIL simulation loop  \nHelp triage HIL tests failures  \nYou have:  \n  \n5+ Years of experience ein SW Development   \nStrong knowledge of modern C++  \nLinux on advanced user level  \nElectrical engineering fundamentals  \nWe prefer:  \n  \nPassion for HIL simulation  \nEmbedded programming experience  \nLinux for real-time applications  \nKnowledge of general-purpose and automotive network systems (Ethernet, CAN, etc)  \nAppreciation for design and code aesthetics  \nFamiliarity with the building blocks for data-center monitoring systems (databases, dashboards, etc)  \n\\#LI-Hybrid  \n  \nThe expected base salary range for this full-time position across US locations is listed below. Actual starting pay will be based on job-related factors, including exact work location, experience, relevant training and education, and skill level. Your recruiter can share more about the specific salary range for the role location or, if the role can be performed remote, the specific salary range for your preferred location, during the hiring process.   \n  \nWaymo employees are also eligible to participate in Waymo’s discretionary annual bonus program, equity incentive plan, and generous Company benefits program, subject to eligibility requirements.   \n  \nSalary Range  \n$158,000—$200,000 USD\n\nTo learn more and apply visit: [https://www.simulationengineerjobs.com](https://www.simulationengineerjobs.com)", "author": "Gold_Worry_3188", "created_time": "2025-01-27T17:00:14", "url": "https://reddit.com/r/AutonomousVehicles/comments/1ibdy5y/job_opening_platform_simulation_engineer_waymo/", "upvotes": 3, "comments_count": 0, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibgmdi", "title": "Could <PERSON><PERSON>’s Lead Be Swamped by General AI Advancements?", "content": "Waymo has a huge lead in the development of true self-driving technology—in at least two dimensions: (i) they are years ahead; and (ii) they have vast resources that they can and will devote to further improvements. With any sort of “normal” technology, you would expect these advantages to give them a huge advantage for years to come. It’s the promise of that huge market advantage that justifies the enormous R&D that <PERSON><PERSON> is throwing into the project.\n\nBut I wonder (I’m not predicting, I just wonder) whether generic AI technology will quickly improve to the point where “driving” will be trivial to solve by tomorrow’s generation of AIs. It wouldn’t be the first time that a market leader in current technology was leapfrogged by new advances.", "author": "OriginalCompetitive", "created_time": "2025-01-27T18:45:43", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ibgmdi/could_waymos_lead_be_swamped_by_general_ai/", "upvotes": 2, "comments_count": 196, "sentiment": "neutral", "engagement_score": 394.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibkcex", "title": "Nvidia sheds almost $600 billion in market cap, biggest one-day loss in U.S. history", "content": "Nvidia lost close to $600 billion in market cap on Monday, the biggest drop for any company on a single day in U.S. history.\n\nThe chipmaker’s stock price plummeted 17% to close at $118.58. It was Nvidia’s worst day on the market since March 16, 2020, which was early in the Covid pandemic. After surpassing Apple last week to become the most valuable publicly traded company, Nvidia’s drop on Monday led a 3.1% slide in the tech-heavy Nasdaq.\n\nThe selloff was sparked by concerns that Chinese artificial intelligence lab DeepSeek is presenting increased competition in the global AI battle. Late last month, DeepSeek unveiled a free, open-source large language model that it says took only two months and less than $6 million to build, using reduced-capability chips from Nvidia, called H800s. \n\nNvidia’s graphics processing units (GPUs) dominate the market for AI data center chips in the U.S., with tech giants like Alphabet, Meta, and Amazon spending billions of dollars on the processors to train and run their AI models. Analysts at Cantor wrote in a report on Monday that the release of DeepSeek’s latest technology has caused “great angst as to the impact for compute demand, and therefore, fears of peak spending on GPUs.”\n\nThe analysts, who recommend buying Nvidia shares, said they “think this view is farthest from the truth,” and that advancements in AI will most likely lead to “the AI industry wanting more compute, not less.”\n\nBut after Nvidia’s huge run-up — the stock soared 239% in 2023 and 171% last year — the market is on edge about any possible pullback in spending. Broadcom, the other big U.S. chipmaker to see giant valuation gains from AI, fell 17% on Monday, pulling its market cap down by $200 billion.\n\nData center companies reliant on Nvidia’s GPUs for their hardware sales saw big selloffs as well. Dell, Hewlett Packard Enterprise and Super Micro Computer dropped at least 5.8%. Oracle, a part of President Donald Trump’s latest AI initiative, fell 14%.\n\nFor Nvidia, the loss was more than double the $279 billion drop the company saw in September, which was the biggest one-day market value loss in history at the time, unseating Meta’s $232 billion loss in 2022. Before that, the steepest drop was $182 billion by Apple in 2020.\n\nNvidia’s decline is more than double the market cap of Coca-Cola and Chevron and exceeds the market value of both Oracle and Netflix.\n\nCEO Jensen Huang’s net worth also took a massive hit, declining roughly $21 billion, according to Forbes’ real-time billionaires list. The move demoted Huang to 17th on the richest-person list.\n\nThe sudden excitement around DeepSeek over the weekend pushed its app past OpenAI’s ChatGPT as the most-downloaded free app in the U.S. on Apple’s App Store. The model’s development comes despite a slew of recent curbs on U.S. chip exports to China.\n\nVenture capitalist David Sacks, who was tapped by Trump to be the White House’s AI and crypto czar, wrote on X that DeepSeek’s model “shows that the AI race will be very competitive” and that Trump was right to rescind President Joe Biden’s executive order last week on AI safety.\n\n“I’m confident in the U.S. but we can’t be complacent,” Sacks wrote.\n\nNvidia is now the third most-valuable public company, behind Apple and Microsoft.\n\nSource: https://www.cnbc.com/2025/01/27/nvidia-sheds-almost-600-billion-in-market-cap-biggest-drop-ever.html", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-01-27T21:16:38", "url": "https://reddit.com/r/stocks/comments/1ibkcex/nvidia_sheds_almost_600_billion_in_market_cap/", "upvotes": 15744, "comments_count": 1205, "sentiment": "bearish", "engagement_score": 18154.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ibnuab", "title": "MKBHD: I Tried Samsung's Secret Android XR Headset!", "content": "", "author": "BcuzRacecar", "created_time": "2025-01-27T23:43:17", "url": "https://reddit.com/r/Android/comments/1ibnuab/mkbhd_i_tried_samsungs_secret_android_xr_headset/", "upvotes": 9, "comments_count": 55, "sentiment": "neutral", "engagement_score": 119.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}]