[{"platform": "reddit", "post_id": "reddit_1h7ptwq", "title": "Google CEO <PERSON><PERSON> says the progress in AI is \"going to get harder\"", "content": "Google CEO <PERSON><PERSON> says the progress in AI is \"going to get harder\" because \"the low-hanging fruit is gone, the hill is steeper\" and \"you're definitely going to need deeper breakthroughs as we go to the next stage\"\n\n[https://x.com/tsarnick/status/1864474204864958642](https://x.com/tsarnick/status/1864474204864958642)\n\n  \nI saw this quote and it made me think of autonomous vehicles since we know they use AI to drive. It reminds me of what <PERSON><PERSON><PERSON><PERSON> said that it is relatively easy to do a self-driving demo with vision-only end-to-end but actually going from that to safe, reliable L4 is a lot harder. Can we think of the current autonomous driving capabilities as the \"low hanging fruit\" and getting AVs to the next level of safety and reliability will be harder? ", "author": "diplomat33", "created_time": "2024-12-06T01:36:07", "url": "https://reddit.com/r/SelfDrivingCars/comments/1h7ptwq/google_ceo_sun<PERSON>_p<PERSON><PERSON>_says_the_progress_in_ai/", "upvotes": 55, "comments_count": 27, "sentiment": "neutral", "engagement_score": 109.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h7qsyk", "title": "With a $1 million 401k, couldn't you just live off returns and never touch principle? ", "content": "Just spit balling here.  We will have close to $1 million in our 401ks when we retire. (<PERSON> and <PERSON>).  I know the 4% rule, etc.  But if the market averages 8% a year, couldn't we just take (on average) that $80k a year and live off that and not touch the principle?  Yes, there would be down years, and we'd have to make up for that (maybe just live on the social security or something).   A very ignorant and vague question I know...but just something crawling around in my head. ", "author": "Terrible-Historian-6", "created_time": "2024-12-06T02:25:19", "url": "https://reddit.com/r/FinancialPlanning/comments/1h7qsyk/with_a_1_million_401k_couldnt_you_just_live_off/", "upvotes": 489, "comments_count": 180, "sentiment": "neutral", "engagement_score": 849.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h7sv0o", "title": "Self Running Water Pump, And Others", "content": "You Tube Video--   I make free water pump no need electric power new style;  \n  \nThe 2 side water levels are equal, therefore no siphoning self running effect.  I am assuming there is a Lithium powerful battery inside the DC low voltage pump/motor side housing that is connected to the 2 plugs. The end shaft is also turned ,- the 2 electrical contacts connect, for the plug on the positive side, running the water pump. (acts like a jumper connection)The Pump stops when plugs disconnected,- for open switch state. He uses the drill power to overcome initial inertia , as pump is off when first plugged.  There is also some momentum saved. It takes lots of electricity to run an electric motor Watts of power. This however, could really be a great invention after all. Ac 220v generated same time as pump motor runs.  \n  \n  \n Also, a DC permanent magnet 12v car heater fan motor can be used to run a transformer . Transformer will run on either AC or pulsing on + off DC, but not DC.  By opening housing, snip wire section on each side 180 degrees apart on armature. Put back together. Run on oscilloscope top see pulsing square waves or hook motor to transformer to get a secondary voltage output. There will be ohms resistance in series from the motor wire. Turning shaft will produce on/off signals from wires output- running backwards.  \n  \nBack in the 1980's I saw an air compressor running by itself and used for mowing grass by pushing piston of mower with long hose out the window of trailer.. Retired carpenter had air tank with balanced SPOKED TYPE flywheel on main shaft with reciprocating hydraulic cylinder on journal of compressor shaft. (no motor, wires, battery) A preadjusted vortex tube heat shielded, had increased pressure of heated air to the cylinder keeping crankshaft going. He had 3 machined parts from 3 different machine shops , so as no one knew what final assembly was. 40lbs initially filled tank by hand rotation flywheel, until self powered.  \n  \nMan from Georgia ran his riding lawn mower engine on salt water in carb 2008. See Youtube videos S1R9A9M9 . Hidden 300watt Inverter box in rear hole of cement block. Battery start. battery removed. Engine ran on hydrogen from spark plug electrolysis of salt water. Alternator under flywheel has 12 magnets powering the conversion .Bridge rectifier  about 11 amp draw for inverter to 110v DC and about 4 amps to electromagnet over plug wire to change to ATDC timing for hydrogen. About 15 amps total . Peak pulse current 7-10A, average current about 1A to plug. Self running Briggs 18HP at idle speed. . Tiny electrodes of zero ohms spark plug requires high volts.  \n  \nMr. John Keely of late 1800's machine shop Philadelphia, had demonstrated self running rotating motor mechanism that had flywheel and band saw sawing wood for visitors,  and investors. Hydo pneumatic vacu engine. It had rotating 4 way shock valve that created internal pressure 50 lbs of water hammer.  Table top model is on Youtube that was stolen and later returned to museum. The Patent application showed it's workings. Full Patent not issued because Investor's names not on manuscript submitted. S.V.P. sells copies of old Keely documents.", "author": "Putrid-Bet7299", "created_time": "2024-12-06T04:12:40", "url": "https://reddit.com/r/CleanEnergy/comments/1h7sv0o/self_running_water_pump_and_others/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h7tx1g", "title": "55, no savings, no retirement, no home ownership. Terrified. ", "content": "I’m 55, no savings, no retirement, no home ownership.\n\nI’ll try to be brief in telling you how I got to this point, but bottom line is I made a poor life choice.\n\n10 years ago, I was married, a stay-at-home wife and mom for 15 years, when my husband “abruptly” walked out. (It turns out, an old girlfriend had tracked him down on Facebook and they’d been plotting his “departure” for several months.) I was shocked to learn he had secretly stopped paying the mortgage, knowingly leaving me and our children in a foreclosed home. He’d also depleted all of our savings. I received nothing in the divorce, as there were no assets left. An additional wrinkle was my diagnosis with a debilitating, chronic illness. \n\nThe past decade has been rough. My education and work before marriage had been in interior design. I was unable to find a job in that field post divorce. I returned to college, cramming through an accelerated bachelor’s program in healthcare administration. I used student loan money to help keep a rented roof over our heads. Upon graduation, I found a no-benefits, $10 per hour job in a doctor’s office. It took nearly every bit of my take home pay to cover rent. \n\nFast forward, I’m now making $20 per hour, as a contract worker. The contract house offers a self-funded health “insurance” plan and a ZERO-percent matching 401k. There are no raises, ever, and no chance to become a direct hire. My take home pay is a meager $2500 per month. I have tried and tried to find a better job, to no avail. At one point, I managed to find a second job, but after 5 months, the 16-hour work days caught up with me and my health. \n\nI have no idea how to get out of this mess. I am terrified about my financial future and worry about how many more years I’ll be able to work given my poor health. I would like to own a home again, not a large house like I used to have, but a small condo in a safe area, and I know I need a retirement savings, but I don’t know if it’s even feasible. Where do I start?\n\n\n\n", "author": "ijjhfds", "created_time": "2024-12-06T05:12:45", "url": "https://reddit.com/r/personalfinance/comments/1h7tx1g/55_no_savings_no_retirement_no_home_ownership/", "upvotes": 2721, "comments_count": 448, "sentiment": "neutral", "engagement_score": 3617.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h7uuu5", "title": "How to deal with $100,000 cash", "content": "Over 20 years I have gradually been saving cash in my safe at home. A long time ago, I thought it might be good to have cash in case the shtf. I’m kind of a prepper. But this month I realized it’s now $100,000. It’s all my money from my salary, but I don’t have records that far back. Plus nowadays everything is bought with a card or online, so I don’t even use that much cash anymore. So it just occurred to me, how am I going to use this?  \nI’m concerned that they’ll ask for proof of where I got it if I deposit more that $10,000 in bank or brokerage account. Maybe I should just start depositing $1000/month. It will take only 8 years, which is much less than what it took to accumulate ", "author": "Square-Brain9064", "created_time": "2024-12-06T06:09:30", "url": "https://reddit.com/r/financialindependence/comments/1h7uuu5/how_to_deal_with_100000_cash/", "upvotes": 0, "comments_count": 63, "sentiment": "bullish", "engagement_score": 126.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h7x5us", "title": "[D] Any OCR recommendations for illegible handwriting?", "content": "\nHas anyone had experience using an ML model to recognize handwriting like this? The notebook contains important information that could help me decode a puzzle I’m solving. I have a total of five notebooks, all from the same person, with consistent handwriting patterns. My goal is to use ML to recognize and extract the notes, then convert them into a digital format.\n\nI was considering Google API after knowing that Tesseract might not work well with illegible samples like this. However, I’m not sure if Google API will be able to read it either. I read somewhere that OCR+ CNN might work, so I’m here asking for suggestions. Thanks! Any advice/suggestions are welcomed! ", "author": "SpaceSheep23", "created_time": "2024-12-06T08:53:03", "url": "https://reddit.com/r/MachineLearning/comments/1h7x5us/d_any_ocr_recommendations_for_illegible/", "upvotes": 209, "comments_count": 172, "sentiment": "neutral", "engagement_score": 553.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h7zgvx", "title": "Oh my god is there actually NO WAY to copy coordinates?????", "content": "So due to my work i need to copy and send coordinates (android phone) that i literally just switched to from an iPhone and i realized unlike Google Maps for IOS, the android one does not give you any option whatsoever to copy the coordinates of any point. HILARIOUSLY HORRIBLE and i dont understand why thats available for ios but not on adroid, on their own damn software. Tried literally anything to get coordinates to copy, no option. It’s infuriating. Im about to return this samsung just purely for that single reason 😂\n\n", "author": "Monte666", "created_time": "2024-12-06T11:40:22", "url": "https://reddit.com/r/GoogleMaps/comments/1h7zgvx/oh_my_god_is_there_actually_no_way_to_copy/", "upvotes": 2, "comments_count": 11, "sentiment": "bullish", "engagement_score": 24.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h81h7e", "title": "Man stuns police in Virginia Walmart during annual 'Shop With a Cop' event: 'It just didn’t matter to him'", "content": "", "author": "nyuhokie", "created_time": "2024-12-06T13:35:16", "url": "https://reddit.com/r/nottheonion/comments/1h81h7e/man_stuns_police_in_virginia_walmart_during/", "upvotes": 21, "comments_count": 19, "sentiment": "neutral", "engagement_score": 59.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h82jlz", "title": "Looking for the Best Training Resources for SEO, Google Ads, and Meta Ads", "content": "Hi all, I’m looking to become more proficient in digital marketing, particularly in SEO, Google Ads, and Meta (Facebook/Instagram) advertising. Can anyone recommend the best Udemy courses, YouTube channels, or other online platforms for these topics? I'm especially interested in learning about Google Ads Editor and Google Analytics. It would be great if the resources cover both beginner and advanced levels. Appreciate your suggestions and thank you in advance!", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-06T14:27:57", "url": "https://reddit.com/r/DigitalMarketing/comments/1h82jlz/looking_for_the_best_training_resources_for_seo/", "upvotes": 15, "comments_count": 47, "sentiment": "neutral", "engagement_score": 109.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8312h", "title": "Best reply to a no show employee", "content": "Employee is pushing the line on taking “sick days” basically doesn’t get payed, so if it’s a slow day it’s whatever. But if it’s busy it sucks. \nWould much rather they just ask for a day off 2 weeks ahead then wouldn’t plan many jobs.\nWe are currently at 16 call ins for the calendar year.\n\nIt is normally a text about them feeling sick about an hour before start time. I have started to just not reply. Maybe that lets them sit in their thoughts all day. \nI have also replied with “busy day, let me know if you feel better by lunch and want to come in the afternoon” \n\nAny other options for a good reply ? ", "author": "Apart_Tutor8680", "created_time": "2024-12-06T14:50:38", "url": "https://reddit.com/r/smallbusiness/comments/1h8312h/best_reply_to_a_no_show_employee/", "upvotes": 0, "comments_count": 37, "sentiment": "neutral", "engagement_score": 74.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8350t", "title": "Scientists develop battery with 5,000+ year lifespan by encasing radioactive carbon in diamond | It could power electronics aboard space probes for centuries, among other things", "content": "", "author": "chrisdh79", "created_time": "2024-12-06T14:55:38", "url": "https://reddit.com/r/tech/comments/1h8350t/scientists_develop_battery_with_5000_year/", "upvotes": 1978, "comments_count": 105, "sentiment": "neutral", "engagement_score": 2188.0, "source_subreddit": "tech", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h85nik", "title": "How long do you think <PERSON><PERSON> remains the richest man in the world? Honestly I don’t see anyone passing him for 15+ years", "content": "", "author": "ConstructionRare4123", "created_time": "2024-12-06T16:44:49", "url": "https://reddit.com/r/elonmusk/comments/1h85nik/how_long_do_you_think_elon_musk_remains_the/", "upvotes": 0, "comments_count": 107, "sentiment": "bullish", "engagement_score": 214.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h85tfm", "title": "DON'T BUY DOMAINS FROM GODADDY!!!! SCAM!!!!!", "content": "I recently had a terrible experience with GoDaddy, and I feel compelled to warn others about their unethical and exploitative practices. \n\nHere's the list what they do (according to what i observed so far)  :-\n\n1. **Deceptive Domain Availability**: When I searched for a domain, it was initially shown as cheap and available. However, shortly afterward, it was claimed as unavailable, marked as a \"premium domain,\" and priced ridiculously high.\n2. **GoDaddy Monopolizes Domains**: I noticed that every domain I searched for was suddenly \"claimed\" by GoDaddy. Visiting these domains leads to their landing page, where they sell the domain at inflated prices or suggest hiring a broker—essentially forcing users to overpay for domains they initially showed as affordable.\n3. **WHOIS Confirms Suspicious Practices**: When I checked WHOIS information for one of the domains, here’s what I found:\n\nhttps://preview.redd.it/meuivvga995e1.png?width=782&format=png&auto=webp&s=51bcfc6eddabc92886686167d641e4309cbcea13\n\n4. It’s clear that GoDaddy is holding these domains through their proxy service, making them unavailable to regular users unless they pay a premium\n\n5. **Unfair Market Manipulation**: GoDaddy is deliberately creating scarcity by claiming domains people search for and reselling them at inflated prices. This is not just unethical; it’s exploitative and undermines trust in domain registration services.\n\n💡 **What You Can Do**:\n\n* Avoid GoDaddy at all costs! Use alternative domain registrars like Namecheap, Hostinger, Cloudflare or Porkbun.\n* Be cautious when searching for domains, as GoDaddy seems to track interest and manipulate availability to force you to pay more.\n\n⚠️ **Conclusion**:  \nGoDaddy’s practices are a blatant scam, designed to exploit users searching for domains. They manipulate the market to extort money and provide no transparency in the process. Stay away from GoDaddy and use trustworthy alternatives instead!\n\nthis happen for all the domain i checked specially (.com) all the one also don't say .com are popular in demand then why it available and now its register then why now and why after i checked and why all and why all the domain are directing to godaddy not once to their registered owners website\n\nhere the live proof:\n\n[in hostinger it says](https://preview.redd.it/0exjn3cra95e1.png?width=494&format=png&auto=webp&s=a1bc97f19c449d2e9bea8ec4709d6d4a7c63f223)\n\n[in godaddy it says](https://preview.redd.it/fkd9h3cra95e1.png?width=948&format=png&auto=webp&s=d9afed2c6bb2e252eb3d2660741d74cc8d208c3d)\n\n[this image is specifically a domain that i wanted its not said premium but it register after i lookup and add to cart in a one day no owner but godaddy only \\(it's been like this for 3-4 months\\)](https://preview.redd.it/72tdix5za95e1.png?width=1919&format=png&auto=webp&s=2d0004a055e4d2dd4d1456d00bc49959f86f4b24)\n\nof course:) i know u say what premium in godaddy visit page look like\n\nhttps://preview.redd.it/dib8gcqnb95e1.png?width=1919&format=png&auto=webp&s=99eb7ee338e58af82f21676effb181c3222ab9a5\n\nsee they are telling to buy at high price it's okay this premium one i don't want but why do they claim anonymously the domain after i looked or searched on their website in one day even after 3 months the domain does not have a website only godaddy promotion this is for all domains i looked into", "author": "deleted", "created_time": "2024-12-06T16:51:46", "url": "https://reddit.com/r/webdev/comments/1h85tfm/dont_buy_domains_from_godaddy_scam/", "upvotes": 1016, "comments_count": 551, "sentiment": "bearish", "engagement_score": 2118.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h887ow", "title": "Will Authentication always suck?", "content": "The entire web dev scene is  coming to a point where authentication is annoying than ever. Something so simple in concept but the way it is being implemented has frustrated me to a point that I'm writing this because i want to know if it is only me who can't implement proper auth. \n\nI've used many authentication services across many architectures but in my 3 years of experience I've still not figured out a way or even guidelines that i can follow for implementing authentication across all my applications! Which completely sucks. Authentication is probably the one thing that is keeping me from developing any of my projects which is pretty stupid but if not implemented correctly it poses huge security risks.\n\n  \nSo I'm writing this to well, maybe discuss what is your approach to authentication. \n\n  \nFor context, I'm a full-stack developer (MERN stack) and the problem i encounter is implementing cross-oirigin authentication, many MERN auth videos i see online they either only do it on the frontend or if they are adding auth to both the client and server, that approach is usually not practical or has security risks. I mostly succeed in adding auth to the frontend side, it is securing the backend where i usually screw up.\n\n  \nAlso when i check the documentation of auth libraries (ex. clerk or next-auth) they lack documentation for client-server architecture and how to secure routes in both frontend and backend. I usually come up with my own route protection system when using any of these but again all types of questions are in my mind. \n\n**Is this conventional? Is this secure? Is this a good approach?**\n\n  \nAgain it's not like I've not implemented authentication in a client-server architecture but it's these questions that make me doubt my way of doing things. Anyways would like to know your opinions on this!\n\n", "author": "Clean_Mention2022", "created_time": "2024-12-06T18:32:27", "url": "https://reddit.com/r/webdev/comments/1h887ow/will_authentication_always_suck/", "upvotes": 2, "comments_count": 77, "sentiment": "neutral", "engagement_score": 156.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h89lme", "title": "Found in old transistor radio", "content": "Didn’t even leak", "author": "Still_Statistician", "created_time": "2024-12-06T19:31:44", "url": "https://reddit.com/r/batteries/comments/1h89lme/found_in_old_transistor_radio/", "upvotes": 304, "comments_count": 55, "sentiment": "neutral", "engagement_score": 414.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8a421", "title": "I don't really understand what Google is trying to do in terms of releasing AI to the public, it's the most confusing and clusterf*ck of an exercise in all of AI", "content": "They started with Bard, the worst chatbot ever which they renamed later to Gemini and which now comes with a free Gemini 1.0 version that's still probably the worst free model among all the big tech providers. They have Gemini advanced for paid users which is again arguably the worst paid service among all other providers. For the past year, they have been releasing all their best models on AIstudio. Just in past two months there have been three different models with different dates in their names and no official benchmark or post of any kind explaining what their difference is and why tf would anyone need this many models. Most of the general public don't even know about AIstudio and will never use it. Seems like they are just caught in some competition to game lmsys leaderboard and have no intention of releasing an actual product. Their AI search overview takes the crown as the absolute worst AI product ever and is regularly used as an example of AI slop. \n\nThey failed to capitalize on their only successful products Gemini flash and Notebooklm. The notebooklm leads have now left to form their own startups. Seems to me like a complete and utterly incompetent leadership who have no clue how to make an actual product.", "author": "obvithrowaway34434", "created_time": "2024-12-06T19:53:52", "url": "https://reddit.com/r/singularity/comments/1h8a421/i_dont_really_understand_what_google_is_trying_to/", "upvotes": 29, "comments_count": 61, "sentiment": "neutral", "engagement_score": 151.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8aeuu", "title": "Planning to FIRE in January, Sanity Check", "content": "Throwaway account for privacy. After working towards FIRE since college, I think I'm ready to pull the trigger in January! I'm quite confident in our numbers but figure it can't hurt to look for holes in the plan.\n\nI'm a 38 year old mechanical engineer, \\~200k/year income. Wife is 36 and works in Tech, income has varied with stock vests, will peak this year at $550k, dropping to $400k next year and would likely drop further the following year. No kids and no plans to have kids. Live in a VHCOL area. I realize we have been very lucky in our careers as well as with how the market has performed to allow us to consider quitting this early. In the last 12 months our investable net worth has gone up 50% between our savings and market appreciation which has trimmed a year or so off my anticipated timeline. I would have settled for a higher withdrawal rate prior to this last year of crazy market appreciation so we've actually surpassed our \"target number\" awhile ago.\n\n**Current assets:**\n\nNW: $4.5m + Primary residence.\n\nTaxable Index Funds: $1.65m  \nTaxable Bonds: $450k  \nTaxable Tech Shares: $650k  \nCash: $60k  \nRetirement Index Funds: $1.7m  \nPrimary Home: $1m  \nMortgage: $360k @ 2.1%, 11 years remaining on 15 year.  \nNW: $4.5m + Primary residence.\n\n**Spending:**\n\nCurrent annual spending: $155,000.  \nAnnual spending without Principal/Interest on mortgage: $120,000\n\nProjected Health Insurance Spending: $15,000/year NOTE: non insurance healthcare spending is already part of the current annual spending.\n\nExtra Spending for home repairs/car replacement etc: $20,000/year\n\nFuture home upgrade: 5+ years down the line we will likely want to upgrade our primary residence after we get some travel out of the way. Will likely spend an additional $750,000 but will be flexible on timing and amount based on circumstances at the time.\n\n**Other Income:**\n\nWe receive approximately $24,000/year in cast gifts from parents. While not absolutely guaranteed, this is unlikely to stop prior to them passing on. Parents are 75 but generally healthy. Likely to receive at minimum $1.5m for inheritance.\n\nWife will plan to continue working for 1 additional year as insurance against the bottom falling out of the market the day after I quit. That's an extra \\~$300k in after tax income between now and both of us pulling the trigger.\n\n**Thoughts:**\n\nAssuming small but positive market gains between now and my wife pulling the trigger in early 2026, we are on track to have approximately $5m in investable assets as well as receiving $24k/year in gifts.\n\nOur spending, stripping out the principle/interest portion of our mortgage, is $131k/year once I add in the $35k in extra health insurance/durable goods spending but subtract the $24k in gifts. Round it up to $140k after accounting for taxes and a bit of extra spending.\n\nI plan to roll the dice on leaving money invested in the stock market vs paying off our 2.1% mortgage. If the market is still super frothy in 1-2 years when we can start taking advantage of the 0% capital gains rate, I may sell off enough shares to pay off the house and invest that money in bonds to take advantage of the 4% interest rate vs our 2.1% mortgage. Will evaluate at the time based on taxes, ACA subsidies and the state of the stock market.\n\nOver the next year and a bit before my wife retires, I plan to finish putting another $300k into bonds. At that point we will have $750k spread out in $35-$40k lumps that come to term every 3 months. That works out to 5 years of expenses other than our mortgage repayment.\n\nSubtracting the $330k that we will owe on our mortgage from the $5m leaves $4.67m in investable assets. With $140k in recurring expenses, assuming we wanted to plan for a 3.5% withdrawal rate, that would imply we have about $670k more than we need. Given our plan to upgrade our house in the 5-10 year future and that will likely involve a \\~$750k expenditure, it seems likely that the $670k current excess will grow to cover that as well as some increased expenses that will come along with the larger home. This is a flexible expense regarding both the amount and timing. Depending on circumstances we could also look into getting a mortgage or loan against our brokerage if the numbers looked favorable instead of selling shares to cover the home upgrade.\n\nGiven the fact that we are both in our late 30’s, we also recognize that we have ample opportunity to make some money in the future, whether taking on some work in our old industries or getting a casual job just to ease some pressure on the finances if there has been a big downturn. We also live in one of the most expensive cities on earth, love to travel, and bought a campervan last year that we want to make much more use of. I believe that it is very possible that our expenses could potentially be lower in retirement when we can spend more time traveling to places where things are significantly cheaper. We also plan to be flexible in our spending. If the market drops 30% I’m perfectly happy to use some airline miles or drive our van down to Mexico/Costa Rica and sit on a beach or spend more time backpacking and hiking that year instead of taking a more expensive trip to Europe.\n\nLet me know what you think. I feel confident but want to know if I’m totally neglecting something obvious.\n\n ", "author": "Parking_Jaguar774", "created_time": "2024-12-06T20:06:33", "url": "https://reddit.com/r/financialindependence/comments/1h8aeuu/planning_to_fire_in_january_sanity_check/", "upvotes": 3, "comments_count": 15, "sentiment": "bearish", "engagement_score": 33.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8azpj", "title": "Lost my whole port today", "content": "Bought Tesla 0dte today at 10 got beat the whole day lost 80% my net worth 90% my port went from $34k to under $3k feel so beat and lost don’t know what to do now ", "author": "Fopocketfull", "created_time": "2024-12-06T20:32:17", "url": "https://reddit.com/r/options/comments/1h8azpj/lost_my_whole_port_today/", "upvotes": 193, "comments_count": 373, "sentiment": "neutral", "engagement_score": 939.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h8dxv6", "title": "Thoughts & Advice on my Individual Portfolio- Currently at $90k, started at $40k going back 2+ years on some", "content": "Soundhound is obvs what I’ve gone “all in” on the most, I have 3,000+ shares of it, got into SH and Rivian WAY early, the rest has been bought about 3 months ago and I just bought PLTR today at $75, I have 200 shares of it. I’ve done my research on these stocks, their P/E, CEOs, directions and they are all long term holds. Especially Opendoor. The housing market is bad now but if it ever rebounds, that stock could blow up for me, it by far my riskiest of the lot, IMO. Any advice is welcomed!!", "author": "GatorBo69", "created_time": "2024-12-06T22:44:19", "url": "https://reddit.com/r/StockMarket/comments/1h8dxv6/thoughts_advice_on_my_individual_portfolio/", "upvotes": 0, "comments_count": 58, "sentiment": "bullish", "engagement_score": 116.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}]