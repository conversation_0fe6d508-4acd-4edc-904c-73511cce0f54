[{"platform": "reddit", "post_id": "reddit_1kx4jqs", "title": "Burnout and “one more year” syndrome", "content": "I am 40, married, 1 little child, west coast HCOL.\n\nHave been in tech for the last 20 years — did everything from tech support to software engineering, and from startups to big tech (at Google as of “recent”).\n\nMy total comp has steadily increased through time and now sits at around $800k/year (crazy, I know, I still can’t believe it!), my spouse has a “normal” non tech job at $90k/year.\n\nWe have $4.5M saved up between taxable and tax advantaged accounts, no cap gain, very conservative allocation. Zero debts and no other assets (we rent). Our expenses are about $150k/year (most of it is rent + childcare).\n\nIt was a long road to get to this point, with ups and downs and starting from very humble beginnings. In the last couple of years I have hit a very rough patch at work (a string of terrible managers, mismanaged projects, layoffs) and had to deal with some health issues. I despise my current role, and ironically I keep getting more responsibilities and the highest ratings.\n\nI never thought I’d say this, but for the first time in my life I just feel extremely tired and burned out. I kept pushing as each month those sweet RSUs keep coming.\n\nWe could easily relocate to LCOL. I fantasize every day about just quitting and enjoying life, exercise, read a book, slow down. I just can’t bring myself to do it: “one more year”, “one more month”, “one more week”.\n\nI think of all the folks that would do anything for a $800k/year job and feel guilty throwing that away.\n", "author": "GOOG_FIRE-oneoff", "created_time": "2025-05-28T01:22:21", "url": "https://reddit.com/r/Fire/comments/1kx4jqs/burnout_and_one_more_year_syndrome/", "upvotes": 139, "comments_count": 92, "sentiment": "bullish", "engagement_score": 323.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kx5iyr", "title": "Google search campaign starting out", "content": "I'm running a Google ad campaign, it's one that I ran for years before Google changed it's algorithm and then eventually blocked my account. When I first ran the campaign it was very easy to get started I just did \"american airlines\" as a keyword and set up a conversion action for phone calls. Google got this done and got me tons of calls within the first few hours and my conversion action knew what it was doing. However, after the Google changes and suspension of my original account I'm trying to get this campaign up and running again with Google ads. Except, I think the issue that I'm having now is that Google doesn't know what a conversion action is and because the broad match / search intent is now king, it doesn't actually know what kind of traffic to send me. Thus, I get tons of clicks for \"american airlines\" because there is like 10,000 a day, but the ones I get are low cost and low intent clicks and I don't get any conversions.  \n  \nI've tried doing this on exact match keywords, \\[american airlines phone\\], as an example, but all those keywords inevitably lead back to the search term \"american airlines\". Which I think would be totally fine if Google had just a few conversions and knew what a conversion for me and my account was. But unfortunately, I haven't been able to get it there and it just keeps overspending my budget.  \n  \nI've tried doing portfolio bidding with a minimum bid amount that is rather high, thus I am in more competitive auctions instead of what likely would be junk traffic. I've also tried doing a high CPA and it just doesn't do anything or totally over spends everything. Nothing has really worked for this. I think the solution would be to get Google as many conversion actions as possible, but this is seeming to be impossible at this time. Not sure what other approaches to take? How can I just really push Google to get those first few conversions?", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-28T02:11:05", "url": "https://reddit.com/r/adwords/comments/1kx5iyr/google_search_campaign_starting_out/", "upvotes": 1, "comments_count": 2, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kx7zt2", "title": "Are These \"Liquid Sodium\" Battery Stats Too Good to Be True?", "content": "Looking for Unbiased Expert Feedback\n\nHey r/batteries,\nI came across a comparison chart of different battery chemistries, and one entry—simply labeled \"Liquid Sodium\"—absolutely jumped out at me for its incredible stats. To keep things as unbiased as possible, I’ve **erased the brand name** from the image, since I really want to hear honest, technical feedback from the community without any marketing influence.\n\nHere are the standout specs for this \"Liquid Sodium\" battery (see attached image for the full table):\n\n- **Energy Density:** 552 Wh/kg (much higher than anything else listed)\n- **Life Cycle:** No loss in discharge (unlimited cycles?)\n- **Open Circuit Voltage:** 4.2V\n- **Operating Temp:** 120°C to 250°C\n- **Self-Discharge:** 0% per month\n- **Safety:** No thermal runaway, insulated for high temps\n- **Toxicity:** Low risk, earth-abundant materials\n\n**A few questions for the experts:**\n- Are these stats for \"liquid sodium\" batteries realistic, or is there something I should be skeptical about?\n- What are the main engineering or safety challenges for batteries that operate at such high temperatures?\n- Has anyone seen independent, peer-reviewed data or real-world deployments for this kind of cell?\n- Are there key factors left out in this chart\n- What would be the biggest technical or commercial barriers?\n\nI’m genuinely curious if this is a breakthrough or just another overhyped lab result. Would love to hear your thoughts—especially from anyone with hands-on or research experience in molten/thermal battery tech.\n\nThanks in advance!\n\n*(Image attached for reference—brand name removed for unbiased discussion)*\n\n\n\n", "author": "julian_jak<PERSON>i", "created_time": "2025-05-28T04:20:15", "url": "https://reddit.com/r/batteries/comments/1kx7zt2/are_these_liquid_sodium_battery_stats_too_good_to/", "upvotes": 143, "comments_count": 169, "sentiment": "bearish", "engagement_score": 481.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxat9g", "title": "Call Ads / Ad schedule", "content": "I have Google ads that run 8am to 8pm. We are only open 9 to 5.\n\nIs there a way to only schedule one ad in an adgroup? I.e. the call ad\n\nThanks", "author": "Enough_Complaint_468", "created_time": "2025-05-28T07:14:30", "url": "https://reddit.com/r/adwords/comments/1kxat9g/call_ads_ad_schedule/", "upvotes": 1, "comments_count": 4, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxdp9l", "title": "Google announces SignGemma their most capable model for translating sign language into spoken text", "content": "\"This open model is coming to the <PERSON> model family later this year, opening up new possibilities for inclusive tech.  \nShare your feedback and interest in early testing ?\": [http://goo.gle/SignGemma](http://goo.gle/SignGemma)  \n[https://x.com/GoogleDeepMind/status/1927375853551235160](https://x.com/GoogleDeepMind/status/1927375853551235160)", "author": "Nunki08", "created_time": "2025-05-28T10:31:27", "url": "https://reddit.com/r/singularity/comments/1kxdp9l/google_announces_signgemma_their_most_capable/", "upvotes": 1445, "comments_count": 112, "sentiment": "neutral", "engagement_score": 1669.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxhtuz", "title": "Live From Vegas: Bitcoin Beats Amazon And Google—What You Need To Know - Forbes", "content": "", "author": "AmazonNewsBot", "created_time": "2025-05-28T14:00:04", "url": "https://reddit.com/r/amazon/comments/1kxhtuz/live_from_vegas_bitcoin_beats_amazon_and/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "amazon", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxjbkp", "title": "Looking for Guidance to Start in Ad Management 🙏", "content": "Hello and good afternoon, fellow AdWorks members!\n\nI'm a 17-year-old who just graduated from high school, and I’m really interested in learning about ad management. The thing is, I’m not sure where to start—whether I should dive into YouTube tutorials, invest in a course, or try learning everything on my own. Fortunately, I have a lot of free time to dedicate to this.\n\nMy goal is to start managing ads for local real estate businesses—I already have some contacts in that industry—as well as for online pages. I’m very open to learning and would love to hear any advice or guidance you can offer.\n\nIt would mean a lot to me if you could recommend useful resources or share how you personally got started in this field. If anyone is open to teaching or mentoring, I’d be incredibly grateful.\n\nI’m always open to DMs and willing to listen to anyone who wants to share their experience or help. You can also reach me at [**<EMAIL>**](mailto:<EMAIL>) or on Instagram at u/sergiisuarezz if that’s more convenient.\n\nThank you very much in advance—I’m really looking forward to connecting with you and learning from this amazing community!", "author": "EconomyProduct5553", "created_time": "2025-05-28T15:00:53", "url": "https://reddit.com/r/adwords/comments/1kxjbkp/looking_for_guidance_to_start_in_ad_management/", "upvotes": 2, "comments_count": 4, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxjqw1", "title": "Google Pixel and Honor are reportedly the fastest-growing Android brands in Europe right now", "content": "", "author": "EqualReality2787", "created_time": "2025-05-28T15:17:16", "url": "https://reddit.com/r/GooglePixel/comments/1kxjqw1/google_pixel_and_honor_are_reportedly_the/", "upvotes": 859, "comments_count": 144, "sentiment": "neutral", "engagement_score": 1147.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxk6w9", "title": "Google Ads had 10+ campaigns during the given time range, GA4 only shows 4", "content": "I have a rather popular problem, but I cannot find the solution that fits\n\nI'm seeing a massive discrepancy between my Google Ads clicks and Google Analytics 4 (GA4) sessions, and I'm also finding that many of my campaigns aren't even appearing in GA4. I'm hoping someone can help me pinpoint the issue.\n\nIn Google Ads, for the same time period, I see 1691 clicks. In GA4, for \"Paid Search\" (specifically Google Ads campaigns), I only see 298 sessions. This is a \\~82% drop-off.\n\nMy GA4 \"Traffic Acquisition\" report (using \"Session Google Ads campaign\" dimension) only shows 4 campaigns. However, I had over 10 active and paused campaigns running during that exact same period in Google Ads. The missing campaigns simply don't show up in GA4 at all.\n\n**What I've already checked:**\n\n* Auto-tagging is ON in my Google Ads account.\n* Google Ads and GA4 are linked.\n* The date range in GA4 matches the Google Ads date range where the campaigns were active/paused.\n\nAre there any common issues I'm missing that could cause both the huge click/session discrepancy and the completely missing campaigns?\n\nAny insights or troubleshooting steps would be greatly appreciated! Thanks in advance.", "author": "iwf_wh", "created_time": "2025-05-28T15:35:11", "url": "https://reddit.com/r/marketing/comments/1kxk6w9/google_ads_had_10_campaigns_during_the_given_time/", "upvotes": 1, "comments_count": 2, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxlzvg", "title": "Google took 25 years to be ready for this AI moment. Apple is just starting", "content": "I thought it fitting that Apple would recoin AI and Apple Intelligence. That's just so Apple: take something, make it their own, then build a walled garden around it.\n\nIt's just too bad that they're coming into the industry with not much. They don't have data centers nor the compute or a SOTA model of their own. They're trying to run AI on the iPhone due to privacy but that's no where near enough compute. <PERSON><PERSON> is a joke.\n\nMicrosoft has partnered with Chat-GPT\n\nGoogle has Gemini\n\nMeta has Llama\n\nAmazon has partnered with Anthropic\n\nApple will use third party LLMs but they're treated as 3rd parties due to privacy concerns.", "author": "GamingDisruptor", "created_time": "2025-05-28T16:46:20", "url": "https://reddit.com/r/singularity/comments/1kxlzvg/google_took_25_years_to_be_ready_for_this_ai/", "upvotes": 1087, "comments_count": 230, "sentiment": "neutral", "engagement_score": 1547.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxmkpg", "title": "Google Ads Leads Dropped to Zero - No Major Changes. Advice?", "content": "I run Google Ads for a B2B manufacturing company. We were getting \\~10 leads/week until mid-May, then leads suddenly dropped to zero.\n\nNo major changes were made:\n\n* Budget is steady at $210/day\n* CPA target is $200\n* All ads rated “Excellent”\n* Same landing page, same structure\n\nWhat I’ve tried:\n\n* Testing new keywords and ad copy\n* Adjusting bids and CPA targets\n* SEMrush confirms a sharp traffic drop around May 17\n* Impression Share tanked (Top IS <10%, Lost IS >80%)\n* No automated rules in play\n\nEven our best-performing keywords have stopped converting. Could this be algorithm-related or competitor pressure?\n\nWould love any thoughts. Thanks!", "author": "Disastrous-Gold3841", "created_time": "2025-05-28T17:09:11", "url": "https://reddit.com/r/marketing/comments/1kxmkpg/google_ads_leads_dropped_to_zero_no_major_changes/", "upvotes": 2, "comments_count": 6, "sentiment": "bearish", "engagement_score": 14.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxorym", "title": "My negative keywords at the ad group level are not working as expected", "content": "Is this another example of Google's overreach into our pockets (likely) or is my understanding of how negative keywords function in need of an adjustment (also likely)?\n\n* In Ad Group 1, I am targeting the keyword \\[health ball\\] (exact match)\n* To control my messaging for health-related terms, I've applied the negative keyword \"health\" (phrase match) to Ad Group 2\n* In spite of this, I see clicks for \\[health ball\\] in my search terms report for Ad Group 2 from an exact match keyword that doesn't include the word 'health' or 'ball'\n\n...What the bloody hell?", "author": "wldsoda", "created_time": "2025-05-28T18:35:59", "url": "https://reddit.com/r/adwords/comments/1kxorym/my_negative_keywords_at_the_ad_group_level_are/", "upvotes": 1, "comments_count": 0, "sentiment": "bearish", "engagement_score": 1.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxqhcb", "title": "Best ad platform for selling products? Facebook, Google, Tiktok, Instagram?", "content": "After a year in business I think I want to try spending some money on ads.. Business can be extremely slow some days and just isn’t cutting it if I ever want to do this full time. \n\nSeems like I’m gonna have to spend quite a few $ on this stuff so i’d like to preferably spend it wisely. \n\n\nOpen to listening to all your comments and advice! ", "author": "Big-Advice-4009", "created_time": "2025-05-28T19:43:39", "url": "https://reddit.com/r/marketing/comments/1kxqhcb/best_ad_platform_for_selling_products_facebook/", "upvotes": 3, "comments_count": 9, "sentiment": "bearish", "engagement_score": 21.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxskwh", "title": "Please fill out this investing google form for my school project", "content": "Hey guys, I'm conducting a mini research project in school on investing trends, specifically among teens (but everyone is welcome to respond). It would be great if you could fill out this super short google form so I can collect data for the project. Thank you very much.\n\n[https://docs.google.com/forms/d/e/1FAIpQLSdvFbUYOE9NlDe3DGejGsUCfhX4B2OOogZoMJeU90lI6U4f-g/viewform?usp=sharing&ouid=112884597025009281369](https://docs.google.com/forms/d/e/1FAIpQLSdvFbUYOE9NlDe3DGejGsUCfhX4B2OOogZoMJeU90lI6U4f-g/viewform?usp=sharing&ouid=112884597025009281369)\n\n", "author": "Crafty-Sprinkles4063", "created_time": "2025-05-28T21:06:30", "url": "https://reddit.com/r/dividends/comments/1kxskwh/please_fill_out_this_investing_google_form_for_my/", "upvotes": 0, "comments_count": 5, "sentiment": "bullish", "engagement_score": 10.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kxwbfn", "title": "Please fill out this investing google form for my school project", "content": "Hey guys, I'm conducting a mini research project in school on investing trends, specifically among teens (but everyone is welcome to respond). It would be great if you could fill out this super short google form so I can collect data for the project. Thank you very much.\n\nhttps://docs.google.com/forms/d/e/1FAIpQLSdvFbUYOE9NlDe3DGejGsUCfhX4B2OOogZoMJeU90lI6U4f-g/viewform?usp=sharing&ouid=112884597025009281369", "author": "xykwnthrkodu", "created_time": "2025-05-28T23:47:29", "url": "https://reddit.com/r/investing_discussion/comments/1kxwbfn/please_fill_out_this_investing_google_form_for_my/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}]