[{"platform": "reddit", "post_id": "reddit_1jhuhah", "title": "Anyone else feel like YouTube is becoming less personal and more like soulless businesses trying to take your attention?", "content": "I'm clearly not the demographic for channels like this, but it's a good example, and I'm not discrediting their hard work or saying they don't deserve the views but like it's really weird how so many people are becoming YouTubers and it feels like they're doing whatever gets views. Everything is so oversaturated and everything is so sensationalized and I feel like everyones just in it for the money", "author": "No-Session-3841", "created_time": "2025-03-23T08:15:17", "url": "https://reddit.com/r/youtube/comments/1jhuhah/anyone_else_feel_like_youtube_is_becoming_less/", "upvotes": 0, "comments_count": 33, "sentiment": "neutral", "engagement_score": 66.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jhvd6a", "title": "WTF is Google up to with exact match  / close variants etc.", "content": "Google is driving me insane.\n\nFor context, i run exact match keywords only at this point. 4 ad groups, each with just 4/5 exact match keywords. I have an insanely long negative keywords list to avoid competition between ad groups.\n\nI have a 9/10 QS for the exact match keyword \\[treatment for XYZ\\], so pretty solid. This is in an ad group for those which i assume have high buying intent, specifically looking for \"treatment for XYZ\".\n\nI have another ad group for those higher up in the funnel, that are looking for \"solutions for XYZ\". This ad group has plenty of negative keywords to avoid people looking for \"treatment\" being served an ad from this group. Including obviously the word \"treatment\" as phrase match negative.  \nAnd Google STILL pulls it of to serve an ad from this group for someone that typed \"treatments for XYZ\". So just because this person typed the plural of my keyword with a 9/10 QS, they decided to show an ad from an entirely different ad group, with keywords having a lower score, leading to higher CPC.\n\nREALLY, GOOGLE? WTF?\n\nHonestly, I was already convinced that Google has a \"how do we screw advertisers over as much as a monopolist can get away with \" algo going on, but this??", "author": "No-Cover6510", "created_time": "2025-03-23T09:21:03", "url": "https://reddit.com/r/PPC/comments/1jhvd6a/wtf_is_google_up_to_with_exact_match_close/", "upvotes": 46, "comments_count": 43, "sentiment": "bearish", "engagement_score": 132.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ji3cxe", "title": "I just made a chrome extension extracts key finance and investment insights from news articles.", "content": "https://preview.redd.it/xux7mp2tvgqe1.jpg?width=640&format=pjpg&auto=webp&s=23d086e668cd2f7735b41648b5df5bbc10492642\n\n[Key risks and assets in articles](https://preview.redd.it/66dcrhwxtgqe1.png?width=1080&format=png&auto=webp&s=4ddc29335230fb511f5d418cc3a35741b37841c3)\n\nI just made a browser extension designed that instantly **summarises articles, extracts key assets mentioned**, and **categorises potential risks** highlighted in the article, saving you hours of research. Its called [Finsnap AI](https://chromewebstore.google.com/detail/finsnap-ai/ajpfdapcgbaioebagpbjhlcadlinmmbo). Would love to hear your thoughts—could this change the way you keep up with the markets? [Try it](https://chromewebstore.google.com/detail/finsnap-ai/ajpfdapcgbaioebagpbjhlcadlinmmbo) out and let me know what you think. Would love to hear from you.", "author": "Lumpy-Piece5555", "created_time": "2025-03-23T16:36:02", "url": "https://reddit.com/r/investing_discussion/comments/1ji3cxe/i_just_made_a_chrome_extension_extracts_key/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ji57nj", "title": "Help a student with research on investment behavior", "content": "  \nI'm conducting a research project exploring how behavioral biases affect retail investor decisions during economic uncertainty. If you've ever invested or traded, your insights would be incredibly valuable!  \n[https://docs.google.com/forms/d/e/1FAIpQLSeqA0zhlJlVohGReOA6qT2ippVuJkDVkLDTiWkKAjwunrGhww/viewform?usp=dialog](https://docs.google.com/forms/d/e/1FAIpQLSeqA0zhlJlVohGReOA6qT2ippVuJkDVkLDTiWkKAjwunrGhww/viewform?usp=dialog)", "author": "Extreme-Thanks-4781", "created_time": "2025-03-23T17:53:52", "url": "https://reddit.com/r/investing_discussion/comments/1ji57nj/help_a_student_with_research_on_investment/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jid9v7", "title": "Youtube is blatantly censoring any comments that get too specific about the actions of our current govt. They are preventing truth from spreading.", "content": "They're trying so hard to prevent the average person from learning what people are actually upset about. They want to push the narrative that it's just about hating one dude or not caring about wasteful spending, rather than people being extremely concerned that our free democracy itself is being dismantled.\n\n\\---\n\nMore detail: So this is something I started noticing at least 2 or 3 years ago, but I assumed maybe it was just an issue specific to my super old account. Though lately I noticed its not every comment that gets censored, only ones that try to go into specific detail about the most concerning things this administration is doing.\n\nAfter noticing that, I tried creating a new account to see if my comments would actually post. The first 2 or 3 did, then the same censorship started happening, where it it makes it seem like the comment was never posted in the first place, no warning or explanation as to why. In fact, you won't notice you've been censored at all, if you don't go back and check.\n\nIt is literally impossible on youtube to talk explicitly about the real things that have been happening. No matter how objective and neutral you try to phrase things, or how much you try to self-censor to figure out what is filtered. Its an attempt to suppress discussion.", "author": "F4cetious", "created_time": "2025-03-23T23:43:38", "url": "https://reddit.com/r/youtube/comments/1jid9v7/youtube_is_blatantly_censoring_any_comments_that/", "upvotes": 57, "comments_count": 46, "sentiment": "bullish", "engagement_score": 149.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}]