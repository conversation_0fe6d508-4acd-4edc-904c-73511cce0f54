[{"platform": "reddit", "post_id": "reddit_1k8uwpp", "title": "Looking for Google ads manager", "content": "Hey everyone \n\nI’m looking for Google ads manager for my locksmith campaigns \nI’ve been running my account for 8 months but I’m looking for someone to take over \n\nThe account is a bit meesy and at the beginning the tracking was incorrect \n\nMy marketing budget is 500 aud a day and I’m looking to pay max 600 aud a month for someone to manage it\n\nI’ve got a website already\n\nAlso I’m looking for someone that knows how to use with call rail so I’ll have tracking after the calls\nPls someone with experience in the filed \n\nI’m in aus ", "author": "Old_Negotiation_8498", "created_time": "2025-04-27T03:43:16", "url": "https://reddit.com/r/PPC/comments/1k8uwpp/looking_for_google_ads_manager/", "upvotes": 16, "comments_count": 55, "sentiment": "bullish", "engagement_score": 126.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k949kk", "title": "One Skill Nobody Talks About for Cloud + Python Beginners (But It Changes Everything)", "content": "Hey everyone 👋\n\nWhen I first started learning Python and AWS, everyone said the same thing:  \n*\"Learn EC2, S3, Lambda...\"*\n\nBut nobody told me about **the real game-changer**:\n\n➔ **Master APIs early.**\n\n**Why?**\n\n* Cloud services (AWS, Azure, GCP) are just APIs under the hood.\n* Python + APIs = full control over cloud resources with just a few lines of code.\n* You move from clicking buttons to automating real-world cloud tasks.\n\n**What i tried to do:**\n\n* Uploaded a file to S3 with Python.\n* Launched an EC2 instance using Python scripts.\n* Triggered a Lambda function automatically.\n\n**Fro**m m**y point of view If you know basic Python + API handling, you are already ahead of 80% of beginners.**\n\n**If you're starting out:**\n\n1. Learn how HTTP requests work (GET, POST, etc.).\n2. Use Python’s `requests` library to talk to public APIs.\n3. Then move into AWS with `boto3` — and start automating small tasks.\n\n**Bonus Tip:**  \nAutomate one tiny thing every week — even if it’s just uploading a file or reading data. You'll build serious cloud muscle.\n\nThinking to also share a few mini project ideas if anyone's interested!\n\nHow many of you are mixing Python + Cloud in your learning path?", "author": "yourclouddude", "created_time": "2025-04-27T13:43:19", "url": "https://reddit.com/r/cloudcomputing/comments/1k949kk/one_skill_nobody_talks_about_for_cloud_python/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k95m98", "title": "Dead Cat Bounce or <PERSON><PERSON> in Sentiment? SPY Net Options Sentiment Looking Bullish", "content": "Been keeping an eye on SPY lately after all the chop this month, and I pulled up the Net Options Sentiment (NOS) chart to see if anything actually changed under the hood.\n\n\n\nhttps://preview.redd.it/wr57921u3exe1.png?width=779&format=png&auto=webp&s=f9bb19c902d946d0a86762f9126da6f11220c87d\n\nChart - [Prospero.AI](http://Prospero.AI)\n\nHere’s what’s interesting:\n\n* Early April was pure pain. NOS was basically flatlined near zero while SPY nuked below 500. Options flow was super bearish or just nonexistent.\n* Around mid-April though, sentiment started turning up. First slow, then a pretty decent spike.\n* By April 22, NOS ripped through the “Bull Line” (around 40) and actually stayed up there for a few days.\n* SPY’s price action caught up too — it’s been grinding up toward 550–560 ever since.\n\n\n\nOn top of that, you’ve got:\n\n* Trump pausing some of the new tariffs (at least for now)\n* Tech earnings coming in strong (Google especially crushed)\n* Market sort of trying to price in a soft landing again\n* So basically, the sentiment + price combo is finally showing some real strength for the first time in weeks.\n* But (and it’s a big but), could just be a classic dead cat bounce too. We’ve seen fakeouts before.\n\n\n\nIf NOS starts rolling over from here, probably back to Chop City.\n\nIf it stays elevated or keeps pushing higher? Could actually be the start of a bigger leg up.\n\nI’m not full send bullish yet but definitely watching this pretty closely now.\n\nCurious if anyone else is seeing the same thing, dead cat or shift in sentiment?", "author": "TopFinanceTakes", "created_time": "2025-04-27T14:46:27", "url": "https://reddit.com/r/options/comments/1k95m98/dead_cat_bounce_or_shift_in_sentiment_spy_net/", "upvotes": 187, "comments_count": 149, "sentiment": "bullish", "engagement_score": 485.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k99no0", "title": "I went and paid way too much for a 5070 ti but it was worth it", "content": "Been waiting to upgrade my gpu for years but I wanted to be 100% sure it would be worth it for my use case which is video editing. Finally this gen of nvenc decoders/encoders supports 10bit 4.2.2 h.265/264.  This is absolutely friggin magical. I can’t believe how well it works- and the feature is still in beta on Adobe Premiere Pro.  I toyed with the idea of getting a Mac Studio but I enjoy building too much- admittedly over the years my build has gotten a wee bit stale and I’m still on AM4 and now it’s almost time to start building out PCs for my twin boys- but when AM6 drops- yea I’m making a dream machine. Still kinda disappointed that ASUS didn’t make ProArt boards for the 50 series. This thing came in at a whopping 1000 but if I’m being honest, it’s totally worth as this will save me so much time which would normally be wasted creating proxies and switching back and forth to color grade- I couldn’t be happier. The TUF series looks a lot nicer in person then I thought it would too- I’m finally playing Cyberpunk, <PERSON>nite looks so damned good and the Witcher 4 and GTA 6 - omg can’t wait.  This turned into a ramble but I’m just so happy lol- I’ve been slowly swapping parts in this build for two years and this was the last piece of the puzzle. ", "author": "JobEnvironmental4842", "created_time": "2025-04-27T17:38:52", "url": "https://reddit.com/r/nvidia/comments/1k99no0/i_went_and_paid_way_too_much_for_a_5070_ti_but_it/", "upvotes": 3, "comments_count": 62, "sentiment": "bearish", "engagement_score": 127.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k9gxym", "title": "Does ISP get to know about what I am searching on Google?", "content": "For example: If am typing in the Google search bar \"car\" and then hit enter for results, will the ISP get to know that I searched \"car\" in Google? ", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-04-27T22:57:10", "url": "https://reddit.com/r/privacy/comments/1k9gxym/does_isp_get_to_know_about_what_i_am_searching_on/", "upvotes": 207, "comments_count": 92, "sentiment": "neutral", "engagement_score": 391.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k9h1yu", "title": "Why Your Google Ads Are Burning Money (And How to Actually Make Them Work) From An Industry Veteran & Fellow Small Business Owner", "content": "If you’re a small business owner and you’ve tried running Google Ads to get leads, but ended up frustrated, bleeding money, and thinking “this doesn’t work” or “this is a scam”, you’re not alone.\n\nI manage Google Ads campaigns professionally and for my own small business (and even freelance on the side), and let me tell you: It’s not your fault. I've been doing paid search for over 10 years and I've worked on both small and large accounts (including everything from literally a barbershop down the street and a local plumbing business, to companies like Bloomingdale's, NFL, and Etsy).\n\nHere’s the brutal truth: Google makes it way too easy for small businesses to waste thousands of dollars without even realizing it. Here’s how it happens — and what you can do about it.\n\n⸻\n\n1. “Smart Campaigns” Are Not Smart\n\nIf you hit the “Easy Mode” setup that Google automatically funnels you through, you’re almost guaranteed to target the wrong people and lose money.\n\n- Your ads show for broad, irrelevant searches.\n- You’re paying $20–$50 per click for people who aren’t even looking for what you sell.\n- You have no control over the terms you’re showing up for.\n\nFix: You need to manually build campaigns in Expert Mode, with thoughtful keyword targeting.\n\n⸻\n\n2. Your Match Types Are Probably Screwed Up\n\nGoogle defaults most keywords to Broad Match — which is insanely wide. Also, no you are not “upgrading” your keywords to broad match. It’s not an “upgrade”; it’s a different match type.\n\nExample: If you sell “red sneakers” in Miami, you could be showing up for “maroon high heels” in NYC.\n\nFix: Use Exact Match or Phrase Match properly, and layer in negative keywords. Most accounts I audit have zero negative keywords — that’s like driving without brakes.\n\n⸻\n\n3. You’re Letting Google Pick Where Your Ads Show (and They Pick Badly)\n\nGoogle Ads includes Search, Display, YouTube, Gmail, Discovery — all lumped together by default.\n\nSearch is great. The rest… not so much for lead gen. Especially if you’re a small business just getting started with online advertising and you don’t have sophisticated measurement tools and methodologies in place.\n\nFix: Make sure you’re running Search Network Only campaigns if you want quality leads. Period.\n\n⸻\n\n4. You’re Optimizing for Clicks Instead of Customers\n\nGoogle will optimize for clicks if you let it — and clicks don’t pay your bills.\n\nFix: Set up proper conversion tracking (phone calls, form fills, etc.) and optimize for actual leads, not traffic. Ideally, optimize for actual customers and not just leads.\n\n⸻\n\n5. You’re Missing the Goldmine: Search Terms Data\n\nYour account has a secret weapon: The Search Terms Report shows exactly what people typed when they clicked your ad.\n\nMost business owners don’t even know this exists.\n\nFix: Check it weekly.\n\n- Add good searches as keywords.\n- Block bad searches with negatives\n\nThis alone can turn an unprofitable campaign profitable.\n\n⸻\n\n6. You’re Ignoring Auction Insights (And Flying Blind Against Competitors)\n\nImagine running a business but never checking what your competitors are doing. No idea what they charge, no idea how they market, no idea how big they are. You’d get eaten alive, right?\n\nThat’s exactly what happens when you ignore Auction Insights in Google Ads.\n\nAuction Insights shows you:\n\n- Who else is competing against you.\n- How often you’re beating them for top spots.\n- Whether someone bigger just jumped into your market with a pile of cash.\n\nIf you don’t check it, you’re basically in a boxing match — blindfolded — and wondering why you keep getting punched in the face.\n\nFix: Check Auction Insights every 1–2 weeks. If you see new aggressive competitors, tighten your targeting or tweak your bids. If you’re losing impression share to weaker players, it might be a quality issue (time to fix ad copy, landing page, or bidding strategy).\n\n⸻\nQuick Bonus Tips:\n\n- Geo-target tightly. Don’t run national if you only serve your metro area.\n\n- Write clear, no-BS ads. Focus on benefits, offers, and a strong CTA. Don’t try to push some fluffy brand message.\n\n- Test, but don’t thrash. Let campaigns run for a few days before making changes.\n\n⸻\n\nBottom Line:\n\nIf you fix even half of the mistakes above, you’ll probably see your cost per lead drop by 30–50% in a month.\n\nWhat’s the biggest frustration you’ve had with Google Ads? I’d love to hear it.", "author": "Commercial_Data3763", "created_time": "2025-04-27T23:02:18", "url": "https://reddit.com/r/Entrepreneur/comments/1k9h1yu/why_your_google_ads_are_burning_money_and_how_to/", "upvotes": 5, "comments_count": 3, "sentiment": "bearish", "engagement_score": 11.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k9hhx9", "title": "Advice on landing a data analyst job with certifications?", "content": "Hi, I have been reading a ton of posts about people wanting to get into data analytics, the input that most concerned me was someone saying that the Microsoft certified analyst or azure analyst certificates are useless because most fresh college grads in this field will have them. I know that certificates of completion will not land you jobs but I thought the certificates that require proctored exams were more valuable? I just don't want to be another certificate hoarder and then struggle to find a job. I completed google's data course and have some experience using R and Tableau, although not in work setting. There is so much info online and I'm not sure what to trust. So I want to ask the people working and recruiting in this field:\n\nWhat certifications, if any? Are still valued on a resume when applying for data analyst jobs?\n\nEven with the right certificates and a decent portfolio, do you think I stand a chance with a BS in biology to land a job? I feel pretty discouraged because this field has been so hyped up recently and I'd be competing with fresh grads with DS degrees and people with many yoe that got laid off.\n\nAny advice or input is appreciated, thanks.", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-04-27T23:24:03", "url": "https://reddit.com/r/analytics/comments/1k9hhx9/advice_on_landing_a_data_analyst_job_with/", "upvotes": 0, "comments_count": 25, "sentiment": "neutral", "engagement_score": 50.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}]