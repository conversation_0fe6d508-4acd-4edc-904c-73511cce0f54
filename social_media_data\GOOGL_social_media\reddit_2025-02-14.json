[{"platform": "reddit", "post_id": "reddit_1ioxilt", "title": "\"Search Google for this image\" just leads to a 413 error", "content": "At first I thought it was my old version of the app, but I tried on a different phone with a recent version and it gives me the same error. It says \"Your client issued a request that was too large.\"\n\nThere is a bypass, where if I just open the image in new tab, copy the URL, then paste it into Google, I can hit \"For matching images, try search by image\", then hit \"Exact matches\", and it'll show me the same results. It's inconvenient to have the extra steps.\n\nFor years, the \"Search Google for this image\" button only appears on incognito mode for some reason. Normally the button is replaced with \"Search with Google Lens\", which is unusable. All it does is open another app to show you ads. They need to fix the button for incognito mode.", "author": "YodasChick-O-Stick", "created_time": "2025-02-14T00:03:58", "url": "https://reddit.com/r/chrome/comments/1ioxilt/search_google_for_this_image_just_leads_to_a_413/", "upvotes": 22, "comments_count": 19, "sentiment": "neutral", "engagement_score": 60.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ip27pd", "title": "YouTube is officially 20 years old", "content": "", "author": "reddit33450", "created_time": "2025-02-14T04:11:49", "url": "https://reddit.com/r/youtube/comments/1ip27pd/youtube_is_officially_20_years_old/", "upvotes": 8442, "comments_count": 371, "sentiment": "neutral", "engagement_score": 9184.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ipa035", "title": "Google AI Chief tells employees company has ‘all the ingredients’ to hold AI lead over China’s DeepSeek", "content": "[https://www.cnbc.com/2025/02/14/google-ai-chief-tells-employees-deeseek-claims-are-exaggerated.html](https://www.cnbc.com/2025/02/14/google-ai-chief-tells-employees-deeseek-claims-are-exaggerated.html)", "author": "Next-Particular1476", "created_time": "2025-02-14T12:58:40", "url": "https://reddit.com/r/business/comments/1ipa035/google_ai_chief_tells_employees_company_has_all/", "upvotes": 34, "comments_count": 16, "sentiment": "neutral", "engagement_score": 66.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ipajgn", "title": "The blogs I write are not even in the top 100 on Google.", "content": "I have an e-commerce site for about more than 1 year. I am constantly doing seo optimisations, I get ideas from people who are dealing in this field, I do my own research. In the last few days, I started writing blogs on Shopify. These blogs are very long and seo-compatible, whether it is meta title and description, the photos I added and alt tags, I tried a lot.\n\nWhen I publish these blogs, I instantly index them via search console. But even though days have passed, they have 0 impressions and do not even enter the top 100 in google rankings in keywords. (site: ‘blog appears when I search with dork’)\n\nBlogs written by other people in my niche appear on google with expressions such as (‘1 day ago’, ‘2 days ago’). But it is really disappointing that I can't even get into the top 100.\n\nI even advertised one of my blogs with Google ADS, but how come I can't even get into the top 100. Let's say google is not acting very fast, how is the blog that these people shared 2 days ago in the first place.", "author": "iQeeDS", "created_time": "2025-02-14T13:27:17", "url": "https://reddit.com/r/SEO/comments/1ipajgn/the_blogs_i_write_are_not_even_in_the_top_100_on/", "upvotes": 16, "comments_count": 64, "sentiment": "bullish", "engagement_score": 144.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ipawld", "title": "My first week at pennystocks - full disclosure", "content": "It's been a rollercoaster, and sitting Friday out. Complete beginner here with 2 weeks trading experience. First week I invested a lump sum into the big stocks I like for long holding, and this week I decided to try penny stocks. Here is what happened:\n\n**CYN:** \\+$792.73\n\n**BHAT:** \\+$152.30\n\n**ADTX:** \\-$110.12\n\n**XAIR:** \\-$90.10\n\n**FBLG:** \\-$418.80\n\n**GNPX:** \\-$59.86\n\n**OCEA:** bag holding, current value -$256.88\n\n  \n**Total gain: +$9.28**\n\n  \nIt has been a sleepless and tiring week, but I learned a ton, and got away without a loss, for which I am grateful. I will share my obvious-for-many takeaways below:\n\n1. **Don't be greedy and take profits.** I got into CYN at $0.09 and was up $4.5k when it was at $0.3 and yet I let it ride until eventually sold at $0.14 for a measly $800 gain. \n\n2. **Don't get persuaded by the hype.** Seems crazy to me now not to sell at least some at +250% gain, but I got swayed by reddit/discord hype of CYN reaching $0.5 and beyond. \n\n3. **Have an exit strategy.** If I get lucky to get into such volatile stocks early again I'll be taking out half at +50%, and a quarter at 75% and letting the rest ride. Or I will be at least taking out initial investment from profits, and use better judgment. \n\n4. **Respect your stop losses.** So many times I've adjusted/cancelled my SLs with ADTX/OCEA only to regret it later. Minimise your losses and move on. Do not get married to a stock. **Set your stop loss to what you are prepared to lose without it affecting your mood.** \n\n5. **Do not go in big when volume is really low.** Got my generous FBLG stop loss sniped so easily at the open.\n\nand of course last, but not least:\n\n6. **Do your own due diligence.** Read about the company, fully read the news yourself, check their LinkedIn and see if employees are adequate and still there. If fellow redditors are hyping it a lot, look into their post history and see their previous decisions.\n\n  \nNotable mentions: \n\n**MGOL -** the first penny stock I came across. Spotted it early on. Was staring at $0.12 per share for hours, read the news about the merger, but didn't have the balls to go in. Missed opportunity.\n\n**PEV -** read the overnight news about incredible earnings. Set the limit buy for 2k shares at $0.35 (stock at $0.25 at the time) for the pre-market and went to bed. Woke up to see the order wasn't filled because Robinhood only trades this stock during market hours. Frustrating but lesson learned and migrating a lot of my portfolio onto another platform. \n\nMeanwhile, my investments from the first week into NVDA, BBAI and GOOGL are up almost 20%, so overall it is a green week. I am yet to determine whether it is worth the effort, time and peace of mind to do penny stocks daily, but I sure am grateful for the experience.\n\nHope it's been a fun read, and have a great weekend everyone!\n\n", "author": "Adventurous_Bad_8490", "created_time": "2025-02-14T13:46:05", "url": "https://reddit.com/r/pennystocks/comments/1ipawld/my_first_week_at_pennystocks_full_disclosure/", "upvotes": 187, "comments_count": 60, "sentiment": "bearish", "engagement_score": 307.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ipchov", "title": "Google AI Chief says company has ‘all the ingredients’ to beat China’s DeepSeek", "content": ">Google’s AI chief told employees that he’s not worried about China’s DeepSeek and said the search giant has superior artificial intelligence technology, according to audio of an all-hands meeting in Paris on Wednesday.\n\n>At the meeting, Alphabet CEO <PERSON><PERSON> read aloud a question about DeepSeek, the Chinese start-up lab that roiled U.S. markets recently, when its app shot to the top of the Apple’s App Store, supplanting ChatGPT. DeepSeek released a research paper last month claiming its AI model was trained at a fraction of the cost of other leading models.\n\n>The question, which was an AI summary of submissions from employees, asked “what lessons and implications” Google can glean from DeepSeek’s success as the company trains future models.\n\n>Google DeepMind CEO <PERSON><PERSON> was called on to provide the answer.\n\n[Full Article](https://stockflow.llc/articles/bfclj3qdoy8ih8fx693v1snu)", "author": "rightlibcapitalist", "created_time": "2025-02-14T15:01:40", "url": "https://reddit.com/r/wallstreetbets/comments/1ipchov/google_ai_chief_says_company_has_all_the/", "upvotes": 358, "comments_count": 198, "sentiment": "neutral", "engagement_score": 754.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ipgjpk", "title": "Rezolve AI  RZLV under the radar Voice AI Agent working with Google and Microsoft.", "content": "They have a strategic partnership with Google and Microsoft. Today they bought the company  GroupBy for 55 million, which currently drives over $30B in annual retail transactions. Rezolve is expected to generate 30 million in revenue from the acquisition.\n\nRezolve AI have been developing their AI model since 2016, training it on common merchandise literature. The CEO outlines his vision here\n\n[https://www.youtube.com/watch?v=fIUD5-ASxl4&t=2024s](https://www.youtube.com/watch?v=fIUD5-ASxl4&t=2024s)\n\nFull disclosure: I own 1007 shares of RZLV. Today I sold SOUN for 100 percent profit after they have been disowned by Nvidia (Nvidia sold their SOUN shares). I think SOUN is an amazing company with a great a future, but with it's high P/E ratio, and lack of Nvidia factor, it may go lower.", "author": "narayan77", "created_time": "2025-02-14T17:56:22", "url": "https://reddit.com/r/pennystocks/comments/1ipgjpk/rezolve_ai_rzlv_under_the_radar_voice_ai_agent/", "upvotes": 10, "comments_count": 8, "sentiment": "bullish", "engagement_score": 26.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ipi8dz", "title": "Google Gemini has some interesting thoughts on POTUS.", "content": "", "author": "RhubarbPi3", "created_time": "2025-02-14T19:06:59", "url": "https://reddit.com/r/google/comments/1ipi8dz/google_gemini_has_some_interesting_thoughts_on/", "upvotes": 860, "comments_count": 82, "sentiment": "neutral", "engagement_score": 1024.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}]