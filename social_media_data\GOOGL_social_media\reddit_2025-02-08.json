[{"platform": "reddit", "post_id": "reddit_1ikekmr", "title": "I've lost 8 Google reviews today. 3 in the last 30 minutes.", "content": "I'm not sure what's happening.  My Google reviews for my business are just disappearing.  It's not my app because I sear he'd my business on duckduckgo, without logging in, and they are gone.\n\nI also lost one on Sunday, so that's 9 in 6 days. \n\nEdit: So I'm only missing one review, and I did fire a client last week, so that could be why.  Another redditor counted their reviews, and they have more reviews than Google says they do. So I counted mine, and I have 8 more reviews than Google's tally. ", "author": "LoverOfGayContent", "created_time": "2025-02-08T04:04:46", "url": "https://reddit.com/r/smallbusiness/comments/1ikekmr/ive_lost_8_google_reviews_today_3_in_the_last_30/", "upvotes": 136, "comments_count": 233, "sentiment": "neutral", "engagement_score": 602.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ikhkub", "title": "Advise needed!! What you gonna do with 1m dollars", "content": "Hey Guys,\n\nI hope you’re all doing well and on your way to achieving FIRE soon!\n\nI love this community and have found some really great ideas while reading posts here. However, I recently found myself facing a tough decision, and I’d really appreciate any advice you guys can offer—whatever it is, I’ll take it!\n\n**A little background about me:**\n\nI live in Jersey City, NJ, and work in NYC. I’m 27 years old with two years of work experience. My wife and I have a combined income of about $8,000/month after taxes. We just had a baby in December 2024, and with high rent and bills, we’re finding it difficult to save money.\n\nLast month, my uncle, who had no children, passed away and left his inheritance to me to help support my family. I haven’t received the money yet, but it’s estimated to be around $1 million after taxes.\n\nTo be honest, I feel a huge sense of relief from financial stress, but at the same time, I feel a lot of pressure to use this money wisely. This is my uncle’s final gift, and I want to make the best of it.\n\nI’ve read plenty of stories about people who won the lottery or inherited money but ended up losing it all due to gambling, overspending, or simply not knowing what to do. I don’t want to be like that—for my family, my child, and my uncle’s legacy. Therefore I really love to have some advices from you to guide me.\n\n# My goal:\n\nI want to grow this **$1M into $4M–$5M** so it can fully cover my family’s expenses. Here’s my plan so far:\n\n1. **$200K–$300K** for a down payment on a house (renting is ridiculously expensive around NYC).\n2. **$300K** to invest in stocks (SPY, QQQ, NVDA, TSLA) and generate additional income through covered calls(low risk).\n3. **$400K** in a bank or bonds as a safety net (not sure if this is the best approach).\n4. Keep working, chasing a **higher salary and career growth** since I’m still young.\n5. I want to **reach FIRE as soon as possible**, but I’m not sure if my plan makes sense or if it will take forever to hit $5M.\n\nPeople always say, **\"The rich get richer,\"** but honestly, I have no clue how to make that happen.\n\nIf you were in my shoes, what do you think of my plan? Would you do anything differently? Any advice for someone young like me trying to make the most of this opportunity?\n\nThanks a lot!", "author": "Indiannevercheat", "created_time": "2025-02-08T07:05:55", "url": "https://reddit.com/r/Fire/comments/1ikhkub/advise_needed_what_you_gonna_do_with_1m_dollars/", "upvotes": 0, "comments_count": 38, "sentiment": "bullish", "engagement_score": 76.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ikp1hs", "title": "Google Maps was launched 20 years ago today.", "content": "", "author": "HelloitsWojan", "created_time": "2025-02-08T14:57:22", "url": "https://reddit.com/r/google/comments/1ikp1hs/google_maps_was_launched_20_years_ago_today/", "upvotes": 3985, "comments_count": 57, "sentiment": "neutral", "engagement_score": 4099.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ikwxc6", "title": "reCAPTCHA: 819 million hours of wasted human time and billions of dollars in Google profits", "content": "", "author": "waozen", "created_time": "2025-02-08T20:34:04", "url": "https://reddit.com/r/technology/comments/1ikwxc6/recaptcha_819_million_hours_of_wasted_human_time/", "upvotes": 38851, "comments_count": 917, "sentiment": "bullish", "engagement_score": 40685.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ikx811", "title": "Quit posting about google.", "content": "It’s not a value stock. 10% growth clip at 20x operating income is fair value. It’s 5% income yield. You can get that nearly risk free from fixed income/bonds.", "author": "Virtual_Seaweed7130", "created_time": "2025-02-08T20:47:01", "url": "https://reddit.com/r/ValueInvesting/comments/1ikx811/quit_posting_about_google/", "upvotes": 0, "comments_count": 32, "sentiment": "bullish", "engagement_score": 64.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1il0ajc", "title": "Does anyone have a Google sheet based file that can be used to instantly look up dividend yield at that very point of time?", "content": "In the past I had someone create a google sheet for me which allowed me to type couple of tickers such as SCHD JEPI JEPQ and it would display the yield at that time (during market hours of course - I can live with 20 mins delay). I think it was pulling dividend information from digrin or nasdaq. \n\nSomehow I lost that sheet. Does anyone have something similar?", "author": "siamrican", "created_time": "2025-02-08T23:02:53", "url": "https://reddit.com/r/dividends/comments/1il0ajc/does_anyone_have_a_google_sheet_based_file_that/", "upvotes": 3, "comments_count": 6, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}]