[{"platform": "reddit", "post_id": "reddit_1h5b0pi", "title": "Buying a house, is this a bad move?", "content": "My wife (26f, first year working) and I (25m, 4 years working) are considering buying our first house for $775,000; $25k down, $750k mortgage @ 6.5% for a monthly mortgage, including property tax and insurance, of ~$4,700.\n\nWe make $380k/year, have $200k in investments ($35k cash/$65k brokerage/$100k retirement), and $190k in student loan debt.\n\nWe budget our current expenses at ~$4,000/mo ($2,700 rent/$300 groceries/$225 gym/$775 fun; mostly date night, sporting events, concerts.), but we’re typically still carrying over ~$200-300. We max one retirement account, and put some towards cash savings, and the rest at the student loan debt.\n\nThis is a home that is brand new construction inside of a quiet, trending towards upper class neighborhood in our area, that is local (15-20min drive) to every thing we do. We would not be looking to move, would have plenty of space for kids (~3 years away), and is in a school district we like. The price is very competitive for the area, however, not many true 1:1 comps due to this being the only net new home within a 3-4 mile radius. Everything else has been there for >50 years.\n\nFinancially speaking, I think we should easily be able to afford this, but since we’ve never bought a house before I thought I’d get a second opinion: is this a crazy house to buy? What should we be considering?", "author": "whoopsserverer<PERSON>r", "created_time": "2024-12-03T00:53:33", "url": "https://reddit.com/r/FinancialPlanning/comments/1h5b0pi/buying_a_house_is_this_a_bad_move/", "upvotes": 9, "comments_count": 193, "sentiment": "bullish", "engagement_score": 395.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5b1g9", "title": "CNBC: Tesla CEO <PERSON><PERSON> loses bid to get $56 billion pay package reinstated", "content": "", "author": "twinbee", "created_time": "2024-12-03T00:54:28", "url": "https://reddit.com/r/elonmusk/comments/1h5b1g9/cnbc_tesla_ceo_elon_musk_loses_bid_to_get_56/", "upvotes": 608, "comments_count": 439, "sentiment": "neutral", "engagement_score": 1486.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5bue1", "title": "Tesla CEO <PERSON><PERSON> loses bid to get $56 billion pay package reinstated", "content": "", "author": "Constant_Falcon_2175", "created_time": "2024-12-03T01:32:53", "url": "https://reddit.com/r/finance/comments/1h5bue1/tesla_ceo_elon_musk_loses_bid_to_get_56_billion/", "upvotes": 1065, "comments_count": 114, "sentiment": "neutral", "engagement_score": 1293.0, "source_subreddit": "finance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5dunr", "title": "Tesla FSD still runs down children in tests contrary to recent intentionally deceptive Tesla PR videos of testing examples", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-03T03:14:09", "url": "https://reddit.com/r/SelfDrivingCars/comments/1h5dunr/tesla_fsd_still_runs_down_children_in_tests/", "upvotes": 0, "comments_count": 59, "sentiment": "neutral", "engagement_score": 118.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5hwx1", "title": "Tesla shuts down Cybertruck production for 3 days at critical time for the company", "content": "", "author": "wonderboy-75", "created_time": "2024-12-03T07:16:58", "url": "https://reddit.com/r/teslainvestorsclub/comments/1h5hwx1/tesla_shuts_down_cybertruck_production_for_3_days/", "upvotes": 5, "comments_count": 30, "sentiment": "neutral", "engagement_score": 65.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5llaq", "title": "Sharing my Penny Stocks in Focus: KULR, MYNZ, BOLT.CN, OPTT, KDLY and Their Potential", "content": "**1. KULR Technology Group Inc. (KULR)**\n\n* **Closing Price:** USD 1.41\n* **Market Cap:** USD 187.49 million\n* **52-Week Range:** USD 0.10 - USD 1.75\n* **Overview:** KULR saw a significant 21.55% surge today, closing at USD 1.41. This sharp increase reflects strong investor interest, potentially driven by market catalysts or positive developments. While the stock is nearing its 52-week high of USD 1.75, its speculative nature and high volatility make it a risky but potentially rewarding opportunity.\n\n**2. Mainz Biomed N.V. (MYNZ)**\n\n* **Closing Price:** USD 0.1992\n* **Market Cap:** USD 5.7 million\n* **52-Week Range:** USD 0.1800 - USD 54.8000\n* **Overview:** MYNZ's performance today reflects the market adjusting to its reverse stock split, which became effective today. Combined with ongoing support and collaborations with industry leaders like Thermo Fisher, the company is well-positioned for potential growth. These developments signal MYNZ's commitment to advancing its diagnostic solutions and may inspire investor confidence.\n\n**3. Bolt Metals Corp. (BOLT.CN)**\n\n* **Closing Price:** CAD 0.2250\n* **Market Cap:** CAD 7.061 million\n* **52-Week Range:** CAD 0.00 - CAD 0.32\n* **Overview:** Despite a 4.65% increase today, BOLT's low market cap and speculative nature make it an intriguing option for high-risk investors. The current price movement suggests mild upward momentum, but its penny stock status and negative earnings underscore the need for caution and thorough research.\n\n**4. Ocean Power Technologies, Inc. (OPTT)**\n\n* **Closing Price:** USD 0.7000\n* **Market Cap:** USD 88.362 million\n* **52-Week Range:** USD 0.12 - USD 0.8160\n* **Overview:** OPTT surged by 40% to close at USD 0.7000, reflecting significant market interest. However, the pre-market shows a sharp decline of 39.90%, indicating high volatility. The stock remains a speculative play, and its 52-week range highlights potential upside, but caution is advised given its penny stock status and price instability.\n\n**5. Kindly MD Inc. (KDLY)**\n\n* **Closing Price:** USD 1.14\n* **Market Cap:** USD 6.26 million\n* **52-Week Range:** USD 0.65 - USD 4.20\n* **Overview:** KDLY closed at USD 1.14, reflecting a slight 2.56% decline today. However, the pre-market surge of 14.91% suggests growing investor optimism. With its relatively low market cap and significant price movements, Kindly MD Inc. shows potential as an emerging growth stock. Investors may find the company's developments and strategic progress an exciting opportunity for further gains in the near term.\n\n*any thoughts about my watchlist?*", "author": "deleted", "created_time": "2024-12-03T11:46:06", "url": "https://reddit.com/r/pennystocks/comments/1h5llaq/sharing_my_penny_stocks_in_focus_kulr_mynz_boltcn/", "upvotes": 116, "comments_count": 101, "sentiment": "bullish", "engagement_score": 318.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5mqi7", "title": "Tesla Cybertuck Line Workers Told To Stay Home: ‘No Need To Report To Work’ | It looks like there’s a demand problem for the electric pickup that allegedly had millions of reservations before going on sale.", "content": "", "author": "chrisdh79", "created_time": "2024-12-03T12:54:16", "url": "https://reddit.com/r/technology/comments/1h5mqi7/tesla_cybertuck_line_workers_told_to_stay_home_no/", "upvotes": 19481, "comments_count": 2204, "sentiment": "neutral", "engagement_score": 23889.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5p8vr", "title": "Developing an AI Sales Bot with low code", "content": "Hi r/cloudcomputing,\n\nI’m seeking advice on how best to approach a AI project with given my constraints and goals. Here’s my situation:\n\n**Background:**\n\nI work as an \"AI engineer\" at a small manufacturing company (\\~105 employees), tasked with improving our sales processes. Specifically, I’ve been asked to develop an **AI sales bot** to assist our sales team in real-time by navigating complex product configurations.\n\nThe bot should allow salespeople to interact with it during calls, answer queries about product options, and provide additional guidance, like warnings or exceptions for certain configurations. My ultimate question is whether Azure services (like Cognitive Search) and current AI tools are sufficient to meet my needs internally, or if I should outsource this work.\n\n**Experience:**\n\n* Experience with **Azure Portal apps as AI Search**, **Logic Apps**, and building workflows in the Azure ecosystem.\n* Powershell\n* A bit Excel and Power Query\n* No experience with SQL databases.\n* Limited coding skills but managed to complete projects through ChatGPT.\n\n**Resources:**\n\n* I’m the only person working on this project—there’s no dedicated IT or development team to help.\n* Timeframe: I need to deliver a Minimum Viable Product (MVP) within 10 weeks and a fully developed system in 12-20 weeks.\n\n**Data Overview:**\n\n* I have 15-20 Excel tables with \\~300 rows and 6 columns each.\n* Data includes product configurations, dimensions, exceptions, and comments.\n* Examples:\n   * **Configuration Table:** Contains details like vehicle type, body type, dimensions, and special notes.\n   * **Exception Table:** Lists rules or warnings for specific configurations (e.g.,\"This requires an extra long body\").\n* The data is currently without normalization, and entirely in Excel.\n\n**What I Need the Bot to Do:**\n\n1. Allow salespeople to ask queries like: “I need a Citroen without a lift.”\n2. Recognize missing information and prompt follow-up questions (e.g., \"What body type: ' 'open platform,' or 'tilt platform'?\").\n3. Provide accurate configuration options (e.g., \"Length: 4200mm, Width: 2100mm\").\n4. Include any relevant exceptions (e.g., \"Special tow hitch required\").\n5. Be integrated into a user-friendly platform, such as Microsoft Outlook.\n\n**Proposed Approach:**\n\n1. **Azure AI Search + Blob Storage:**\n   * Upload Excel files to Azure Blob Storage.\n   * Use Cognitive Search to index data directly from Excel files.\n   * Build a search interface for querying the indexed data.\n   * Add exception handling by including relevant comments/notes in the index.\n2. **Azure Logic Apps for Business Logic:**\n   * Use Logic Apps to orchestrate workflows and apply rules based on search results.\n   * Handle prompts for missing information by adding logic to iterate queries.\n3. **Azure OpenAI Service for NLP:**\n   * Add natural language processing to interpret user queries and structure responses.\n4. **User Interface Options:**\n   * Integrate with Microsoft Outlook using Azure Bot Service or something like that.\n\n**Why This Approach?**\n\n* Leverages my Azure knowledge without requiring SQL knowledge.\n* Minimizes coding by using Azure AI Search, Logic Apps, and pre-built Azure services.\n* Avoids the complexity of normalizing data into a SQL database.\n\n**Challenges:**\n\n* **Complex Data:** The current Excel files are not normalized, and the data relationships are loose. I’m concerned about how well Azure AI Search can handle this without significant pre-processing.\n* **Iterative Queries:** Building a system to recognize missing information and prompt for clarification may require more advanced logic than Logic Apps can handle.\n* **Time Pressure:** I have approximately 10 weeks to deliver a working MVP. \n\n**My Questions:**\n\n1. Is relying on Azure Cognitive Search for structured queries (like filtering by body type and vehicle) a viable approach, or am I better off doing something else?\n2. Has anyone here successfully built a similar search solution in Azure or Microsoft Dataverse/Copilot Studio without SQL databases? Any tips or resources?\n3. Would integrating Azure OpenAI Service for natural language understanding complicate the MVP, or is it straightforward enough to include?\n4. For handling messy Excel data, are there better pre-processing tools or workflows you’d recommend in the Azure ecosystem or Microsoft in general?\n5. Would it be cheaper to outsource it to IT-experts or is it possible for me to do this on my own?\n\nAppreciate any guidance or suggestions on this! Thank you!", "author": "Historical_Air5674", "created_time": "2024-12-03T14:56:50", "url": "https://reddit.com/r/cloudcomputing/comments/1h5p8vr/developing_an_ai_sales_bot_with_low_code/", "upvotes": 1, "comments_count": 1, "sentiment": "bullish", "engagement_score": 3.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5q9ej", "title": "DIY...dont be scared", "content": "**UPDATE**: <PERSON><PERSON> Approved my net meter application and Swapped my meter 12/26 so I have my PTO\n\nJust passed my final county inspection on my install, 42 Jinko 425W panels, Sol-ark 15K, 3 EG4 indoor 14.3 KWh batteries. Currently using and storing with no grid sell until I get my PTO from the power company which is in progess.\n\nFor anybody on the fence of  DIY, just do it---break it into small pieces. planning, drawings, purchasing, permits etc.... It did take me since september but I was not focused on it full time.\n\nI'm am in the USA and for people that feel unsure of their mechanical/electrical ability you can find the same subcontractos that do work for the door knockers and other solar companies that have 1 employee. I found an installer that charged $75 a panel labor, that included getting all the wiring to the drop for the inverter.\n\nI used [https://www.opensolar.com/](https://www.opensolar.com/) for my initial panel layout after some research on solark and other sites for the size I wanted, then a company call [https://ecuip.com/](https://ecuip.com/) for the stamped engineering drawings to submit for my permit.\n\nI used the free racking BOM calculator from [https://www.ironridge.com/](https://www.ironridge.com/) to get my bill of material for racking. The other companies have simialr free tools.\n\nI used [https://www.greentechrenewables.com/](https://www.greentechrenewables.com/) , [https://www.soligent.net/](https://www.soligent.net/) , [https://www.cityelectricsupply.com](https://www.cityelectricsupply.com) , and [https://signaturesolar.com/](https://signaturesolar.com/) for components.\n\nSoligent will let you buy upto $5k a day without an account as a walk-in. I did not buy a DIY kit and saved a bit more and got exacly what I wanted.\n\nAll-in including the battery storage Im at roughly $1.4 per watt using 17.85KW before 30% federal credit. Lowest estimate for not DIY I had was $1.99/watt without storage after the credit. I have verifyed all my manufacturer warranties are valid even with DIY.\n\nfeel free to message if you need some pointers in the process to motivate you", "author": "rpm429", "created_time": "2024-12-03T15:39:46", "url": "https://reddit.com/r/solar/comments/1h5q9ej/diyd<PERSON>_be_scared/", "upvotes": 179, "comments_count": 81, "sentiment": "neutral", "engagement_score": 341.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5r27m", "title": "The robots took my job (but not how everyone thought it would)", "content": "I'm a content marketer (2017-present) and for the past couple of years since ChatGPT was released everyone has been panicking about generative AI taking jobs away from humans. I figured it would be another few years until AI could produce content as good as mine so I didn't worry too much.\n\nWhat I didn't anticipate was that AI would reduce global search volume and mess with algorithms to such an extent that the ROI on human-made content would become significantly diminished. In other words, AI didn't have to catch up to me in content quality to start screwing me over.\n\nDespite AI-generated content still being far below the caliber of articles I produce, it has eroded the ROI to such an extent that I've had my hours (and therefore earnings) cut in half at my current role. Let this serve as a warning that AI doesn't have to become as good at your job as you are to start making life difficult.\n\n**TL;DR:** AI isn't better than you (yet) but it's already widespread enough to disrupt your income.", "author": "JakeRedditYesterday", "created_time": "2024-12-03T16:13:44", "url": "https://reddit.com/r/marketing/comments/1h5r27m/the_robots_took_my_job_but_not_how_everyone/", "upvotes": 247, "comments_count": 79, "sentiment": "neutral", "engagement_score": 405.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5sckn", "title": "27M, $1.4M Net Worth, Unemployed – Seeking Advice on Next Steps", "content": "Hi everyone,\n\nI’m 27 years old with a current net worth of \\~$1.4M, mostly due to luck in stock gains. I left my SWE job semi-recently due to family-related matters and am currently unemployed but exploring my next steps. Here’s a breakdown of my finances:\n\n* Net Worth: $1.4M\n* Investments: $995K\n* $848K in Nvidia stock\n* $147K in other equities, ETFs, mutual funds, and cash\n* Savings: $254K\n* $238K in a 3.9% HYSA (including $70K earmarked for taxes due to equity sales)\n* $21K in CDs (4%-5%)\n* Retirement Accounts: $79.2K\n* $72K in a Fidelity 401(k)\n* $7.2K in a Schwab IRA\n* HSA: $4.15K (partially invested)\n\nI’m looking for advice on:\n\n1. Diversification: With Nvidia being a huge portion of my portfolio, what’s the best way to reduce my risk while minimizing taxes? Should I sell off shares in stages across tax years? I'm concerned about new risks and exposures with the incoming US administration.\n2. Generating Income: How can I leverage my portfolio to generate stable income while unemployed?\n3. Next Steps: Should I prioritize job hunting, or would it make sense to focus on managing my investments and pursuing financial independence at this stage?\n\nAny advice on asset allocation, tax strategies, or general next steps would be greatly appreciated!", "author": "Subject-Internal9266", "created_time": "2024-12-03T17:08:22", "url": "https://reddit.com/r/financialindependence/comments/1h5sckn/27m_14m_net_worth_unemployed_seeking_advice_on/", "upvotes": 0, "comments_count": 70, "sentiment": "bearish", "engagement_score": 140.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5w80u", "title": "I'm Out. Closed All Positions Except LEAPS.", "content": "Hotter CPI arrives on Dec. 11th, historically tax harvesting sell-off begins 8th-15th, we've ran to all-time highs and SPY could be in process of double top rejecting at $600, tariffs are regressive, volatility is far too low right now and put to call ratio is off the charts low.\n\nhttps://preview.redd.it/mve3ogziso4e1.png?width=883&format=png&auto=webp&s=e72be09c8258d242f77ac3426226d86e6732733d\n\nMade this post few weeks back \"85% Chance of Up Day Next Two Days\",\n\n[https://new.reddit.com/r/options/comments/1gs8jin/85\\_chance\\_upday\\_next\\_two\\_trading\\_sessions/](https://new.reddit.com/r/options/comments/1gs8jin/85_chance_upday_next_two_trading_sessions/)\n\nThe game plan then was to get out Dec. 6th, before the storm came. It seems this was so obvious though the smart players actually got on out Nov. 29th. So had the right idea, just wasn't ahead of the pack in reality because it was so obvious Dec. was bringing heat.\n\nGame Plan: Have call debt spreads on UVXY 12/13, have <PERSON><PERSON> puts 12/06 which will continue to open more aggressively as get closer to 11th-15th. These are hedges. Have closed all plays except LEAPS. Have sold calls against these LEAPS at .15-.20 delta 12/20 date, before <PERSON> <PERSON> begins. Am prepared for downside action, will reopen at better prices all my previously closed plays for Santa Rally at better prices or accept assignment on CC's.\n\nEDIT: I've made my own subreddit to continue sharing my progress and trades in the future, if interested in further updates can find more posts at r/OptionsInvesting thanks! ", "author": "breakyourteethnow", "created_time": "2024-12-03T19:47:02", "url": "https://reddit.com/r/options/comments/1h5w80u/im_out_closed_all_positions_except_leaps/", "upvotes": 166, "comments_count": 168, "sentiment": "bullish", "engagement_score": 502.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5wbby", "title": "Investment/ Savings Strategy Critique!", "content": "I am looking for some advice on my current portfolio and savings plan.\n\nI'm a fairly recent immigrant to this country (36m) so started my retirement/ savings from scratch in my early 30's. Currently live in a very HCOL city on the west coast, making around 100-150k/ year running my own business as an S corp, where I pay myself a W2 salary thats roughly 60% of my income, which i adjust regularly to stay ahead of taxes. The nature of my work is project based and freelance so every month is different. This year I am paying myself approx 6k gross per month on w2. The remainder of the income from the business and other 1099 income I usually manage to write off completely as I invest it back into the business.\n\nMy main expense is rent and utilities, other than that I have no other debt (paid off car, no student debt) aside from small credit card bills (under 3k that I pay off monthly- no interest)\n\n  \n**Current Accounts and Savings:**\n\n  \nPersonal Checking Account- $3k, keep this empty for monthly expenses only. Zero interest. I view this as a pass through account- salary in, bills out.\n\nBusiness Checking Account- $20k, this account fluctuates a lot depending on cash flow, I usually try to keep it around $20k- thinking I could/ should move at least 50% into an account that accrues some interest. \n\nSEP IRA (Deductible)- 25k, I max out the contribution yearly, 25% of my w2 income (started this in 2022) I divide that total amount by 12 and make 12 monthly DCA buys into VTI. That's it. Could possibly just start dropping a lump sum?\n\nROTH IRA- 18k, Maxing this out every year. $500 monthly DCA buys into VTI (50%) VOO (40%) and 10% cash.\n\nM1 Investment Account- $3k, started this is March of this year to start putting some money into some individual stocks and slightly more speculative plays. $100 weekly DCA buys into a basket of stocks MSTR, TSLA, NVDA, AMZN, AAPL, V, LLY  etc. I like to own individual names and regular DCA just keeps the guesswork out.\n\nRegular Brokerage Account- $1.5k, use this for the occasional weekly/ monthly option contract. Any gains over 1.5k just get emptied into the ROTH. I regularly generate 400-500 per month from this account, so most months i don't even need to fund my ROTH from my personal account.\n\nChase Investment Account- $14k, $400 monthly contribtions, this was a Chase Auto Invest account (mid risk) that yielded about 10%, so below avg market returns. Chase has ended this auto investment service so the money is 80% invested into various funds but the new contributions will just be cash unless i Manually buy new funds. As this has been underperforming the market, i'm thinking about splitting this up over a few of my other accounts to streamline.\n\n  \nCrypto- $20k, been buying $100 of BTC weekly for years. Occasionally add bigger lumps on big dips. Will continue this DCA strategy.\n\n  \nSo my currently contributions to all my accounts are :\n\n  \nSEP IRA- 1k monthly DCA into VTI  \nROTH IRA- $500 monthly DCA into VTI/ VOO  \nM1- $400 monthly DCA into various QQQ type stocks  \nChase Invest- $400 monthly DCA into money market  \nCrypto- $400 DCA into BTC  \n\n\nI don't really ever sell stocks or funds, unless they become seriously overweight, or I need the cash, which hasn't happened so far.\n\nI'm fairly happy with the amount that I am able to save and invest monthly, just wondering if anyone had advice on how I could streamline this approach, or if there are other account types or assets I should consider. The monthly DCA strategy just works for me because it means I really don't worry too much about market movements, I actually prefer to see prices go down so i can buy more cheaper. \n\nTLDR: Please critique my current investment strategy, are there any other account types i should be contributing to regularly to build wealth for the future and how could i manage my regular buys/ DCA/ contributions more effectively?", "author": "Broad-Whereas-1602", "created_time": "2024-12-03T19:50:48", "url": "https://reddit.com/r/personalfinance/comments/1h5wbby/investment_savings_strategy_critique/", "upvotes": 1, "comments_count": 2, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5xkjo", "title": "Is Mainz Biomed MYNZ the Next Big Opportunity in Biotech?", "content": "Biomed MYNZ is a biotech company making waves with its promising developments. The company has shown significant price movements recently, and many investors are starting to pay attention. With growing interest in the biotech sector, MYNZ's potential for growth could be substantial. Is it a good time to invest, or is it better to wait for a more favorable entry point? Would love to hear the thoughts of the community, especially those with experience in biotech stocks.  \n", "author": "deleted", "created_time": "2024-12-03T20:41:02", "url": "https://reddit.com/r/investing_discussion/comments/1h5xkjo/is_mainz_biomed_mynz_the_next_big_opportunity_in/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h5z9ix", "title": "Data Analytics Project Ideas", "content": "Hey everyone! I’m getting started in data analytics and looking to build some projects to sharpen my skills and grow my portfolio. I’m aiming for roles like Data Scientist or Business Analyst. I work with SQL, R, Excel, and Tableau. what are some project ideas that make good use of these tools? Also, what kinds of projects do you think stand out most to employers? Appreciate any advice!", "author": "perfjabe", "created_time": "2024-12-03T21:51:14", "url": "https://reddit.com/r/analytics/comments/1h5z9ix/data_analytics_project_ideas/", "upvotes": 1, "comments_count": 8, "sentiment": "neutral", "engagement_score": 17.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h60lx7", "title": "Should I Realize Capital Gains to Reset Cost Basis?", "content": "**TL;DR - Should I sell my entire portfolio and immediately by back into the market to raise my cost basis if I won't have to pay any taxes on the gains?**\n\n20M with roughly \\~$20k portfolio (normal brokerage account, not including my Roth account). Here is the situation:\n\nThis is going to be the last year where I will be in the lowest federal income tax bracket (<$11.6k).\n\nI have about $10k in unrealized gains with the majority of my holdings pushing 90% returns (NVDA, TSLA, CCL well over 150%). This year, I have already realized \\~$3.5k of long-term gains and \\~(-$300) in short-term losses.\n\nAlso, from my work-study job on campus, I will be bringing in roughly $8.5k this year.\n\nIn this portfolio, there is only around \\~$75 in *potential* short-term gains (if I sell the entire portfolio) leaving practically the full $10k of unrealized gains as *potential* long-term gains.\n\nMy current capital gains tax rate is 0% (<$47k).\n\nIs there any reason that I shouldn't realize all these gains and immediately buy back into the market so that I can raise my cost basis and basically save $10k of earnings that might be taxed in the future? I also plan on maxing the rest of my Roth contribution for the year (only need additional $2.3k to max) from the cash I will receive if I sell.\n\nThanks!\n\n", "author": "Present-Flower446", "created_time": "2024-12-03T22:47:23", "url": "https://reddit.com/r/personalfinance/comments/1h60lx7/should_i_realize_capital_gains_to_reset_cost_basis/", "upvotes": 0, "comments_count": 28, "sentiment": "bearish", "engagement_score": 56.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1h61vph", "title": "Is Performance Based Pay a scam to the employee?", "content": "Hi, I’ve been working at a plumbing company for about 7 years now. I have loved everything and been a staunch supporter of it up until a bit recently. I have always worked my ass off and been one of the most reliable guys for years, however I have recently run into issues trying to get a raise as I know I’m one of the lowest paid Journeymen in the state where as a second year JM I was making 23. I have finally gotten a $1 raise but had been asking for $2 as the average for a first year JM is $25 working outside of the local union.\nThis company that I work for has had performance pay in place since 2019. At first, I truly believed that this was a great pay program. I was able to make my hourly rate, some commission off of sales and make a small bonus off of revenue completed. Great. I’ve had several issues with my pay, but that’s not what I’m here to ask.\nThe company now wants to introduce a separate but new performance based program that is either going to be all hourly rate or all revenue based based on how the last two weeks were. So some weeks you may see a really good pay because a lot of revenue was completed or you might see a shitty paycheck because sales weren’t made so Work was not able to be performed. The issue is only so much work can be completed in a week, especially given very specific types of work.\nMy question is performance paste pay may be an incentive, but is it also a way for company owners to pay their employees less in general especially in the trades you are going to have slow times where not a lot of work is sold. so is this a way for companies to cut employee pay without it actually looking like they’re cutting your pay?", "author": "<PERSON><PERSON>", "created_time": "2024-12-03T23:42:47", "url": "https://reddit.com/r/business/comments/1h61vph/is_performance_based_pay_a_scam_to_the_employee/", "upvotes": 0, "comments_count": 6, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}]