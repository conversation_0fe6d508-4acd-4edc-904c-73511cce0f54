[{"platform": "reddit", "post_id": "reddit_1iz51zb", "title": "Google Starts Scanning All Your Photos—One Click Stops It", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-27T02:17:22", "url": "https://reddit.com/r/technology/comments/1iz51zb/google_starts_scanning_all_your_photosone_click/", "upvotes": 8085, "comments_count": 679, "sentiment": "neutral", "engagement_score": 9443.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1izfyyw", "title": "Game Ready & Studio Driver 572.60 FAQ/Discussion", "content": "# [GeForce Hotfix Driver Version 572.65](https://nvidia.custhelp.com/app/answers/detail/a_id/5631)\n\nGeForce Hotfix Display Driver version 572.65 is based on our latest Game Ready Driver 572.60.    \n   \nThis hotfix addresses the following issue:\n\n* PC may boot to a black screen when connected via DisplayPort with certain monitors \\[5131002\\]\n\n[**Click here**](https://international-gfe.download.nvidia.com/Windows/572.65hf/572.65-desktop-notebook-win10-win11-64bit-international-dch.hf.exe) to download the GeForce Hotfix display driver version 572.65 for Windows 10 x64 / Windows 11 x64.\n\nP.S. Hotfix driver will not show up in NVIDIA App or NVIDIA Driver Search. You MUST download it from [this article here](https://nvidia.custhelp.com/app/answers/detail/a_id/5631) or [directly here](https://international-gfe.download.nvidia.com/Windows/572.65hf/572.65-desktop-notebook-win10-win11-64bit-international-dch.hf.exe)\n\n\\----------------------\n\n# Game Ready Driver 572.60 has been released. Lots of bug fixes including black screen issues.\n\n# If you cannot find the driver in NVIDIA Website Search or not showing in NVIDIA App, please give it time to propagate.\n\n**Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/naraka-bladepoint-dlss-4-multi-frame-gen-game-ready-driver/)\n\n**Game Ready Driver Download Link**: [Link Here](https://us.download.nvidia.com/Windows/572.60/572.60-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n**Studio Driver Download Link**: [Link Here](https://us.download.nvidia.com/Windows/572.60/572.60-desktop-win10-win11-64bit-international-nsd-dch-whql.exe)\n\n**New feature and fixes in driver 572.60:**\n\n**Game Ready** \\- This new Game Ready Driver provides the best gaming experience for the latest new games supporting DLSS 4 technology including NARAKA BLADEPOINT. Further support for new titles leveraging DLSS technology includes Monster Hunter Wilds.\n\n**Applications** \\- The February NVIDIA Studio Driver provides optimal support for the latest new creative applications and updates including DLSS 4 updates for D5 Render, and Chaos Vantage, as well as enhanced Blackwell support within Maxon Redshift.\n\n**Fixed Gaming Bugs**\n\n* \\[SteamVR\\] Some apps may display stutter on GeForce RTX 50 series \\[5088118\\]\n\n**Fixed General Bugs**\n\n* \\[Adobe Substance 3D Sampler\\] Crashing at launch with R570 branch drivers \\[5083712\\]\n* \\[Adobe Substance 3D Painter\\] Texture corruption in baking results from GPU raytracing \\[5091781\\]\n* \\[VRay 6\\] Unexpected Low Performance on CUDA Vpath Tests for Blackwell GPUs \\[4915763\\]\n* \\[GeForce RTX 50 series\\] Various black screen issues \\[5088957\\] \\[5100062\\] \\[5089089\\]\n* Audio issues when GPU is connected via DisplayPort 1.4 w/ DSC at very high refresh rates \\[5104848\\]\n* Applications may display slight image corruption on pixelated 2D patterns \\[5071565\\]\n\n**Open Issues**\n\n* Changing state of \"Display GPU Activity Icon in Notification Area\" does not take effect until PC is rebooted \\[4995658\\]\n* PC may bugcheck IRQL NOT LESS OR EQUAL 0xa during gameplay with HDR enabled \\[5091576\\]\n\n**Additional Open Issues from** [GeForce Forums](https://www.nvidia.com/en-us/geforce/forums/user/15//558073/geforce-grd-57260-feedback-thread-released-22725/)\n\n* PC may boot to black screen on certain monitors when connected via DisplayPort \\[5131002\\] \n\n**Driver Downloads and Tools**\n\nDriver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n\nLatest Game Ready Driver: **572.60** WHQL\n\nLatest Studio Driver: **572.60** WHQL\n\nDDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n\nDDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n\n**DDU/WagnardSoft Patreon:** [**Link Here**](https://www.patreon.com/wagnardsoft)\n\nDocumentation: [Game Ready Driver 572.60 Release Notes](https://us.download.nvidia.com/Windows/572.60/572.60-win11-win10-release-notes.pdf) | [Studio Driver 572.60 Release Notes](https://us.download.nvidia.com/Windows/572.60/572.60-win10-win11-nsd-release-notes.pdf)\n\nNVIDIA Driver Forum for Feedback: [Link Here](https://www.nvidia.com/en-us/geforce/forums/user/15//558073/geforce-grd-57260-feedback-thread-released-22725/)\n\n**Submit driver feedback directly to NVIDIA**: [Link Here](https://forms.gle/kJ9Bqcaicvjb82SdA)\n\n[r/NVIDIA](https://new.reddit.com/r/NVIDIA/) Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\nHaving Issues with your driver? Read here!\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue**\n\nThere is only one real way for any of these problems to get solved, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows 10 or 11\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\nIf it still crashes, we have a few other troubleshooting steps but this is fairly involved and you should not do it if you do not feel comfortable. Proceed below at your own risk:\n\n* A lot of driver crashing is caused by Windows TDR issue. There is a huge post on GeForce forum about this [here](https://forums.geforce.com/default/topic/413110/the-nvlddmkm-error-what-is-it-an-fyi-for-those-seeing-this-issue/). This post dated back to 2009 (Thanks Microsoft) and it can affect both Nvidia and AMD cards.\n* Unfortunately this issue can be caused by many different things so it’s difficult to pin down. However, editing the [windows registry](https://www.reddit.com/r/battlefield_4/comments/1xzzn4/tdrdelay_10_fixed_my_crashes_since_last_patch/) might solve the problem.\n* Additionally, there is also a tool made by Wagnard (maker of DDU) that can be used to change this TDR value. [Download here](http://www.wagnardmobile.com/Tdr%20Manipulator/Tdr%20Manipulator%20v1.1.zip). Note that I have not personally tested this tool.\n\nIf you are still having issue at this point, visit [GeForce Forum for support](https://forums.geforce.com/default/board/33/geforce-drivers/) or contact your manufacturer for RMA.\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n\n**Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n* **What does the new Power Management option “Optimal Power” means? How does this differ from Adaptive?** *The new power management mode is related to what was said in the Geforce GTX 1080 keynote video. To further reduce power consumption while the computer is idle and nothing is changing on the screen, the driver will not make the GPU render a new frame; the driver will get the one (already rendered) frame from the framebuffer and output directly to monitor.*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations. The software will not be perfect and there will be issues for some people. For a more comprehensive list of open issues, please take a look at the Release Notes. Again, I encourage folks who installed the driver to post their experience here... good or bad.\n\n*Did you know NVIDIA has a Developer Program with 150+ free SDKs, state-of-the-art Deep Learning courses, certification, and access to expert help. Sound interesting?* [Learn more here](https://nvda.ws/3wCfH6X)*.*", "author": "Nestledrink", "created_time": "2025-02-27T13:37:44", "url": "https://reddit.com/r/nvidia/comments/1izfyyw/game_ready_studio_driver_57260_faqdiscussion/", "upvotes": 299, "comments_count": 1470, "sentiment": "bearish", "engagement_score": 3239.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1izgg7d", "title": "Google’s ‘Secret’ Update Scans All Your Photos—One Click Stops It", "content": "", "author": "ControlCAD", "created_time": "2025-02-27T14:01:12", "url": "https://reddit.com/r/google/comments/1izgg7d/googles_secret_update_scans_all_your_photosone/", "upvotes": 1402, "comments_count": 103, "sentiment": "neutral", "engagement_score": 1608.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1izn5xn", "title": "Tesla Sets Sights on Waymo, Uber in California Ride-Hail Bid", "content": "", "author": "walky<PERSON><PERSON><PERSON>", "created_time": "2025-02-27T18:46:09", "url": "https://reddit.com/r/SelfDrivingCars/comments/1izn5xn/tesla_sets_sights_on_waymo_uber_in_california/", "upvotes": 29, "comments_count": 165, "sentiment": "neutral", "engagement_score": 359.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1izoqfb", "title": "Should I do the Google Data Analytics Professional Certificate?", "content": "I’ve read a lot of posts about this saying that it’s not recognised or valued by employers which is fine.\n\nI’m doing an actual degree in Computer Science and engineering but won’t be done with that for another 3 years as I’m starting soon.\n\nBut in terms of data analysis, I have no idea what I’m doing. I know about excel, sql, pandas, powerBI and i don’t have any problem learning about these different tools but the application is the problem.\n\nI don’t know how a data analyst works and what they actually do with those tools and was wondering if this course would give me some direction where I could actually do the job of a data analyst and just improve specific skills rather than have the skills and not be able to use them", "author": "Ok_Reality_6072", "created_time": "2025-02-27T19:51:36", "url": "https://reddit.com/r/analytics/comments/1izoqfb/should_i_do_the_google_data_analytics/", "upvotes": 6, "comments_count": 22, "sentiment": "neutral", "engagement_score": 50.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1izuaqk", "title": "Is Google ($GOOGL) a great long-term buy after the recent drop", "content": "Google (GOOGL) has been dropping quite a bit recently, and I’m wondering if this presents a great long-term buying opportunity.\n\nDo you think it’s a solid investment for the next 10 years?\n\nI’d love to hear your thoughts on their business model, search dominance, AI potential, and any risks you see (e.g., competition, regulation, or ad revenue slowdown).\n\nWould you be buying at these levels or waiting for a better entry point?\n", "author": "biznisgod", "created_time": "2025-02-27T23:56:30", "url": "https://reddit.com/r/investing/comments/1izuaqk/is_google_googl_a_great_longterm_buy_after_the/", "upvotes": 327, "comments_count": 235, "sentiment": "bullish", "engagement_score": 797.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}]