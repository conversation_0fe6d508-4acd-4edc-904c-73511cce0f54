{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-02-28", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Why doesn't Teams offer the option of always muting the mic by default when joining a meeting?", "content": "It is highly frustrating that Microsoft Teams still does not offer the option to mute the microphone by default when joining a meeting. Many other products provide this functionality, yet despite years of user requests, Microsoft has not implemented what is clearly a simple and logical feature. \n\nhttps://answers.microsoft.com/en-us/msteams/forum/all/teams-mute-by-default-when-entering-a-meeting/979e5882-3ffa-45f2-8436-e996056d1d5d?page=18", "created_time": "2025-02-28T00:56:25", "platform": "reddit", "sentiment": "neutral", "engagement_score": 49.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "RevolutionStill4284", "url": "https://reddit.com/r/microsoft/comments/1izvizu/why_doesnt_teams_offer_the_option_of_always/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "AI/ML role interview process", "content": "Can anyone share your interview experience on AI/ML roles at MSFT?\n\nI just passed the initial screening, but my recruiter hasn’t responded to me yet regarding the interview process.\n\nI appreciate your help.", "created_time": "2025-02-28T07:58:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Blasphemer666", "url": "https://reddit.com/r/microsoft/comments/1j02nmz/aiml_role_interview_process/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Microsoft preparing to shut down Skype in May", "content": "", "created_time": "2025-02-28T08:42:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 342.0, "upvotes": 248, "num_comments": 0, "subreddit": "unknown", "author": "digidude23", "url": "https://reddit.com/r/microsoft/comments/1j039cn/microsoft_preparing_to_shut_down_skype_in_may/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Outlook e-mail ads are a security vulnerability", "content": "I'm mind blown by the fact that Microsoft has an obvious security vul in their Outlook app.\n\n  \nAre you guys braindead over there or what?\n\n  \nThis is clearly a security issue waiting to be exploit. Remove the ad as an e-mail function from your app please.", "created_time": "2025-02-28T15:45:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "EntertainmentOk356", "url": "https://reddit.com/r/microsoft/comments/1j0ao8d/outlook_email_ads_are_a_security_vulnerability/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Microsoft O365 GCC vs GCC High", "content": "We have a client who we are working with on CMMC level 2. We were going to move them to Microsoft GCC but they want to move to GCC high due to potentially having vendors sending ITAR data to them through email. We are having a hard time finding what the restrictions are when it comes to GCC High. One that I'm pretty sure of (But correct me if I'm wrong) is that any enterprise apps that you want to add have to be FedRAMP authorized or you wont be able to add them. This is a fairly big issue since we can't tie in a lot of security services like SIEM/SOC, Reporting, tickets, etc. But overall this limitation would make sense from a security perspective. If its not that case that would be a completely different story.\n\n  \nI know there's other limitations when it comes to stuff like sharing which I'm not overly concerned about. But it's all of the other potential limitations I'm hoping people can shed light on compared to what GCC or even a normal Microsoft tenant has that they don't where its a pain.", "created_time": "2025-02-28T20:23:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "BrandonSB2", "url": "https://reddit.com/r/microsoft/comments/1j0hc2q/microsoft_o365_gcc_vs_gcc_high/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Turning 30", "content": "I (30m, married to 29f, 2 yr old kiddo and another on the way) have been following this subreddit since 2018, but first-time posting my financials. I have learned so much from this thread and have come across so many helpful people along the way. I really appreciate you guys and gals!\n\nThis post is really just to reflect on my journey since starting my job career in 2018, and to show some of the great results that can be had from following the basic principles taught in this community. Like most people here, I started with the intentions of savings as much as possible, as fast as possible, so that I could retire early. It was an exciting endeavor, but as my life has progressed I've started to shift my mindset. I'm not as hyperfocused on a high savings rate and an early retirement date. I'm still young and I know that my life goals will continue to be fluid in the next 30 years, but I feel okay with the fact that I might work (at least part time) well into my 60s. I have eased up on my frugality and learned to enjoy spending, especially when it is philanthropic. My company has solid pay, good culture, and even better benefits. I'm very fortunate to have been hired on at such an early age (22), because the company fosters long-term careers. An example of that is the retirement pension which is uncommon today in corporate America.\n\nMy first year working I only contributed to my 401k up to the company match while I paid of 17k of student loans, but I was still able to max out my Roth IRA. Every year since, I've maxed out my 401k and Roth IRA/IRAs, and I even put money into my after-tax 401k once the before-tax limits were reached. At the end of every year, I submit an in-plan roth conversion to get that money into my Roth 401k. Any excess savings has gone towards brokerage or cash savings. All of my 401k investments are in low cost, US stock index funds. My non-401k investments are in VTI.\n\nMy wife stepped away from teaching when we got married in 2021, and started her own jewelry business from home. It was a slow start with a lot of investment up front that she slowly paid back, but it has started to bring in some decent income the last two years. She's done this while also caring for our son at home which is amazing!\n\nAs my wife and I continue to grow our family, my main focus will be to do what's best for them. I believe that staying on this path will give me many options to do just that in the not-so-distant future by being financially independent. Shout out to JL Collins!\n\nEnough chatter, here are the numbers:\n\n|Year|NW|Gross Household Income|Expenses|Savings Rate (Gross)|Comments|\n|:-|:-|:-|:-|:-|:-|\n|2018|0k|96k|47k|51%|Started career|\n|2019|42k|113k|62k|45%||\n|2020|115k|124k|60k|52%||\n|2021|211k|138k|80k|42%|Married/bought first home|\n|2022|375k|146k|83k|43%||\n|2023|377k|164k|93k|43%|First child born|\n|2024|541k|172k|101k|41%||\n|2025|716k|145k (estimate)|117k (estimate)|19% (estimate)|Changed jobs within company|\n\n**Assets:**\n\n* 401k: 348k\n   * Before-Tax: 202k\n   * After-Tax: 5k\n   * General/Company Match: 85k\n   * Roth: 4k\n   * Roth Conversion: 52k\n* My IRA: 74k\n* Wife's IRA: 33k\n* Brokerage: 93k\n* Cash: 6k\n* Vehicles: 25k\n* Home equity: 137k\n\nSide notes: Like mentioned before, I am vested in my company's retirement pension. Similar to social security, I do not include it in my calculations while in this early wealth accumulation phase. Also, we have two 529s open (one for our child, another for our neice). We plan to open another account when our second child is born. We do not count these towards our NW.\n\n**Debts:**\n\n* Mortgage #1: 176k\n   * 30-year fixed: 3.25% interest rate\n   * Monthly payment: $1391.12\n* Mortgage #2/Home Improvement Loan: 112k\n   * 10-year fixed: 7.125% interest rate\n   * Monthly payment: $1314.99\n\nThree big changes/impacts to our finances in 2025:\n\n1. **Lower income:** Changed jobs within my company. Went from hourly to salaried employee. Initially lower pay due to no more overtime, but I have better long-term growth potential/higher ceiling for salary growth.\n2. **Additional mortage:** Started 128k home addition/renovation project in January to update home and make more space for my wife's home business and our growing family. Adding 438 sqft to our exisitng 1300 sqft, updating kitchen, new floors, new windows, & painting exterior. Financed a 10-year secondary mortgage for this project which puts our combined mortgage payments at $2700/month.\n3. **Another family member:** Second child is being born in June. In preparation, we chose a lower deductible, higher premium health care plan for better child delivery coverage. This has resulted in lower net income on my paychecks. Once the baby is here that will bring additional expenses as well (food, diapers, etc.), but not nearly as much as our first since we still have all our baby care items and lots of hand-me-down clothes.\n\nLastly, please do not comment about me including our car values and home equity in our NW. The way I see it: Whether or not you include these is not worth debating. What's more important is that you stay consistent throughout your tracking.\n\nOverall, I am happy with how we've progressed. However, this year is going to be pretty tight financially with decreased income & increased expenses. We are taking our foot off the savings gas pedal quite a bit (not maxing out 401k or IRAs). Thankfully, we've laid a lot of ground work in prior years that will allow us to get through this period. I'm also expecting a significant salary increase in the next 1-2 years that will help us get our savings rate back on track.", "created_time": "2025-02-27T03:07:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 49.0, "upvotes": 19, "num_comments": 0, "subreddit": "unknown", "author": "Fireitsy_68168", "url": "https://reddit.com/r/financialindependence/comments/1iz61ip/turning_30/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Microsoft Phoenix Office- worth visiting ??", "content": "I’m traveling to this area and working remotely for one day, was wondering if phoenix office has anything fun (ex. Snacks ?) ", "created_time": "2025-02-27T05:27:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "sonny-land", "url": "https://reddit.com/r/microsoft/comments/1iz8je4/microsoft_phoenix_office_worth_visiting/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "What’s up with the Microsoft stock value ?", "content": "The prices plummeted any ideas out there.", "created_time": "2025-02-27T13:42:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 134.0, "upvotes": 48, "num_comments": 0, "subreddit": "unknown", "author": "Kdja4738", "url": "https://reddit.com/r/microsoft/comments/1izg2ch/whats_up_with_the_microsoft_stock_value/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Offer Timeline", "content": "Hello! \nI had my interview at Microsoft and things moved pretty fast, the recruiter reached out to me in 2 days to discuss salary and after 3 more days got a verbal offer. It’s been 2 days since.\nHow long is it going to take to get a written offer? I am in an EU country if that changes anything.", "created_time": "2025-02-27T14:37:27", "platform": "reddit", "sentiment": "bullish", "engagement_score": 15.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "IceEast", "url": "https://reddit.com/r/microsoft/comments/1izh7j0/offer_timeline/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Windows 11 pirates have a new ally — Microsoft Copilot", "content": "", "created_time": "2025-02-27T17:20:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 25.0, "upvotes": 19, "num_comments": 0, "subreddit": "unknown", "author": "Healthy_Block3036", "url": "https://reddit.com/r/microsoft/comments/1izl2vl/windows_11_pirates_have_a_new_ally_microsoft/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Microsoft pushes ahead with AI in gaming", "content": "", "created_time": "2025-02-27T19:34:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "factchecker01", "url": "https://reddit.com/r/microsoft/comments/1izoc3u/microsoft_pushes_ahead_with_ai_in_gaming/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Microsoft To Do - What is there that’s better?", "content": "I’ve started using To Do to manage my own workload at work but it’s awful.\n\nThe feature of dragging in flagged emails from Outlook is fantastic but they appear in a separate list from user generated tasks.\n\nThere seems no easy way to create an Eisenhower prioritisation matrix without categories. However is you this for projects or work streams as well it rapidly crashes.\n\nI can’t set start dates or create dependencies. Sub tasks are always hidden.\n\nIs there a better Microsoft product? I don’t need to manage complex projects or a large team. With me previous work we used Asana which was far superior.", "created_time": "2025-02-27T21:37:13", "platform": "reddit", "sentiment": "bearish", "engagement_score": 5.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Dadda_Green", "url": "https://reddit.com/r/microsoft/comments/1izr7oq/microsoft_to_do_what_is_there_thats_better/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Post-interview questions: how long to wait after loop interviews/sending thank-you notes", "content": "I had my four-person loop interview for an Azure CXP role last Thursday and Friday.  I had a few questions that I wasn't able to find recent answers on in the sub or elsewhere:\n\n1) Now that the interviews are over, how long should I wait before asking the recruiter if there's any feedback or followup?  I didn't get any followup from the recruiter or scheduler after I'd gotten scheduled, and the loop interview participants each said they'd be putting in their feedback over the following few days.  \n\nSome posts in the sub mentioned anywhere from three days to three weeks, so if I'm in for the long haul, no worries, I'd like to at least know what to expect.\n\n2) The manager in the first loop interview mentioned that I could always reach out if I had any questions or other info to add.  My only contact emails are for the recruiter and scheduler.  Would it be inappropriate if I tried sending a thank-you email to the manager by guessing their email address?  \n\nI already emailing them with firstnamelastname@microsoft, firstname.lastname, and firstinitiallastname, but got access denied bounces each time.  I'm guessing this is intended behavior on <PERSON>' side to stop people like me from spamming would-be hiring managers.  I got the vibe from the manager that they were fine with hearing from me again but if that's just a polite mention rather than an offer to stay in touch, I can back off, but if it's a good idea to go so far as to message them on Linkedin, so be it.  \n\nThe waiting is the hard part, but as long as I know indeed that it's a wait for a yes or no and not an indication of something going wrong, I can wait.", "created_time": "2025-02-27T21:44:41", "platform": "reddit", "sentiment": "bullish", "engagement_score": 12.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "MohnJaddenPowers", "url": "https://reddit.com/r/microsoft/comments/1izrdz6/postinterview_questions_how_long_to_wait_after/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Cheapest way to get Word & Excel after Microsoft 365’s Price Hike?", "content": "I’ve always preferred Microsoft Word and Excel for my hobbies, they’re straightforward, offline, and don’t force me to work in a browser window. But now that Microsoft 365 is getting even more expensive (the new monthly fee is more than I can comfortably afford), I’m starting to wonder if there’s a cheaper way to get Word and Excel without paying for the entire subscription suite. I don’t really need PowerPoint, OneNote, or Outlook; just the basics.\n\nI know some folks recommend Google Docs and Sheets, but I don’t want to rely solely on a web app. I’ve also heard about WPS Office as a potential replacement, which apparently has a free tier and decent compatibility with Word and Excel files. Has anyone tried it, and does it really stand up to what Microsoft offers for simple tasks and spreadsheets? Or are there other tricks to buy Office cheaply, like a perpetual license or a one-off purchase for just Word and Excel? I’d love to hear your suggestions before I jump into another subscription.\n\n", "created_time": "2025-02-27T22:55:56", "platform": "reddit", "sentiment": "bullish", "engagement_score": 180.0, "upvotes": 68, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1izt046/cheapest_way_to_get_word_excel_after_microsoft/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Question for a UK Microsoft Partner...", "content": "Are Microsoft Partners allowed to overcharge for 365 products (such as 365 Business Basic)? \n\nA colleague is trying to sort out their office systems and they've found that their IT provider is charging about 50% over the list price for 365 products, and has been for several years. \n\nWe'd like to recoup some of this, hence the question. If it does go against MS's policies, it would be useful to know for leverage...\n\nTIA", "created_time": "2025-02-27T23:18:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "trentsc", "url": "https://reddit.com/r/microsoft/comments/1izti22/question_for_a_uk_microsoft_partner/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Best time to get xbox in employee store?", "content": "Recently joined and I noticed the hardware with employee discount is not that great for xbox... just curious if there are special deals or other times where prices could go down by a lot? Even refurbished xboxs doesn't seem that much cheaper..", "created_time": "2025-02-26T02:45:19", "platform": "reddit", "sentiment": "neutral", "engagement_score": 26.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Lioil1", "url": "https://reddit.com/r/microsoft/comments/1iydal1/best_time_to_get_xbox_in_employee_store/", "ticker": "MSFT", "date": "2025-02-26"}, {"title": "Azure, Power BI, and QuickBooks Online", "content": "I do real estate development and use quickbooks online for accounting. For each project, I set up a new QBO company (...I have many). I'd like to aggregate the data from all entities as the chart of accounts and activity is largely the same company-to-company. Is the best solution to store the data in an Azure SQL database ? I'd also like to connect to my data to Power BI for visualization.\n\nSecondly, I have no experience with Azure or SQL. Is this something I can set up myself or will need to outsource to a software engineer? I've been trying to connect to Intuit's API endpoints from Power Query and that's been challenging itself.\n\nThanks!", "created_time": "2025-02-26T14:05:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Tekell09", "url": "https://reddit.com/r/microsoft/comments/1iyo9le/azure_power_bi_and_quickbooks_online/", "ticker": "MSFT", "date": "2025-02-26"}, {"title": "Time to hear back after final interviews", "content": "Hi,\n\nI had my final interviews for an SDE2 role (Azure Engineering) 2 weeks ago (Feb 10-11), and haven't heard back (Feb 26 today). I emailed the recruiter a week ago, and again on Feb 24, but no response. There were 2 headcounts for the same role, and my interviews went well, so it would be a bit surprising to get rejected.\n\nIt would be very helpful if someone could give some information about what this complete silence could mean, and what my chances are of getting an offer.\n\nI understand they may be interviewing other candidates, or keeping me as a backup, or having internal delays. No response to 2 follow-ups, and having waited for 2+ weeks feels excruciating though, and I would appreciate any insider information.\n\nThanks!\n\nUpdate: Heard back after 3 weeks and they've paused the hiring on this position.", "created_time": "2025-02-26T22:48:24", "platform": "reddit", "sentiment": "neutral", "engagement_score": 61.0, "upvotes": 23, "num_comments": 0, "subreddit": "unknown", "author": "earthling-banana", "url": "https://reddit.com/r/microsoft/comments/1iz0or0/time_to_hear_back_after_final_interviews/", "ticker": "MSFT", "date": "2025-02-26"}, {"title": "Australia: Get your old subscription price back!!", "content": "Here in Australia, (found out in this news article) Microsoft have hidden their old plan during a recent price increase. \nSimply log into your account and go to \"My Microsoft Account\", then \"Manage Microsoft 365...\", then \"Manage Subscription\", then click \"Cancel subscription\". This screen will then show (finally!!) that there is a Classic option without AI. So the exact same plan but at the old price. Select that and your price will reduce! Woohoo!", "created_time": "2025-02-25T15:26:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 9.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "LubberDownUnder", "url": "https://reddit.com/r/microsoft/comments/1ixxkjs/australia_get_your_old_subscription_price_back/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Microsoft CEO Admits That AI Is Generating Basically No Value | \"The real benchmark is: the world growing at 10 percent.\"", "content": "", "created_time": "2025-02-25T16:14:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 223.0, "upvotes": 171, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1ixyp8g/microsoft_ceo_admits_that_ai_is_generating/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Seeking Advice for Software Engineering IC5 Interview at Microsoft", "content": "Hello Reddit community,\n\nI have an upcoming interview for a software engineering IC5 position at Microsoft. I'm really excited about this opportunity and want to make sure I'm as prepared as possible.\n\nCould you please share any tips or advice on:\n\n* What technical skills and topics I should focus on\n* The types of questions I might encounter\n* General strategies for preparation\n\nAny insights or experiences from those who've been through the process would be greatly appreciated! Thank you!", "created_time": "2025-02-25T17:10:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "LuisNeuralnet", "url": "https://reddit.com/r/microsoft/comments/1iy030w/seeking_advice_for_software_engineering_ic5/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Announcing Free, Unlimited Access to Think Deeper and Voice | Microsoft Copilot Blog", "content": "", "created_time": "2025-02-25T17:33:38", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "McSnoo", "url": "https://reddit.com/r/microsoft/comments/1iy0n4k/announcing_free_unlimited_access_to_think_deeper/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Microsoft is testing a new limited ad-supported free desktop version of its Office suite", "content": "", "created_time": "2025-02-25T17:55:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 92.0, "upvotes": 44, "num_comments": 0, "subreddit": "unknown", "author": "pastamuente", "url": "https://reddit.com/r/microsoft/comments/1iy1639/microsoft_is_testing_a_new_limited_adsupported/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Microsoft Explore First-Year", "content": "Has anyone heard back? interview or offer? My application is still under review but it feels late", "created_time": "2025-02-25T18:01:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Limp_Attorney_7374", "url": "https://reddit.com/r/microsoft/comments/1iy1can/microsoft_explore_firstyear/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Xbox Studios head <PERSON> confirms 'Fable' is delayed to 2026, \"I know that's not maybe the news people want to hear.\"", "content": "We have been investigating some rumors that Fable was eyeing a delay to next year. Today on the Official Xbox Podcast, Microsoft's games studio lead confirmed Fable is slipping.", "created_time": "2025-02-25T18:28:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 20, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1iy202w/xbox_studios_head_craig_dunc<PERSON>_confirms_fable_is/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Is this Material Design on the Microsoft sign-in page?", "content": "Today I noticed that the Microsoft sign-in page (in this case, as redirected from the Xbox website) had a text field that apparently looks and behaves like Google's Material Design components. [Here's a screenshot](https://imgur.com/a/xBEtVSB). This seems like a break-away from their current usual strategy of moving towards all Fluent design components.", "created_time": "2025-02-25T19:31:28", "platform": "reddit", "sentiment": "neutral", "engagement_score": 5.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "SCP-iota", "url": "https://reddit.com/r/microsoft/comments/1iy3jqa/is_this_material_design_on_the_microsoft_signin/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Microsoft Backing Out of Expensive New Data Centers After Its CEO Expressed Doubt About AI Value", "content": "", "created_time": "2025-02-25T19:50:19", "platform": "reddit", "sentiment": "neutral", "engagement_score": 110.0, "upvotes": 66, "num_comments": 0, "subreddit": "unknown", "author": "anandan03", "url": "https://reddit.com/r/microsoft/comments/1iy405r/microsoft_backing_out_of_expensive_new_data/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Microsoft Summer 2025 Intern Roommate", "content": "Hi! Looking for a female roommate to sublease a 2B/2Br apartment in north Capitol Hill this summer. Rent would be ~ $1500 each after utilities. Place is unfurnished but next to public transportation and Microsoft connector bus.", "created_time": "2025-02-25T22:56:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Adorable_Equal_2383", "url": "https://reddit.com/r/microsoft/comments/1iy8gm6/microsoft_summer_2025_intern_roommate/", "ticker": "MSFT", "date": "2025-02-25"}, {"title": "Just Accepted a Microsoft Internship Offer and I am having a question about fitness benefit?", "content": "The offer claims that I am eligible for a fully paid membership to a health club. I will have the choice to enroll in one of the Microsoft approved health clubs during the first 30 days of the internship. **What are the Microsoft approved Health Clubs?**\n\nI picked the 10K housing lump sum and will live in a house near downtown Bellevue. I’m a big weight lifter, so I’m looking for a gym that’s good for that. It seems like Life Time Fitness and Tiger Gate Gym are the only options for me. I’m leaning towards Life Time Fitness, but I’m not sure if membership can be covered.\n\nI know most Microsoft employees go to ProClub, which is near the Redmond campus. I also know that FTEs have perks+. What’s the fitness benefit like for interns? Could I get reimbursement for membership at Life Time Fitness?", "created_time": "2025-02-24T00:05:09", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "CorrectMulberry3914", "url": "https://reddit.com/r/microsoft/comments/1iwok7u/just_accepted_a_microsoft_internship_offer_and_i/", "ticker": "MSFT", "date": "2025-02-24"}, {"title": "Microsoft Dropped Some AI Data Center Leases, TD <PERSON><PERSON>", "content": "", "created_time": "2025-02-24T15:59:33", "platform": "reddit", "sentiment": "bearish", "engagement_score": 114.0, "upvotes": 102, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1ix5e54/microsoft_dropped_some_ai_data_center_leases_td/", "ticker": "MSFT", "date": "2025-02-24"}, {"title": "Aight guys, this started as a meme but it doesn't clear up further down the rabbit hole: I have a file to share, do I use teams, one drive, or Sharepoint, and where does exchange come into play here?", "content": "If you share something in one drive, it opens in Sharepoint, which can be done inside teams. Our whole office is confused lol ", "created_time": "2025-02-24T16:12:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 38.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "ParticularLimeade", "url": "https://reddit.com/r/microsoft/comments/1ix5pui/aight_guys_this_started_as_a_meme_but_it_doesnt/", "ticker": "MSFT", "date": "2025-02-24"}, {"title": "Xbox Series X Sales Halt in Brazil Sparks Debate Over Microsoft’s Hardware Future", "content": "", "created_time": "2025-02-23T08:42:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 37.0, "upvotes": 35, "num_comments": 0, "subreddit": "unknown", "author": "Zombotic69", "url": "https://reddit.com/r/microsoft/comments/1iw5rz1/xbox_series_x_sales_halt_in_brazil_sparks_debate/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Should I switch from Apple ecosystem to Windows laptop?", "content": "I’ve been using Apple products for as long as I can remember. Right now, I have a MacBook, iPhone, iPad, and AirPods. However, I’m considering replacing my MacBook with a gaming laptop to be able to play games and have more flexibility in software development. That said, I’m so used to the Apple ecosystem that the thought of switching feels overwhelming.\n\nIdeally, I’d like to have both a MacBook and a gaming laptop, but I don’t have an established setup yet and don’t want to allocate that much budget. I also considered getting a PS5, but since the games I play the most are LoL and HOI4, it wouldn’t really serve my needs.\n\nMy question is, has anyone made a similar switch? If so, what was your experience like? I think the features I would miss the most are AirPods' seamless switching and AirDrop. Other than that, I actually prefer Windows over macOS.\n\nI’ve been looking at the **Asus Zephyrus** since its design quality is close to the MacBook. But I’m unsure whether I’d regret switching because, for a non-gaming daily user, MacBooks are truly amazing devices. Still, as a student living alone abroad, I feel like I’d lose my mind if I couldn’t play games 😁.\n\nOne last thing to note: I’ll be studying **Computer Science**. I believe macOS would be sufficient, but would using Windows give me more flexibility as a developer?", "created_time": "2025-02-23T16:51:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 96.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Pluxy01", "url": "https://reddit.com/r/microsoft/comments/1iwehsu/should_i_switch_from_apple_ecosystem_to_windows/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Separate Calendar App", "content": "Hi!  I’m just curious, am I the only one who would like to see a calendar app that is actually separate from Outlook?  The Outlook calendar is what I use for my entire life, just to make it easy, and I guess I would love to skip the seeing my email first or going through Teams. Am I the only one?  Is there a functionality that I’m missing?  I’m fairly adept at Microsoft but certainly not an expert. ", "created_time": "2025-02-23T17:04:33", "platform": "reddit", "sentiment": "neutral", "engagement_score": 17.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "v<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1iwetdm/separate_calendar_app/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "With the upcoming Microsoft 365 price increase from roughly 70 dollars to 100 dollars, you're still able to keep the old subscription. Here's how:", "content": "Go to: [account.microsoft.com/services/microsoft365](http://account.microsoft.com/services/microsoft365)\n\nUnder Manage Subscription, select Cancel Subscription\n\nYou'll then have the option to switch back to your original plan, aka \"Microsoft 365 Personal Classic\"— without all the AI stuff Microsoft is pushing down your throat.\n\nPersonally I really don't need these kinda features, figured there might be more people that don't know about this.\n\nAfter switching back it'll charge you on your usual billing date.\n\nHope this helps you out!\n\n  \n\n\nedit: removed 'www.' from the link, which caused it not to work", "created_time": "2025-02-23T17:39:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 126.0, "upvotes": 86, "num_comments": 0, "subreddit": "unknown", "author": "RedAceBeetle", "url": "https://reddit.com/r/microsoft/comments/1iwfnbe/with_the_upcoming_microsoft_365_price_increase/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Google Cloud's quantum-safe encryption contrasts with Microsoft's advances", "content": "\n**Google Cloud is making strides in encryption safety with its quantum-safe digital signatures.** This recent update to the Cloud Key Management Service (Cloud KMS) addresses the imminent threats posed by quantum computing. As Microsoft also progresses in this arena with its Majorana 1 chip, the competition heats up in securing digital infrastructures.\n\nOrganizations leveraging cloud solutions must recognize the rapid advancements in cybersecurity technologies that companies like Google and Microsoft are introducing. Both tech giants are prioritizing encryption methods capable of resisting quantum attacks, highlighting an essential transition for businesses handling sensitive data. As users begin testing these new features, the focus remains on refining methods to combat emerging cyber threats effectively.\n\n- Google introduces quantum-safe digital signatures in Cloud KMS.\n\n- Microsoft advancing its own quantum computing solutions.\n\n- Both companies emphasize encryption's importance in cybersecurity.\n\n- Users encouraged to integrate new features seamlessly.\n\n[(View Details on PwnHub)](https://www.reddit.com/r/pwnhub/comments/1iwhlut/google_cloud_enhances_security_with_quantumsafe/)\n        ", "created_time": "2025-02-23T19:04:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON>-<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1iwho4a/google_clouds_quantumsafe_encryption_contrasts/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Can we bring back Microsoft assistant characters instead of the search bar?", "content": "", "created_time": "2025-02-23T19:04:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "laced1", "url": "https://reddit.com/r/microsoft/comments/1iwhoar/can_we_bring_back_microsoft_assistant_characters/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Copilot Pages, OneNote, Loop…where should I store my notes and tasks?", "content": "Microsoft always seems to release products which competes with already existing services. They are not overlapping 100% but they do it enough to confuse users.\n\nJust to give you some examples, we have Planner and ToDo, Loop and OneNote… and now Copilot Pages.\nThere might be others also but let’s focus on these.\n\nI started using Copilot more frequently and really like how integrated it is with Teams, Outlook and Sharepoint. Teams transcriptions with AI generated meeting notes is awesome.\n\nHowever, I am trying to find a good place to store all notes and tasks but I am so confused.\n\nWhat is the use cases for Copilot Pages vs Loop?\nIs Loop the future for storing notes or is OneNote still the best?\nAll the follow up tasks created, should I store them in ToDo or Planner?\n\nMy todos created in ToDo app seems to show up in Planner anyway, should I just use that one instead?\nAnd <PERSON> is the so called ”Notion killer” (or was supposed to be), is that the future for notes or should I use OneNote?\n\nI feel lost, how do you handle your notes and tasks in a M365 environment? \n", "created_time": "2025-02-22T20:05:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 108.0, "upvotes": 70, "num_comments": 0, "subreddit": "unknown", "author": "EN-D3R", "url": "https://reddit.com/r/microsoft/comments/1ivrq0f/copilot_pages_onenote_loopwhere_should_i_store_my/", "ticker": "MSFT", "date": "2025-02-22"}, {"title": "My email address is dead to Microsoft due to failed Authenticator migration", "content": "I had been a reasonably happy user of Microsoft services for many years. I had Microsoft 365 and Visual Studio subscriptions. Some time ago something convinced me to add two-factor authentication to my login and that meant installing Microsoft Authenticator on my Google Pixel 6 phone. A few months ago, I replaced my phone with a brand new Google Pixel 9. As far as I know, I installed Authenticator on my new phone and everything was fine. Then I wiped my old phone and sent it in for recycling. \n\nA little bit later I found that Authenticator no longer did any authenticating. When I tried logging in via my PC, it said to watch for Authenticator to display a code but the code never came. I tried resetting my password and probably a few other things but I always ended up in a doom loop where it asked for a code that I could not supply.\n\nAfter searching online for help, I finally ended up in Microsoft support chat. I spent a few hours chatting with a representative but eventually it became clear that somehow my authentication credentials had not transferred properly from my old phone to my new phone. I was so naive to think that this just meant that I had to authenticate myself the hard way, by giving answers to secret questions, or sending in my picture id, or perhaps even showing up in person at some facility. Imagine my dismay when I was told that there is no backup authentication process. It is the end of the line for my email address at Microsoft. Perhaps if I faked my own death and got a court order to release the account to my wife, then they might help me. Other than that, I am screwed.\n\nI lost access to all my old calendar, contact, and email data. I had to get Microsoft to cancel all autopayments for my subscriptions. What a total lack of support! It seems to me there ought to be a law requiring a backup authentication method for all login procedures.", "created_time": "2025-02-22T20:58:35", "platform": "reddit", "sentiment": "bullish", "engagement_score": 56.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "PaulTopping", "url": "https://reddit.com/r/microsoft/comments/1ivswn9/my_email_address_is_dead_to_microsoft_due_to/", "ticker": "MSFT", "date": "2025-02-22"}, {"title": "Microsoft SE initial Phone call", "content": "Microsoft SE initial Phone call with recruiter what kind of questions should I expect?", "created_time": "2025-02-22T21:51:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "InevitableEye3955", "url": "https://reddit.com/r/microsoft/comments/1ivu3dj/microsoft_se_initial_phone_call/", "ticker": "MSFT", "date": "2025-02-22"}, {"title": "I got an internship with Microsoft, what should I expect?", "content": "I recently accepted an offer from Microsoft for a Product Design internship and have no clue what to expect. I will be at Microsoft HQ in Redmond Washington, and the offer letter details most of the logistical information. I am curious though, anything I should consider or any tips for getting the most out of the internship? I have worked at a mid-scale company before but this is the first time moving into  \"big tech.\" Any thoughts or feedback would be appreciated!", "created_time": "2025-02-21T03:35:01", "platform": "reddit", "sentiment": "neutral", "engagement_score": 64.0, "upvotes": 14, "num_comments": 0, "subreddit": "unknown", "author": "Desperate_Rest2814", "url": "https://reddit.com/r/microsoft/comments/1iuhgw9/i_got_an_internship_with_microsoft_what_should_i/", "ticker": "MSFT", "date": "2025-02-21"}, {"title": "Microsoft CEO says there is an 'overbuild' of AI systems, dismisses AGI milestones as show of progress | Nadella suggested AI progress should be measured by economic growth.", "content": "", "created_time": "2025-02-21T14:47:24", "platform": "reddit", "sentiment": "bullish", "engagement_score": 347.0, "upvotes": 261, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1iuskrj/microsoft_ceo_says_there_is_an_overbuild_of_ai/", "ticker": "MSFT", "date": "2025-02-21"}, {"title": "Visit MS Campus redmond as External/Guest. Tips and tricks?", "content": "In a few weeks i will be heading to the MS Campus in redmond for a week. I’ve never been here before. Have you got some advise on things we can do here during free time? Things i should visit? Things i should know?\n\nQuestions i have right now:\n\n•\t⁠Is it allowed to walk around the campus in the evening? What buildings can you go in?\n\nIs it allowed to just walk into random buildings?\n\n•\t⁠Is it allowed to enter a gym or join people who are doing sports on the Commons soccer field? Is it strange to ask them to join?\n\n•\t⁠Is Microsoft Commons open during the evening?\n\n•\t⁠Are the campus transport busses active during the evening? Can you just wave to the driver and get in? Or do i have to wait for a bus on a specific bus stop?\n\nAny other things i should know? :)\n\nThanks!", "created_time": "2025-02-21T22:42:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "OneSwordfish6949", "url": "https://reddit.com/r/microsoft/comments/1iv3yx7/visit_ms_campus_redmond_as_externalguest_tips_and/", "ticker": "MSFT", "date": "2025-02-21"}], "metadata": {"timestamp": "2025-07-06T20:09:26.701929", "end_date": "2025-02-28", "days_back": 7, "successful_dates": ["2025-02-28", "2025-02-27", "2025-02-26", "2025-02-25", "2025-02-24", "2025-02-23", "2025-02-22", "2025-02-21"], "failed_dates": [], "source": "local"}}}}