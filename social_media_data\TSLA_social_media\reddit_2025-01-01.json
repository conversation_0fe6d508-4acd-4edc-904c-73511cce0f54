[{"platform": "reddit", "post_id": "reddit_1hqslqg", "title": "I interviewed for this company as a Software Engineer when I was 13 and got rejected, and now I made $3.5k off of my own SaaS at 15", "content": "Hey everyone! I applied for this software engineering job at this one company and they were hiring interns and I thought I would apply. The position was unpaid, so I thought that I would get some experience and get ahead of the competition early as I was only 13 at the time. They reached back to me after looking at my skills on my GitHub and I scheduled an interview time.\n\nAt the time, I was pretty skilled at coding, I knew numerous frontend and backend stacks and made a ton of web applications for projects, like the MERN stack and I also knew how to code in Java with Java Spring Boot and React.js for the frontend as well. \n\nHere are all of the coding skills I knew related at the time:  \n\\- Python (a lot of random libraries and some web scraping tools like Selenium and Beautiful Soup)  \n\\- Javascript, HTML, CSS  \n\\- MERN (MongoDB, Express, React, and Node)  \n\\- Also knew Java and Java Spring Boot to make full stack apps\n\nThe company itself basically handles and schedules hair salon appointments and connects you with barbers, and a dashboard for hair stylists and hair salon owners etc, I'm not too sure what else they did. \n\nAnyway, when it was time for the interview (it was at 6 am before I had to go to school and I was in 8th grade at the time), they asked me questions and the interviewer was very surprised that I knew so much and that I could code as well as some of the other applicants. But....he cut off the interview after 5 minutes and he was asking me generic questions like what school I went to and what my favourite subject was....so I knew that I basically had no shot.\n\nAfter a few days after the interview, my parents were proud and thought I had secured it, but I knew that I wasn't getting in. Well, surprise surprise I got rejected and I wanted to know why. They...didn't even contact me back. Probably because I was too young or they were coding with a stack I didn't know at the time. \n\nI also didn't know how to make an actual SaaS or any other type of application except for, well a inventory manager. That's about it. \n\nI was pretty pissed I didn't get in, because I worked this hard and faced failure on something I couldn't control, so the motivation was through the roof. I kept on coding and learnt new stacks, and gained a lot of experience.   \n  \nI then started coding my first few projects, 8 of them in fact, for around 2 years. All of them failed. Not a single dollar was made. I honestly was going to quit coding after doing it for so long, spending hours everyday and not focusing on school work, but finally. I had one more shot, and I made my first paid SaaS application called BigIdeasDB which was made around 2 months ago and got me 3.5k dollars to this date.\n\nI am pretty happy on where I am and I am grinding more than ever, marketing and improving my SaaS from the community's feedback.   \n  \nThere is still a lot to improve on and so much more to be done, and I hope that the new year is even better for my coding journey and I make even more money!\n\nTL;DR\n\nGot rejected from an unpaid internship, and built my own SaaS making me $3.5k.\n\n[REJECTION LETTER](https://preview.redd.it/95x9wn4p9aae1.png?width=3340&format=png&auto=webp&s=b478eeb14b96b22c22a155a4a68b3148b2a96207)\n\n[Stripe Dashboard for BigIdeasDB](https://preview.redd.it/fptfv1uu9aae1.png?width=1668&format=png&auto=webp&s=74aa649b43e76d3fa2a06aca9bed72d716bf4446)\n\n  \n", "author": "Many_Breadfruit9359", "created_time": "2025-01-01T01:17:35", "url": "https://reddit.com/r/Entrepreneur/comments/1hqslqg/i_interviewed_for_this_company_as_a_software/", "upvotes": 0, "comments_count": 10, "sentiment": "bullish", "engagement_score": 20.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hqukuy", "title": "Unpopular Opinion: GOOGL's search business is untouchable", "content": "I remember reading a while back that AI will destroy Google's search engine (and with that, the ads business). However, I find that Google's latest generative AI search - the AI summary you get on top of the search results, has been giving me good results lately. I've been studying for my AWS exam and I find myself browsing through the documentation less and less thanks to the AI summary.\n\nCouple that with its unbeatable search algorithm (which is no doubt itself augmented by AI already), I have a hard time believing that AI would disrupt Google's search business anytime soon.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-01T03:20:02", "url": "https://reddit.com/r/ValueInvesting/comments/1hqukuy/unpopular_opinion_googls_search_business_is/", "upvotes": 363, "comments_count": 312, "sentiment": "neutral", "engagement_score": 987.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hqulws", "title": "NBA employee - <PERSON> - criticizes NBA analytics for weakening the \"product\"", "content": "Interesting perspective from <PERSON><PERSON> on his podcast - he's basically saying analytics have sucked the soul out of NBA basketball by turning every team into robots chasing the same three shots: three-pointers, layups, or free throws. He points out that while players today might be more skilled than ever, they're all being forced to play this cookie-cutter style that's making games boring to watch, especially for old-school fans who miss the physical, gritty basketball of the past. The kicker is that even though we've got all this talent in the league, teams are so obsessed with playing the \"analytically correct\" way that we're not even getting to see players show off what they can really do on the court anymore.\n\nFWIW: TIcket demand and fees for casting rights appear unaffected...", "author": "SalvatoreTirabassi1", "created_time": "2025-01-01T03:22:01", "url": "https://reddit.com/r/analytics/comments/1hqulws/nba_employee_paul_george_criticizes_nba_analytics/", "upvotes": 66, "comments_count": 34, "sentiment": "bearish", "engagement_score": 134.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hqwaa4", "title": "<PERSON>: If 800 US companies paid their taxes, no American would have to pay a dime in federal tax", "content": "I came across a statement suggesting that if certain large U.S. companies paid their full share of taxes, it could offset the need for individual Americans to pay federal taxes. Is there any truth to this claim? How realistic is the idea that corporate tax revenue alone could cover federal spending or even eliminate the tax burden on individuals? I’m curious about the math or logic behind this concept and whether the federal budget shows any scenario where corporate tax compliance could completely replace individual taxes. How valid is this argument from an economic perspective, and what would the financial implications look like?", "author": "Alternative_Gur_7706", "created_time": "2025-01-01T05:14:26", "url": "https://reddit.com/r/economy/comments/1hqwaa4/warren_buffett_if_800_us_companies_paid_their/", "upvotes": 4905, "comments_count": 217, "sentiment": "neutral", "engagement_score": 5339.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hqy1in", "title": "How would you handle 40k dollars in savings with intelligence?", "content": "I have 20k for the rainy day and another 20k which is just accumulated. I also pay rent, so may be it is good to keep some money somewhere ahead and to use as fund to pay rent and to have a bit profit. Also, every month I have 3-4k dollars I just store in the bank without any profit.", "author": "Agitated-Whereas2804", "created_time": "2025-01-01T07:20:51", "url": "https://reddit.com/r/FinancialPlanning/comments/1hqy1in/how_would_you_handle_40k_dollars_in_savings_with/", "upvotes": 23, "comments_count": 10, "sentiment": "bullish", "engagement_score": 43.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hqy8vw", "title": "Bitcoin Isn’t Unique But Infinite—$100K Is Beyond Absurd", "content": "Imagine this: air, the most abundant and freely available resource on Earth. Everyone can breathe it without restriction, it’s everywhere, and it costs nothing. Now, imagine a company decides to package this air into bottles, claiming, “Only 21 million bottles will ever exist.” They sell the bottles, marketing them as rare and special, and soon, the price of a single bottle soars to $100,000.\n\nBut here’s the catch: anyone can grab the same air, bottle it themselves, impose their own arbitrary limits, and sell it too. The air inside these bottles is identical, same purity, same ability to sustain life. Yet somehow, the original company convinces people their air is unique, while the others are dismissed as worthless. This isn’t just absurd but comically irrational. And yet, it’s a perfect analogy for Bitcoin.\n\nThink about it: bottling air to sell is ridiculous. Why would anyone pay for something that is freely and infinitely available? Worse, imagine dedicating an entire decentralized system—one consuming massive amounts of electricity, requiring complex networks, and involving global participants—to package, transfer, and store this bottled air. This is the level of absurdity we reach with Bitcoin.\n\nBitcoin’s defenders often point to its decentralization, anonymity, and capped supply of 21 million coins as reasons for its value. But what is this decentralized system really securing? Digital air. The units being produced, transferred, and protected represent nothing—they are infinitely replicable tokens that anyone can create at any time. Anyone with the technical knowledge can clone Bitcoin’s code, impose their own arbitrary cap, and launch their own cryptocurrency.\n\nThis brings us to the critical difference between Bitcoin (and cryptocurrencies) and other financial assets like stocks or fiat currencies: cryptocurrencies represent nothing and are inherently limitless.\n\nStocks represent ownership in a company. A company cannot be copied like a piece of code. The value of a share is tied to the performance, assets, and operations of that unique entity. You cannot clone Tesla or Apple, and therefore, you cannot duplicate the value tied to their stocks. Stocks are inherently scarce because companies themselves are finite, tied to real-world assets, operations, and innovation.\n\nFiat currencies, on the other hand, represent units of debt. They are issued by central banks and commercial banks through loans and bonds based on the ability of borrowers—companies, governments, or individuals—to repay them. Banks cannot create money infinitely because it is tied to the real-world capacity of debtors to meet their obligations. No one can walk into a bank and request a trillion-dollar loan without collateral or a realistic ability to repay it.\n\nCryptocurrencies operate under no such constraints. If you wanted to create a trillion crypto tokens tomorrow, nothing stops you. Bitcoin’s 21 million coin cap is arbitrary and meaningless because anyone can copy the Bitcoin protocol, adjust the parameters, and produce trillions of coins in their own system. In this way, cryptocurrencies represent nothing—no ownership, no debt, no tangible connection to the real economy. They are the digital equivalent of bottling air, infinitely replicable with no inherent value.\n\nBitcoin’s defenders argue that its capped supply makes it valuable, likening it to gold. But unlike gold, Bitcoin’s scarcity is artificial and replicable. Limiting Bitcoin to 21 million units is no different than bottling air and claiming, “We’re only producing 21 million bottles.” The air is still abundant, and anyone else can create their own bottles with their own arbitrary limits.\n\nThe absurdity deepens when you consider the massive resources dedicated to securing, transferring, and storing these digital tokens. Bitcoin mining consumes more electricity than entire nations, and yet what is being protected? A digital representation of air, something freely available, infinitely replicable, and ultimately meaningless.\n\nBitcoin’s price doesn’t reflect the value of its features. If decentralization, anonymity, and security were truly valuable, Bitcoin’s clones, many of which improve on these features, would share its valuation. Instead, Bitcoin’s price is fueled by speculation and the collective illusion that it is unique. People aren’t paying $100,000 because Bitcoin is the best cryptocurrency; they’re paying because they believe someone else will pay more.\n\nThis speculative bubble cannot last. Once people recognize that Bitcoin’s features are infinitely replicable, and that its competitors offer the same or better functionality at a fraction of the cost, the illusion will collapse.\n\nBitcoin isn’t digital gold, nor is it a revolutionary asset. It’s a digital air, packaged and sold as rare and valuable despite being infinitely and freely available. Paying $100,000 for a single Bitcoin is not a testament to its worth but evidence of a collective delusion. The elaborate decentralized system supporting Bitcoin exists to secure and transfer something that anyone can recreate endlessly at no cost.\n\nWhen the hype fades, and the absurdity of the system becomes clear, Bitcoin’s price will plummet, leaving behind the inescapable truth: no rational person should pay a fortune for something as abundant and meaningless as digital air.", "author": "Life_Ad_2756", "created_time": "2025-01-01T07:36:43", "url": "https://reddit.com/r/investing/comments/1hqy8vw/bitcoin_isnt_unique_but_infinite100k_is_beyond/", "upvotes": 0, "comments_count": 60, "sentiment": "bearish", "engagement_score": 120.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hqzq9x", "title": "Ocean Power Technologies DD", "content": "The DD so you don't have to ..\n\nOcean Power Technologies, Inc. (OPTT) specializes in innovative and cost-effective low-carbon marine power, data, and service solutions. Their flagship products include the PowerBuoy® platforms and WAM-V® autonomous surface vessels, serving sectors such as defense, oil and gas, science and research, and offshore wind.\n\nRecent Developments:\n\nNaval Postgraduate School Collaboration: In December 2024, OPT completed the main assembly of a PowerBuoy® equipped with advanced features, including AT&T® 5G technology and subsea sensors, integrated into their latest Merrows™ suite for AI-capable operations. This system is scheduled for deployment in early 2025 to provide continuous autonomous monitoring and data collection services in a significant maritime environment. \n\nSecurities Purchase Agreement: On December 20, 2024, OPT entered into a securities purchase agreement with an institutional investor for the issuance and sale of senior convertible notes totaling $54 million. An initial $4 million was issued on the closing date, with provisions for additional closings up to $50 million upon meeting specific conditions. \n\nFinancial Performance: In the second quarter of fiscal year 2025, OPT reported a 2.7x year-over-year increase in revenue to $2.4 million and a 46% reduction in net loss to $3.9 million. Operating expenses decreased by 41% compared to the same period last year. The company anticipates achieving profitability by late 2025. \n\nLatin America Expansion: OPT secured $3 million in purchase order commitments over 36 months for its WAM-V unmanned surface vehicles in Latin America, doubling the total commitments in the region. \n\n\nFinancial Overview:\n\nRevenue Growth: OPT's revenue has shown significant growth, with fiscal year 2024 revenues increasing by 102.23% to $5.53 million compared to the previous year. \n\nOperating Expenses: The company has implemented restructuring and streamlining activities, resulting in a 39% decrease in operating expenses to $4.9 million in Q1 2025 compared to $8.1 million in the same period the prior year. \n\nCash Position: As of October 31, 2024, OPT reported cash and cash equivalents of $2.09 million. \n\n\nMarket Position and Outlook:\n\nOPT is transitioning from its research and development phase to full commercialization, focusing on executing its strategic goals and expanding its market presence. The company is well-positioned to drive shareholder value creation in this post-R&D phase, with fully commercialized solutions and a robust pipeline. \n\nRisks and Considerations:\n\nProfitability Timeline: While OPT projects profitability by late 2025, achieving this target depends on successful execution of its strategic initiatives and market adoption of its technologies.\n\nCapital Requirements: The recent securities purchase agreement indicates ongoing capital needs to support operations and growth. Investors should monitor the terms and potential dilution associated with these financings.\n\nMarket Competition: The marine renewable energy sector is competitive, with emerging technologies and players. OPT's success will depend on its ability to differentiate its offerings and secure market share.\n\n\nConclusion:\n\nOcean Power Technologies is making significant strides toward commercialization and revenue growth, with strategic partnerships and product deployments enhancing its market position. However, investors should carefully consider the company's financial health, capital requirements, and the competitive landscape when evaluating potential investment opportunities.", "author": "robert607", "created_time": "2025-01-01T09:33:21", "url": "https://reddit.com/r/pennystocks/comments/1hqzq9x/ocean_power_technologies_dd/", "upvotes": 117, "comments_count": 43, "sentiment": "bullish", "engagement_score": 203.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr0w4h", "title": "Why I'm bearish on Nvidia ", "content": "I've recently become very bearish on Nvidia, here's why:\n\n**Inference - Size isn't everything and the rise of reasoning**\n\nReports from inside OpenAI suggest GPT-5 has disappointed and the same seems to be true of other next scale LLMs. For whatever reason, it looks like training on larger and larger datasets is no longer bringing the goods. The scaling law, that took us from gpt-1 to gpt-4, has broken but all is not lost. OpenAI's latest o3 looks incredibly impressive. The secret is that it thinks before it speaks and reasons through problems (at huge cost).\n\nMy assumption is that reasoning is going to be the new frontier. In other words, the next phase in AI development will be focused on inference rather than training. This is important for Nvidia because their chips specifically excel at training. Other chips from the likes of AMD are much more competitive when it comes to inference.\n\n**DeepSeek, the cost of training and a note on the human brain**\n\nYour brain consumes significantly less energy than a 100 watt lightbulb, which is relevant because it shows how far we can go to reduce the cost of intelligence. This compares with the reputedly hundreds of thousands of dollars it cost gpt-o3 to run the benchmark tests.\n\nChinese up-start, I mean start-up, DeepSeek recently launched a state of the art model which compares favourably with GPT-4o and Claude 3.5 and outperforms Meta's and Alibaba's best. Good on them! But the really impressive thing is it took just two months to train, only cost $5.58 million and it was all done without Nvidia chips because of U.S. export controls.\n\nWhat does this mean for Nvidia? Well, it's recent news and I'm still digesting it but I think it means the cost of training is going to plummet. I don't think it necessarily means that training LLMs on more data is going to lead to dramatic improvements - but I might be wrong. My best guess is that the demand for the GPUs Nvidia makes is going to fall through the floor.\n\n**AI adoption - Stupid is what stupid does**\n\nSo far as I understand it, the cost of inference has also plummeted with DeepSeek's V3. However, this is early days and I'm not an AI researcher. Let's say it takes some time for the cost of advanced reasoning models, like gpt-o3, to come down, which so far as I know it might. Sam Altman thinks \"we will hit AGI much sooner than most people in the world think and it will matter much less\". This makes sense if the cost of advanced reasoning models remain very high. The question that worries me is how far and fast AI will be adopted given this state of affairs. Cheap AI still makes incredibly simple mistakes and I'm not convinced that in their current form AI agents are a good replacement for people, except for some very specific tasks.\n\nNvidia's valuation relies on a lot of growth and that growth ultimately relies on adoption. I'm not sure that happens any time soon if Sam's right.\n\n**What are other people's thoughts. Is Nvidia's valuation still justified?**", "author": "Helpful-Raisin-5782", "created_time": "2025-01-01T11:05:10", "url": "https://reddit.com/r/investing_discussion/comments/1hr0w4h/why_im_bearish_on_nvidia/", "upvotes": 6, "comments_count": 7, "sentiment": "bearish", "engagement_score": 20.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr19zg", "title": "Tesla replaced laid off US workers with foreign workers using H-1B visas: Electrek", "content": "", "author": "marketrent", "created_time": "2025-01-01T11:34:04", "url": "https://reddit.com/r/technology/comments/1hr19zg/tesla_replaced_laid_off_us_workers_with_foreign/", "upvotes": 36510, "comments_count": 1449, "sentiment": "neutral", "engagement_score": 39408.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr27v4", "title": "Interesting article on how Tesla/Waymo will threaten Uber's dominance", "content": "", "author": "deleted", "created_time": "2025-01-01T12:41:27", "url": "https://reddit.com/r/AutonomousVehicles/comments/1hr27v4/interesting_article_on_how_teslawaymo_will/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr493m", "title": "What are peoples' reasons for trying to break into analytics still?", "content": "Each day I see numerous posts about people attempting to break into analytics with the most random backgrounds that make them less than ideal candidates. They likely face a massive uphill battle to break into an analytics related role.\n\nWhy does this keep happening?\n\nDo people believe there's still a huge boom in the job market for analytics?\n\nIt just confuses me to be honest given how saturated the field is and bleak the job market is right now. You have an exponentially increasing supply of labor and decreasing demand for it.\n\nEdit: it appears that a few people are getting upset and think that I am gatekeeping. All I am asking is what are poeples' motivations to try and enter this field. It seems like many people think we're in a 2021-22 situation where you can complete a bootcamp or masters with no relevant experience or domain knowledge and then have the opportunity to jump right into the industry with a hybrid/remote role as a data analyst/scientist, etc. I personally think people are getting influenced by trendy influencer/youtube videos and universities creating these programs.\n\nObviously people can do as they wish. I don't care, it's just a job. However, I worry that many of the people posting about how they want to break in don't understand the true nature of the general job market and the analytics industry in particular. No shit most industries are saturated right now, but analytics is clearly at a higher level due to the combination of hype, off-shoring and cooling of the overall job market.\n\nI feel bad for the individuals who have decided to complete a bootcamp, a MS in analytics or just graduated with an irrelevant degree, and possess zero domain knowledge with few analytical skills but want to completely jump ship and break into analytics. They're going down a path that'll likely lead to hundreds maybe even over a 1000 applications with most being rejections and ultimately making a failed investment.\n\nThey can do what they want, however, I worry that many people think the barrier to entry is much lower than it truly is and are making poor decisions.", "author": "deleted", "created_time": "2025-01-01T14:46:02", "url": "https://reddit.com/r/analytics/comments/1hr493m/what_are_peoples_reasons_for_trying_to_break_into/", "upvotes": 157, "comments_count": 121, "sentiment": "neutral", "engagement_score": 399.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr5w8u", "title": "Black powder muzzleloader hunting with a self-driving cart", "content": "[https://www.youtube.com/watch?v=QIHxalHVWy8](https://www.youtube.com/watch?v=QIHxalHVWy8) \n\nSometimes old and new technology just come together. Here I try black powder muzzleloader deer hunting with the help of our self-driving cart -- it's a deer driving self-driving cart!\n\n(Attempting to cross-post on r/AutonomousVehicles and r/blackpowder )", "author": "thandal", "created_time": "2025-01-01T16:09:44", "url": "https://reddit.com/r/AutonomousVehicles/comments/1hr5w8u/black_powder_muzzleloader_hunting_with_a/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr746l", "title": "Just want some genuine advice on investing.", "content": "I'm 22 years old and I've never invested before, I keep seeing these things on YouTube about the election and crypto and I have been hearing that x<PERSON> or dog<PERSON><PERSON>n or xai will blow up really soon and I just don't know what's true and what's not. ", "author": "Excellent_Rain3878", "created_time": "2025-01-01T17:06:16", "url": "https://reddit.com/r/investing_discussion/comments/1hr746l/just_want_some_genuine_advice_on_investing/", "upvotes": 6, "comments_count": 5, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr783s", "title": "Gains are not worth the risk", "content": "I wrote this in the hopes of saving some of you future heartache and irreversible trauma. I lost 110k over the past month. The majority after options calls during the bloodbath after december fed meeting. \n \nIf i could go back to my past self, i would say this. The loss isn't worth the potential gains. Before, I was just burnt out from my job. But at least i was proud to have saved up my first 100k. Now im burnt out, down 3 years of savings, and have a lot less freedom in my life.  I can't focus on work, i'm depressed and can't find joy in my hobbies anymore. I'm probably in the process of ruining my relationship as well. Even if i had won, i definitely don't think I'd be happier an equivalent amount. \n\nLife is hard. If you worked hard and earned some money. Dont make degenerate bets. The vast majority of us are just normal humans who should just save their time and invest in part index fund and part cash equivalents.\n\nOr maybe this marks the bottom and it is a buying opportunity. Your choice. \n\nEDIT\nWas only expecting maybe max 100 upvotes but i guess I said something that resonates. \n\nAfter wading through the comments, insults, memes, etc. I was touched by enough kind people reaching out to add some more. I dont think i can stomach another comment reading though so please dont expect me to react anymore. Notifications are off. Posted a 80k loss screenshot of part of my portfolio. Another 30k was lost in another account. https://imgur.com/a/jMvs9DR\n\n1. \"only bet what you can afford to lose\" doesn’t really make sense. Dont use that saying to convince yourself to make risky gambles. I could afford to lose 110k in the sense that i won't starve, i would still have a roof over my head, and i still have 30k i left that i promised to myself i wouldnt touch. But i lost things i didn't expect. Like my passions for my hobbies, a healthy exercise habit, my mental health after recovering from depression during college. Even during the time i was trading, i also hated how it felt. I was glued to the ticker and was losing connection with real life relationships.  Before you use the money you think you are willing to lose. Try spending a part of that amount on yourself. Get yourself some luxuries, some experiences, maybe travel, take a sabbatical from work, or spend it on someone close to you.  Its all numbers on the screen when trading, so its easy to lose a sense of it all. Afterwards, imagine losing the ability to do all that and only proceed if youre ok with that. \n\n2. For those who think this isnt something a normal person could go through. I saved roughly 60-70% of my paycheck the past 3 years. I made sacrifices on lifestyle and luxuries. \n\n3. For those that still want to go on, i sure cant stop you. Maybe some of us need to learn a lesson firsthand. Might be better even to learn early on before you have a family with hundreds of thousands saved up over decades. This might help. \n\nLooking back, i definitely had chances to make money.  I was thinking about RKLB when it was $5 (now $25). I had a chance to jump into RDDT when it was still $80. I considered googl at 165 since the bad news seemed overblown.   Even at my most insane already down 50k, before i lost it all i almost went all in in on christmas eve with 1 week dte options on tsla calls.  Instead i did it on the Friday afterwards hoping for a similar bounce to recover the from the drop after fed earnings.  If you get into single stocks, crypto, options it's a lot riskier. Youre going to have to be lucky with the timing. Youre also going to have to be disciplined with your strategy.  \n\nWhen you make your bet. If you win, stop.  i hope you become happy. I hope you get more time to pursue your passions. To spend time with family and friends. To become a person you are proud of. \n\nIf you lose, i hope you recover. Never gamble again. Life will be harder. But, maybe we can still find a part of that happiness. I dont think we really want money. We just want a more human experience. ", "author": "ThinkingIsGoodYT", "created_time": "2025-01-01T17:11:17", "url": "https://reddit.com/r/wallstreetbets/comments/1hr783s/gains_are_not_worth_the_risk/", "upvotes": 7852, "comments_count": 1117, "sentiment": "neutral", "engagement_score": 10086.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr79e4", "title": "Tesla on FSD 13.2.2 finds spot in busy parking lot ", "content": "", "author": "coffeebeanie24", "created_time": "2025-01-01T17:12:56", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hr79e4/tesla_on_fsd_1322_finds_spot_in_busy_parking_lot/", "upvotes": 758, "comments_count": 187, "sentiment": "neutral", "engagement_score": 1132.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hr7qj1", "title": "Airspace Batteries ($ABTT) is positioning itself to be the next $KULR 🚀", "content": "**Not financial advice. Do your own due diligence before you invest.**\n\n\n\nCongrats to everyone who saw gains from $ABTT on Tuesday. Considering it was a down day for most other stocks, I’ll happily take a 24% gain. However Airspace Batteries ($ABTT) is likely due for at least one more surge in January. With high earnings per share, cutting edge tech and large institutional buy in, there is a chance this could be the next $KULR.\n\n \n\n* **Market Cap - $98.2mil**\n* **Current Price - $2.54**\n* **52-Week Range - $0.81–2.67**\n\n**1. Recent Developments**\n\n* Has developed a new patented lithium ion battery (Named *Cheetah)* in 2023 which can store charge 12% better than its competitors. It’s now out the research and development phase and the company is now **laser focused on securing contracts**, with several buyers interested.\n* **Reported net profit of $0.4 EPS** in their latest earnings report, which is mind-boggling, considering they’ve been focused on developing their products. Analysts expect this to soar in 2025 and 2026.\n* Partnerships and Acquisitions: Interest from the military with a rumoured partnership with a well known carmaker.\n\n**2. History and Product**\n\n* Quite a new company as it was founded in 2019 by MIT alumni <PERSON>, a serial entrepreneur who founded Pop Rocketr before its acquisition for $1.6mil. <PERSON> was unhappy with the state of electric car batteries and wanted to **turbocharge progress so the next generation of batteries could be launched today.**\n* Inspired by his wife’s pent up rage towards his business ventures, <PERSON> wanted the battery to store an additional 86% charge than Tesla and Duracell batteries. So far results are positive, with a single Cheetah outlasting the latter by over 370%. The implications of this are massive, as you could charge your phone in twice the speed as a conventional charger. <PERSON> is hoping to shrink the Cheetah by 10X so it can eventually stand comfortably on your desk.\n* $ABTT raised a staggering $30mil last year when <PERSON> approached a renown loan shark for capital in exchange for repayment. Sadly, not realising the loan shark was connected to the mob (Who have charged bespoke interest fees just for him), Hughes has struggled to repay on time due to the **cutting edge research and development** costs that come with building a battery. This has resulted in Hughes losing his two best typing figures, with analysts predicting double digit losses by 2027 if capital is not repaid. However Hughes remains optimistic that he can repay, once his Bitcoin treasury pays dividends (See below).\n* However their main market is electric vehicles. The results are coming in and **$ABTT batteries are outperforming their competitors by a country mile**: One study has found that when fitted in an electric car of your choice, the **Cheetah battery can outlast the car’s lifespan by over 10 years**. Critics have argued this is due to the battery shorting the car’s circuits, rendering it unusable. However Hughes has since successfully made the case in court that you should diversify your car holdings to allow for combustible-fuel engines as well.\n\n \n\n**3. Detailed Financial Analysis and developments**\n\n* Where most companies often struggle turning a profit until their research and development stage is over, **$ABTT has already started to turn profitable**, bringing in a whopping $0.4 per share in earnings. There are **currently no plans for dilution** in the next 6-12 days and **analysts are positive** that the EPS could even rise to $0.41 next quarter.\n* Airspace Batteries shocked the investing world when they announced a **bold plan** to create a massive Bitcoin treasury this financial quarter, using net cash. Investors have been cautiously optimistic as the company has already allocated funds for a whopping 685,485 Satoshis. Their crypto broker has confirmed this is the maximum amount of Bitcoin you can buy per financial year, and Hughes has stated that this justifies spending 68% of their net cash on cryptocurrency and fees. There are already plans next financial quarter to buy the rest of the bitcoin. Institutional investors are now observing whether this bold move will bring big returns, but considering the success of other companies with Bitcoin treasuries, I think this can only mean good things.\n* Net revenue for the last financial year was $23mil, a **90% increase on the financial year before**.\n\n \n\n**4. Growth Drivers for next 5 years.**\n\n* a. The demand for lithium ion batteries is expanding by 18% per year, with **no signs of stopping**. Futurologists are already predicting the end of combustible engine batteries, which means there will be a market void to fill.  $ABTT is well positioned to get a big slice of that pie.\n* b. With current price indicators remaining bullish on crypto over the next 5 years, analysts are confident that the newly established Bitcoin treasury will **likely boost revenues** by an eye watering $3.6.\n* The C.E.O, Jordan Hughes has a great record for promoting his company online, and shows no sign of stopping. This has increased visibility in the company by 600% since August last year. He has also made numerous media appearances whilst giving out flyers on Wall Street. This will likely pay dividends in the company’s future.\n\n \n\n**5. Potential Risks**\n\n* Airspace Batteries is a relatively new company, so will have difficulty jostling for a bigger slice of the market, compared to its competitors.\n* Hughes is a notorious gambler, often taking his company’s revenue to Vegas each financial quarter and betting it all on the roulette table. This has been a risky strategy, but has **paid off** at least 2/6 times so far. However Hughes is looking to pivot his high risk, high reward strategy towards poker. Could more gains be on the horizon?\n* Activist investors are unsure about whether to invest money in a company where the mob has a 48% stake in it. However **analysts are predicting that with such big institutional buy in that this should ensure stability towards the company’s success.**\n* It's always worth doing due diligence with a company of this type.\n\n\n\n  \n\n\nIn summary, $ABTT is a high risk, high reward play that could pay big in 2025. As the company is out of its research and development phase, with huge institutional buy in, Airspace Batteries can now focus on acquiring customers and finally take on big battery.  With investors already looking for the next $KULR, I hope you make money!\n\n \n\n \n\n**Not financial advice. Do your own due diligence before you invest.**\n\n ", "author": "J-Sou-Flay", "created_time": "2025-01-01T17:34:44", "url": "https://reddit.com/r/pennystocks/comments/1hr7qj1/airspace_batteries_abtt_is_positioning_itself_to/", "upvotes": 188, "comments_count": 81, "sentiment": "bullish", "engagement_score": 350.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hrah9b", "title": "There’s big money in D2D sales if you have the mentality. ", "content": "Hit $22,000 gross in two weeks as a handyman. I target upper middle class neighborhoods and go door to door. No ads, no flyers, no website yet even. Just magnetic business cards I hand out when I can’t make a sale. \n\nI specialize in jobs that provide high perceived value that I can knock out quick and charge at least a couple hundred bucks for. Mostly exterior repairs, fixes, etc. Average around $200-$400 an hour per job that way. \n\nIt’s amazing how much effort and money people (<PERSON><PERSON> included in the past) put into getting customers to come to them but won’t take a single step in the other direction. Everything changed when I stopped focusing on building websites/apps, optimizing google ads, obsessing over my google business profile, etc and just went out and knocked on doors. \n\nI was a software engineer for 10 years when I got laid off February 2024. Months of failed jobs apps led me to start doing some personal training, I had a lot of experience in the strength world and did ok, enough to pay bills but nothing like the salary I was used to. Started providing handyman services but wasn’t getting many leads through the usual recommendations for local service businesses. Needed cash fast and started going door to door. If I can do it, so can you. Get out there and make things happen, don’t just sit back and wait. ", "author": "37hduh3836", "created_time": "2025-01-01T19:34:14", "url": "https://reddit.com/r/smallbusiness/comments/1hrah9b/theres_big_money_in_d2d_sales_if_you_have_the/", "upvotes": 1389, "comments_count": 327, "sentiment": "neutral", "engagement_score": 2043.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hrakb9", "title": "Today marks 10 years of keeping track of my NW", "content": "\nwww.imgur.com/a/D0sM3nm\n\n10 years! Crazy to think that when I started this I had a grand total of $11,972.11 to my name, and most of that was Lego and guitars. I started to track it then due to finally paying off my student loans the previous December. \n\nSince then I've been able to make it grow to $715,639.89. Our discipline helped us buy a house cash in 2023, hence the big spike for Total Hard Assets then. \n\n**Stats:**\n\n43, married DIN<PERSON>s.\n\nAll mutual funds except for 30k in AAPL. \n\nDebt: $0\n\n**Current totals:**\n\nTotal Liquid assets: $133,968.27\n\nTotal Hard Assets: $237,479.50\n\nTotal Retirement assets: $344,192.12\n\n**Goal:**\n\n1,500,000 in investments to retire.\n\nJust gotta keep a n keeping on. ", "author": "FImilestones", "created_time": "2025-01-01T19:38:05", "url": "https://reddit.com/r/Fire/comments/1hrakb9/today_marks_10_years_of_keeping_track_of_my_nw/", "upvotes": 146, "comments_count": 47, "sentiment": "bullish", "engagement_score": 240.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hrcnef", "title": "YieldMax ETFs and share price (\"NAV\") erosion", "content": "**The images in this post are best viewed on a computer monitor or laptop, not a phone.**\n\nHappy New Year! Since there are frequent posts asking for \"thoughts\" on MSTY and other YieldMax ETFs, I will share my thoughts on YieldMax ETFs. I'll put the TLDR at the beginning instead of at the end.\n\n***TLDR: you can make money with YieldMax ETFs, but in almost every case you would make more money in the corresponding (\"underlying\") stock. Most YieldMax ETFs suffer share price declines (\"NAV erosion\") that drag on total return and can lock you into or trap you in the funds, forcing you to take a loss if you sell. YieldMax ETFs are most suitable for retired people who already have a lot of money and actually need the income, not young people who are working, earning money at their jobs, and have modest portfolios that need to grow.***\n\n* YieldMax ETFs are a relatively new family of funds (the oldest TSLY started in November 2022, the newest started in December 2024) that have attracted attention because of their high distribution (\"dividend\") yields\n* All of the funds trade call and put options on actual stocks and ETFs like NVDA, MSFT, COIN, MSTR, ARKK, etc.  None of the YieldMax ETFs except ULTY, YMAG, and YMAX actually own the stocks or ETFs on which they trade options. YMAX and YMAG are \"funds of funds\" that own other YieldMax ETFs, and ULTY owns some actual stocks and ETFs.\n* Except for ULTY, none of the money (\"dividends\") that YieldMax ETFs distribute to shareholders comes from actual dividends. Many of the stocks the ETFs track - like TSLA and AMZN - don't even pay dividends. Even if the stocks do pay dividends - like AAPL and MSFT - Yieldmax ETFs don't own any shares of the stock so they aren't entitled to the dividends the stocks pay. All of the money (\"dividends\") that YieldMax ETFs distribute to shareholders of the funds comes from trading options and from interest collected from US Treasury Notes that the funds hold.\n* Because of the way the ETFs are constructed, they tend to not capture the full gains of the stock they are tracking. If MSTR goes down, MSTY goes down too. If MSTR goes up, MSTY goes up too, but not as much as MSTR. As the YieldMax fund managers themselves explain:\n\n\n**The Fund’s strategy will cap its potential gains if MSTR shares increase in value. The Fund’s strategy is subject to all potential losses if MSTR shares decrease in value, which may not be offset by income received by the Fund. The Fund may not be suitable for all investors.** [https://www.yieldmaxetfs.com/msty/](https://www.yieldmaxetfs.com/msty/)\n\n* Because of the way YieldMax ETFs are constructed, if the stock it is tracking is generally rising in price - and the YieldMax ETF managers have picked stocks that tend to rise in price, like NVDA, AMZN, MSFT, etc. to track with their ETFs - the share price of the Yieldmax ETF will lag farther and farther and farther behind the stock it is tracking as time goes on, even to the point the YieldMax ETF shares are not only not gaining as much as the stock it is tracking but are actually losing value - \"NAV erosion\" - despite the fact the stock it is tracking is rising in price. For example, here is the share price of TSLY (blue line) - the oldest YieldMax ETF so it has had more time to fall behind the stock it tracks - compared to the share price of TSLA (red line)\n\n[https://s3.tradingview.com/snapshots/4/45sDO8uu.png](https://s3.tradingview.com/snapshots/4/45sDO8uu.png)\n\nSince TSLY's inception in November 2022, TSLA's shares are up +120% and TSLY's shares are down -64%. TSLY even had to do a 2:1 reverse split in February 2024 to keep the price from being scary low.\n\nSince CONY's inception in August 2023 it's share price is down -34% (blue line) while during the same time period COIN's shares are up +213% (red line).\n\n[https://s3.tradingview.com/snapshots/b/bU9S2eRS.png](https://s3.tradingview.com/snapshots/b/bU9S2eRS.png)\n\nEven when a YieldMax ETF like MSTY hasn't had any share price (\"NAV\") erosion - yet - it's share price gain has lagged far behind that of MSTR, the stock it tracks.\n\nSince MSTY's inception in February 2024 its shares are up only +24% (blue line) while MSTR's shares (red line) are up +306%\n\n[https://s3.tradingview.com/snapshots/o/OIPSU72a.png](https://s3.tradingview.com/snapshots/o/OIPSU72a.png)\n\n* Most YieldMax ETFs have suffered share price declines - \"NAV erosion\" - since their inception, but some are much worse than others. This chart shows the share price action for several popular YieldMax ETFs since their inceptions. I included the S&P 500 index (VOO) price action since the November 2022 inception of the oldest YieldMax ETF (TSLY) as a benchmark and reference. The chart is busy because of the number of ETFs but look at the numbers on the right edge of the chart for each ETF.\n\n[https://s3.tradingview.com/snapshots/r/RjeIvE2L.png](https://s3.tradingview.com/snapshots/r/RjeIvE2L.png)\n\nIf you have trouble reading the chart the results are\n\n* VOO +47.70%\n* the following have share price increases, although far below the stocks they track\n* PLTY +37.47% (PLTR +82.46% during the same time)\n* MSTY +24.15% (MSTR +306.11% during the same time)\n* NVDY +17.27% (NVDA +369.91% during the same time)\n* and then they go increasingly negative (share price \"NAV\" erosion)\n* YMAG -3.24%\n* TSMY -5.15% (TSM +15.19%)\n* AMZY -6.19% (AMZN +69.90%)\n* FBY -7.64% (META +79.89%)\n* NFLY -8.90% (NFLX +103.36%)\n* JPMO -9.70% (JPM +63.80%)\n* MSFO -9.91% (MSFT +30.50%)\n* APLY -10.68 (AAPL +50.43%)\n* YMAX -14.82%\n* GOOY -27.71% (GOOG +43.18%)\n* CONY -34.03% (COIN +213.59%)\n* YBIT -39.51% (BTC +40.56%)\n* AMDY -49.45% (AMD +18.88%)\n* ULTY -53.64%\n* TSLY -64.40% (TSLA +120.44%)\n\nAs you can see, in most cases while the stocks that YieldMax ETFs track were going up up up, the share prices of YieldMax ETFs were going down down down.\n\nIn general, when you invest, you want to buy shares that go up in price, not down. Buy low and sell high. You can't sell high if the price went down.\n\n* Since dividend yield is inversely related to share price - as share price goes down, dividend yield goes up - part of the reason YieldMax ETFs have such high yields is because as the share price went down, the yield went up, even if the dividend per share stayed the same.\n* Yes, but what about the \"dividends\"?! \"I'm making so much money every month from YMAX, who cares if the share price is going down!\" some might say. \"I'm taking the YieldMax \"dividends\" and using them to buy SCHD\" others might say. Well, the problem with declining share price (\"NAV erosion\") it guarantees you will take a loss if you sell your YieldMax ETF shares. If you don't want to take that loss by selling it locks you in or traps you in that YieldMax ETF. You will take a loss if you sell your shares because you need the money for something, or you want to move that money to a better investment, even another YieldMax ETF, or if the YieldMax ETF no longer fits your needs or risk tolerance.\n* But don't the \"dividends\" make up for the dropping share price? Well, as we know or should know, **total return** is the combination of share price increase (or decrease) and reinvested dividends (if any). Even though YieldMax share prices in general go down, don't all those dividends make up for it? Well, in most cases they help offset the negative effect of dropping share price on total return, but not enough to make up for all of it. Even with all those dividends you would have more gains investing in the actual stock - NVDA, MSTR, COIN - than in the YieldMax ETFs *even with the dividends*. Scroll down to \"Growth of $10,000\" in each of the links that follow.\n\n[https://totalrealreturns.com/n/NVDA,NVDY](https://totalrealreturns.com/n/NVDA,NVDY)\n\n[https://totalrealreturns.com/n/MSTR,MSTY](https://totalrealreturns.com/n/MSTR,MSTY)\n\n[https://totalrealreturns.com/n/COIN,CONY](https://totalrealreturns.com/n/COIN,CONY)\n\n* Sometimes, even with the dividends, the YieldMax ETF not only hasn't matched the stock it tracks, *it even underperformed the S&P 500 index*. Scroll down to \"Growth of $10,000\" in each of the links that follows:\n\nTSLY [https://totalrealreturns.com/n/TSLA,VOO,TSLY](https://totalrealreturns.com/n/TSLA,VOO,TSLY)\n\nGOOY [https://totalrealreturns.com/n/GOOG,VOO,GOOY](https://totalrealreturns.com/n/GOOG,VOO,GOOY)\n\nAPLY [https://totalrealreturns.com/n/AAPL,VOO,APLY](https://totalrealreturns.com/n/AAPL,VOO,APLY)\n\n* For those who are using YieldMax ETFs to \"feed\" purchases of SCHD or other funds, in most cases you would have more money to invest in SCHD and other funds if you had invested in the actual stock that the YiekdMax ETF tracks than in the YieldMax ETF that tracks the stock, and sold a dollar amount of shares every month or quarter or whatever.\n* So, are there circumstances where YieldMax ETFs make sense? As the YieldMax ETF fund managers point out:\n\n>**The Fund may not be suitable for all investors.**\n\nSo who are YieldMax ETFs suitable for? Well, not young people who want/need to grow their portfolios. As I have shown, they would have more gains/make more money investing in the actual stocks - NVDA, NFLX. MSTR, etc. - than in the YieldMax ETFs - NVDY, NFLY, MSTY - that track the stocks. But sadly, it looks like lots of young people are only looking at the high dividend yield of YieldMax ETFs and aren't paying attention to share price declines (\"NAV erosion\") and total return. They are making gains, but not as much as they could be making.\n\n* In my opinion, YieldMax ETFs are suitable for people who already have 6 or 7 or 8 figure portfolios, are living on their investments and need income, who want income from options trading without having to sell covered calls or get involved with options trading personally, and already have lots of money and can tolerate the share price declines (\"NAV erosion\").\n\nNone of the above means I \"hate dividends\" or I'm \"anti-dividends\". I collected over $61k in dividends in 2024. I'm not even anti-YieldMax ETFs per se, when it is appropriate for the investor. I have 2.73% of my portfolio in NVDY, but I'm one of those people I described who already has a large portfolio after years of investing, who is near retirement and needs the income, and who doesn't want to trade options. \"But you said own the stock instead of the YieldMax ETF, what a hypocrite!\" some might think. Well I do own NVDA stock as well. NVDA is 13.77% of my portfolio, much larger than my NVDY position.\n\nIt's your money, invest in whatever you want. But it makes sense before you invest your hard-earned money to understand what you are investing in so you know if it makes sense for you. Don't just look at dividend yield.\n\nHappy New Year!", "author": "Jumpy-Imagination-81", "created_time": "2025-01-01T21:11:58", "url": "https://reddit.com/r/dividends/comments/1hrcnef/yieldmax_etfs_and_share_price_nav_erosion/", "upvotes": 63, "comments_count": 83, "sentiment": "bearish", "engagement_score": 229.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hrd4zp", "title": "Musk say Tesla investigating Cybertruck fire in Las Vegas. ", "content": "", "author": "Minimac1029", "created_time": "2025-01-01T21:33:31", "url": "https://reddit.com/r/wallstreetbets/comments/1hrd4zp/musk_say_tesla_investigating_cybertruck_fire_in/", "upvotes": 1586, "comments_count": 570, "sentiment": "neutral", "engagement_score": 2726.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hrdlpw", "title": "My Journey with Digital Products and MRR", "content": "PS. I was meant to post this 3 days ago. Hope it's not late\n\nAs we count down to the final days of the year, I find myself reflecting on how much my life has changed since I started selling digital products back in July. With just two days left in 2024, I thought it would be the perfect time to share my journey. Who knows? Maybe this post will inspire someone to take the leap and set themselves up for a more fulfilling 2025.\n\n*Why I Started*:\n\nEarlier this year, I was stuck. After losing a job and spending countless weeks submitting applications that went unanswered, I felt defeated. I’d scroll through Instagram and TikTok, seeing people talk about making money online, some of them earning more in a month than I could dream of in a year. I wasn’t just envious; I was curious.\n\nBut I didn’t have a massive following or products to sell. I was a 22-year-old with nothing more than a smartphone, a reliable internet connection, and a burning desire to make something happen. That’s when I came across digital marketing and digital products.\n\n*The Early Struggles:*\n\nLet me be real: the first three months were brutal. I was completely lost, spending hours on Google and YouTube trying to piece together how to get started. There were moments of doubt—“What if I fail? What if this is just another scam?” But the frustration wasn’t enough to stop me.\n\nI finally invested in a course (UBC) and let me tell you, it was a game-changer. It wasn’t just about marketing or selling; it was about learning how to build a brand, connect with people, and market effectively without being pushy or salesy. Most importantly, it came with 1:1 mentorship, which helped me figure out what I was doing wrong and how to fix it.\n\n*The Turning Point:*\n\nOnce I pivoted and started applying what I learned in the course, everything changed. My first sale felt like magic-it wasn’t a lot, but it was proof that this could work. By the second month, I hit my first 4-figure milestone.\n\nNow, I’m averaging 4 figures monthly, and while it’s not six figures (yet), it’s consistent and growing. For someone who was completely broke just a few months ago, this has been life-changing.\n\n*The Process:*\n\nHere’s how I approached selling digital products:\n\n* **Platforms:** I use Beacons for hosting and selling my products, with a Beacons link in my social media bio to drive traffic.\n* **Promotion:** Everything I do is organic—no paid ads, no fancy setups. I use Instagram, TikTok, and Threads to connect with people. The best part? I don’t show my face! You can absolutely succeed without being on camera.\n* **Focus:** It’s not about spamming people with sales pitches. I focus on building genuine connections, creating content that educates and inspires, and showing people how my products can help them.\n\n*Expectations vs. Reality:*\n\nIf you’re thinking about starting, here’s the truth:\n\n* The income is real, the flexibility is unmatched, and the ability to scale is incredible. Whether you’re selling templates, eBooks, or courses, there’s no inventory, no overhead, and no shipping headaches. Plus, it’s a skill you can carry into any future business.\n* It’s not “easy” money. You’ll need to put in work, especially in the beginning. There’s a learning curve, and some months may feel slow. Consistency and adaptability are key.\n\n*What I’ve Learned:*\n\nThis journey has taught me so much about online selling, content creation, and even myself. I’ve learned how to stay consistent even when things feel slow, how to pivot when something isn’t working, and how to celebrate the small wins along the way.\n\n*Why I’m Sharing This Now:*\n\nAs 2025 approaches, I know many people are looking for ways to earn extra income or even start a side hustle. If you’ve been hesitant, let this be your sign. You don’t need to have it all figured out; you just need to start.\n\nIf you’ve got questions about digital products, marketing, or how I got started, drop them in the comments. I’d be happy to share more about what worked for me.\n\n  \nEdit/Update: I just made $500 on the second day of 2025, and I wanted to share this moment with you because it’s proof that one decision can truly change your life.", "author": "WayRevolutionary1", "created_time": "2025-01-01T21:54:07", "url": "https://reddit.com/r/DigitalMarketing/comments/1hrdlpw/my_journey_with_digital_products_and_mrr/", "upvotes": 0, "comments_count": 11, "sentiment": "bearish", "engagement_score": 22.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hreoi1", "title": "Cybertruck catches fire outside Trump hotel and explodes.", "content": "", "author": "ventoreal_", "created_time": "2025-01-01T22:42:48", "url": "https://reddit.com/r/teslainvestorsclub/comments/1hreoi1/cybertruck_catches_fire_outside_trump_hotel_and/", "upvotes": 0, "comments_count": 62, "sentiment": "neutral", "engagement_score": 124.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hrfduu", "title": "$TSLA expolsion as a chance for $KULR", "content": "Here’s what I’m thinking: a Tesla exploded. It doesn’t really matter if it was caused by fireworks in the trunk or something else—Elon will likely address it somehow. There’s a good chance his response will include a statement emphasizing Tesla’s commitment to battery safety, the fact that their batteries are reliable, and even ongoing efforts to improve safety further. My first thought? KULR. Even if Elon stays silent, the market might still make the connection: TSLA  safety = batteries  + KULR.\n\nThese are just my loose thoughts—I’m not claiming this will definitely happen, nor that KULR would be the only beneficiary of such a scenario. The market often reacts emotionally and speculatively, and everyone should assess for themselves whether they see an opportunity here. \nBut as golden rule saying : buy rumors , sell the news", "author": "mrK0z01", "created_time": "2025-01-01T23:14:44", "url": "https://reddit.com/r/pennystocks/comments/1hrfduu/tsla_expolsion_as_a_chance_for_kulr/", "upvotes": 0, "comments_count": 10, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hrfmip", "title": "AV maintenance challenges", "content": "Hey all!  \nI've been diving into the challenges of unplanned maintenance for fleets and have been exploring how predictive maintenance can help reduce downtime and provide better visibility for fleet managers.\n\nWith the AV sector growing rapidly, I've been wondering, would predictive maintenance tools be particularly valuable for AV fleet managers? From my understanding, autonomous vehicles have unique maintenance requirements, like sensor calibration, software updates, and even environmental considerations, that traditional fleets don't face.\n\nDo you think AV fleets would benefit from a predictive maintenance tool tailored to their specific needs, or is the current fleet management tech enough to handle these challenges? I'd love to hear your thoughts—especially from those working with AVs or fleet management!", "author": "No_Imagination1698", "created_time": "2025-01-01T23:26:07", "url": "https://reddit.com/r/AutonomousVehicles/comments/1hrfmip/av_maintenance_challenges/", "upvotes": 2, "comments_count": 7, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}]