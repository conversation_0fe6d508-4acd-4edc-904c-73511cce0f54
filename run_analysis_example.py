#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对冲基金系统代理信号分析 - 快速使用示例

该脚本提供了几个预设的分析示例，用户可以直接运行来分析不同的实验数据。
"""

import os
import sys
from pathlib import Path
from analyze_agent_signals import AgentSignalAnalyzer

def analyze_experiment(experiment_name, output_subdir=None):
    """分析指定的实验"""
    experiment_path = Path("reasoning_logs") / experiment_name
    
    if not experiment_path.exists():
        print(f"❌ 实验路径不存在: {experiment_path}")
        return False
    
    print(f"🔍 正在分析实验: {experiment_name}")
    
    # 设置输出目录
    if output_subdir:
        output_dir = Path("charts") / output_subdir
    else:
        output_dir = Path("charts")
    
    try:
        # 创建分析器
        analyzer = AgentSignalAnalyzer(experiment_path)
        
        # 加载数据
        print("📊 正在加载数据...")
        data = analyzer.load_agent_data()
        
        if not data:
            print("❌ 未找到有效数据")
            return False
        
        print(f"✅ 成功加载 {len(data)} 天的数据")
        
        # 统计代理数量
        all_agents = set()
        for date_data in data.values():
            all_agents.update(date_data.keys())
        
        print(f"📈 发现 {len(all_agents)} 个代理:")
        for agent in sorted(all_agents):
            display_name = analyzer.agent_name_mapping.get(agent, agent)
            print(f"   - {display_name}")
        
        # 创建可视化
        print("🎨 正在生成可视化图表...")
        analyzer.create_comprehensive_visualization(data, output_dir)
        
        print("✅ 分析完成！")
        return True
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        return False

def list_available_experiments():
    """列出可用的实验"""
    reasoning_logs_path = Path("reasoning_logs")
    
    if not reasoning_logs_path.exists():
        print("❌ reasoning_logs 目录不存在")
        return []
    
    experiments = []
    for item in reasoning_logs_path.iterdir():
        if item.is_dir() and item.name.startswith("experiment_"):
            experiments.append(item.name)
    
    return sorted(experiments)

def main():
    print("🚀 AI对冲基金系统代理信号分析工具")
    print("=" * 50)
    
    # 列出可用的实验
    experiments = list_available_experiments()
    
    if not experiments:
        print("❌ 未找到任何实验数据")
        print("请确保 reasoning_logs 目录存在且包含实验数据")
        return
    
    print(f"📁 发现 {len(experiments)} 个实验:")
    for i, exp in enumerate(experiments, 1):
        print(f"   {i}. {exp}")
    
    print("\n" + "=" * 50)
    
    # 预设的分析示例
    examples = [
        {
            'name': 'AAPL - Gemini 2.0 Flash',
            'path': 'experiment_AAPL_20250101-20250601_gemini2.0_flash',
            'output': 'aapl_gemini'
        },
        {
            'name': 'MSFT - GPT-3.5',
            'path': 'experiment_MSFT_20250101-20250601_gpt3.5',
            'output': 'msft_gpt35'
        },
        {
            'name': 'MSFT - Grok Beta',
            'path': 'experiment_MSFT_20250101-20250601_grok_beta',
            'output': 'msft_grok'
        },
        {
            'name': 'NVDA - Gemini 2.0',
            'path': 'experiment_NVDA_20250101-20250601_gemini2.0',
            'output': 'nvda_gemini'
        },
        {
            'name': 'NVDA - GPT-3.5',
            'path': 'experiment_NVDA_20250101-20250601_gpt3.5',
            'output': 'nvda_gpt35'
        },
        {
            'name': 'NVDA - Grok Beta',
            'path': 'experiment_NVDA_20250101-20250601_grok-beta',
            'output': 'nvda_grok'
        },
        {
            'name': 'NVDA - Llama4 Scout',
            'path': 'experiment_NVDA_20250101-20250601_llama4-scout',
            'output': 'nvda_llama'
        }
    ]
    
    print("🎯 预设分析示例:")
    for i, example in enumerate(examples, 1):
        print(f"   {i}. {example['name']}")
    
    print(f"   {len(examples) + 1}. 自定义分析")
    print(f"   {len(examples) + 2}. 批量分析所有实验")
    
    try:
        choice = input(f"\n请选择要执行的分析 (1-{len(examples) + 2}): ").strip()
        
        if not choice.isdigit():
            print("❌ 请输入有效的数字")
            return
        
        choice = int(choice)
        
        if 1 <= choice <= len(examples):
            # 执行预设示例
            example = examples[choice - 1]
            print(f"\n🎯 执行分析: {example['name']}")
            analyze_experiment(example['path'], example['output'])
            
        elif choice == len(examples) + 1:
            # 自定义分析
            print("\n📝 自定义分析")
            print("可用的实验:")
            for i, exp in enumerate(experiments, 1):
                print(f"   {i}. {exp}")
            
            exp_choice = input(f"请选择实验 (1-{len(experiments)}): ").strip()
            if exp_choice.isdigit() and 1 <= int(exp_choice) <= len(experiments):
                selected_exp = experiments[int(exp_choice) - 1]
                output_name = input("输出子目录名称 (可选，直接回车使用默认): ").strip()
                analyze_experiment(selected_exp, output_name if output_name else None)
            else:
                print("❌ 无效的选择")
                
        elif choice == len(examples) + 2:
            # 批量分析
            print("\n🔄 批量分析所有实验")
            success_count = 0
            
            for exp in experiments:
                print(f"\n{'='*30}")
                if analyze_experiment(exp):
                    success_count += 1
            
            print(f"\n✅ 批量分析完成！成功分析 {success_count}/{len(experiments)} 个实验")
            
        else:
            print("❌ 无效的选择")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")

if __name__ == "__main__":
    main()
