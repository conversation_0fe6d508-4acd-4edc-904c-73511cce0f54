[{"platform": "reddit", "post_id": "reddit_1jfesog", "title": "To those of you defending Google here", "content": "What’s Google search worth?\n\nSpecifically, as someone who worked at Google, here’s my take: \n\nGoogle Search will definitely have less market share in the future than it does today. GenAI makes it too easy for tens of companies — Meta, OpenAI, Microsoft, Apple, Anthropic, Perplexity, etc. etc. — to provide search for a meaningful fraction of query use cases. The trillion dollar question is whether the pie will grow so fast that Google’s profits will stay steady or grow.\n\nMeanwhile, the government is threatening two sources of distribution: the Apple deal and Chrome. \n\nOutside of this, Google feels healthy to downright exciting. YouTube is increasing in relevance as a Netflix + TikTok combo. Google Cloud is on a tear. Waymo could 10x from here. Android gives them distribution for new software products and Android + Pixel gives them a full stack alternative to Apple (I’d say the worst position Apple’s been in in years because of their track record with AI). Deepmind + Gemini could result in new businesses. And the rest of core Google like Maps, Gmail, and Docs offers a bunch of surface area to monetize.\n\nSo the real question is: what’s the right multiple for Search?", "author": "thefrogmeister23", "created_time": "2025-03-20T02:30:23", "url": "https://reddit.com/r/ValueInvesting/comments/1jfesog/to_those_of_you_defending_google_here/", "upvotes": 244, "comments_count": 197, "sentiment": "bullish", "engagement_score": 638.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jfkd3s", "title": "EU throws down gauntlet to <PERSON> with Apple, Google rulings", "content": "", "author": "doopityWoop22", "created_time": "2025-03-20T08:43:02", "url": "https://reddit.com/r/worldnews/comments/1jfkd3s/eu_throws_down_gauntlet_to_trump_with_apple/", "upvotes": 1699, "comments_count": 132, "sentiment": "neutral", "engagement_score": 1963.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jfn0h2", "title": "Did OpenAI lose its way and momentum to keep up?", "content": "Okay, hear me out, I’ve been diving into the AI scene lately, and I’m starting to wonder if OpenAI’s hitting a bit of a wall. Remember when they were the name in generative AI, dropping jaw-dropping models left and right? Lately, though, it feels like they’re struggling to keep the magic alive. From what I’ve seen, they can’t seem to figure out how to build a new general model that actually improves on the fundamentals like better architecture or smarter algorithm efficiency. Instead, their whole strategy boils down to one trick: crank up the computing power by, like, 100 times and hope that brute force gets them over the finish line.\n\nNow, don’t get me wrong, this worked wonders before. Take the jump from gpt-3.5 to gpt-4. That was a legit game changer. The performance boost was massive, and it felt like they’d cracked some secret code to AI domination. But then you fast-forward to gpt4 to gpt-4.5, and it’s a different story. The improvement? Kinda underwhelming. Sure, it’s a bit better, but nowhere near the leap we saw before. And here’s the kicker: the price tag for that modest bump is apparently 15 times higher than gpt-4o. I don’t know about you, but that sounds like diminishing returns screaming in our faces. Throwing more compute at the problem clearly isn’t scaling like it used to.\n\nMeanwhile, the rest of the field isn’t sitting still. Google’s out here playing a totally different game with Gemini 2.0 Flash. They’ve gone all-in on optimizing their architecture, and it’s paying off killer performance, super efficient, and get this: it’s priced at just $0.4 per 1 million token output . That’s pocket change compared to what OpenAI’s charging for their latest stuff. Then there’s DeepSeek, absolutely flexing with the DeepSeek R1, it’s got performance damn near matching o1, but 30 times cheaper. That’s not just a small step forward; that’s a giant leap. And if that wasn’t wild enough, Alibaba just swooped in with QwQ-32B. A 32b model that’s going toe-to-toe with the full 671b model.\n\nIt’s got me wondering: has OpenAI painted itself into a corner? Are they so locked into this “moar compute” mindset that they’re missing the forest for the trees? Google and DeepSeek seem to be proving you can do more with less if you rethink the approach instead of just piling on the hardware. I used to think OpenAI was untouchable, but now it feels like they might be losing their edge—or at least their momentum. Maybe this is just a temporary stumble, and they’ve got something huge up their sleeve. Or maybe the competition’s starting to outmaneuver them by focusing on smarter, not just bigger.\n\nWhat’s your take on this? Are we watching OpenAI plateau while others race ahead? Or am I overreacting to a rough patch? Hit me with your thoughts—I’m genuinely curious where people think this is all heading!", "author": "Own-Entrepreneur-935", "created_time": "2025-03-20T11:47:39", "url": "https://reddit.com/r/singularity/comments/1jfn0h2/did_openai_lose_its_way_and_momentum_to_keep_up/", "upvotes": 3, "comments_count": 63, "sentiment": "bearish", "engagement_score": 129.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jfzh0o", "title": "Where can I post a google survey for business owner market research?", "content": "I am trying to find out what business owners are looking for in UX designers, I created a google survey to take down the responses, but these subs are not liking it because they think it's spam. help? advice? tips?", "author": "FrenchieHoneytoast", "created_time": "2025-03-20T20:58:51", "url": "https://reddit.com/r/Entrepreneur/comments/1jfzh0o/where_can_i_post_a_google_survey_for_business/", "upvotes": 2, "comments_count": 4, "sentiment": "neutral", "engagement_score": 10.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jfzj8r", "title": "Where can I post a google survey for business owner market research? (I will not promote)", "content": "I am trying to find out what business owners are looking for in UX designers, I created a google survey to take down the responses, but these subs are not liking it because they think it's spam. help? advice? tips?\n\nI have to get to 250 characters, so that's what this sentence is. I will not promote.", "author": "FrenchieHoneytoast", "created_time": "2025-03-20T21:01:16", "url": "https://reddit.com/r/startups/comments/1jfzj8r/where_can_i_post_a_google_survey_for_business/", "upvotes": 0, "comments_count": 3, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}]