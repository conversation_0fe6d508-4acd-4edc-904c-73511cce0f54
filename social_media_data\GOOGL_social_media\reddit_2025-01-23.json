[{"platform": "reddit", "post_id": "reddit_1i7rg6w", "title": "Google’s <PERSON> is already winning the next-gen assistant wars", "content": "", "author": "early-retirement-plz", "created_time": "2025-01-23T01:06:52", "url": "https://reddit.com/r/wallstreetbets/comments/1i7rg6w/googles_gemini_is_already_winning_the_nextgen/", "upvotes": 717, "comments_count": 146, "sentiment": "neutral", "engagement_score": 1009.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i7tfv1", "title": "Does Google analyze their own website using Google Analytics?", "content": "It feels like a bit of an analytics inception moment—tracking the tracker! ", "author": "AffectionateSong8", "created_time": "2025-01-23T02:45:43", "url": "https://reddit.com/r/analytics/comments/1i7tfv1/does_google_analyze_their_own_website_using/", "upvotes": 6, "comments_count": 6, "sentiment": "neutral", "engagement_score": 18.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i7viyz", "title": "Google skipped Biden", "content": "", "author": "Available-Drink-5232", "created_time": "2025-01-23T04:37:26", "url": "https://reddit.com/r/google/comments/1i7viyz/google_skipped_biden/", "upvotes": 9517, "comments_count": 499, "sentiment": "neutral", "engagement_score": 10515.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i82li2", "title": "<PERSON> Nvidia, Apple And Loads Up On Google-Parent Alphabet And Amazon Calls", "content": "", "author": "FUSeekMe69", "created_time": "2025-01-23T12:48:44", "url": "https://reddit.com/r/economy/comments/1i82li2/nancy_pelosi_sells_nvidia_apple_and_loads_up_on/", "upvotes": 21, "comments_count": 7, "sentiment": "neutral", "engagement_score": 35.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i8cw4y", "title": "Google restores <PERSON> to ‘U.S. presidents’ search results, blames ‘data error’ for omission", "content": "", "author": "m<PERSON><PERSON>", "created_time": "2025-01-23T20:18:45", "url": "https://reddit.com/r/technology/comments/1i8cw4y/google_restores_joe_biden_to_us_presidents_search/", "upvotes": 22420, "comments_count": 919, "sentiment": "neutral", "engagement_score": 24258.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i8dc93", "title": "Google restores <PERSON> to ‘U.S. presidents’ search results, blames ‘data error’ for omission", "content": "", "author": "ControlCAD", "created_time": "2025-01-23T20:37:27", "url": "https://reddit.com/r/google/comments/1i8dc93/google_restores_joe_biden_to_us_presidents_search/", "upvotes": 1908, "comments_count": 63, "sentiment": "neutral", "engagement_score": 2034.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i8hp9v", "title": "How to Start a Data Analyst Career With No Degree or Certificates?", "content": "Hey everyone,\n\nI’m really interested in starting a career as a data analyst, but I don’t have a college degree or any certifications. I’m starting completely from scratch with zero experience. I know it’s a competitive field, but I’m ready to put in the work.\n\nCould you help me figure out:\n\n1. What skills I need to learn to get started?\n2. Which certifications (if any) are worth pursuing to build credibility?\n3. How I can gain experience when I don’t have any professional background in data analysis?\n\nHere’s what I know so far:\n\n* Data analysts work a lot with tools like Excel, SQL, Tableau, and Python/R.\n* I need to understand concepts like data cleaning, visualization, and reporting.\n* Communication skills are important to present findings.\n\nBut what would be the best path for someone like me? Should I dive into free resources online? Are there specific entry-level jobs that can help me transition into data analysis? How can I start building a portfolio to showcase my skills without professional experience?\n\nI’m also wondering about certifications like Google Data Analytics, Microsoft Power BI, or even Coursera/edX courses—are they really necessary, or can I get by with just self-study and practice?\n\nAny advice, personal stories, or resources would be super helpful! Thanks in advance!", "author": "shomeeee", "created_time": "2025-01-23T23:45:43", "url": "https://reddit.com/r/analytics/comments/1i8hp9v/how_to_start_a_data_analyst_career_with_no_degree/", "upvotes": 0, "comments_count": 41, "sentiment": "neutral", "engagement_score": 82.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i8ht62", "title": "General Investing Advice", "content": "Hey there, I am relatively new to Reddit, and this is my first post. A lot of you seem to really know what you’re doing, I would like some advice. \n\nI rolled over my 401k into an IRA. It was not a large amount, only $3,081. I did some research and ended up buying 7 shares of Google, and 7 shares of NVDA yesterday. I plan on buying more of those, and also buying Microsoft and Apple. I have a lot of catching up to do, I will need to max out my IRA every year. \n\nI have $677.41 left over. I left that there for cheap stocks or coins that I can pump and dump. What stocks or coins would you recommend for short term gain? What information sources do you use to get this knowledge? I want to be in the loop so to speak. I wasn’t aware of the Trump or Melania coin that went crazy. Perfect pump and dump opportunity that I missed out on. Basically, I don’t want to miss any more big opportunities like that. I’m open to any and all suggestions. Thank you for reading this. ", "author": "qwerty7769", "created_time": "2025-01-23T23:50:52", "url": "https://reddit.com/r/investing_discussion/comments/1i8ht62/general_investing_advice/", "upvotes": 1, "comments_count": 6, "sentiment": "bullish", "engagement_score": 13.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}]