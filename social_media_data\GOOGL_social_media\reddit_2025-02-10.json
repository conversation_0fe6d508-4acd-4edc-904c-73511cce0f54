[{"platform": "reddit", "post_id": "reddit_1ilv0hu", "title": "Google Calendar removed events like Pride and BHM because its holiday list wasn’t ‘sustainable’ | Google says it switched to only showing default entries for public holidays and national observances last year.", "content": "", "author": "ControlCAD", "created_time": "2025-02-10T01:46:20", "url": "https://reddit.com/r/google/comments/1ilv0hu/google_calendar_removed_events_like_pride_and_bhm/", "upvotes": 890, "comments_count": 207, "sentiment": "neutral", "engagement_score": 1304.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1im8agn", "title": "BYD Releases \"God's Eye\" Autonomous Driving, Here's Driver Footage", "content": "", "author": "IcyHowl4540", "created_time": "2025-02-10T15:11:13", "url": "https://reddit.com/r/AutonomousVehicles/comments/1im8agn/byd_releases_gods_eye_autonomous_driving_heres/", "upvotes": 6, "comments_count": 0, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1imd65e", "title": "Link Building in 2025 - Strategies for success, and what's changed?", "content": "Hi - I wrote something similar last year, and it garnered some good response along with a lot of questions regarding different elements of link building and how to go about it as a business owner.\n\nThe reality is that we don’t 100% know all the elements the HCU entailed or changed. But In my experience, links have become a bit more impactful from what I've seen since the HCU and early 2024 updates, and are still an incredibly powerful ranking factor. Again, the sub is full of bad and nonsensical advice and commentary in regards to link building (elements of good, too) - its an incredibly (in my experience) logical process that people oftentimes overthink - however, it's time consuming to do it properly which is why so many people (agencies AND business owners) cut corners and get subpar results. \n\nBelow, I’m sharing some information I mainly wrote in response to questions from business owners that could potentially benefit others. If you know link building well, this may not be useful to you but hopefully this can help some business owners create a more powerful profile of links.\n\nThere’s a mixture of strategy and discourse. Again, if you’re right at the start of your journey, my earlier posts outline some of the more general aspects of link building.\n\nEach of these are essentially strategies and changes we’ve used over the last year.\n\nIt’s important to note with all things that this is what worked for me and my clients - there are other views and methodologies that might have worked just as well for others - and that these are my own opinions based on what I’ve seen rather than universally accepted concepts.\n\n# Stopped Using Link Inserts\n\nFor this client, they’d been using link inserts for a long period of time with mixed results. Every now and then they’d get a *small* bump followed by a retraction. The strategy just wasn’t working. One of the issues was that, as a large B2B machinery seller in the financial sector, the weak link inserts previously procured just weren't moving the needle for the more difficult keywords. Before we look at the strategy - I just wanted to run through link inserts in a bit more detail…\n\nThey’ve always been a cheaper option - and can sometimes be effective. However, there’s a way to get the best out of them. A way that the majority of large “link building agencies” don’t use or really care about due to the volume they’re processing. Unfortunately, its led to misinformation in general about what works best for link inserts.\n\nI find the best way to look at them is in a kind of tier system. This is just something that's in my own head, but it might help you out. Remember, link inserts, in my opinion, rarely beat post placements because with a post, you can completely control the breadth of content that sits around the link, allowing you to get the best from it entirely. With a link insert, the content isn’t primed to drive your link in the best possible way. Anyway:\n\nTier one: A link that's thrown into content that isn’t even indexed on google.\n\nIn our opinion these are the lowest of the low (though some might think otherwise) - and usually what these agencies procure on mass for their clients (or other agencies outsourcing to them). Doesn’t matter if the website is decent, if the page the link is in isn’t indexed, it’s going to do near nothing! \n\nIf you’re procuring a link insert yourself - check the content you want it inserted into is at least indexed on google! You can do this with a simple site:(webpage) search on google itself. \n\nIn the case above, upon investigation, these were mainly the links procured for the client up until we started working together.\n\nTier two: A link in a page that’s indexed\n\nIts better because its indexed. However, here you have to make sure the content is worthwhile, isn’t terrible, and ties in with your own link. \n\nYou don’t just want to throw your link into a page just because its indexed. Sure, you might be able to reword some of it, and potentially add in a paragraph that surrounds the link - but it has to be contextually relevant to what the link leads to. \n\nThe client had a few of these too, some moderately relevant, but no consistency. \n\nTier three: a link in content that ranks on google\n\nNow we’re getting somewhere. The content actually ranks on google - it isn’t just indexed…its ranked for terms. This means google is passing the content/page value…its saying that essentially it trusts the page enough to show it to people. A link here is clearly more valuable than the above. Again - the content has to be on point, and you can’t just throw your link into any content…there has to be relevancy. With that said - a link in content that ranks, if done right, will usually pull.\n\nThe client had none of these…\n\nTier four: A link in content that ranks for industry specific keywords\n\nThese are great, because the keywords are completely related to you, and to what you do. Difficult to get, but completely worthwhile.\n\nTier five: A link in content that ranks for what you’re trying to rank for\n\nA holy grail - but usually out of reach. These work incredibly well usually - but most sites aren’t going to link to a competitor from a page that ranks for a keyword they’re trying to beat them in - but it can be done in certain niches and situations. \n\nRemember - the content also has to be right when you’re looking at link inserts, this is just illustrative of the different kinds out there without really looking at assessing the website or content - its a way of highlighting how you can leverage getting a good link insert out of your provider.\n\nMost bought are tier 1 - a good agency won’t get you these kind of inserts (a great one will use inserts sparingly anyway - instead curating content that gives your link the best chance of doing well) - but this gives you an idea of how to leverage something out of it if buying them for yourself or assessing a provider.\n\nNow - back to the client, they sell large machinery with some pretty tough keywords to crack. The agencies previously primarily were using tier one and two above…so no real efficacy, on pages with weak relevancy.\n\nBy pivoting to content curation, we were able to write for the target website while really making the most out of the link in the content we’ve written. We focused down on websites in the B2B niche as well as websites within the niches that would use this kind of software - the link inserts previously were just slapped into any kind of weakly relevant content. Remember, with link inserts, the content has been written for another purpose (maybe even for another link) - so you’re usually better off putting content together. The differentiation here got them where they wanted to be within 4 months, and when you think they’d spent years building crappy link inserts it speaks volumes.\n\nThe main takeaway here is you can’t cut corners. You either need to get GOOD link inserts, or curate the content yourselves and you’ll see results if consistent. It boils down to logic. It also kind of shows how so many do this wrong (either due to lack of knowledge, or because they just can’t be bothered to do it right). \n\nDon’t just slap your links into any kind of content - Pivot to placing content written to support your link.\n\n# Link Velocity: How Many?\n\nWhen a website goes viral or hits the headlines - however old - it accrues a tonne of links and nothing bad happens. \n\nThe reality is that to an extent and from what we’ve seen - numbers don’t matter, what matters is the quality of the links you’re getting. The more quality links the better. Some do stand by numbers, and may have evidence to back that up - but in my experience quality stands on its own two feet where quantity doesn’t so well.\n\nOne quality link can be worth hundreds of other links. You can get good links as often as you can. Never pass up a great opportunity because you think you’ve got too many in a month or a year. \n\nFor a brand new website - it’s good to take a well planned out and measured approach. Different kinds of sites that are all of a certain quality, pertaining to the niche in question always drive value.\n\nIn this case - We began working with a brand new brand in the B2C niche selling a popular ecom product. Competitors had thousands of links and a lot of business owners will jump to a (logical to be fair) conclusion that they need to match those links. Instead, focus on quality, and focus on making up a solid link profile that makes sense. In doing this, we matched and then surpassed the competitors for primary keywords after 6 months with 20% the amount of links that they had.\n\nQuality over quantity. What makes a quality site is the real question - but don’t just look at the number of referring domains, the domains themselves are whats important. Many people focus on the number of referring domains - its the quality level of domains - not the amount! \n\n# Competitor Sniping? The links may not work in the same way\n\nThis is an easy one - but worth a mention. The clients were between 4th and 6th for their main keyword - selling a consumer product similar to kids water toy, huge volume etc. \n\nTheir link building was essentially copying whatever links their main (and leading) competitor was procuring. \n\nI’ve seen many clients and individuals who have come to me and said ‘I’ve copied my competitors link profile, but its not really worked and they’re still higher than me etc.”\n\nSometimes links won’t work in the same was as they have for someone else. Sniping a competitors profile can work, and if there are any epic websites in the profile it can be worth trying to secure your own link. However, you’d be better off in the long run simply focusing on procuring your own link profile. Simply because of the quirks of link building, copying a competitor profile may disappoint you if it doesn’t give you a similar boost. Don’t spend all your time on your competitors' profile! There are many variables in play.\n\nThis is a perfect example of why opinions can and do differ - because sometimes the same methodology works differently from one site to the next.\n\nCompetitor link sniping can work as part of a wider link acquisition strategy, but it shouldn’t be all you do.\n\n# Try out the first person (in Content)\n\nThe majority of articles are written in the third person. It’s logical, easier, makes more sense. Sometimes it can also make sense to vary things as best possible and push out some first person posts. There was a single product ecom client in a really niche industry, but the keyword was nails. They were getting the right kind of link profile bit by bit, but every single bit of content they put out there on other sites (which held the link) was super similar.\n\nIn fairness - it's hard to make each article unique with their one product store that fits a very slim user profile. However, we varied the content and made 70% of new posts 1st person - this works well for a few reasons:\n\n* It really looks like the owner of the website/blog etc., has written the content - it feels more immediate and real.\n* It stands out - if done well, it breaks the monotony of the same posts going to similar websites - meaning there could be more chance of ranking (especially if the “author” is recognised as an expert. If it ranks, the link will be more powerful instantly.)\n\nThere are some drawbacks - if not done right, the article can read too promotional. It needs to be as neutral as possible. In this case, changing a load of link placements so that they’re in first person written content worked really well and pushed them into a gaining position.\n\nVary the voice, tone, and person of the articles you’re placing on other websites. In the real world, the articles wouldn’t be too similar if it were happening naturally - again, create a believable link profile and use varied content to achieve this. You’re not just creating single links - you’re creating a varied profile, first person content can be part of this.\n\n# Write for the Website or the link?\n\nPeople get confused with this - do you write for the link, or the website?\n\nThe two will tie over slightly because logically the site you’ve targeted will naturally be in the same niche as your business. The best bet is to write for the website - because it gives you more chance of being published - and looks like the website owner has written the content.\n\nHowever, you have to give your link the best chance of success too. So - you curate the content in a certain way that's not promotional, but as if the website author has just naturally linked to it as if it would be a good resource, good product, on point information etc.  \n\nSo what you need to do is write for the theme of the website your publishing on - while focusing the niche/minutiae on your own websites intent. Takes practice but all you need to do is put yourself in the website owners shoes when writing the content.\n\n# Create Your Own Network\n\nPBNs are usually referred to in a negative light, in a lot of cases this is justified. It’s because a lot of PBNs are from spammy link farms with spoofed traffic etc., owned by one person who rarely does things right.\n\nHowever, if you have the time and inclination you can build a logical one of your own.\n\nYou essentially would build blogs around the service/product you’re selling - then link from the blogs logically to the service. For example say you own an electric bike website, you might build a couple of content sites:\n\n* A blog on electric bike laws in different states\n* A blog on electric bike reviews\n* A blog on best places to use an electric bike\n* A blog on electric bike maintenance tips etc.\n\nIf you rank all of these for logical terms, the links from them to your electric bike shop will be pretty powerful. \n\nHowever, its limited by how many blogs you can create. Also, if the keywords you’re targeting are incredibly difficult, you’ll need links from other relevant blogs/websites in any case. \n\nLastly - it is eminently time consuming. However, this is an example of a workable PBN.\n\nAgain, a decent strategy depending on the use case and KW difficulty you’re going for.\n\n# Website Traffic: Quality over Quantity\n\nWeb traffic is a main website assessment metric. However, a lot of people use it in the wrong way. Most people now know (not all) that focusing on DA/DR etc. as a way to assess a website is a one way ticket to at best, a link that does nothing and a quick way to burn through your cash. So, we look at site traffic instead. We often consult on external link campaigns, on one, a client was approving any links (from their internal marketing team) with traffic over 5k - that was their only barometer, traffic over 5k. There are multiple things wrong here.\n\n* The traffic might be coming from a country that the client business doesn’t even operate in. \n* The traffic might be coming from completely fake/nonsense sources\n* The keywords the site ranks for might also be complete nonsense (meaning the traffic means nothing or is just fake and spoofed).\n\nSo - instead of focusing on traffic numbers - focus on where the traffic is coming from. Instead of looking at quantity, go for quality. Here - we taught the team to look at what the site is ranking for, and whether or not they’re relevant in the grand scheme of the campaign. By focusing on this instead of the blind numbers, they’re not only getting websites that rank for relevant terms to link to them, but sites with real traffic. In this case - a site with 2k relevant and real traffic is better than one with 50k nonsense anyday! \n\nNumbers can be good if you’re assessing two sites with real traffic against each other - obviously then, if you’ve the budget, you go for the larger one as seemingly Google is passing that one more (relevant) traffic (for whatever reason). \n\nIn the end - remember, you’re trying to create a profile of believable links to your website. You’ll need different kinds of websites (while keeping relevancy and quality in mind.) This is where so many go wrong - because they tend to snatch at links here and there and don’t focus on building a mutually beneficial portfolio of links. Hope this helped with your link building campaign. Again - this is what I've observed and what's worked for me. Other approaches may be just as viable.", "author": "Character_Ad_1990", "created_time": "2025-02-10T18:29:24", "url": "https://reddit.com/r/SEO/comments/1imd65e/link_building_in_2025_strategies_for_success_and/", "upvotes": 648, "comments_count": 72, "sentiment": "bearish", "engagement_score": 792.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1imdv4o", "title": "I Feel Guilty For Owning A Tesla", "content": "Sorry for the poor formatting and editing: I am writing this by phone\n\nI started majoring in EECS at MIT in 2018 as a 17 year old boy and later graduated in 2022, having held 3 internships by then (an unpaid internship in 2017, a paid internship at a software company in 2019, and a FAANG internship in 2020). \n\nAfter graduation, I was thinking of taking a paid temporary software consulting position/business opportunity as an aspiring AI entrepreneur for Summer 2023 in California (it never happened) to experience living there. That fueled my obsession for a car. Also, I have been a car enthusiast since childhood and ever since I was 11 (before <PERSON><PERSON> shifted right), I have dreamed of owning a Tesla. \n\nSince Spring of 2022, I have been intermittently working as a remote software consultant for several major tech companies whilst also working on my AI projects/research, and investing, mainly for financial stability, and that meant I have the money for a car for occasional cross country road trips, weekend tours around the Northeast of the US, and Doordashing as a hobby (for a purpose of going outside since I am mostly remote, outside the days where I meet in person, mostly for my tech startup).\n\nEven though I had a drivers licence since 2019 (18 years old), owning a car is a liability where I live (given the off street parking price per month and the fact I would have to move my car very often if I was street parking), so I held back on owning a car (despite having at least a million in assets due to the Tesla stock boom in 2020-1).\n\nUntil Summer 2022.\n\nIn November 2021, I sold my stock (half the stock I owned was Tesla) just as recession fears started unraveling due to inflation. I poured in at least 75 thousand USD of my internship money for Tesla in October 2019, and I received a huge ROI.\n\nDue to the fact Teslas were still overpriced as of August 2022 and Elon Musk's views started to shift towards the far right (I was already concerned about his lockdown views and move to Texas by then), I never considered a Tesla.\n\nI was also turned down by used cars because used cars were overpriced (August 2022 was the peak), so I instead resorted to a new car. I looked towards a Toyota Corolla, Mazda3, and Hyundai Elantra, but then, when Tesla lowered the Model 3 prices, I still didn't consider Tesla due to the fact I wanted to wait and see what the Highland and Juniper offers.\n\nI kept on bleating about the Hyundai Elantra (early 2023) until the Kona came out, and when the Volvo EX30 came out in June (I was in Milan the day before the EX30 was announced), I decided to switch my obsession towards Volvo. \n\nFunnily, my 75 year old father in Vietnam who is a prominent healthcare official and multi millionaire in Vietnam actually owns a Volvo XC40, which motivated me to switch my minds to a Volvo. Before 2021, he owned a slew of Mercedes Benz E Classes for 28 years ranging from the W124 to the W213. \n\nEven though the Tesla Model 3 has a proven track record compared with the Volvo EX30, in my mind, the Volvo has better build quality, support for Apple CarPlay/Android Auto, includec safety and autonomous features such as auto park assist, etc, and is more manueverable as it is 165 inches in length and I live in Cambridge.\n\nThen comes September 2023. The Tesla Model 3 Highland came out and by the time EX30 reviews came in Europe in November, I started slowly switching my minds back to the Tesla Model 3 as in my beliefs, the Highland is more likely to come out to the US before the EX30.\n\nThat is despite the fact Elon has gone further right and despite the fact I further despise Elon Musk for his unhingedness.\n\nIn January 2024, the Highland was released and based on my analysis, not much has changed, so I decided to go with a used Tesla due to me not qualifying for tax rebates/credits.\n\nIn June 2024, I upgraded my Scion XD (which is ageing and rusting) to a 2021 Tesla Model 3 for 25k and I chose Tesla for a few reasons:\n\nIn Cambridge, EV infrastructure is decent (my city offers municipal chargers which cost 20 cents per kwh and are half a mile away from my apartment). In my calculation, that is kind of like owning a 55 mpg Prius and using a $3 per gallon gas station \n\nI am also planning to move to another apartment soon, which meant I might be able to have an off street parking solution, which meant I might be able to use a Level 1 charger, which would further facilitate charging. That in turn makes charging cheaper than gas. I am also an environmentalist, so I support EVs, public transportation, and biking\n\nWhen charging outside, using a Tesla Supercharger on a Tesla is cheaper than using a Tesla Supercharger whilst also having a dongle on a Ford/Rivian and having to pay a subscription. Tesla Supercharger price per kwh is also more affordable than Electrify America, Chargepoint, or EVGo\n\nTesla Model 3 has a more efficient watt hours per mile than other cars, further lessening the costs\n\nThe technology is better in some areas (say the infotainment OS) and worse in others\n\nIt is also easier to find a Model 3 than a Mach E/Ioniq 5/BMW i4/Polestar/Volvo XC40 recharge/EV6 (six of my runner ups) for under 25k\n\nWith all of this said, I bought a Tesla Model 3, and even though I love my Tesla (apart from a few issues), I feel kind of alienated from my Tesla, due to Elon's shift towards Nazi salutes, sabotaging the economy through DOGE, and being fully entrenched in the far right. He has even attempted to infiltrate into Germany's political system, trying to lobby for the AfD to win nationwide.\n\nElon's recent antics made me feel uncomfortable and it made me consider selling my Tesla at a loss, even if I really love the car.\n\nI am just a frugal man. I don't want to bear the financial loss, be broke, and not have the money to funnel into my tech startup.\n\nI am scared people might perceive me as a far right sympathizer for liking my Tesla. But the fact is, I despise fascism.\n\nAlso, I do understand that Henry Ford espoused some racist views, that VW/VAG was founded by Hitler, and that Toyota has become so anti environment.", "author": "MussleGeeYem", "created_time": "2025-02-10T18:57:22", "url": "https://reddit.com/r/electricvehicles/comments/1imdv4o/i_feel_guilty_for_owning_a_tesla/", "upvotes": 0, "comments_count": 105, "sentiment": "bearish", "engagement_score": 210.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1imdz70", "title": "Autonomes Fahren BYD vs Tesla - autonomous driving BYD vs Tesla — Hive", "content": "", "author": "blkchnDE", "created_time": "2025-02-10T19:01:35", "url": "https://reddit.com/r/AutonomousVehicles/comments/1imdz70/autonomes_fahren_byd_vs_tesla_autonomous_driving/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ime684", "title": "Google AI Chief Say<PERSON>Seek’s Cost Claims Are ‘Exaggerated’", "content": "", "author": "F0urLeafCl0ver", "created_time": "2025-02-10T19:08:55", "url": "https://reddit.com/r/artificial/comments/1ime684/google_ai_chief_says_deepseeks_cost_claims_are/", "upvotes": 13, "comments_count": 20, "sentiment": "neutral", "engagement_score": 53.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1imjmxt", "title": "Dear Autonomous Vehicle industry - AV is already taken", "content": "On behalf of the Audio Visual industry around the world, I'd like to kindly request that the Autonomous Vehicle industry picks another acronym besides AV.\n\nThe Audio Visual industry has been using AV now for several decades, and recently we've been finding more and more search results being peppered with pages where people have shortened Autonomous Vehicle to AV.\n\nCould I suggest:  \nSDV - Self-Driving Vehicle\n\nHAV - Highly Automated Vehicle\n\nSAE - Self-Automated Vehicle or Self-Automated Equipment\n\nCAV - Connected and Automated Vehicle\n\n.\n\n\n\nMay your audio be crystal clear and your video pixel-perfect. Yours truly,\n\nThe Audio Visual Industry", "author": "HitchmoMcStang", "created_time": "2025-02-10T22:51:57", "url": "https://reddit.com/r/AutonomousVehicles/comments/1imjmxt/dear_autonomous_vehicle_industry_av_is_already/", "upvotes": 0, "comments_count": 7, "sentiment": "bearish", "engagement_score": 14.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1imjpxn", "title": "Apple does not have a Google Graveyard • 9to5Mac", "content": "In 2025, in addition to **Numbers, Notes** and **Shortcuts,** I still have **Texas Hold’em**  on my iPhone 15 Pro. Not a gamer’s app, by any stretch, but still an app dating back to my iPhone 3G. \n\nNot every app has to iterate beyond 1.x. Despite owning **Minecraft,** I’m pretty sure Microsoft leaves **Minesweeper** alone. \n\nJust as <PERSON><PERSON><PERSON> predicts, I believe **Invites** will get rolled into **Calendar** within the next two OS cycles. ", "author": "CouscousKazoo", "created_time": "2025-02-10T22:55:41", "url": "https://reddit.com/r/apple/comments/1imjpxn/apple_does_not_have_a_google_graveyard_9to5mac/", "upvotes": 746, "comments_count": 134, "sentiment": "bearish", "engagement_score": 1014.0, "source_subreddit": "apple", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1imjwyg", "title": "Gulf of America or Mexico?", "content": "I caught Google glitching in the map.\n\nWhen I zoomed in on Gulf of Mexico it became Gulf of America and continues to do it when repeated.\n\nWhat a weird glitch. (Posting in comments.)\n\nEdit. Grammar error sorry. ", "author": "Botman7x", "created_time": "2025-02-10T23:04:06", "url": "https://reddit.com/r/GoogleMaps/comments/1imjwyg/gulf_of_america_or_mexico/", "upvotes": 16, "comments_count": 37, "sentiment": "neutral", "engagement_score": 90.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1imkyc2", "title": "Google Maps now shows the ‘Gulf of America’", "content": "", "author": "ObjectiveOrange3490", "created_time": "2025-02-10T23:51:30", "url": "https://reddit.com/r/technology/comments/1imkyc2/google_maps_now_shows_the_gulf_of_america/", "upvotes": 18233, "comments_count": 4068, "sentiment": "neutral", "engagement_score": 26369.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}]