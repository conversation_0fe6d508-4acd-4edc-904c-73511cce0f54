[{"platform": "reddit", "post_id": "reddit_1kb36te", "title": "Waymo Personalization has begun..", "content": "Waymo, Toyota strike partnership to bring self-driving tech to personal vehicles \n\nhttps://www.cnbc.com/2025/04/29/waymo-toyota-partner-to-bring-self-driving-tech-to-personal-vehicles-.html?__source=androidappshare\n\n", "author": "qqww80", "created_time": "2025-04-30T00:15:47", "url": "https://reddit.com/r/google/comments/1kb36te/waymo_personalization_has_begun/", "upvotes": 88, "comments_count": 20, "sentiment": "neutral", "engagement_score": 128.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kb3dbv", "title": "Toyota And Google's Waymo May Build An Autonomous Car Platform Together", "content": "", "author": "Lucky_Chainsaw", "created_time": "2025-04-30T00:24:41", "url": "https://reddit.com/r/electricvehicles/comments/1kb3dbv/toyota_and_googles_waymo_may_build_an_autonomous/", "upvotes": 48, "comments_count": 7, "sentiment": "neutral", "engagement_score": 62.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kb3y39", "title": "Toyota And Google's Waymo May Build An Autonomous Car Platform Together", "content": "", "author": "PointSuccessful956", "created_time": "2025-04-30T00:53:21", "url": "https://reddit.com/r/electriccars/comments/1kb3y39/toyota_and_googles_waymo_may_build_an_autonomous/", "upvotes": 9, "comments_count": 3, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kb5h2n", "title": "How old are these batteries?", "content": "I got an old camera with film and batteries still inside. The camera is most likely from the 1930s, the film (Kodak Verichrome Pan) was in production until the 90s, so I think the batteries are my best bet to figure out when this camera was last used.", "author": "TankArchives", "created_time": "2025-04-30T02:10:42", "url": "https://reddit.com/r/batteries/comments/1kb5h2n/how_old_are_these_batteries/", "upvotes": 1337, "comments_count": 87, "sentiment": "neutral", "engagement_score": 1511.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kb66ix", "title": "Will Robots Replace Humans Or Become Our Helpers?", "content": "In the world of cutting-edge technology and innovation, the question on everyone's mind is: Will Robots Replace Humans Or Become Our Helpers? Join us as we delve into the latest advancements in AI, robotics, and automation, featuring breakthroughs from pioneers like Tesla, Boston Dynamics, and OpenAI. From humanoid robots like Atlas and Unitree to the futuristic Tesla Optimus, we'll explore the potential of artificial intelligence to revolutionize industries and transform our daily lives. With insights from experts in the field, we'll examine the possibilities and challenges of AI integration, and what it means for the future of work and humanity. Get ready for a fascinating journey into the world of AI news, tech breakthroughs, and the future of technology.", "author": "Es<PERSON><PERSON><PERSON>", "created_time": "2025-04-30T02:47:17", "url": "https://reddit.com/r/youtube/comments/1kb66ix/will_robots_replace_humans_or_become_our_helpers/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kb78xl", "title": "Alternatives to Google Maps that has a media player control while on the map?", "content": "Driving mode was great. It's gone. I do not know why Google keeps doing shit like this, because literally all I want is to have my navigation and to have a really big button to pause and play media. \n\nI have a smart phone and a dumb car, and I am driving this car into the ground for longer than <PERSON><PERSON> ever have any phone. \n\nSo, is there a reasonable alternative to Google Maps that I can put on my android phone and have a media player to pause and play? ", "author": "Lonely-Somewhere-385", "created_time": "2025-04-30T03:45:40", "url": "https://reddit.com/r/GoogleMaps/comments/1kb78xl/alternatives_to_google_maps_that_has_a_media/", "upvotes": 42, "comments_count": 16, "sentiment": "bullish", "engagement_score": 74.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbcqi6", "title": "Am I romanticizing working in big digital companies like Microsoft, Google, and Meta?", "content": "People who've worked in big tech always made it sound prestigious. But now I’m wondering if it’s just a “wow” factor, or if I should really make it my goal. Would love to hear your real-life take—how’s your experience at work, and what led you to work there?", "author": "Next_Examination3015", "created_time": "2025-04-30T10:02:16", "url": "https://reddit.com/r/marketing/comments/1kbcqi6/am_i_romanticizing_working_in_big_digital/", "upvotes": 50, "comments_count": 96, "sentiment": "neutral", "engagement_score": 242.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbe2bt", "title": "It Finally Happend it. Rejected for Not Using AI First", "content": "So I just got rejected from a software dev job, and the email was... interesting.\n\nYesterday, I had an interview with CEO of a startup that sounded cool. Their tech stack was mainly Ruby and migrating to Elixir, and I had three interviews: one with HR, another was a CoderByte test, and then a technical discussion with the team. The final round was with the CEO, who asked about my approach to coding and how I incorporate AI into my development process. I said something like, \"You can’t vibe your way to production. LLMs are too verbose, and their code is either insecure or tries to write basic functions from scratch instead of using built-in tools. Even when I used Agentic AI in my small hobby project, it struggled to add a simple feature. I use AI as smarter autocomplete, not a crutch.\"\n\nFast forward five minutes after the interview, and I got an email with this line:\n\n\n\"Thank you for your time. We’ve decided to move forward with someone who prioritizes AI-first workflows to maximize productivity and shape the future of tech.\"\n\n\nHere’s the thing: I respect innovation, I’m not saying LLMs are completely useless. But I’m not gonna let an AI write entire code for a feature for me. They’re great for brainstorming or breaking down tasks, but when you let them dictate the logic, it’s a mess. And yes, their code is often wildly overengineered and insecure.\n\nTo be honest, I’m pissed off. I was laid off a few months ago, and this was the first company to actually respond to my application and I made it all the way to the final round and I was optimistic. I keep reviewing the meeting in my mind, where did I fuck up? did I come up as an Elitist dick but I didn't make fun of vibe coders and I wasn't completely dismissive of LLMs either.\n\nanyway I wanted to vent here.\n\n\n**EDIT: I want to say I apperciate everybody comments here and multiple users have pointed out I was coming out as too negative, I felt that I framed in a way that I use copilot to increase my productivity but not do my job for me without supervision but I guess I failed to convey that, multiple people mentioned using the sandwich method and I would do that in the future.\n\nsome suggested I reach out to the CEO to explain my position clearly but I think I will come out as deseprate and probably rejected anyway.**", "author": "supermedo", "created_time": "2025-04-30T11:26:25", "url": "https://reddit.com/r/webdev/comments/1kbe2bt/it_finally_happend_it_rejected_for_not_using_ai/", "upvotes": 4592, "comments_count": 853, "sentiment": "neutral", "engagement_score": 6298.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k<PERSON><PERSON>", "title": "How do you still win despite Google's featured snippets and AI overviews?", "content": "I'm curious as to how do you still win with SEO despite Google's featured snippets and AI overviews taking almost 50% of traffic. You also have knowledge panels, local pack and Google's \"People Also Ask.\" \n\nGoogle now scrapes your content, summarizes it, and serves it on a silver platter with no thank-you note, or no traffic.\n\nI'm asking all SEO pros out there: - What do you do for your company or brand that still lets you win despite these taking over majority of traffic?", "author": "Tea_J95", "created_time": "2025-04-30T12:15:35", "url": "https://reddit.com/r/DigitalMarketing/comments/1kbey<PERSON>/how_do_you_still_win_despite_googles_featured/", "upvotes": 20, "comments_count": 32, "sentiment": "neutral", "engagement_score": 84.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbhda6", "title": "Game Ready Driver 576.28 FAQ/Discussion", "content": "# Game Ready Driver 576.28 has been released.\n\n# If you cannot find the driver in NVIDIA Website Search or not showing in NVIDIA App, please give it time to propagate.\n\n**Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/geforce-rtx-april-30-2025-game-ready-driver/)\n\n**Game Ready Driver Direct Download Link**: [Link Here](https://us.download.nvidia.com/Windows/576.28/576.28-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n# New feature and fixes in driver 576.28:\n\n# Game Ready\n\nThis new Game Ready Driver resolves several issues which were found in the previous release.\n\n# Fixed Gaming Bugs\n\n* **FIXED** \\[RTX 50 series\\] \\[Black Myth\\]: The game will randomly crash when <PERSON><PERSON>g transforms \\[5231902\\]\n* **FIXED** \\[RTX 50 series\\] \\[Red Dead Redemption 2\\]: The game crashes shortly after starting in DX12 mode. No issue in Vulkan mode \\[5137042\\]\n* **FIXED** \\[RTX 50 series\\] \\[Horizon Forbidden West\\]: The game freezes after loading a save game \\[5227554\\]\n* **FIXED** \\[RTX 50 series\\] Grey screen crashes with multiple monitors \\[5239138\\]\n* **FIXED** \\[RTX 50 series\\] \\[Dead Island 2\\]: The game crash after updating to GRD 576.02 \\[5238676\\]\n* **FIXED** \\[RTX 50 series\\] \\[Resident Evil 4 Remake\\]: Flickering background textures \\[5227655\\]\n* **FIXED** \\[RTX 50 series\\] Some games may display shadow flicker/corruption after updating to GRD 576.02 \\[5231537\\]\n* **FIXED** \\[RTX 50 series\\] Some games may crash while compiling shaders after updating to GRD 576.02 \\[5230492\\]\n* **FIXED** \\[Forza Horizon 5\\]: Lights flicker at nighttime \\[5038335\\]\n* **FIXED** \\[Forza Motorsport\\]: Track corruption occurs in benchmark or night races. \\[5201811\\]\n\n# Fixed General Bugs\n\n* **FIXED** \\[RTX 50 series\\] Lower idle GPU clock speeds after updating to GRD 576.02 \\[5232414\\]\n* **FIXED** \\[RTX 50 series\\] Momentary display flicker occurs when running in DisplayPort2.1 mode with a high refresh rate \\[5009200\\]\n* **FIXED** Lumion 2024 crashes on GeForce RTX 50 series graphics card when entering render mode \\[5232345\\]\n* **FIXED** GPU monitoring utilities may stop reporting the GPU temperature after PC wakes from sleep \\[5231307\\]\n* **FIXED** \\[RTX 50 series\\] \\[LG 27GX790A/45GX950A/32GX870A/40WT95UF/27G850A\\]: Display blank screens when running in DisplayPort 2.1 mode with HDR \\[5080789\\]\n* **FIXED** \\[RTX 50 series notebook\\] Resume from Modern Standby can result in black screen \\[5204385\\]\n* **FIXED** \\[RTX 50 series\\] SteamVR may display random V-SYNC micro-stutters when using multiple displays \\[5152246\\]\n\n# Open Issues\n\n**Includes additional open issues from** [**GeForce Forums**](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/564384/geforce-grd-57628-feedback-thread-released-43025/)\n\n* \\[RTX 50 series\\] Cyberpunk 2077 will crash when using Photo Mode to take a screenshot with path tracing enabled \\[5076545\\]\n* \\[RTX 50 series\\] Flickering/corruption around light sources in Ghost of Tsushima Directors Cut \\[5138067\\]\n* \\[Monster Hunter Wilds\\] Random stability issues \\[5204023\\]\n* \\[RTX 50 series\\] Dragons Dogma 2 displays shadow flicker \\[5252205\\]\n* \\[RTX 50 series\\] Dead Space Remake displays shadow flicker \\[5241013\\]\n* \\[RTX 50 series\\] EA Sports FC 25 may crash during gameplay \\[5251937\\]\n* \\[RTX 50 series\\] Video playback in a web browser may show brief red/green flash corruption \\[5241341\\]\n* Wuthering Waves may randomly crash during gameplay after updating to R575 drivers \\[5259963\\]\n* \\[GeForce RTX 50 series\\] F1 23/F1 24 crashes at the end of a race \\[5240429\\]\n* \\[SCUM\\] Game may crash after updating to R575 drivers \\[5257319\\]\n* \\[RTX 50 series\\] Diablo II Resurrected displays black screen corruption when using DLSS \\[5264112\\]\n\n# Driver Downloads and Tools\n\n**Information & Documentation**\n\n* Driver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n* Latest Game Ready Driver: 576.28 WHQL - [Game Ready Driver Release Notes](https://us.download.nvidia.com/Windows/576.28/576.28-win11-win10-release-notes.pdf)\n* Latest Studio Driver: 576.02 WHQL - [Studio Driver Release Notes](https://us.download.nvidia.com/Windows/576.02/576.02-win10-win11-nsd-release-notes.pdf)\n\n**Feedback & Discussion Forums**\n\n* **Submit driver feedback directly to NVIDIA:** [**Link Here**](https://forms.gle/kJ9Bqcaicvjb82SdA)\n* NVIDIA 576.28 Driver Forum: [Link Here](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/564384/geforce-grd-57628-feedback-thread-released-43025/)\n* [r/NVIDIA](https://new.reddit.com/r/NVIDIA/) Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\n**Having Issues with your driver and want to fully clean the driver? Use DDU (Display Driver Uninstaller)**\n\n* DDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n* DDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n* DDU/WagnardSoft Patreon: [Link Here](https://www.patreon.com/wagnardsoft)\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue -** [**Link Here**](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/564384/geforce-grd-57628-feedback-thread-released-43025/)\n\n**There is only one real way for any of these problems to get solved**, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n* **Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations between hardware and software. Driver will never be perfect and there will always be issues for some people. Two people with the same hardware configuration might not have the same experience with the same driver versions. Again, I encourage folks who installed the driver to post their experience here good or bad.", "author": "Nestledrink", "created_time": "2025-04-30T14:05:29", "url": "https://reddit.com/r/nvidia/comments/1kbhda6/game_ready_driver_57628_faqdiscussion/", "upvotes": 607, "comments_count": 2343, "sentiment": "bearish", "engagement_score": 5293.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbj1f3", "title": "Google is working on a big UI overhaul for Android: Here's an early look", "content": "", "author": "Xisrr1", "created_time": "2025-04-30T15:16:53", "url": "https://reddit.com/r/GooglePixel/comments/1kbj1f3/google_is_working_on_a_big_ui_overhaul_for/", "upvotes": 696, "comments_count": 294, "sentiment": "neutral", "engagement_score": 1284.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbj26z", "title": "Google is working on a big UI overhaul for Android: Here's an early look", "content": "", "author": "Xisrr1", "created_time": "2025-04-30T15:17:45", "url": "https://reddit.com/r/Android/comments/1kbj26z/google_is_working_on_a_big_ui_overhaul_for/", "upvotes": 648, "comments_count": 299, "sentiment": "neutral", "engagement_score": 1246.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbjrja", "title": "Spent $570 on Google Ads, 627 Clicks, 0 Real Leads", "content": "I’ve been running Google Ads for a commercial cleaning company for 19 days and spent $570 total across two campaigns:\n\n* A Search campaign (about $244 spent)\n* A Performance Max campaign (rest of the budget — I paused it today after after I added some videos and it was running up the budget with no results)\n\nAcross both campaigns, I’ve gotten:\n\n* 627 clicks\n* 13.8K impressions\n* 0 actual leads\n\nI’ve had a real form\\_submit conversion set up in GA4 since day one — it works and tracks perfectly when tested. The issue is: no one has clicked on an ad and actually submitted the form, so Google Ads has never tracked a single real conversion. That means the algorithm has no conversion data to optimize off of.\n\nEarly on, I had a second conversion that fired on contact page load with the hopes that it would optimize around this and someone that made it to that page would send a form and I could change it. However, it just gave me 135 fake conversions. I’ve removed that from primary actions so it doesn’t mess with performance tracking anymore.\n\nMy Search campaign is running broad and phrase match keywords, with a decent list of negative keywords filtering out stuff like “cleaning jobs,” “supplies,” etc. The landing page is clean — Webflow-built, short form, strong CTA, licensed/bonded/insured trust language — all looks good on my end.\n\nIf anyone has ideas on why no one is converting or what I should change to get more conversions or fix my conversion tracking. I just do not want to be spending so much with nothing to show for it.", "author": "Abocado2", "created_time": "2025-04-30T15:47:17", "url": "https://reddit.com/r/adwords/comments/1kbjrja/spent_570_on_google_ads_627_clicks_0_real_leads/", "upvotes": 8, "comments_count": 10, "sentiment": "bearish", "engagement_score": 28.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbjycc", "title": "Spent $570 on Google Ads, 627 Clicks, 0 Real Leads", "content": "I’ve been running Google Ads for a commercial cleaning company for 19 days and spent $570 total across two campaigns:\n\n* A Search campaign (about $244 spent)\n* A Performance Max campaign (rest of the budget — I paused it today after after I added some videos and it was running up the budget with no results)\n\nAcross both campaigns, I’ve gotten:\n\n* 627 clicks\n* 13.8K impressions\n* 0 actual leads\n\nI’ve had a real form\\_submit conversion set up in GA4 since day one — it works and tracks perfectly when tested. The issue is: no one has clicked on an ad and actually submitted the form, so Google Ads has never tracked a single real conversion. That means the algorithm has no conversion data to optimize off of.\n\nEarly on, I had a second conversion that fired on contact page load with the hopes that it would optimize around this and someone that made it to that page would send a form and I could change it. However, it just gave me 135 fake conversions. I’ve removed that from primary actions so it doesn’t mess with performance tracking anymore.\n\nMy Search campaign is running broad and phrase match keywords, with a decent list of negative keywords filtering out stuff like “cleaning jobs,” “supplies,” etc. The landing page is clean — Webflow-built, short form, strong CTA, licensed/bonded/insured trust language — all looks good on my end.\n\n I just do not want to be spending so much with nothing to show for it. Even tried finding my own ad and submitting my own form so that google ads recognizes what my real conversion is and that it is working but I was unable to even find my own ad. If anyone has ideas on why no one is converting or what I should change to get more conversions or fix my conversion tracking please let me know.", "author": "Abocado2", "created_time": "2025-04-30T15:55:07", "url": "https://reddit.com/r/marketing/comments/1kbjycc/spent_570_on_google_ads_627_clicks_0_real_leads/", "upvotes": 16, "comments_count": 25, "sentiment": "bearish", "engagement_score": 66.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbjzac", "title": "Spent $570 on Google Ads, 627 Clicks, 0 Real Leads", "content": "I’ve been running Google Ads for a commercial cleaning company for 19 days and spent $570 total across two campaigns:\n\n* A Search campaign (about $244 spent)\n* A Performance Max campaign (rest of the budget — I paused it today after after I added some videos and it was running up the budget with no results)\n\nAcross both campaigns, I’ve gotten:\n\n* 627 clicks\n* 13.8K impressions\n* 0 actual leads\n\nI’ve had a real form\\_submit conversion set up in GA4 since day one — it works and tracks perfectly when tested. The issue is: no one has clicked on an ad and actually submitted the form, so Google Ads has never tracked a single real conversion. That means the algorithm has no conversion data to optimize off of.\n\nEarly on, I had a second conversion that fired on contact page load with the hopes that it would optimize around this and someone that made it to that page would send a form and I could change it. However, it just gave me 135 fake conversions. I’ve removed that from primary actions so it doesn’t mess with performance tracking anymore.\n\nMy Search campaign is running broad and phrase match keywords, with a decent list of negative keywords filtering out stuff like “cleaning jobs,” “supplies,” etc. The landing page is clean — Webflow-built, short form, strong CTA, licensed/bonded/insured trust language — all looks good on my end.\n\nI just do not want to be spending so much with nothing to show for it. Even tried finding my own ad and submitting my own form so that google ads recognizes what my real conversion is and that it is working but I was unable to even find my own ad. If anyone has ideas on why no one is converting or what I should change to get more conversions or fix my conversion tracking please let me know.", "author": "Abocado2", "created_time": "2025-04-30T15:56:12", "url": "https://reddit.com/r/Entrepreneur/comments/1kbjzac/spent_570_on_google_ads_627_clicks_0_real_leads/", "upvotes": 3, "comments_count": 8, "sentiment": "bearish", "engagement_score": 19.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbk48f", "title": "Google confirms it’s close to getting Gemini support on iPhones", "content": "", "author": "favicondotico", "created_time": "2025-04-30T16:01:49", "url": "https://reddit.com/r/apple/comments/1kbk48f/google_confirms_its_close_to_getting_gemini/", "upvotes": 773, "comments_count": 196, "sentiment": "neutral", "engagement_score": 1165.0, "source_subreddit": "apple", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kbok1a", "title": "Google Ads Competitor Analysis", "content": "As you can see my impression share is higher than my competitors and their ranking is better. I am not very satisfied with my clicks and conversion. I am using the click strategy. What do you think my competitors are doing?", "author": "Time-Requirement-705", "created_time": "2025-04-30T19:05:13", "url": "https://reddit.com/r/adwords/comments/1kbok1a/google_ads_competitor_analysis/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}]