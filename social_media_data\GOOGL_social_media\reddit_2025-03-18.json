[{"platform": "reddit", "post_id": "reddit_1je1p92", "title": "PPC Salary Survey 2025 Final Report - 10th Year Edition", "content": "Howdy Y'All\n\nThis is our 10th year doing the salary survey. It only feels like yesterday we got started on this.\n\nWe got 830 responses this year. Countries/regions are listed in alphabetical as we got 120+ slides. For reporting, the bar is 20 for the USA and 10 for the rest of world to show a country, region, province/state or a city.\n\nI want to give a special shout out to Portugal this year as they got their own slide. Our community members from India keep showing up and getting their own sections again this year. It is great to see us continue to brach out and collect more data from around the world.\n\nAlso, the Netherlands cracked the top 3 countries this year for the first time. They knocked out Canada for the top 3rd spot for number of responses. Congrats to each country.\n\n**Some Notes**\n\n* Top 6 countries now has a slide to show how much data we get from each one\n* Even less currency conversions to do this year. Remote work seems harder to come by, unless more people are getting paid in their local currency. A few people who do work remote are paid very well vs their local PPCers.\n* Some people have 1-3 years experience in paid but having been working for 8-10 years, thus they can skew salaries higher.\n* Some people include their bonus in their salaries I imagine. This can make their salary higher than someone who might not have. Hence why we try to use the median salary across all reports\n\n**Results Served Two Ways**\n\n[Google Slides 2025 Salary Survey](https://docs.google.com/presentation/d/1_DGxTu_UARHFmRYO5cp7qLSybxlWq3WgpKx8708hu9c/edit?usp=sharing)\n\n**or**\n\n[PDF 2025 Salary Survey](https://drive.google.com/file/d/16mOwBiRlS7UQPk6oXSly_1LF7GUIn8OU/view)\n\nThanks you for helping make this happen. I spend a couple weeks on this project each year and it's truly interesting to see the data doing this labour of love project.\n\nIf you see a mistake or you think something is off, let me know in the comments or DM me and I'll look into it. This folder has [past salary survey](https://drive.google.com/drive/folders/1gQIsjgag-E0nAgHoR5w7iufHmtRfP-JB?usp=share_link) results.", "author": "fathom53", "created_time": "2025-03-18T10:20:54", "url": "https://reddit.com/r/PPC/comments/1je1p92/ppc_salary_survey_2025_final_report_10th_year/", "upvotes": 144, "comments_count": 69, "sentiment": "neutral", "engagement_score": 282.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1je2kqs", "title": "Help! I'm embarrassed by the quality of the videos with the front camera", "content": "I bought the Pixel 9 Pro because I want to try it, I'm fascinated by the Pixel Experience, its speed, its not getting lost in useless functions, its being essential and at the same time a complete smartphone.\n\nThe photos are really beautiful, and I'm also surprised by the selfies from the front camera.\n\nBut here I discovered something that doesn't seem possible on a top smartphone.\n\nThe videos recorded with the front camera have something wrong, they have a very low quality, they are literally unusable.\n\nThey are even better if recorded by third-party apps, although they remain unusable.\n\nThey seem to have been recorded by an Android smartphone from 2012.\n\nWith a little less light, embarrassing lines appear.\n\nAs much as I hate Video Boost because I can't keep up with its times to have my video \"ready\" to use, why not include it for videos made with the front? This makes the smartphone unusable for those who are creators and want something different from an iPhone.\n\nSelfies are processed very well after the shot, videos are embarrassing and unjustifiable.\n\nIs there any way to fix this? Even with third-party apps?\n\nI leave you here the examples of my pajamas in a selfie, and in a video.\n\n  \nSelfie: [https://imgur.com/a/K9uP39G](https://imgur.com/a/K9uP39G)  \nVideo: [https://imgur.com/a/9OISLMe](https://imgur.com/a/9OISLMe)", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-18T11:17:37", "url": "https://reddit.com/r/GooglePixel/comments/1je2kqs/help_im_embarrassed_by_the_quality_of_the_videos/", "upvotes": 1, "comments_count": 31, "sentiment": "bullish", "engagement_score": 63.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1je3bod", "title": "$GOOG & $GOOGL buy WIZ start up $32 Billion", "content": "\n\n$GOOG & $GOOGL buy WIZ start up $32 Billion\n\n[https://finance.yahoo.com/news/google-agrees-buy-cybersecurity-startup-114156877.html](https://finance.yahoo.com/news/google-agrees-buy-cybersecurity-startup-114156877.html)", "author": "Othe-un-dots", "created_time": "2025-03-18T12:03:22", "url": "https://reddit.com/r/wallstreetbets/comments/1je3bod/goog_googl_buy_wiz_start_up_32_billion/", "upvotes": 1256, "comments_count": 221, "sentiment": "bullish", "engagement_score": 1698.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1je5h9k", "title": "AMA: Former Google Employee, Now Running A Growth Marketing Agency", "content": "<PERSON><PERSON><PERSON>; was employed at Google for many years, started as an SMB rep, grew very quickly, pivoted roles several times, oversaw millions in budget, left for a series of very high profile startups, now running an agency.\n\nFeel free to ask me anything related to optimizing Google Ads, the rep programs, etc. Will do my best to answer within reason.", "author": "Competitive-Day2034", "created_time": "2025-03-18T13:53:30", "url": "https://reddit.com/r/Entrepreneur/comments/1je5h9k/ama_former_google_employee_now_running_a_growth/", "upvotes": 1, "comments_count": 17, "sentiment": "bullish", "engagement_score": 35.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1je6mzu", "title": "How YouTube can have dubbing for any video?", "content": "I just found a YouTube video on PetaPixel:\n\n* [Tesla Autopilot Car Drove Into a Giant Photo of a Road](https://petapixel.com/2025/03/17/tesla-autopilot-car-drove-into-a-giant-photo-of-a-road/)\n\nFor a channel of <PERSON>, and the sound have a Polish dubbing. When I open the video on YouTube it's in English, but it has a translated title.\n\nWhat kind of feature YouTube is using for the embedded video?\n\nCan you turn this feature on when watching a video on YouTube? Do you know any documentation of information how this works?", "author": "jcubic", "created_time": "2025-03-18T14:45:29", "url": "https://reddit.com/r/youtube/comments/1je6mzu/how_youtube_can_have_dubbing_for_any_video/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1je6tg9", "title": "Roast my Tesla idea", "content": "Realistically, I'm NOT pursuing this. But I still think given the current political climate there is a market for this and am curious what yall think. \n\n\nNews has lots of people protesting teslas. Social media has posts of people destroying teslas on the street. Regardless of what side you stand on politically, you know that a ton of people want to protest and watch tesla die. The idea would be to make a YouTube channel in which you get tesla owners to trade in their teslas for a Lucid (or other competing electric vehicle). You buy them the lucid, and take ownership of the tesla. Then you find creative ways to destroy the tesla.\n\n\nI think this has a built in audience and at the moment has a high chance of going viral. Cost to produce one of these videos would be high because it would basically cost the price of a car. In an ideal world you could partner with a car company after a few of these. Would be great for them. I also think there might be enough political passion to help pay for those cars via a go fund me. After a video or two of these you will likely get other tesla owners wanting to participate as part of a protest movement. \n\n\nIm sure there are people on both sides, I'm not trying to turn this into a political discussion. I'm just curious as an idea what your thoughts are? ", "author": "juggling-monkey", "created_time": "2025-03-18T14:53:18", "url": "https://reddit.com/r/Entrepreneur/comments/1je6tg9/roast_my_tesla_idea/", "upvotes": 0, "comments_count": 15, "sentiment": "bullish", "engagement_score": 30.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1je7emu", "title": "Twitch Streamers Uploading Youtube Videos Watching Other Youtube Videos is getting worse.", "content": "https://preview.redd.it/kf8fvb5nsgpe1.png?width=1254&format=png&auto=webp&s=13a1fc52fca2c843c1bd4caa9278241dfac84ead\n\nThere was a grey area in my opinion when the thumbnail was of the ORIGNAL video thumbnail with just the reactor's face added to it. At least it was a quick visual to see \"This is a reaction video and the real video is this one without the reactor's face added to it\"\n\nThis video is reacting to <PERSON>'s new video and at no point, not in the thumbnail nor the name of the video do you see that.\n\nThe channel put the \"Original Credit to\" and links to <PERSON>'s video in the description but I think this isn't enough credit but I would like to hear some thoughts from others.", "author": "RallyXMonster", "created_time": "2025-03-18T15:18:35", "url": "https://reddit.com/r/youtube/comments/1je7emu/twitch_streamers_uploading_youtube_videos/", "upvotes": 7, "comments_count": 1, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1je86qh", "title": "Google to acquire cloud security startup Wiz for $32 billion", "content": "", "author": "ControlCAD", "created_time": "2025-03-18T15:51:59", "url": "https://reddit.com/r/business/comments/1je86qh/google_to_acquire_cloud_security_startup_wiz_for/", "upvotes": 71, "comments_count": 7, "sentiment": "neutral", "engagement_score": 85.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jebihe", "title": "how to become a cloud engineer?", "content": "so , i have taken cloud computing as an specilization and i know nothing about it , still i have more then 3 years to prepare about it and i trust that my college that they are not going to teach me about the specific until its too late , so please help me and provide a roadmap or atleast tell me from where to start\n\nedit : ignore the typo ", "author": "shaleen0", "created_time": "2025-03-18T18:07:11", "url": "https://reddit.com/r/cloudcomputing/comments/1jebihe/how_to_become_a_cloud_engineer/", "upvotes": 13, "comments_count": 12, "sentiment": "neutral", "engagement_score": 37.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jeddbc", "title": "Game Ready & Studio Driver 572.83 FAQ/Discussion", "content": "# Game Ready Driver 572.83 has been released. LOTS of bug fixes\n\n# If you cannot find the driver in NVIDIA Website Search or not showing in NVIDIA App, please give it time to propagate.\n\n**Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/half-life-2-rtx-demo-inzoi-geforce-game-ready-driver/)\n\n**Game Ready Driver Download Link**: [Link Here](https://us.download.nvidia.com/Windows/572.83/572.83-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n**Studio Driver Download Link**: [Link Here](https://us.download.nvidia.com/Windows/572.83/572.83-desktop-win10-win11-64bit-international-nsd-dch-whql.exe)\n\n**New feature and fixes in driver 572.83:**\n\n**Game Ready** \\- This new Game Ready Driver supports the new GeForce RTX 5070 Ti GPU and provides the best gaming experience for the latest new games supporting DLSS 4 technology including the Half-Life 2 RTX Demo and Warhammer 40,000: Darktide. Further support for titles leveraging DLSS technology includes Assassin's Creed Shadows, The Last of Us Part II Remastered, and the enhanced update for Control. In addition, this driver supports inZOI which features the first integration of NVIDIA ACE technology. And there’s support for 61 new and updated NVIDIA app DLSS overrides.\n\n**Gaming Technology** \\- Adds support for the GeForce RTX 5090, 5080, and 5070 Ti notebooks\n\n**Applications** \\- The March NVIDIA Studio Driver provides optimal support for the latest new creative applications and updates including the official release of Remix, ChatRTX support for new NVIDIA Inference Microservices (NIMs), and enhanced Blackwell support within OctaneRender.\n\n**Fixed Gaming Bugs**\n\n* N/A\n\n**Fixed General Bugs**\n\n* \\[GeForce RTX 5080/5090\\] Graphics cards may not run at full speeds on system reboot when overclocked \\[5088034\\]\n* \\[GeForce RTX 50 series\\] GeForce RTX 50 series GPUs crashes with black screen \\[5120886\\]\n* Some NVIDIA Control Panel / NVIDIA App settings changes immediately get reset or give error \\[5160516\\]\n* PC may bugcheck IRQL NOT LESS OR EQUAL 0xa during gameplay with HDR enabled \\[5091576\\]\n* \\[VRED 2026\\] Optix compile error with R570 branch drivers \\[5122360\\]\n* \\[Derivative TouchDesigner \\] Application stability issues \\[4606316\\]\n\n**Open Issues**\n\n* Changing state of \"Display GPU Activity Icon in Notification Area\" does not take effect until PC is rebooted \\[4995658\\]\n\n**Additional Open Issues from** [GeForce Forums](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/560098/geforce-grd-57283-feedback-thread-released-31825/)\n\n* Forza Horizon 5 lights flicker at night time \\[5038335\\]\n* \\[RTX 50 series\\] Red Dead Redemption 2 crashes shortly after starting a game in DX12 mode. No issue in Vulkan mode \\[5137042\\]\n* \\[RTX 50 series\\] Display may show black screen when selecting DLDSR resolution \\[5144768\\]\n* \\[RTX 50 series\\] Starfield may disaplay dithering/banding artifacts while in the menu screen \\[5121715\\]\n* \\[RTX 50 series\\] NVIDIA Control Panel setting \"Perform scaling on\" missing \"GPU\" option when connected to a monitor in DSC mode \\[5156168\\]\n* \\[RTX 50 series\\] Cyberpunk 2077 will crash when using Photo Mode to take a screenshot with path tracing enabled \\[5076545\\]\n* \\[RTX 50 series\\] Colors may appear slightly saturated in games when in game-resolution is below native resolution of monitor and display scaling is set to 100% \\[5158681\\]\n* \\[RTX 50 series\\] Varjo Aero VR headset may fail to establish connection \\[5117518\\]\n* \\[RTX 50 series\\] Slightly higher DPC latency may be observed on some system configurations \\[5168553\\]\n* On certain PC configurations, vertical sync interrupt may get missed which may result in intermittent micro-stutters \\[5171856\\]\n* \\[RTX 50 series\\] UBISoft Connect client may incorrectly be detected triggering lower power state by GPU / lower performance in games \\[5183470\\]\n* \\[Cyberpunk 2077/Half-Life 2 RTX\\] PC may bugcheck with error 0xd1 when playing game while using DLSS Frame Gen + G-SYNC \\[5144337\\]\n\n**Please note:** When using certain 3rd party performance overlays alongside DLSS Frame Generation, crashes can occur.\n\n**Driver Downloads and Tools**\n\nDriver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n\nLatest Game Ready Driver: **572.83** WHQL\n\nLatest Studio Driver: **572.83** WHQL\n\nDDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n\nDDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n\n**DDU/WagnardSoft Patreon:** [**Link Here**](https://www.patreon.com/wagnardsoft)\n\nDocumentation: [Game Ready Driver 572.83 Release Notes](https://us.download.nvidia.com/Windows/572.83/572.83-win11-win10-release-notes.pdf) | [Studio Driver 572.83 Release Notes](https://us.download.nvidia.com/Windows/572.83/572.83-win10-win11-nsd-release-notes.pdf)\n\nNVIDIA Driver Forum for Feedback: [Link Here](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/560098/geforce-grd-57283-feedback-thread-released-31825/)\n\n**Submit driver feedback directly to NVIDIA**: [Link Here](https://forms.gle/kJ9Bqcaicvjb82SdA)\n\n[r/NVIDIA](https://new.reddit.com/r/NVIDIA/) Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\nHaving Issues with your driver? Read here!\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue**\n\nThere is only one real way for any of these problems to get solved, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows 10 or 11\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\nIf it still crashes, we have a few other troubleshooting steps but this is fairly involved and you should not do it if you do not feel comfortable. Proceed below at your own risk:\n\n* A lot of driver crashing is caused by Windows TDR issue. There is a huge post on GeForce forum about this [here](https://forums.geforce.com/default/topic/413110/the-nvlddmkm-error-what-is-it-an-fyi-for-those-seeing-this-issue/). This post dated back to 2009 (Thanks Microsoft) and it can affect both Nvidia and AMD cards.\n* Unfortunately this issue can be caused by many different things so it’s difficult to pin down. However, editing the [windows registry](https://www.reddit.com/r/battlefield_4/comments/1xzzn4/tdrdelay_10_fixed_my_crashes_since_last_patch/) might solve the problem.\n* Additionally, there is also a tool made by Wagnard (maker of DDU) that can be used to change this TDR value. [Download here](http://www.wagnardmobile.com/Tdr%20Manipulator/Tdr%20Manipulator%20v1.1.zip). Note that I have not personally tested this tool.\n\nIf you are still having issue at this point, visit [GeForce Forum for support](https://forums.geforce.com/default/board/33/geforce-drivers/) or contact your manufacturer for RMA.\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n\n**Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n* **What does the new Power Management option “Optimal Power” means? How does this differ from Adaptive?** *The new power management mode is related to what was said in the Geforce GTX 1080 keynote video. To further reduce power consumption while the computer is idle and nothing is changing on the screen, the driver will not make the GPU render a new frame; the driver will get the one (already rendered) frame from the framebuffer and output directly to monitor.*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations. The software will not be perfect and there will be issues for some people. For a more comprehensive list of open issues, please take a look at the Release Notes. Again, I encourage folks who installed the driver to post their experience here... good or bad.\n\n*Did you know NVIDIA has a Developer Program with 150+ free SDKs, state-of-the-art Deep Learning courses, certification, and access to expert help. Sound interesting?* [Learn more here](https://nvda.ws/3wCfH6X)*.*", "author": "Nestledrink", "created_time": "2025-03-18T19:21:46", "url": "https://reddit.com/r/nvidia/comments/1jeddbc/game_ready_studio_driver_57283_faqdiscussion/", "upvotes": 272, "comments_count": 2032, "sentiment": "bearish", "engagement_score": 4336.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jeg2bs", "title": "youtubers discovered a new adjective", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-18T21:11:10", "url": "https://reddit.com/r/youtube/comments/1jeg2bs/youtubers_discovered_a_new_adjective/", "upvotes": 359, "comments_count": 25, "sentiment": "neutral", "engagement_score": 409.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jegzs9", "title": "Youtube Videos Suddenly So Large", "content": "https://preview.redd.it/4gors7dbripe1.png?width=3840&format=png&auto=webp&s=f82cee3ed26bd5b69913e4d35fe8e236ffafa238\n\nI opened up Youtube today and suddenly, my videos are huge? They are taking up a lot of screen space all of a sudden. Does anyone know why or how I could fix this? It's jarring and annoying.", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-03-18T21:51:05", "url": "https://reddit.com/r/youtube/comments/1jegzs9/youtube_videos_suddenly_so_large/", "upvotes": 3, "comments_count": 5, "sentiment": "neutral", "engagement_score": 13.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}]