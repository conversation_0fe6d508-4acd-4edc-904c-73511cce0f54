[{"platform": "reddit", "post_id": "reddit_1ivxei6", "title": "Six Year Update: 43 y/o FIREd", "content": "February 22 2019 was the day I retired. Six year anniversary.\n\nLast year's post is here: https://old.reddit.com/r/financialindependence/comments/1axapip/five_year_update_42_yo_fired/\n\n**EXPENSES:**\n\nStill doing this manually.  I tried dumping the CSVs into an AI model, got a pretty decent response, but it wasn't exactly what I wanted.   So here's a top-level look at my expenses which just looks at my Checking Account.  Essentially **everything** except for HOA, Gas, and Health Insurance (which require a Check or Debit Card, not a CC), ends up on my Credit Card.\n\nCategory| Description| Amount\n:--|:--|:--|\nFebruary 2024\t|Credit Card Expenses\t|$1,689.19\nMarch 2024\t|Credit Card Expenses\t|$1,475.41\nApril 2024|\tCredit Card Expenses\t|$2,916.24\nMay 2024\t|Credit Card Expenses\t|$1,176.49\nJune 2024\t|Credit Card Expenses\t|$2,668.56\nJuly 2024\t|Credit Card Expenses\t|$3,061.93\nAugust 2024|\tCredit Card Expenses\t|$2,547.53\nSeptember 2024|\tCredit Card Expenses\t|$5,486.95\nOctober 2024\t|Credit Card Expenses\t|$3,518.73\nNovember 2024\t|Credit Card Expenses\t|$2,872.80\nDecember 2024\t|Credit Card Expenses\t|$2,733.46\nJanuary 2025\t|Credit Card Expenses\t|$1,798.08\n\t||\t\nHOA\t|$99 Per Month\t|$1,188.00\nProperty Tax||$1,640\nUtilities GAS\t|$20 Summer $80 Winter\t|$418.00\nHealth Insurance\t|$389 2024, $480 2025\t|$4,850.00\nAuto GAS\t|One-timer on Debit Card?\t|$66.00\nAuto Registration\t|\t|$608.00\nVenmo\t|Xmas Dinner\t|$176.00\nVenmo\t|Iceland Split Expenses\t|$2,829.00\n\t\t||\n\tTotal||\t$43,720.37\n\nI left the monthly Credit Card payments to show how my expenses average month to month.   The total \"normal\" expenses this year ended up being around $44,000 which is about $10,000 more than 2024.  This makes sense, since I had a ~$9000 ten day vacation to Iceland in October.\n\nThere were two additional expenses last year not shown above.  I bought a Tesla Model Y in March for $48,000, and then bought a Tesla Cybertruck in June for $107,000.   I sold my old 2018 Subaru STI for $24,500 and sold the Model Y for $40,000.  The TLDR here is that I knew the CT was coming but I wasn't expecting it so soon, so I wanted to get into a Tesla to get accustomed to driving an EV.  I happened to get that great offer to buy my STI so I took it and bought the Y.  If I knew I'd be getting the truck three months later I would have made different choices, but it is what it is.  Loved the Model Y, love the Cybertruck even more.  Best vehicle I've ever owned, it's awesome.  [Note, the Model Y and Cybertruck purchases were with cash, no loans.]\n\nNext topic is income Taxes.  I have this broken out from \"typical\" expenses because for me because it's a function of my \"income\" which I control by realizing capital gains, which for me are kind of artificially high.  For 2024 my AGI was around $570,000 so my Fed Liability was $93,000 and State Liability was $24,000.   For **me** it doesn't make sense for me to roll those numbers into my expenses and say \"My expenses for 2024 was $161,000.\"  That's just not a useful number for me to appreciate.\n\nLooking more closely at the Credit Card, I see 70 line items for Amazon Purchases (more on that later), and 677 other line items.  Finally Grok3 shows its usefulness.  I'll just go through the heavy hitters:\n\nCategory| Total Expense| Line Items\n:--|:--|:--|\nDinner\t|$3,552\t|98\nFast Food\t|$1,660\t|81\nJunkSnack |$461|45\nPizza|$820|38\nCoffee/Smoothie|$321|37\nGroceries|$2,023|31\n7-11 (Also JunkSnack)|$225|25\nICELAND|$5691|64\nVideo Games|$215|18\nLunch | $337|18\nCar Wash|$82|11\nAmazon Prime| $190| 12\nDental Insurance| $365|12\nUtilties kWh | $1370|12\nUtilities Internet | $976| 13\n\nAnd some other stuff.  7-11 is popping in for a Hot Dog and a Slurpie.  JunkSnack is all the stupid <$10 gas station charges I see.  Fast Food is the BK, McD, Taco Bell, Arbies lines.  Lunch and Dinner would be more Restaurant-y type places.  I've recently started to go to Tropical Smoothie Cafe and Bigby Coffee to get smoothies: huge waste of money at $7 a pop, but they are yummy.\n\nAnd finally: Amazon.  Report just came through, 105 line items.  Coffee stuff, microfiber cloths, some Atkins shakes, Cough Drops and other OTC stuff, ramen.  But, what about the Jerky and Redbull?!?!  There are 5 line items for Beef Jerky (and 3 line items for Slim Jims, we'll throw that in), and 41 lines for RedBull.  Total cost of Beef Jerky (and jerky adjacent products) was $490 and Red Bull was $2010.   Last year's update was $3000 in Beef Jerky and $3350 in Redbull.  We did it guys!  Pizza category is also down about 50% from last year.\n\n\n**INVESTMENTS**\n\nSame old table, brand new column...\n\nType|Retirement Day|1 Year|2 Years|3 Years|4 Years|5 Years|6 Years\n:--|:--|:--|:--|:--|:--|:--|:--\n*Traditional IRA*|$299,000|$348,000|$380,170|$410,285|$360,715|$395,500|$494,320\n*Roth IRA*|$14,500|$18,150|$70,236|$75,800|$91,469|$170,300|$232,890\n*Brokerage*|$18,400|$22,900|$37,108|$179,110|$139,420|$205,575|$546,130\n**Total Vanguard (3 Above)**|**$331,800**|**$389,100**|**$487,515**|**$665,195**|**$591,600**|**$771,375**|**$1,273,340**\nOther Holdings, Crypto/Bitcoin|$145,000|$291,000|$1,315,000|$985,000|$595,000|$1,260,000|$1,640,000\nHSA Investment|$6000|$7400|$8760|$9453|$9237|$11,700|$15,790\nCash|$20,000|$9000|$135,000|$9345|$11,785|$11,000|$17,460\n**Total NW**|**$502,900**|**$696,000**|**$1,946,000**|**$1,669,000**|**$1,207,000**|**$2,055,000**|**$2,947,000**\n\n(Total NW not including house and car)\n\nThe stock market was ripping last year and Bitcoin entered another booming cycle after the Halvening last year.   I stuck with my pre-determined plan that I've been executing for the past 10 years: sell off a fraction of my bitcoin every time the price increases 10%, and on a big retrace buy some back.   If the price just keeps going up I need to be happy with my sell point (I am), and if it comes back down the net result is I've sold near the top.   This time I ended up hitting \"sell triggers\" like six times.  This caused me to realize a lot of long term capital gains and a hefty tax bill, but I'm executing my plan and must be satisfied with those results.\n\nI had a side little dalliance with TSLA too.  In July of 2024 TSLA was at $265 and then \"crashed\" after an earnings call, so I decided to make a move.  I bought $100,000 of TSLA at $219.  Since then I've executed a similar plan, trying to keep my holdings at a value of $100k.  I sold 55 shares at $270, 50 @ $290, 50 @ $346, and 50 @ $400.   So I was able to realize $67,000 in gains and still have 250 shares ($85k).   Over the years there have been a few times I wanted to make a move into TSLA, saw a similar opportunity but never did it (and it would have obviously ended well).  I'm glad I finally decided to take the action.\n\nRoth Conversion Ladder!  The first few years I was doing $20, and now I'm doing $26,000 per year to fill the standard deduction and the 10% bracket.  I was comtemplating doing the 12% bracket last year also but my tax bill was already bananas so I didn't.  If this year Bitcoin stays flat and I don't need to sell off I'll consider doing a larget conversion this year.  The plan is to keep building the ladder but not \"removing any rungs.\"  Those first year conversions will just be there available in the future if I need or want to use them.\n\nHigh Level Picture: after expenses, buying and selling cars, and going on vacation, at the end of the day my start number was $2,005,000 and end number was $2,947,000.    ¯\\\\_(ツ)_/¯\n\n**LIFE STUFF**\n\nLife's been great.  I went on a 10 day vacation to Iceland in October with four couples.  We rented three Landcruisers and did the Ring Road, staying in eight different locations / hotels.   Some of the best food we ever had, it was a great time.  We hit the weather lottery too, it was clear and sunny with temps in the 45°F range, got into a little bit of snow around day 5 when we were in the north, but driving was clear.   There was been chatter about the next group vacation being to Greece, but no timetables on that yet.\n\nStill board gaming with a few of my friends.  The typical host has two kids that are getting older, turning 4 and 6 this year.  They are sweethearts, I can't wait until they get older and want to play games with us.  I get over that way a couple times per month for dinner and games.  Once I move house (if that ever happens, I feel like I've been talking about that for three years), it could be even more frequent to pop over.\n\n\"What do you do with all your time?!?\" Keep up my house, leasurely shop, cook for myself (my expenses list betraying me right now...).  I have a nice selection of YouTube channels I like to watch which in combination have a couple hours of new uploads every day to check out.  I really like Reaction channels, actually.  It's a way to rewatch a show or movie in a condensed way while also getting the enjoyment of seeing someone else experience it for the first time.   I have a good half-dozen channels I really like, which means I end up \"watching a movie with a friend\" basically every day.\n\nI haven't been gaming all that much lately, but it goes in spurts.  A new game or season will come out, I'll go hard on it for a few days or a week, then put it back down.\n\n**FINAL**\n\nI'll just copy paste from last year.  Everything is going great, still totally happy, never bored. Never going back to work.", "author": "Oracle_of_FIRE", "created_time": "2025-02-23T00:28:14", "url": "https://reddit.com/r/financialindependence/comments/1ivxei6/six_year_update_43_yo_fired/", "upvotes": 0, "comments_count": 32, "sentiment": "neutral", "engagement_score": 64.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iw23lw", "title": "Thank you, Google AI, very cool.", "content": "", "author": "watoermaloen", "created_time": "2025-02-23T04:39:11", "url": "https://reddit.com/r/google/comments/1iw23lw/thank_you_google_ai_very_cool/", "upvotes": 750, "comments_count": 76, "sentiment": "neutral", "engagement_score": 902.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iw5g81", "title": "Apple preparing Google Gemini integration with Apple Intelligence", "content": "", "author": "favicondotico", "created_time": "2025-02-23T08:19:26", "url": "https://reddit.com/r/apple/comments/1iw5g81/apple_preparing_google_gemini_integration_with/", "upvotes": 1567, "comments_count": 282, "sentiment": "neutral", "engagement_score": 2131.0, "source_subreddit": "apple", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iwhn43", "title": "Google Cloud KMS now features quantum-safe signatures", "content": "\n**Google Cloud KMS is now equipped with quantum-safe digital signatures upgrade.** This new feature is aimed at protecting sensitive information from potential quantum computing threats that could jeopardize encryption methods in the future. As the cloud computing landscape evolves, organizations must adapt their cybersecurity strategies accordingly.\n\nThe inclusion of innovative algorithms, ML-DSA-65 and SLH-DSA-SHA2-128S, enables businesses to adopt quantum-resistant technologies with ease. Google Cloud is also facilitating open-source implementations for these algorithms, promoting transparency and collaborative improvement. This proactive investment in developing security measures highlights Google's commitment to maintaining a reliable cloud infrastructure for all users.\n\n- Google Cloud enhances its KMS with quantum-safe signatures.\n\n- Addresses future vulnerabilities from quantum computing advancements.\n\n- Algorithms ML-DSA-65 and SLH-DSA-SHA2-128S are key developments.\n\n- Users encouraged to test and integrate these features.\n\n- Open-source implementations ensure community involvement.\n\n- Importance of adopting quantum-resistant encryption methods.\n\n[(View Details on PwnHub)](https://www.reddit.com/r/pwnhub/comments/1iwhlut/google_cloud_enhances_security_with_quantumsafe/)\n        ", "author": "<PERSON>-<PERSON>", "created_time": "2025-02-23T19:03:27", "url": "https://reddit.com/r/cloudcomputing/comments/1iwhn43/google_cloud_kms_now_features_quantumsafe/", "upvotes": 2, "comments_count": 1, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iwlgsw", "title": "Is anyone generating AI Content that isn’t punished by Google?", "content": "I have a few experiments running where I have a very good affiliate site running. It is 90% real content. The AI content I use with the same strategy as the real content never ranks at all. \n\nAny tips? ChatGPT vs others?", "author": "vegasbrianc", "created_time": "2025-02-23T21:45:21", "url": "https://reddit.com/r/DigitalMarketing/comments/1iwlgsw/is_anyone_generating_ai_content_that_isnt/", "upvotes": 13, "comments_count": 28, "sentiment": "neutral", "engagement_score": 69.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}]