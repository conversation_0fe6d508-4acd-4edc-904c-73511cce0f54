[{"platform": "reddit", "post_id": "reddit_1hqslqg", "title": "I interviewed for this company as a Software Engineer when I was 13 and got rejected, and now I made $3.5k off of my own SaaS at 15", "content": "Hey everyone! I applied for this software engineering job at this one company and they were hiring interns and I thought I would apply. The position was unpaid, so I thought that I would get some experience and get ahead of the competition early as I was only 13 at the time. They reached back to me after looking at my skills on my GitHub and I scheduled an interview time.\n\nAt the time, I was pretty skilled at coding, I knew numerous frontend and backend stacks and made a ton of web applications for projects, like the MERN stack and I also knew how to code in Java with Java Spring Boot and React.js for the frontend as well. \n\nHere are all of the coding skills I knew related at the time:  \n\\- Python (a lot of random libraries and some web scraping tools like Selenium and Beautiful Soup)  \n\\- Javascript, HTML, CSS  \n\\- MERN (MongoDB, Express, React, and Node)  \n\\- Also knew Java and Java Spring Boot to make full stack apps\n\nThe company itself basically handles and schedules hair salon appointments and connects you with barbers, and a dashboard for hair stylists and hair salon owners etc, I'm not too sure what else they did. \n\nAnyway, when it was time for the interview (it was at 6 am before I had to go to school and I was in 8th grade at the time), they asked me questions and the interviewer was very surprised that I knew so much and that I could code as well as some of the other applicants. But....he cut off the interview after 5 minutes and he was asking me generic questions like what school I went to and what my favourite subject was....so I knew that I basically had no shot.\n\nAfter a few days after the interview, my parents were proud and thought I had secured it, but I knew that I wasn't getting in. Well, surprise surprise I got rejected and I wanted to know why. They...didn't even contact me back. Probably because I was too young or they were coding with a stack I didn't know at the time. \n\nI also didn't know how to make an actual SaaS or any other type of application except for, well a inventory manager. That's about it. \n\nI was pretty pissed I didn't get in, because I worked this hard and faced failure on something I couldn't control, so the motivation was through the roof. I kept on coding and learnt new stacks, and gained a lot of experience.   \n  \nI then started coding my first few projects, 8 of them in fact, for around 2 years. All of them failed. Not a single dollar was made. I honestly was going to quit coding after doing it for so long, spending hours everyday and not focusing on school work, but finally. I had one more shot, and I made my first paid SaaS application called BigIdeasDB which was made around 2 months ago and got me 3.5k dollars to this date.\n\nI am pretty happy on where I am and I am grinding more than ever, marketing and improving my SaaS from the community's feedback.   \n  \nThere is still a lot to improve on and so much more to be done, and I hope that the new year is even better for my coding journey and I make even more money!\n\nTL;DR\n\nGot rejected from an unpaid internship, and built my own SaaS making me $3.5k.\n\n[REJECTION LETTER](https://preview.redd.it/95x9wn4p9aae1.png?width=3340&format=png&auto=webp&s=b478eeb14b96b22c22a155a4a68b3148b2a96207)\n\n[Stripe Dashboard for BigIdeasDB](https://preview.redd.it/fptfv1uu9aae1.png?width=1668&format=png&auto=webp&s=74aa649b43e76d3fa2a06aca9bed72d716bf4446)\n\n  \n", "author": "Many_Breadfruit9359", "created_time": "2025-01-01T01:17:35", "url": "https://reddit.com/r/Entrepreneur/comments/1hqslqg/i_interviewed_for_this_company_as_a_software/", "upvotes": 0, "comments_count": 10, "sentiment": "bullish", "engagement_score": 20.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hqukuy", "title": "Unpopular Opinion: GOOGL's search business is untouchable", "content": "I remember reading a while back that AI will destroy Google's search engine (and with that, the ads business). However, I find that Google's latest generative AI search - the AI summary you get on top of the search results, has been giving me good results lately. I've been studying for my AWS exam and I find myself browsing through the documentation less and less thanks to the AI summary.\n\nCouple that with its unbeatable search algorithm (which is no doubt itself augmented by AI already), I have a hard time believing that AI would disrupt Google's search business anytime soon.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-01T03:20:02", "url": "https://reddit.com/r/ValueInvesting/comments/1hqukuy/unpopular_opinion_googls_search_business_is/", "upvotes": 363, "comments_count": 312, "sentiment": "neutral", "engagement_score": 987.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr27v4", "title": "Interesting article on how Tesla/Waymo will threaten Uber's dominance", "content": "", "author": "deleted", "created_time": "2025-01-01T12:41:27", "url": "https://reddit.com/r/AutonomousVehicles/comments/1hr27v4/interesting_article_on_how_teslawaymo_will/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr493m", "title": "What are peoples' reasons for trying to break into analytics still?", "content": "Each day I see numerous posts about people attempting to break into analytics with the most random backgrounds that make them less than ideal candidates. They likely face a massive uphill battle to break into an analytics related role.\n\nWhy does this keep happening?\n\nDo people believe there's still a huge boom in the job market for analytics?\n\nIt just confuses me to be honest given how saturated the field is and bleak the job market is right now. You have an exponentially increasing supply of labor and decreasing demand for it.\n\nEdit: it appears that a few people are getting upset and think that I am gatekeeping. All I am asking is what are poeples' motivations to try and enter this field. It seems like many people think we're in a 2021-22 situation where you can complete a bootcamp or masters with no relevant experience or domain knowledge and then have the opportunity to jump right into the industry with a hybrid/remote role as a data analyst/scientist, etc. I personally think people are getting influenced by trendy influencer/youtube videos and universities creating these programs.\n\nObviously people can do as they wish. I don't care, it's just a job. However, I worry that many of the people posting about how they want to break in don't understand the true nature of the general job market and the analytics industry in particular. No shit most industries are saturated right now, but analytics is clearly at a higher level due to the combination of hype, off-shoring and cooling of the overall job market.\n\nI feel bad for the individuals who have decided to complete a bootcamp, a MS in analytics or just graduated with an irrelevant degree, and possess zero domain knowledge with few analytical skills but want to completely jump ship and break into analytics. They're going down a path that'll likely lead to hundreds maybe even over a 1000 applications with most being rejections and ultimately making a failed investment.\n\nThey can do what they want, however, I worry that many people think the barrier to entry is much lower than it truly is and are making poor decisions.", "author": "deleted", "created_time": "2025-01-01T14:46:02", "url": "https://reddit.com/r/analytics/comments/1hr493m/what_are_peoples_reasons_for_trying_to_break_into/", "upvotes": 157, "comments_count": 121, "sentiment": "neutral", "engagement_score": 399.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr5w8u", "title": "Black powder muzzleloader hunting with a self-driving cart", "content": "[https://www.youtube.com/watch?v=QIHxalHVWy8](https://www.youtube.com/watch?v=QIHxalHVWy8) \n\nSometimes old and new technology just come together. Here I try black powder muzzleloader deer hunting with the help of our self-driving cart -- it's a deer driving self-driving cart!\n\n(Attempting to cross-post on r/AutonomousVehicles and r/blackpowder )", "author": "thandal", "created_time": "2025-01-01T16:09:44", "url": "https://reddit.com/r/AutonomousVehicles/comments/1hr5w8u/black_powder_muzzleloader_hunting_with_a/", "upvotes": 2, "comments_count": 0, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr746l", "title": "Just want some genuine advice on investing.", "content": "I'm 22 years old and I've never invested before, I keep seeing these things on YouTube about the election and crypto and I have been hearing that x<PERSON> or dog<PERSON><PERSON>n or xai will blow up really soon and I just don't know what's true and what's not. ", "author": "Excellent_Rain3878", "created_time": "2025-01-01T17:06:16", "url": "https://reddit.com/r/investing_discussion/comments/1hr746l/just_want_some_genuine_advice_on_investing/", "upvotes": 6, "comments_count": 5, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr783s", "title": "Gains are not worth the risk", "content": "I wrote this in the hopes of saving some of you future heartache and irreversible trauma. I lost 110k over the past month. The majority after options calls during the bloodbath after december fed meeting. \n \nIf i could go back to my past self, i would say this. The loss isn't worth the potential gains. Before, I was just burnt out from my job. But at least i was proud to have saved up my first 100k. Now im burnt out, down 3 years of savings, and have a lot less freedom in my life.  I can't focus on work, i'm depressed and can't find joy in my hobbies anymore. I'm probably in the process of ruining my relationship as well. Even if i had won, i definitely don't think I'd be happier an equivalent amount. \n\nLife is hard. If you worked hard and earned some money. Dont make degenerate bets. The vast majority of us are just normal humans who should just save their time and invest in part index fund and part cash equivalents.\n\nOr maybe this marks the bottom and it is a buying opportunity. Your choice. \n\nEDIT\nWas only expecting maybe max 100 upvotes but i guess I said something that resonates. \n\nAfter wading through the comments, insults, memes, etc. I was touched by enough kind people reaching out to add some more. I dont think i can stomach another comment reading though so please dont expect me to react anymore. Notifications are off. Posted a 80k loss screenshot of part of my portfolio. Another 30k was lost in another account. https://imgur.com/a/jMvs9DR\n\n1. \"only bet what you can afford to lose\" doesn’t really make sense. Dont use that saying to convince yourself to make risky gambles. I could afford to lose 110k in the sense that i won't starve, i would still have a roof over my head, and i still have 30k i left that i promised to myself i wouldnt touch. But i lost things i didn't expect. Like my passions for my hobbies, a healthy exercise habit, my mental health after recovering from depression during college. Even during the time i was trading, i also hated how it felt. I was glued to the ticker and was losing connection with real life relationships.  Before you use the money you think you are willing to lose. Try spending a part of that amount on yourself. Get yourself some luxuries, some experiences, maybe travel, take a sabbatical from work, or spend it on someone close to you.  Its all numbers on the screen when trading, so its easy to lose a sense of it all. Afterwards, imagine losing the ability to do all that and only proceed if youre ok with that. \n\n2. For those who think this isnt something a normal person could go through. I saved roughly 60-70% of my paycheck the past 3 years. I made sacrifices on lifestyle and luxuries. \n\n3. For those that still want to go on, i sure cant stop you. Maybe some of us need to learn a lesson firsthand. Might be better even to learn early on before you have a family with hundreds of thousands saved up over decades. This might help. \n\nLooking back, i definitely had chances to make money.  I was thinking about RKLB when it was $5 (now $25). I had a chance to jump into RDDT when it was still $80. I considered googl at 165 since the bad news seemed overblown.   Even at my most insane already down 50k, before i lost it all i almost went all in in on christmas eve with 1 week dte options on tsla calls.  Instead i did it on the Friday afterwards hoping for a similar bounce to recover the from the drop after fed earnings.  If you get into single stocks, crypto, options it's a lot riskier. Youre going to have to be lucky with the timing. Youre also going to have to be disciplined with your strategy.  \n\nWhen you make your bet. If you win, stop.  i hope you become happy. I hope you get more time to pursue your passions. To spend time with family and friends. To become a person you are proud of. \n\nIf you lose, i hope you recover. Never gamble again. Life will be harder. But, maybe we can still find a part of that happiness. I dont think we really want money. We just want a more human experience. ", "author": "ThinkingIsGoodYT", "created_time": "2025-01-01T17:11:17", "url": "https://reddit.com/r/wallstreetbets/comments/1hr783s/gains_are_not_worth_the_risk/", "upvotes": 7852, "comments_count": 1117, "sentiment": "neutral", "engagement_score": 10086.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr78gk", "title": "If you disable <PERSON>, Google ALSO disables itself and it's gonna be the death of me", "content": "", "author": "mentaleatingpaper", "created_time": "2025-01-01T17:11:44", "url": "https://reddit.com/r/google/comments/1hr78gk/if_you_disable_gemini_google_also_disables_itself/", "upvotes": 2, "comments_count": 18, "sentiment": "neutral", "engagement_score": 38.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr83e4", "title": "Google Pixel 9 Pro - Can't receive nudes via rcs/sms?", "content": "Title says it all\n\nThe girl I'm seeing at the moment was a bit upset with me today, given I hadn't responded to her spontaneous spicy pictures she's sent my way  \n  \nI (*after reassuring her that I was in fact very appreciative of the gesture*) was very confused and showed her no eye cady had been received  \n  \nWe ran some tests together after having a laugh, and found out that my Pixel 9 Pro won't receive any nude pictures via google messages, but other messaging services like whatsapp and facebook messenger seem fine?  \n  \nIs there any documentation or info on this?", "author": "Cyphonelik", "created_time": "2025-01-01T17:50:35", "url": "https://reddit.com/r/GooglePixel/comments/1hr83e4/google_pixel_9_pro_cant_receive_nudes_via_rcssms/", "upvotes": 500, "comments_count": 222, "sentiment": "neutral", "engagement_score": 944.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hr9u72", "title": "<PERSON><PERSON> (OpenAI) and <PERSON> (Google) have a disagreement", "content": "", "author": "sachos345", "created_time": "2025-01-01T19:06:24", "url": "https://reddit.com/r/singularity/comments/1hr9u72/roon_openai_and_logan_google_have_a_disagreement/", "upvotes": 334, "comments_count": 326, "sentiment": "neutral", "engagement_score": 986.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hrah9b", "title": "There’s big money in D2D sales if you have the mentality. ", "content": "Hit $22,000 gross in two weeks as a handyman. I target upper middle class neighborhoods and go door to door. No ads, no flyers, no website yet even. Just magnetic business cards I hand out when I can’t make a sale. \n\nI specialize in jobs that provide high perceived value that I can knock out quick and charge at least a couple hundred bucks for. Mostly exterior repairs, fixes, etc. Average around $200-$400 an hour per job that way. \n\nIt’s amazing how much effort and money people (<PERSON><PERSON> included in the past) put into getting customers to come to them but won’t take a single step in the other direction. Everything changed when I stopped focusing on building websites/apps, optimizing google ads, obsessing over my google business profile, etc and just went out and knocked on doors. \n\nI was a software engineer for 10 years when I got laid off February 2024. Months of failed jobs apps led me to start doing some personal training, I had a lot of experience in the strength world and did ok, enough to pay bills but nothing like the salary I was used to. Started providing handyman services but wasn’t getting many leads through the usual recommendations for local service businesses. Needed cash fast and started going door to door. If I can do it, so can you. Get out there and make things happen, don’t just sit back and wait. ", "author": "37hduh3836", "created_time": "2025-01-01T19:34:14", "url": "https://reddit.com/r/smallbusiness/comments/1hrah9b/theres_big_money_in_d2d_sales_if_you_have_the/", "upvotes": 1389, "comments_count": 327, "sentiment": "neutral", "engagement_score": 2043.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hrcnef", "title": "YieldMax ETFs and share price (\"NAV\") erosion", "content": "**The images in this post are best viewed on a computer monitor or laptop, not a phone.**\n\nHappy New Year! Since there are frequent posts asking for \"thoughts\" on MSTY and other YieldMax ETFs, I will share my thoughts on YieldMax ETFs. I'll put the TLDR at the beginning instead of at the end.\n\n***TLDR: you can make money with YieldMax ETFs, but in almost every case you would make more money in the corresponding (\"underlying\") stock. Most YieldMax ETFs suffer share price declines (\"NAV erosion\") that drag on total return and can lock you into or trap you in the funds, forcing you to take a loss if you sell. YieldMax ETFs are most suitable for retired people who already have a lot of money and actually need the income, not young people who are working, earning money at their jobs, and have modest portfolios that need to grow.***\n\n* YieldMax ETFs are a relatively new family of funds (the oldest TSLY started in November 2022, the newest started in December 2024) that have attracted attention because of their high distribution (\"dividend\") yields\n* All of the funds trade call and put options on actual stocks and ETFs like NVDA, MSFT, COIN, MSTR, ARKK, etc.  None of the YieldMax ETFs except ULTY, YMAG, and YMAX actually own the stocks or ETFs on which they trade options. YMAX and YMAG are \"funds of funds\" that own other YieldMax ETFs, and ULTY owns some actual stocks and ETFs.\n* Except for ULTY, none of the money (\"dividends\") that YieldMax ETFs distribute to shareholders comes from actual dividends. Many of the stocks the ETFs track - like TSLA and AMZN - don't even pay dividends. Even if the stocks do pay dividends - like AAPL and MSFT - Yieldmax ETFs don't own any shares of the stock so they aren't entitled to the dividends the stocks pay. All of the money (\"dividends\") that YieldMax ETFs distribute to shareholders of the funds comes from trading options and from interest collected from US Treasury Notes that the funds hold.\n* Because of the way the ETFs are constructed, they tend to not capture the full gains of the stock they are tracking. If MSTR goes down, MSTY goes down too. If MSTR goes up, MSTY goes up too, but not as much as MSTR. As the YieldMax fund managers themselves explain:\n\n\n**The Fund’s strategy will cap its potential gains if MSTR shares increase in value. The Fund’s strategy is subject to all potential losses if MSTR shares decrease in value, which may not be offset by income received by the Fund. The Fund may not be suitable for all investors.** [https://www.yieldmaxetfs.com/msty/](https://www.yieldmaxetfs.com/msty/)\n\n* Because of the way YieldMax ETFs are constructed, if the stock it is tracking is generally rising in price - and the YieldMax ETF managers have picked stocks that tend to rise in price, like NVDA, AMZN, MSFT, etc. to track with their ETFs - the share price of the Yieldmax ETF will lag farther and farther and farther behind the stock it is tracking as time goes on, even to the point the YieldMax ETF shares are not only not gaining as much as the stock it is tracking but are actually losing value - \"NAV erosion\" - despite the fact the stock it is tracking is rising in price. For example, here is the share price of TSLY (blue line) - the oldest YieldMax ETF so it has had more time to fall behind the stock it tracks - compared to the share price of TSLA (red line)\n\n[https://s3.tradingview.com/snapshots/4/45sDO8uu.png](https://s3.tradingview.com/snapshots/4/45sDO8uu.png)\n\nSince TSLY's inception in November 2022, TSLA's shares are up +120% and TSLY's shares are down -64%. TSLY even had to do a 2:1 reverse split in February 2024 to keep the price from being scary low.\n\nSince CONY's inception in August 2023 it's share price is down -34% (blue line) while during the same time period COIN's shares are up +213% (red line).\n\n[https://s3.tradingview.com/snapshots/b/bU9S2eRS.png](https://s3.tradingview.com/snapshots/b/bU9S2eRS.png)\n\nEven when a YieldMax ETF like MSTY hasn't had any share price (\"NAV\") erosion - yet - it's share price gain has lagged far behind that of MSTR, the stock it tracks.\n\nSince MSTY's inception in February 2024 its shares are up only +24% (blue line) while MSTR's shares (red line) are up +306%\n\n[https://s3.tradingview.com/snapshots/o/OIPSU72a.png](https://s3.tradingview.com/snapshots/o/OIPSU72a.png)\n\n* Most YieldMax ETFs have suffered share price declines - \"NAV erosion\" - since their inception, but some are much worse than others. This chart shows the share price action for several popular YieldMax ETFs since their inceptions. I included the S&P 500 index (VOO) price action since the November 2022 inception of the oldest YieldMax ETF (TSLY) as a benchmark and reference. The chart is busy because of the number of ETFs but look at the numbers on the right edge of the chart for each ETF.\n\n[https://s3.tradingview.com/snapshots/r/RjeIvE2L.png](https://s3.tradingview.com/snapshots/r/RjeIvE2L.png)\n\nIf you have trouble reading the chart the results are\n\n* VOO +47.70%\n* the following have share price increases, although far below the stocks they track\n* PLTY +37.47% (PLTR +82.46% during the same time)\n* MSTY +24.15% (MSTR +306.11% during the same time)\n* NVDY +17.27% (NVDA +369.91% during the same time)\n* and then they go increasingly negative (share price \"NAV\" erosion)\n* YMAG -3.24%\n* TSMY -5.15% (TSM +15.19%)\n* AMZY -6.19% (AMZN +69.90%)\n* FBY -7.64% (META +79.89%)\n* NFLY -8.90% (NFLX +103.36%)\n* JPMO -9.70% (JPM +63.80%)\n* MSFO -9.91% (MSFT +30.50%)\n* APLY -10.68 (AAPL +50.43%)\n* YMAX -14.82%\n* GOOY -27.71% (GOOG +43.18%)\n* CONY -34.03% (COIN +213.59%)\n* YBIT -39.51% (BTC +40.56%)\n* AMDY -49.45% (AMD +18.88%)\n* ULTY -53.64%\n* TSLY -64.40% (TSLA +120.44%)\n\nAs you can see, in most cases while the stocks that YieldMax ETFs track were going up up up, the share prices of YieldMax ETFs were going down down down.\n\nIn general, when you invest, you want to buy shares that go up in price, not down. Buy low and sell high. You can't sell high if the price went down.\n\n* Since dividend yield is inversely related to share price - as share price goes down, dividend yield goes up - part of the reason YieldMax ETFs have such high yields is because as the share price went down, the yield went up, even if the dividend per share stayed the same.\n* Yes, but what about the \"dividends\"?! \"I'm making so much money every month from YMAX, who cares if the share price is going down!\" some might say. \"I'm taking the YieldMax \"dividends\" and using them to buy SCHD\" others might say. Well, the problem with declining share price (\"NAV erosion\") it guarantees you will take a loss if you sell your YieldMax ETF shares. If you don't want to take that loss by selling it locks you in or traps you in that YieldMax ETF. You will take a loss if you sell your shares because you need the money for something, or you want to move that money to a better investment, even another YieldMax ETF, or if the YieldMax ETF no longer fits your needs or risk tolerance.\n* But don't the \"dividends\" make up for the dropping share price? Well, as we know or should know, **total return** is the combination of share price increase (or decrease) and reinvested dividends (if any). Even though YieldMax share prices in general go down, don't all those dividends make up for it? Well, in most cases they help offset the negative effect of dropping share price on total return, but not enough to make up for all of it. Even with all those dividends you would have more gains investing in the actual stock - NVDA, MSTR, COIN - than in the YieldMax ETFs *even with the dividends*. Scroll down to \"Growth of $10,000\" in each of the links that follow.\n\n[https://totalrealreturns.com/n/NVDA,NVDY](https://totalrealreturns.com/n/NVDA,NVDY)\n\n[https://totalrealreturns.com/n/MSTR,MSTY](https://totalrealreturns.com/n/MSTR,MSTY)\n\n[https://totalrealreturns.com/n/COIN,CONY](https://totalrealreturns.com/n/COIN,CONY)\n\n* Sometimes, even with the dividends, the YieldMax ETF not only hasn't matched the stock it tracks, *it even underperformed the S&P 500 index*. Scroll down to \"Growth of $10,000\" in each of the links that follows:\n\nTSLY [https://totalrealreturns.com/n/TSLA,VOO,TSLY](https://totalrealreturns.com/n/TSLA,VOO,TSLY)\n\nGOOY [https://totalrealreturns.com/n/GOOG,VOO,GOOY](https://totalrealreturns.com/n/GOOG,VOO,GOOY)\n\nAPLY [https://totalrealreturns.com/n/AAPL,VOO,APLY](https://totalrealreturns.com/n/AAPL,VOO,APLY)\n\n* For those who are using YieldMax ETFs to \"feed\" purchases of SCHD or other funds, in most cases you would have more money to invest in SCHD and other funds if you had invested in the actual stock that the YiekdMax ETF tracks than in the YieldMax ETF that tracks the stock, and sold a dollar amount of shares every month or quarter or whatever.\n* So, are there circumstances where YieldMax ETFs make sense? As the YieldMax ETF fund managers point out:\n\n>**The Fund may not be suitable for all investors.**\n\nSo who are YieldMax ETFs suitable for? Well, not young people who want/need to grow their portfolios. As I have shown, they would have more gains/make more money investing in the actual stocks - NVDA, NFLX. MSTR, etc. - than in the YieldMax ETFs - NVDY, NFLY, MSTY - that track the stocks. But sadly, it looks like lots of young people are only looking at the high dividend yield of YieldMax ETFs and aren't paying attention to share price declines (\"NAV erosion\") and total return. They are making gains, but not as much as they could be making.\n\n* In my opinion, YieldMax ETFs are suitable for people who already have 6 or 7 or 8 figure portfolios, are living on their investments and need income, who want income from options trading without having to sell covered calls or get involved with options trading personally, and already have lots of money and can tolerate the share price declines (\"NAV erosion\").\n\nNone of the above means I \"hate dividends\" or I'm \"anti-dividends\". I collected over $61k in dividends in 2024. I'm not even anti-YieldMax ETFs per se, when it is appropriate for the investor. I have 2.73% of my portfolio in NVDY, but I'm one of those people I described who already has a large portfolio after years of investing, who is near retirement and needs the income, and who doesn't want to trade options. \"But you said own the stock instead of the YieldMax ETF, what a hypocrite!\" some might think. Well I do own NVDA stock as well. NVDA is 13.77% of my portfolio, much larger than my NVDY position.\n\nIt's your money, invest in whatever you want. But it makes sense before you invest your hard-earned money to understand what you are investing in so you know if it makes sense for you. Don't just look at dividend yield.\n\nHappy New Year!", "author": "Jumpy-Imagination-81", "created_time": "2025-01-01T21:11:58", "url": "https://reddit.com/r/dividends/comments/1hrcnef/yieldmax_etfs_and_share_price_nav_erosion/", "upvotes": 63, "comments_count": 83, "sentiment": "bearish", "engagement_score": 229.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hrdlpw", "title": "My Journey with Digital Products and MRR", "content": "PS. I was meant to post this 3 days ago. Hope it's not late\n\nAs we count down to the final days of the year, I find myself reflecting on how much my life has changed since I started selling digital products back in July. With just two days left in 2024, I thought it would be the perfect time to share my journey. Who knows? Maybe this post will inspire someone to take the leap and set themselves up for a more fulfilling 2025.\n\n*Why I Started*:\n\nEarlier this year, I was stuck. After losing a job and spending countless weeks submitting applications that went unanswered, I felt defeated. I’d scroll through Instagram and TikTok, seeing people talk about making money online, some of them earning more in a month than I could dream of in a year. I wasn’t just envious; I was curious.\n\nBut I didn’t have a massive following or products to sell. I was a 22-year-old with nothing more than a smartphone, a reliable internet connection, and a burning desire to make something happen. That’s when I came across digital marketing and digital products.\n\n*The Early Struggles:*\n\nLet me be real: the first three months were brutal. I was completely lost, spending hours on Google and YouTube trying to piece together how to get started. There were moments of doubt—“What if I fail? What if this is just another scam?” But the frustration wasn’t enough to stop me.\n\nI finally invested in a course (UBC) and let me tell you, it was a game-changer. It wasn’t just about marketing or selling; it was about learning how to build a brand, connect with people, and market effectively without being pushy or salesy. Most importantly, it came with 1:1 mentorship, which helped me figure out what I was doing wrong and how to fix it.\n\n*The Turning Point:*\n\nOnce I pivoted and started applying what I learned in the course, everything changed. My first sale felt like magic-it wasn’t a lot, but it was proof that this could work. By the second month, I hit my first 4-figure milestone.\n\nNow, I’m averaging 4 figures monthly, and while it’s not six figures (yet), it’s consistent and growing. For someone who was completely broke just a few months ago, this has been life-changing.\n\n*The Process:*\n\nHere’s how I approached selling digital products:\n\n* **Platforms:** I use Beacons for hosting and selling my products, with a Beacons link in my social media bio to drive traffic.\n* **Promotion:** Everything I do is organic—no paid ads, no fancy setups. I use Instagram, TikTok, and Threads to connect with people. The best part? I don’t show my face! You can absolutely succeed without being on camera.\n* **Focus:** It’s not about spamming people with sales pitches. I focus on building genuine connections, creating content that educates and inspires, and showing people how my products can help them.\n\n*Expectations vs. Reality:*\n\nIf you’re thinking about starting, here’s the truth:\n\n* The income is real, the flexibility is unmatched, and the ability to scale is incredible. Whether you’re selling templates, eBooks, or courses, there’s no inventory, no overhead, and no shipping headaches. Plus, it’s a skill you can carry into any future business.\n* It’s not “easy” money. You’ll need to put in work, especially in the beginning. There’s a learning curve, and some months may feel slow. Consistency and adaptability are key.\n\n*What I’ve Learned:*\n\nThis journey has taught me so much about online selling, content creation, and even myself. I’ve learned how to stay consistent even when things feel slow, how to pivot when something isn’t working, and how to celebrate the small wins along the way.\n\n*Why I’m Sharing This Now:*\n\nAs 2025 approaches, I know many people are looking for ways to earn extra income or even start a side hustle. If you’ve been hesitant, let this be your sign. You don’t need to have it all figured out; you just need to start.\n\nIf you’ve got questions about digital products, marketing, or how I got started, drop them in the comments. I’d be happy to share more about what worked for me.\n\n  \nEdit/Update: I just made $500 on the second day of 2025, and I wanted to share this moment with you because it’s proof that one decision can truly change your life.", "author": "WayRevolutionary1", "created_time": "2025-01-01T21:54:07", "url": "https://reddit.com/r/DigitalMarketing/comments/1hrdlpw/my_journey_with_digital_products_and_mrr/", "upvotes": 0, "comments_count": 11, "sentiment": "bearish", "engagement_score": 22.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}]