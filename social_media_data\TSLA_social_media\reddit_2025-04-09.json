[{"platform": "reddit", "post_id": "reddit_1jusupw", "title": "Celebrate the Bear Market.  A once a decade opportunity.", "content": "They say the best buys are made when you are shitting bricks.  We should hit bear market levels (-20%) tomorrow or this week.  We are almost there.  How will you celebrate Bear Market Day ?  What is on you list to buy.  I plan to buy NVDA and NVO.  Two stocks I had missed out on but want to get my hands on them.\n\nEdit:  Today's furious rally showed that <PERSON> has overplayed his hand and now is beating retreat.  Something's never change.  There is always recovery after a bear market.", "author": "pravchaw", "created_time": "2025-04-09T00:22:27", "url": "https://reddit.com/r/ValueInvesting/comments/1jusupw/celebrate_the_bear_market_a_once_a_decade/", "upvotes": 454, "comments_count": 316, "sentiment": "neutral", "engagement_score": 1086.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1juuz19", "title": "Google Banned Me from Local Guides After 10+ Years and 100M Photo Views — No Warning, No Explanation", "content": "***So happy to report that after some great connections, advice and appeal to Google they have effectively reinstated my Google Local Guides account and status. Appreciate the comments and support. What a ride that was...SKOL!***\n\n\n\nI’ve been contributing to Google Maps for over a decade—uploading photos, videos, reviews, and corrections to help improve the platform. I hit **Level 10** as a Local Guide with over **45,000 contributions** and nearly **100 million views**.\n\nOut of nowhere, I noticed:\n\n* My Local Guide badge and profile were gone\n* My uploads were stuck in “upload pending”\n* My view count had completely frozen\n\nThen I tried rejoining the Local Guides program and got this message:  \n**“You’re not eligible to become a Local Guide.”**\n\nFinally, I found a hidden message saying I’d been banned for violating the rules—but they never sent me a warning, a reason, or anything. Just silently nuked my status, my visibility, and 10+ years of contributions.\n\nSupport? Nothing. Forums? Banned there too. No way to appeal.\n\nSo that’s it: 100 million views, thousands of hours, all gone with no explanation.  \nThanks for the transparency, Google.\n\nhttps://preview.redd.it/2qzvy9oywpte1.png?width=3456&format=png&auto=webp&s=090f5b32b9b8a5128ffc0d68d33f773be5f1467e\n\nhttps://preview.redd.it/nzbwe9oywpte1.png?width=3456&format=png&auto=webp&s=1441bfc44fc09a56e0ae041e06165ffac1c8ad4d\n\nhttps://preview.redd.it/j02zjnvzwpte1.png?width=3456&format=png&auto=webp&s=f7017ecf04c3f46ab32b8475efbb27744d404281\n\nhttps://preview.redd.it/mjxqwlj0xpte1.png?width=1179&format=png&auto=webp&s=568379b87b13b667999e06ee5ce7c8101caede9b", "author": "vikingsfanben", "created_time": "2025-04-09T02:11:06", "url": "https://reddit.com/r/GoogleMaps/comments/1juuz19/google_banned_me_from_local_guides_after_10_years/", "upvotes": 103, "comments_count": 65, "sentiment": "neutral", "engagement_score": 233.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1juvjn0", "title": "How Are U.S. Small Businesses Handling 104% Tariffs on Products That Can Only Be Sourced from China?", "content": "Hi everyone,\n\nI’m part of a Chinese manufacturing company that has been exporting indoor playground equipment globally for over 15 years — mainly to small business clients like family entertainment centers, kids' cafés, and franchises.\n\nJust last week, **the U.S. tariff on our category jumped from 34% to 104%**. One of our American customers said, *“There’s no way I can make a profit now.”*\n\nI'm not here to promote or sell anything — I’m genuinely looking to understand how **U.S. small businesses are adapting to these new tariffs**, especially when:\n\n* The products are not produced locally in the U.S. at all.\n* Alternatives (e.g., India, Vietnam) don’t offer the same quality or safety certifications.\n* Buyers still need these products for planned launches or seasonal openings.\n\nA few questions I’d love your insight on:\n\n* If you were affected by similar tariffs, how did you manage or negotiate around them?\n* Have you worked with suppliers that ship through third countries to reduce the duty impact?\n* How do you communicate such a big cost jump to your customers?\n\nI truly believe this issue affects both sides of the supply chain. I’m here to listen and learn from your experiences — thanks in advance.", "author": "toymakerinchina", "created_time": "2025-04-09T02:41:31", "url": "https://reddit.com/r/smallbusiness/comments/1juvjn0/how_are_us_small_businesses_handling_104_tariffs/", "upvotes": 745, "comments_count": 411, "sentiment": "bullish", "engagement_score": 1567.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1juy55s", "title": "BREAKING: 30-year Treasury yield is now above 5%. China Dumping US bonds at a high rate! Armageddon on tomorrows market guaranteed", "content": "Bond rout starting to sound market alarm bells\n\nSINGAPORE (Reuters) - U.S. Treasuries extended heavy losses on Wednesday in a sign investors are selling even their safest assets as a global market rout unleashed by U.S. tariffs takes an unnerving turn towards distress and a dash for the safety of cash.\n\n\"This is beyond fundamentals right now. This is about liquidity,\" said <PERSON>, senior rates strategist at ANZ in Sydney.\n\nThe 10-year U.S. Treasury yield, the globe's benchmark safe-haven anchor, was up 20 basis points and rising in Asia - a remarkable move in a time zone where it's usually fairly steady.\n\nAt 4.46% the yield is up 59 basis points from Monday's low, with traders saying hedge funds were the heaviest sellers as they started to be forced from leveraged bets that in calmer times profit from small gaps between cash and futures prices.\n\n\"This kind of thing becomes problematic if the prime broker starts saying that now ... I want to charge you a higher margin or I basically want more margins from you,\" said <PERSON><PERSON><PERSON>, chief investment officer at Aravali Asset Management, a global arbitrage fund based in Singapore.\n\nThirty-year U.S. yields spiked 24 bps to 4.9553% and the three-day move in yield - if sustained - would mark the heaviest selloff in the long end since 1981.\n\nSelling was heavy in Japan and Australia.\n\nSo basically we are going back to the stone ages tomorrow. This is much much worse than you think...\nhttps://finance.yahoo.com/news/bond-rout-starting-sound-market-*********.html", "author": "Onnimation", "created_time": "2025-04-09T05:13:26", "url": "https://reddit.com/r/stocks/comments/1juy55s/breaking_30year_treasury_yield_is_now_above_5/", "upvotes": 16260, "comments_count": 1740, "sentiment": "bearish", "engagement_score": 19740.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jv3z6t", "title": "@Tesla: Our goal is to make service seamless instead of annoying. Everything is handled within the Tesla app, no effort required.", "content": "", "author": "TeslaAI", "created_time": "2025-04-09T11:57:23", "url": "https://reddit.com/r/teslamotors/comments/1jv3z6t/tesla_our_goal_is_to_make_service_seamless/", "upvotes": 0, "comments_count": 50, "sentiment": "neutral", "engagement_score": 100.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jv4p3v", "title": "Tesla shares plunge below Lutnick’s ‘never this cheap’ level", "content": "", "author": "newsspotter", "created_time": "2025-04-09T12:35:26", "url": "https://reddit.com/r/politics/comments/1jv4p3v/tesla_shares_plunge_below_lutnicks_never_this/", "upvotes": 2024, "comments_count": 135, "sentiment": "neutral", "engagement_score": 2294.0, "source_subreddit": "politics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jv5ygz", "title": "Self-Driving Cars: Future Revolution or Risky Gamble?", "content": "The age of self-driving cars is here, but are we ready for it? Will autonomous vehicles make our roads safer, or are they a technological gamble with unknown risks?  \n\nAutonomous vehicles are no longer just a dream—they are being tested on roads worldwide. Companies like Tesla, Waymo, and major automakers are investing billions to develop self-driving technology. These cars rely on artificial intelligence, sensors, and cameras to navigate roads without human input. But how well do they really work?  ", "author": "Es<PERSON><PERSON><PERSON>", "created_time": "2025-04-09T13:36:25", "url": "https://reddit.com/r/youtube/comments/1jv5ygz/selfdriving_cars_future_revolution_or_risky_gamble/", "upvotes": 0, "comments_count": 0, "sentiment": "bullish", "engagement_score": 0.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jv8d1x", "title": "Remember in early COVID when we all thought we were going to die? The market fell off a cliff and everyone panicked. The winners were the diligent investors who kept piling money in just in case we did not die.", "content": "My wife and I were terrified in early COVID just like everyone else. The market dropped, everyone seemed to be dying and the future was so unclear. All we told ourselves is that if we live, the market will recover one day. We put in all of our money and continued our weekly DCA. We did the same thing in 2022. Investing heavily during those periods cut 5 to 10 years off of our working lives. I see so many posts of people full of fear. Ignore the noise. Stay the course, this too shall pass and you will thank yourself later.  ", "author": "Fire-Philosophy-616", "created_time": "2025-04-09T15:19:50", "url": "https://reddit.com/r/Fire/comments/1jv8d1x/remember_in_early_covid_when_we_all_thought_we/", "upvotes": 1305, "comments_count": 483, "sentiment": "bearish", "engagement_score": 2271.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jvc1hj", "title": "Chicken littles will never learn", "content": "Everybody wants to buy stocks cheap until they’re cheap, and then everyone starts becoming experts on macroeconomics, talking about the end of American dominance and “decade long bear markets”. \n\nAnd what’s the funniest part? They’ll never learn. Next time there’s a crash, they’ll go on places like Reddit and say the same thing, costing anyone unfortunate enough to believe them years of gains. \n\nEdit: and because people are saying I’m only posting after the fact, here I am 2 days ago saying literally the same thing and getting stunted on my chicken littles: \n\nhttps://www.reddit.com/r/ValueInvesting/s/E3lK67QEuZ", "author": "Torontobizphd", "created_time": "2025-04-09T17:49:52", "url": "https://reddit.com/r/ValueInvesting/comments/1jvc1hj/chicken_littles_will_never_learn/", "upvotes": 148, "comments_count": 252, "sentiment": "bullish", "engagement_score": 652.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jvdiij", "title": "Didn’t see a positive post a out TSLA, I only see negative ones", "content": "If people are gonna call out the downs for criticism, we gotta call out the ups to give praise too!\n", "author": "JTBBALL", "created_time": "2025-04-09T18:49:16", "url": "https://reddit.com/r/elonmusk/comments/1jvdiij/didnt_see_a_positive_post_a_out_tsla_i_only_see/", "upvotes": 0, "comments_count": 54, "sentiment": "neutral", "engagement_score": 108.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jvdsjb", "title": "Elon Musk's Tesla $TSLA rises 20% following <PERSON>'s tariff pause.", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-09T19:00:43", "url": "https://reddit.com/r/StockMarket/comments/1jvdsjb/elon_musks_tesla_tsla_rises_20_following_trumps/", "upvotes": 1, "comments_count": 6, "sentiment": "neutral", "engagement_score": 13.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jvf7he", "title": "I am a Software Developer and I am tired and I never want to sit in front of a computer again. A rant", "content": "I know this is might be a little unjustified because I have a job that is well-paying, high demand and in a field with lots of opportunities. I am a web developer with some knowledge in NLP, meaning I've been working on AI things too.\n\nBut. I simply cannot do it anymore. I don't ever want to hear the word \"agile\" again. I don't ever want to play Planning Poker again. I don't ever want to wake up to find out that my most recent implementation is outdated because another super hot LLM has dropped overnight. I don't ever want to pretend to be proficient in yet another framework because the one I've been using is not cool anymore. I don't ever want to google how to revert a commit after pushing to remote again. I don't want to update oh-my-zsh every other day!!!!!!!!! I don't want to say \"I'm still working on it but I've made a lot of progress\" when in reality I haven't opened VSCode in three days because I'm sick of it. I don't want to discuss which IDE is best, I don't want to be stuck on a customer's API just to find out their documentation is completely wrong, I don't want to run into issue after issue until I can't remember what the actual task was anymore, I don't ever want to run out of GPU in Colab again. I don't want to have to check 5 different browsers to see if a margin is applied correctly. I don't ever want to compare model cards on huggingface again, I don't ever want to adjust parameters again, I don't ever want to refactor a single line of code again, I don't want to read another completely redundant comment other people's code because it was created by ChatGPT or Copilot. I don't want to see another component that is illegible because it is stuffed with tailwind. I don't want to discuss UX with stakeholders who apparently have never used an application in their lives. I don't want to be automatically labelled as  frontend and UX expert simply because I am a woman. I don't want to have to explain that the problem isn't the AI but the badly maintained data. I don't want to write a single Readme .md again. I don't want to write another prompt in my life. I don't want to restart another jupyter notebook ever again. I don't ever want to npm install again, I don't ever want to pip install -r requirements.txt just to run into dependency hell, and I don't want to take minutes every time I look for a previous message because I can't remember if it's in slack, teams, or discord. I don't want to write another word on a sticky note in miro and I don't want to look for \"the gif that best describes my mood\" either. I don't want to read another sentence on the world wide web that contains any of the words \"enhance\", \"leverage\", \"delve\". I don't want to \"embark\" or \"indulge\".\n\nI hate the internet. I have completely lost the ability to concentrate for longer than a couple of minutes. I have two monitors in addition to my laptop, I swipe between multiple desktops and it's still not enough for showing my emails, calendar, slack, teams, chatgpt, my IDE which in itself is separated into the main view and three different terminal tabs, the mongodb compass, postman, a browser window for googling, a browser window for compiling, a million other browser windows for github, jira, confluence, gcp or aws, and MY NOTES APP BECAUSE I DON'T REMEMBER A SINGLE THING ANYMORE.\n\nI know that a lot of these issues are directly related to my workplace, but I have tried all kinds of setups and also working independently, and I am done. Open for any job suggestions that do not involve any of the above. Also open for any additions to this list.\n\n Edit: UPDATE\n\nPeople of reddit, you are incredible! I did not expect this to be read and commented on by so many people. And I am honestly touched by the sympathy, concern and advice in your responses. I will try to reply to as many as possible in the next couple of days. Not sure whether to be happy or sad to see that so many people feel the same, but I am glad that some of your were able to improve their situation, be it in a new position or a completely new field of work.\n\nMost of you have suggested burnout, and I agree that it is time for a break for me (as soon as I can afford it). In the long run, I am still considering changing profession. I feel like my brain is just not suitable for doing all these things at once. I started programming because I did enjoy solving problems and the abstract thinking that is needed. But the IT world just seems too fast-paced for me. The jobs I had before, where I had to physically do something (mostly service and hospitality industry) were exhausting and at times it was hard not to hate people, but they weren’t frying my brain in the way that is is being fried now. It came with a different kind of satisfaction, and I guess this is something that differs from person to person. \n\nI also appreciate the people who took the time to tell me to suck it up. There was no need to be rude, but sometimes such comments put things into perspective again.\n\nMy offline hobby is cycling and taking longer bike trips, but I might try some of the things you suggested too, especially the ones that are about creating things. \n\nAgain, thank you very much for sharing your own stories and your thoughts!\n\nPS: I am a woman, but happy to be your bro. Also, I’m European.", "author": "Realistic_Shoulder13", "created_time": "2025-04-09T19:59:18", "url": "https://reddit.com/r/webdev/comments/1jvf7he/i_am_a_software_developer_and_i_am_tired_and_i/", "upvotes": 1443, "comments_count": 457, "sentiment": "bullish", "engagement_score": 2357.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jvff89", "title": "Study Finds Many Americans Okay with Violence Against Tesla, <PERSON><PERSON>, <PERSON>", "content": "", "author": "InitialSheepherder4", "created_time": "2025-04-09T20:08:00", "url": "https://reddit.com/r/electriccars/comments/1jvff89/study_finds_many_americans_okay_with_violence/", "upvotes": 4621, "comments_count": 597, "sentiment": "neutral", "engagement_score": 5815.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jvfvtp", "title": "<PERSON><PERSON>: \"The good interpretation of the woke movement is that we want to have empathy for our fellow human beings. <...> But we need to have empathy that is deep, not shallow. Shallow empathy is caring about criminals. Deep empathy is caring about the victims of the criminals.\"", "content": "Full quote from <PERSON><PERSON>:\n\n> The good interpretation of the woke movement is that we want to have empathy for our fellow human beings. Of course, we want to have empathy for our fellow humans.\n\n> I strongly believe we should care about humanity, and we should care about the future. But we need to have empathy that is deep, not shallow. Shallow empathy is caring about criminals. Deep empathy is caring about the victims of the criminals.\n\n> Why do we have repeat violent offenders released on the streets, often with no bail? They prey upon people, innocent people, and then our streets are not safe. \n\n> How can America be the greatest country in the world, and we don't even have safe streets? This is crazy.", "author": "twinbee", "created_time": "2025-04-09T20:27:07", "url": "https://reddit.com/r/elonmusk/comments/1jvfvtp/elon_the_good_interpretation_of_the_woke_movement/", "upvotes": 0, "comments_count": 198, "sentiment": "bullish", "engagement_score": 396.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jvisnt", "title": "Rent won’t cover my $1,316 mortgage + $850 HOA fees. Is paying down the loan with stocks the smart move?", "content": "I need help making a financial decision.\n\nI own a condo and owe $197,216. The payment is $1,316 a month. I have an interest rate of 3.250%. The condo HOA fees are about $850 per month.\n\nRecently, my tenant moved out and I am unable to move into this condo because I am under another lease. The market to sell the condo is not good. I purchased the condo at $230,000 and right now my building has 25 units for sell with most listings between $190-210K. Some on the market over a year. I would lose money if I was to sell. I have the option to rent the condo but no one is willing to rent at $2200 which would cover the mortgage payment and HOA fee.\n\nI have 65,000 in a stock. I was going to use this money as a downpayment for a house because I rather live in another country. However, I need a solution for handling the condo so I am not stuck with another monthly payment making up the difference from the rent.\n\nI am consider using the 65K of stock to pay down the mortgage and recast the mortgage so the payment decrease. I used a calculator to predict what the new payment would be and this would give me a net income of $550 a month.  New payment $575 + $850 = 1425. <PERSON><PERSON><PERSON> suggests I can rent at 1975.\n\nDoes this make sense to do ? Am I missing something ? How much would my stock have to gain to produce $550 a month? Does it make sense to compare the money this way? That I am basically changing my investment from the stock market into my own property.  I am saving money on my interest payments assuming it will outperform the market gains on this single stock. Right?\n\nNo, I cannot do short term rentals.\n\nI hope this does not sound painfully stupid, if so please kind.\n\n\n\n  \nEdit: Thank you all for the replies. I am going to rent for as high as possible and take the negative cash flow. I agree having the cash offers more flexibility for me. Hoping the market corrects itself eventually. ", "author": "OtherwiseIamNobody", "created_time": "2025-04-09T22:33:50", "url": "https://reddit.com/r/personalfinance/comments/1jvisnt/rent_wont_cover_my_1316_mortgage_850_hoa_fees_is/", "upvotes": 0, "comments_count": 35, "sentiment": "bearish", "engagement_score": 70.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}]