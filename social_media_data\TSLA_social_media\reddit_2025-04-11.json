[{"platform": "reddit", "post_id": "reddit_1jwd473", "title": "FedEx Stole My 4090 FE RMA Inbound to Nvidia - I'm having a panic attack", "content": "I am making this post to share my nightmare I'm dealing with to this subreddit and the world, and to just vent my frustration right now.\n\nIn October 2022, I bought the 4090 FE from Best Buy and used it for over 2 years just fine no problem. In January 2025 I bought a Corsair HX1500i PSU to replace my aging EVGA 850w T2.\n\n2 weeks later, while upgrading my case, I happened to catch the cable starting to melt in the GPU. I snapped photos, contacted Nvidia customer support, and got an RMA to have the connector/card replaced with the newer 12v-2x6 standard. They gave me a FedEx shipping label to send the card to their Omni RMA center in California.\n\nI was already feeling queasy about sending this card out there. I was googling around about Nvidia FE RMA stories and saw a bunch of horrible situations where users got back physically damaged cards, cards that didn't work, cards with people's hair intertwined in the fans and heatsink, etc. But no one seemed to have an exact situation like mine where the card was stolen in transit to the RMA department from the customer. \n\nMy card arrived on March 26th, Wednesday after a noticeable delay in New Mexico. A week went by and I didn't hear from anyone at Nvidia so I decided to email their customer support about getting a status for my RMA.\n\nI just received the reply tonight: my card wasn't in the box, and they claim no signs of tampering were present on the packaging.\n\nMy heart sank, my stomach is in knots, and I feel like I'm going to throw up.\n\nThey told me I will have to file a claim with FedEx. I've also seen reports in the past on this very subreddit where users were in a similar situation and they were told only Nvidia can make the claim since it was their paid shipping label.\n\nI don't know what to do at this point. I don't know who to contact, I don't know how to process this. I am devastated. <PERSON> help me.", "author": "KuraiS<PERSON>dos<PERSON>", "created_time": "2025-04-11T00:27:23", "url": "https://reddit.com/r/nvidia/comments/1jwd473/fedex_stole_my_4090_fe_rma_inbound_to_nvidia_im/", "upvotes": 1172, "comments_count": 309, "sentiment": "bullish", "engagement_score": 1790.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwft4d", "title": "I (22) made over $100k this week trading options and don’t know what to do now", "content": "Hey guys! I (22) currently have about $115k sitting in my Robinhood account. I got insanely lucky with the huge swings in the market while trading options this week and turned $1.3k into this amount. I’m done options trading now because I know I will lose it. Any advice what to do with this $115k?\n\nI am considering just putting it all into the S&P500. Should I lump sum or DCA? Or not at all?\n\nAlso considering maxing out traditional IRA for both 2024 and 2025. \n\nI’m also wondering if I should take a chunk out and buy a house. \n\nAny advice would be appreciated! \n\n\n\nUPDATE as of 2:30 pm 4/11:\n\nThis morning I withdrew 20k to have some safety just in case. I saw opportunity and bought 0dte spy calls (I know that wasn’t smart). Anyways spy went up and I sold near the top.\n\nI NOW have an account balance of $176,000. I am planning on withdrawing everything except for 25k, and then figure out what to do with the rest.\n\nI promise, I’m actually done with options for good. I know how lucky I am and most people lose it all by continuing. Thank you everyone for all the comments. Does your advice change with the amount increased?\n\nThere are a lot of comments about maxing out Roth IRA but I don’t think I would qualify, isn’t there an income limit? So traditional is probably the way to go?\n\n", "author": "Mysterious_Hat6681", "created_time": "2025-04-11T02:49:59", "url": "https://reddit.com/r/investing_discussion/comments/1jwft4d/i_22_made_over_100k_this_week_trading_options_and/", "upvotes": 953, "comments_count": 594, "sentiment": "bullish", "engagement_score": 2141.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwfyas", "title": "Next month, your $20 product from China could cost you $50 before it even hits your warehouse. What's your plan?", "content": "The 145% tariff hits next month. For anyone sourcing from China, this isn’t a bump — it’s a wrecking ball.  Are you moving your supply chain? Raising prices? Getting out completely? Genuinely curious how small brands are planning to survive what feels like the final boss of import costs.  If you're staying in the game, you're gonna need a real strategy.", "author": "fulltrendypro", "created_time": "2025-04-11T02:57:54", "url": "https://reddit.com/r/smallbusiness/comments/1jwfyas/next_month_your_20_product_from_china_could_cost/", "upvotes": 852, "comments_count": 476, "sentiment": "neutral", "engagement_score": 1804.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwgjst", "title": "Looking for some help in Google Ads", "content": "Hi all,\n\nI have been running Google Ads for a while, but compared to Meta, my cost per conversion is very very unstable, which is why I have not been able to scale the budget.\n\nIs this a common problem everybody faces or just unique to my account?", "author": "head<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-11T03:31:00", "url": "https://reddit.com/r/adwords/comments/1jwgjst/looking_for_some_help_in_google_ads/", "upvotes": 2, "comments_count": 8, "sentiment": "neutral", "engagement_score": 18.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwhl4b", "title": "Tesla suspends taking new orders for Model S and Model X on Chinese website", "content": "", "author": "esporx", "created_time": "2025-04-11T04:30:45", "url": "https://reddit.com/r/technology/comments/1jwhl4b/tesla_suspends_taking_new_orders_for_model_s_and/", "upvotes": 315, "comments_count": 44, "sentiment": "neutral", "engagement_score": 403.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwhxg2", "title": "Help. Mom spent entire portfolio on TSLA at 375 on a whim.", "content": "Title explains the situation but for some context: \n\nI just found out my mom spent her entire portfolio (7k) on TSLA in February. I know 7k is not a lot for some but for her it is significant - not gonna put us in serious trouble or anything but it is hard earned money.\n\nShe said she \"just felt like it\" one morning. I was furious but nothing I can do about it now.\n\nWhat do I do? Already down about 2.3k and wondering if we should just cut our losses ASAP or hold out hope. It just hurts knowing she quite literally threw thousands of dollars down the drain.\n\nEdit: 1. Not American. 2. My mom's not an Elon fan. She barely knows who he is, and she barely speaks English 😭 I understand the absurdity of the situation and I wish I knew why she did that too", "author": "chocolateganache444", "created_time": "2025-04-11T04:52:27", "url": "https://reddit.com/r/stocks/comments/1jwhxg2/help_mom_spent_entire_portfolio_on_tsla_at_375_on/", "upvotes": 883, "comments_count": 468, "sentiment": "neutral", "engagement_score": 1819.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwiktd", "title": "$GOOGL growth trajectory?", "content": "All you people that believe in Alphabets growth in the next 5 years. 52% of $GOOGLE revenue comes from advertising through searching on Google. How is this business not being complete cannibalized by Chat GPT, Grok, Mistral, Gemini etc. \nFor myself I can say that I almost never use Google search anymore. Please explain how $GOOGLE predicted growth trajectory makes sense?", "author": "Pretty-Spot-8197", "created_time": "2025-04-11T05:34:59", "url": "https://reddit.com/r/ValueInvesting/comments/1jwiktd/googl_growth_trajectory/", "upvotes": 0, "comments_count": 69, "sentiment": "bullish", "engagement_score": 138.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwkfp0", "title": "🚨 Passwords: The Evil We Still Need (Securing Microsoft Business Premium Part 04)", "content": "Passwordless is the ideal future we’re all striving for—but let's face it, the harsh reality is that many organizations, especially SMBs aren't there yet. Passwords remain a necessary evil that organizations need to handle securely and effectively.\n\nIn Part 04 of my detailed security series, I dive into how **Microsoft Entra’s Self-Service Password Reset (SSPR)** and **Password Protection** features can make dealing with passwords significantly less painful:\n\n* Empower users to reset their own passwords securely, reducing helpdesk friction.\n* Utilize Microsoft's advanced password protection tools to proactively guard against weak passwords and common attacks.\n* Configure robust password policies easily in both cloud-only and hybrid AD environments.\n\nPasswords aren't going away tomorrow, so let’s handle them responsibly today.\n\n👉 [Check out the full article](https://www.chanceofsecurity.com/post/securing-microsoft-business-premium-part-04-password-protection)\n\nThoughts, feedback, and experiences welcome!", "author": "Noble_Efficiency13", "created_time": "2025-04-11T07:48:58", "url": "https://reddit.com/r/cloudcomputing/comments/1jwkfp0/passwords_the_evil_we_still_need_securing/", "upvotes": 6, "comments_count": 0, "sentiment": "bearish", "engagement_score": 6.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwov7g", "title": "Preliminary results from MC-Bench with several new models including Optimus-Alpha and Grok-3.", "content": "", "author": "CheekyBastard55", "created_time": "2025-04-11T12:40:08", "url": "https://reddit.com/r/singularity/comments/1jwov7g/preliminary_results_from_mcbench_with_several_new/", "upvotes": 0, "comments_count": 46, "sentiment": "neutral", "engagement_score": 92.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwpp21", "title": "Tesla stops taking new orders in China for two imported, U.S.-made models: Reuters", "content": "", "author": "abdas<PERSON>", "created_time": "2025-04-11T13:19:53", "url": "https://reddit.com/r/Economics/comments/1jwpp21/tesla_stops_taking_new_orders_in_china_for_two/", "upvotes": 79, "comments_count": 5, "sentiment": "neutral", "engagement_score": 89.0, "source_subreddit": "Economics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwqg2v", "title": "The <PERSON> administration is begging <PERSON> to call Trump quickly.", "content": "President <PERSON> granted a 90-day tariff reprieve to most countries, boosting global markets, but escalated tariffs on China to 145% on all Chinese goods entering the US. In retaliation, China raised tariffs on American goods to 125%. Despite US efforts to arrange a call between <PERSON> and Chinese President <PERSON>, Beijing has refused, with <PERSON> emphasizing China’s self-reliance and readiness for a prolonged trade conflict. The White House insists China must make the first move, while <PERSON> believes Beijing will eventually seek a deal to address issues like US exports, fentanyl, and TikTok. The escalating trade war between the two superpowers shows no signs of easing as both sides wait for the other to yield.", "author": "DoublePatouain", "created_time": "2025-04-11T13:54:11", "url": "https://reddit.com/r/StockMarket/comments/1jwqg2v/the_trump_administration_is_begging_xi_jinping_to/", "upvotes": 33132, "comments_count": 4137, "sentiment": "bullish", "engagement_score": 41406.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwsz9l", "title": "The US bond market is continuing to crash. Will this make Trump back off of China?", "content": "Bond yields peaked right before <PERSON> paused his tariffs. <PERSON> himself even said he paused because the bond markets were getting “yippy” and lots of sources say the bond market crash was his main reason for the pause. \n\nToday bond yields have spiked just as high as they were when <PERSON> enacted his 90 day pause. He clearly cares about this measure given the action he took a few days ago. Could this continued sell off on bonds cause Trump to back off of China? \n\nAccording the the FT, ten minutes ago, talking about bond yields today: “They point to a complete loss of faith in the strongest bond market in the world.”\n\nI know I’m the one who asked the question, but in my opinion it seems like there’s a decent chance this will cause <PERSON> to back off, because otherwise this will lead to a much larger crisis, with the U.S losing its reserve currency status and the debt becoming more expensive than whatever we may generate from tariffs. He already backed off once for the same reason….\n", "author": "Jshbone12", "created_time": "2025-04-11T15:43:29", "url": "https://reddit.com/r/stocks/comments/1jwsz9l/the_us_bond_market_is_continuing_to_crash_will/", "upvotes": 10860, "comments_count": 1438, "sentiment": "bearish", "engagement_score": 13736.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1j<PERSON><PERSON>", "title": "Share your startup - quarterly post", "content": "Share Your Startup - Q4 2023\n\n[**r/startups**](https://www.reddit.com/r/startups/) **wants to hear what you're working on!**\n\nTell us about your startup in a comment within this submission. Follow this template:\n=====================================================================================\n\n​\n\n*   **Startup Name / URL**\n*   **Location of Your Headquarters**\n    *   Let people know where you are based for possible local networking with you and to share local resources with you\n*   **Elevator Pitch/Explainer Video**\n*   **More details:**\n    *   What life cycle stage is your startup at? (reference the stages below)\n    *   Your role?\n*   **What goals are you trying to reach this month?**\n    *   How could [r/startups](https://www.reddit.com/r/startups/) help?\n    *   Do **NOT** solicit funds publicly--this may be illegal for you to do so\n*   **Discount for** [r/startups](/r/startups) **subscribers?**\n    *   Share how our community can get a discount\n\n​\n\n\n\\--------------------------------------------------\n\n​\n\n**Startup Life Cycle Stages** (Max Marmer life cycle model for startups as used by Startup Genome and Kauffman Foundation)\n\n**Discovery**\n\n*   Researching the market, the competitors, and the potential users\n*   Designing the first iteration of the user experience\n*   Working towards problem/solution fit (Market Validation)\n*   Building MVP\n\n​\n\n**Validation**\n\n*   Achieved problem/solution fit (Market Validation)\n*   MVP launched\n*   Conducting Product Validation\n*   Revising/refining user experience based on results of Product Validation tests\n*   Refining Product through new Versions (Ver.1+)\n*   Working towards product/market fit\n\n​\n\n**Efficiency**\n\n*   Achieved product/market fit\n*   Preparing to begin the scaling process\n*   Optimizing the user experience to handle aggressive user growth at scale\n*   Optimizing the performance of the product to handle aggressive user growth at scale\n*   Optimizing the operational workflows and systems in preparation for scaling\n*   Conducting validation tests of scaling strategies\n\n​\n\n**Scaling**\n\n*   Achieved validation of scaling strategies\n*   Achieved an acceptable level of optimization of the operational systems\n*   Actively pushing forward with aggressive growth\n*   Conducting validation tests to achieve a repeatable sales process at scale\n\n​\n\n**Profit Maximization**\n\n*   Successfully scaled the business and can now be considered an established company\n*   Expanding production and operations in order to increase revenue\n*   Optimizing systems to maximize profits\n\n​\n\n**Renewal**\n\n*   Has achieved near-peak profits\n*   Has achieved near-peak optimization of systems\n*   Actively seeking to reinvent the company and core products to stay innovative\n*   Actively seeking to acquire other companies and technologies to expand market share and relevancy\n*   Actively exploring horizontal and vertical expansion to increase prevent the decline of the company", "author": "julian88888888", "created_time": "2025-04-11T17:01:09", "url": "https://reddit.com/r/startups/comments/1jwuuer/share_your_startup_quarterly_post/", "upvotes": 50, "comments_count": 629, "sentiment": "bullish", "engagement_score": 1308.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwxxil", "title": "Google's Cos<PERSON>nti<PERSON> finds what took Researchers a Decade", "content": "The article at [https://www.techspot.com/news/106874-ai-accelerates-superbug-solution-completing-two-days-what.html](https://www.techspot.com/news/106874-ai-accelerates-superbug-solution-completing-two-days-what.html) highlights a Google AI CoScientist project featuring a multi-agent system that generates original hypotheses without any gradient-based training. It runs on base LLMs, Gemini 2.0, which engage in back-and-forth arguments. This shows how “test-time compute scaling” without RL can create genuinely creative ideas.\n\n**System overview**\nThe system starts with base LLMs that are not trained through gradient descent. Instead, multiple agents collaborate, challenge, and refine each other’s ideas. The process hinges on hypothesis creation, critical feedback, and iterative refinement.\n\n**Hypothesis Production and Feedback** \nAn agent first proposes a set of hypotheses. Another agent then critiques or reviews these hypotheses. The interplay between proposal and critique drives the early phase of exploration and ensures each idea receives scrutiny before moving forward.\n\n**Agent Tournaments**\nTo filter and refine the pool of ideas, the system conducts tournaments where two hypotheses go head-to-head, and the stronger one prevails. The selection is informed by the critiques and debates previously attached to each hypothesis.\n\n**Evolution and Refinement**\nA specialized evolution agent then takes the best hypothesis from a tournament and refines it using the critiques. This updated hypothesis is submitted once more to additional tournaments. The repeated loop of proposing, debating, selecting, and refining systematically sharpens each idea’s quality.\n\n**Meta-Review** \nA meta-review agent oversees all outputs, reviews, hypotheses, and debates. It draws on insights from each round of feedback and suggests broader or deeper improvements to guide the next generation of hypotheses.\n\n\n\nFuture Role of RL Though gradient-based training is absent in the current setup, the authors note that reinforcement learning might be integrated down the line to enhance the system’s capabilities. For now, the focus remains on agents’ ability to critique and refine one another’s ideas during inference.\n\nPower of LLM Judgment A standout aspect of the project is how effectively the language models serve as judges. Their capacity to generate creative theories appears to scale alongside their aptitude for evaluating and critiquing them. This result signals the value of “judgment-based” processes in pushing AI toward more powerful, reliable, and novel outputs.\n\nConclusion Through discussion, self-reflection, and iterative testing, Google AI CoScientist leverages multi-agent debates to produce innovative hypotheses—without further gradient-based training or RL. It underscores the potential of “test-time compute scaling” to cultivate not only effective but truly novel solutions, especially when LLMs play the role of critics and referees.", "author": "PianistWinter8293", "created_time": "2025-04-11T19:10:16", "url": "https://reddit.com/r/artificial/comments/1jwxxil/googles_coscientist_finds_what_took_researchers_a/", "upvotes": 20, "comments_count": 11, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jwxyfl", "title": "<PERSON> - Heavy Rain Test - 2026 Tesla Model Y HW4 FSD", "content": "", "author": "TheKingHippo", "created_time": "2025-04-11T19:11:26", "url": "https://reddit.com/r/SelfDrivingCars/comments/1jwxyfl/mark_rober_debunk_heavy_rain_test_2026_tesla/", "upvotes": 98, "comments_count": 244, "sentiment": "neutral", "engagement_score": 586.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jx1moi", "title": "People who say markets always go up never mention the Nikkei (Japan)", "content": "If you bought the Nikkei225 in 1989, you’d be down around 10% right now excluding dividends. Could we be headed for something similar in the major US markets. ", "author": "LongTheLlama", "created_time": "2025-04-11T21:50:32", "url": "https://reddit.com/r/ValueInvesting/comments/1jx1moi/people_who_say_markets_always_go_up_never_mention/", "upvotes": 474, "comments_count": 228, "sentiment": "neutral", "engagement_score": 930.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jx3q6e", "title": "Tesla US Sales Are Worse Than You Think — And Here's Why", "content": "", "author": "1oneplus", "created_time": "2025-04-11T23:29:01", "url": "https://reddit.com/r/electriccars/comments/1jx3q6e/tesla_us_sales_are_worse_than_you_think_and_heres/", "upvotes": 987, "comments_count": 140, "sentiment": "neutral", "engagement_score": 1267.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}]