[{"platform": "reddit", "post_id": "reddit_1hgot8d", "title": "What should my next move be for my bidding strategy? (Performance Max)", "content": "I started running Google Ads for a small-scale e-commerce store three months ago. Below, I will provide a brief review as well as some stats. I think this review and the responses you guys provide can help lots of vulnerable people in this space.\n\nAs of right now, I am using a $100.00 USD per day budget on Performance Max. My bidding strategy is set to maximize conversions, fully automated with no target CPA set. **Last month (November) I had a 271% ROAS, 0.58% CTR, 92 Purchases, 4.54% Conv. Rate.** Now I know those stats don't mean much without context of my business model. **This month (December) which is a bit more than halfway finished, I had a 340% ROAS, 0.69% CTR, 89 purchases, 6.17% Conv. Rate.** I know I know, CTR is not great.\n\nNow, I have made attempts at Target CPA previously, and Target ROAS as well, but had no success. Why do I think I was unsuccessful?\n\n1. I did not have enough conversion data\n2. I perhaps didn't set the correct Target CPA/ROAS\n\nMy business model is a little different than most stores. We sell multiple versions of the same product. 95% of our entire inventory, is priced at $19.99. Which makes me think, is the \"conversion value, target ROAS\" route not for me? I hear everyone talking about how just about every e-commerce store ends up running some sort of value-based strategy, but I've also heard that value-based bidding is meant for stores with higher ticket items, which we do not have or plan on having.\n\n**My plan for January 1st:** as of right now, my plan is to let this thing run on \"maximize conversions\" automated bidding until the end of the year. Then on January 1st, I will be evaluating my campaign and maybe try out \"conversion value\" once again. Now that we have more conversion data, maybe it will do better. Maximize conversions did great during the Holidays, definitely let us find some new customers. But... without the holiday traffic, it was very hard to push past 300% ROAS. If it wasn't for the holidays, I believe I would be stuck in the 200% range again. Let me know of any opinions, advice, criticism. Anything helps more than you guys know. Have a great rest of your year!", "author": "ProfessionalGoat99", "created_time": "2024-12-18T00:07:50", "url": "https://reddit.com/r/adwords/comments/1hgot8d/what_should_my_next_move_be_for_my_bidding/", "upvotes": 4, "comments_count": 7, "sentiment": "bearish", "engagement_score": 18.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgqw1s", "title": "The FTC is officially banning hidden junk fees from hotel and ticket prices | The new rule will require businesses to disclose the total price of a hotel stay or live event tickets before checkout.", "content": "", "author": "ControlCAD", "created_time": "2024-12-18T01:52:34", "url": "https://reddit.com/r/technews/comments/1hgqw1s/the_ftc_is_officially_banning_hidden_junk_fees/", "upvotes": 2886, "comments_count": 105, "sentiment": "neutral", "engagement_score": 3096.0, "source_subreddit": "technews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgrah5", "title": "The new era of driverless driving began,Tesla/WiMi Innovation Application Pilot Zone is the future - Newstrail", "content": "", "author": "Gas<PERSON>den", "created_time": "2024-12-18T02:13:33", "url": "https://reddit.com/r/Economics/comments/1hgrah5/the_new_era_of_driverless_driving_beganteslawimi/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "Economics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgs1my", "title": "Google Delivered an empty box to my home, now Im down $1,378.75", "content": "During the black friday sales, I purchased a **Pixel 9 Pro XL Obsidian 512GB (Unlocked)** from google, the box was delayed and arrived a day later than expected.\n\nWhen It arrived I noticed on a NEST video the delivery driver doing something weird, after placing the box, he turned it around, in hindsight this was to allow to hide the package was already open.\n\nHe took his picture [https://imgur.com/qCeB8Hw](https://imgur.com/qCeB8Hw) You can see the damaged box on the bottom.\n\nWhen bringing the box in it looked bad, [https://imgur.com/CmhK4Vq](https://imgur.com/CmhK4Vq) the content of my pixel pro had been pilfered, only delivering the case, glass and hand in kit. [https://imgur.com/mgknAKg](https://imgur.com/mgknAKg) .\n\nI contacted Google Support, their \"specialists\" have determined I don't have a case, they delivered a 100% of the products.\n\nIm down a 1378.5 dollars on this, and have been stollen from, by FEDEX, and google. I used to believe google not being evil was just a stance. I've found out the hard way, they will steal from you and not even think twice. No matter how many videos or photos you send you are now their wage slave, and you have to like it.\n\nIf anyone has ideas or a way to get to someone that can actually help feel free to post.", "author": "Antiokloodun", "created_time": "2024-12-18T02:54:12", "url": "https://reddit.com/r/google/comments/1hgs1my/google_delivered_an_empty_box_to_my_home_now_im/", "upvotes": 885, "comments_count": 215, "sentiment": "neutral", "engagement_score": 1315.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgs8cv", "title": "What's the Hardest Lesson You Learned Trading Penny Stocks?", "content": "Hey everyone, I’ve been dabbling in penny stocks for a little while now, and man, it’s been a wild ride. I’ve had a couple of ‘too good to be true’ plays that crashed hard, and a few random bets that somehow paid off. One big lesson I’ve learned (the hard way) is that hype isn’t a strategy — chasing the ‘next big thing’ without research almost always backfires.\n\nThat got me thinking — what’s your biggest lesson from trading penny stocks that you wish you knew earlier? Maybe it’s something that saved you from a big loss or a mindset shift that changed how you trade. I feel like there’s a ton of wisdom in this community that could help the rest of us avoid the same mistakes. Drop your thoughts — I’m all ears!\n", "author": "HelpfulStuff5626", "created_time": "2024-12-18T03:04:05", "url": "https://reddit.com/r/pennystocks/comments/1hgs8cv/whats_the_hardest_lesson_you_learned_trading/", "upvotes": 118, "comments_count": 233, "sentiment": "bearish", "engagement_score": 584.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgshyg", "title": "Where are the Tesla bears at? ", "content": "I have an irresponsibly long Tesla position. Roughly 50% of my portfolio in equity and a large 5x levered long call option position. I can’t see this company not capturing a significant chunk of the $50 trillion Total Addressable Market of humanoid robotics, which is a standalone investment thesis for being bullish on Tesla. T<PERSON> is obviously doesn’t take into consideration any of the other parts of their business. \n\nOutside of black swan events and Elon falling out with <PERSON>. Why would someone be bearish Tesla? I’m genuinely hoping that someone can change my mind. Fire away! ", "author": "wpottenger", "created_time": "2024-12-18T03:18:16", "url": "https://reddit.com/r/teslainvestorsclub/comments/1hgshyg/where_are_the_tesla_bears_at/", "upvotes": 23, "comments_count": 263, "sentiment": "neutral", "engagement_score": 549.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgto7k", "title": "Can someone explain this gain on my GOOG call options?", "content": "Body:\nI bought GOOG call options and I don’t understand why I am seeing a profit even though the price has not yet touched the breakeven point.\n\nHere are the key details of my position:\n\nContracts: +5\nAverage Cost: $0.04\nCurrent Price of the Option: $0.31\nMarket Value: $155.00\nDate Bought: 12/17\nExpiration Date: 12/20\nGOOG Breakeven Price: $245.04\nCurrent GOOG Price: $197.36\nToday’s Return: +$135.00 (+675.00%)\nTotal Return: +$135.00 (+675.00%)\nThe Greeks (Stats):\nDelta: 0.0355\nGamma: 0.0034\nTheta: -0.3328\nVega: 0.0132\nRho: 0.0005\nOther Stats:\n\nIV (Implied Volatility): 135.41%\nBid: $0.01 x 50\nAsk: $0.61 x 416\nMark Price: $0.31\nLast Trade Price: $0.01\nPrevious Close: $0.04\nVolume: 6\nOpen Interest: 537\nQuestion:\nI assumed that profit would only occur if the price of GOOG rises above the breakeven price ($245.04). Can someone explain why I am seeing a gain even though the price is at $197.36?\n\nIs this related to the IV (implied volatility) or some other factor in the options pricing?\nAny insights would be greatly appreciated!", "author": "satyam0795", "created_time": "2024-12-18T04:23:46", "url": "https://reddit.com/r/options/comments/1hgto7k/can_someone_explain_this_gain_on_my_goog_call/", "upvotes": 0, "comments_count": 20, "sentiment": "bullish", "engagement_score": 40.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgtss7", "title": "Tesla Nacs Charging in the Ariya", "content": "Just received my OEM nissan Nacs Adapter impressed and relieved  ", "author": "leot292", "created_time": "2024-12-18T04:30:57", "url": "https://reddit.com/r/electriccars/comments/1hgtss7/tesla_nacs_charging_in_the_ariya/", "upvotes": 26, "comments_count": 1, "sentiment": "neutral", "engagement_score": 28.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgxube", "title": "How long does it to train a new PPC guy?", "content": "Hello everyone. \n\nIf you had to train someone new for your job, how much time do you think you would need? (lets say someone who studied marketing but its their first job)\n\nand how long did your training take? when did you feel \"ok i got this, i can do everything here\"?\n\n", "author": "Wight3012", "created_time": "2024-12-18T09:13:10", "url": "https://reddit.com/r/PPC/comments/1hgxube/how_long_does_it_to_train_a_new_ppc_guy/", "upvotes": 2, "comments_count": 25, "sentiment": "bullish", "engagement_score": 52.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgxwp5", "title": "has YC lost its aura?", "content": "I literally see YC accepting literal college freshman who have never scaled a business let alone sell a peice of software or even lemonade at a lemonade stand, accepting like super \"basic\" (imo) ideas, or even just like people/ideas in general that don't come off as super qualified (i understand its subjective to a certain extent).\n\nkeep in mind, the CEO of replit got rejected from YC 4 times as the founder of a company already doing like 6-7 figures in annual revenue, made the JS REPL breakthrough in 2011 as a kid from jordan that got crazy amount of recogntiion from dev community and even tweeted about by CTO of mozilla at the time, and like only got accepted into YC because <PERSON><PERSON> himself literally referred him to <PERSON>tman\n\n", "author": "deleted", "created_time": "2024-12-18T09:18:36", "url": "https://reddit.com/r/startups/comments/1hgxwp5/has_yc_lost_its_aura/", "upvotes": 307, "comments_count": 153, "sentiment": "bullish", "engagement_score": 613.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgyxy8", "title": "‘Deep slander’ to accuse Ireland of being antisemitic, President says", "content": "", "author": "HellYeahDamnWrite", "created_time": "2024-12-18T10:40:19", "url": "https://reddit.com/r/worldnews/comments/1hgyxy8/deep_slander_to_accuse_ireland_of_being/", "upvotes": 5, "comments_count": 94, "sentiment": "neutral", "engagement_score": 193.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgzxlb", "title": "Google's Waymo self driving taxis succeeding in San Francisco, and expanding", "content": "According to Reuters: \"When I visited San Francisco last month and rode in Waymo robotaxis, I ordered them via the Uber app and Waymo’s own. Waymo seems to be not only succeeding in the city by the Bay but expanding: it announced it would start service in Miami in 2026 two weeks ago; shares in Uber and Lyft slumped upon the news. Just before Cruise announced its demise, Uber publicized a new partnership with the Chinese autonomous vehicle maker WeRide in Abu Dhabi. WeRide makes the cars, Uber sends them your way.\"\n\nSo GM is shutting down Cruise. But the mainly software company Google is succeeding in self driving taxis. It shows how important software has become for the automotive companies, along with microchips. With robotaxis, automobiles will become high tech. It is unfair to highlight single events with self driving companies. Instead look at the overall impact on safety by metrics such as accidents per mile driven or otherwise, and compare it with human driven miles or otherwise.\n\nIndia would be a more difficult territory for robitaxis like Waymo. Uber and Indian OLA, are market leaders in app based taxi services at least where I live in New Delhi NCR. Most of the problems are not technical, but based on poor customer service. But drivers in India, can be faced with not following the traffic rules, and narrow streets. Thus implementing robotaxis will remove the driver and poor customer service, but AI software will have to be trained in Indian driving and road conditions. So I would be glad if Waymo entered the market, at least in the largest cities, where it will be easier to implement robotaxis. Or Uber partnered with a Chinese automaker, and introducing robotaxis to large metros in India.\n\nReference: https://www.theguardian.com/technology/2024/dec/16/robotaxis-general-motors-self-driving", "author": "fool49", "created_time": "2024-12-18T11:52:37", "url": "https://reddit.com/r/economy/comments/1hgzxlb/googles_waymo_self_driving_taxis_succeeding_in/", "upvotes": 0, "comments_count": 0, "sentiment": "bullish", "engagement_score": 0.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh03ri", "title": "Google is winning the ai race", "content": "Gemini 2.0 - top of the LLM arena, cheapest model per token if we account for quality it's way cheaper than competitors, 1/0100th the cost of o1\n\nTPU - <PERSON><PERSON> said, more compute and better compute infra than all their competitors and it isn't close\n\nNotebookLM - amazing product\n\nVeo 2/imagen 3 - SOTA, massively ahead of competition with a gap similar to that of gpt 3.5 vs gpt 4\n\nWaymo - only company I'm aware of offering driverless taxi services\n\nNot even mentioning DeepMind, alpha fold, alpha geometry, etc. what else have I missed?\n\nRemember: consensus on this sub was that Google is too big to innovate..now sentiment is shifting to \"they were always going to win anyway\"", "author": "AverageUnited3237", "created_time": "2024-12-18T12:03:09", "url": "https://reddit.com/r/singularity/comments/1hh03ri/google_is_winning_the_ai_race/", "upvotes": 1241, "comments_count": 382, "sentiment": "neutral", "engagement_score": 2005.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh0ovm", "title": "Income question ", "content": "When filling out an application for a credit card, loan or similar, what do you generally put down for income?\n\nWe get about $85k/yr social security and I have our “bank” send us $10k/month. They also pay our mortgage and property taxes and insurance directly and a few other minor things. So that’s about $160k/yr plus the $85k mentioned earlier \n\nWe have a nest egg of about $7M so in reality our declared “income” could be a lot more but we are really only drawing what we spend.   So, would you write down $245k or maybe round up to $300k? Or something different?  \n\nA couple years ago we were drawing less (actual expenses were less) and I applied for a different credit card and kept running into the limit each month  I also intend to buy a new car this year and will probably fill out a loan app for ~$100k and want lowest possible rate \n\nI never really know what to put down so it’s never consistent ", "author": "Effyew4t5", "created_time": "2024-12-18T12:39:17", "url": "https://reddit.com/r/financialindependence/comments/1hh0ovm/income_question/", "upvotes": 0, "comments_count": 95, "sentiment": "bullish", "engagement_score": 190.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh0p46", "title": "Google's <PERSON><PERSON> is really next gen. These are all videos made by people who had preview access and posted them on X. You can easily generate intros and design that are animated. It can even do anime. Especially crazy because it can generate them in 4k. ", "content": "", "author": "GodEmperor23", "created_time": "2024-12-18T12:39:43", "url": "https://reddit.com/r/singularity/comments/1hh0p46/googles_veo_is_really_next_gen_these_are_all/", "upvotes": 1128, "comments_count": 213, "sentiment": "neutral", "engagement_score": 1554.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh11fo", "title": "(dinner meetings) how can i socially explain you don’t Drink Alcohol without making it weird or kill the”mood”", "content": "How to socially explain to someone you don’t Drink (smoke or do drugs) without making it weird. People in their mid-late 20s and 30s, any suggestions? \n\n- (I am mainly concerned with the drinking aspect as it is so socially acceptable and prevalent nowadays when I go out with friends or have a business meeting in a restaurant setting)\n\nI don’t smoke or drink or take drugs anymore and I am realizing that it sometimes makes people feel awkward, especially if we’re out at bars and I’m drinking a soda (people usually assume that it’s alcohol alcoholic) and they offered to buy me a drink and then I tell them that I don’t drink and then if they don’t ask why then it usually just gets awkward although some people are just very respectable. any ideas on what to say? Or are there any other social sober people out there in their mid 20s and 30s?\n\nI know that <PERSON> doesn’t drink and he was very successful in business, but he has the story of his brother who died from alcoholism to tell somebody who asks him why he doesn’t drink. I don’t have something like that to say in a business setting where alcohol is very prevalent. ", "author": "deleted", "created_time": "2024-12-18T12:59:33", "url": "https://reddit.com/r/business/comments/1hh11fo/dinner_meetings_how_can_i_socially_explain_you/", "upvotes": 0, "comments_count": 75, "sentiment": "bullish", "engagement_score": 150.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh1swl", "title": "I received dividends 97 times this year, amounting to a total passive income of $12 245😳 interesting point", "content": "In general, I like to watch similar year’s results, every time I find some interesting points that I could not track during the analysis", "author": "InvestorFrench", "created_time": "2024-12-18T13:40:34", "url": "https://reddit.com/r/dividends/comments/1hh1swl/i_received_dividends_97_times_this_year_amounting/", "upvotes": 737, "comments_count": 87, "sentiment": "neutral", "engagement_score": 911.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh2tne", "title": "In reversal, key House panel votes to release <PERSON> ethics report", "content": "", "author": "<PERSON><PERSON><PERSON>", "created_time": "2024-12-18T14:32:16", "url": "https://reddit.com/r/politics/comments/1hh2tne/in_reversal_key_house_panel_votes_to_release_matt/", "upvotes": 44571, "comments_count": 1269, "sentiment": "neutral", "engagement_score": 47109.0, "source_subreddit": "politics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh33nd", "title": "Killer captured by Google Maps while moving dismembered body in northern Spanish village", "content": "A picture is worth a thousand words. A red car and a man bent over and putting a large white bag in the boot. It's the image from Google Maps Street View application that helped National Police officers solve a murder in Soria, in north-central Spain, which they had started investigating more than a year ago.\n\nA vehicle from the search engine giant equipped with numerous cameras on its roof photographed every corner of Tajueco in Soria (Castilla y León). It was a routine visit as part of its route to take images for Google street view until it arrived at Calle El Norte.\n\nThere, an old red car was parked like any other day and next to it, a man was putting a big bundle in the boot. It could be a normal everyday scene, but it was not, and it proved to be the key piece that solved the puzzle National Police had been trying to put together since November 2023.\n\nSince that date, officers together with Guardia Civil had been looking for a 40-year-old Cuban man. The young man had come to Spain looking for his wife and been missing since November last year. Details were scarce and nobody knew his whereabouts.\n\nAfter a long period of investigation, a strange coincidence set off alarm bells: the Google Street View image of a man putting a bulky white plastic bag in the boot of a car in Calle El Norte de Tajueco.\n\nPolice officers discovered who the car belonged to and tapped the suspect's phone. They found out that he lived with the Cuban wife of the missing man. After months of listening to the couple's conversations, both were arrested and charged in connection with the crime.\n\nAfter ten months of investigation, the case has been solved. The remains of the young Cuban man were found dismembered in the cemetery of the municipality of Andaluz, just 12 minutes from the location where the vital image that solved the crime was taken.", "author": "Swissdanielle", "created_time": "2024-12-18T14:46:12", "url": "https://reddit.com/r/nottheonion/comments/1hh33nd/killer_captured_by_google_maps_while_moving/", "upvotes": 9126, "comments_count": 150, "sentiment": "bullish", "engagement_score": 9426.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh3o9p", "title": "o1-preview is far superior to doctors on reasoning tasks and it's not even close ", "content": "Paper: https://arxiv.org/pdf/2412.10849\n\nThread: https://x.com/deedydas/status/1869049071346102729", "author": "MetaKnowing", "created_time": "2024-12-18T15:13:01", "url": "https://reddit.com/r/artificial/comments/1hh3o9p/o1preview_is_far_superior_to_doctors_on_reasoning/", "upvotes": 84, "comments_count": 152, "sentiment": "neutral", "engagement_score": 388.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh470m", "title": "Obviously reddit app knows who am I ", "content": "So I was flagged for ban evasion while using data on my app (which is very fortunate because it eliminates the possibility of my Wi-Fi IP exposing me)\n\nSo with a new account , obviously reddit knows my other banned alt account as well.\n\nIs there some Google setting to stop all apps from knowing who am I, not just reddit \n\n\n", "author": "Blk925ChickenRice", "created_time": "2024-12-18T15:37:27", "url": "https://reddit.com/r/privacy/comments/1hh470m/obviously_reddit_app_knows_who_am_i/", "upvotes": 0, "comments_count": 21, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh6h8l", "title": "Last chance saloon for ATOS ($ATO.FR) ", "content": "Rights issue has been resolved, capital increase has been performed, creditors now own all the new shares and haven't sold a single one as they are all priced too high. High frequency traders and shorters are being squeezed out.\n\nMarket cap is €300M, revenue is €10B prior to the collapse market cap was €14B\n\nShare price at close today was 0.0021 (+40%)\n\nNow is the chance to get 500,000 shares and sit on them.  \n\nThe new ceo has invested 9 million of his own money and has wiped most of ATOS' debt by turning it into equity after diluting causing no adverse effects on stock price. Quite clever actually. \n\nRetail Investor sentiment is positive. It's only up from here in my eyes!\n", "author": "aaaabbbbbccccccccc", "created_time": "2024-12-18T17:17:45", "url": "https://reddit.com/r/pennystocks/comments/1hh6h8l/last_chance_saloon_for_atos_atofr/", "upvotes": 107, "comments_count": 92, "sentiment": "neutral", "engagement_score": 291.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh7uuj", "title": "California will require EV charging for all new residential units in 2026", "content": "", "author": "Bravadette", "created_time": "2024-12-18T18:17:09", "url": "https://reddit.com/r/electricvehicles/comments/1hh7uuj/california_will_require_ev_charging_for_all_new/", "upvotes": 2882, "comments_count": 289, "sentiment": "neutral", "engagement_score": 3460.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh90xs", "title": "[R] LMUnit: Fine-grained Evaluation with Natural Language Unit Tests", "content": "Hi! I'm <PERSON><PERSON>, CTO at Contextual AI 👋. One of the biggest challenges in deploying LLMs is reliably measuring and improving their behavior. Today's evaluation approaches all have significant limitations:\n\n* **Human evaluation** is expensive and inconsistent, especially at the cutting edge of capabilities\n* **Reward models** compress complex quality dimensions into opaque scores and can't be steered after training\n* **LLM judges** have learned biases (like favoring longer responses) and can't learn from human feedback\n\nToday, we're excited to share our work on making LLM evaluation more principled through natural language unit tests:\n\n* **Natural language unit tests paradigm:** Breaking down evaluation into explicit, testable criteria that both technical and non-technical stakeholders can understand\n* **LMUnit:** A state-of-the-art evaluation model achieving SOTA on FLASK/BigGenBench and top-10 on RewardBench\n* **Strong human validation of the paradigm:** Our approach improves inter-annotator agreement from 71% to 86%! \n\nTry it yourself:\n\n* 📝 Paper:[ https://arxiv.org/abs/2412.13091](https://arxiv.org/abs/2412.13091)\n* 💻 API:[ https://contextual.ai/request-lmunit-api](https://contextual.ai/request-lmunit-api)\n* 📚 Blog:[ https://contextual.ai/news/lmunit](https://contextual.ai/news/lmunit)\n\nHappy to answer questions about the work! We're excited to see how people use LMUnit to build more reliable AI systems.\n\nhttps://preview.redd.it/mewe7zz6on7e1.png?width=1355&format=png&auto=webp&s=b04c6aeb185c2d27d593efcdeac28306f847166a\n\n", "author": "a<PERSON>dehal", "created_time": "2024-12-18T19:07:14", "url": "https://reddit.com/r/MachineLearning/comments/1hh90xs/r_lmunit_finegrained_evaluation_with_natural/", "upvotes": 3, "comments_count": 1, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hh99b7", "title": "I Lost My Entire Timeline. If You Did Too, Let’s Work Together!", "content": "Hi everyone!\n\nMany of us, including myself, recently discovered that our Google Maps Timeline data has disappeared due to the migration from the cloud-based Timeline system to the on-device version. If you’ve been affected by this, I want to say how sorry I am that this happened to you. This is incredibly frustrating, and for so many of us, that data represented years of cherished memories.\n\nI wanted to share what I’ve done so far to try and get Google’s attention and encourage all of you to join in. If enough of us come together, we might just make a difference. I’ll go over everything in this post, but in an attempt to organize as much data as possible, I’ve also created a website to collect and organize this information. It allows users to submit their stories or share links to other websites where they’ve posted their story. You can check it out at [https://SaveGoogleTimeline.com](https://SaveGoogleTimeline.com) (backup available at https://SaveOurTimeline.com)\n\n# What I've Done So Far\n\n* **Contacted Google Account Support** After explaining the issue, I was advised to post in the Community Support forums & submit feedback through the Maps app.\n* **Made a Post in the Google Maps Community Forum** Unfortunately, it’s not public yet, but when it is, you’ll be able to view it [here](https://support.google.com/maps/thread/*********?hl=en). In the meantime, I can share the contents of that post in a comment if anyone wants to see it. I think posting here will be an important part of getting Google's attention. My post is slightly different from others I’ve seen in that I directly ask for my request to be forwarded to the Maps team for investigation.\n* **Sent a Certified Letter to Google Headquarters** Based on conversations with a handful of friends who work at Google and advice I received in the forums, I wrote a detailed letter explaining the issue, shared my personal story, and requested an investigation into possible recovery options. I sent the letters via certified mail, but I know this can be cost-prohibitive for some people, so standard mail is probably fine.\n   * I sent a total of three letters: one to Google LLC, one to the Google Maps Team, and one directly to Sundar Pichai. Again, this might be overkill, a single letter to Google LLC or the Google Maps Team is likely enough.\n\n# How You Can Help\n\nThis is a widespread issue, and the more people who raise their voices, the more likely Google is to notice and take action. Here’s how you can contribute:\n\n**1. Share Your Story and Raise Awareness**  \nThere are several ways you can share your experience and help amplify this issue:\n\n* **Submit Your Story on** [**SaveGoogleTimeline.com**](https://SaveGoogleTimeline.com)**:** If you don’t want to share a full story, you can simply add your name to the count of affected users or share links to external websites where you’ve posted your story. This will help me keep track of all public posts and build a comprehensive view of the issue. If you prefer not to use the website, [here is a Google form](https://forms.gle/tPkTcnroyM21L3BG6) that can be used as an alternative.\n* **Share Your Story in the Comments Here:** Feel free to share your story below! Every story helps build awareness and strengthens our collective voice. If you do, please consider submitting a link to your comment on the website to help keep everything organized.\n* **Post Publicly on Social Media:**\n   * Share your experience on platforms like Twitter, Facebook, or Instagram.\n   * Use hashtags like **#SaveGoogleTimeline** (We can workshop something better if you don't like this one) and tag Google or Google Maps in your posts to increase visibility. Here are their official handles:\n      * **Reddit:** u/Google\n      * **Twitter:** [Google](https://twitter.com/Google), [GoogleMaps](https://twitter.com/GoogleMaps)\n      * **Instagram:** [Google](https://instagram.com/Google), [GoogleMaps](https://instagram.com/GoogleMaps)\n      * **Facebook**: [Google](https://www.facebook.com/Google), [GoogleMaps](https://www.facebook.com/GoogleMaps)\n      * **TikTok**: [Google](https://www.tiktok.com/@google)\n\n**2. Post in the Community Support Forums**\n\n* Share your story and ask for help escalating the issue to the Maps team.\n* Tell them your Maps Timeline data is gone, explain how devastated you are, and ask them to investigate.\n* Be respectful and request that your issue be forwarded to the Maps team for review.\n\nSome product experts may say it’s your fault or that nothing can be done. **DON’T LET THEM GET UNDER YOUR SKIN**. Thank them for their response, and calmly reiterate your request to escalate the issue to the Maps team.\n\nIf you’d like, I can draft a template for Community Support posts or letters to Google, but personal stories are likely to be more impactful. Try to include details about your experience and how your data was lost.\n\n**3. Send a Letter to Google**\n\nExplain your situation in a respectful letter and mail it to **Google LLC**. I’m not posting the full address here to comply with Rule #1, but it’s easy to find via a quick Google search. If you’re having trouble locating it, you can also find it listed on [SaveGoogleTimeline.com](http://SaveGoogleTimeline.com)\n\nTell them your Timeline data is gone, explain how important the data is to you, tell them how devastating this is, and ask them to investigate.\n\nThis may seem like a small step, but Google does notice these letters. If enough of us take action, it can demonstrate the scope of the issue and encourage them to respond.\n\n**4. Submit Feedback Through the Maps App**\n\nWhile I don’t know how effective this will be, it was recommended by a Google employee, so it’s still worth doing. The more reports Google receives, the better. Instructions from Google on how to submit feedback can be found [here](https://support.google.com/maps/answer/3094045)\n\n# Why This Matters\n\nGoogle Maps Timeline has been an essential tool for so many of us, whether for tracking trips, revisiting memories, or keeping a personal log of life events. Losing this data is more than inconvenient; it’s heartbreaking.\n\nIf we can show Google how widespread and important this issue is, we have a much better chance of convincing them to take action.\n\n# What’s Next?\n\nOnce I have some stats on how many people have been affected by this, I plan to reach out to tech journalists and writers to ask them to cover the issue. Getting media coverage will help put additional pressure on Google to address this problem, but I need to hear from all of you first! Your stories and support are crucial to making this happen.\n\n# Let’s Do This Together\n\nThis is a massive issue, but it’s important we stay respectful and organized in our efforts. Together, we can make our voices heard and push for a solution.\n\nIf you have any questions, ideas or comments, feel free to share them here.\n\nIf you know someone who has lost their Timeline data or would be interested in supporting this cause, please share this post with them. The more people we can bring together, the stronger our voice will be.", "author": "lost_in_timelines", "created_time": "2024-12-18T19:17:08", "url": "https://reddit.com/r/GoogleMaps/comments/1hh99b7/i_lost_my_entire_timeline_if_you_did_too_lets/", "upvotes": 81, "comments_count": 46, "sentiment": "bullish", "engagement_score": 173.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hhdxia", "title": "I Made My First $300 Online At 18 Years Old!", "content": "I've always dreamt of making money online. I would constantly obsess over new ways to do it, but nothing felt right to me. I was always into tech and coding, but didn't realize I could use it to make money as side hustle online. This was until I discovered agent development. \n\nI recently learned how to code in JavaScript, and was able to use it to help my friend's dad. He was trying to get this rare collector's item from a website called popmart, but wasn't able to due to the high demand. I tried to use my coding skills to build him an agent that could auto-click, and purchase the item automatedly. It didn't work the first few times. I kept amending my code, and I was able to make it work. It ended up being a fast automation agent, that could beat anybody trying to get something at the same time. He paid me $300 for it, and that first online payment definitely felt different. \n\nP.S This probably took about 15 hours to make, which I do think is a lot. But, for my first taste of making money online, I can't complain. :)", "author": "Fun-Case2356", "created_time": "2024-12-18T22:40:29", "url": "https://reddit.com/r/Entrepreneur/comments/1hhdxia/i_made_my_first_300_online_at_18_years_old/", "upvotes": 414, "comments_count": 95, "sentiment": "neutral", "engagement_score": 604.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hhdzz7", "title": "If You Lost Money Today - Here's A Strategy To Prepare For the Next Correction", "content": "Trade like the market is out to get you...because it is! \n\nBear credit call spreads on the SPX/SPY became quickly profitable today. Whether you do this 1 DTE for small daily gains or 45 DTE for the BIG bucks, when the market drops this much this fast those 45 DTE trades get to >50% profit in a hurry. \n\nI do 45 DTE on a weekly basis so every week I'm opening new positions and closing mature positions that have 15-20 DTE left and are at least 50% at max potential profit.\n\nI know this kind of trading doesn't have the kick in the pants adrenaline rush of a cheap call hitting - or not as is the case most of the time -  but making money regularly and rarely losing is quite nice :-)\n\n ", "author": "deleted", "created_time": "2024-12-18T22:43:43", "url": "https://reddit.com/r/options/comments/1hhdzz7/if_you_lost_money_today_heres_a_strategy_to/", "upvotes": 379, "comments_count": 143, "sentiment": "bullish", "engagement_score": 665.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}]