#!/usr/bin/env python3
"""
社交媒体数据缺口填补脚本

专门用于填补AAPL和NVDA股票的社交媒体数据缺口
时间范围：2024-12-01 到 2025-06-01 (6个月)
"""

import os
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Set, Dict, Any
import argparse
from reddit_live_collector import RedditLiveCollector, load_reddit_config

class SocialMediaGapFiller:
    """社交媒体数据缺口填补器"""
    
    def __init__(self, data_dir: str = "social_media_data"):
        self.data_dir = Path(data_dir)
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "gap_filler.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def generate_date_range(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """生成日期范围内的所有日期"""
        dates = []
        current_date = start_date
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        return dates
    
    def get_existing_dates(self, ticker: str) -> Set[str]:
        """获取指定股票已有的数据日期"""
        ticker_dir = self.data_dir / f"{ticker}_social_media"
        existing_dates = set()
        
        if ticker_dir.exists():
            for file_path in ticker_dir.glob("reddit_*.json"):
                # 从文件名提取日期 reddit_2024-12-01.json -> 2024-12-01
                date_str = file_path.stem.replace("reddit_", "")
                existing_dates.add(date_str)
        
        return existing_dates
    
    def find_missing_dates(self, ticker: str, start_date: datetime, end_date: datetime) -> List[datetime]:
        """找出缺失的日期"""
        all_dates = self.generate_date_range(start_date, end_date)
        existing_dates = self.get_existing_dates(ticker)
        
        missing_dates = []
        for date in all_dates:
            date_str = date.strftime('%Y-%m-%d')
            if date_str not in existing_dates:
                missing_dates.append(date)
        
        return missing_dates
    
    def analyze_data_gaps(self, tickers: List[str], start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """分析数据缺口"""
        analysis = {}
        total_days = (end_date - start_date).days + 1
        
        for ticker in tickers:
            existing_dates = self.get_existing_dates(ticker)
            missing_dates = self.find_missing_dates(ticker, start_date, end_date)
            
            analysis[ticker] = {
                'total_days': total_days,
                'existing_days': len(existing_dates),
                'missing_days': len(missing_dates),
                'coverage_percentage': (len(existing_dates) / total_days) * 100,
                'missing_dates': [d.strftime('%Y-%m-%d') for d in missing_dates]
            }
            
            self.logger.info(f"{ticker} 数据分析:")
            self.logger.info(f"  总天数: {total_days}")
            self.logger.info(f"  已有数据: {len(existing_dates)} 天")
            self.logger.info(f"  缺失数据: {len(missing_dates)} 天")
            self.logger.info(f"  覆盖率: {analysis[ticker]['coverage_percentage']:.1f}%")
        
        return analysis
    
    def fill_gaps_for_ticker(self, ticker: str, missing_dates: List[datetime], 
                           collector: RedditLiveCollector) -> Dict[str, int]:
        """为指定股票填补数据缺口"""
        stats = {'attempted': 0, 'successful': 0, 'failed': 0}
        
        self.logger.info(f"开始为 {ticker} 填补 {len(missing_dates)} 个缺失日期的数据")
        
        # 按月分组处理，提高效率
        dates_by_month = {}
        for date in missing_dates:
            month_key = date.strftime('%Y-%m')
            if month_key not in dates_by_month:
                dates_by_month[month_key] = []
            dates_by_month[month_key].append(date)
        
        for month_key, month_dates in dates_by_month.items():
            self.logger.info(f"处理 {ticker} {month_key} 的 {len(month_dates)} 个日期")
            
            # 为整个月收集数据
            month_start = min(month_dates)
            month_end = max(month_dates)
            
            try:
                # 收集该月的数据
                collection_stats = collector.collect_data(
                    start_date=month_start,
                    end_date=month_end,
                    tickers=[ticker],
                    limit_per_subreddit=1500  # 增加限制以获得更好的历史数据覆盖
                )
                
                stats['attempted'] += len(month_dates)
                
                # 检查哪些日期成功获得了数据
                for date in month_dates:
                    date_str = date.strftime('%Y-%m-%d')
                    ticker_dir = self.data_dir / f"{ticker}_social_media"
                    file_path = ticker_dir / f"reddit_{date_str}.json"
                    
                    if file_path.exists():
                        stats['successful'] += 1
                        self.logger.info(f"✓ {ticker} {date_str} 数据收集成功")
                    else:
                        stats['failed'] += 1
                        self.logger.warning(f"✗ {ticker} {date_str} 数据收集失败")
                
                self.logger.info(f"{ticker} {month_key} 处理完成: 相关帖子 {collection_stats.get('relevant_posts', 0)} 个")
                
            except Exception as e:
                self.logger.error(f"处理 {ticker} {month_key} 失败: {e}")
                stats['failed'] += len(month_dates)
        
        return stats
    
    def fill_all_gaps(self, tickers: List[str], start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """填补所有股票的数据缺口"""
        # 首先分析缺口
        analysis = self.analyze_data_gaps(tickers, start_date, end_date)
        
        # 创建Reddit收集器
        config = load_reddit_config()
        collector = RedditLiveCollector(config, str(self.data_dir))
        
        # 为每个股票填补缺口
        fill_results = {}
        for ticker in tickers:
            missing_dates = self.find_missing_dates(ticker, start_date, end_date)
            
            if not missing_dates:
                self.logger.info(f"{ticker} 无需填补数据，覆盖率已达100%")
                fill_results[ticker] = {'attempted': 0, 'successful': 0, 'failed': 0}
                continue
            
            # 填补缺口
            stats = self.fill_gaps_for_ticker(ticker, missing_dates, collector)
            fill_results[ticker] = stats
            
            self.logger.info(f"{ticker} 填补完成:")
            self.logger.info(f"  尝试: {stats['attempted']} 天")
            self.logger.info(f"  成功: {stats['successful']} 天")
            self.logger.info(f"  失败: {stats['failed']} 天")
            self.logger.info(f"  成功率: {(stats['successful']/stats['attempted']*100) if stats['attempted'] > 0 else 0:.1f}%")
        
        # 重新分析填补后的情况
        final_analysis = self.analyze_data_gaps(tickers, start_date, end_date)
        
        return {
            'initial_analysis': analysis,
            'fill_results': fill_results,
            'final_analysis': final_analysis
        }

def main():
    parser = argparse.ArgumentParser(description='社交媒体数据缺口填补工具')
    parser.add_argument('--tickers', nargs='+', default=['AAPL', 'NVDA'],
                       help='目标股票代码 (默认: AAPL NVDA)')
    parser.add_argument('--start-date', type=str, default='2024-12-01',
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2025-06-01',
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--data-dir', default='social_media_data',
                       help='数据目录')
    parser.add_argument('--analyze-only', action='store_true',
                       help='仅分析缺口，不填补数据')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    try:
        # 设置日志级别
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
        
        # 解析日期
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        
        # 创建填补器
        filler = SocialMediaGapFiller(args.data_dir)
        
        if args.analyze_only:
            # 仅分析
            analysis = filler.analyze_data_gaps(args.tickers, start_date, end_date)
            
            print("\n" + "="*60)
            print("数据缺口分析结果")
            print("="*60)
            for ticker, info in analysis.items():
                print(f"\n{ticker}:")
                print(f"  总天数: {info['total_days']}")
                print(f"  已有数据: {info['existing_days']} 天")
                print(f"  缺失数据: {info['missing_days']} 天")
                print(f"  覆盖率: {info['coverage_percentage']:.1f}%")
                
                if info['missing_dates']:
                    print(f"  缺失日期示例: {info['missing_dates'][:10]}...")
        else:
            # 填补缺口
            results = filler.fill_all_gaps(args.tickers, start_date, end_date)
            
            print("\n" + "="*60)
            print("数据缺口填补完成")
            print("="*60)
            
            for ticker in args.tickers:
                initial = results['initial_analysis'][ticker]
                final = results['final_analysis'][ticker]
                fill_stats = results['fill_results'][ticker]
                
                print(f"\n{ticker}:")
                print(f"  填补前覆盖率: {initial['coverage_percentage']:.1f}%")
                print(f"  填补后覆盖率: {final['coverage_percentage']:.1f}%")
                print(f"  尝试填补: {fill_stats['attempted']} 天")
                print(f"  成功填补: {fill_stats['successful']} 天")
                print(f"  填补成功率: {(fill_stats['successful']/fill_stats['attempted']*100) if fill_stats['attempted'] > 0 else 0:.1f}%")
        
    except Exception as e:
        print(f"执行失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
