# Reddit实时数据收集完整指南

## 🎯 **系统概览**

Reddit实时数据收集系统为AI对冲基金提供实时股票讨论数据，支持：
- ✅ **实时数据收集**: 从Reddit API获取最新股票讨论
- ✅ **智能过滤**: 自动识别股票相关内容和情感倾向
- ✅ **增量更新**: 避免重复收集，支持断点续传
- ✅ **定时任务**: 支持每小时/每日/每周自动收集
- ✅ **数据验证**: 自动验证数据质量和完整性
- ✅ **系统集成**: 与AI对冲基金回测系统完全兼容

## 📋 **快速开始**

### 步骤1: 安装依赖包

```bash
pip install praw python-dotenv tqdm schedule pandas
```

### 步骤2: 配置Reddit API

```bash
# 运行配置工具
python setup_reddit_api.py
```

按照提示输入您的Reddit API凭据：
1. 访问 https://www.reddit.com/prefs/apps
2. 创建"script"类型的应用
3. 获取Client ID和Client Secret
4. 输入到配置工具中

### 步骤3: 开始收集数据

```bash
# 基本收集 - 2025年1-6月数据
python reddit_live_collector.py

# 增量更新 - 收集最近的数据
python reddit_live_collector.py --incremental

# 指定股票和时间范围
python reddit_live_collector.py \
  --tickers AAPL MSFT NVDA \
  --start-date 2025-01-01 \
  --end-date 2025-03-31
```

### 步骤4: 验证数据质量

```bash
# 验证收集的数据
python reddit_data_validator.py

# 生成详细报告
python reddit_data_validator.py --export-csv reddit_stats.csv
```

### 步骤5: 集成到AI系统

```bash
# 在回测中使用Reddit数据
python src/backtester.py \
  --tickers AAPL MSFT NVDA \
  --use_local_social_media \
  --start-date 2025-01-01 \
  --end-date 2025-03-31
```

## 🔧 **详细配置**

### Reddit API配置

在`.env`文件中设置：
```env
REDDIT_CLIENT_ID=your_client_id
REDDIT_CLIENT_SECRET=your_client_secret
REDDIT_USER_AGENT=AI-Hedge-Fund-Bot/1.0
REDDIT_USERNAME=your_username  # 可选
REDDIT_PASSWORD=your_password  # 可选
```

### 股票配置

系统默认支持以下股票：
- **AAPL**: Apple Inc.
- **MSFT**: Microsoft Corp.
- **NVDA**: NVIDIA Corp.
- **GOOGL**: Alphabet Inc.
- **AMZN**: Amazon.com Inc.
- **TSLA**: Tesla Inc.
- **META**: Meta Platforms Inc.
- **NFLX**: Netflix Inc.
- **AMD**: Advanced Micro Devices
- **INTC**: Intel Corp.

### 目标子版块

系统监控以下Reddit子版块：
- r/stocks, r/investing, r/wallstreetbets
- r/SecurityAnalysis, r/ValueInvesting
- r/StockMarket, r/options, r/dividends
- r/finance, r/business, r/technology
- 以及各公司专门版块

## 📊 **数据格式**

### 输出文件结构
```
social_media_data/
├── AAPL_social_media/
│   ├── reddit_2025-01-01.json
│   ├── reddit_2025-01-02.json
│   └── ...
├── MSFT_social_media/
│   └── ...
└── processed_posts_cache.json
```

### JSON数据格式
```json
[
  {
    "platform": "reddit",
    "post_id": "reddit_abc123",
    "title": "AAPL earnings discussion",
    "content": "Apple just released...",
    "author": "investor123",
    "created_time": "2025-01-01T09:30:00",
    "url": "https://reddit.com/r/stocks/comments/abc123",
    "upvotes": 250,
    "comments_count": 45,
    "ticker": "AAPL",
    "sentiment": "bullish",
    "engagement_score": 340.0,
    "source_subreddit": "stocks",
    "hashtags": null
  }
]
```

## ⏰ **定时任务**

### 启动定时收集

```bash
# 每日收集 (推荐)
python reddit_scheduler.py --mode daily

# 每小时收集
python reddit_scheduler.py --mode hourly

# 连续收集 (每小时 + 每日 + 每周)
python reddit_scheduler.py --mode continuous

# 后台运行
python reddit_scheduler.py --mode daily --daemon
```

### 定时任务配置

- **每小时**: 收集过去1小时的数据
- **每日**: 每天凌晨2点收集前一天的数据
- **每周**: 每周日凌晨1点进行补充收集
- **日志清理**: 每天凌晨3点清理30天前的日志

## 🔍 **数据验证**

### 验证命令

```bash
# 基本验证
python reddit_data_validator.py

# 详细报告
python reddit_data_validator.py \
  --output-report detailed_report.txt \
  --export-csv stats.csv

# 静默模式
python reddit_data_validator.py --quiet
```

### 验证内容

- ✅ **数据结构**: 检查必需字段完整性
- ✅ **数据类型**: 验证字段数据类型正确性
- ✅ **时间格式**: 检查时间戳格式
- ✅ **情感分析**: 验证情感标签有效性
- ✅ **覆盖统计**: 分析股票和日期覆盖情况

## 📈 **使用场景**

### 1. 日常数据收集
```bash
# 每日运行，收集前一天的数据
python reddit_live_collector.py --incremental
```

### 2. 历史数据补充
```bash
# 收集特定时间段的历史数据
python reddit_live_collector.py \
  --start-date 2025-01-01 \
  --end-date 2025-01-31 \
  --tickers AAPL MSFT
```

### 3. 特定事件分析
```bash
# 收集特定股票在特定时间的讨论
python reddit_live_collector.py \
  --tickers NVDA \
  --start-date 2025-02-15 \
  --end-date 2025-02-20 \
  --subreddits stocks investing wallstreetbets
```

### 4. 实时监控
```bash
# 启动连续监控
python reddit_scheduler.py --mode continuous
```

## 🚨 **故障排除**

### 常见问题

**Q1: API认证失败**
```bash
# 检查API配置
python setup_reddit_api.py

# 验证凭据
python -c "from reddit_live_collector import load_reddit_config; print('配置正确')"
```

**Q2: 收集到的数据很少**
- 检查时间范围是否合理
- 确认股票关键词配置
- 增加目标子版块数量
- 检查API限制情况

**Q3: 数据格式错误**
```bash
# 验证数据质量
python reddit_data_validator.py

# 查看错误详情
python reddit_data_validator.py --output-report error_report.txt
```

**Q4: 定时任务不工作**
- 检查系统时间设置
- 确认脚本权限
- 查看日志文件: `logs/reddit_scheduler_*.log`

### 性能优化

1. **API限制管理**
   - 使用合理的请求间隔
   - 监控API使用配额
   - 实现指数退避重试

2. **存储优化**
   - 定期清理旧数据
   - 使用压缩存储
   - 实现数据归档

3. **内存优化**
   - 批量处理数据
   - 及时释放内存
   - 使用流式处理

## 🔗 **系统集成**

### 与AI对冲基金集成

```bash
# 确保数据格式兼容
python reddit_data_validator.py

# 运行回测
python src/backtester.py \
  --tickers AAPL MSFT NVDA \
  --use_local_social_media \
  --track_accuracy \
  --save_reasoning
```

### 数据流程

```mermaid
graph TD
    A[Reddit API] --> B[数据收集器]
    B --> C[情感分析]
    C --> D[数据存储]
    D --> E[数据验证]
    E --> F[AI分析系统]
    F --> G[投资决策]
```

## 📚 **进阶功能**

### 自定义股票关键词

编辑`reddit_live_collector.py`中的`ticker_keywords`：
```python
self.ticker_keywords = {
    'AAPL': ['Apple', 'AAPL', 'iPhone', 'iPad', 'Tim Cook'],
    'CUSTOM': ['Custom Corp', 'CUSTOM', 'custom keyword'],
    # 添加更多股票...
}
```

### 自定义情感分析

修改`sentiment_keywords`配置：
```python
self.sentiment_keywords = {
    'bullish': ['buy', 'bull', 'moon', 'rocket', 'pump'],
    'bearish': ['sell', 'bear', 'crash', 'dump', 'fall'],
    # 添加更多关键词...
}
```

### 扩展子版块

添加新的目标子版块：
```python
self.target_subreddits = [
    'stocks', 'investing', 'wallstreetbets',
    'your_custom_subreddit',  # 添加自定义版块
    # 更多版块...
]
```

## 🎯 **最佳实践**

1. **定期监控**: 设置每日数据收集和验证
2. **数据备份**: 定期备份重要的历史数据
3. **质量检查**: 使用验证工具确保数据质量
4. **性能监控**: 监控API使用情况和系统性能
5. **错误处理**: 实现完善的错误处理和重试机制

---

**准备开始了吗？** 运行 `python setup_reddit_api.py` 开始配置您的Reddit数据收集系统！
