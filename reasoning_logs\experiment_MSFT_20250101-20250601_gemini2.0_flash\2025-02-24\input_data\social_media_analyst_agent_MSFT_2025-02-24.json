{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-02-24", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Just Accepted a Microsoft Internship Offer and I am having a question about fitness benefit?", "content": "The offer claims that I am eligible for a fully paid membership to a health club. I will have the choice to enroll in one of the Microsoft approved health clubs during the first 30 days of the internship. **What are the Microsoft approved Health Clubs?**\n\nI picked the 10K housing lump sum and will live in a house near downtown Bellevue. I’m a big weight lifter, so I’m looking for a gym that’s good for that. It seems like Life Time Fitness and Tiger Gate Gym are the only options for me. I’m leaning towards Life Time Fitness, but I’m not sure if membership can be covered.\n\nI know most Microsoft employees go to ProClub, which is near the Redmond campus. I also know that FTEs have perks+. What’s the fitness benefit like for interns? Could I get reimbursement for membership at Life Time Fitness?", "created_time": "2025-02-24T00:05:09", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "CorrectMulberry3914", "url": "https://reddit.com/r/microsoft/comments/1iwok7u/just_accepted_a_microsoft_internship_offer_and_i/", "ticker": "MSFT", "date": "2025-02-24"}, {"title": "Microsoft Dropped Some AI Data Center Leases, TD <PERSON><PERSON>", "content": "", "created_time": "2025-02-24T15:59:33", "platform": "reddit", "sentiment": "bearish", "engagement_score": 114.0, "upvotes": 102, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1ix5e54/microsoft_dropped_some_ai_data_center_leases_td/", "ticker": "MSFT", "date": "2025-02-24"}, {"title": "Aight guys, this started as a meme but it doesn't clear up further down the rabbit hole: I have a file to share, do I use teams, one drive, or Sharepoint, and where does exchange come into play here?", "content": "If you share something in one drive, it opens in Sharepoint, which can be done inside teams. Our whole office is confused lol ", "created_time": "2025-02-24T16:12:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 38.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "ParticularLimeade", "url": "https://reddit.com/r/microsoft/comments/1ix5pui/aight_guys_this_started_as_a_meme_but_it_doesnt/", "ticker": "MSFT", "date": "2025-02-24"}, {"title": "Xbox Series X Sales Halt in Brazil Sparks Debate Over Microsoft’s Hardware Future", "content": "", "created_time": "2025-02-23T08:42:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 37.0, "upvotes": 35, "num_comments": 0, "subreddit": "unknown", "author": "Zombotic69", "url": "https://reddit.com/r/microsoft/comments/1iw5rz1/xbox_series_x_sales_halt_in_brazil_sparks_debate/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Should I switch from Apple ecosystem to Windows laptop?", "content": "I’ve been using Apple products for as long as I can remember. Right now, I have a MacBook, iPhone, iPad, and AirPods. However, I’m considering replacing my MacBook with a gaming laptop to be able to play games and have more flexibility in software development. That said, I’m so used to the Apple ecosystem that the thought of switching feels overwhelming.\n\nIdeally, I’d like to have both a MacBook and a gaming laptop, but I don’t have an established setup yet and don’t want to allocate that much budget. I also considered getting a PS5, but since the games I play the most are LoL and HOI4, it wouldn’t really serve my needs.\n\nMy question is, has anyone made a similar switch? If so, what was your experience like? I think the features I would miss the most are AirPods' seamless switching and AirDrop. Other than that, I actually prefer Windows over macOS.\n\nI’ve been looking at the **Asus Zephyrus** since its design quality is close to the MacBook. But I’m unsure whether I’d regret switching because, for a non-gaming daily user, MacBooks are truly amazing devices. Still, as a student living alone abroad, I feel like I’d lose my mind if I couldn’t play games 😁.\n\nOne last thing to note: I’ll be studying **Computer Science**. I believe macOS would be sufficient, but would using Windows give me more flexibility as a developer?", "created_time": "2025-02-23T16:51:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 96.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Pluxy01", "url": "https://reddit.com/r/microsoft/comments/1iwehsu/should_i_switch_from_apple_ecosystem_to_windows/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Separate Calendar App", "content": "Hi!  I’m just curious, am I the only one who would like to see a calendar app that is actually separate from Outlook?  The Outlook calendar is what I use for my entire life, just to make it easy, and I guess I would love to skip the seeing my email first or going through Teams. Am I the only one?  Is there a functionality that I’m missing?  I’m fairly adept at Microsoft but certainly not an expert. ", "created_time": "2025-02-23T17:04:33", "platform": "reddit", "sentiment": "neutral", "engagement_score": 17.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "v<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1iwetdm/separate_calendar_app/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "With the upcoming Microsoft 365 price increase from roughly 70 dollars to 100 dollars, you're still able to keep the old subscription. Here's how:", "content": "Go to: [account.microsoft.com/services/microsoft365](http://account.microsoft.com/services/microsoft365)\n\nUnder Manage Subscription, select Cancel Subscription\n\nYou'll then have the option to switch back to your original plan, aka \"Microsoft 365 Personal Classic\"— without all the AI stuff Microsoft is pushing down your throat.\n\nPersonally I really don't need these kinda features, figured there might be more people that don't know about this.\n\nAfter switching back it'll charge you on your usual billing date.\n\nHope this helps you out!\n\n  \n\n\nedit: removed 'www.' from the link, which caused it not to work", "created_time": "2025-02-23T17:39:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 126.0, "upvotes": 86, "num_comments": 0, "subreddit": "unknown", "author": "RedAceBeetle", "url": "https://reddit.com/r/microsoft/comments/1iwfnbe/with_the_upcoming_microsoft_365_price_increase/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Google Cloud's quantum-safe encryption contrasts with Microsoft's advances", "content": "\n**Google Cloud is making strides in encryption safety with its quantum-safe digital signatures.** This recent update to the Cloud Key Management Service (Cloud KMS) addresses the imminent threats posed by quantum computing. As Microsoft also progresses in this arena with its Majorana 1 chip, the competition heats up in securing digital infrastructures.\n\nOrganizations leveraging cloud solutions must recognize the rapid advancements in cybersecurity technologies that companies like Google and Microsoft are introducing. Both tech giants are prioritizing encryption methods capable of resisting quantum attacks, highlighting an essential transition for businesses handling sensitive data. As users begin testing these new features, the focus remains on refining methods to combat emerging cyber threats effectively.\n\n- Google introduces quantum-safe digital signatures in Cloud KMS.\n\n- Microsoft advancing its own quantum computing solutions.\n\n- Both companies emphasize encryption's importance in cybersecurity.\n\n- Users encouraged to integrate new features seamlessly.\n\n[(View Details on PwnHub)](https://www.reddit.com/r/pwnhub/comments/1iwhlut/google_cloud_enhances_security_with_quantumsafe/)\n        ", "created_time": "2025-02-23T19:04:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON>-<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1iwho4a/google_clouds_quantumsafe_encryption_contrasts/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Can we bring back Microsoft assistant characters instead of the search bar?", "content": "", "created_time": "2025-02-23T19:04:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "laced1", "url": "https://reddit.com/r/microsoft/comments/1iwhoar/can_we_bring_back_microsoft_assistant_characters/", "ticker": "MSFT", "date": "2025-02-23"}, {"title": "Copilot Pages, OneNote, Loop…where should I store my notes and tasks?", "content": "Microsoft always seems to release products which competes with already existing services. They are not overlapping 100% but they do it enough to confuse users.\n\nJust to give you some examples, we have Planner and ToDo, Loop and OneNote… and now Copilot Pages.\nThere might be others also but let’s focus on these.\n\nI started using Copilot more frequently and really like how integrated it is with Teams, Outlook and Sharepoint. Teams transcriptions with AI generated meeting notes is awesome.\n\nHowever, I am trying to find a good place to store all notes and tasks but I am so confused.\n\nWhat is the use cases for Copilot Pages vs Loop?\nIs Loop the future for storing notes or is OneNote still the best?\nAll the follow up tasks created, should I store them in ToDo or Planner?\n\nMy todos created in ToDo app seems to show up in Planner anyway, should I just use that one instead?\nAnd <PERSON> is the so called ”Notion killer” (or was supposed to be), is that the future for notes or should I use OneNote?\n\nI feel lost, how do you handle your notes and tasks in a M365 environment? \n", "created_time": "2025-02-22T20:05:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 108.0, "upvotes": 70, "num_comments": 0, "subreddit": "unknown", "author": "EN-D3R", "url": "https://reddit.com/r/microsoft/comments/1ivrq0f/copilot_pages_onenote_loopwhere_should_i_store_my/", "ticker": "MSFT", "date": "2025-02-22"}, {"title": "My email address is dead to Microsoft due to failed Authenticator migration", "content": "I had been a reasonably happy user of Microsoft services for many years. I had Microsoft 365 and Visual Studio subscriptions. Some time ago something convinced me to add two-factor authentication to my login and that meant installing Microsoft Authenticator on my Google Pixel 6 phone. A few months ago, I replaced my phone with a brand new Google Pixel 9. As far as I know, I installed Authenticator on my new phone and everything was fine. Then I wiped my old phone and sent it in for recycling. \n\nA little bit later I found that Authenticator no longer did any authenticating. When I tried logging in via my PC, it said to watch for Authenticator to display a code but the code never came. I tried resetting my password and probably a few other things but I always ended up in a doom loop where it asked for a code that I could not supply.\n\nAfter searching online for help, I finally ended up in Microsoft support chat. I spent a few hours chatting with a representative but eventually it became clear that somehow my authentication credentials had not transferred properly from my old phone to my new phone. I was so naive to think that this just meant that I had to authenticate myself the hard way, by giving answers to secret questions, or sending in my picture id, or perhaps even showing up in person at some facility. Imagine my dismay when I was told that there is no backup authentication process. It is the end of the line for my email address at Microsoft. Perhaps if I faked my own death and got a court order to release the account to my wife, then they might help me. Other than that, I am screwed.\n\nI lost access to all my old calendar, contact, and email data. I had to get Microsoft to cancel all autopayments for my subscriptions. What a total lack of support! It seems to me there ought to be a law requiring a backup authentication method for all login procedures.", "created_time": "2025-02-22T20:58:35", "platform": "reddit", "sentiment": "bullish", "engagement_score": 56.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "PaulTopping", "url": "https://reddit.com/r/microsoft/comments/1ivswn9/my_email_address_is_dead_to_microsoft_due_to/", "ticker": "MSFT", "date": "2025-02-22"}, {"title": "Microsoft SE initial Phone call", "content": "Microsoft SE initial Phone call with recruiter what kind of questions should I expect?", "created_time": "2025-02-22T21:51:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "InevitableEye3955", "url": "https://reddit.com/r/microsoft/comments/1ivu3dj/microsoft_se_initial_phone_call/", "ticker": "MSFT", "date": "2025-02-22"}, {"title": "I got an internship with Microsoft, what should I expect?", "content": "I recently accepted an offer from Microsoft for a Product Design internship and have no clue what to expect. I will be at Microsoft HQ in Redmond Washington, and the offer letter details most of the logistical information. I am curious though, anything I should consider or any tips for getting the most out of the internship? I have worked at a mid-scale company before but this is the first time moving into  \"big tech.\" Any thoughts or feedback would be appreciated!", "created_time": "2025-02-21T03:35:01", "platform": "reddit", "sentiment": "neutral", "engagement_score": 64.0, "upvotes": 14, "num_comments": 0, "subreddit": "unknown", "author": "Desperate_Rest2814", "url": "https://reddit.com/r/microsoft/comments/1iuhgw9/i_got_an_internship_with_microsoft_what_should_i/", "ticker": "MSFT", "date": "2025-02-21"}, {"title": "Microsoft CEO says there is an 'overbuild' of AI systems, dismisses AGI milestones as show of progress | Nadella suggested AI progress should be measured by economic growth.", "content": "", "created_time": "2025-02-21T14:47:24", "platform": "reddit", "sentiment": "bullish", "engagement_score": 347.0, "upvotes": 261, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1iuskrj/microsoft_ceo_says_there_is_an_overbuild_of_ai/", "ticker": "MSFT", "date": "2025-02-21"}, {"title": "Visit MS Campus redmond as External/Guest. Tips and tricks?", "content": "In a few weeks i will be heading to the MS Campus in redmond for a week. I’ve never been here before. Have you got some advise on things we can do here during free time? Things i should visit? Things i should know?\n\nQuestions i have right now:\n\n•\t⁠Is it allowed to walk around the campus in the evening? What buildings can you go in?\n\nIs it allowed to just walk into random buildings?\n\n•\t⁠Is it allowed to enter a gym or join people who are doing sports on the Commons soccer field? Is it strange to ask them to join?\n\n•\t⁠Is Microsoft Commons open during the evening?\n\n•\t⁠Are the campus transport busses active during the evening? Can you just wave to the driver and get in? Or do i have to wait for a bus on a specific bus stop?\n\nAny other things i should know? :)\n\nThanks!", "created_time": "2025-02-21T22:42:35", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "OneSwordfish6949", "url": "https://reddit.com/r/microsoft/comments/1iv3yx7/visit_ms_campus_redmond_as_externalguest_tips_and/", "ticker": "MSFT", "date": "2025-02-21"}, {"title": "red team at microsoft", "content": "Hi there,\n\n  \nI wanted to see if anyone knows the team that I should be talking to for networking purposes - right now I am an SRE and I was wanting to learn about the cyber offensive teams that Microsoft has, especially ones that are CTJ based. Any information on this would be greatly appreciated. Thanks", "created_time": "2025-02-20T01:14:54", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "ansolo00", "url": "https://reddit.com/r/microsoft/comments/1itmb2w/red_team_at_microsoft/", "ticker": "MSFT", "date": "2025-02-20"}, {"title": "Is xbox down?", "content": "Friend on phone can't do anything I can play downloaded games but can't click on anybody names on friends list", "created_time": "2025-02-20T02:52:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 13.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "thickolas_rage", "url": "https://reddit.com/r/microsoft/comments/1itoamm/is_xbox_down/", "ticker": "MSFT", "date": "2025-02-20"}, {"title": "<PERSON> says Microsoft might not have become a success if he hadn't dropped out of Harvard or snuck out to write codes until 2 a.m. at 13", "content": "", "created_time": "2025-02-20T20:45:46", "platform": "reddit", "sentiment": "bearish", "engagement_score": 253.0, "upvotes": 177, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1iu8vzi/bill_gates_says_microsoft_might_not_have_become_a/", "ticker": "MSFT", "date": "2025-02-20"}, {"title": "A path to a million qubits: Microsoft’s quantum computer", "content": "", "created_time": "2025-02-20T23:49:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 56.0, "upvotes": 30, "num_comments": 0, "subreddit": "unknown", "author": "Mynpplsmychoice", "url": "https://reddit.com/r/microsoft/comments/1iud5lv/a_path_to_a_million_qubits_microsofts_quantum/", "ticker": "MSFT", "date": "2025-02-20"}, {"title": "Does anyone else fondly look back at the Windows Insider Program from 2014-15?", "content": "2014-15 was when the Windows Insider Program got beta builds of Windows 10. I enrolled on Day 1, I would always download the updates ASAP on the family computer no matter how much it pissed off my sister. I got excited for new builds on Wednesdays and often looked at  the Windows blog as well as winbeta.org before its rebrands. I think it played a big role in my interest in software and tech.", "created_time": "2025-02-19T07:24:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 19.0, "upvotes": 11, "num_comments": 0, "subreddit": "unknown", "author": "TheTwelveYearOld", "url": "https://reddit.com/r/microsoft/comments/1iszuff/does_anyone_else_fondly_look_back_at_the_windows/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft Solutions Partner", "content": "Our company specializes in IT solutions, and providing Infrastructure as a Service (IaaS) and Software as a Service (SaaS). Unlike traditional service providers, we don’t have external customers in a direct sales model; instead, we deliver services on behalf of our customers, functioning as a third-party service provider. We managed our current customers' azure services under our own tenant.\n\nCurrently, we are a legacy Gold member, but Microsoft discontinued renewals as of January 22. We still have access until November 6, 2025, and we are planning to enroll in the Solutions Partner program for either Modern Work (Enterprise) or Infrastructure.\n\nAccording to Microsoft Partner Center, achieving a Solutions Partner designation is measured by performance, skilling, and customer success.\n\nOur situation is that even with significant effort, we could acquire at most five customers. Beyond that, we would not be able to expand our customer base for the foreseeable future.\n\nSince we cannot continuously acquire new customers, meeting the customer success requirements seems impossible. What we would like to understand is: If we don’t bring in new customers, can we still earn customer success points through deployments and usage growth of existing customers for adding new VMs and Azure subscriptions? Would these count toward the performance metrics? Please advise how do we manage to become Microsoft Partner.", "created_time": "2025-02-19T10:24:20", "platform": "reddit", "sentiment": "bullish", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "sleepeezz", "url": "https://reddit.com/r/microsoft/comments/1it2cnb/microsoft_solutions_partner/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Azure AI services for contract analysis", "content": "Hi everyone🖐\n\nI would like to know if an AI agent or automation flow can be created in Azure using Azure AI services, OpenAI services, or any other Azure services to help me with the following:\n\nI have a database—a folder in SharePoint—where I store general terms and conditions of sales, template sales agreements, main contractual provisions, and similar documents.\n\nWhenever I receive agreements or contracts from potential clients, I want them to be automatically compared against the database. The AI should answer my predefined questions, cite the relevant page and paragraph, and generate a report.\n\nHere are some of the questions:\n\n1. Do the provisions on warranty and liability in \\[Agreement A\\] and \\[Agreement B\\] Standard Terms and Conditions deviate from the warranty and liability provisions we typically include in our agreements? What kind of risks result from these deviations?\n2. Do the provisions in the provided agreements deviate from those we usually include in our agreements in any other way that poses a substantial risk to \\[Company X\\]?\n3. Are there any contractual penalties included in \\[Agreement A\\] and \\[Agreement B\\] Standard Terms and Conditions provided by \\[Supplier Y\\]?\n\nI want all of this to be done autonomously using an AI agent.\n\nDoes anyone have any ideas on how this can be achieved in Azure? Also can my logic be improved?", "created_time": "2025-02-19T10:48:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1it2pb6/azure_ai_services_for_contract_analysis/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft is spending $700 million to ramp up security and computing power in Poland | AI infrastructure and cybersecurity are getting a boost from Microsoft", "content": "", "created_time": "2025-02-19T11:42:13", "platform": "reddit", "sentiment": "neutral", "engagement_score": 80.0, "upvotes": 76, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1it3jdt/microsoft_is_spending_700_million_to_ramp_up/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft demonstrates working qubits based on exotic physics | Stronger evidence for a hypothetical quasiparticle, plus actual processing hardware.", "content": "", "created_time": "2025-02-19T16:42:20", "platform": "reddit", "sentiment": "bullish", "engagement_score": 192.0, "upvotes": 174, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1it9xrk/microsoft_demonstrates_working_qubits_based_on/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "10-core vs 12-core", "content": "i’m hoping to get opinions ahead of purchasing a new laptop. \ni love the microsoft surface laptop and have used it both personally and professionally in the past. i’m getting a new one for myself at my office which is the Surface Laptop, Copilot+ PC. there is the option for the Snapdragon X Plus (10 Core) or the Snapdragon X Elite (12 core) processor - with about a $250 difference between the two. \ni handle firm business operations and am the executive assistant to our CEO. i love to multitask and am often using multiple programs and have numerous chrome tabs open, but I am not doing things like video editing. i use outlook, dropbox, adobe pro, chrome (for QBO, bank management, general research) most extensively - i also use word, excel, one note and a scanning program on occasion. \n\nshould i be ok if i get the 10 core? or is the additional $250 really worth it for the 12 core?\n\nthanks so much! ", "created_time": "2025-02-19T21:07:02", "platform": "reddit", "sentiment": "bearish", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "heatles22", "url": "https://reddit.com/r/microsoft/comments/1itgmm2/10core_vs_12core/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Microsoft releases Muse, an AI model for 3D game environment creation", "content": "", "created_time": "2025-02-19T21:08:56", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 20, "num_comments": 0, "subreddit": "unknown", "author": "dreadpiratewombat", "url": "https://reddit.com/r/microsoft/comments/1itgoc4/microsoft_releases_muse_an_ai_model_for_3d_game/", "ticker": "MSFT", "date": "2025-02-19"}, {"title": "Why is Microsoft stuff considered bad and bloated, but Google, Apple, and many other stuff is not?", "content": "Why is Microsoft stuff considered bad and bloated, but Google, Apple, and many other stuff is not?\n\nHonestly, I've had this question for a very long while now. Everywhere I go, I always see excessive hate on Microsoft products and software, with people trying to debloat Windows, and while as a tech enthusiast I see the appeal of it, it simply doesn't do much to affect system performance, IMHO. Things like Edge, OneDrive, and other apps from Windows and the Microsoft ecosystem are considered bad, but when it comes to things from Google and Apple, they seem to be praised more, and people don't hate them as much. People actually want them on their system and don't try to remove them. \nWhen it comes to third-party software from other companies, it gets worse. They will praise them like there is no other. I'm sure they're not much better than the other offerings; they serve the same purpose anyways. \nI get everything with it being expensive or having subscriptions or not being customizable, but I'm curious on everybody's reasoning for hating Microsoft products, if you do at all, as I never understood why people do. \nAs someone that has jumped between ecosystems, and eventually settled with a hybrid Google ecosystem, I want to understand the hate for other software. \nI personally don't hate Microsoft apps, they just don't work well for me.\n\ntl;dr: Why the hate for Microsoft products when similar stuff from Google/Apple/others gets praise?\n", "created_time": "2025-02-18T06:17:41", "platform": "reddit", "sentiment": "bullish", "engagement_score": 536.0, "upvotes": 204, "num_comments": 0, "subreddit": "unknown", "author": "former-ad-elect723", "url": "https://reddit.com/r/microsoft/comments/1is6620/why_is_microsoft_stuff_considered_bad_and_bloated/", "ticker": "MSFT", "date": "2025-02-18"}, {"title": "How can I appeal a false-positive Email quarantine without being a MS365 customer?", "content": "My small business' emails (we're on Google Workspace) are being blocked/quarantined by MS365 \\*for Malware\\*. This means that we are not able to email any of our clients who use MS365.\n\nThis is not cold outreach / mass email marketing, we don't and never have done that. Just regular business emails. We don't have malware, as far as any scans have shown.\n\nI have updated our DKIM which Google suggested could have been causing the issue, though I don't actually have any insight into why. The issue started several weeks ago, while I was overseas, so it's not IP related.\n\nOne client asked her tech team to look into it and they found all our emails quarantined and were able to release them. But I don't think this will remedy the issue going forward (new emails will still get blocked) and won't help with any potential new customers, who we won't know whether or not they're receiving our mails as we won't know if they're on MS365.\n\n\\*\\*EDITED TO ADD\\*\\* Another client's IT team has confirmed my mail was blocked for Malware. I've scanned both my laptop and my website and haven't found any sign of malware anywhere.\n\nIs there \\*any\\* way to contact Microsoft / MS365 as a non-user? Or any other way to lodge an appeal against this false positive for our domain?\n\nI've found various help centre listings but they all assume I'm a MS customer. I would be super grateful for any help or advice you could offer.", "created_time": "2025-02-18T15:01:26", "platform": "reddit", "sentiment": "bullish", "engagement_score": 20.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Mysterious_Beach5860", "url": "https://reddit.com/r/microsoft/comments/1isemb8/how_can_i_appeal_a_falsepositive_email_quarantine/", "ticker": "MSFT", "date": "2025-02-18"}, {"title": "Copilot Pro Doesn't have Access to Microsoft Editor, that's Copilot 365", "content": "I pay $20/month for Copilot Pro, which has GPT and image generation. However, that doesn't give me access to the Microsoft Editor to rewrite text. That's the wrong version of Copilot.\n\nI need to subscribe to Microsoft 365 for $30/month for 365 Copilot.\n\nBut I can't cancel my previous subscription because 365 Copilot doesn't have GPT-4 or image generation capabilities.\n\nIf I pay for fancy Copilot, let me do fancy Copilot things!\n\nHow can Microsoft expect the user to understand or care about the differences between these two tools?", "created_time": "2025-02-18T21:55:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "livejamie", "url": "https://reddit.com/r/microsoft/comments/1isos95/copilot_pro_doesnt_have_access_to_microsoft/", "ticker": "MSFT", "date": "2025-02-18"}, {"title": "Seeking Advice on Housing, Finances, and Next Steps—Buying Condo Cash vs. Renting & Investing", "content": "**Body:**\n\nHey everyone,\n\nI’d love some outside perspectives on my financial and life situation. I’m 30M, a full-time law student in my final year, and I have no family or friends in my life—just my girlfriend. I’ll be graduating in a year and plan to start my own business. Admittedly, I’m not very financially literate, and I know there are probably better ways to invest my capital, but the only thing I’ve really done so far is put it into a GIC.\n\nHere’s where I stand financially:\n\n* **Liquid capital:** \\~$480K in a GIC earning 3.05%\n\n* **Real estate:** I own a commercial office unit outright, which used to generate $1,000/month net. The lease recently ended, and I’m trying to re-rent it.\n\n* **Car:** Owned outright\n\n* **Current housing:** Just returned to Toronto from a 3-month trip to Southeast Asia. Sold all my belongings before leaving. I’ve been struggling to secure a rental despite an 840+ credit score, strong bank statements, and references. I was looking at a 1-year lease around $2,200/month but am now considering a cheaper $1,400/month shared unit near a university with parking and utilities included.\n\nFor the past two years, I’ve been wanting to buy a property. Initially, I was looking at condos (1-bed, parking, North York area) but found dealing with real estate agents and mortgage brokers frustrating. I also considered waiting a year and saving while the market cools, hoping to buy a townhouse later with a mortgage, possibly renting out part of it to offset costs.\n\nHowever, now that I’m back, I’m feeling tempted to just **buy a condo outright in cash** (around $450K). I know it’s not the best idea to put all my eggs in one basket, but I’m **so tired** of renting, dealing with roommates, and going through the mortgage/agent/bank process. I’d rather just cut through all that and secure a place to live with peace of mind.\n\nI know a condo isn’t the best investment, and at one point, I viewed real-estate as a wealth-building tool. But at this point, I see my residence as **just that—a place to live, not a money-making asset**. I’m confident I’ll make money in the future through my business, so I’m not relying on my home to generate returns.\n\nStill, I can’t shake the feeling that I might be making an emotional decision rather than a rational one. I’d love to hear some objective perspectives—what would you do in my position? Are there smarter ways I could invest my capital?", "created_time": "2025-02-17T18:12:26", "platform": "reddit", "sentiment": "bullish", "engagement_score": 19.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "Sufficient_Insect812", "url": "https://reddit.com/r/financialindependence/comments/1irqgb3/seeking_advice_on_housing_finances_and_next/", "ticker": "MSFT", "date": "2025-02-17"}, {"title": "The same job  has been posted again with a different job number.", "content": "I applied for a role at Microsoft and successfully completed the technical interviews. The interviewer acknowledged that I did well - i answered all the questions - . I then proceeded to the final behavioral round, which I believe didn’t go as well as I had hoped.\n\nAfter multiple attempts to contact the recruiter, I was eventually informed that I was rejected due to technical skills. However, I was added to the active talent pool.\n\nThe thing is, I don’t believe technical skills were the issue, as I wasn’t asked any technical questions in the final round.\n\nNow, I’ve noticed that the same job has been reposted with a different job number, but the location, title, and requirements remain the same.\n\nIs there any chance I could secure this offer this time? Has anyone been in a similar situation? Any advice would be greatly appreciated!  \nNote: in the Action Center, the status for my previous application is still marked as \"Scheduled.\"", "created_time": "2025-02-17T23:52:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 32.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Walterwhite_234", "url": "https://reddit.com/r/microsoft/comments/1iryssa/the_same_job_has_been_posted_again_with_a/", "ticker": "MSFT", "date": "2025-02-17"}], "metadata": {"timestamp": "2025-07-06T21:10:25.916635", "end_date": "2025-02-24", "days_back": 7, "successful_dates": ["2025-02-24", "2025-02-23", "2025-02-22", "2025-02-21", "2025-02-20", "2025-02-19", "2025-02-18", "2025-02-17"], "failed_dates": [], "source": "local"}}}}