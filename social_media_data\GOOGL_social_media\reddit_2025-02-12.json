[{"platform": "reddit", "post_id": "reddit_1infsav", "title": "Apple, Microsoft Joining Google Using Gulf of America in Maps Programs", "content": "", "author": "moeka_8962", "created_time": "2025-02-12T01:50:13", "url": "https://reddit.com/r/technews/comments/1infsav/apple_microsoft_joining_google_using_gulf_of/", "upvotes": 518, "comments_count": 206, "sentiment": "neutral", "engagement_score": 930.0, "source_subreddit": "technews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1inibal", "title": "I love Google Pixel. I just wish they'd return back to the Snapdragon chipsets.", "content": "I've been all Pixel since the 6 Pro... (Other than a few side phones I had during phone repairs)... But I feel like the Tensor is so underpowered. I just want to game a little on my phone y'know? Genshin Impact and Warzone Mobile run horribly above Medium Graphics... I'm hoping the next pixel uses the Snapdragon 8+ Gen 4... I know this is going to get Downvoted even though I'm defending the pixel. I just want it to be a little more powerful. ", "author": "RealityOwn9267", "created_time": "2025-02-12T04:02:04", "url": "https://reddit.com/r/GooglePixel/comments/1inibal/i_love_google_pixel_i_just_wish_theyd_return_back/", "upvotes": 406, "comments_count": 339, "sentiment": "neutral", "engagement_score": 1084.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1inpox7", "title": "RTX 50 Series 12VHPWR Megathread", "content": "^(Version 2.2)\n\n# List of Confirmed Cases\n\nThe cases in this section are verified 50 series melting cases.\n\n|Case|Date|Link|GPU|PSU|Impacted Connectors|Notes|\n|:-|:-|:-|:-|:-|:-|:-|\n|C1|Feb 9|[Reddit Link](https://www.reddit.com/r/nvidia/comments/1ilhfk0/rtx_5090fe_molten_12vhpwr/)|NVIDIA RTX 5090 Founders Edition|Asus Loki SFX-L 1000W ATX 3.0|PSU Cable + Terminal. GPU Cable + Terminal|User Uses [12VHPWR ModDIY Cable](https://www.moddiy.com/products/ATX-3.0-PCIe-5.0-600W-12VHPWR-16-Pin-to-16-Pin-PCIE-Gen-5-Power-Cable.html)|\n|C2|Feb 9|[Youtube Link - Spanish](https://www.youtube.com/watch?v=Nw7HaVRUN9k) / [El Chapuzas Informatico - Spanish](https://elchapuzasinformatico.com/2025/02/se-quema-cable-de-alimentacion-geforce-rtx-5090/)|NVIDIA RTX 5090 Founders Edition|FSP Hydro GT PRO ATX 3.0|PSU Cable + Terminal Only.|Per Google Translate: \"Toro Tocho confirms that this wiring burned due to a bad connection because of the wear of the 12VHPWR connector. Toro Tocho emphasizes that *the power supply was very used\"*|\n|C3|Feb 11|[Reddit Link](https://www.reddit.com/r/ASUS/comments/1inhbo7/does_rog_lokis_molted_rtx_5000_gpu_12vhpwr_cable/)|Asus RTX 5080 Astral|Asus Loki SFX-L 1000W ATX 3.0|PSU Cable Only|Per User: \"GPU side remained unaffected\"|\n|C4|Mar 20|[Reddit Link](https://www.reddit.com/r/nvidia/comments/1jfis9z/molten_12vhpwr_cables_5090/)|MSI RTX 5090 Gaming X Trio|Corsair HX1000i|GPU Side Cable Only.|Per User: GPU Connector, PSU side cable, and PSU connectors are unaffected.|\n|C5|Apr 4|[Reddit Link](https://www.reddit.com/r/nvidia/comments/1jr5yfm/msi_5090_gaming_trio_oc_melted_cable_repost_with/)|MSI RTX 5090 Gaming X Trio|Corsair SF1000L ATX 3.0|PSU Cable + Terminal. GPU Cable + Terminal||\n|C6|April 29|[Quazar Zone Link](https://quasarzone.com/bbs/qf_vga/views/6673542)|MSI RTX 5090 Suprim|Superflower 1300W (Model Unknown)|Only GPU side adapter|User is using supplied adapter from MSI.|\n\n# List of Unconfirmed Cases\n\nThe cases in this section are verified but might not be related related to the issue above.\n\n|Case|Date|Link|GPU|PSU|Impacted Connectors|Notes|\n|:-|:-|:-|:-|:-|:-|:-|\n|U1|Mar 19|[Reddit Link](https://www.reddit.com/r/gigabyte/comments/1je983m/gaming_oc_rtx_5080/)|Gigabyte RTX 5080 Gaming OC|Corsair AX850 Gold|Only Adapter melted. 12v-2x6 connector in the GPU is not affected.|**UNCONFIRMED.**  This AX850 PSU is old. First released back in 2010 and the melting occurred on the adapter connecting to the PSU. No 12V-2x6 connector is impacted.|\n\n# List of Suspicious Cases\n\nAll the cases in this section are suspicious and should be taken with grains of salt. This could be anyone trolling, posting melting case from prior generation, or need more basic information. So... grains of salt until it's moved to other section above.\n\n|Case|Date|Link|GPU|PSU|Impacted Connectors|Notes|\n|:-|:-|:-|:-|:-|:-|:-|\n|S1|Feb 11|[Reddit Link](https://www.reddit.com/r/nvidia/comments/1imvxk3/comment/mc6qgib/)|Unknown|Unknown|At least 1 side|**SUSPICIOUS.** Probably fake. User posted an image to the comment section with melted connector and commented \"That was not the original cable included with the card, I used cable included with a 1200w power supply.\" They were also talking about his \"melting Cablemod adapter\" last year.|\n|S2|Feb 16|[Reddit Link](https://www.reddit.com/r/pcmasterrace/comments/1iqtw4r/l_used_all_my_luck_to_get_the_5090fe_now_it_wants/?share_id=jEji0Ve9Sp69f6F7Utqcy&utm_content=1&utm_medium=ios_app&utm_name=ioscss&utm_source=share&utm_term=1)|NVIDIA RTX 5090 Founders Edition|Corsair RM1000 ATX 2.0|GPU Side Cable Only. GPU Side Terminal Unaffected.|**SUSPICIOUS.** See [Lian Li Response Here](https://www.reddit.com/r/pcmasterrace/comments/1iqtw4r/comment/md7ykqe/). \"Based on the images, it appears you're using our STRIMER PLUS V2 3×8-PIN to 12+4-PIN model, **which is not physically compatible with the RTX 5090 Founders Edition**. The 12VHPWR sense pins do not carry load, meaning even **when 12VHPWR cables melt, the sense pin should remain unaffected**. **However, in your images, the sense pin appears to have melted**. Typically, when **12VHPWR cables melt, the copper terminals turn black from excessive heat, but in this case, the terminals appear unaffected**\"|\n|S3|Feb 12|[Reddit Link](https://www.reddit.com/r/nvidia/comments/1io6e39/update_heres_another_one/)|NVIDIA RTX 5090 Founders Edition|EVGA SuperNOVA 1200 P3|PSU Cable Side Only|**SUSPICIOUS**. 100% User Error. User mixing Corsair cable and EVGA cable. [Potentially sending 12V to GND](https://www.reddit.com/r/nvidia/comments/1io6e39/comment/mcgucl7/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button)|\n\n# Verified Research & Comments\n\n# Der8auer\n\n**Video 1** \\- [12VHPWR on RTX 5090 is Extremely Concerning](https://www.youtube.com/watch?v=Ndmoi1s0ZaY)\n\n(Temp in **Celcius** at full load)\n\nGPU Side - approx. 82°C = 179°F\n\nhttps://preview.redd.it/mf9pje3njpie1.jpg?width=2366&format=pjpg&auto=webp&s=9686a13d9f4faf7e28ed100f5aab683ef932ce44\n\nPSU Side - approx. 154°C = 309 °F\n\nhttps://preview.redd.it/21sa0ixnjpie1.jpg?width=2356&format=pjpg&auto=webp&s=2f86141044afe1609450781f7a0321b690ecbbb6\n\nCurrent = 22A\n\nhttps://preview.redd.it/2ivgxppojpie1.jpg?width=3120&format=pjpg&auto=webp&s=5986bab92091215a6a6b1e8148f03e84236ba84e\n\n**Video 2** \\- [The real \"User Error\" is with Nvidia](https://www.youtube.com/watch?v=oB75fEt7tH0)\n\n* Confirmed his prior finding about high current flowing through some wires by artificially cutting some of the wires in the connector (similar to Gamers Nexus test back in 2022).\n* Replaced the cables to a brand new Corsair cables and confirmed all the currents flowing are now normal and within spec.\n\n# Buildzoid\n\n* Videos:\n   * [Nvidia RTX 5090s melting power connectors AGAIN!](https://www.youtube.com/watch?v=41eZsOYUVx0&pp=ygUJYnVpbGR6b2lk)\n   * [How Nvidia made the 12VHPWR connector even worse.](https://www.youtube.com/watch?v=kb5YzMoVQyw&pp=ygUJYnVpbGR6b2lk)\n\nhttps://preview.redd.it/4m8xiyrpjpie1.jpg?width=2644&format=pjpg&auto=webp&s=6ce6dbc01f77649d34e95a089bd8d9e322f600e6\n\n# ModDIY\n\n[Can I use the existing 12VHPWR cable with the new RTX50 GPU?](https://help.moddiy.com/en/article/can-i-use-the-existing-12vhpwr-cable-with-the-new-rtx50-gpu-1vll88l/)\n\n>**Upgrade to the Latest 12V-2X6 Cables for RTX50 Series GPUs**\n\n>We are pleased to announce the release of our new 12V-2X6 cables, designed specifically for the recently launched RTX50 series GPUs. As of 2025, the industry standard has transitioned to 12V-2X6, replacing the previous 12VHPWR standard. Our new cables incorporate significant advancements, including enhanced terminal and connector housing materials, along with thicker wires, to provide an additional safety buffer for the latest GPUs.\n\n>At MODDIY, all 12VHPWR / 12V-2X6 cables purchased from 2025 onward are manufactured in accordance with the new 12V-2X6 specifications and standards, ensuring compatibility and optimal performance with the RTX50 series GPUs.\n\n>Prior to 2024, the RTX50 series GPUs had not yet been introduced, and the prevailing standard was 12VHPWR. All cables produced before this period were designed and tested for use with the RTX40 series GPUs.\n\n>We recommend that all users upgrade to the new 12V-2X6 cables to take full advantage of the enhanced safety and performance features offered by this new standard.\n\n>You can buy the new 12V-2X6 cable at [ATX 3.1 PCIe 5.1 H++ 12V-2X6 675W 12VHPWR 16 Pin Power Cable](https://www.moddiy.com/products/ATX-3.1-PCIe-5.1-H%252b%252b-12V%252d2X6-675W-12VHPWR-16-Pin-Power-Cable.html).\n\n>**How can I identify if my cable is 12VHPWR or 12V-2X6?**\n\n>To determine the type of cable you have, consider the purchase date:\n\n>If the cable was purchased on or before 2024, it is a 12VHPWR.  \nIf the cable was purchased in 2025 or later, it is a 12V-2X6.\n\n>**Are there no changes in specifications between 12VHPWR and 12V-2X6?**\n\n>Yes, 12VHPWR and 12V-2X6 are fully compatible, and there is no change in cable specifications. However, this does not imply that the cable cannot be improved or enhanced.\n\n>It is a misconception that a product cannot be enhanced, or a new product cannot be released unless there is a change in specifications. This is clearly not the case.\n\n>In the PC industry, every product is continually improving and evolving. New products are introduced regularly, offering better features, superior performance, enhanced durability, improved materials, and more attractive designs, regardless of specification changes.\n\n# Falcon Northwest\n\n[Link to post here](https://x.com/falconnw/status/1889428378769564121?s=46)\n\n>HUGE respect for der8auer's testing, but we're not seeing anything like his setup's results.  \nWe tested ***many*** 5090 Founder's builds with multiple PSU & cable types undergoing days of closed chassis burn-in.  \nTemps (**images in F)** & amperages on all 12 wires are nominal.\n\nGPU Side = 165 °F = 73.89 °C\n\nhttps://preview.redd.it/bt13u7qm8pie1.png?width=936&format=png&auto=webp&s=f811da34d31fae20ddbc143f8e9b650b01e88bd8\n\nPSU Side = 157 °F = 69.44 °C\n\nhttps://preview.redd.it/7ulgszdq8pie1.png?width=935&format=png&auto=webp&s=c9b98a2dd4adff345f2440e32f73e25460a98802\n\nCurrent = 7.9A\n\nhttps://preview.redd.it/3j1n57kljpie1.jpg?width=2268&format=pjpg&auto=webp&s=7a21aa050464bed5a9d7d84bd08b6cb703d0bd08\n\n# Jonny-Guru-Gerow (Corsair Head of R&D)\n\n^(Also a legendary PSU reviewer back in 2000s and 2010s)\n\n[Link to Reddit Account here](https://www.reddit.com/user/Jonny-Guru-Gerow/)\n\nSome relevant comments:\n\n>It's a misunderstanding on MODDIY's end. Clearly they're not a member of the PCI-SIG and haven't read through the spec. Because the spec clearly states that the changes made that differentiate 12VHPWR from 12V-2x6 is made only on the connector on the GPU and the PSU (if applicable).\n\n>My best guess of this melted cable comes down to one of several QC issues. Bad crimp. Terminal not fully seated. That kind of thing. Derau8er already pointed out the issue with using mixed metals, but I didn't see any galvanic corrosion on the terminal. Doesn't mean it's not there. There's really zero tolerance with this connector, so even a little bit of GC could potentially cause enough resistance to cause failure. Who knows? I don't have the cable in my hands. :D\n\n\\------\n\n>The MODDIY was not thicker gauge than the Nvidia. They're both 16g. Just the MODDIY cable had a thicker insulation.\n\n\\------\n\n>That's wrong. Then again, that video is full of wrong (sadly. Not being like Steve and looking to beat up on people, but if the wire was moving 22A and was 130°C, it would have melted instantly.)\n\n>16g is the spec and the 12VHPWR connector only supports 16g wire. In fact, the reason why some mod shops sell 17g wire is because some people have problems putting paracord sleeve over a 16g wire and getting a good crimp. That extra mm going from16g to 17g is enough to allow the sleeve to fit better. But that's not spec. Paracord sleeves aren't spec. The spec is 16g wire. PERIOD.\n\n\\------\n\n>If it was that hot, he wouldn't be able to hold it in his hand. I don't know what his IR camera was measuring, but as Aris pointed out.... that wire would've melted. I've melted wires with a lot less current than that.\n\n>Also, the fact that the temperature at the PSU is hotter than the GPU is completely backwards from everything I've ever tested. And I've tested a lot. Right now I have a 5090 running Furmark 2 for an hour so far and I have 46.5°C at the PSU and 64.2°C at the GPU in a 30°C room. The card is using 575.7W on average.\n\n>Derau8er is smart. Hr'll figure things out sooner than later. I just think his video was too quick and dirty. Proper testing would be to move those connectors around the PSU interface. Unplug and replug and try again. Try another cable. At the very least, take all measurements at least twice. He's got everyone in an uproar and it's really all for nothing. Not saying there is no problem. I personally don't \\*like\\* the connector, but we don't have enough information right now and shouldn't be basing assumptions on some third party cable from some Hong Kong outfit.\n\n\\------\n\n>ABSOLUTELY. There is no argument that there is going to be different resistance across different pins. But no wire/terminal should get hotter than 105°C. We're CLEARLY seeing a problem where terminals are either not properly crimped, inserted, corroded, etc. what have you, and the power is going to a path of less resistance. But this is a design problem. I can't fix this. :-( (well... I can, maybe, but it requires overcomplicating the cable and breaking the spec)\n\n\\------\n\n>They provide this if your PSU is not capable of more than 150W per 8-pin. If used with a PSU that CAN provide more than 150W per 8-pin, it just splits the load up across the four connections\n\n>There is no \"6+2-pin to 12VHPWR\". The cable is a 2x4-pin Type 4 or 5 to 12V-2x6. There is no disadvantage to using this as the 12VHPWR has 6 12V conductors and 6 grounds and two sense that need to be grounded. 2x Type 4 connection gives you up to 8x 12V and 8x ground. So, this is a non-issue.\n\n>12VHPWR to 12VHPWR is fine too. Just like the 2x Type 4 8-pin or 2x Type 5 8-pin, you have a one-to-one connection between the PSU and the GPU. That' s why I don't like calling these cables \"adapters\". If it's one-to-one, it's not an adapter. It's just a \"cable\".\n\n\\------\n\n>The 8-pin PCIe is rated for 150W on the GPU side. The actual cable and connectors' rating is dependant on the materials used.\n\n>The 150W part came from the assumption that the worst case materials are used. Things like 20g wire. Phosphor bronze terminals. In most cases today, a single 8-pin (which is actually effectively only 6-pin since 2 of the pins are \"sense\" wires) can easily handle 300W each.\n\n\\------\n\n>So, as an update... I intentionally damaged a terminal (shoved a screwdriver in it and twisted), am getting < 1A on it and the others are over 10A. Not 20A, though. Which, if der8auers numbers are accurate, means the cable has MULTIPLE faults. Which may actually be the case. But I think he would have noticed that and called that out. \\*shrug\\* I hope he posts an update. He's more than welcome to reach out to me for a unlimited supply of cables. :D\n\n# Wendell - Level1Techs\n\n[Link to post here](https://x.com/tekwendell/status/1889431981425041691)\n\n>I've been testing with the FE 5090 w/ 550w+  in and out of the tiki and haven't had anything alarming for cable heating yet fwiw. I only have the one 5090 but I imagine Falcon has A Lot More Than One going out the door \\[right now\\]. plus the thermal imaging is neat! still testing\n\n# Andreas Schilling - Hardwareluxx\n\n[Link to post here](https://x.com/aschilling/status/1889360334466457843?t=-Rb2ee-A-NVoaXoKx25maQ&s=19)\n\nhttps://preview.redd.it/h8k9199h7pie1.png?width=1206&format=png&auto=webp&s=831a022fe63a4e66a1a4b415c0558fa8f21712a5\n\n# Igor's Lab\n\nArticle: [Groundhog Day: The 12V2X6, melting contacts and unbalanced loads – what we know and what we don’t know](https://www.igorslab.de/en/groundhog-day-the-12v2x6-melting-contacts-and-unbalanced-loads-what-we-know-and-what-we-dont-know/)\n\n**RTX 5090 Founders Edition Measurements**:\n\nhttps://preview.redd.it/y03fag95hwie1.png?width=3478&format=png&auto=webp&s=dff5f28988d83cd10512a3487932bf05b3d93805\n\n**MSI RTX 5090 Suprim Measurements:**\n\nhttps://preview.redd.it/1b7d89t6hwie1.png?width=3496&format=png&auto=webp&s=0c6ae23e3cd2b262e064dcae15788eefad386dab\n\n>What can be concluded from this? If something goes wrong, then at most it is the cable and connector. Two plugs, four results? It’s not quite that extreme, but another cable change shows: The values change slightly each time they are plugged in, which indicates the general deficiencies of the plug connection (clamping surface, contact). Added to this is the voltage drop, which also depends on chance.\n\n>The shortcomings of the 12VHPWR connector, in particular the uneven current distribution through the cable and connector, can cause unbalanced loads where individual pins are loaded more than others. These local overloads lead to increased contact resistance and heat generation, which under certain conditions can cause thermal damage to contacts and cables. In addition, by dispensing with active balancing and splitting the power supply across several rails in the board topology, NVIDIA has itself abandoned possible protective and corrective measures. As the cards directly take over the faulty distribution of the input side, the power load remains uncontrolled, which can lead to escalation under the wrong conditions.\n\n>This situation shows how several factors can interact: The inadequate plug connection as a starting point, the resulting thermal issues as a potential symptom, and the lack of protection measures on the board as an untapped opportunity to remedy the situation. Although such problems do not necessarily have to occur, the system remains susceptible to this concatenation if the load and the external conditions coincide unfavorably\n\n>The symptoms of melting contacts and overheated cables in modern GPUs can be explained as a chain of unfortunate circumstances that do not necessarily have to occur. On the contrary, it will probably remain the exception. But it can happen\n\n# OC3D\n\nVideo - [Link Here](https://www.youtube.com/watch?v=w3BXarPl1F0)\n\nArticle - [Link Here](https://overclock3d.net/reviews/gpu_displays/asus-saved-our-bacon-we-had-12vhpwr-12v-2x6-cable-issues/)\n\nhttps://preview.redd.it/0qtucj5qyyie1.png?width=3598&format=png&auto=webp&s=a06d4dabbd26e0e02ac4e21b8310de7d99a53f6e\n\n>While testing ASUS’ ROG Astral RTX 5090 LC GPU, we uncovered a startling problem. **Despite correctly/fully inserting our 16-pin GPU power cable, several of our GPU’s voltage pins had red indicators. Power was being unevenly pulled through our power connectors**.\n\n>**After repeatedly reseating our cables, we found that at least one light remained red**. While **we could get all lights to be green with careful manipulation, we clearly had a problem**. More shockingly, this problem would not have been noticed without ASUS’ “Power Detector” feature. Had we not been reviewing this specific graphics card, this problem would never have been noticed.\n\n>**All lights were green when we switched to a new 12V-2×6 power cable. Only our hard-used 16-pin power cables had issues. This implies that general wear and tear could make the difference between a safe and a dangerous power cable**. However, we must note that we have been using the same 16-pin power cables for years of GPU testing, making our cables incredibly well-worn.\n\n>**Today, we learned that worn/used 16-pin GPU power cables can have uneven power distribution across the cable**. Potentially, this can lead to dangerous amounts of power going through specific voltage pins. **To be frank, the OC3D GPU test system was on the road to disaster. Our cables were used to test a huge number of graphics cards, and that wear adds up. While we don’t expect many other PC builders to use/abuse their 16-pin cables as much as we do, cable wear is a factor that PC builders must consider. The safety margins of the 12V-2×6/12VHPWR standard are too low for us to simply ignore this issue.**\n\n>From now on, 16-pin GPU power cables will be considered by us as a consumable item. To help avoid issues, we will be replacing our cables regularly to help prevent catastrophic issues.\n\n>**For consumers, our recommendation is clear. When you buy a power-hungry GPU, consider buying a new 16-pin power cable.** **If you bought a new PSU with your GPU, you won’t need a new cable. However, if you plan to reuse your power supply, a new 12V-2×6 cable could save your bacon**. A lot of PSU manufacturers sell replacement 12V-2×6 cables, and many good 3rd party options are available (like those from **CableMod**).\n\n>With high-wattage GPUs costing £1,000+, purchasing a £20-30 cable is a worthy investment for those who want some extra peace of mind. It’s just a shame that such considerations are necessary.\n\n# Jayz2Cents\n\nVideo - [I inserted these cables over 100 times! Does 12VHPWR REALLY wear out after 30 cycles?](https://www.youtube.com/watch?v=lAdLOf5of8Y)\n\nhttps://preview.redd.it/godkbsm1eppe1.png?width=1589&format=png&auto=webp&s=d12ec6ef33146d8e3090e4cea886eefad77dc862\n\nhttps://preview.redd.it/xnylgkz5eppe1.png?width=1565&format=png&auto=webp&s=4ace4ac83f45462a2f9fd2649ae3903970dc503d\n\nhttps://preview.redd.it/094lknm9eppe1.png?width=1620&format=png&auto=webp&s=baadfbe46e12779d3cd595806a1f2d36445c42ef", "author": "Nestledrink", "created_time": "2025-02-12T12:30:57", "url": "https://reddit.com/r/nvidia/comments/1inpox7/rtx_50_series_12vhpwr_megathread/", "upvotes": 564, "comments_count": 1193, "sentiment": "neutral", "engagement_score": 2950.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1inu9y7", "title": "Is the Tesla Bubble about to pop?", "content": "I’m not a Tesla expert, and I’m sure many of you have deeper insights into the company, but after watching the latest developments, I have some concerns that I wanted to share as a long time holder. Tesla’s stock is sitting around $330 per share, up 75% from last year, but I’m struggling to see how it justifies the current valuation.\n\nHere’s a list of what bothers me:\n\n1. **<PERSON>on Musk’s PR Nightmare** – It’s hard to ignore how much <PERSON><PERSON>’s public image has deteriorated. Between the “Sieg Heil” incident on live TV and his controversial actions, he’s lost a lot of credibility, and by extension, <PERSON><PERSON>’s brand has taken a serious hit. A lot of people now see him and the company as 'problematic'.\n\n1. **Competition Is Catching Up Fast** – Tesla used to be the only game in town when it came to EVs and FSD, but that’s no longer the case. Other automakers are making significant strides in both areas, and some are already ahead in terms of technology and innovation.\n\n1. **Declining Profit Margins** – Te<PERSON>’s profit margins have been shrinking year after year. This is concerning for a company that’s valued at over a trillion dollars. If this trend continues, it could put a major dent in their long-term prospects.\n\n1. **The Cybertruck Disaster** – The Cybertruck was supposed to be Tesla’s next big thing, but it’s turned into more of a meme. The build quality issues and the general lack of progress on the product have made it a major disappointment, and it’s starting to look like a marketing gimmick more than a groundbreaking vehicle.\n\n1. **Full Self-Driving Tech Overhyped** – Despite the constant chatter about robotaxis, Tesla’s self-driving tech is still stuck at Level 2-3. Other companies have already rolled out Level 4 autonomous vehicles, making Tesla’s claims look more like wishful thinking at this point.\n\n1. **Political and Tariff Risks** – Tesla’s business is exposed to a number of external factors, from steel and aluminum tariffs to an anti-EV U.S. President. Plus, with a California governor who openly opposes Musk, there’s a real risk of losing state EV incentives, which would hurt sales.\n\n1. **Raw Material Costs Are Rising** – Tesla is also facing increasing raw material costs, and tariffs on batteries imported from China could push those prices even higher. With profitability already under pressure, this is something that could really squeeze margins going forward.\n\n1. **Struggling to Attract AI Talent** – Tesla’s ability to innovate in AI seems to be stalling. The company is having trouble attracting top AI talent, with many researchers opting to join OpenAI, Google, Amazon, and other big tech companies. This could set them back in the long run, especially as competitors accelerate their own AI developments.\n\n1. **Losing Support from Left-Leaning Consumers** – Tesla’s alignment with Musk’s political views has alienated many left-leaning consumers, who are typically more inclined to purchase EVs. This shift in consumer sentiment could hurt Tesla’s market share, especially as alternatives to Tesla grow.\n\nI used to think of Tesla as the Nvidia of EVs, miles ahead of everyone else. But right now, it feels like they’re slipping, with more and more companies closing the gap. Like probably lots of you (well, at least those on TikTok) I’ve been using the trademind AI a lot recently, and that also has TSLA as a clear red flag to sell now. With all these issues piling up, I really struggle to see how Tesla maintains its $1.1 trillion valuation.\n\nWhat do you think? Am I missing something, or is Tesla headed for some rough times ahead?", "author": "BirthdayOk5077", "created_time": "2025-02-12T16:02:59", "url": "https://reddit.com/r/investing/comments/1inu9y7/is_the_tesla_bubble_about_to_pop/", "upvotes": 695, "comments_count": 148, "sentiment": "bullish", "engagement_score": 991.0, "source_subreddit": "investing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1inwvlg", "title": "TIL Google’s 2004 IPO used a Dutch auction, where investors bid the highest price they’re willing to pay, and everyone gets shares at the lowest price that clears all available stock (the market-clearing price).", "content": "", "author": "Olshansk", "created_time": "2025-02-12T17:48:05", "url": "https://reddit.com/r/todayilearned/comments/1inwvlg/til_googles_2004_ipo_used_a_dutch_auction_where/", "upvotes": 7552, "comments_count": 88, "sentiment": "neutral", "engagement_score": 7728.0, "source_subreddit": "todayilearned", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1inydfk", "title": "Tesla stock is tumbling. Blame BYD", "content": "https://qz.com/byd-tesla-gods-eye-self-driving-autonomous-1851760339\n\n> The Chinese automaker backed by <PERSON>'s Berkshire Hathaway announced it's rolling out new autonomous driving capabilities on all models\n\nSomething to note - BYD doesn't sell cars in the US so Trump's tariffs has 0 impact on them. They also have the best selling electric cars in China and are growing their market in Europe + Latin America. And as the world sours on America, American products (including cars), and Tesla - they can even gain from <PERSON>'s decrees. It has gone up by 25% since <PERSON> came into office.\n", "author": "mr_fobolous", "created_time": "2025-02-12T18:47:33", "url": "https://reddit.com/r/stocks/comments/1inydfk/tesla_stock_is_tumbling_blame_byd/", "upvotes": 2756, "comments_count": 490, "sentiment": "bearish", "engagement_score": 3736.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1io0wsa", "title": "Google defends scrapping AI pledges and DEI goals in all-staff meeting", "content": "", "author": "a_dogs_mother", "created_time": "2025-02-12T20:30:31", "url": "https://reddit.com/r/news/comments/1io0wsa/google_defends_scrapping_ai_pledges_and_dei_goals/", "upvotes": 2716, "comments_count": 335, "sentiment": "neutral", "engagement_score": 3386.0, "source_subreddit": "news", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1io3lk0", "title": "Reddit shares plunge after Google algorithm change contributes to miss in user numbers", "content": "A Google search algorithm change caused some “volatility” with user growth in fourth quarter, but Reddit’s search-related traffic has recovered in the first quarter, Reddit CEO <PERSON> said.\n\n[https://www.cnbc.com/2025/02/12/reddit-rddt-q4-2024.html](https://www.cnbc.com/2025/02/12/reddit-rddt-q4-2024.html)", "author": "Next-Particular1476", "created_time": "2025-02-12T22:23:31", "url": "https://reddit.com/r/business/comments/1io3lk0/reddit_shares_plunge_after_google_algorithm/", "upvotes": 451, "comments_count": 61, "sentiment": "bullish", "engagement_score": 573.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}]