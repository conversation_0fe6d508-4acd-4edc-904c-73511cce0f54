#!/usr/bin/env python3
"""
隐蔽式Reddit数据收集器
使用更温和的策略避免403错误
"""

import os
import json
import time
import logging
import random
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
import argparse

import praw
import prawcore
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class StealthRedditCollector:
    """隐蔽式Reddit收集器"""
    
    def __init__(self, output_dir: str = "social_media_data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.setup_logging()
        self.reddit = self.create_reddit_client()
        
        # 股票关键词
        self.ticker_keywords = {
            'GOOGL': ['Google', 'GOOGL', 'Alphabet', 'YouTube', 'Android', 'Chrome'],
            'TSLA': ['Tesla', 'TSLA', 'Elon Musk', 'Model S', 'Model 3', 'Model Y', 'Cybertruck'],
        }
        
        # 使用更小众的子版块，避免热门版块的严格限制
        self.stealth_subreddits = [
            'SecurityAnalysis',  # 较小的投资版块
            'ValueInvesting',    # 价值投资版块
            'financialindependence',  # 财务独立版块
        ]
        
        # 处理过的帖子
        self.processed_posts: Set[str] = set()
        self.load_processed_posts()
        
        # 请求计数器和限制
        self.request_count = 0
        self.max_requests_per_hour = 30  # 严格限制请求频率
        self.start_time = time.time()
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "reddit_stealth.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_reddit_client(self):
        """创建Reddit客户端"""
        client_id = os.getenv('REDDIT_CLIENT_ID')
        client_secret = os.getenv('REDDIT_CLIENT_SECRET')
        user_agent = os.getenv('REDDIT_USER_AGENT', 'research-tool/1.0')  # 更学术化的用户代理
        
        if not client_id or not client_secret:
            raise ValueError("Reddit API凭据未配置")
        
        return praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
    
    def load_processed_posts(self):
        """加载已处理的帖子"""
        cache_file = self.output_dir / "processed_posts_cache.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.processed_posts = set(data.get('processed_posts', []))
                self.logger.info(f"加载了 {len(self.processed_posts)} 个已处理帖子ID")
            except Exception as e:
                self.logger.warning(f"加载缓存失败: {e}")
    
    def save_processed_posts(self):
        """保存已处理的帖子"""
        cache_file = self.output_dir / "processed_posts_cache.json"
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'processed_posts': list(self.processed_posts),
                    'last_updated': datetime.now().isoformat()
                }, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def check_rate_limit(self):
        """检查请求频率限制"""
        elapsed_time = time.time() - self.start_time
        if elapsed_time < 3600:  # 一小时内
            if self.request_count >= self.max_requests_per_hour:
                wait_time = 3600 - elapsed_time
                self.logger.info(f"达到请求限制，等待 {wait_time:.0f} 秒...")
                time.sleep(wait_time)
                self.request_count = 0
                self.start_time = time.time()
    
    def safe_request(self, func, *args, **kwargs):
        """安全执行请求"""
        self.check_rate_limit()
        self.request_count += 1
        
        # 随机延迟，模拟人类行为
        delay = random.uniform(10, 30)  # 10-30秒随机延迟
        self.logger.info(f"等待 {delay:.1f} 秒后执行请求...")
        time.sleep(delay)
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 403:
                    wait_time = (attempt + 1) * 60  # 递增等待时间
                    self.logger.warning(f"403错误，等待 {wait_time} 秒后重试 (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue
                elif e.response.status_code == 429:
                    wait_time = (attempt + 1) * 120  # 更长的等待时间
                    self.logger.warning(f"429限制错误，等待 {wait_time} 秒后重试")
                    time.sleep(wait_time)
                    continue
                else:
                    raise
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 30
                    self.logger.warning(f"请求失败，等待 {wait_time} 秒后重试: {e}")
                    time.sleep(wait_time)
                    continue
                else:
                    raise
        
        return None
    
    def extract_tickers_from_text(self, text: str) -> List[str]:
        """从文本中提取股票代码"""
        if not text:
            return []
        
        text_lower = text.lower()
        found_tickers = []
        
        for ticker, keywords in self.ticker_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    found_tickers.append(ticker)
                    break
        
        return list(set(found_tickers))
    
    def get_subreddit_posts(self, subreddit_name: str, limit: int = 3) -> List:
        """获取子版块帖子"""
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            
            # 只尝试获取热门帖子，避免new和top
            def get_hot_posts():
                return list(subreddit.hot(limit=limit))
            
            posts = self.safe_request(get_hot_posts)
            return posts or []
            
        except Exception as e:
            self.logger.error(f"获取 r/{subreddit_name} 帖子失败: {e}")
            return []
    
    def process_submission(self, submission) -> Optional[Dict[str, Any]]:
        """处理单个提交"""
        try:
            if submission.id in self.processed_posts:
                return None
            
            # 获取文本内容
            title = getattr(submission, 'title', '') or ""
            content = getattr(submission, 'selftext', '') or ""
            full_text = f"{title} {content}"
            
            # 提取相关股票代码
            tickers = self.extract_tickers_from_text(full_text)
            if not tickers:
                return None
            
            # 创建帖子数据
            created_time = datetime.fromtimestamp(
                submission.created_utc, tz=timezone.utc
            ).replace(tzinfo=None)
            
            post_data = {
                'platform': 'reddit',
                'post_id': f"reddit_{submission.id}",
                'title': title,
                'content': content,
                'author': str(submission.author) if submission.author else 'deleted',
                'created_time': created_time.isoformat(),
                'url': f"https://reddit.com{submission.permalink}",
                'upvotes': getattr(submission, 'score', 0),
                'comments_count': getattr(submission, 'num_comments', 0),
                'tickers': tickers,
                'sentiment': 'neutral',
                'engagement_score': getattr(submission, 'score', 0) * 1.0 + getattr(submission, 'num_comments', 0) * 2.0,
                'source_subreddit': submission.subreddit.display_name,
                'hashtags': None
            }
            
            # 标记为已处理
            self.processed_posts.add(submission.id)
            
            return post_data
            
        except Exception as e:
            self.logger.error(f"处理提交失败 {submission.id}: {e}")
            return None
    
    def collect_from_subreddit(self, subreddit_name: str, limit: int = 3) -> List[Dict[str, Any]]:
        """从子版块收集数据"""
        posts = []
        
        try:
            self.logger.info(f"开始收集 r/{subreddit_name} 数据...")
            
            submissions = self.get_subreddit_posts(subreddit_name, limit)
            
            if not submissions:
                self.logger.warning(f"r/{subreddit_name} 未获取到任何帖子")
                return posts
            
            self.logger.info(f"r/{subreddit_name} 获取到 {len(submissions)} 个帖子")
            
            # 处理提交
            for submission in submissions:
                try:
                    post_data = self.process_submission(submission)
                    if post_data:
                        posts.append(post_data)
                        self.logger.info(f"找到相关帖子: {post_data['title'][:50]}...")
                    
                except Exception as e:
                    self.logger.error(f"处理提交失败: {e}")
                    continue
            
            self.logger.info(f"r/{subreddit_name} 收集到 {len(posts)} 个相关帖子")
            
        except Exception as e:
            self.logger.error(f"收集 r/{subreddit_name} 失败: {e}")
        
        return posts
    
    def save_posts_by_ticker(self, posts: List[Dict[str, Any]]):
        """按股票代码保存帖子"""
        if not posts:
            return
        
        posts_by_ticker = {}
        
        for post in posts:
            for ticker in post['tickers']:
                if ticker not in posts_by_ticker:
                    posts_by_ticker[ticker] = []
                
                ticker_post = post.copy()
                ticker_post['ticker'] = ticker
                del ticker_post['tickers']
                
                posts_by_ticker[ticker].append(ticker_post)
        
        # 保存到文件
        for ticker, ticker_posts in posts_by_ticker.items():
            ticker_dir = self.output_dir / f"{ticker}_social_media"
            ticker_dir.mkdir(parents=True, exist_ok=True)
            
            date_str = datetime.now().strftime('%Y-%m-%d')
            file_path = ticker_dir / f"reddit_{date_str}.json"
            
            # 合并现有数据
            existing_posts = []
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        existing_posts = json.load(f)
                except Exception as e:
                    self.logger.warning(f"读取现有文件失败: {e}")
            
            # 去重并保存
            existing_ids = {post.get('post_id') for post in existing_posts}
            new_posts = [post for post in ticker_posts 
                        if post.get('post_id') not in existing_ids]
            
            if new_posts:
                all_posts = existing_posts + new_posts
                all_posts.sort(key=lambda x: x.get('created_time', ''))
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(all_posts, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"保存 {ticker}: {len(new_posts)} 个新帖子")
    
    def collect_data(self, limit_per_subreddit: int = 3) -> Dict[str, int]:
        """收集数据"""
        self.logger.info("开始隐蔽式Reddit数据收集")
        self.logger.info(f"目标股票: {list(self.ticker_keywords.keys())}")
        self.logger.info(f"目标子版块: {self.stealth_subreddits}")
        self.logger.info(f"请求限制: 每小时最多 {self.max_requests_per_hour} 个请求")
        
        all_posts = []
        stats = {'total_posts': 0, 'relevant_posts': 0, 'subreddits_processed': 0}
        
        for subreddit_name in self.stealth_subreddits:
            try:
                posts = self.collect_from_subreddit(subreddit_name, limit_per_subreddit)
                all_posts.extend(posts)
                stats['subreddits_processed'] += 1
                stats['relevant_posts'] += len(posts)
                
                # 立即保存数据
                if posts:
                    self.save_posts_by_ticker(posts)
                    self.save_processed_posts()
                
                # 子版块间添加长延迟
                if subreddit_name != self.stealth_subreddits[-1]:  # 不是最后一个
                    delay = random.uniform(300, 600)  # 5-10分钟延迟
                    self.logger.info(f"子版块间等待 {delay/60:.1f} 分钟...")
                    time.sleep(delay)
                
            except Exception as e:
                self.logger.error(f"处理 r/{subreddit_name} 失败: {e}")
                continue
        
        self.save_processed_posts()
        stats['total_posts'] = len(self.processed_posts)
        
        return stats

def main():
    parser = argparse.ArgumentParser(description='隐蔽式Reddit数据收集器')
    parser.add_argument('--output-dir', default='social_media_data', help='输出目录')
    parser.add_argument('--limit-per-subreddit', type=int, default=3, help='每个子版块的帖子限制')
    
    args = parser.parse_args()
    
    try:
        collector = StealthRedditCollector(args.output_dir)
        
        print("⚠️  隐蔽模式启动")
        print("   - 使用极低的请求频率")
        print("   - 每小时最多30个请求")
        print("   - 子版块间等待5-10分钟")
        print("   - 预计完成时间: 30-60分钟")
        print()
        
        stats = collector.collect_data(args.limit_per_subreddit)
        
        print("\n" + "="*50)
        print("Reddit数据收集完成")
        print("="*50)
        print(f"处理子版块数: {stats['subreddits_processed']}")
        print(f"相关帖子数: {stats['relevant_posts']}")
        print(f"总处理帖子数: {stats['total_posts']}")
        print(f"数据保存在: {args.output_dir}")
        
    except Exception as e:
        print(f"收集失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
