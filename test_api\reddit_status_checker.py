#!/usr/bin/env python3
"""
Reddit API状态检查器
用于诊断"昨天能用今天不能用"的403错误问题
"""

import os
import time
import requests
import praw
import prawcore
from datetime import datetime, timedelta
from dotenv import load_dotenv
import json

load_dotenv()

class RedditStatusChecker:
    def __init__(self):
        self.client_id = os.getenv('REDDIT_CLIENT_ID')
        self.client_secret = os.getenv('REDDIT_CLIENT_SECRET')
        self.user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
        self.username = os.getenv('REDDIT_USERNAME')
        self.password = os.getenv('REDDIT_PASSWORD')
        
    def check_reddit_status(self):
        """检查Reddit服务状态"""
        print("=== Reddit服务状态检查 ===")
        
        # 1. 检查Reddit状态页面
        try:
            response = requests.get('https://www.redditstatus.com/api/v2/status.json', timeout=10)
            if response.status_code == 200:
                status_data = response.json()
                status = status_data.get('status', {}).get('description', 'unknown')
                print(f"✓ Reddit官方状态: {status}")
            else:
                print(f"⚠ 无法获取Reddit状态 (HTTP {response.status_code})")
        except Exception as e:
            print(f"⚠ Reddit状态检查失败: {e}")
        
        # 2. 检查Reddit API状态
        try:
            response = requests.get('https://www.reddit.com', timeout=10)
            print(f"✓ Reddit主站访问: HTTP {response.status_code}")
        except Exception as e:
            print(f"✗ Reddit主站访问失败: {e}")
    
    def check_api_limits(self):
        """检查API限制和配额"""
        print("\n=== API限制检查 ===")
        
        try:
            reddit = praw.Reddit(
                client_id=self.client_id,
                client_secret=self.client_secret,
                user_agent=self.user_agent,
                username=self.username,
                password=self.password
            )
            
            # 检查认证状态
            try:
                user = reddit.user.me()
                print(f"✓ 用户认证正常: {user.name}")
                print(f"✓ 账户创建时间: {datetime.fromtimestamp(user.created_utc)}")
                print(f"✓ 账户karma: {user.link_karma + user.comment_karma}")
                
                # 检查账户是否被限制
                if user.is_suspended:
                    print("❌ 账户已被暂停")
                    return False
                    
            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 401:
                    print("❌ 认证失败 - 可能密码已更改")
                elif e.response.status_code == 403:
                    print("❌ 账户访问被禁止")
                else:
                    print(f"❌ 认证错误: {e}")
                return False
            
            # 测试API调用频率
            print("\n检查API调用频率限制...")
            start_time = time.time()
            
            for i in range(5):
                try:
                    subreddit = reddit.subreddit('test')
                    _ = subreddit.display_name
                    print(f"  调用 {i+1}: ✓")
                    time.sleep(1)
                except prawcore.exceptions.TooManyRequests:
                    print(f"  调用 {i+1}: ❌ 速率限制")
                    return False
                except Exception as e:
                    print(f"  调用 {i+1}: ❌ {e}")
            
            elapsed = time.time() - start_time
            print(f"✓ 5次API调用耗时: {elapsed:.2f}秒")
            
            return True
            
        except Exception as e:
            print(f"❌ API限制检查失败: {e}")
            return False
    
    def test_different_approaches(self):
        """测试不同的访问方法"""
        print("\n=== 测试不同访问方法 ===")
        
        approaches = [
            ("只读模式", self._test_readonly),
            ("用户认证模式", self._test_user_auth),
            ("延迟访问", self._test_with_delay),
            ("小批量访问", self._test_small_batch)
        ]
        
        results = {}
        
        for name, test_func in approaches:
            print(f"\n测试 {name}:")
            try:
                success = test_func()
                results[name] = success
                print(f"  结果: {'✓ 成功' if success else '✗ 失败'}")
            except Exception as e:
                results[name] = False
                print(f"  结果: ✗ 异常 - {e}")
        
        return results
    
    def _test_readonly(self):
        """测试只读模式"""
        reddit = praw.Reddit(
            client_id=self.client_id,
            client_secret=self.client_secret,
            user_agent=self.user_agent
        )
        
        subreddit = reddit.subreddit('test')
        posts = list(subreddit.new(limit=1))
        return len(posts) > 0
    
    def _test_user_auth(self):
        """测试用户认证模式"""
        reddit = praw.Reddit(
            client_id=self.client_id,
            client_secret=self.client_secret,
            user_agent=self.user_agent,
            username=self.username,
            password=self.password
        )
        
        subreddit = reddit.subreddit('test')
        posts = list(subreddit.new(limit=1))
        return len(posts) > 0
    
    def _test_with_delay(self):
        """测试带延迟的访问"""
        reddit = praw.Reddit(
            client_id=self.client_id,
            client_secret=self.client_secret,
            user_agent=self.user_agent,
            username=self.username,
            password=self.password
        )
        
        subreddit = reddit.subreddit('test')
        time.sleep(5)  # 5秒延迟
        posts = list(subreddit.new(limit=1))
        return len(posts) > 0
    
    def _test_small_batch(self):
        """测试小批量访问"""
        reddit = praw.Reddit(
            client_id=self.client_id,
            client_secret=self.client_secret,
            user_agent=self.user_agent,
            username=self.username,
            password=self.password
        )
        
        # 尝试访问多个子版块，每次只获取1个帖子
        subreddits = ['test', 'python', 'news']
        success_count = 0
        
        for sub_name in subreddits:
            try:
                subreddit = reddit.subreddit(sub_name)
                posts = list(subreddit.new(limit=1))
                if posts:
                    success_count += 1
                time.sleep(2)  # 每次请求间隔2秒
            except:
                continue
        
        return success_count > 0
    
    def generate_solutions(self, test_results):
        """根据测试结果生成解决方案"""
        print("\n=== 解决方案建议 ===")
        
        if any(test_results.values()):
            print("✓ 找到可用的访问方法:")
            for method, success in test_results.items():
                if success:
                    print(f"  • {method} - 可用")
        else:
            print("❌ 所有访问方法都失败")
            
        print("\n推荐的解决方案:")
        
        # 基于结果提供具体建议
        if test_results.get("延迟访问", False):
            print("1. 🔧 添加请求延迟 (推荐)")
            print("   - 在每次API调用之间添加2-5秒延迟")
            print("   - 使用指数退避策略处理429错误")
            
        if test_results.get("小批量访问", False):
            print("2. 🔧 减少批量大小")
            print("   - 将limit从1000减少到10-50")
            print("   - 分批处理数据收集")
            
        if test_results.get("用户认证模式", False):
            print("3. 🔧 使用用户认证模式")
            print("   - 确保使用用户名和密码认证")
            
        print("4. ⏰ 时间相关解决方案:")
        print("   - 等待1-2小时后重试 (可能是临时限制)")
        print("   - 避开Reddit高峰时段 (美国东部时间9-17点)")
        print("   - 尝试在不同时间段运行脚本")
        
        print("5. 🔄 重试策略:")
        print("   - 实现指数退避重试机制")
        print("   - 捕获403错误并等待后重试")
        print("   - 记录失败的请求以便后续处理")
        
        print("6. 📊 替代数据源:")
        print("   - 使用已收集的本地数据")
        print("   - 考虑其他社交媒体数据源")
        print("   - 使用Reddit的RSS feeds (限制较少)")

def main():
    checker = RedditStatusChecker()
    
    print("Reddit API 403错误诊断工具")
    print("=" * 50)
    print(f"诊断时间: {datetime.now()}")
    
    # 1. 检查Reddit服务状态
    checker.check_reddit_status()
    
    # 2. 检查API限制
    api_ok = checker.check_api_limits()
    
    # 3. 测试不同方法
    if api_ok:
        test_results = checker.test_different_approaches()
        checker.generate_solutions(test_results)
    else:
        print("\n❌ 基础API访问失败，建议:")
        print("1. 等待1-2小时后重试")
        print("2. 检查Reddit账户状态")
        print("3. 考虑使用备用账户")
    
    print(f"\n诊断完成: {datetime.now()}")

if __name__ == '__main__':
    main()
