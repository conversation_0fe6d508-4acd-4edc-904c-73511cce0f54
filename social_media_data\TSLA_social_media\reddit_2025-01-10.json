[{"platform": "reddit", "post_id": "reddit_1hxt2s3", "title": "BREAKING: Tesla China reveals the new Model Y", "content": "", "author": "Adventurous-Date-397", "created_time": "2025-01-10T01:33:41", "url": "https://reddit.com/r/wallstreetbets/comments/1hxt2s3/breaking_tesla_china_reveals_the_new_model_y/", "upvotes": 1931, "comments_count": 779, "sentiment": "neutral", "engagement_score": 3489.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hxutng", "title": "$3,800 spent on FB ads for Dental Clinic — Here’s What Actually Worked", "content": "Hey guys, recently been working with a dental clinic from Zurich, helping them with Fb lead form ads, content marketing and CRM automation.\n\nHere's what worked, what didn’t, and why these tips might just save you some headaches and get $ fast if you work in similar niches.\n\n**Ads stats:**\n\n* Leads Generated: 166\n* Ad Spend: 3,500 CHF (\\~$3,800)\n* CPL: 21 CHF ($23) well below the $50–$285 industry average.\n* Projected Revenue: $39,000 - $59,000 (based on deal values)\n\n**What Worked Best:**\n\n**1. Reactivate Clinic Database First**  \nWe started by emailing and texting old leads of the clinic (that were considered dead 💀)\n\n* Out of 1k prospects, within 2 weeks, **15 appointments** were booked.\n\n**2. Respond to Leads in Under 5 Minutes**  \nAutomatic email and SMS to notify staff the second a lead form was submitted, and initiate a bridge call so the lead got contacted instantly. (if out of biz hours - the lead gets an email and contacted next day)'\n\n* **30-40%** more bookings\n\n**3. No Stock Content**  \nI have a video/photographer so we have shot real photos and videos of the clinic’s staff and space. Authenticity boosted trust and:\n\n* CTR improved **by 29%**.\n\n**4. Decrease No-Shows**  \nNo-shows were a big issue for this clinic, so we automated **3 reminders** for every appointment:\n\n1. 24 hours before\n2. The morning of the appointment\n3. 1 hour before\n\n* We got **30% fewer** no-shows by the end of the month.\n\n**5. Highlight Your USP**  \nThe clinic’s USP was Premium Veneers product (very few clinics in Switzerland have them) and the best price for \"All on 4\" procedures. We plastered that everywhere: ad copy, visuals, landing page, social media.\n\n* Engagement rates jumped.\n* Conversion rates **tripled** compared to generic messaging.\n\n**6. Automate Follow-Ups**  \nLeads need multiple nudges to book. We set up a CRM with **5 automated follow-ups** (I'd suggest even more) via SMS and email, ensuring no one fell through the cracks.\n\n* **35% of leads converted to appointments** (58 out of 166)\n\nFollow-ups aren’t optional. Leads forget, get busy, or lose interest—remind them.\n\nWould love to hear your thoughts! What’s worked for you when running lead gen campaigns? Happy to discuss.", "author": "Mortgage-Conscious", "created_time": "2025-01-10T03:03:24", "url": "https://reddit.com/r/DigitalMarketing/comments/1hxutng/3800_spent_on_fb_ads_for_dental_clinic_heres_what/", "upvotes": 125, "comments_count": 50, "sentiment": "neutral", "engagement_score": 225.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hxyf7l", "title": "Has anyone made their money in tech stocks and then converted to dividends for early retirement?", "content": "I've done well with TSLA, AAPL, AMZN, and other tech stocks over the years, and while AAPL does have a dividend, it's minimal compared to something like SCHD or the other dividend stocks.\n\nI'm contemplating early retirement and moving my tech stock gains into SCHD and/or other dividend stocks to live off the dividend income and, ideally, have some modest share price appreciation as well (like SCHD has seen). \n\nHas anyone specifically done this exact move before? I own zero dividend ETFs right now, but I'm running the math, and at a certain point, it feels like it could make sense to de-risk and go into a basket of dividend ETFs and just live off that cash flow.\n\nMy assumption that was buying dividend ETFs now in after tax accounts wouldn't make as much sense for me when I'm also being taxed on my income from working, but when my income from working goes to zero this could make a lot more sense.\n\nThoughts?\n\nAdding some more personal info here:\n\nEarly 40s and married with 3 kids. The youngest is seven, and the oldest 12.", "author": "Bitter_Sugar_8440", "created_time": "2025-01-10T06:37:12", "url": "https://reddit.com/r/dividends/comments/1hxyf7l/has_anyone_made_their_money_in_tech_stocks_and/", "upvotes": 21, "comments_count": 41, "sentiment": "bullish", "engagement_score": 103.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hxzn7b", "title": "DD: BeammWave (BEAMMW_B) - a paradigm shift in wireless communication", "content": "Good day to all of you, this is a deep dive into a a real sleeper! En<PERSON> <PERSON>, who's innovation will likely underpin a great chunk of wireless communication in the future.\n\nThis will be a lengthy one so hang on - but what's five minutes of reading for early retirement in a couple of years, eh?\n\n**Ticker**: BEAMMW.B or BEAMMW\\_B (Nasdaq First North growth market, apparently ticker varies a bit from broker to broker and you might need to activate certain features or markets with your broker to be able to trade)  \n**Current Price**: 5,25 SEK / \\~$0.50 USD  \n**Market Cap**: \\~$15.7M USD  \n**Industry**: wireless communication\n\n*The TL;DR*:\n\n* They have a technology that literally everyone in the mobile industry and beyond will need to be able to compete in the higher radio frequencies\n* Their solution is superior to anything out there today; faster, smaller and more energy efficient, at an affordable price point\n* Competition to what they do is basically zero, with extremely high barriers of entry due to a literal minefield of patents that they own\n* Their tech fits in smartphones, routers, base stations; you name it, it will fit\n* They have the absolute dream team working on this\n* Applications range from mobile, base stations, CPE, operators, satellite, self-driving cars, military, radar etc. the possibilities are endless as long as data needs to be transmitted wirelessly\n\nThis is a recipe for TSLA levels of success.\n\n# Who are they?\n\nBeammWave is a Swedish tech company developing and industrializing a technology called digital distributed beamforming, which makes wireless data transfer significantly faster and more efficient, and solves the looming issue of bandwidth running out on the frequencies in use today. It's the sorely missing key for the mobile industry to utilize higher radio frequencies at scale, something all the big players (think Apple, Qualcomm, Samsung etc.) are frothing at their mouths to get at.\n\nTheir key product is a compact (3x3mm) and affordable (1-2USD/chip) chip system with fully integrated antennas, enabling reliable wireless communication on the mmWave-spectrum without the bulk and subpar performance of the solutions in use today.\n\nLast summer they became the world-first to showcase an over-the-air demo of digital distributed beamforming, and having protected their solution with over 40 patents so far they aim to own the entire market (think being able to own Bluetooth\\* outright, imagine what that would be worth...)\n\n\\**ok, apples to oranges, since Bluetooth is a protocol and distributed digital beamforming is a technical solution, but the common factor is both represent a paradigm shift in the industry, something that everyone will need to adopt to be competitive*\n\n# How are they pulling it off?\n\nFor being a small company in such a daunting industry they have some serious talent behind them – the team consists of telecom industry OG's, several of them having held key roles in developing bluetooth in the 90's and leading development of 5G at some of the biggest firms in the telecom industry, one of their engineers holding over 2000 patents to his name alone.\n\nThese guys garner a lot of respect in the industry and due to that they've had access to discuss their idea with all the big players for years already. As the CEO, Stefan Svedberg, himself says, everybody who's anybody in the industry knows who BeammWave are and what they are working on. Their personal merits have awarded them opportunities to work directly with chip manufacturers who normally don't do prototyping for small startups, and gain access to special tooling and materials usually reserved for the big guys. They also have a company member inside the organization for mobile broadband network standarization, 3GPP, with significant influence there, and they obviously aim to position digital distributed beamforming as the standard for mmWave operations in the future.\n\nSimply put, BeammWave have positioned themselves as thought leaders when it comes to mmWave. The tech they own has sprung out of a decade of research at the University of Lund and what speaks to their clout is they have been able to attract global talent from the likes of Samsung, Huawei etc. to work for them instead.\n\nThe exciting bit is nobody's really caught onto them outside the industry. Yet.\n\n# The geeky bits underpinning the value\n\nWireless communication is a MASSIVE market. The game has always been the same; to get faster speeds, more bandwdith and lower latency with less power consumption and at a cheaper price.\n\nData traffic's been increasing at breakneck speed through the adoption of smartphones, developments in computing, AI etc. to the point where current frequencies in use are simply running out of bandwidth. Moving to higher frequencies, what are called mmWave, is unavoidable in order to solve the data bandwidth problem.\n\nThe issue is once you go up in frequency, the reach and penetration of radio waves diminish significantly. Huge investments have been made into mmWave only for the tech to disappoint time after time, to a point where the whole field has gotten a bit of a bad rep. This is because the industry has been trying to solve it the wrong way.\n\nThe tech in use today is called analog beamforming; the hardware is bulky, expensive, slow at catching a signal, sensitive to spatial change, needs an unobstructed line of sight and simply doesn't work well. From the beginning the founders of BeammWave argued that analog beamforming was a stillborn idea – it simply would not work in real life. They argued the intelligent way to do beamforming was digital – and this is something everyone would agree on, but, the retort was, it wouldn't be cheap enough. It wouldn't be energy efficient enough. It might just be impossible, full stop.\n\nWell, turns out it wasn't. And nobody else was ”crazy” enough to go for it.\n\n# Where are they at today?\n\nSince 2023/4 BeammWave has been out of their research and development phase and are now selling their development platform called ADP1, and are working with customers on projects that aim for production. According to the CEO they're on track to have a fully commercial product ready this year, with production of scale ramping up in 2026.\n\nCurrently, four major companies are working with them and evaluating their product;\n\n1. Molex, which is the second largest electronic connector company in the world,\n2. Alpha Networks, with whom they are working on a fixed wireless access solution together,\n3. SAAB, with whom they are working on innovations with military-civilian synergies, and\n4. an as of yet undisclosed customer, who has revealed they're an Asian component manufacturer and industry leader in mmWave, with over 70 000 employees and 10+ billion USD in revenue. All the hints point toward muRata Manufacturing. In the event the rumors are correct, I can't stress how huge that would be. It could imply that both Apple and Samsung are nibbling on BeammWave's solution already, since muRata supplies them both.\n\nKeep in mind BeammWave are the only ones in the world with this kind of tech - something everyone in the industry needs to be able to compete in the mmWave frequencies. And they've protected their solution with over 40 patents so far, and more incoming. It will be extremely difficult, bordering impossible for anyone to bypass their intellectual property with a distributed digital beamforming solution, no matter who you are.\n\n# So what about the MONEY??\n\nIn interviews the CEO, Stefan Svedberg, has repeatedly been asked to tease some numbers on how their revenues will scale, but understandably he's been quite vague. His exact words from a few months back were that the numbers get pretty ridiculous pretty fast if you start to do math...\n\nBut, since I'm not the CEO, let me pull some numbers outta my ass anyway.\n\nCurrent stock price is sitting around 0,50USD with a mkt cap of around 13,7 million at a 30 million share float. Say BeammWave chips get into 20% of the smartphones before 2030, around 300 million units per year. The estimate is they'll need 6-10 chips per unit at 1-2USD/chip, that's 6-20USD per device. Revenue would be totalling 1,8 – 6 billion USD a year.\n\nLet's lowball a P/E of 5. That would mean a market cap of 9 – 30 billion USD, in other words 300 – 1000 USD/share at the current float. PWOAH! Yeah, that went apeshit pretty fast..! And that's only the mobile market. I won't pull any more numbers outta my ass, but to me this is looking like one of the plays of the decade.\n\nWhy is the current valuation so low? Well, personally I think they'd already be due for a 10x with what they have so far, but they are a very tech-oriented company and what they do is very, very complex. I don't think a lot of people understand the scope of their business case and they haven't been shouting about themselves from the rooftops either. There's no sexy one-paragraph way of explaining the proposition. On top of that, they're trading on the rather obscure Nasdaq First North growth market, and not many eyes have been on them so far. But once their stuff starts going to mass production – oh boy.\n\n**Disclaimer**: Not financial advice and am no expert on this whatsoever – just trying to provide something of value for you guys instead of repeating the same pre-mooned tickers that are on the front page over and over. Do your own DD, but if this thing hits, don’t forget the little guy when you’re sipping cocktails on a beach somewhere.\n\nP.S. There are some really good company presentations where the CEO does a much better job of presenting than I've done, here are a couple of links to start you off:\n\n[https://www.redeye.se/research/1057442/beammwave-interview-with-ceo-stefan-svedberg-2](https://www.redeye.se/research/1057442/beammwave-interview-with-ceo-stefan-svedberg-2)  \n[https://www.redeye.se/video/event-presentation/1063185/beammwave-ceo-stefan-svedberg-presents-at-redeye-technology-life-science-day-2024-december-3](https://www.redeye.se/video/event-presentation/1063185/beammwave-ceo-stefan-svedberg-presents-at-redeye-technology-life-science-day-2024-december-3)\n\n**Disclosure**; I'm balls deep into BeammWave stock, plus I owe a huge debt of gratitude to u/vantrivs for bringing this company to my attention late last year – in this very r/pennystocks.", "author": "he<PERSON><PERSON><PERSON>", "created_time": "2025-01-10T08:06:15", "url": "https://reddit.com/r/pennystocks/comments/1hxzn7b/dd_beammwave_beammw_b_a_paradigm_shift_in/", "upvotes": 38, "comments_count": 26, "sentiment": "bullish", "engagement_score": 90.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hy7rk6", "title": "Why Face ID Isn't As Secure As You Think", "content": "Face ID seems convenient and secure, but here’s the catch: your face is public. Unlike a PIN code or password, your face is always visible and can’t be changed.\n\nFrom my experience working with sensitive data, I’ve seen cases where biometrics were exploited in unexpected ways. For example, using 3D replicas or even under physical coercion, attackers could bypass Face (Touch) ID.\n\nIf you value privacy, switching to a PIN code might be a safer choice—it requires your active consent and stays hidden from the world.\n\nBiometric systems promise security at the cost of privacy. Do you think they’re worth the trade-off, or should we focus on traditional access methods?", "author": "Gremlin_SSD", "created_time": "2025-01-10T16:05:17", "url": "https://reddit.com/r/privacy/comments/1hy7rk6/why_face_id_isnt_as_secure_as_you_think/", "upvotes": 1, "comments_count": 34, "sentiment": "neutral", "engagement_score": 69.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hy7uwb", "title": "Do you REALLY believe that people want better content?", "content": "With all these “What are we leaving behind in the New Year” posts, I see a lot of people saying to focus more on connection and community. And while I agree that’s important… I feel like there’s still a place for the borderline spam content? I feel like this sub gets stuck in an information loop and we forget that we’re the marketeers; of course we’re fed up with the tips and tricks we’re using. But if they’re still getting results, aren’t they still useful? I feel like the average consumer isn’t at the point of not consuming average content just yet (even if we are getting close). I still feel like we have another year or two at least before the mass of the general public catches up, but I’d love to hear your thoughts.", "author": "brandongboyce", "created_time": "2025-01-10T16:09:33", "url": "https://reddit.com/r/DigitalMarketing/comments/1hy7uwb/do_you_really_believe_that_people_want_better/", "upvotes": 1, "comments_count": 22, "sentiment": "neutral", "engagement_score": 45.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hyc3oc", "title": "Is it true that the field of analytics is over saturated? If so, what are other options or roles for one’s interested in analytics?", "content": "\nIn one of my previous posts someone commented that analytics is over-saturated. If that’s the case, what are other roles someone who is interested in analytics can look into ? I’m an MIS major at my undergrad college and my coding skills or skills necessary for analytics are below the bar for a tech/analytics role and I was wondering if analytics is actually over-saturated what are other roles I can look into ?", "author": "deleted", "created_time": "2025-01-10T19:05:47", "url": "https://reddit.com/r/analytics/comments/1hyc3oc/is_it_true_that_the_field_of_analytics_is_over/", "upvotes": 3, "comments_count": 51, "sentiment": "neutral", "engagement_score": 105.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hyejik", "title": "14 years ago, <PERSON> (now Google's Chief AGI Scientist) predicted AGI in 2028, which he still believes. He also estimated a 5-50% chance of human extinction one year later.", "content": "", "author": "MetaKnowing", "created_time": "2025-01-10T20:47:41", "url": "https://reddit.com/r/artificial/comments/1hyejik/14_years_ago_shane_legg_now_googles_chief_agi/", "upvotes": 62, "comments_count": 93, "sentiment": "neutral", "engagement_score": 248.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hygyj4", "title": "I’m an idiot. Rolled my 401(k)s into my new employers plan and regretting my decision.", "content": "So, as the post heading says, I am an idiot and do not know enough about personal finance. The least I could’ve done was a cursory search of Google or Reddit before I made this decision but alas, here we are.\n\nI had multiple 401(k)s out there from past employers and decided it would be nice to have them all in one place. The old 401(k)s were at Principal and Fidelity and my new company uses Empower. I spoke to someone at empower who told me what a great idea it would be to roll everything to them, but never mentioned that I should check into asset management fees. Today I checked my retirement balance and saw that I will be getting charged about $200 a year in fees at Empower. Not crazy, but I looked at statements from my old 401(k)’s and I don’t see any asset management fees. Not sure how that’s possible, but maybe the companies were paying them even after I left. Also, now that I’ve done that cursory Google search, it seems like Fidelity just has lower fees overall.\n\nAnyways, I talked to a retirement advisor at empower today and she apologized, saying that they should have gone over that and she would’ve told me if I was thinking of rolling over that I should look into fees. Yeah thanks but too late. \n\nWishing I could reverse the transactions but assuming that’s not possible. I do not have an IRA and I’m not sure if that’s something I should look in to. Maybe these fees aren’t actually very high at all, but it seems like they’re taking my money for no reason since it’s in a target date fund.\n\nSome context,  I am almost 40, high earner, target date fund 2045. \n\nThanks in advance for your advice, and any roasting I receive because internet.\n\n\nEdit-Update: First of all, thank you to most of you for the great advice regarding this issue. I took all of your advice in, and took the time to educate myself more about this issue. For those that felt the need to troll and make snarky comments, next time maybe you can just scroll past. No one wants to hear you. And for those wondering why I was ‘wasting’ all this time over $200, it actually ended up being a lot more than that, and really it was the want/need to understand the situation better and educate myself about financial issues that will affect me the rest of my life. So well worth it. Perhaps instead of taking the time to be awful humans from behind your computer in your dark sad room, you should also try this. \n\nAlso wanted to post this update as a thanks to the people that did help and for those saying that they were interested in following this post so they wouldn’t make the same mistake. Hopefully I have summed up all the great comments here.\n\nWhat I found is that there was indeed a stark difference between the gross expense ratio from Fidelity and the gross investment expenses at empower. While these things are named differently at different institutions, they are the same. They are not something you will see in your 401k transaction history, but instead built into the share price of the plan. They are basically the cost to manage the plan and all institutions have them. You can find them in your plan description, plan summary, or fee comparison documents in your account (I say either because often the ‘fee schedule’ on one document will reference another document entirely). \nIn addition to this, empower charges a general administrative services fee, which is .22% in my case. This is something that I was not charged by any previous employer. Empower says that this is an additional expense that is sometimes wholly paid by employers, but sometimes (all or partially) passed on to employees. This is what was showing in my transaction history and the $46 I referred to in the original post. In my case, it looks like all of the fee is passed on from my employer to its employees. This was not the case at any previous employer (they paid the entire fee) so that kind of sucks.\n\nSo all told, basically I am paying four times the amount in management and administrative fees to have my money at empower than I was at Fidelity (0.58% vs 0.14%, respectively). I haven’t looked into principal because the majority of the rollover money came from Fidelity and that’s what I’m focusing on here. \n\nI also appreciate the comment from the person about the DOL2022–02 rule. I looked into that and indeed these companies are supposed to do a fee comparison or at least tell you to compare fees before looking into rolling over your money from one account to another. I won’t go into too much detail, but when I brought this up to empower, they got pretty nasty. Which caused me to file a complaint with them. Not sure how much good that will do, but I am currently looking into IRAs to roll my rollover money into that have lower expense ratios. I am, however, trying to get more information about the rule of 55 and if that applies to rollover money at empower, which should be part of the plan description, but I can’t find it and neither can they. That might also factor into my decision to open an IRA and roll this money there. \n\nHopefully this helps someone else with rollovers, thanks for the feedback from some saying that this was helpful. Don’t make the mistake that I did and wait to educate yourself about something until after you do it. And the big Takeaway is to look into all these fees before you make any moves. I’m still an idiot because I didn’t even know these existed. But I’m less of an idiot now, because of the fine people of Reddit, and various financial websites. Happy New Year to all.\n", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-10T22:33:01", "url": "https://reddit.com/r/personalfinance/comments/1hygyj4/im_an_idiot_rolled_my_401ks_into_my_new_employers/", "upvotes": 858, "comments_count": 644, "sentiment": "neutral", "engagement_score": 2146.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}]