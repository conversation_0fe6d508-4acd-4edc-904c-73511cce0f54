[{"platform": "reddit", "post_id": "reddit_1hnt0zs", "title": "dan bi<PERSON><PERSON><PERSON> looks busy - found a sneaky detail in <PERSON>'s coaching program booking system", "content": "I was checking out <PERSON>'s new coaching program where you can book a call with his team after watching some mandatory videos. Being curious, I inspected the network requests during the booking process and found something interesting:\n\nThe appointment booking URL has a parameter `enableLookBusy=true`\n\n[`https://backend.leadconnectorhq.com/appengine/appointment/free-slots?calendar_id=Yg9YjuQEl0j5UkKg3Ar3&startDate=1748728800000&endDate=1751320799999&timezone=Europe/Amsterdam&sendSeatsPerSlot=false&enableLookBusy=true`](https://backend.leadconnectorhq.com/appengine/appointment/free-slots?calendar_id=Yg9YjuQEl0j5UkKg3Ar3&startDate=1748728800000&endDate=1751320799999&timezone=Europe/Amsterdam&sendSeatsPerSlot=false&enableLookBusy=true)\n\nWhat's more telling is that while the next available slot shows as being months away, you can actually book for the next day by manipulating the request. They're artificially creating scarcity.\n\nI get why businesses use tactics like this - it creates FOMO and makes the service seem more exclusive. But it feels pretty dishonest to programmatically fake being booked up, especially for a coaching program that's supposed to be about authenticity and success.\n\nJust thought I'd share this little discovery. Pretty clever from a marketing perspective, but definitely on the shadier side of business practices.\n\nWhat do you all think about these kinds of artificial scarcity tactics?", "author": "enjoythements", "created_time": "2024-12-28T00:13:12", "url": "https://reddit.com/r/marketing/comments/1hnt0zs/dan_bilzerian_looks_busy_found_a_sneaky_detail_in/", "upvotes": 213, "comments_count": 47, "sentiment": "neutral", "engagement_score": 307.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnv66l", "title": "Google maps timeline deleted", "content": "Hi! Just found out my whole Google maps timeline was completely deleted and i was actually warned about it several times, but i didnt see the emails. I dont know why but im truly sad about it! I travelled a lot this year and i used to check my routes often to remember where ive been. Is there any way to recover or access the data again? I really want to get that info back, it made me pretty sad, i had a lot of memories over there. Thank u!", "author": "Thin_Lingonberry_810", "created_time": "2024-12-28T02:01:50", "url": "https://reddit.com/r/GoogleMaps/comments/1hnv66l/google_maps_timeline_deleted/", "upvotes": 5, "comments_count": 15, "sentiment": "neutral", "engagement_score": 35.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hnyik9", "title": "Sold my Business Yesterday.... Crazy feeling. ", "content": "I owned a very large tire and automotive repair shop. I am 3rd generation, knew from a young age that is what I wanted to do. I started running the business 16 years ago, and purchased it from my parents 8 years ago. I've worked there since I was 12, so 31 years.  I made a huge push. Pushed my guys hard, but compensated them better then anyone else could. Customer Service was 100% the focus. I wanted the Customers to be happy 100% of the time. Fix their problem, honestly, in a timely fashion but get paid well for it. \n\nIt worked. I was approached by a big company 3 months ago. They wanted me. I got what I needed. Now, Im sitting here at 43 years old wondering what next week is going to bring. I know I have freedom, time and no customer or employee stress. Today was day 1. I made breakfast for my family, cleaned the garage, spent two hours at the gym, then got a massage. Pretty nice day. \n\nWhen I woke up at 7am this morning, I was shocked. Normally, I would have already been at the shop for an hour at that time. I only checked the cameras 11 times today to see how my guys were doing. \n\nIts worth it. Push hard, then get out when the time is right. I think I timed it perfectly. Now, the fun begins. ", "author": "HookedUp_77", "created_time": "2024-12-28T05:07:33", "url": "https://reddit.com/r/smallbusiness/comments/1hnyik9/sold_my_business_yesterday_crazy_feeling/", "upvotes": 1451, "comments_count": 288, "sentiment": "neutral", "engagement_score": 2027.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ho0c63", "title": "Pixel 9 pro xl creaking.", "content": "I had my first pixel 9 pro xl replaced and now the second one they sent me is creaking as well...\n\nGreat quality Google. Guess I'll have to start a claim on this one too.", "author": "QuotableRaven", "created_time": "2024-12-28T07:04:30", "url": "https://reddit.com/r/GooglePixel/comments/1ho0c63/pixel_9_pro_xl_creaking/", "upvotes": 1, "comments_count": 11, "sentiment": "neutral", "engagement_score": 23.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ho1pqa", "title": "Tech\nGoogle CEO <PERSON><PERSON><PERSON> tells employees to gear up for big 2025: ‘The stakes are high’", "content": "[https://www.cnbc.com/2024/12/27/google-ceo-pichai-tells-employees-the-stakes-are-high-for-2025.html](https://www.cnbc.com/2024/12/27/google-ceo-pichai-tells-employees-the-stakes-are-high-for-2025.html)\n\nTPU is so back. AGI is coming", "author": "Conscious-Jacket5929", "created_time": "2024-12-28T08:45:45", "url": "https://reddit.com/r/singularity/comments/1ho1pqa/tech_google_ceo_p<PERSON><PERSON>_tells_employees_to_gear_up/", "upvotes": 574, "comments_count": 246, "sentiment": "neutral", "engagement_score": 1066.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ho2tf8", "title": "Dental Marketing (Invisalign) - Health in Personalized Advertising Policy Issue.", "content": "\"Hello folks, I have been running an Invisalign search campaign for a while, and I already have Invisalign-related keywords in my ad groups. Now I would like to add other Invisalign keywords, but Google does not allow me to do so.\n\nFor example, I have \\[invisalign\\] exact match keywords in my list, but it doesn't allow me to add the \\[invisalign near me\\] keyword because of restrictions related to health in personalized advertising. It asks me either to fix the error or request an exception.\n\nPlease help me understand how to resolve this issue.\"", "author": "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-28T10:10:23", "url": "https://reddit.com/r/adwords/comments/1ho2tf8/dental_marketing_invisalign_health_in/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ho7o8p", "title": "[D] Review of Imperial College London's Professional Certificate in AIML (25 weeks) course", "content": "TL;DR:\n\nA deep dive into foundational to advanced topics like Python, statistics, neural networks, and reinforcement learning, with a hands-on capstone project simulating a real-world ML competition. Tons of content: videos, quizzes, Jupyter Notebook assignments; real-world projects and discussions. 20–30 hours/week (falling behind is not an option). Brush up on your Python if you’re not fluent. Non-GenAI. If you're ready to commit and supplement your learning with extra resources, it's an intense but rewarding experience for £4000. AMA if you're curious!\n\nLonger version: :)\n\nI recently completed the Professional Certificate in Machine Learning and Artificial Intelligence, a 25-week, very intense, deep dive into AI/ML fundamentals and advanced topics. It's a comprehensive program offered by Imperial College London -  while it was incredibly rewarding - it is not for the faint of heart. \n\nHere's a breakdown of my experience:\n\n# Programme:\n\nThe course is split into three parts with a total of 25 modules:\n\n* Foundations of ML and AI – Covers Python basics, statistics, and foundational ML concepts.\n* Methods of ML and AI – Practical machine learning methods and real-world applications.\n* Deep Learning and Neural Networks – Advanced topics like neural networks, reinforcement learning, and hyperparameter tuning.\n\nThe curriculum is loaded and packed! Trust me - sometimes you’d wonder when do you get your weekends back!\n\nIt starts with a Python refresher and moves into topics like probability, decision trees, support vector machines, clustering, and more. The final capstone project simulates a real-world ML competition - which was an awesome way to apply everything I’d learned.\n\n# What I Loved:\n\n* Tons of content, from videos and quizzes to Jupyter Notebook assignments and discussions. You’ll definitely learn a lot!\n* The practical activities and capstone project make sure you’re not just passively learning but applying concepts.\n* You get to engage with like-minded peers and build a network. Our cohort even formed a WhatsApp group to share ideas and tackle challenges together.\n\n# Challenges to Keep in Mind:\n\n* This program is intense - unless you have 20–30 hours a week, don’t commit to it. Falling behind by even a week can make it tough to catch up - so consistency is key\n* Python is the backbone of the course, so if you’re not already comfortable with it, be prepared to invest extra time in learning. The refresher module helps, but you’ll likely need additional resources for ML-specific libraries\n* If you’re hoping to learn about GPT models, diffusion techniques, or operationalizing ML workflows, this program doesn’t cover those areas.\n* Most content is delivered through videos and code exercises. There are bi-weekly TA sessions, but they often fall during work hours, which can be tough to attend.\n\n# Tips:\n\n* Don’t let yourself fall behind; the workload piles up quickly.\n* Use YouTube, books, or online resources to clarify tough topics.\n* Participate in discussions and consider forming a study group (our WhatsApp group was a lifesaver!).\n* Be realistic about the time you’ll need to dedicate each week.\n\n# Fee:\n\nAt around £4,000, this program is an investment - but but but it’s worth it if you’re serious about building a strong foundation in AI/ML. Checkout their referral program - you’ll get close to £500 off if someone you referred joins and stays for stipulated period (could of weeks I think)\n\n# Certificate:\n\nThe certificate from Imperial College London is prestigious - the skills you gain will set you up to tackle real-world problems.\n\n# Commitment:\n\nThis program requires significant weekly commitment, and it’s essential not to let yourself fall behind. Missing even a week or two can create a backlog that’s difficult to catch up on due to the extensive material and fast pace.\n\nNot all the material presented will be easy to grasp on the first attempt. Be prepared to dive into additional resources like YouTube videos, books, or online articles to reinforce your understanding of complex topics.\n\nActively participate in the discussions facilitated during the program. Interacting with fellow participants can provide new perspectives and help clarify doubts.\n\nConsider forming or joining a group, such as a WhatsApp group, to exchange ideas, suggestions, and resources. Collaboration with peers can make the challenging parts of the program more manageable and enriching.\n\n**Job Prospects:**\n\n\\- While this program gives excellent exposure to AI/ML concepts, it might not directly land you an AI/ML Engineer role right away. However, it’s a great complement to your existing work - one that enhances your ability to integrate AI/ML into your current projects.\n\n\\- For those aiming to become Junior AI/ML Engineers - unfortunately the chances are slim without prior experience or additional hands-on work. Consider using the skills gained to build a strong portfolio.\n\n\\- For software engineers - this program is highly informative but may fall short when it comes to the MLOps or Data Engineering (DE) perspective. While there’s some content on data cleansing, it doesn’t delve deeply into essential skills like data migration, enrichment, or creating scalable ML pipelines.\n\n\\- Similarly, the program doesn’t cover deploying models or monitoring their performance or integrating ML workflows into production systems (which are key components of MLOps). \n\nIf your focus is on operationalising machine learning systems or managing data pipelines, you’ll likely need to seek additional specialised training or resources to bridge these gaps.\n\nHowever, it does provide a solid understanding of machine learning fundamentals, which can complement MLOps or DE learning if you plan to expand into those areas later.\n\n**Summary**\n\nIf you’re ready to commit the time and effort (and of course the money!) - this course is a fantastic way to dive deep into the world of AI/ML. \n\nJust make sure you’re prepared for the workload and ready to supplement your learning where needed. It’s intense, but absolutely worth it.\n\nYes, there are a plethora of materials and resources online - I do think three reasons this course might stand out: professional certificate from Imperial College London, Programme faculty and of course networking!\n\nHave questions about the program? Feel free to ask! 😊", "author": "<PERSON><PERSON><PERSON><PERSON>_Bluejay_881", "created_time": "2024-12-28T15:17:01", "url": "https://reddit.com/r/MachineLearning/comments/1ho7o8p/d_review_of_imperial_college_londons_professional/", "upvotes": 12, "comments_count": 16, "sentiment": "bearish", "engagement_score": 44.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hoa4y5", "title": "Google completely broke Now Playing ", "content": "Now Playing was probably my favourite Pixel feature. Since November (or A15? not sure.), it barely ever detects that music is playing, even next to a speaker or in a car.\n\nThe rare times it does, the offline search of course rarely works (5% of the time), even on songs it used to recognise.\n\nSo I press the manual (online) search button. Every single time i get \"No song found\".\n\nSo I open YouTube Music, use the sound search and within 2 seconds (no exaggeration) it finds it. Meaning the mics work fine.\n\nEdit: it seems Google was aware of the issue during beta, thought they fixed it, but failed. [**https://issuetracker.google.com/issues/354094738**](https://issuetracker.google.com/issues/354094738)\n\nEdit 2: interstingly, in this situation, using the sound search from Circle to Search or the Google app also fails instantly. So **this points to a Google app bug**. Either way, Google should just ditch it and use YTM's lighting fast implementation.\n\n>TLDR:\n\n>**To** u/pixelcommunity **: talk to the YTM team. They know how to do it.**\n\n>**To users: use Y<PERSON> if your Now Playing is broken too. +1 the issue here:** [**https://issuetracker.google.com/issues/354094738**](https://issuetracker.google.com/issues/354094738)\n\nEdit 3: they fixed in the March update (o a bit before)", "author": "TheTomatoes2", "created_time": "2024-12-28T17:11:57", "url": "https://reddit.com/r/GooglePixel/comments/1hoa4y5/google_completely_broke_now_playing/", "upvotes": 527, "comments_count": 164, "sentiment": "neutral", "engagement_score": 855.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hoed57", "title": "Are Dividends Worth it for Young People or Should you just buy strong stocks w/o Dividends? ", "content": "Being new/young in the investing world I wondered. Are dividends even worth it for young people, for me in school it seems not worth it. As I begin my job within the next years would it be worth it to start dividends early on? Do you guys recommend buying any stock, non dividend related. What are your top 10 stocks DIVIDEND OR NON DIVIDEND. This chat has decent responses and I am looking for your top recommended stocks based on your info/portfolio. Here’s my thinking:\n- Google, Microsoft, AMZN, Costco, Meta (long term growth to buy now/within the next month) \n- NVIDIA and TSLA for next 3-5 years with AI and Tech growth but idk bout long term. I’m not big on Apple. \n- Palantir and Sofi (riskier but long term good plays?)\n\nPlease let me know your information and suggestions based on your portfolio/experiences. Thank you all!", "author": "Specific_Leather_82", "created_time": "2024-12-28T20:22:26", "url": "https://reddit.com/r/dividends/comments/1hoed57/are_dividends_worth_it_for_young_people_or_should/", "upvotes": 27, "comments_count": 86, "sentiment": "bullish", "engagement_score": 199.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1hofmqg", "title": "Penny stock Mania Watchlist for December 30th (10x potential)", "content": "I want to begin by noting that the market is currently experiencing intense euphoria, with momentum stocks consistently posting increases ranging from 10 to 80 times their yearly lows on a daily basis (including Quantum penny stocks, which do not even possess functioning models). These selections are influenced by past instances of rapid growth and carry a high-risk, high-reward potential.\n\n**$RVSN** RVSN appears to be gearing up for a significant surge this January. A major driver will be the upcoming release of its H2 2024 financial results in early January 2025. The surge in trading volume indicates strong investor interest in RVSN, and it wouldn’t be surprising if institutional investors are also joining the fray. I am placing my bets on RVSN alongside them.\n\nLast year, RVSN saw a remarkable increase from $1.37 on January 19 to $11.84 by February 2. Given the substantial volume incoming, we're likely to witness another breathtaking ascent in the coming week, particularly in light of the recent regulatory approval RVSN has secured.\n\n**$HOLO** (Significantly high-risk, high-reward opportunity)  \nOver the past four years, Holo has experienced several surges of 10 to 50 times its value. Given its historical performance, I believe it possesses incredible potential for dramatic fluctuations, both up and down, and it might still be in the early stages of its next uptrend. Recently, it has gained considerable attention on StockTwits, where it was ranked as the most-watched stock this Friday, indicating the possibility of an extraordinary weekly rally ahead.\n\n**$KULR** KULR Technology Group ($KULR) has made headlines with its recent foray into cryptocurrency, purchasing 217.18 Bitcoin for around $21 million. This acquisition, executed at an average price of $96,556.53 per Bitcoin, follows the company’s newly unveiled Bitcoin Treasury strategy and marks a significant transformation in its capital management approach.\n\nThis strategy by KULR might work out but it will be tied closely with Bitcoin and seeing that it has already ran up over 15x in the past 3 months I don't know how much more gains is left. \n\nMassive potential for a crazy week for penny stocks next week. \n\nUpdate to my post : $HOLO has just announced as of December 30th: MicroCloud Hologram Inc. Develops Semiconductor Quantum Dot Hole Spin Qubit Technology, Advancing the Frontiers of Quantum Computing", "author": "<PERSON>_boi1", "created_time": "2024-12-28T21:20:32", "url": "https://reddit.com/r/pennystocks/comments/1hofmqg/penny_stock_mania_watchlist_for_december_30th_10x/", "upvotes": 287, "comments_count": 191, "sentiment": "bullish", "engagement_score": 669.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "GOOGL"}]