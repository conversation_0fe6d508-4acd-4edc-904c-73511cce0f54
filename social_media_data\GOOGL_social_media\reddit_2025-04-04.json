[{"platform": "reddit", "post_id": "reddit_1jr73gy", "title": "Meta AI has upto ten times the carbon footprint of a google search", "content": "Just wondered how peeps feel about this statistic. Do we have a duty to boycott for the sake of the planet?", "author": "intensivetreats", "created_time": "2025-04-04T08:36:03", "url": "https://reddit.com/r/artificial/comments/1jr73gy/meta_ai_has_upto_ten_times_the_carbon_footprint/", "upvotes": 61, "comments_count": 73, "sentiment": "neutral", "engagement_score": 207.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jrabxn", "title": "Google AI falls for <PERSON>' prank story, presents it as real news", "content": "", "author": "AdSpecialist6598", "created_time": "2025-04-04T12:11:29", "url": "https://reddit.com/r/technews/comments/1jrabxn/google_ai_falls_for_april_fools_prank_story/", "upvotes": 1374, "comments_count": 48, "sentiment": "bearish", "engagement_score": 1470.0, "source_subreddit": "technews", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jrbomd", "title": "Are we seeing the beginning of the end of traditional university education?", "content": "With the unstoppable advance of artificial intelligence, online courses, specialized certifications and self-education, it seems increasingly obvious that the traditional university model is becoming obsolete.\n\nToday, a person can learn programming, design, marketing, languages ​​or even biotechnology from home, for free or for less than the cost of a university semester. Platforms like Coursera, edX, Khan Academy, Udemy, and even YouTube are training the next generation of professionals without the need for classrooms or tuition.\n\nAdd to this that many technology companies are starting to ignore college degrees and focus more on practical skills and portfolios.\n\nSo I wonder:\nAre we really just decades away from abandoning the traditional university system as we know it? Or do you think it will always have a dominant place?\n\nI'm especially interested in how you think this will affect developing countries, where access to quality education is limited but the internet is becoming more accessible.", "author": "<PERSON><PERSON><PERSON>_martin", "created_time": "2025-04-04T13:19:13", "url": "https://reddit.com/r/Futurology/comments/1jrbomd/are_we_seeing_the_beginning_of_the_end_of/", "upvotes": 0, "comments_count": 161, "sentiment": "neutral", "engagement_score": 322.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jrcg2x", "title": "Is there a way to disable Google's automatic translations on Wikipedia? (Chrome on PC and mobile)", "content": "Most of the time, whenever i manually access an english Wikipedia article, Google automatically translates it into spanish. How can i disable this feature permanently and view articles in their original language without Google automatically translating them?", "author": "alex<PERSON><PERSON><PERSON>", "created_time": "2025-04-04T13:54:23", "url": "https://reddit.com/r/chrome/comments/1jrcg2x/is_there_a_way_to_disable_googles_automatic/", "upvotes": 28, "comments_count": 63, "sentiment": "neutral", "engagement_score": 154.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jre1i2", "title": "Why is google maps so damn stupid", "content": "Is it just me, am I doing something wrong?  I search a business in google. There with the business listing is a little map of the business location. I tap that little map with the reasonable expectation that it will take me to a map with directions from my current location. But what actually happens is I get asked if I want to go to Google maps or stay in the web. If I make the horrific decision, or accidentally click on going to the app I am taken to Google maps that shows my location and nothing about the business or location I want to go to. I’m supposed to have remembered the business address and reenter it. Google is one of the largest most advanced tech giants in the world and they can’t create an app that remembers an address???? WTHELF???? it just pisses me off. THEN if I try to go back to the web, it automatically forces me back to the app in an endless mindfuck loop. I have to exit Google entirely, I’ve even had to shut my phone off to get out of that nightmare. Is the re a way for me to get the hell away from Google. (Btw WTHELF= what the hell ever lovjng fuck?)", "author": "bradley524", "created_time": "2025-04-04T15:02:22", "url": "https://reddit.com/r/GoogleMaps/comments/1jre1i2/why_is_google_maps_so_damn_stupid/", "upvotes": 1, "comments_count": 7, "sentiment": "neutral", "engagement_score": 15.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jrgr2n", "title": "How I got rid of ad fatigue for a brand and everything I learned about Meta Ads doing so", "content": "For context, I’m a freelance marketer on sites like upwork and have 4+ clients. This specific client is an ecommerce brand that has been around and doing 6 figs in revenue per year, nothing crazy.\n\nThey also had a bunch of ads they had designed and paid a designer a tonnn of money that delivered less than 0.8x return on ad spend.\nI know from another campaign I'm doing for a different brand, the “fake” instagram story and DM creatives work really well.\n\nLong story short, I found some canva templates for these and honestly they’re killer. The first ad is already doing a 2.5X roas. I’m using a google search template and putting in the product, a comparison template, and an instagram DM template I found on magicflow.app\n\nThe 3 things I learned:\n\n1. I use Advantage+ and don’t even worry about campaign structure - it’s incredible and anyone saying you need a complicated funnel doesn’t know what they’re doing. Meta’s AI is so incredibly powerful now\n\n2. Video creatives + familiar ad creative formats (notes app, instagram dm, text search, fake google search) are still a good combo together\n\n3. “Good” expensive design work doesn’t mean it’ll convert. In fact, I think it has the opposite effect looking at all the brands I’m managing. \n\nYou need good formats that are easy to understand, not complex designs. So far, the airdrop template of a product, the text message, and instagram DM templates have been working fairly well.\n\nTLDR; I’m testing a bunch of creatives in 2 Advantage Shop Campaigns and I’m saving a sh**T ton of time finding a winner instead of trying to design from scratch. Currently testing a minimum of 5-10+ creatives per week.", "author": "mariben9", "created_time": "2025-04-04T16:55:10", "url": "https://reddit.com/r/DigitalMarketing/comments/1jrgr2n/how_i_got_rid_of_ad_fatigue_for_a_brand_and/", "upvotes": 83, "comments_count": 15, "sentiment": "bearish", "engagement_score": 113.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jrkyl4", "title": "Inverter Failure, Installers saying there is nothing they can do", "content": "Inverter is showing an arc fault\n\nWe're a former client of SolAire/Sunpower\n\n9.5 year old system, installer is no longer in business and the installer they're referring us to says they \"don't make this model anymore\"\n\nThe model number of the inverter is sb 6000tl \n\nWhen I Google it I get \"**SMA Sunny Boy 6000TL-US-22**\"\n\nIs there not another unit that has the same specifications as this one that I can just drop in where the old one was?", "author": "thestuff<PERSON><PERSON><PERSON>", "created_time": "2025-04-04T19:52:24", "url": "https://reddit.com/r/solar/comments/1jrkyl4/inverter_failure_installers_saying_there_is/", "upvotes": 18, "comments_count": 51, "sentiment": "neutral", "engagement_score": 120.0, "source_subreddit": "solar", "hashtags": null, "ticker": "GOOGL"}]