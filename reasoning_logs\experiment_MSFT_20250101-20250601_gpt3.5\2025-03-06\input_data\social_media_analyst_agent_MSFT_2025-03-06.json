{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-03-06", "api_calls": {"load_local_social_media_data": {"data": [{"title": "MS licensing questions for the legacy program, and some partner doubts!", "content": "Guys, my buddy at an smb has some questions about MS licensing.\n\nI did my reading and digging through MS portals to give him some ideas, then I thought you guys would love this topic since everyone loves to talk about MS licensing here so much .. ./s\n\nPlease could yall help me out a lil to make sense of these questions for us and share some \"behind the scenes\" info not mentioned in kb articles about this MS licensing stuff? Also some gotcha moments with MS licensing from your experience?\n\n**He's not really a redditor so here I am posting for him:**\n\n1. The Microsoft Legacy program is ending soon and with it some included and free Microsoft licenses to server and desktop Microsoft applications. What cost effective options are out there for a current Microsoft Legacy program partner to get the similar level of free server and desktop Microsoft licenses without the ability to get a lot of new certifications and sales\n2. If you have Microsoft Server Datacenter 2022 installed, and then you create 5 virtual server environments running Microsoft Server 2022 Standard, then do you need to purchases licenses for the virtual server environments if they are running Microsoft Server 2022 or 2019 Standard?\n3. Can you buy all three Partner Launch, Partner Success Core, and Partner Success Expanded for a single partner global account? Can you confirm that this would effectively allow the partner to have 24 Microsoft Visual Studio Professional Subscription as an example. Can you confirm by sending a link that shows this is possible\n4. Confirm that, with the Partner Success packages, the licensing allows us to use any older version of the software?\n5. Confirm that the Partner Success packages include server software?\n\n  \n**UPDATE:**\n\n  \nAfter a bit of digging we have this info so far, could anyone take a look and confirm this?\n\n**1. The Microsoft Legacy program is ending soon and with it some included and free Microsoft licenses to server and desktop Microsoft applications. What cost effective options are out there for a current Microsoft Legacy program partner to get the similar level of free server and desktop Microsoft licenses without the ability to get a lot of new certifications and sales.**\n\n* We can't find any docs which refers to this \"legacy program\" is this referring to \"Microsoft Action Pack subscription\"? This just stopped in Jan 2025.\n* So the only way is to move to one of the launch, success core, or success expanded packages?\n\n  \n**2. If you have Microsoft Server Datacenter 2022 installed, and then you create 5 virtual server environments running Microsoft Server 2022 Standard, then do you need to purchases licenses for the virtual server environments if they are running Microsoft Server 2022 or 2019 Standard?**\n\n* Licensing is based on the host not on the VMs if you have DC 2022 license you can run unlimited VMs on it the min requirements are you need to license all 16 cores per server even if you have fewer cores?\n\n**3. Can you buy all three Partner Launch, Partner Success Core, and Partner Success Expanded for a single partner global account? Can you confirm that this would effectively allow the partner to have 24 Microsoft Visual Studio Professional Subscription as an example. Can you confirm by sending a link that shows this is possible?**\n\n* Yes we can buy all 3 launch, success core, success expanded packages but **ONLY 1 OF EACH INDIVIDUALLY** \\- so we cannot buy 2 of launch/SC/SE - I can't find any doc which confirms that yes we can buy all 3 but the part about buying only 1 of each is well documented in the [FAQ on every partner benefit page](https://learn.microsoft.com/en-us/partner-center/membership/partner-launch-benefits#can-i-buy-more-than-one-partner-launch-benefits).\n* And ones we buy all 3 can we use all 24 of the visual studio professional licenses? We can't find any info which confirms if we can merge benefits like these, is this against TOS?\n\n**4. Confirm that, with the Partner Success packages, the licensing allows us to use any older version of the software?**\n\n* The answer is we can only use the version of the software until the license expires and then we are expected to upgrade, but then once we upgrade do we again buy licenses?\n* Found this in the \"[terms of participation](https://assetsprod.microsoft.com/mpn-maps-product-usage-guide.pdf)\" pdf:\n   * Each partner is responsible for tracking their own consumption and entitlement of product benefits. If a partner organization is selected for a compliance audit, that organization is responsible for presenting records regarding the active program licenses used and compliance with the terms of use. **Licenses do not provide downgrade rights or any other Software Assurance services.** For deployment, management, and other similar services, learn more about Software Assurance.\n* What type of software does this even apply to? SCCM? SCEP?\n\n**5. Confirm that the Partner Success packages include server software?**\n\n* Yes it includes server management tools like:\n   * Windows 365 Enterprise – 8 vCPU, 32 GB RAM, 512 GB Storage\n   * System Center Client Management Suite (2022)\n   * System Center Endpoint Protection (2019)\n   * Windows Server CALs (not edition-specific)\n   * Windows Server Datacenter – Per core (2022)\n   * Windows Server Standard – Per core (2022)", "created_time": "2025-03-06T01:01:18", "platform": "reddit", "sentiment": "bullish", "engagement_score": 53.0, "upvotes": 47, "num_comments": 0, "subreddit": "unknown", "author": "masterofrants", "url": "https://reddit.com/r/microsoft/comments/1j4jcbn/ms_licensing_questions_for_the_legacy_program_and/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "How Can I Get Involved in Volunteer Opportunities with Microsoft?", "content": "Hello, Microsoft community!\n\nI’m interested in gaining experience by volunteering in roles related to customer service, moderation, or support for Microsoft products and services. I’m eager to help out and contribute my time and skills without the expectation of payment—just looking to gain hands-on experience.\n\nAre there any volunteer opportunities with Microsoft, or ways to get involved in these areas remotely? Any guidance or suggestions would be greatly appreciated!\n\nThank you in advance!", "created_time": "2025-03-06T01:31:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "IdeaSprout22", "url": "https://reddit.com/r/microsoft/comments/1j4jyv2/how_can_i_get_involved_in_volunteer_opportunities/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "AA Round or not?", "content": "Hi recently i went through an interview loop. At the final round it was a super senior person with 25 years of experience in microsoft (not sure his title). Is this the as appropriate round? The interview was quite nice and relaxed, basically a try to get to know me, whats your learning process, questions about my internship, any client facing roles you have etc Thank you everyone!!!!!  ", "created_time": "2025-03-06T02:24:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 26.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1j4l1j7/aa_round_or_not/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Microsoft 365 Family without AI for $99.99 a year is still available", "content": "\\[This is for US subscriptions\\] \n\nI forgot I had OneDrive full of data and had to go back and signup for another month of Microsoft 365 Family at $12.99. I had canceled my subscription two weeks ago. I renewed for a month, went back to cancel the rebill and it offered M365 Family with AI for $99.99/year. Altogether it billed me another $107.49.\n\n  \nI went back to cancel that rebill to avoid paying $129.99/yr + taxes next year. Clicked 'Cancel Subscription' and when the page loaded it offered to switch to monthly at $12.99 but right below, highlighted in bright yellow, was the 'LOWER COST WITHOUT AI' option. M365 Family Classic $99.99/year for 6 people, and a link to 'Buy at $9.99/month'\n\nElsewhere someone said switching like that will tack a year of M365 Family, so in March 2026 I would get billed $99.99+taxes for service without AI until March 2027.", "created_time": "2025-03-06T21:26:07", "platform": "reddit", "sentiment": "bullish", "engagement_score": 40.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "brozelam", "url": "https://reddit.com/r/microsoft/comments/1j56ld1/microsoft_365_family_without_ai_for_9999_a_year/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Xbox update resetting consoles to factory settings, Insiders say", "content": "", "created_time": "2025-03-06T21:45:52", "platform": "reddit", "sentiment": "neutral", "engagement_score": 81.0, "upvotes": 59, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j5725h/xbox_update_resetting_consoles_to_factory/", "ticker": "MSFT", "date": "2025-03-06"}, {"title": "Audit tool against Best Practices", "content": "Hi All,\n\nI’m currently working for a MSP that’s looking after a few clients that consume a whole host of Microsoft 365 products and services, such as: exchange online, m365 apps, intune, teams, SharePoint, OneDrive, entra, security etc. \n\nI was wondering if there’s a tool out there - whether it’s 1 tool or a few tools combined, that can provide me with a host of recommendations to update/check against a tenant? \n\nThis would be ideal if it also generated a report that had all the good/bad/ugly in the tenant. \n\nI am thinking along the lines of ORCA but more feature and service rich.\n\nKeen to hear how you guys do it. \n\nThanks. ", "created_time": "2025-03-05T03:39:19", "platform": "reddit", "sentiment": "bullish", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "fungusfromamongus", "url": "https://reddit.com/r/microsoft/comments/1j3u0y6/audit_tool_against_best_practices/", "ticker": "MSFT", "date": "2025-03-05"}, {"title": "Who is responsible for the Hiring Decision?", "content": "Hi everybo<PERSON>, I went to interview loop with Microsoft for Technology Specialist Intern, went for two interviews back to back, I think feedback was great. I think the last interview the person was significantly more senior. But who actually makes the hiring decision, is it <PERSON><PERSON> or the last guy in the interview loop? Thank you everyone", "created_time": "2025-03-05T03:41:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 32.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NewAppointment2190", "url": "https://reddit.com/r/microsoft/comments/1j3u2en/who_is_responsible_for_the_hiring_decision/", "ticker": "MSFT", "date": "2025-03-05"}, {"title": "Microsoft 365 is so bad", "content": "Shifted to a new company that uses Microsoft 365.\nEverything in the suite is a hot pile of garbage.\n\n- Teams doesn’t send mobile notifications\n- outlook is extremely unintuitive and search sucks\n- I can’t manage separate mails according to custom tags, need to follow the stupid coloring system. If there is a way it’s hidden somewhere and couldn’t find out where even after googling it\n- one note is horrible, almost never pastes images on one attempt, syncing always has issues.\n- SharePoint is its own UI made for people who hate finding files.\n- copilot is absolutely useless, unable to hold context\n- calendar feels like a Nokia era calendar, doesn’t seem to integrate with personal accounts or other providers\n\nBasically everything sucks and yet they keep shipping new features without addressing any of the complaints in the existing software.", "created_time": "2025-03-05T21:57:09", "platform": "reddit", "sentiment": "neutral", "engagement_score": 36.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "No-Equipment5090", "url": "https://reddit.com/r/microsoft/comments/1j4f8kl/microsoft_365_is_so_bad/", "ticker": "MSFT", "date": "2025-03-05"}, {"title": "Microsoft has introduced Dragon Copilot, designed to assist with clinical workflows.", "content": "[Meet Microsoft Dragon Copilot: Your new AI assistant for clinical workflow - Microsoft Industry Blogs](https://www.microsoft.com/en-us/industry/blog/healthcare/2025/03/03/meet-microsoft-dragon-copilot-your-new-ai-assistant-for-clinical-workflow/)", "created_time": "2025-03-04T04:18:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 16, "num_comments": 0, "subreddit": "unknown", "author": "Inevitable-Rub8969", "url": "https://reddit.com/r/microsoft/comments/1j32i5f/microsoft_has_introduced_dragon_copilot_designed/", "ticker": "MSFT", "date": "2025-03-04"}, {"title": "New Microsoft 365 outage impacts Teams, causes call failures", "content": "", "created_time": "2025-03-04T04:55:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 126.0, "upvotes": 94, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j335or/new_microsoft_365_outage_impacts_teams_causes/", "ticker": "MSFT", "date": "2025-03-04"}, {"title": "Microsoft sunsetting Skype on May 2025", "content": "A huge respect and a humble tribute to <PERSON><PERSON>. Only millennials would know the importance. This was our “Teams”, “Slack”, “discord”, “WhatsApp”", "created_time": "2025-03-03T05:10:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 81.0, "upvotes": 51, "num_comments": 0, "subreddit": "unknown", "author": "gyardgain", "url": "https://reddit.com/r/microsoft/comments/1j2b84u/microsoft_sunsetting_skype_on_may_2025/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Microsoft: Official Support Thread", "content": "This thread was created in order to facilitate easy-to-access support for our Reddit subscribers. We will make a best effort to support you. We may also need to redirect you to a specialized team when it would best serve your particular situation. Also, we may need to collect certain personal information from you when you use this service, but don't worry -- you won't provide it on Reddit. Instead, we will private message you as we take data privacy seriously.\n\n### Here are some of the types of issues we can help with in this thread:\n\n* Microsoft Support: Needing assistance with specific Microsoft products (Windows, Office, etc..)\n\n* Microsoft Accounts: Lockouts, suspensions, inability to gain access\n\n* Microsoft Devices: Issues with your Microsoft device (Surface, Xbox)\n\n* Microsoft Retail: Needing to find support on a product or purchase, assistance with activating online product keys or media, assistance with issues raised from liaising with colleagues in the Microsoft Store.\n\nThis list is not all inclusive, so if you're unsure, simply ask.\n\n### When requesting help from us, you may be requested to provide Microsoft with the following information (you'll be asked via private message from the MSModerator account):\n\n* Your full name (First, Last)\n\n* Your interactions with support thus far, including any existing service request numbers\n\n* An email address that we can use to contact you\n\nThank you for being a valued Microsoft customer.\n\n*For previous Support Threads, please use the [Support Thread](https://msft.it/61699qDDXf) flair.*", "created_time": "2025-03-03T12:25:25", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10027.0, "upvotes": 43, "num_comments": 0, "subreddit": "unknown", "author": "MSModerator", "url": "https://reddit.com/r/microsoft/comments/1j2hi7e/microsoft_official_support_thread/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Has any US based MSFT employee (who is not an EU citizen) been able to get an internal role/sponsorship for an EU role?", "content": "I’m looking at a few internal roles in Dublin and Barcelona and curious what the odds are for a current US based worker (and US citizen) to actually get sponsored to move internally abroad. \n\nI know I have to interview and still get the role but was curious if anyone has done this and had success or if anyone had any tips. \n\n", "created_time": "2025-03-03T13:48:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 48.0, "upvotes": 16, "num_comments": 0, "subreddit": "unknown", "author": "The_Federal", "url": "https://reddit.com/r/microsoft/comments/1j2j15j/has_any_us_based_msft_employee_who_is_not_an_eu/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Azure M365 Outage", "content": "Anyone feeling the effects of a wide area outage?", "created_time": "2025-03-03T17:13:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 60.0, "upvotes": 30, "num_comments": 0, "subreddit": "unknown", "author": "swlci", "url": "https://reddit.com/r/microsoft/comments/1j2nqfu/azure_m365_outage/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Microsoft Offer Timeline - Action Center Status Change", "content": "Hey everyone, I was just curious—if you’ve received an offer from Microsoft, how long did it take for your application status in the Action Center to change from Scheduled to Completed? I had finished 3 rounds of interview last Thursday so it’s only been 2 business days.\n\n", "created_time": "2025-03-03T17:16:08", "platform": "reddit", "sentiment": "bullish", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1j2nspo/microsoft_offer_timeline_action_center_status/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Am I the only one struggling with Microsoft's UI, or is everyone on the same page?", "content": "I've been using Google Workspace for a while but recently decided to shift to the Microsoft ecosystem mainly because it's cheaper. Thought it wouldn't be a big deal, but from yesterday, I’m just completely fucked up.\n\nEverything feels unnecessarily complicated, settings are all over the place, and the UI just doesn't feel intuitive. Outlook, Teams, OneDrive—nothing feels as smooth as Google's ecosystem. Am I missing something, or is this just how it is?\n\nHas anyone else gone through this transition? How long does it take to get used to it?\n\n4o", "created_time": "2025-03-03T20:35:32", "platform": "reddit", "sentiment": "bullish", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ishibam97", "url": "https://reddit.com/r/microsoft/comments/1j2spjy/am_i_the_only_one_struggling_with_microsofts_ui/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "Microsoft Product Design Intern Status Update", "content": "Has anyone heard back after the Feb 11th interview for the Product Design Intern position? I know some people got offers for the Azure and BIC teams, but was wondering if anyone heard back for other teams. If not, has your status changed to \"completed\" in the Microsoft Career portal or does it still say \"scheduling\"? Freaking out.", "created_time": "2025-03-03T21:50:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ListenExtension1620", "url": "https://reddit.com/r/microsoft/comments/1j2ui2b/microsoft_product_design_intern_status_update/", "ticker": "MSFT", "date": "2025-03-03"}, {"title": "SDE 2 Job Offer flexibility", "content": "Hi all,\n\nI recently received an unofficial offer for a SDE 2 position from Microsoft for the Azure. Prior to the hiring event, I was told this position would be remote and even clarified with the hiring manager if it would be remote, which they said yes. \n\nThe following day after I spoke with the hiring manager, I spoke with the recruiter and they wanted to confirm if I would be okay relocating to Redmond (I'm located in Atlanta) by April. Obviously this came as a surprise and would be a big move, which would include me and my girlfriend moving together. While we both have always wanted to live on the west coast, this obviously was sprung on us out of nowhere so we were very unprepared and stressed to make a decision. \n\nAfter discussing the circumstances further with the recruiter and renegotiating, myself and my girlfriend have decided that April is too soon. I was planning to send an email to my recruiter requesting to start the position remote and move later in the year once we are more prepared for the move. I am unfamiliar with how the hybrid policy at Microsoft works, and am aware that many decisions are up to the hiring managers discretion, and so I wanted to see if anyone could provide insight on if this would be possible or if anyone has been in a similar situation and could provide some additional insight. ", "created_time": "2025-03-02T01:22:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 21.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "justsomeguyintech84", "url": "https://reddit.com/r/microsoft/comments/1j1f85n/sde_2_job_offer_flexibility/", "ticker": "MSFT", "date": "2025-03-02"}, {"title": "BioWare co-founder laments Jade Empire's commercial failure and blames it on 'the worst advice, absolutely moronic advice' from Microsoft", "content": "\"I just think it would have been a way more successful product at the beginning of a cycle than the end.\"", "created_time": "2025-03-02T04:09:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 69.0, "upvotes": 57, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1j1iax0/bioware_cofounder_laments_jade_empires_commercial/", "ticker": "MSFT", "date": "2025-03-02"}, {"title": "How does Bonus and Annual Stock Award work?", "content": "NG SWE offer letter says this:\n\n\"You will also be eligible for an annual bonus, ranging from zero to a maximum of 20% of your bonus eligible salary during the rewards period based on your performance. If you are a new hire, your first eligibility for a bonus will be determined based on your start date and will be reviewed each year per Microsoft eligibility rules.\" \n\n\n\nIs this same as Target Bonus???\n\n\n\nI am also curious about this, idk what this means: \n\n\"Annual Stock Award. You are also eligible to be considered for future Stock Awards based on your start date.\" \n\n\n\nIs it like you get extra stocks if you perform well?", "created_time": "2025-03-01T05:13:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 36.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Supreme-Philosopher", "url": "https://reddit.com/r/microsoft/comments/1j0s0fo/how_does_bonus_and_annual_stock_award_work/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Digital sovereignty: Microsoft finalizes EU data border for cloud services", "content": "", "created_time": "2025-03-01T08:05:45", "platform": "reddit", "sentiment": "neutral", "engagement_score": 193.0, "upvotes": 127, "num_comments": 0, "subreddit": "unknown", "author": "donutloop", "url": "https://reddit.com/r/microsoft/comments/1j0umaj/digital_sovereignty_microsoft_finalizes_eu_data/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Microsoft Skype is Now Microsoft Teams.", "content": "What do you think of Skype after 22 years of existence?", "created_time": "2025-03-01T12:40:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 41.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1j0ylho/microsoft_skype_is_now_microsoft_teams/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Microsoft Copilot also comes as a MacOS program", "content": "", "created_time": "2025-03-01T13:04:53", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 12, "num_comments": 0, "subreddit": "unknown", "author": "donutloop", "url": "https://reddit.com/r/microsoft/comments/1j0z0rj/microsoft_copilot_also_comes_as_a_macos_program/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Difference between CBI and Annual Rewards", "content": "Hi, what's the difference between Core Priority Based Incentive (CBI) and Annual Rewards in Microsoft?\n\nCan someone please explain how CBI works?", "created_time": "2025-03-01T13:39:11", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Recent-Two-3708", "url": "https://reddit.com/r/microsoft/comments/1j0znzm/difference_between_cbi_and_annual_rewards/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "With <PERSON><PERSON> retiring, I will miss the ability to answer incoming calls automatically. Is there any chance that this feature can be added to Microsoft Teams?", "content": "Hello everyone,\n\nAs a longtime user of Skype, one feature I've come to rely on is its ability to automatically answer incoming calls. This has been incredibly useful for both personal and family calls, ensuring no one misses out on important conversations, even if they're momentarily away from their device.\n\nWith Skype retiring, I'm looking to make the transition to Microsoft Teams. While I appreciate the many features Teams offers, I noticed that it currently lacks the automatic call-answering capability that I've grown to depend on.\n\nI believe adding this feature to Microsoft Teams would greatly enhance its functionality and user experience. Not only would it benefit former Skype users like myself, but it would also provide added convenience to the broader Teams community.\n\nI'm reaching out to the community here to gather support and feedback. Have any of you also found this feature to be valuable? Let's come together and ask Microsoft to consider integrating automatic call-answering into Teams.\n\nThanks for your support and insights!", "created_time": "2025-03-01T14:12:02", "platform": "reddit", "sentiment": "bullish", "engagement_score": 20.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Candid_Chef8378", "url": "https://reddit.com/r/microsoft/comments/1j10bhq/with_skype_retiring_i_will_miss_the_ability_to/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Microsoft leveling during interviews", "content": "I am preparing for a TPM interview loop at Microsoft. The position is “Senior” TPM, but, after doing some more research, I think that is a lower level than I am.\n\nI have 15 years of experience in TPM across several industries in tech. I was hired as e5 at Meta in 2022 (I no longer work there due to a layoff). I am at a “Staff” level at my current company, a large corporation, and likely up for a promotion within the next year.\n\nMy salary expectations also fall more within the “Principal” level at Microsoft.\n\nMy questions are, can I be upleveled during the interview process, even though the original job description was “Senior”? Can I have a conversation with the recruiter about this? Any other tips?\n", "created_time": "2025-03-01T14:28:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 58.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "ApartmentStunning484", "url": "https://reddit.com/r/microsoft/comments/1j10nkz/microsoft_leveling_during_interviews/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Why Skype Shouldn't Be Retired: A Beloved Communication Tool", "content": "Hey everyone,\n\nI wanted to start a discussion about the recent announcement regarding the end of life (EoL) of Skype. As someone who has relied on Skype for years, I believe retiring it is a significant loss, and I'd like to share my thoughts on why it shouldn't be retired.\n\n**1. A Beloved and Trusted Platform**\n\nSkype has been a trusted communication tool for millions of users worldwide. It has connected friends, families, and colleagues across the globe, providing high-quality video and audio calls, messaging, and more. The familiarity and reliability of Skype have made it a go-to platform for staying connected with loved ones and conducting business meetings.\n\n**2. Ease of Use and Accessibility**\n\nOne of Skype's greatest strengths is its user-friendly interface and accessibility. It works seamlessly across various devices and operating systems, making it easy for users to connect regardless of their technical expertise. Retiring Skype would force many users to transition to alternative platforms, which may not offer the same ease of use and accessibility.\n\n**3. Unique Features**\n\nSkype offers several unique features that set it apart from other communication tools. From the ability to make international calls at affordable rates to integrated chat and file sharing, Skype has provided a comprehensive communication solution that many users have come to rely on. These features are not always fully replicated in other platforms.\n\n**4. Impact on Users**\n\nThe decision to retire Skype will have a significant impact on users who have built their communication routines around it. Many users, especially those who are not tech-savvy or have older devices, may face difficulties transitioning to new platforms. This disruption could affect personal and professional relationships that have thrived on Skype.\n\n**5. A Sense of Nostalgia**\n\nFor many of us, Skype holds a special place in our hearts. It has been a part of our lives for years, helping us celebrate special moments, stay connected with distant family members, and conduct important business discussions. The thought of losing this beloved platform is deeply saddening.\n\nIn conclusion, retiring Skype is a decision that will negatively impact millions of users who have come to rely on its unique features and user-friendly interface. I urge Microsoft to reconsider this decision and explore ways to keep Skype alive and supported. Let's rally together and show our support for this beloved platform.\n\nWhat are your thoughts on the retirement of Skype? Do you agree that it should be kept alive? Let's discuss!\n\nBest,  \nBackground Jello.", "created_time": "2025-03-01T14:55:05", "platform": "reddit", "sentiment": "bullish", "engagement_score": 32.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Background-Jello-221", "url": "https://reddit.com/r/microsoft/comments/1j117hu/why_skype_shouldnt_be_retired_a_beloved/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Will you switch to Microsoft Teams when <PERSON><PERSON> is retired?", "content": "Hey r/Microsoft community,\n\nAs we all know, <PERSON>pe is set to be retired soon, and I'm curious to hear what everyone plans to do next.\n\nAre you planning to switch to Microsoft Teams, or do you have other messaging apps in mind? Personally, I believe Teams might not be the best replacement for Skype, and I'm concerned about losing some of the features and user-friendliness that Skype offered.\n\nLet's discuss:\n\n* What features do you think Teams lacks compared to Skype?\n* Are there any other apps you are considering as a replacement?\n* How do you feel about this change in general?\n\nI hope to gather a lot of opinions here so we can draw Microsoft's attention and maybe even get them to reconsider their decision.\n\nLooking forward to your thoughts!", "created_time": "2025-03-01T18:06:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 99.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Candid_Chef8378", "url": "https://reddit.com/r/microsoft/comments/1j15ncn/will_you_switch_to_microsoft_teams_when_skype_is/", "ticker": "MSFT", "date": "2025-03-01"}, {"title": "Why doesn't Teams offer the option of always muting the mic by default when joining a meeting?", "content": "It is highly frustrating that Microsoft Teams still does not offer the option to mute the microphone by default when joining a meeting. Many other products provide this functionality, yet despite years of user requests, Microsoft has not implemented what is clearly a simple and logical feature. \n\nhttps://answers.microsoft.com/en-us/msteams/forum/all/teams-mute-by-default-when-entering-a-meeting/979e5882-3ffa-45f2-8436-e996056d1d5d?page=18", "created_time": "2025-02-28T00:56:25", "platform": "reddit", "sentiment": "neutral", "engagement_score": 49.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "RevolutionStill4284", "url": "https://reddit.com/r/microsoft/comments/1izvizu/why_doesnt_teams_offer_the_option_of_always/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "AI/ML role interview process", "content": "Can anyone share your interview experience on AI/ML roles at MSFT?\n\nI just passed the initial screening, but my recruiter hasn’t responded to me yet regarding the interview process.\n\nI appreciate your help.", "created_time": "2025-02-28T07:58:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Blasphemer666", "url": "https://reddit.com/r/microsoft/comments/1j02nmz/aiml_role_interview_process/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Microsoft preparing to shut down Skype in May", "content": "", "created_time": "2025-02-28T08:42:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 342.0, "upvotes": 248, "num_comments": 0, "subreddit": "unknown", "author": "digidude23", "url": "https://reddit.com/r/microsoft/comments/1j039cn/microsoft_preparing_to_shut_down_skype_in_may/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Outlook e-mail ads are a security vulnerability", "content": "I'm mind blown by the fact that Microsoft has an obvious security vul in their Outlook app.\n\n  \nAre you guys braindead over there or what?\n\n  \nThis is clearly a security issue waiting to be exploit. Remove the ad as an e-mail function from your app please.", "created_time": "2025-02-28T15:45:48", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "EntertainmentOk356", "url": "https://reddit.com/r/microsoft/comments/1j0ao8d/outlook_email_ads_are_a_security_vulnerability/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Microsoft O365 GCC vs GCC High", "content": "We have a client who we are working with on CMMC level 2. We were going to move them to Microsoft GCC but they want to move to GCC high due to potentially having vendors sending ITAR data to them through email. We are having a hard time finding what the restrictions are when it comes to GCC High. One that I'm pretty sure of (But correct me if I'm wrong) is that any enterprise apps that you want to add have to be FedRAMP authorized or you wont be able to add them. This is a fairly big issue since we can't tie in a lot of security services like SIEM/SOC, Reporting, tickets, etc. But overall this limitation would make sense from a security perspective. If its not that case that would be a completely different story.\n\n  \nI know there's other limitations when it comes to stuff like sharing which I'm not overly concerned about. But it's all of the other potential limitations I'm hoping people can shed light on compared to what GCC or even a normal Microsoft tenant has that they don't where its a pain.", "created_time": "2025-02-28T20:23:49", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "BrandonSB2", "url": "https://reddit.com/r/microsoft/comments/1j0hc2q/microsoft_o365_gcc_vs_gcc_high/", "ticker": "MSFT", "date": "2025-02-28"}, {"title": "Turning 30", "content": "I (30m, married to 29f, 2 yr old kiddo and another on the way) have been following this subreddit since 2018, but first-time posting my financials. I have learned so much from this thread and have come across so many helpful people along the way. I really appreciate you guys and gals!\n\nThis post is really just to reflect on my journey since starting my job career in 2018, and to show some of the great results that can be had from following the basic principles taught in this community. Like most people here, I started with the intentions of savings as much as possible, as fast as possible, so that I could retire early. It was an exciting endeavor, but as my life has progressed I've started to shift my mindset. I'm not as hyperfocused on a high savings rate and an early retirement date. I'm still young and I know that my life goals will continue to be fluid in the next 30 years, but I feel okay with the fact that I might work (at least part time) well into my 60s. I have eased up on my frugality and learned to enjoy spending, especially when it is philanthropic. My company has solid pay, good culture, and even better benefits. I'm very fortunate to have been hired on at such an early age (22), because the company fosters long-term careers. An example of that is the retirement pension which is uncommon today in corporate America.\n\nMy first year working I only contributed to my 401k up to the company match while I paid of 17k of student loans, but I was still able to max out my Roth IRA. Every year since, I've maxed out my 401k and Roth IRA/IRAs, and I even put money into my after-tax 401k once the before-tax limits were reached. At the end of every year, I submit an in-plan roth conversion to get that money into my Roth 401k. Any excess savings has gone towards brokerage or cash savings. All of my 401k investments are in low cost, US stock index funds. My non-401k investments are in VTI.\n\nMy wife stepped away from teaching when we got married in 2021, and started her own jewelry business from home. It was a slow start with a lot of investment up front that she slowly paid back, but it has started to bring in some decent income the last two years. She's done this while also caring for our son at home which is amazing!\n\nAs my wife and I continue to grow our family, my main focus will be to do what's best for them. I believe that staying on this path will give me many options to do just that in the not-so-distant future by being financially independent. Shout out to JL Collins!\n\nEnough chatter, here are the numbers:\n\n|Year|NW|Gross Household Income|Expenses|Savings Rate (Gross)|Comments|\n|:-|:-|:-|:-|:-|:-|\n|2018|0k|96k|47k|51%|Started career|\n|2019|42k|113k|62k|45%||\n|2020|115k|124k|60k|52%||\n|2021|211k|138k|80k|42%|Married/bought first home|\n|2022|375k|146k|83k|43%||\n|2023|377k|164k|93k|43%|First child born|\n|2024|541k|172k|101k|41%||\n|2025|716k|145k (estimate)|117k (estimate)|19% (estimate)|Changed jobs within company|\n\n**Assets:**\n\n* 401k: 348k\n   * Before-Tax: 202k\n   * After-Tax: 5k\n   * General/Company Match: 85k\n   * Roth: 4k\n   * Roth Conversion: 52k\n* My IRA: 74k\n* Wife's IRA: 33k\n* Brokerage: 93k\n* Cash: 6k\n* Vehicles: 25k\n* Home equity: 137k\n\nSide notes: Like mentioned before, I am vested in my company's retirement pension. Similar to social security, I do not include it in my calculations while in this early wealth accumulation phase. Also, we have two 529s open (one for our child, another for our neice). We plan to open another account when our second child is born. We do not count these towards our NW.\n\n**Debts:**\n\n* Mortgage #1: 176k\n   * 30-year fixed: 3.25% interest rate\n   * Monthly payment: $1391.12\n* Mortgage #2/Home Improvement Loan: 112k\n   * 10-year fixed: 7.125% interest rate\n   * Monthly payment: $1314.99\n\nThree big changes/impacts to our finances in 2025:\n\n1. **Lower income:** Changed jobs within my company. Went from hourly to salaried employee. Initially lower pay due to no more overtime, but I have better long-term growth potential/higher ceiling for salary growth.\n2. **Additional mortage:** Started 128k home addition/renovation project in January to update home and make more space for my wife's home business and our growing family. Adding 438 sqft to our exisitng 1300 sqft, updating kitchen, new floors, new windows, & painting exterior. Financed a 10-year secondary mortgage for this project which puts our combined mortgage payments at $2700/month.\n3. **Another family member:** Second child is being born in June. In preparation, we chose a lower deductible, higher premium health care plan for better child delivery coverage. This has resulted in lower net income on my paychecks. Once the baby is here that will bring additional expenses as well (food, diapers, etc.), but not nearly as much as our first since we still have all our baby care items and lots of hand-me-down clothes.\n\nLastly, please do not comment about me including our car values and home equity in our NW. The way I see it: Whether or not you include these is not worth debating. What's more important is that you stay consistent throughout your tracking.\n\nOverall, I am happy with how we've progressed. However, this year is going to be pretty tight financially with decreased income & increased expenses. We are taking our foot off the savings gas pedal quite a bit (not maxing out 401k or IRAs). Thankfully, we've laid a lot of ground work in prior years that will allow us to get through this period. I'm also expecting a significant salary increase in the next 1-2 years that will help us get our savings rate back on track.", "created_time": "2025-02-27T03:07:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 49.0, "upvotes": 19, "num_comments": 0, "subreddit": "unknown", "author": "Fireitsy_68168", "url": "https://reddit.com/r/financialindependence/comments/1iz61ip/turning_30/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Microsoft Phoenix Office- worth visiting ??", "content": "I’m traveling to this area and working remotely for one day, was wondering if phoenix office has anything fun (ex. Snacks ?) ", "created_time": "2025-02-27T05:27:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "sonny-land", "url": "https://reddit.com/r/microsoft/comments/1iz8je4/microsoft_phoenix_office_worth_visiting/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "What’s up with the Microsoft stock value ?", "content": "The prices plummeted any ideas out there.", "created_time": "2025-02-27T13:42:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 134.0, "upvotes": 48, "num_comments": 0, "subreddit": "unknown", "author": "Kdja4738", "url": "https://reddit.com/r/microsoft/comments/1izg2ch/whats_up_with_the_microsoft_stock_value/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Offer Timeline", "content": "Hello! \nI had my interview at Microsoft and things moved pretty fast, the recruiter reached out to me in 2 days to discuss salary and after 3 more days got a verbal offer. It’s been 2 days since.\nHow long is it going to take to get a written offer? I am in an EU country if that changes anything.", "created_time": "2025-02-27T14:37:27", "platform": "reddit", "sentiment": "bullish", "engagement_score": 15.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "IceEast", "url": "https://reddit.com/r/microsoft/comments/1izh7j0/offer_timeline/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Windows 11 pirates have a new ally — Microsoft Copilot", "content": "", "created_time": "2025-02-27T17:20:39", "platform": "reddit", "sentiment": "neutral", "engagement_score": 25.0, "upvotes": 19, "num_comments": 0, "subreddit": "unknown", "author": "Healthy_Block3036", "url": "https://reddit.com/r/microsoft/comments/1izl2vl/windows_11_pirates_have_a_new_ally_microsoft/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Microsoft pushes ahead with AI in gaming", "content": "", "created_time": "2025-02-27T19:34:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "factchecker01", "url": "https://reddit.com/r/microsoft/comments/1izoc3u/microsoft_pushes_ahead_with_ai_in_gaming/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Microsoft To Do - What is there that’s better?", "content": "I’ve started using To Do to manage my own workload at work but it’s awful.\n\nThe feature of dragging in flagged emails from Outlook is fantastic but they appear in a separate list from user generated tasks.\n\nThere seems no easy way to create an Eisenhower prioritisation matrix without categories. However is you this for projects or work streams as well it rapidly crashes.\n\nI can’t set start dates or create dependencies. Sub tasks are always hidden.\n\nIs there a better Microsoft product? I don’t need to manage complex projects or a large team. With me previous work we used Asana which was far superior.", "created_time": "2025-02-27T21:37:13", "platform": "reddit", "sentiment": "bearish", "engagement_score": 5.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Dadda_Green", "url": "https://reddit.com/r/microsoft/comments/1izr7oq/microsoft_to_do_what_is_there_thats_better/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Post-interview questions: how long to wait after loop interviews/sending thank-you notes", "content": "I had my four-person loop interview for an Azure CXP role last Thursday and Friday.  I had a few questions that I wasn't able to find recent answers on in the sub or elsewhere:\n\n1) Now that the interviews are over, how long should I wait before asking the recruiter if there's any feedback or followup?  I didn't get any followup from the recruiter or scheduler after I'd gotten scheduled, and the loop interview participants each said they'd be putting in their feedback over the following few days.  \n\nSome posts in the sub mentioned anywhere from three days to three weeks, so if I'm in for the long haul, no worries, I'd like to at least know what to expect.\n\n2) The manager in the first loop interview mentioned that I could always reach out if I had any questions or other info to add.  My only contact emails are for the recruiter and scheduler.  Would it be inappropriate if I tried sending a thank-you email to the manager by guessing their email address?  \n\nI already emailing them with firstnamelastname@microsoft, firstname.lastname, and firstinitiallastname, but got access denied bounces each time.  I'm guessing this is intended behavior on <PERSON>' side to stop people like me from spamming would-be hiring managers.  I got the vibe from the manager that they were fine with hearing from me again but if that's just a polite mention rather than an offer to stay in touch, I can back off, but if it's a good idea to go so far as to message them on Linkedin, so be it.  \n\nThe waiting is the hard part, but as long as I know indeed that it's a wait for a yes or no and not an indication of something going wrong, I can wait.", "created_time": "2025-02-27T21:44:41", "platform": "reddit", "sentiment": "bullish", "engagement_score": 12.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "MohnJaddenPowers", "url": "https://reddit.com/r/microsoft/comments/1izrdz6/postinterview_questions_how_long_to_wait_after/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Cheapest way to get Word & Excel after Microsoft 365’s Price Hike?", "content": "I’ve always preferred Microsoft Word and Excel for my hobbies, they’re straightforward, offline, and don’t force me to work in a browser window. But now that Microsoft 365 is getting even more expensive (the new monthly fee is more than I can comfortably afford), I’m starting to wonder if there’s a cheaper way to get Word and Excel without paying for the entire subscription suite. I don’t really need PowerPoint, OneNote, or Outlook; just the basics.\n\nI know some folks recommend Google Docs and Sheets, but I don’t want to rely solely on a web app. I’ve also heard about WPS Office as a potential replacement, which apparently has a free tier and decent compatibility with Word and Excel files. Has anyone tried it, and does it really stand up to what Microsoft offers for simple tasks and spreadsheets? Or are there other tricks to buy Office cheaply, like a perpetual license or a one-off purchase for just Word and Excel? I’d love to hear your suggestions before I jump into another subscription.\n\n", "created_time": "2025-02-27T22:55:56", "platform": "reddit", "sentiment": "bullish", "engagement_score": 180.0, "upvotes": 68, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1izt046/cheapest_way_to_get_word_excel_after_microsoft/", "ticker": "MSFT", "date": "2025-02-27"}, {"title": "Question for a UK Microsoft Partner...", "content": "Are Microsoft Partners allowed to overcharge for 365 products (such as 365 Business Basic)? \n\nA colleague is trying to sort out their office systems and they've found that their IT provider is charging about 50% over the list price for 365 products, and has been for several years. \n\nWe'd like to recoup some of this, hence the question. If it does go against MS's policies, it would be useful to know for leverage...\n\nTIA", "created_time": "2025-02-27T23:18:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "trentsc", "url": "https://reddit.com/r/microsoft/comments/1izti22/question_for_a_uk_microsoft_partner/", "ticker": "MSFT", "date": "2025-02-27"}], "metadata": {"timestamp": "2025-07-06T20:13:17.032230", "end_date": "2025-03-06", "days_back": 7, "successful_dates": ["2025-03-06", "2025-03-05", "2025-03-04", "2025-03-03", "2025-03-02", "2025-03-01", "2025-02-28", "2025-02-27"], "failed_dates": [], "source": "local"}}}}