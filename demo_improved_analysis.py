#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版AI对冲基金系统代理信号分析演示

该脚本演示了改进后的可视化功能：
1. 单一综合热力图显示交易信号和置信度
2. 颜色透明度表示置信度强度
3. 数值标注显示具体置信度值
4. 改进的图例和说明
"""

import os
import sys
from pathlib import Path
from analyze_agent_signals import AgentSignalAnalyzer

def demo_analysis():
    """演示改进后的分析功能"""
    print("🚀 改进版AI对冲基金系统代理信号分析演示")
    print("=" * 60)
    
    # 检查可用的实验数据
    reasoning_logs_path = Path("reasoning_logs")
    if not reasoning_logs_path.exists():
        print("❌ reasoning_logs 目录不存在")
        return
    
    # 寻找实验数据
    experiments = []
    for item in reasoning_logs_path.iterdir():
        if item.is_dir() and item.name.startswith("experiment_"):
            experiments.append(item.name)
    
    if not experiments:
        print("❌ 未找到实验数据")
        return
    
    print(f"📁 发现 {len(experiments)} 个实验")
    
    # 选择几个代表性的实验进行演示
    demo_experiments = [
        {
            'name': 'AAPL - Gemini 2.0 Flash',
            'path': 'experiment_AAPL_20250101-20250601_gemini2.0_flash',
            'description': '苹果公司股票，使用Gemini 2.0 Flash模型'
        },
        {
            'name': 'MSFT - Grok Beta',
            'path': 'experiment_MSFT_20250101-20250601_grok_beta',
            'description': '微软公司股票，使用Grok Beta模型'
        },
        {
            'name': 'NVDA - GPT-3.5',
            'path': 'experiment_NVDA_20250101-20250601_gpt3.5',
            'description': '英伟达公司股票，使用GPT-3.5模型'
        }
    ]
    
    print("\n🎯 演示实验:")
    for i, exp in enumerate(demo_experiments, 1):
        if exp['path'] in experiments:
            print(f"   {i}. {exp['name']} - {exp['description']}")
        else:
            print(f"   {i}. {exp['name']} - ❌ 数据不存在")
    
    print("\n" + "=" * 60)
    
    # 创建演示输出目录
    demo_output = Path("demo_charts")
    demo_output.mkdir(exist_ok=True)
    
    success_count = 0
    
    for exp in demo_experiments:
        if exp['path'] not in experiments:
            continue
            
        print(f"\n🔍 正在分析: {exp['name']}")
        print(f"📝 描述: {exp['description']}")
        
        try:
            # 创建分析器
            analyzer = AgentSignalAnalyzer(reasoning_logs_path / exp['path'])
            
            # 加载数据
            print("📊 正在加载数据...")
            data = analyzer.load_agent_data()
            
            if not data:
                print("❌ 未找到有效数据")
                continue
            
            # 统计信息
            total_days = len(data)
            all_agents = set()
            for date_data in data.values():
                all_agents.update(date_data.keys())
            
            print(f"✅ 成功加载 {total_days} 天的数据")
            print(f"📈 发现 {len(all_agents)} 个代理")
            
            # 显示代理列表
            print("🤖 代理列表:")
            for agent in sorted(all_agents)[:10]:  # 只显示前10个
                display_name = analyzer.agent_name_mapping.get(agent, agent)
                print(f"   • {display_name}")
            if len(all_agents) > 10:
                print(f"   ... 还有 {len(all_agents) - 10} 个代理")
            
            # 创建可视化
            print("🎨 正在生成改进版可视化图表...")
            analyzer.create_comprehensive_visualization(data, demo_output)
            
            success_count += 1
            print("✅ 分析完成！")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            continue
    
    print(f"\n🎉 演示完成！成功分析 {success_count}/{len(demo_experiments)} 个实验")
    print(f"📁 图表保存在: {demo_output.absolute()}")
    
    # 显示改进功能说明
    print("\n" + "=" * 60)
    print("🆕 改进功能说明:")
    print("1. 📊 单一综合热力图 - 同时显示交易信号和置信度")
    print("2. 🎨 颜色透明度 - 置信度高的信号颜色更深")
    print("3. 🔢 数值标注 - 每个单元格显示具体置信度值")
    print("4. 📋 详细图例 - 清楚说明颜色和透明度含义")
    print("5. 🌐 中文支持 - 完整的中文界面和代理名称")
    print("6. 📈 自适应布局 - 根据数据量自动调整图表大小")
    
    print("\n颜色说明:")
    print("• 🟢 绿色 = BUY信号 (看涨)")
    print("• 🔴 红色 = SELL信号 (看跌)")
    print("• ⚪ 灰色 = HOLD信号 (中性)")
    print("• 透明度 = 置信度强度 (越深越确信)")
    
    print("\n使用建议:")
    print("• 关注颜色深的区域 - 代表高置信度的交易信号")
    print("• 观察横向模式 - 了解特定时期的市场共识")
    print("• 分析纵向模式 - 比较不同代理的决策风格")
    print("• 结合数值标注 - 获取精确的置信度信息")

def show_file_structure():
    """显示生成的文件结构"""
    demo_output = Path("demo_charts")
    if demo_output.exists():
        print(f"\n📁 生成的文件:")
        for file in demo_output.iterdir():
            if file.is_file():
                size_mb = file.stat().st_size / (1024 * 1024)
                print(f"   📄 {file.name} ({size_mb:.1f} MB)")

if __name__ == "__main__":
    try:
        demo_analysis()
        show_file_structure()
        
        print(f"\n💡 提示:")
        print("• 可以使用 analyze_agent_signals.py 分析其他实验数据")
        print("• 可以使用 run_analysis_example.py 进行交互式分析")
        print("• 查看 README_agent_analysis.md 获取详细使用说明")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
