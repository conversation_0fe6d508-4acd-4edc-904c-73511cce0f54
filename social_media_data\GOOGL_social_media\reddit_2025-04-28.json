[{"platform": "reddit", "post_id": "reddit_1k9l2ww", "title": "Chrome translates Google search results even when Google Translate is turned off in the settings", "content": "I'm having this problem that has been troubling me for months now, and it's bothering me a lot. My main language is portuguese, but I often search for things in english too. However, no matter what I change in the settings, Google results keep getting automatically translated to portuguese.\n\nThis was forcing me to have to click in the \"show original\" text before clicking the result. More recently though, clicking the text still sends me to the translated page, and then I have to go back to the search results and click the result again in order to get the original page in english.\n\nI have already tried changing the settings in \"three dots > settings > language\", both messing around with the languages and disabling Google Translate. None of it made any difference. I have searched around in the flags, nothing there disables this. The issue persists even in incognito mode.\n\nDoes someone have any idea of how to possibly disable this feature?", "author": "ErasedX", "created_time": "2025-04-28T02:31:34", "url": "https://reddit.com/r/chrome/comments/1k9l2ww/chrome_translates_google_search_results_even_when/", "upvotes": 15, "comments_count": 24, "sentiment": "neutral", "engagement_score": 63.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k9sjnu", "title": "anyone use Google Gemini AI app to compare stocks?", "content": "I use it sometimes. its quite handy. comparing stocks and to judge my portfolio", "author": "Money_Exchange_8796", "created_time": "2025-04-28T10:48:26", "url": "https://reddit.com/r/dividends/comments/1k9sjnu/anyone_use_google_gemini_ai_app_to_compare_stocks/", "upvotes": 0, "comments_count": 4, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k9tqe5", "title": "Is there an online calculator with different age inputs for retirement age and social security drawing age?", "content": "Looking for a calculator that lets me input separate ages for \"when I will stop working\" and \"when I will start taking social security\". All the calculators I've found assume those are the same age?\n\nSay I have retirement savings and can stop working, then have a gap of X years and draw from savings, then start taking social security at 67 or 70 for example.\n\nEDIT: my quick and terrible reviews of some of the options\n\nboldin.com - requires signup, meh\n\nprojectionlab.com - requires signup, meh\n\nssa.tools - requires SSA sign in and copy/paste, meh\n\nmoneybee.net - somewhat limited and frustrating, can't input dollar amounts for how much I'm saving per year plus that is vague (does it include employer match or not?) plus it's only whole percentages, no apparent calculation for employer raises. Social Security inputs are frustrating. No way to have different saving percentage for myself vs. spouse. No easy way to go back and change inputs, it's a bunch of \"save and next\" separate pages. It's like the calculator is overly complex where it shouldn't be, and over simplified when it shouldn't be.\n\nRich/Broke/Dead (engaging-data.com) - this one looked good initially, but unfortunately it's based on knowing the precise amounts of everything at the instant you stop working, and knowing the exact age you will stop working. So not great for my case since I'm trying to compare what my retirement would look like depending on different stop-working ages.\n\nficalc.app - slick website but same problem as Rich/Broke/Dead, it's based on knowing your stop-working age and precise financial picture before you start using the calculator. Suppose I could use another calculator first to get those numbers, then bring them in here to get at what I want.\n\nIn the end I just made a google spreadsheet where most everything is dynamic/calculated, and all I have to do is enter what age I want to stop working. Left in some static assumptions like average annual salary increase, percent of income to contribute each year for now, SSA COLA, inflation, etc. and it works well. The output goes to a table where each row is a given future year and shows how much retirement I'll have, how much I need to draw considering inflation, SSA benefit in future dollars, etc.\n\nThanks to everyone for recommendations though!", "author": "RedditSubUser", "created_time": "2025-04-28T11:57:50", "url": "https://reddit.com/r/Fire/comments/1k9tqe5/is_there_an_online_calculator_with_different_age/", "upvotes": 11, "comments_count": 26, "sentiment": "bearish", "engagement_score": 63.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1k9ztyf", "title": "Anyone interested in fixing cloud computing? I'm looking for co-founders with fair equity split.", "content": "I'm not sure if sharing my idea is a good move, but considering it's unlikely anyone would actually build it, I'm probably worrying for nothing. It's pretty complex anyway. Easier to find someone as committed as I am than trying to build it with random people.\n\nThe idea: cloud costs for AI-heavy apps are insane and only getting worse. The plan is to fix that with a new platform; DCaaS (Decentralized Compute as a Service). Instead of paying through the nose for centralized servers, apps could tap into \\*their\\* users' devices, cutting cloud bills by 30–80%. It’s deep tech, involves AI model sharding, chain inference, security, but should be doable, and honestly I find it exciting.", "author": "Lumpy_Signal2576", "created_time": "2025-04-28T16:28:42", "url": "https://reddit.com/r/cloudcomputing/comments/1k9ztyf/anyone_interested_in_fixing_cloud_computing_im/", "upvotes": 8, "comments_count": 28, "sentiment": "neutral", "engagement_score": 64.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ka15j2", "title": "As a $TSLA HODLer, why I’m Adding $GOOGL for Waymo & DeepMind’s AI Value", "content": "TSLA has led the auto industry’s shift into a new era, one powered by autonomy, AI, and robotics.  \nI’ve been a proud $TSLA HODLer for years, and I’m still 100% committed to <PERSON><PERSON>’s vision: Full Self-Driving (FSD), Optimus robotics, Energy scaling.. Tesla is at the center of the AI-driven industrial revolution. But when I step back and think as an investor, I realize that *revolutions aren’t won by a single player* — and sometimes, parallel technologies can also dominate in their niches.\n\nThat’s why lately, I’ve been looking beyond just $TSLA, **not to replace it**, but to **strategically complement it**. Specifically:  \n**Waymo** (under Alphabet / $GOOGL) — leading in Level 4-5 autonomous driving at scale.  \n**DeepMind** — driving breakthroughs in AI, healthcare, and systems optimization.\n\nMost people only see $GOOGL as a Search and Ads giant, but the hidden value in Waymo and DeepMind could be *explosive* as the AI economy matures. If <PERSON><PERSON> nails autonomy + robotics, and Way<PERSON> dominates robotaxis, then **holding both $TSLA and $GOOGL could mean mastering the next auto and AI revolutions from both angles.** 🚀\n\nI recently wrote a [**value investing analysis on Alphabet**](https://blog.alert-invest.com/alphabet-value-investing/) (GOOGLE) covering Waymo, DeepMind, and other Google's venture. Would love your take:\n\n* Do you see $GOOGL as a smart complement to $TSLA?\n* Or do you think Tesla will dominate so much that Waymo becomes irrelevant?", "author": "AcceptableGiraffe172", "created_time": "2025-04-28T17:22:58", "url": "https://reddit.com/r/teslainvestorsclub/comments/1ka15j2/as_a_tsla_hodler_why_im_adding_googl_for_waymo/", "upvotes": 0, "comments_count": 17, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ka59dn", "title": "OMG i just realized google deleted years of my timeline data", "content": "Is there anything I can do to retrieve this?\n\nThis isn't something I go in and check on everyday so I was completely unaware...\n\nEverything prior 2024 is gone and I'm devastated. I had so many trips in there with friends i no longer have contact with and this was a great way to get back through memory lane.  \nThis was such a valuable feature to me. \n\nI've checked all 8 of my google accounts for backups and only one had some early 2024 stuff backed up since it had to manually be turned on... \n\nApparently there was some deadline to \"save your timeline\" that was sent in an ordinary email with a deadline of December 8, 2024... seriously?! '\n\nI'm about to cry man i can't believe this \n\n", "author": "antilytics", "created_time": "2025-04-28T20:09:45", "url": "https://reddit.com/r/GoogleMaps/comments/1ka59dn/omg_i_just_realized_google_deleted_years_of_my/", "upvotes": 23, "comments_count": 40, "sentiment": "bullish", "engagement_score": 103.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ka73e1", "title": "🔥 Find trending topics faster with Crawlup — Google & Instagram monitoring!", "content": "If you're into SEO, content marketing, or side projects, you know how important it is to catch trends early. 🚀\n\nWe built **Crawlup** to make it easier:\n\n* Track Google Trends in real-time\n* Monitor Instagram media by hashtag or location\n* Get instant keyword spikes & news tracking\n* Easy API or live dashboards\n\n✅ Free trial available — no credit card needed.  \nGood luck with your projects! 🔥", "author": "FinancialEconomist62", "created_time": "2025-04-28T21:26:40", "url": "https://reddit.com/r/Entrepreneur/comments/1ka73e1/find_trending_topics_faster_with_crawlup_google/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ka7kld", "title": "Funded Startup CEO <PERSON><PERSON>, No Revenue, No Commercial Application Yet. I will not promote.", "content": "Is $900k ridiculous for a startup CEO salary without revenue?\n\nI invested in a biotech startup that has a bright future and has had some wins (patents pending, positive testing, etc). I recently learned the CEO is paying himself almost $1mm/year. There is a board, but they are all in the pocket of the CEO and other founder. This really rubs me wrong. Seems like WAAAY too much for a startup. They raised a big round - mid-teens millions. They are about to close another similar size. Not sure what if anything I can do, but would also just like to hear people's opinions.\n\nYes, he has ownership.\n\n\n\nUpdate: A ton of people have contacted me directly after this post.  \n\n   * Yes, I invest from time to time but no I'm not interested right now because I'm working on buying a company for myself to own/operate. \n   * My background is digital advertising. I have had 2 successful multi-million exits and one failure. \n   * I could only offer operations experience in the world of digital advertising, B2B sales, B2C marketing and the like. I know nothing about biotech, per se. \n   * The serious messages and posts have been great here and I appreciate the intelligent,  thoughtful comments provided. I have learned from them. \n   * I do consult for businesses and would do that again. That was not the goal of this post. ", "author": "prescott0330", "created_time": "2025-04-28T21:47:27", "url": "https://reddit.com/r/startups/comments/1ka7kld/funded_startup_ceo_salary_no_revenue_no/", "upvotes": 767, "comments_count": 291, "sentiment": "bullish", "engagement_score": 1349.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}]