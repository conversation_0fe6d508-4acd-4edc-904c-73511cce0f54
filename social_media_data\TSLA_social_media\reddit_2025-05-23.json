[{"platform": "reddit", "post_id": "reddit_1kt75ho", "title": "Tesla 48 hour experience", "content": "Just saw this offer on the Tesla app and thought it’s pretty cool. Anyone already try this out?", "author": "Fantastic_Tomato7444", "created_time": "2025-05-23T01:08:28", "url": "https://reddit.com/r/teslamotors/comments/1kt75ho/tesla_48_hour_experience/", "upvotes": 894, "comments_count": 391, "sentiment": "neutral", "engagement_score": 1676.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kt7cg6", "title": "Why don't more banks offer personal loans?", "content": "I am not sure what to call it but it is not the stereotypical personal loan that everyone talks about. Navy Federal Offers savings secured loans to where you can put money in a savings account or cd, they lock your money in it, and you take out a loan with your own money at the accounts apy + 2%. Each payment you make releases money back into your account.\n\nThe loan is advantageous for a few reasons. If you don't like the interest rate you are being charged you can be your own bank. If I have the cash for a car and dont like toyota financials interest rates, I can get a rate that is 50-75% less etc. Some may like the lower interest loans to keep the cash \"on hand.\"\n\nIts free money for the bank. You pay them interest and they then loan your money out at higher rates. I am not sure why other banks don't do this.\n\n  \nEdit: Im not saying I would do it. Its pretty popular on the NFCU subreddit. I was just curious as to why other banks dont offer it.", "author": "OpinionofC", "created_time": "2025-05-23T01:18:46", "url": "https://reddit.com/r/personalfinance/comments/1kt7cg6/why_dont_more_banks_offer_personal_loans/", "upvotes": 0, "comments_count": 120, "sentiment": "neutral", "engagement_score": 240.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktfhmh", "title": "What features do you wish Google My Maps had?", "content": "Hey everyone,\n\nI’ve been using Google My Maps for a variety of projects and love how accessible and easy it is to use. That said, I’ve often found myself wishing it had a few extra features — and I’m sure others here have too.\n\nI’m curious:\nWhat features do you wish Google My Maps had but currently doesn’t?\nAre there any limitations that consistently get in your way, or anything you’ve had to work around?\n\nWhether it’s better collaboration tools, more customization options, data handling improvements, or anything else — I’d love to hear your thoughts. I’m just trying to get a better understanding of what people actually want or need in a custom mapping tool.\n\nThanks in advance!\n\n\n", "author": "BadPutrid2046", "created_time": "2025-05-23T09:45:17", "url": "https://reddit.com/r/GoogleMaps/comments/1ktfhmh/what_features_do_you_wish_google_my_maps_had/", "upvotes": 17, "comments_count": 40, "sentiment": "neutral", "engagement_score": 97.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktgpmd", "title": "Google Ads Support is a Nightmare", "content": "Does anyone else feel that the quality of support for Google Ads has plummeted. \n\nA couple of years back I used to get actual folks with technical knowhow who tried to fix the issues and succeeded at least 50% of the time.\n\nFor the last 4/5 issues, we have been only getting irrelevant nonsense. I will send them a message saying my Ads are not spending any money. And they come back with recommendations on how to optimise my campaigns.\n\nA recurring issue for us has been that a perfectly running campaign suddenly stops gathering impressions and Google support has been absolutely hopeless at debugging it. ", "author": "Individual-Lab-2008", "created_time": "2025-05-23T11:02:33", "url": "https://reddit.com/r/adwords/comments/1ktgpmd/google_ads_support_is_a_nightmare/", "upvotes": 11, "comments_count": 17, "sentiment": "neutral", "engagement_score": 45.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kthogz", "title": "This will never not continue to blow my mind.", "content": "", "author": "AdolinKholin1", "created_time": "2025-05-23T11:56:51", "url": "https://reddit.com/r/singularity/comments/1kthogz/this_will_never_not_continue_to_blow_my_mind/", "upvotes": 3853, "comments_count": 488, "sentiment": "neutral", "engagement_score": 4829.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktiarq", "title": "Revealed: UK Supermarket Seabass Linked to Devastating Overfishing in Senegal", "content": "", "author": "crustose_lichen", "created_time": "2025-05-23T12:27:32", "url": "https://reddit.com/r/sustainability/comments/1ktiarq/revealed_uk_supermarket_seabass_linked_to/", "upvotes": 120, "comments_count": 2, "sentiment": "neutral", "engagement_score": 124.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktifcv", "title": "The Hidden Revival of Platinum and Palladium", "content": "", "author": "<PERSON><PERSON>-", "created_time": "2025-05-23T12:34:03", "url": "https://reddit.com/r/SecurityAnalysis/comments/1ktifcv/the_hidden_revival_of_platinum_and_palladium/", "upvotes": 18, "comments_count": 0, "sentiment": "neutral", "engagement_score": 18.0, "source_subreddit": "SecurityAnalysis", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktildg", "title": "Tesla Inverters vs Microinveters", "content": "I was recently quoted a system with two Tesla inverters instead of the micro inverters. I asked my rep and he said the Tesla inverters would be much cheaper and work just as well, if not better than the micro inverters because my roof doesn’t get much shade. Which should I go with and why?\n\n", "author": "CompterAnim8", "created_time": "2025-05-23T12:42:22", "url": "https://reddit.com/r/solar/comments/1ktildg/tesla_inverters_vs_microinveters/", "upvotes": 2, "comments_count": 41, "sentiment": "neutral", "engagement_score": 84.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktjn4l", "title": "YouTube promoting racism again.", "content": "When people post comments saying that \"Pakis are criminals\" or immigrants commit more crime, I post government statistics proving them wrong.\n\nYouTube protects the racist comments while deleting the government stats. I don't post a link in the comments because that's not allowed. My comment might be something like:\n\n>\"In the UK, 60 Roma committed crimes in the last 10 years compared to 60 MILLION white Brits.\"\n\nEvery time I post this comment it gets deleted. Proving beyond any doubt that YouTube promotes racism. YouTube promotes racism, pornography to children and gun crime to countries that don't have guns. YouTube is a criminal organisation. I bet this gets downvoted by simps brainwashed by YouTube. Such is the world. 🤷‍♂️", "author": "Advanced-Welcome-928", "created_time": "2025-05-23T13:31:19", "url": "https://reddit.com/r/youtube/comments/1ktjn4l/youtube_promoting_racism_again/", "upvotes": 0, "comments_count": 59, "sentiment": "neutral", "engagement_score": 118.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktk1zu", "title": "Googles Discovery & Display Channels Really Are Utter Trash Traffic Aren't They? So why do we all do performance max campaigns?", "content": "If anyone here, has ever tried to run a standalone campaign on either of the display and/or discovery/demand gen channels, chances are you will see lots of worthless clicks - along with high spend, and astonishingly high cpc's!\n\nAs far as we are concerned, both of these channels, always have been, and likely always will be, completely and utterly useless junk/trash traffic which is worthless to the vast majority of businesses.\n\nEveryone know's that this is the reason Performance Max was created in the first place - so google could easily package up and mix in their shitty junk traffic with the better quality traffic from their search channels - simultaneously raising CPCs across them all.\n\nIsn't it about time google just come clean with this, and stop trying to have us all on - scrap PMAX, and let us all judge the merits and worth of each channel individually. All marketers and CFOs etc need to be able to critically judge the effectiveness of their spend across channels - wasted spend is unacceptable and google should respect this rather than trying to pull the wool over everyone's eyes and attempt to completely manipulate cpc's across different channels.\n\nThe sooner ChatGPT gets going with it's advertising the better - so long as they are more transparent and honest with us, they are bound to win a lot of advertisers over compared to googles sneaky snakey tactics of late.", "author": "ConstructionOdd4862", "created_time": "2025-05-23T13:49:59", "url": "https://reddit.com/r/PPC/comments/1ktk1zu/googles_discovery_display_channels_really_are/", "upvotes": 29, "comments_count": 47, "sentiment": "bullish", "engagement_score": 123.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ktsrlv", "title": "Building a tool to make Google analytics (GA4) somewhat easier to use", "content": "Hey everyone, I’m building a tool that plugs your Google Analytics 4 data right into Slack.\n\nYou just install it, connect your GA4 account, then tag it in any channel and ask things like *“How many new users did we get last week?*” or *“Compare mobile vs desktop conversions for our spring promo.”*\n\nIt pulls the data in real time and drops back a quick summary, optionally with chart in the channel (or DM). You don't have to deal with the GA4 dashboard at all.\n\nWould you use something like this in your Slack workspace? Would love to hear your thoughts. Thanks!", "author": "prous5tmaker", "created_time": "2025-05-23T19:48:11", "url": "https://reddit.com/r/DigitalMarketing/comments/1ktsrlv/building_a_tool_to_make_google_analytics_ga4/", "upvotes": 8, "comments_count": 16, "sentiment": "bearish", "engagement_score": 40.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}]