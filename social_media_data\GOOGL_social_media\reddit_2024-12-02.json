[{"platform": "reddit", "post_id": "reddit_1h4i15b", "title": "I Have This Crazy Idea: Think Airbnb for Toilets — What Do You Think?", "content": "I’ve been brainstorming this startup idea called **\"Find My Loo\"**, and I’d love your honest feedback.\n\nThe problem: Finding clean public toilets can be a nightmare, and sometimes you’re forced to buy something at a cafe or mall just to use theirs.\n\nThe solution: An app where users can book clean toilets at nearby partner businesses (like cafes or shops) for a small fee. Businesses can list their unused toilet stalls on my app, and my company will ensure they meet high hygiene standards. In return, businesses earn a commission for every use, creating a win-win model: users get reliable, clean toilets, and businesses earn extra income from a resource they weren’t monetizing before.\n\nI’m curious:\n\n* Do you think this idea is viable?\n* Would you use an app like this?\n* Would businesses actually partner for something like this?\n\nLooking forward to your thoughts (be as brutal as you guys can be, like is it even viable or nah?)", "author": "Alone_Calendar1117", "created_time": "2024-12-02T00:25:33", "url": "https://reddit.com/r/Entrepreneur/comments/1h4i15b/i_have_this_crazy_idea_think_airbnb_for_toilets/", "upvotes": 176, "comments_count": 309, "sentiment": "bullish", "engagement_score": 794.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4ltro", "title": "Google Maps ruined navigation with non-stop unhelpful \"alerts\"", "content": "I'm in disbelief at this.\n\nI recently took a long road trip, about 16 hours round trip, and started to get annoyed by the extremely frequent \"police are ahead,\" and \"there's a stalled vehicle ahead\" and other similar alerts. Naturally, I opened the settings to disable them, like I would do with any other feature that isn't relevant for me. it turns out, there is no way to disable these alerts.\n\nThe alerts are not only unhelpful 99% of the time, they are actively distracting while trying to drive and also interrupt whatever music or podcasts you're trying to listen to on your road trip. Every 3 minutes I would hear \"ping ping! There's police up ahead\" followed by a dialog box asking me to confirm if what it just told me is even true or not \\*facepalm\\*. On shorter drives to work and what not, this feature didn't bother me that much but on longer drives, where navigation is more often needed, it was nothing short of maddening after a while.\n\nWhy these alerts are unhelpful to me personally:\n\n\"Police up ahead\" - I'm not trying to evade the law - and even if I was I don't need your help, thanks. I choose not to speed to the point of getting a ticket,  therefore I'm not afraid of getting one when I pass a \"speed trap.\" Also, I believe in common sense rules of the road like speed limits since speeding causes accidents and deaths, and therefore don't really support Google trying to help people endanger others lives without being caught.\n\n\"Stalled vehicle ahead\" - These vehicles are always on the shoulder of the road. I've never once found it to be something I needed warning of while driving before these alerts came about. If you're watching the road, which you generally should while driving, then this should not be an issue.\n\nSo, I guess I'm just posting this in the vague hope that someone from Google will see this and realize the product has been tanked.\n\nThe worst part? I tried switching to the other major maps provider as a result of this, and they also have the same feature that also can't be disabled! As I said, I'm in complete disbelief at this decision by both major companies to force the same annoying feature on something so critical as Navigation.\n\nWould be interested to know if others agree, or what your thoughts are.", "author": "JigsawExternal", "created_time": "2024-12-02T03:37:13", "url": "https://reddit.com/r/GoogleMaps/comments/1h4ltro/google_maps_ruined_navigation_with_nonstop/", "upvotes": 69, "comments_count": 31, "sentiment": "bullish", "engagement_score": 131.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4nlq7", "title": "If you're ever feeling down, just remember that even Google is pay-to-win.", "content": "", "author": "VerdantSeamanJL", "created_time": "2024-12-02T05:16:30", "url": "https://reddit.com/r/google/comments/1h4nlq7/if_youre_ever_feeling_down_just_remember_that/", "upvotes": 1, "comments_count": 11, "sentiment": "neutral", "engagement_score": 23.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4nvz4", "title": "Android Police: Google Maps is getting the last thing keeping you on Waze", "content": "", "author": "Snowfish52", "created_time": "2024-12-02T05:34:00", "url": "https://reddit.com/r/technology/comments/1h4nvz4/android_police_google_maps_is_getting_the_last/", "upvotes": 3413, "comments_count": 624, "sentiment": "neutral", "engagement_score": 4661.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4ofqd", "title": "Any Tips for Bypassing AI Detectors Like Copyleaks?", "content": "Hey everyone, I’ve been generating a lot of content using ChatGPT, where I add references and articles and usually work section by section while developing my blog. Despite this, whenever I run my content through Copyleaks or QuillBot AI detection, I always end up with a high AI score.\n\nI’ve tried using multiple prompts and advice from GPT to avoid certain phrases, write in simpler language, and even intentionally add wrong punctuation or grammatical errors to bypass detection. Still, nothing seems to lower the AI score significantly. Ideally, I want my content either not to be detected or, if it is, for the score to be as low as possible. Since I’m looking to automate this process and don’t have time for manual tweaks, I wonder if anyone has found a solution that works. Paid tools are fine, too. Any tips would be greatly appreciated. Honestly, I just want to get rid of this problem :p", "author": "AromaticRange8948", "created_time": "2024-12-02T06:08:06", "url": "https://reddit.com/r/SEO/comments/1h4ofqd/any_tips_for_bypassing_ai_detectors_like_copyleaks/", "upvotes": 2, "comments_count": 78, "sentiment": "bearish", "engagement_score": 158.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4pfjd", "title": "Google to Ban Payday Lending Ads, Calling Industry 'Harmful'", "content": "", "author": "thinkB4WeSpeak", "created_time": "2024-12-02T07:14:23", "url": "https://reddit.com/r/economy/comments/1h4pfjd/google_to_ban_payday_lending_ads_calling_industry/", "upvotes": 6, "comments_count": 5, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4rbf2", "title": "Google losing ground to TikTok and Instagram for searches", "content": "I came across an interesting observation recently:\n\nPeople over 30 still start their searches on Google and confirm their findings on YouTube.\n\nMeanwhile, those under 30 are increasingly starting searches on TikTok and validating them on Instagram.\n\nThis trend feels like a seismic shift in how we access information, especially for marketers, educators, and anyone who creates online content. It makes me wonder:\n\nAre younger generations moving away from traditional search engines because social platforms feel more engaging or personalized?\n\nWhat does this mean for businesses that have traditionally relied on SEO to drive traffic?\n\nHow do we, as creators or marketers, adapt to this new landscape?\n\nLet’s open this up:\n\nIf you’re over 30, do you still default to Google for everything?\n\nIf you’re under 30, do TikTok and Instagram feel more “searchable” or useful than Google?\n\nAnd for everyone: How does this impact how we present or consume information online?\n\nWould love to hear your thoughts. Is this the beginning of Google’s decline or just the evolution of search behavior?", "author": "Elegant-Fix8085", "created_time": "2024-12-02T09:36:13", "url": "https://reddit.com/r/marketing/comments/1h4rbf2/google_losing_ground_to_tiktok_and_instagram_for/", "upvotes": 91, "comments_count": 63, "sentiment": "bearish", "engagement_score": 217.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4smhz", "title": "How I made a high tech salary in my first selling month", "content": "For over 7 years I worked as a full-stack developer, helping other companies bring their ideas to life. But one day, I thought *“Why not try making my own dream come true?”*. That’s when I decided to quit my job and start my own journey to becoming an entrepreneur.\n\nAt first, it wasn’t easy. I didn’t make any money for months and had no idea where to start. I felt lost. Then, I decided to focus on something popular and trending. AI was everywhere, and ChatGPT was the most used AI platform. So I looked into it and I found the OpenAI community forum where people had been asking for features that weren’t being added.\n\nThat gave me an idea. Why not build those features myself? I created a Chrome extension and I worked on some of the most requested features, like:\n\n* Downloading the advanced voice mode and messages as MP3\n* Adding folders to organize chats\n* Saving and reusing prompts\n* Pinning important chats\n* Exporting chats to TXT/JSON files\n* Deleting or archiving multiple chats at once\n* Making chat history searches faster and better\n\nIt took me about a week to build the first version, and when I published it, the response was incredible. People loved it! Some even said things like, *“You’re a lifesaver!”* That’s when I realized I had something that could not only help people but also turn into a real business.\n\nI kept the first version free to see how people would respond. Many users have been downloading my extension, which prompted Chrome to review it to determine if it qualified for the featured badge. I received the badge, and it has significantly boosted traffic to my extension ever since.\n\nAfter all the positive feedback, I launched a paid version one month ago. A few minutes after publishing it, I made my first sale! That moment was so exciting, and it motivated me to keep going.\n\nI already have over 4,000 users and have made more than $4,500 in my first selling month. I’ve decided to release 1-2 new features every month to keep improving the extension based on what users ask for.\n\nI also created the same extension for Firefox and Edge users because many people have been asking for it!\n\nI also started a Reddit community, where I share updates, sales, discount codes, and ideas for new features. It’s been awesome to connect with users directly and get their feedback.\n\nAdditionally, I’ve started working on another extension for Claude, which I’m hoping will be as successful as this one.\n\n**My message to you is this: never give up on your dreams. It might feel impossible at first, but with patience, hard work, and some creativity, you can make it happen.**\n\nI hope this inspires you to go after what you want. Good luck to all of us!\n\n", "author": "Ok_Negotiation_2587", "created_time": "2024-12-02T11:11:27", "url": "https://reddit.com/r/startups/comments/1h4smhz/how_i_made_a_high_tech_salary_in_my_first_selling/", "upvotes": 297, "comments_count": 70, "sentiment": "bearish", "engagement_score": 437.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4vtsj", "title": "Tried to gamble my way out of debt - Didn’t work :(", "content": "Well here it goes. \n\nAbout a year ago I dumped about 5k into a company that I may or may not have had private knowledge of. I thought I was so smart, turns out that less than a month later all my shares are worthless because the company filed for bankruptcy. \n\nFast forward a couple months and my position was eliminated and I have been searching for jobs but been unemployed ever since. That 5k was a big chunk of my savings and with being unemployed and having bills to pay I quickly ran out of cash. I racked up about 12,000 in credit card debt and needed money fast. \n\nSo naturally, I started gambling. I gambled by using this same credit card on some sketchy online casino, I didn’t deposit much at once, usually only $100 or so, but man I realized I had a gambling addiction once I spent over 4k on this site. I was making the money back but then I was chasing my losses and lost it all. \n\nThen autopay resumed on my card and tried to charge my entire balance which I didn’t have and Amex canceled my card. I was still making my monthly payments before that. \n\nSo now I’m 16k in debt on this card, 3k on another, and have about $100 to my name. I sold my computers and guns and anything worth money but now I don’t know how I am going to pay my bills this month. \n\nGuys, please be careful, be honest with yourselves, casino gambling and options gambling are both just as dangerous. You have to limit yourself or else you are a few bad days away from being like me. \n\nI don’t know what I’m gonna do anymore but I thought I’d leave a warning for other degenerate gamblers like me. \n\n", "author": "whitelightning096", "created_time": "2024-12-02T14:11:49", "url": "https://reddit.com/r/wallstreetbets/comments/1h4vtsj/tried_to_gamble_my_way_out_of_debt_didnt_work/", "upvotes": 26242, "comments_count": 2471, "sentiment": "bearish", "engagement_score": 31184.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4wpez", "title": "Is it okay if I hate the pixel 8", "content": "Like I've got a pixel for the first time and was excited about all the good stuff I heard about it but just absolutely hated it after 6 hours which is why I am returning it (I made sure it looked as untouched as possible in the packaging).\n\nLike somehow I feel like it is bad that I completely hate everything on the Pixel 8 (except for the bubbly clock on the lockscreen, that one is soo CUTE). I hate the sound it makes, I hate the ui the font the way a protective glass doesn't really stick on it. I find the camera powerful but I actually hate the images it does (and since I own real cameras, the smartphone one doesn't have to be natural or accurate, I want it to feel flashy).\n\nLike my lifelong I feel while I saw many people use Samsung, I knew allot of friends who hated the device and called them bad and bloated but to my surprise I seem to love them and find them pretty much perfect in most regards, I also used to enjoy Sony a big while back and people seem to be negatively against those too (except my brother who still rocks this brand).\n\nIt seems to me that the brands people hate or recommend really matter little when it comes to your personal choice. But I feel kinda guilty since I know many people LOVE the pixel to say that I hate it.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-02T14:53:20", "url": "https://reddit.com/r/GooglePixel/comments/1h4wpez/is_it_okay_if_i_hate_the_pixel_8/", "upvotes": 0, "comments_count": 20, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4wu89", "title": "Google Ads cannot verify \"University\" or \"College\" as organisation/business account", "content": "This is both hilarious and frustrating. (sorry, mostly just a rant)\n\nWe are running our own ads for our international BSc environmental science programme on Google Ads. We had to verify ourselves as business due to new Google Ad policy this year.\n\nWhen we got to the question \"What's your industry (or industries)?\", there is a huge list of options, but it does not include \"university\", \"education\", \"school\" or \"college\"! The Google Ad team somehow forget that these seem to exist and can run ads as well...\n\nEventually I contacted a Google Ad helpdesk employee and they could not solve the issue. I simply selected \"government agency\" as that seemed to be the best match.\n\nBut seriously, how can they forget about this?", "author": "ESSETavans", "created_time": "2024-12-02T14:59:23", "url": "https://reddit.com/r/adwords/comments/1h4wu89/google_ads_cannot_verify_university_or_college_as/", "upvotes": 3, "comments_count": 1, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h4x9sv", "title": "Seeking your thoughts on my Google investment", "content": "Hey fellow investors,\n\nI’d like to hear your opinions on my current investment. Two weeks ago, I bought approximately 600 shares of GOOG at an average price of around $168, which now makes up 80% of my portfolio.\n\nI’m fully aware that this is not an ideal diversification strategy, but I struggle to see where this investment could go wrong. In my view, the downside risk might be around 10%. If the stock were to drop by 10%, we’d be looking at a P/E ratio of 20 and a forward P/E of 17 - something that has rarely happened with Google.\n\nSeveral factors keep me optimistic:\n\n* **Ad revenue** is likely to be strong in January due to the holidays and election-related spending.\n* **Google Cloud** is experiencing rapid growth.\n* **Waymo** is expanding quickly, and user feedback has been outstanding.\n* The company’s vast data resources and well-integrated ecosystem provide a solid foundation for continued success.\n\nOn top of that, Google is set to pay a dividend next week, which reinforces my confidence in the stock. And massive buybacks. \n\nWhat are your thoughts on this?\n\nLooking forward to hearing from you!", "author": "Former_Drawer6732", "created_time": "2024-12-02T15:17:49", "url": "https://reddit.com/r/ValueInvesting/comments/1h4x9sv/seeking_your_thoughts_on_my_google_investment/", "upvotes": 61, "comments_count": 89, "sentiment": "bullish", "engagement_score": 239.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h513bc", "title": "How can I deploy my web app (django+react)? ", "content": "For the past few months, I’ve been working on a web app—a Reddit clone—using Django for the backend and React for the frontend. The app focuses on stock market tickers, allowing users to post and discuss specific securities, similar to how Reddit functions.\n\nThis is my first time building something like this, and I don’t have a background in computer science. Now, I’m ready to take the next step and deploy my app, but I have no idea where to start.\n\nI’ve heard about AWS, Azure, and other hosting platforms, but I’m not sure which one would be best for a beginner like me. I’d really appreciate any guidance, resources, or tutorials (e.g., YouTube videos, step-by-step guides) that can help me with deployment.\n\nThanks in advance for your help!\n\nhttps://preview.redd.it/79mm15wu4h4e1.png?width=3014&format=png&auto=webp&s=e676281a8c0e8b23515a573c5bb25c1b9bed50cc\n\nhttps://preview.redd.it/mjqay3wu4h4e1.png?width=3018&format=png&auto=webp&s=df0a450bd57ecfe705b28d6ec4f78ccd3aaf7a7a\n\nhttps://preview.redd.it/wx849twu4h4e1.png?width=3014&format=png&auto=webp&s=d308b2f51c97e8d1755c6bd8ac4252b983274d39\n\nhttps://preview.redd.it/630pa4wu4h4e1.png?width=3022&format=png&auto=webp&s=6725a184cba40323e957a0c06ff5f45c55fd9273\n\nhttps://preview.redd.it/ybuvi4wu4h4e1.png?width=3016&format=png&auto=webp&s=3533b390dc533d67bd19599896c39bb47e5f184d\n\n", "author": "pussydestroyerSPY", "created_time": "2024-12-02T17:56:09", "url": "https://reddit.com/r/webdev/comments/1h513bc/how_can_i_deploy_my_web_app_djangoreact/", "upvotes": 0, "comments_count": 2, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h51q3l", "title": "GOOGLE OVERVALUED", "content": "This is my first ever stock analysis, so I want as much feedback and constructive criticism as possible. Please also tell me if I'm wrong about anything. Numbers are taken between 2016 and 2023 from TIKR stock screener.\n\nGoogle had an average growth of revenue of 19% yearly from 2016-2023, as well as a pretty stable profit margin of 60% every year. The revenue growth was good but the profit margin is outstanding as expected of a company that doesn't have to produce anything physical as a main form of income.\n\nGoogle also had a mildly growing operating margin starting at 26% in 2016 and being 32% in 2023, which again is an amazing margin. They have also had a strong growth in earnings from continuing operations, which ends up being the same as their net income.\n\nTheir CapEx is also growing, though not excessively, but I do not know what kind of ventures this includes.\n\nThey are also paying of more and more debt which of course is good, and they could as of 2023 pay of their long term debt over 10 times just using their Cash & Short-term Investments (They also have a Current Ratio of almost 2 which again is great). This, combined with the fact that their retained earnings were growing at an average of 10% over these years shows that their financial health is excellent. Their Cash & Short-term Investments is also growing yearly, and they have started paying dividends this year as a result, which also is good.\n\nGoogle has also bought back more and more stock each year, which shows that they believe in themselves, and their Total Assets are growing both strongly and evenly. The CEO also has a lot of stock himself, meaning that it's in his best interest that the company does well and gives some insurance of good leadership.\n\nIt also shouldn't have to be mentioned that they have a great moat as they essentially have a monopoly on browsing.\n\nThey also have 25 dollars earned per share, giving them a 25-1 return on cash ratio which is excellent, and their debt to equity ratio of around 0.3 is also great.\n\nOne thing that can be seen as a problem is that Googles Cash from Operations is growing at a slower rate than their Net Income, which means that they aren't actually collecting all the payments from the sales they record. They also have a low Cash Return of around 3.5%.\n\nOver to the intrinsic value, my calculations (which might very well be wrong as this is my first analysis) tell me that Googles terminal value in 2030 will be 829,168MM (679,886MM discounted at 15%) assuming their Free Cash Flow continues at an average of 15% per year. This means that the intrinsic value I've calculated comes out at 1,359,772MM. I made an intrinsic value range from 90% to 110% of this number and added Cash & Short Term Marketable Securities to the values, giving the intrinsic value of Google a range between 1,334,794MM and 1,606,794MM. Adding a margin of safety of 20% (as I assume nothing will go too wrong for Google) the range becomes 1,067,835MM to 1,285,435MM. The total Enterprise Value of Google divided by the Intrinsic Value on either side of the range gives us a ratio of 1.92-2.32, meaning that people are currently paying around twice as much as they should for google stock. This can kinda be confirmed by its current P/E ratio of 22.4 compared to its earnings growth of 19% on average, meaning that their earnings aren't keeping up with their stock price.\n\nAs stated, this is my first analysis so I want as much feedback as you guys can give me so I improve further :)\n\n  \nEdit: This is also an industry I don't know much about, I just needed a company to start practicing my analyzing skills, so there also might be outside factors and company plans I'm unaware of.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-02T18:21:14", "url": "https://reddit.com/r/ValueInvesting/comments/1h51q3l/google_overvalued/", "upvotes": 0, "comments_count": 34, "sentiment": "bullish", "engagement_score": 68.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h55tqh", "title": "Is google pay as safe as paypal?", "content": "Which one has higher odds of being hacked or used in a bad way by scammers", "author": "pomme<PERSON><PERSON><PERSON><PERSON>er", "created_time": "2024-12-02T21:07:45", "url": "https://reddit.com/r/privacy/comments/1h55tqh/is_google_pay_as_safe_as_paypal/", "upvotes": 2, "comments_count": 7, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1h59b2y", "title": "Adgroups: Automated Bidding, Will this Work?", "content": "I have a TROAS Bidding Strategy, currently with a single campaign with thousands of products, across different brands. I am thinking of the following, but will this do anything with an automated strategy? \n\n**Campaign - Google Shopping, TROAS Bidding Strategy**\n\n**AdGroups**\n\nAll Products\n\nBrand Name 1\n\nBrand Name 2\n\nBrand Name 3\n\n  \nMy strategy is to run a script on the brand Ad Groups, let's say \"Tesla\".  Search terms that do not contain the name of the Ad Group (non branded) would become negatives of that Ad Group. \n\nThe idea is that over time, the Ad Groups should perform better as the non branded terms are taken out. However they can still get action in the Ad Group with All Products. \n\nMy question, will this even matter or would the automated bidding strategy take this into consideration? if the Adgroups start performing better wouldn't it funnel that brand traffic? \n\nFull disclosure I know this can be accomplished with Campaign Priorities and have done it in the past, but it seems with automated strategies it just doesn't work like it used to.", "author": "Antique-Cartoonist-5", "created_time": "2024-12-02T23:34:48", "url": "https://reddit.com/r/PPC/comments/1h59b2y/adgroups_automated_bidding_will_this_work/", "upvotes": 1, "comments_count": 2, "sentiment": "bearish", "engagement_score": 5.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}]