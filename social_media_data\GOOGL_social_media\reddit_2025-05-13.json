[{"platform": "reddit", "post_id": "reddit_1klcg9c", "title": "Google Ads Fake Leads", "content": "Has anyone experienced Google Ads tracking being way off? With Google forms, I’ve never received one accurate lead. The people who do answer the phone say they never submitted their info. Currently running a performance max ad, received two conversions but did not receive the calls during the day. Google Analytics also always has different data, don’t know if I should believe and keep on pouring money into Google Ads.", "author": "Aggressive_Can4667", "created_time": "2025-05-13T03:48:04", "url": "https://reddit.com/r/DigitalMarketing/comments/1klcg9c/google_ads_fake_leads/", "upvotes": 1, "comments_count": 13, "sentiment": "bullish", "engagement_score": 27.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klh3px", "title": "What stocks are you guys buying right now?", "content": "I am currently buying Google, Amazon, Waste Management and Visa. I am curios to see what you guys have been buying as well.", "author": "Regular_Newspaper990", "created_time": "2025-05-13T08:52:04", "url": "https://reddit.com/r/dividends/comments/1klh3px/what_stocks_are_you_guys_buying_right_now/", "upvotes": 85, "comments_count": 253, "sentiment": "bullish", "engagement_score": 591.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klhi9j", "title": "Google banned me for local guide program", "content": "Hi, so I checked my profile a few days back and found that I can't enter the local guide program. When I emailed Google and asked for a reason and to reverse it, they simply refused and said it was permanent, and no reason was given. I fulfilled all their terms and conditions, never paid-posted (I hardly have any reviews and posts), but still got banned.\n\nWhat could be the reason? Did anybody else face this issue?", "author": "rickypro03", "created_time": "2025-05-13T09:20:31", "url": "https://reddit.com/r/GoogleMaps/comments/1klhi9j/google_banned_me_for_local_guide_program/", "upvotes": 3, "comments_count": 20, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klmfw5", "title": "[P] Content Moderation for AI Agents using OpenAI's API, Google ADK, and MCP", "content": "Recently I found that OpenAI's Moderation API is free. I am very interested in AI security, \n\n so I created a project that uses this API via Google ADK and Model Context Protocol (MCP)\n\n to share with GenAI community.\n\nAll code is available on GitHub: https://github.com/alexey-tyurin/ai-agent-mcp.\n\nFeel free to ask questions here.", "author": "alex<PERSON><PERSON>", "created_time": "2025-05-13T13:50:23", "url": "https://reddit.com/r/MachineLearning/comments/1klmfw5/p_content_moderation_for_ai_agents_using_openais/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klodia", "title": "Just hit $300k!", "content": "Sorry for another annoying milestone post (though there haven't been as many recently - wonder why? /s, though the floodgates may be opening on those).\n\nThese are helpful for me I think too to memorialize where I was at a moment in time. Hopefully someone in the community in a similar spot can take something away from this, and I always appreciate all feedback and advice/perspectives from this community, which has been wonderful for me to learn about and follow. \n\nI  want to keep a habit of posting here at milestones so I can time-stamp my journey :)\n\n  \nI also don't really have anyone I feel comfortable sharing the financial milestone with so wanted to anonymously celebrate with the FIRE community - reddit and this sub in particular has been a great source of motivation for me, so thank you all!\n\n\n\nNW Breakdown as follows:\n\n401k: $55k.\n\nRoth IRA: $48k, mostly in Google and BRK/B, though I now have sizeable positions in Amazon and QQQ here (highly tech-concentrated, I know. I am comfortable with the risk profile). \n\nBrokerage: Broad-based ETFs (QQQ, VTI mostly): $160k. This does include some individual stock positions, (AAPL, MSFT, BRKB, TOST, etc), but is majority led by ETFs, as mentioned. This group does include \\~$17k of cash, money market, and bond ETFs.\n\nBTC: $28k. \\*This is marked \\~15% below market value.\n\nPrivate Commercial Real Estate Investments (marked at cost): $15k. \\*Note this is lower than at my last post, one of two properties was sold and thus that $5k of cost basis has been reduced to 0, though I realized a cash gain. \n\nTotal = \\~$306k.\n\nI am 27, earn about $150k cash comp, rent, and spend more than I probably should ( \\~$60k/yr spend in VHCOL, I end up saving \\~$2700-$3000/month, 401k contributions included). And I always save my full annual bonus (\\~$15k after tax/401k). Would love any advice people have on how to really set myself up for accelerating growth as I enter really important career/saving years. Would love to eventually retire in late 40's/early 50's with $100k+ annual spend.\n\nA new update here is I may be taking a new job with a paycut - from \\~150k to \\~130k in year 1. But I think the opportunity could set me up well for future growth from a skillset and experience perspective, and could lead to significant financial gains down the road. Time will tell on that, I suppose! Would love anybody's thoughts or anecdotes there as well. \n\nThanks so much in advance! Next post, at 400k!\n\n", "author": "SOLH21", "created_time": "2025-05-13T15:09:14", "url": "https://reddit.com/r/Fire/comments/1klodia/just_hit_300k/", "upvotes": 55, "comments_count": 33, "sentiment": "bullish", "engagement_score": 121.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klrhyz", "title": "It’s official: Google reveals Material 3 Expressive, Android's next major design overhaul!", "content": "", "author": "Xisrr1", "created_time": "2025-05-13T17:12:21", "url": "https://reddit.com/r/GooglePixel/comments/1klrhyz/its_official_google_reveals_material_3_expressive/", "upvotes": 795, "comments_count": 173, "sentiment": "neutral", "engagement_score": 1141.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klruh9", "title": "Google wants to make stolen Android phones basically unsellable", "content": "", "author": "FragmentedChicken", "created_time": "2025-05-13T17:25:54", "url": "https://reddit.com/r/Android/comments/1klruh9/google_wants_to_make_stolen_android_phones/", "upvotes": 2440, "comments_count": 247, "sentiment": "bearish", "engagement_score": 2934.0, "source_subreddit": "Android", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klswh4", "title": "Google's Chief Scientist <PERSON> says we're a year away from AIs working 24/7 at the level of junior engineers", "content": "", "author": "MetaKnowing", "created_time": "2025-05-13T18:06:36", "url": "https://reddit.com/r/artificial/comments/1klswh4/googles_chief_scientist_jeff_dean_says_were_a/", "upvotes": 493, "comments_count": 257, "sentiment": "neutral", "engagement_score": 1007.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klu4ns", "title": "[Request for Advice] Efficient Li-ion 3S Charging and Powering Setup for a 12V / 5A Circuit", "content": "Hi everyone,\n\nI'm currently working on a DIY project and setting up, for the first time, a power system based on a **Li-ion 3S2P battery pack**. My circuit requires 12V with peaks up to 5A (but most of the time it draws around 2A or less).\n\n**Battery:** [Panasonic NCR18650B 3.6V](https://www.google.com/search?q=Panasonic+NCR18650B+3.6V&rlz=1C1GCEU_frFR1161FR1161&oq=Panasonic+NCR18650B+3.6V&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIICAEQABgWGB4yCAgCEAAYFhgeMgcIAxAAGO8FMgoIBBAAGIAEGKIEMgoIBRAAGIAEGKIEMgcIBhAAGO8FMgYIBxBFGDzSAQczMDNqMGo3qAIAsAIA&sourceid=chrome&ie=UTF-8)\n\n**BMS:** [HXYP-3S-BM25](https://www.google.com/search?q=HXYP-3S-BM25&rlz=1C1GCEU_frFR1161FR1161&oq=HXYP-3S-BM25&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIHCAEQABjvBTIHCAIQABjvBTIKCAMQABiABBiiBDIHCAQQABjvBTIHCAUQABjvBTIGCAYQRRg9MgYIBxBFGD3SAQc1NjdqMGo3qAIAsAIA&sourceid=chrome&ie=UTF-8)\n\nThe circuit is powered via a boost converter set to output 12V.\n\nI'm struggling to find a safe and reliable way to recharge the batteries while simultaneously powering the circuit, without exceeding the capabilities of the charger or compromising battery health. Here are the two scenarios I'm considering, but neither feels fully satisfying, and I'd love your input (especially if you know of any useful off-the-shelf modules):\n\n**Option 1 – Basic parallel wiring**\n\nA basic setup where:\n\n* A 12.6V 2A CC/CV Li-ion charger\n* The battery pack\n* The circuit's input (via buck converter) are all wired in parallel.\n\nThis is the simplest configuration, but I’m concerned that:\n\n* The regulator might impose a higher load too early, forcing the charger into CV mode prematurely.\n* The charger is sized for battery charging, not for powering the whole system (5A max load, 2A average), which could overload it.\n\n**Option 2 – Using a DC power supply +** [XL4015 ](https://www.google.com/search?q=XL4015&sca_esv=cefa0a7e75bc11f8&rlz=1C1GCEU_frFR1161FR1161&sxsrf=AHTn8zqN_MRLh1yMykzaqJ93w4YN2OLimQ%3A1747162387803&ei=E5UjaKLmMKWvkdUP3tDVgAM&ved=0ahUKEwjiu_WLj6GNAxWlV6QEHV5oFTAQ4dUDCBA&uact=5&oq=XL4015&gs_lp=Egxnd3Mtd2l6LXNlcnAiBlhMNDAxNTIEECMYJzIKECMYgAQYJxiKBTIFEAAYgAQyBRAAGIAEMgUQABiABDIIEAAYgAQYywEyCBAAGIAEGMsBMggQABiABBjLATIIEAAYgAQYywEyCBAAGIAEGMsBSIoEUNQCWNQCcAF4AJABAJgBMaABMaoBATG4AQPIAQD4AQL4AQGYAgKgAjrCAggQABiwAxjvBcICCxAAGIAEGLADGKIEmAMAiAYBkAYFkgcBMqAH_gayBwExuAc2&sclient=gws-wiz-serp)**module**\n\nThis would involve:\n\n* A 14V or 16V 4A DC power supply (slightly above 12.6V to give the CC/CV module enough headroom)\n* A DC-DC CC/CV module (like [XL4015](https://www.google.com/search?q=XL4015&sca_esv=cefa0a7e75bc11f8&rlz=1C1GCEU_frFR1161FR1161&sxsrf=AHTn8zqN_MRLh1yMykzaqJ93w4YN2OLimQ%3A1747162387803&ei=E5UjaKLmMKWvkdUP3tDVgAM&ved=0ahUKEwjiu_WLj6GNAxWlV6QEHV5oFTAQ4dUDCBA&uact=5&oq=XL4015&gs_lp=Egxnd3Mtd2l6LXNlcnAiBlhMNDAxNTIEECMYJzIKECMYgAQYJxiKBTIFEAAYgAQyBRAAGIAEMgUQABiABDIIEAAYgAQYywEyCBAAGIAEGMsBMggQABiABBjLATIIEAAYgAQYywEyCBAAGIAEGMsBSIoEUNQCWNQCcAF4AJABAJgBMaABMaoBATG4AQPIAQD4AQL4AQGYAgKgAjrCAggQABiwAxjvBcICCxAAGIAEGLADGKIEmAMAiAYBkAYFkgcBMqAH_gayBwExuAc2&sclient=gws-wiz-serp)) between the supply and the battery\n* Diodes to isolate the battery and the circuit from each other during charging\n\nIn this scenario:\n\n* The DC supply powers the circuit directly.\n* The battery is recharged through the XL4015.\n* Power routing is handled via diodes to avoid backflow and battery drain during charging.\n\n**Option 3 – USB-C PD Charging Module**\n\nI'm exploring [USB-C PD](https://www.google.com/search?q=3s+USB+PD&sca_esv=bdb835347b1fb0e4&rlz=1C1GCEU_frFR1161FR1161&sxsrf=AHTn8zqZK-JN5aF7kSZJrvb--zURRcfSDQ%3A1747164287786&ei=f5wjaNzgL5mQkdUPq4PCmQw&ved=0ahUKEwicnPOVlqGNAxUZSKQEHauBMMMQ4dUDCBA&uact=5&oq=3s+USB+PD&gs_lp=Egxnd3Mtd2l6LXNlcnAiCTNzIFVTQiBQRDIGEAAYFhgeMgYQABgWGB4yBhAAGBYYHjIGEAAYFhgeMgYQABgWGB4yBhAAGBYYHjIGEAAYFhgeMgYQABgWGB4yCBAAGIAEGKIEMgUQABjvBUjnFlDHD1iHFXACeACQAQCYAU2gAcYCqgEBNrgBA8gBAPgBAZgCCKAC3wLCAgsQABiABBiwAxiiBMICCBAAGLADGO8FwgINEC4YgAQYExjHARivAcICBxAAGIAEGBPCAggQABiABBjLAcICAhAmmAMAiAYBkAYFkgcBOKAHiyuyBwE2uAfbAg&sclient=gws-wiz-serp) bidirectional charging modules with multiple ports (Type-C and Type-A). Some support up to 12V@5A, which could be ideal for both charging a 3S battery and powering my circuit.\n\n* I'm unsure whether these modules handle simultaneous charging and discharging reliably.\n* I'm considering using a [USB-C PD trigger module](https://www.google.com/search?q=trigger+usb+c&sca_esv=cefa0a7e75bc11f8&rlz=1C1GCEU_frFR1161FR1161&sxsrf=AHTn8zqwrLUsbkMlsNVr_4Au2b6fnhV75w%3A1747164139656&ei=65sjaMnvJ7qhkdUPq4SeiQE&ved=0ahUKEwiJkaLPlaGNAxW6UKQEHSuCJxEQ4dUDCBA&uact=5&oq=trigger+usb+c&gs_lp=Egxnd3Mtd2l6LXNlcnAiDXRyaWdnZXIgdXNiIGMyBhAAGBYYHjIGEAAYFhgeMgYQABgWGB4yBhAAGBYYHjIGEAAYFhgeMgYQABgWGB4yBhAAGBYYHjIGEAAYFhgeMgYQABgWGB4yBhAAGBYYHkjeG1D_BViuE3ABeAGQAQCYAUOgAcMFqgECMTO4AQPIAQD4AQGYAg6gAoMGwgIKEAAYsAMY1gQYR8ICChAjGIAEGCcYigXCAgwQIxiABBgTGCcYigXCAgsQABiABBixAxiDAcICEBAAGIAEGLEDGEMYgwEYigXCAg4QLhiABBixAxjRAxjHAcICCxAuGIAEGLEDGIMBwgIREC4YgAQYsQMY0QMYgwEYxwHCAggQABiABBixA8ICBBAjGCfCAgoQLhiABBhDGIoFwgIKEAAYgAQYQxiKBcICERAuGIAEGLEDGMcBGI4FGK8BwgIOEC4YgAQYxwEYjgUYrwHCAg0QABiABBixAxhDGIoFwgINEC4YgAQYsQMYQxiKBcICDRAAGIAEGLEDGEYY_wHCAggQLhiABBixA8ICBRAuGIAEwgIFEAAYgATCAgsQLhiABBjRAxjHAcICCBAAGIAEGMsBwgIIEAAYFhgKGB6YAwCIBgGQBgWSBwIxNKAH564BsgcCMTO4B_0F&sclient=gws-wiz-serp) on the output side to request 12V@5A, but I'm not sure if this is safe or could interfere with the module's internal charging logic.\n\nHonestly, I’m surprised I haven’t found a more “ready-made” solution, like a single module that handles both DC (through USB-C, for example) charging and simultaneous load output. Maybe I’m overlooking something obvious?\n\nAny advice, ideas, or module suggestions would be greatly appreciated!\n\nThanks in advance 🙏", "author": "HonestConsequence224", "created_time": "2025-05-13T18:54:09", "url": "https://reddit.com/r/batteries/comments/1klu4ns/request_for_advice_efficient_liion_3s_charging/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klv071", "title": "Is this the beginning of the end for Google Search?", "content": "I’ve been thinking a lot about how search behavior is shifting, especially with AI tools and social platforms becoming go-to sources for information. I ended up writing something about it to explore the idea a bit deeper.\n\nI came to the conclusion that the gist isn’t that Google is dead, but that its role is changing:\n\t•\tPeople are skipping search entirely in favor of AI tools like ChatGPT and Grok, Tiktok, Reddit…\n\t•\tGoogle itself is changing, focusing more on authoritative sources and less on indexing the whole web.\n\t•\tAs a result, creators and websites may find it harder to get discovered through traditional SEO.\n\nFeels like we’re heading into a new era where search is more personalized, fragmented, and conversational.\n\nWhat do you think the future of Google Search looks like from here?\n\nHere is the link to the article I wrote: https://open.substack.com/pub/archiveinvest/p/the-end-of-google-search?r=4kv2ev&utm_medium=ios", "author": "ArchiveInvest", "created_time": "2025-05-13T19:29:24", "url": "https://reddit.com/r/ValueInvesting/comments/1klv071/is_this_the_beginning_of_the_end_for_google_search/", "upvotes": 0, "comments_count": 14, "sentiment": "neutral", "engagement_score": 28.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1klw2wx", "title": "Left a google review - owner called my employer.", "content": "As title states, I left a 1 star review of a business, in response the business owner found my place of employment, called it and told another employee to give me a message saying delete the review or he will leave my employer 50 1 star reviews as retaliation. What do I do about this? It sounds psychotic to track someone down over a review. \n\nEdit: just a couple clarifying points, my employer is fully aware of the situation, I’m not in any trouble, and the call he made was recorded, all our company lines let people know the usual “all calls are recorded for quality and assurance purposes”. The company was a contractor that I had called, so he knew my name which doesn’t take rocket science to link to a google review with the same first name, he does not know where I live though so not too worried about that.\n\n", "author": "Foreign-Wealth4642", "created_time": "2025-05-13T20:12:25", "url": "https://reddit.com/r/google/comments/1klw2wx/left_a_google_review_owner_called_my_employer/", "upvotes": 2883, "comments_count": 258, "sentiment": "bullish", "engagement_score": 3399.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}]