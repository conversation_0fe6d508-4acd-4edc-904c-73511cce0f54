{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-01-06", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Army to Microsoft ", "content": "Hi everyone! I’m getting out of the army soon and I’d really like to get employed with Microsoft. I’m currently obtaining my net and sec+ certs and my background is 3 years as an army intelligence mission manager for a 3 letter government agency. What I’d really like to know is what jobs at Microsoft I should be looking for. From what I’ve seen entry level positions are few and far between so if anyone has any recommendations as to how I can make myself stand out and what I should be looking for that would be awesome. Thanks!", "created_time": "2025-01-06T01:10:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 59.0, "upvotes": 41, "num_comments": 0, "subreddit": "unknown", "author": "Donaldthecriminal", "url": "https://reddit.com/r/microsoft/comments/1humvtj/army_to_microsoft/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Pros and cons of SWE and PM at Microsoft?", "content": "I have to opportunity to go down either path and would love to hear about the work, WLB, promotions, career growth, and anything else that might be helpful to me making a decision. Thank you!", "created_time": "2025-01-06T06:25:44", "platform": "reddit", "sentiment": "bullish", "engagement_score": 30.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "tetracell_", "url": "https://reddit.com/r/microsoft/comments/1husrr0/pros_and_cons_of_swe_and_pm_at_microsoft/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Migrating GoDaddy Mail Services to Microsoft Outlook", "content": "I'm looking to migrate my GoDaddy mail services to Microsoft Outlook(using Microsoft for Startups credit) and could use some guidance. Does anyone have experience with this process or know the best way to go about it?\n\nThanks in advance.", "created_time": "2025-01-06T09:21:14", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Main_Helicopter9096", "url": "https://reddit.com/r/microsoft/comments/1huv5wk/migrating_godaddy_mail_services_to_microsoft/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Internal Hiring Bias", "content": "I'm an internal employee and I'm applying for roles within MSFT. I love the company and the culture but there seems to be an internal hiring bias and I'm witnessing it to be easier to leave the company and come back to the role you want to be at. I want to be wrong, am I?", "created_time": "2025-01-06T13:59:34", "platform": "reddit", "sentiment": "neutral", "engagement_score": 24.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Zestyclose_Depth_196", "url": "https://reddit.com/r/microsoft/comments/1huzmvy/internal_hiring_bias/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Microsoft Bing shows misleading Google-like page for 'Google' searches", "content": "", "created_time": "2025-01-06T14:24:00", "platform": "reddit", "sentiment": "neutral", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hv05hm/microsoft_bing_shows_misleading_googlelike_page/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Microsoft multilingual glossary?", "content": "I use different language packs at home and at work. Is there a reference somewhere, where I can check the specific translation Microsoft uses for important words in their terminology? (an example : what is the 'official' French equivalent for \"Windows product key\"?)", "created_time": "2025-01-06T16:42:29", "platform": "reddit", "sentiment": "bearish", "engagement_score": 11.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Anycauli", "url": "https://reddit.com/r/microsoft/comments/1hv3bq4/microsoft_multilingual_glossary/", "ticker": "MSFT", "date": "2025-01-06"}, {"title": "Gifting Minecraft to a Mongolian friend from the US", "content": "\nHey y’all, I have this Mongolian friend of mine who I want to give <PERSON><PERSON> to, but from what I’ve heard literally everything related to Microsoft is region locked. Is there any way I can gift it?", "created_time": "2025-01-05T06:46:33", "platform": "reddit", "sentiment": "bullish", "engagement_score": 15.0, "upvotes": 9, "num_comments": 0, "subreddit": "unknown", "author": "Msquared5816", "url": "https://reddit.com/r/microsoft/comments/1htzuk9/gifting_minecraft_to_a_mongolian_friend_from_the/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Message to Microsoft: please bring back 3d Biilder for Windows 11. ", "content": "As per the title. ", "created_time": "2025-01-05T07:58:55", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "BRunner--", "url": "https://reddit.com/r/microsoft/comments/1hu17z0/message_to_microsoft_please_bring_back_3d_biilder/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Microsoft president says AI is ‘the electricity of our age’ as company prepares to hit $80 billion spend", "content": "", "created_time": "2025-01-05T10:55:02", "platform": "reddit", "sentiment": "neutral", "engagement_score": 647.0, "upvotes": 497, "num_comments": 0, "subreddit": "unknown", "author": "108CA", "url": "https://reddit.com/r/microsoft/comments/1hu4ia1/microsoft_president_says_ai_is_the_electricity_of/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Are all surface Pro keyboards the same?", "content": "I've had the microsoft surface 7 pro for several years now, and I'm needing a new keyboard. Are all surface keyboards the same or will I have to find one that's specific to my model?", "created_time": "2025-01-05T21:00:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Little_beep", "url": "https://reddit.com/r/microsoft/comments/1huh6et/are_all_surface_pro_keyboards_the_same/", "ticker": "MSFT", "date": "2025-01-05"}, {"title": "Dumping Memory to Bypass BitLocker on Windows 11", "content": "", "created_time": "2025-01-04T00:53:56", "platform": "reddit", "sentiment": "bearish", "engagement_score": 38.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NoInitialRamdisk", "url": "https://reddit.com/r/microsoft/comments/1ht27up/dumping_memory_to_bypass_bitlocker_on_windows_11/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "Old MacBook Air", "content": "I have a 13-year-old MacBook Air that won't update OS, it's the last intel chip version..  Using Big Sur 11.9.   I have a Microsoft 365 subscription.  Is there a version that I can install on this laptop?  My primary reason to keep using it is to use bibliography manager plug in.", "created_time": "2025-01-04T05:05:37", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Craigccrncen", "url": "https://reddit.com/r/microsoft/comments/1ht70uv/old_macbook_air/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "Microsoft expects to spend $80 billion on AI-enabled data centers in fiscal 2025", "content": "", "created_time": "2025-01-04T05:32:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 231.0, "upvotes": 173, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1ht7hib/microsoft_expects_to_spend_80_billion_on/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "LIQ<PERSON>DTEXT WOES ON SURFACE PRO 11- SEEKING ALTERNATIVE", "content": "Hello everyone!\n\nI’ve been a loyal user of LiquidText for my legal work, as it’s been a game-changer for managing case notes and documents. About six months ago, I decided to upgrade to a Surface Pro 11, excited about its ARM chip and overall utility for my profession. Unfortunately, that excitement quickly turned to frustration when I discovered the LiquidText app doesn’t work properly on the ARM chip.\n\nSince then, I’ve been in *constant* communication with the LiquidText team, trying to get the issue resolved. Six months later, all I have to show for my patience are lagging performance, frequent app crashes, and a series of unhelpful responses from their support team.\n\nHere’s the kicker: instead of solving the problem, the LiquidText team has essentially told me to *buy a new laptop*. Below are excerpts from their responses for your amusement:\n\n\"Hi,   We had been working on it. I am afraid the issue is with the ARM chip, this chip has been having issues with LiquidText for some time. Would you try any other device that runs with X64? I apologize for all the inconvenience.    Best wishes,\" \"We are working with the Microsoft team, and it seems like this specific surface model is having the same issues with LiquidText, a few other users have reported the issue as well. Microsoft is a bit slow at resolving issues, so the progress might come a bit late. I advise you to use any other laptop if you have any to see if the app is running better without any lags and crashes. \"\n\nEssentially, their solution to their app's failure is for *me* to shell out more money for new hardware. That’s like a tailor telling you to change your body because the suit doesn’t fit. Can anyone recommend apps similar to LiquidText that actually work seamlessly on the Surface Pro 11 with an ARM chip? I’d love to hear your thoughts and experiences because it seems like I need to jump ship.\n\nThanks in advance for your suggestions. And LiquidText, if you're reading this: maybe take a break from blaming Microsoft and focus on fixing your app.", "created_time": "2025-01-04T08:41:54", "platform": "reddit", "sentiment": "bearish", "engagement_score": 7.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1hta93a/liquidtext_woes_on_surface_pro_11_seeking/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": "Microsoft has finally upgraded notepad by replicating all features of notepad++ ", "content": "Using notepad was one of the most painful features of Microsoft OS. It did not auto save nor you had option of multiple tabs. But finally with W11 they have made it on par with notepad++\n\nBut why did microsoft take so long to do this ?", "created_time": "2025-01-04T09:25:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Infinite-Fold-1360", "url": "https://reddit.com/r/microsoft/comments/1htau73/microsoft_has_finally_upgraded_notepad_by/", "ticker": "MSFT", "date": "2025-01-04"}, {"title": " What GUI framework does Microsoft's Phone Link app on iOS use? .NET MAUI or Native Frameworks?", "content": "I'm curious about the **Phone Link** app by Microsoft on iOS. I know that Microsoft uses various frameworks for their apps across platforms, but I can't seem to find clear information on the GUI framework used in the iOS version of Phone Link.\n\nDoes anyone know if Microsoft uses **.NET MAUI** for the iOS version of Phone Link, or do they stick to native frameworks like **UIKit** (or maybe even **SwiftUI**) for iOS development?\n\nIt would be interesting to know how they approach the UI development for such an app, especially considering the cross-platform nature of the app and the performance needs on iOS.\n\nThanks for any insights!", "created_time": "2025-01-03T13:59:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 33.0, "upvotes": 27, "num_comments": 0, "subreddit": "unknown", "author": "DazzlingPassion614", "url": "https://reddit.com/r/microsoft/comments/1hsn2ff/what_gui_framework_does_microsofts_phone_link_app/", "ticker": "MSFT", "date": "2025-01-03"}, {"title": "Amazon's Seattle campus still quiet as 5-days-in-office deadline hits", "content": "", "created_time": "2025-01-03T14:00:05", "platform": "reddit", "sentiment": "neutral", "engagement_score": 115.0, "upvotes": 59, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1hsn2it/amazons_seattle_campus_still_quiet_as/", "ticker": "MSFT", "date": "2025-01-03"}, {"title": "Does RCS for Phone Link no longer work?", "content": "I have been using phone link recently on my Windows 11 PC.\n\nMy phone is a Samsung Galaxy S24 Ultra.\n\nI saw on [Microsoft's support](https://support.microsoft.com/en-us/topic/supported-devices-for-phone-link-experiences-cb044172-87aa-9e41-d446-c4ac83ce8807) that my phone is supported for RCS on phone link.\n\nThe main issue is it says Samsung Messages has to be default but Samsung uses google messages, Samsung messages does not seem to be available anymore.\n\nDoes anyone know if this just doesn't work anymore?", "created_time": "2025-01-03T23:45:00", "platform": "reddit", "sentiment": "bullish", "engagement_score": 28.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "Captain<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1ht0plv/does_rcs_for_phone_link_no_longer_work/", "ticker": "MSFT", "date": "2025-01-03"}, {"title": "Microsoft Explore Interview", "content": "Hello! I have an upcoming interview for Sophomore Microsoft Explore Internship and I was wondering if anyone had been through the interview process could give any advice or idea on what to expect. I’ve been going through the Blind 75 and writing down answers to Behavioral/Product Management questions, if there’s anything else I should be doing please let me know!", "created_time": "2025-01-02T03:25:08", "platform": "reddit", "sentiment": "neutral", "engagement_score": 51.0, "upvotes": 41, "num_comments": 0, "subreddit": "unknown", "author": "Vanarno13", "url": "https://reddit.com/r/microsoft/comments/1hrkgb6/microsoft_explore_interview/", "ticker": "MSFT", "date": "2025-01-02"}, {"title": "Seattle Amazon workers begin return to office full time starting Thursday - KIRO 7", "content": "", "created_time": "2025-01-02T14:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 15.0, "upvotes": 15, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1hruasz/seattle_amazon_workers_begin_return_to_office/", "ticker": "MSFT", "date": "2025-01-02"}, {"title": "Microsoft literally can buy Sony", "content": "Microsoft could literally buy Sony,\nEvidence; As of December 31, 2024, Sony's net worth, or market cap, was $128.99 billion.\nAs of January 2, 2025, Microsoft's net worth is $3.159 trillion\nIt's not even close!", "created_time": "2025-01-02T15:00:40", "platform": "reddit", "sentiment": "bullish", "engagement_score": 54.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "ClaydoyoyoA", "url": "https://reddit.com/r/microsoft/comments/1hrvlfr/microsoft_literally_can_buy_sony/", "ticker": "MSFT", "date": "2025-01-02"}, {"title": "This is the day Amazon's 'return to office' policy takes effect - NPR", "content": "", "created_time": "2025-01-02T20:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 516.0, "upvotes": 268, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1hs2u46/this_is_the_day_amazons_return_to_office_policy/", "ticker": "MSFT", "date": "2025-01-02"}, {"title": "How can I stop Microsoft from advertising on my computer? It's really bad, it comes up when I'm teaching Power BI to large groups on my laptop.", "content": "I'm finding it very annoying that this pops up on my computer from time to time.\n\n[link to image](https://imgur.com/a/82cL9Js)\n\nThis harms my brand, I don't play games on my computer and it makes me look bad in front of my clients when I'm using my computer to teach Power BI and stuff like that.\n\nShouldn't I be asked for permission before microsoft will show advertisements on my computer?\n\nHow to make this stop?\n\nThe ad text is as follows: Suggested/Black Ops 6: Vault Edition/Buy the Vault Edition of Black Ops 6 for premium bonus content./Buy Now/Dismiss\n\nThanks in advance.\n\n**update**\n\nThanks for the ideas so far.\n\nI found the xbox app installed and I'm supposing that's the source of the problem. I uninstalled it. I'll report back if this works or not.\n\nI saw the idea of just turning off notifications, thanks for that. But it wasn't clear which notification to turn off because none of the items there looked like they were related to the notification. \n\nAnd annoyingly, the notification itself doesn't say where it's from.", "created_time": "2025-01-02T20:08:31", "platform": "reddit", "sentiment": "bullish", "engagement_score": 162.0, "upvotes": 56, "num_comments": 0, "subreddit": "unknown", "author": "darcyWhyte", "url": "https://reddit.com/r/microsoft/comments/1hs31wa/how_can_i_stop_microsoft_from_advertising_on_my/", "ticker": "MSFT", "date": "2025-01-02"}, {"title": "Seeking Advice on Navigating Post-Interview Feedback for a Sales Role at Microsoft", "content": "Hi all,\n\nI recently went through the interview process for a sales role at Microsoft and received the following feedback:\n\n“I was informed that I was a strong candidate, but the company decided to move forward with another individual. They mentioned that leadership was impressed with my skills and suggested they’d consider me for other opportunities or revisit my candidacy if the selected person doesn’t accept.”\n\nWhile I’m disappointed, I want to make the most of this feedback and keep the door open for future sales opportunities at Microsoft.\n\nMy questions are:\n1. How can I stay on their radar without being too pushy?\n\n2.\tHow often should I follow up?\n\n3.\tIs there anything I can do to strengthen my position if the original candidate doesn’t accept?\n\n4.\tAre there steps I can take now to improve my chances for future sales roles at Microsoft?\n\nI’d really appreciate advice from anyone who’s been through something similar or knows how to approach this with Microsoft’s sales team. \n\nThanks in advance!", "created_time": "2025-01-02T22:16:59", "platform": "reddit", "sentiment": "bullish", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Daze2024", "url": "https://reddit.com/r/microsoft/comments/1hs668f/seeking_advice_on_navigating_postinterview/", "ticker": "MSFT", "date": "2025-01-02"}, {"title": "Microsoft ", "content": "I’m 180 questions deep I still am having trouble with the proving I’m not a robot why is this so hard for the life of me I can’t do it ", "created_time": "2025-01-01T03:41:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 10.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "AUSTINGREG1", "url": "https://reddit.com/r/microsoft/comments/1hquwah/microsoft/", "ticker": "MSFT", "date": "2025-01-01"}], "metadata": {"timestamp": "2025-07-06T20:56:01.968305", "end_date": "2025-01-06", "days_back": 7, "successful_dates": ["2025-01-06", "2025-01-05", "2025-01-04", "2025-01-03", "2025-01-02", "2025-01-01"], "failed_dates": ["2024-12-31", "2024-12-30"], "source": "local"}}}}