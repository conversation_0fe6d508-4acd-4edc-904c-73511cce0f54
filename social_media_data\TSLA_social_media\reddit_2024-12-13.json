[{"platform": "reddit", "post_id": "reddit_1hczdwx", "title": "Think of AI as a child", "content": "I’m not a programmer but I am just thinking we should reframe how we look at AI.\n\nIt is a new type of intelligence, it’s like a tool.\n\nBut it’s to a tool to emulate us.\n\nThat is how a child functions, through imitation.\n\nRight now AI is in its infancy so of course many are going to be like “It’s not that smart, it can’t do my job yet”….yet. \n\nLiterally everything we can do on computers can be imitated. Even our voices. \n\nHumanity has created its own unified child. And we are teaching…rapidly. Before we know it it’ll be an adult. \n\nI think many people still are not even aware of the potential AI will be able to do. \n\nThe film industry is going to be hit the hardest first because of the ease of generation. \n\nNow a lot of these changes will probably be really good. Just as with every new generation there are discoveries and fresh perspectives…it changes the current lifestyle and status quo.\n\nAI is our generation, it will be a disruptor and change things rapidly, perhaps even more than the advent of the Internet. Be flexible in the next decade because things are about to get weird. ", "author": "deleted", "created_time": "2024-12-13T00:08:42", "url": "https://reddit.com/r/artificial/comments/1hczdwx/think_of_ai_as_a_child/", "upvotes": 0, "comments_count": 53, "sentiment": "bullish", "engagement_score": 106.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hczj9b", "title": "Hit the 4 year mark as an analyst at my company. Looking for a reality / sanity check as to what is my actual role?", "content": "Hey everyone,\n\nAs the title says, I’ve been a data / business analyst on the marketing team at my company for just over four years now and am considering looking for a new role, however the issue is I have no idea what I even truly can call what I do-- I do a bit of everything but feel I'm a \"master of none\" in the eyes of roles beyond what I'm currently in. Including my last position, I have about six years of experience doing various work with SQL and Tableau.\n\n(for the record, I didn't run this through an AI program or anything, so if it reads weirdly, apologies!)\n\nSince starting, I have done (and continue to do) a variety of things:\n\n* Tableau\n   * The marketing team had no BI/reporting prior to me, so I got us into the Tableau ecosystem\n   * I currently create and manage multiple dashboards for 15-20 people, am the administrator for our Tableau portal, handle all Tableau Prep Flows, and the SQL pipeline used to power the viz layer\n   * All imposter syndrome aside, I would say I'm pretty good with Tableau. Various people on my team have said they've never seen somebody do in Tableau what I can do as quickly as I can do them, including using Tableau for live table QA\n* SQL and Data\n   * I am responsible for a large chunk of financial and virtually all surfaced marketing data at the company. My job is heavily SQL based and touches on many different types of data, from performance marketing to user retention to in-product analytics using log-level Mixpanel data\n   * I work with various teams to spec out important metrics, where to source from platform portals, working with devops to develop connections to our database, mapping out how this should look at the table and reporting level, and all QA– from the digital channel to the database to the dashboards\n   * Various team members have told me that my SQL readability is \"unparalleled\" and window functions and stuff are pretty easy\n* QA\n   * Our data is very dirty, so a huge chunk of my time is spent doing QA – unit tests on data, finding discrepancies and putting in tickets with our data team to rectify\n* Reporting\n   * I’m responsible for putting together all marketing reporting for c-suite and the board of directors\n   * Due to a large chunk of our data not existing in the database, and because marketers will never leave gSheets, I also maintain every gSheet report\n* Things I wish I had more opportunities to explore:\n   * I help surface insights when I can, however given my bandwidth it’s not feasible to do in-depth exploratory analyses for and/or alongside subject matter experts on each team\n   * I feel like I’ve been funneled into a role building and maintaining reports, and while I can and do offer insights into how campaigns perform, I don’t have a hands-on look into how our performance marketing is being tweaked by our agencies\n   * While I know how to use Python, I haven’t done even a fizzbuzz in years– I mainly use it for very basic API pulls in Databricks but haven’t had much experience with using python for stats\n   * My math isn’t the strongest. I have a degree in Econ and haven’t done a linear regression in over a decade at this point, hah\n\nFor full transparency, doing the above, I an full time and currently make $133k a year and have had a consistent 20% bonus every year. (Bay Area) \n\nThanks in advance for any insights :)", "author": "zbwd8eXFf54NvmM3a", "created_time": "2024-12-13T00:15:52", "url": "https://reddit.com/r/analytics/comments/1hczj9b/hit_the_4_year_mark_as_an_analyst_at_my_company/", "upvotes": 52, "comments_count": 24, "sentiment": "bullish", "engagement_score": 100.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd0u9f", "title": "New game revealed in the Game Awards was rendered with an unannounced NVIDIA GPU 👀 ", "content": "", "author": "GametheSame", "created_time": "2024-12-13T01:20:58", "url": "https://reddit.com/r/nvidia/comments/1hd0u9f/new_game_revealed_in_the_game_awards_was_rendered/", "upvotes": 1158, "comments_count": 233, "sentiment": "neutral", "engagement_score": 1624.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd3n9f", "title": "Palantir Stock Outshines Tesla: A Superior Investment During the Trump Administration", "content": "[https://medium.com/@StockMarketLoop/palantir-stock-outshines-tesla-a-superior-investment-during-the-trump-administration-feef09f003f3](https://medium.com/@StockMarketLoop/palantir-stock-outshines-tesla-a-superior-investment-during-the-trump-administration-feef09f003f3)", "author": "Major_Access2321", "created_time": "2024-12-13T03:48:52", "url": "https://reddit.com/r/investing_discussion/comments/1hd3n9f/palantir_stock_outshines_tesla_a_superior/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd3pp4", "title": "Will a masters in Data Analytics help me get into becoming a ML/AI engineer? ", "content": "Am I delusional for thinking that a data analytics masters sounds like it can help me pivot into a Machine Learning role? \n\nAre these types of degrees cash grabs that have no real substance to them? I am looking specifically at the degree from University of Maryland Global Campus\n\n\nBackground: \n\nI am currently a IT system administrator working for an employer where they will cover the cost of any graduate degree completely, so I would like to take advantage of this opportunity.\n\nI would like to eventually take my career to possibly becoming a Machine Learning engineer away from general IT. I have a bachelor in IT and one in cybersecurity (dual major). I know python pretty well as I use it a bit at my job for automation. Have used pandas and numpy for projects in school but that’s about it. ", "author": "TipUnable638", "created_time": "2024-12-13T03:52:38", "url": "https://reddit.com/r/analytics/comments/1hd3pp4/will_a_masters_in_data_analytics_help_me_get_into/", "upvotes": 2, "comments_count": 17, "sentiment": "neutral", "engagement_score": 36.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd6j5h", "title": "The guy who wanted to take his own life that posted in this sub", "content": "Remember the guy threatened to off himself if he couldn’t get a job in analytics even if he is overqualified. Where is he now?\n\nIt’s been a month. Did somebody reported him to suicide prevention?\n\nEven though you’re an asshole to everyone I hope you’re still alive somewhere.", "author": "notimportant4322", "created_time": "2024-12-13T06:45:24", "url": "https://reddit.com/r/analytics/comments/1hd6j5h/the_guy_who_wanted_to_take_his_own_life_that/", "upvotes": 77, "comments_count": 21, "sentiment": "neutral", "engagement_score": 119.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd7yrq", "title": "💹 Vertex Trading Review: Is This AI Bot the Future of Trading? 🚀", "content": "Hey r/investing_discussion  fam! 👋\n\nI recently came across Vertex Trading, a platform claiming to revolutionize trading with AI-driven algorithms and real-time market analysis. Naturally, I had to dive in and see if it lives up to the hype. Here's a quick breakdown:\n\n✅ What It Offers:\n\nAI Automation: Simplifies trading by analyzing trends and executing trades.\n\nMulti-Asset Trading: Supports crypto, forex, stocks, and more.\n\nRisk-Free Demo Mode: Perfect for beginners or testing strategies.\n\nCustomizable Options: Tailor it to your style and risk tolerance.\n\n✅ My Thoughts:\n\nIt’s sleek and user-friendly, but the $250 deposit might be steep for some.\n\nWhile the AI tools are impressive, I’d love more clarity on licensing details.\n\nOverall, great for both beginners and experienced traders, but as always, proceed cautiously.\n\nIf you’re curious, I came across with review of the platform in detail here: [Watch the Full Review](https://youtu.be/zN5QeydNZKg)\n\nWhat’s your take on AI trading bots like Vertex? Are they the future, or is manual trading still king? Let me know your thoughts below! ⬇️\n\n⚠️ Disclaimer: Trading involves risks. Always research and trade responsibly.", "author": "Electrical-Door-6399", "created_time": "2024-12-13T08:35:16", "url": "https://reddit.com/r/investing_discussion/comments/1hd7yrq/vertex_trading_review_is_this_ai_bot_the_future/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd8uxk", "title": "To my beginner friends, a warning: transitioning your career out of JS is hard", "content": "I've worked (in Italy as my first dev role) as a Full Stack dev around the MERN stack for a little less than 4 years. At the time, it wasn't a difficult position to get, and it allowed me to finance my degree in Germany.\n\nNow that I have my degree in Comp Ling, I'm again interviewing for positions more around what I'm interested in, which is BE only and, possibly, other ecosystems like .NET (there are many open positions for this environment in my area now, which there weren't a few years ago).\n\nUnfortunately, it seems it's really quite hard to transition out of both the front end and javascript in general. I had a company turn me down due to not having professional experience with Kubernetes and relational DBs, while there was good overlap with the rest of the stack. This is of course completely fair on their part, but I think I could've become a competent user in relatively little time thanks to experimenting with these things in my personal interests and projects. Regardless, it seems I have room to grow in selling what I'm capable of.\n\nOverall, this process of trying to transition out of front-end work and (specifically) MongoDB has been a bit more depressing than I anticipated. I hope you'll have better luck out there, but if you're getting into development on the front-end or full-stack in a JS-centric stack, you might want to look a bit further if your long-term goal is devops or back-end.\n\nWith that, happy holidays!", "author": "RandomGoodGuy2", "created_time": "2024-12-13T09:46:57", "url": "https://reddit.com/r/webdev/comments/1hd8uxk/to_my_beginner_friends_a_warning_transitioning/", "upvotes": 0, "comments_count": 16, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd9fpg", "title": "Microsoft’s Recall AI is creepy, clever, and compelling", "content": "", "author": "Confident_Arm_7844", "created_time": "2024-12-13T10:31:48", "url": "https://reddit.com/r/microsoft/comments/1hd9fpg/microsofts_recall_ai_is_creepy_clever_and/", "upvotes": 17, "comments_count": 10, "sentiment": "neutral", "engagement_score": 37.0, "source_subreddit": "microsoft", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hd9l3m", "title": "I feel like I’m holding too much cash… inherited $250k", "content": "I’m 31, married, bought a house (triplex) 3 years ago, have rental income from the other two units, ~$350k household income (salaries + rental), ~$200k in 401k, ~$30k individual stocks, and I inherited $250k several months ago. Hoping to reach FIRE and I’m not sure what to do with the cash. My wife and I will start trying to have kids so more expenses are likely on the way. I’ve kept the $250k in a HYS earning 4-5% as we are looking to maybe buy a house with more privacy and living area, but home prices for what we’re looking for at this time don’t make financial sense and I’m not sure that they will any time soon. I feel like I should be investing the $250k in other ways. Any advice is much appreciated. Radical ideas are welcome.", "author": "chipzandguac", "created_time": "2024-12-13T10:43:08", "url": "https://reddit.com/r/Fire/comments/1hd9l3m/i_feel_like_im_holding_too_much_cash_inherited/", "upvotes": 78, "comments_count": 68, "sentiment": "bullish", "engagement_score": 214.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdarf0", "title": "Exclusive-Trump transition recommends scrapping car-crash reporting requirement opposed by Tesla", "content": "", "author": "walky<PERSON><PERSON><PERSON>", "created_time": "2024-12-13T12:04:18", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hdarf0/exclusivetrump_transition_recommends_scrapping/", "upvotes": 433, "comments_count": 155, "sentiment": "bearish", "engagement_score": 743.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdbfho", "title": "Startup budget for café?", "content": "I started a similar post before, but didn't get any information. Possibly because I didn't word it properly. So I have funding in another state to open a café. It might not be relevant, but I am currently renting my apartment. My lease is ends at the end of February. That is when I would relocate/move to the other state to work on this. My other option is to renew my lease for another year to stay here at this location, possibly use some of this funding to setup a temporary online business. (The reason the café is in another state is because I am moving there no matter what. If not when my lease ends here, then next year.)\n\nI have no clue how to setup a business. As in setup a budget for startup costs. And what I need to open a café. What I mainly need to know is what I need physically to start a legal business and how to go about doing it. As in I can't just get a location, buy a bunch of Coffee grounds at the grocery, make coffee, and start selling it. It's illegal.\n\nI have no idea what I am supposed to do legally. I presume register a corporation. Open a bank account. No clue how to take credit cards. Get some form of commercial. No clue where to get supply like ingredients and such. I don't know what I'm doing. I need to calculate startup cost. But for that I need a list of everything I need bare minimum. The current funding I will be getting when I move is $20 or more. I also have some savings that I could use to start a home business if I decide to put off moving for a year.\n\nI have no plan. I just need basics on what is required.", "author": "starvergent", "created_time": "2024-12-13T12:46:27", "url": "https://reddit.com/r/business/comments/1hdbfho/startup_budget_for_café/", "upvotes": 0, "comments_count": 13, "sentiment": "neutral", "engagement_score": 26.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdbrju", "title": "Qarden Token?", "content": "Im not even sure what that is but for some reason, i checked out a Matthew Beem video and seem these comments everywhere", "author": "TheBlueDudeLol", "created_time": "2024-12-13T13:05:22", "url": "https://reddit.com/r/youtube/comments/1hdbrju/qarden_token/", "upvotes": 10, "comments_count": 5, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdcdmm", "title": "Trump transition wants to scrap crash reporting requirement opposed by Tesla", "content": "", "author": "DomesticErrorist22", "created_time": "2024-12-13T13:38:19", "url": "https://reddit.com/r/technology/comments/1hdcdmm/trump_transition_wants_to_scrap_crash_reporting/", "upvotes": 15274, "comments_count": 827, "sentiment": "bearish", "engagement_score": 16928.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdcvp7", "title": "RFK's <PERSON>yer Has Asked the F.D.A. to <PERSON><PERSON> Approval of the Polio Vaccine", "content": "", "author": "Tau_of_the_sun", "created_time": "2024-12-13T14:03:44", "url": "https://reddit.com/r/news/comments/1hdcvp7/rfks_lawyer_has_asked_the_fda_to_revoke_approval/", "upvotes": 48946, "comments_count": 6967, "sentiment": "neutral", "engagement_score": 62880.0, "source_subreddit": "news", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hde2gm", "title": "Does briefly turning on a new phone before fully charging it affect the battery?", "content": "**TL;DR:**  \nI turned on my phone briefly before fully charging it, turned it off right away, and then fully charged it. Did that first power-on count as the initial startup or did it start after the full charge and real use?\n\n**Long Version:**  \nI recently got my S24 Ultra. In my excitement, I accidentally turned on the phone before fully charging it, as recommended by some websites and even the Samsung guide. Realizing my mistake, I immediately turned it off and started charging it to 100%.\n\nI looked online, including places like Battery University, but couldn’t find anything specific about the initial charge process. Most of the advice there focuses on keeping the phone's battery between 20% and 80%. I also found information online that manufacturers test the phone's power-on process before selling it.\n\nSo, here’s my question:  \nDoes the first time I turned the phone on (briefly before fully charging it) count as the \"initial startup\"? Or does the \"real\" initial startup happen after fully charging and actually using the battery?\n\nP.S. I know it’s just a phone, and I’m overthinking it, but that’s just how I am. Please don't judge", "author": "MarsupialObjective25", "created_time": "2024-12-13T15:00:52", "url": "https://reddit.com/r/batteries/comments/1hde2gm/does_briefly_turning_on_a_new_phone_before_fully/", "upvotes": 1, "comments_count": 17, "sentiment": "neutral", "engagement_score": 35.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdh33o", "title": "I Love Watching My Tesla Charge", "content": "Am I the only one that feels giddy watching the electric car charge? Background, there is a NEMA 14-30 outlet at the back of my office building I can plug into for free. at home I just use a regular house outlet which is painfully slow. I plug into the work outlet a couple times a week to supplement charge as much as I can. The winter has made it harder with charging just using the 110 Volt outlet. From the Tesla app you can watch the battery level, as well as estimated time to full charge. Maybe its because I'm used to the 1kWh charger at home, but when I plug into the 5kWh charger I get positively ecstatic. I have no idea why it's like that. Maybe because the electricity is free, maybe I just feel more productive charging my car at work. Am I the only one? Honestly one of the best feelings.", "author": "Shards-_", "created_time": "2024-12-13T17:15:01", "url": "https://reddit.com/r/batteries/comments/1hdh33o/i_love_watching_my_tesla_charge/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdi88i", "title": "Google Maps Timeline - DONT KILL IT!", "content": "Oh, Google, what are you doing? For a company that prides itself on innovation and user loyalty, you sure have a knack for breaking hearts. I've been with you since the G1, the original Android pioneer. Through all the ups and downs, through every pixel-perfect camera upgrade and experimental feature, I’ve stuck with you. I’m talking G1 to Pixel 9 Pro XL — that’s not just loyalty, that’s a relationship. And yet, here we are again, mourning another product execution.\n\nLet’s talk about the Maps Timeline — one of the few Google products that truly nails the intersection of nostalgia, utility, and tech. This isn’t some throwaway feature. It’s personal. It’s my life, mapped out in a way that makes memories tangible. It’s like Facebook Memories for people who actually go places. It’s a reminder of where I’ve been, the adventures I’ve had, the little spots that made trips magical. It’s a time capsule, not just data.\n\nBut now you’re taking it away. Why? Why? Is it privacy? Fine, make it opt-in. Is it cost? Charge me for it. Slap it into Google One, bundle it with Workspace, put it in a subscription — I’ll pay for the privilege of keeping my own memories intact. You’re not just killing a feature; you’re erasing the breadcrumbs of my past.\n\nDo you know what it’s like to pull up a map from a trip eight years ago and rediscover that tiny Italian restaurant on Lake Geneva? To re-live a spontaneous detour through Kyoto? To trace the steps of your honeymoon or that unforgettable business trip to Berlin? These aren’t just pins on a map. They’re pieces of me. And you’re about to delete them like they’re last year’s emails.\n\nI’ve lived through the death of Nest Secure, Google+, Google Wave, Google Glass, Stadia, and countless others. Each time, I’ve rolled with it, sometimes bitterly, but always with hope that the next thing would be better. But Maps Timeline? This is different. It’s not a niche product. It’s not a failed experiment. It’s a tool that’s been silently and beautifully doing its job for years.\n\nThis isn’t just a mistake; it’s a tragedy. It’s short-sighted, and it’s out of touch with the users who’ve been loyal to you since day one. Google, you’re better than this. And if you’re not, maybe I’m not better for sticking around.\n\nEnd rant. Mic drop.", "author": "OffToReditToFindOut", "created_time": "2024-12-13T18:04:50", "url": "https://reddit.com/r/GooglePixel/comments/1hdi88i/google_maps_timeline_dont_kill_it/", "upvotes": 853, "comments_count": 163, "sentiment": "bearish", "engagement_score": 1179.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdjklf", "title": "[D] NVIDIA’s hostages: A Cyberpunk Reality of Monopolies\n", "content": "In AI and professional workstations, NVIDIA's dominance feels like a suffocating monopoly. Their segmented product lines widen the gap between consumer and professional GPUs, particularly in VRAM, performance, and price.\n\nAI enthusiasts struggle with prohibitive costs for GPUs equipped with sufficient VRAM. The reliance on CUDA cores—a proprietary standard—further locks developers into NVIDIA’s ecosystem, stifling competition and innovation.\n\nNVIDIA’s control extends beyond hardware, as their CUDA platform discourages adoption of open, competitive solutions. This feeds a cyberpunk dystopia where corporations consolidate power, leaving consumers and developers with few choices.\n\nWhy does the tech world remain complicit? Why aren’t we pursuing alternative hardware architectures or broader software compatibility beyond CUDA? AMD’s ROCm is a start, but more aggressive development and policy interventions are needed to challenge NVIDIA’s grip.\n\nUntil when will this continue? Who will stand up for the end consumer?", "author": "SevenShivas", "created_time": "2024-12-13T19:02:54", "url": "https://reddit.com/r/MachineLearning/comments/1hdjklf/d_nvidias_hostages_a_cyberpunk_reality_of/", "upvotes": 48, "comments_count": 25, "sentiment": "neutral", "engagement_score": 98.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdk8lu", "title": "Follow my Stock Picks to Gains Town", "content": "Since my last post about $QBTS just 16 days ago, it has **risen 79%.** Here are some other small cap stocks I believe will explode soon 🚀\n\n**$RGTI:**\n\n\\- Accessible with AWS Bracket: [https://aws.amazon.com/braket/?nc2=h\\_mo-lang](https://aws.amazon.com/braket/?nc2=h_mo-lang)\n\n\\- Multiple Government Contracts\n\n\\- Recently achieved calibration with AI, huge news: [https://www.globenewswire.com/news-release/2024/12/10/2994603/0/en/Quantum-Machines-and-Rigetti-Announce-Successful-AI-Powered-Calibration-of-a-Quantum-Computer.html](https://www.globenewswire.com/news-release/2024/12/10/2994603/0/en/Quantum-Machines-and-Rigetti-Announce-Successful-AI-Powered-Calibration-of-a-Quantum-Computer.html)\n\n**$QBTS:**\n\n\\- DD: [https://www.reddit.com/r/pennystocks/comments/1h1azk0/qbts\\_bull\\_run\\_just\\_getting\\_started/?utm\\_source=share&utm\\_medium=web3x&utm\\_name=web3xcss&utm\\_term=1&utm\\_content=share\\_button](https://www.reddit.com/r/pennystocks/comments/1h1azk0/qbts_bull_run_just_getting_started/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button)\n\n**$LAES:**\n\n\\- Quantum Computing is here and were going to need a new encryption and safety precautions. Sealsq has a wide range of products to deal with the emerging threats.\n\n\\- **P/S ratio of 4.39**, incredibly low for a company that is in such a burgeoning industry (38% compounded annual growth rate)\n\n\\- Has many products launching soon, increasing revenue\n\n**$RCAT**\n\n\\- 250 Million / 5 Year US Army contract. Added services and repairs revenue expected. Possible for NATO countries to purchase aswell\n\n[https://www.youtube.com/watch?v=1hpflgOOmKs](https://www.youtube.com/watch?v=1hpflgOOmKs)\n\n**$ASPI:**\n\n\\- Recently fell 50% after a short report was released about them. Today they came out with a response and will also host a Investor Day visit to their facilities to further ease concerns.\n\n\\- [https://finance.yahoo.com/news/asp-isotopes-inc-responds-short-*********.html](https://finance.yahoo.com/news/asp-isotopes-inc-responds-short-*********.html)\n\n**$UAMY**\n\n\\- Only North American smelter of Antimony\n\n\\- Currently at 50% capacity, will increase capacity 500% within the next 4 months (900% increase in output)\n\n\\- [https://m.youtube.com/watch?v=qRxtpladK80](https://m.youtube.com/watch?v=qRxtpladK80)\n\n**Edit: I also think QUBT will do very well**", "author": "Toronto_Stud", "created_time": "2024-12-13T19:32:21", "url": "https://reddit.com/r/pennystocks/comments/1hdk8lu/follow_my_stock_picks_to_gains_town/", "upvotes": 5, "comments_count": 42, "sentiment": "neutral", "engagement_score": 89.0, "source_subreddit": "pennystocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdmi1v", "title": "<PERSON> is probably going to kill the crash reporting rule that made Tesla look bad", "content": "", "author": "SpriteZeroY2k", "created_time": "2024-12-13T21:14:23", "url": "https://reddit.com/r/electricvehicles/comments/1hdmi1v/trump_is_probably_going_to_kill_the_crash/", "upvotes": 1047, "comments_count": 221, "sentiment": "bearish", "engagement_score": 1489.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdpask", "title": "Good books to read to learn about good financial habits ", "content": "I'm a 25 year old I work in construction and I was never taught how to handle my finances and now I'm married and have a kid and I'm living pay check to pay check my bills are getting paid but I'm barely getting by I might have 75 bucks left each week and that always gets used before pay day are there any books that can help me get better at saving and being more prepared in the future\n\nAny advice and recommendations are welcome", "author": "Ancient_Body5212", "created_time": "2024-12-13T23:24:25", "url": "https://reddit.com/r/FinancialPlanning/comments/1hdpask/good_books_to_read_to_learn_about_good_financial/", "upvotes": 63, "comments_count": 47, "sentiment": "neutral", "engagement_score": 157.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hdpwou", "title": "Will We Know When Tech Turns Harmful?", "content": "I just wonder, when we reach a point where technology does more harm than good, how will we know? \n\nIn fact, you can argue it already does.\n\nSocietal changes creep up on us. I feel drowned in digital distractions. Even a simple walk now feels pointless without music or a podcast. It’s not just about chasing dopamine; it’s about the endless pursuit of novelty. Our minds are packed, our attention is short, and the promised tech “freedom” has become its own trap.\n\nTechnology has relieved us of physical burdens, only to replace them with spiritual exhaustion. It has closed distances yet bred new forms of division. Armed with all the world’s information, we were supposed to be absolved of ignorance. Instead, ignorance seems more pervasive than ever.\n\nI’m still figuring this out. Some days I fail miserably at unplugging. But each morning offers a fresh start, a chance to reclaim our time, rediscover silence, and redefine what’s truly meaningful. Maybe in those quiet moments, we’ll finally recognise if we’ve gone too far.", "author": "alxpht", "created_time": "2024-12-13T23:54:02", "url": "https://reddit.com/r/Futurology/comments/1hdpwou/will_we_know_when_tech_turns_harmful/", "upvotes": 0, "comments_count": 28, "sentiment": "bearish", "engagement_score": 56.0, "source_subreddit": "Futurology", "hashtags": null, "ticker": "TSLA"}]