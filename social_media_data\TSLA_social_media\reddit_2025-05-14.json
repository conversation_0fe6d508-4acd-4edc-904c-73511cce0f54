[{"platform": "reddit", "post_id": "reddit_1km2mk1", "title": "Tesla Optimus New Movements", "content": "", "author": "Gab1024", "created_time": "2025-05-14T01:01:26", "url": "https://reddit.com/r/singularity/comments/1km2mk1/tesla_optimus_new_movements/", "upvotes": 2071, "comments_count": 788, "sentiment": "neutral", "engagement_score": 3647.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1km92ry", "title": "Google wants to make stolen Android phones basically unsellable | Google is upgrading Factory Reset Protection to make it even harder for thieves to sell stolen phones", "content": "", "author": "a_Ninja_b0y", "created_time": "2025-05-14T07:15:40", "url": "https://reddit.com/r/gadgets/comments/1km92ry/google_wants_to_make_stolen_android_phones/", "upvotes": 3950, "comments_count": 207, "sentiment": "bearish", "engagement_score": 4364.0, "source_subreddit": "gadgets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1km98yh", "title": "@Tesla_Optimus on X: \"Was just getting warmed up\"", "content": "", "author": "ItzWarty", "created_time": "2025-05-14T07:27:36", "url": "https://reddit.com/r/teslainvestorsclub/comments/1km98yh/tesla_optimus_on_x_was_just_getting_warmed_up/", "upvotes": 121, "comments_count": 178, "sentiment": "neutral", "engagement_score": 477.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1km9byg", "title": "Cloud provider frustrations: what’s keeping you tied to them (despite the red flags)?", "content": "Hi Guys, \n\nI'm trying to find out why many Europeans stay with the big 3 Cloud providers while they have many reasons not to. Also I'm trying to find out your main red flags with cloud providers. You would greatly help me out by commenting here your red flags/ frustrations with cloud providers and your reasons for staying with them anyways. \n\nSome things I often hear (curious if this resonates with you):\n\n* My data is hosted in the US and it doesn’t feel secure  \n* I’m hosting in the EU, but I have no idea who can access my data \n* My costs keep rising and are totally unpredictable  \n* I feel trapped with my provider and switching seems impossible \n* No issues, everything’s running smoothly\n\nWould love to hear your thoughts and real-world experiences!", "author": "Not_a_Je<PERSON>fish1", "created_time": "2025-05-14T07:33:33", "url": "https://reddit.com/r/cloudcomputing/comments/1km9byg/cloud_provider_frustrations_whats_keeping_you/", "upvotes": 4, "comments_count": 4, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmf5yj", "title": "We live in the age of refinement, not invention. (I will not promote)", "content": "It might be controversial, but I think that the world is out of big inventions.\n\nTake Shopify for example...\n\nThey did **$8.8 Billion** last year in revenue.\n\nBut they didn't...\n\n...invent ecommerce  \n....create the first online shop UI  \n....be the the first to host a plugin store.\n\nIn fact, the product itself arguably already existed.\n\nHowever, they did one thing correctly - understand customer pain!\n\nUp until that point, all ecommerce sites were expensive to build, self-hosted and completely self-managed.\n\nThis presented a HUGE barrier to entry for the new 'none' tech savy entrepreneurs that wanted to sell their stuff online (opportunity) and that's EXACTLY where Shopify positioned themselves.\n\nInstead of pushing a new idea, they leveraged a pre-existing market to PULL users into their easy done-for-you solution.\n\nEveryday, it feels like we are constantly being pushed forward by big tech and media. In reality - it is those that innovate on existing solutions that win big.\n\nRemember, it's the pioneers that lead with arrows in their backs.\n\nEven AI agent builders now like n8n will essentially be blenders that combine pre-existing applications with LLM's to deliver intelligent autonomous AI solutions BUT the technology already exists.\n\nA big part of my job (I AM NOT PROMOTING) is working with startups and the amount of pressure that I see founders putting themselves under trying to invent the next wheel is tenuous.\n\nThe wheel has already been invented, you can't make it rounder - all you can do is create a better vehicle that it is attached to.\n\n(I'm sitting with my popcorn getting ready to be told that AI agents are going to 'change the world'. Thank you for reading!)", "author": "BuildWConnor", "created_time": "2025-05-14T13:28:18", "url": "https://reddit.com/r/startups/comments/1kmf5yj/we_live_in_the_age_of_refinement_not_invention_i/", "upvotes": 0, "comments_count": 28, "sentiment": "bearish", "engagement_score": 56.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmfi7f", "title": "Should you still use Google Ads for B2B SaaS in 2025? Here's my honest take after managing $2M+ in ad spend (with real numbers)", "content": "Hey /ppc community!\n\nAfter managing over $2M in Google Ad spend for B2B SaaS companies, I want to give you my honest take on whether Google Ads still works in 2025. Spoiler: it does, but not how most people are running it.\n\nHere's the brutal truth: in about 60% of the B2B/SaaS accounts we audit, more than half the budget is going to complete waste. We're talking about money spent on job seekers, tire kickers, and people who will never buy your product.\n\nBut when done right, it still works incredibly well. Just last month, we helped a B2B service company generate 59 qualified leads in 14 days, got a SaaS tool 146 actual users (not just trials) in a month, and delivered 75 SQLs for a pharma manufacturing client.\n\nIMAGE proofs:\n\n[https://cdn.gamma.app/53a7azcxi5mxmml/df71078119cc4cbbb5445683952ae2c7/original/image.png](https://cdn.gamma.app/53a7azcxi5mxmml/df71078119cc4cbbb5445683952ae2c7/original/image.png)  \n[https://cdn.gamma.app/53a7azcxi5mxmml/fc0775dcd5b7499f8e4d229c41a423c4/original/image.png](https://cdn.gamma.app/53a7azcxi5mxmml/fc0775dcd5b7499f8e4d229c41a423c4/original/image.png)  \n[https://cdn.gamma.app/53a7azcxi5mxmml/1198a9a5eb984b809b1e03557ca79993/original/image.png](https://cdn.gamma.app/53a7azcxi5mxmml/1198a9a5eb984b809b1e03557ca79993/original/image.png)\n\nI know these numbers might sound too good, so let me break down exactly how we did it. We developed what we call the \"No-Waste Framework\" after seeing the same mistakes over and over again.\n\nHere's what actually works in 2025:\n\n1. Match Types Are Different Now\n\nForget everything you know about phrase match. We only use two match types: exact (for position) and broad (for intent). Here's why: broad match in 2025 is scary good at using Google's user signals - search history, behavior, time of day, etc. Phrase match? It's dead. It doesn't have the intelligence of broad or the precision of exact.\n\n2. The Negative Keywords Trap\n\nThis one's counterintuitive. That massive negative keyword list you've built? It's probably killing your performance. The algorithm has changed dramatically in the last 18 months. We do a quarterly cleanup because those old negative keywords are often blocking good traffic now.\n\n3. Ad Copy That Repels (Yes, Really)\n\nInstead of trying to get more clicks, we use ad copy to pre-qualify. We explicitly speak to ideal buyers and actively try to repel wrong-fit clicks. Example: Adding \"Enterprise-Only Solution\" in headlines cut our cost per SQL in half because we stopped paying for small business clicks.\n\n4. Landing Pages:\n\nLess is More You don't need 20 sections anymore. We stripped everything that doesn't directly serve conversion. One strong offer, one call to action, and relevant social proof. That's it. When we implemented this for a client, their trial-to-paid conversion rate doubled.\n\n5. The Hidden Killer:\n\nWrong Conversion Data This is the biggest mistake I see. I've audited $300k/month accounts with completely wrong conversion tracking. In B2B SaaS, you MUST import offline conversions. Let Google optimize for SQLs and closed deals, not just lead form fills.\n\nIs Google Ads worth it in 2025? If you're throwing your budget at broad keywords and optimizing for leads, probably not. But if you implement these changes, it can be your most predictable channel.\n\nI've turned this framework into a detailed checklist that we use internally for every account audit. Lemme know if you want it. I'll be happy to share it with you :)", "author": "WeirdFirefighter4110", "created_time": "2025-05-14T13:42:57", "url": "https://reddit.com/r/PPC/comments/1kmfi7f/should_you_still_use_google_ads_for_b2b_saas_in/", "upvotes": 56, "comments_count": 81, "sentiment": "neutral", "engagement_score": 218.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmg398", "title": "The Poop Audio Emoji is the greatest thing Google has ever invented", "content": "Just got a Pixel 9a and they have these great audio emojis.\n\nIt is without a doubt that the poop audio emoji thing is the greatest thing Google has EVER invented\n\nSPAM caller: \"Hello is this xxx?\"  \nme: \"yes it is how can I help.\"  \nspam:\"well....\"  \nme: \"its not the best time for me right now so you'll just have to be patient\" - hit poop emoji.....\"I'm just in the middle of a difficult thing. I think I ate something....\"poop emoji\"\n\nJust need multiple variations. This is amazing.", "author": "radix-", "created_time": "2025-05-14T14:08:07", "url": "https://reddit.com/r/GooglePixel/comments/1kmg398/the_poop_audio_emoji_is_the_greatest_thing_google/", "upvotes": 1796, "comments_count": 98, "sentiment": "neutral", "engagement_score": 1992.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmhbrc", "title": "1 in 4 cars sold in 2025 will be EVs, and that’s just the beginning", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-14T14:58:39", "url": "https://reddit.com/r/sustainability/comments/1kmhbrc/1_in_4_cars_sold_in_2025_will_be_evs_and_thats/", "upvotes": 82, "comments_count": 1, "sentiment": "neutral", "engagement_score": 84.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmhk1j", "title": "Is washing your panels necessary?", "content": "Total noob when it comes to modern solar (studied it in the 70's & 80's.) Just bought an EV and have been contemplating going solar. Also got a hefty tax refund so I could pay cash for solar. So I've been lurking and googling and the advise to clean the panels periodically came up. I have a 3 story town house, I can't even get to the gutters with my 32' ladder and no way am I going on the roof. So if I install panels and they need to be Windexed periodically, I don't see how I'm going to save any money if I have to pay someone to go up there with a spray bottle.\n\nYears ago I purchased a shower enclosure that had \"self cleaning\" glass. Basically the surface of normal glass under extreme magnification looks like a lava field, the self cleaning aspect is a ceramic coating that fills in all the nooks and crannies. Apparently this is done on skyscraper glass so that it doesn't need external cleaning. Any solar panels use this tech?", "author": "TooGoodToBeeTrue", "created_time": "2025-05-14T15:07:29", "url": "https://reddit.com/r/solar/comments/1kmhk1j/is_washing_your_panels_necessary/", "upvotes": 8, "comments_count": 35, "sentiment": "neutral", "engagement_score": 78.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmia4y", "title": "Meet AlphaEvolve, the Google AI that writes its own code—and just saved millions in computing costs", "content": "", "author": "joe4942", "created_time": "2025-05-14T15:37:02", "url": "https://reddit.com/r/singularity/comments/1kmia4y/meet_alphaevolve_the_google_ai_that_writes_its/", "upvotes": 1064, "comments_count": 125, "sentiment": "neutral", "engagement_score": 1314.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmjx3u", "title": "Google needs 'big bang breakup' that would value its businesses at $3.7 trillion as AI threatens Search", "content": "Goog is one of my largest positions. I'm bullish on the company and thinking about going all in.\n\nThe stock has been punished for fears it seems to be:\n\n1. Out of the AI race (it's not, <PERSON> is fantastic)\n2. Search is dead (it's not but it probably will be given enough time. However, it could easily be like tobacco companies that have great margins and exist for decades as solid businesses without growth due to personal habits)\n3. Will be broken up\n\nThis last point is fascinating to me because as I understand it, if it were to be broken up, shareholders would receive shares in all of the constituent parts. \n\nMeaning, I would get a shares in GCP, YouTube, Search, Waymo, etc. I would LOVE to have shares in any of those businesses individually. \n\nThe article below values all of Google's businesses at a staggering $3.7 trillion.\n\n[https://finance.yahoo.com/news/google-needs-big-bang-breakup-that-would-value-its-businesses-at-37-trillion-as-ai-threatens-search-analyst-*********.html?guce\\_referrer=aHR0cHM6Ly93d3cuZ29vZ2xlLmNvbS8&guce\\_referrer\\_sig=AQAAADxZpheK3u8TveRxgpzmaZCVXvQ7wAOPpW-pIlY8Xa6bYSOX99OVwn9LAuPPLqTzyKGtgE7I74IzSfrlQUAWHvaU53JttuhwRj4hUNZpWhflmHfDyjLhHxhX7YXdK\\_b68waPP1g545TNU2lkXrZJWlAiysD8Px6AqA6xPVXqThK7&guccounter=2](https://finance.yahoo.com/news/google-needs-big-bang-breakup-that-would-value-its-businesses-at-37-trillion-as-ai-threatens-search-analyst-*********.html?guce_referrer=aHR0cHM6Ly93d3cuZ29vZ2xlLmNvbS8&guce_referrer_sig=AQAAADxZpheK3u8TveRxgpzmaZCVXvQ7wAOPpW-pIlY8Xa6bYSOX99OVwn9LAuPPLqTzyKGtgE7I74IzSfrlQUAWHvaU53JttuhwRj4hUNZpWhflmHfDyjLhHxhX7YXdK_b68waPP1g545TNU2lkXrZJWlAiysD8Px6AqA6xPVXqThK7&guccounter=2)", "author": "ironcladjogging", "created_time": "2025-05-14T16:41:42", "url": "https://reddit.com/r/stocks/comments/1kmjx3u/google_needs_big_bang_breakup_that_would_value/", "upvotes": 477, "comments_count": 127, "sentiment": "bullish", "engagement_score": 731.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmlbf6", "title": "Meet AlphaEvolve, the Google AI that writes its own code—and just saved millions in computing costs", "content": "", "author": "bambin0", "created_time": "2025-05-14T17:36:38", "url": "https://reddit.com/r/artificial/comments/1kmlbf6/meet_alphaevolve_the_google_ai_that_writes_its/", "upvotes": 277, "comments_count": 45, "sentiment": "neutral", "engagement_score": 367.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmmv90", "title": "Chevron Must Pay $745 Million for Coastal Damages, Louisiana Jury Rules", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-14T18:37:44", "url": "https://reddit.com/r/sustainability/comments/1kmmv90/chevron_must_pay_745_million_for_coastal_damages/", "upvotes": 787, "comments_count": 11, "sentiment": "neutral", "engagement_score": 809.0, "source_subreddit": "sustainability", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmnk32", "title": "General Motors Overtakes Tesla In China EV Sales", "content": "", "author": "1oneplus", "created_time": "2025-05-14T19:05:00", "url": "https://reddit.com/r/electriccars/comments/1kmnk32/general_motors_overtakes_tesla_in_china_ev_sales/", "upvotes": 438, "comments_count": 61, "sentiment": "neutral", "engagement_score": 560.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmq91a", "title": "Leaked Internal email at Google Regarding PMAX", "content": "Not permitted to add images so here is the emails transcribed.   \nSure if you google you will find this and see its legit. \n\n**From:** <PERSON><PERSON><PERSON> Muxxxxxx\n\n**Sent:** 5/23/2024 4:51:40 PM\n\n**To:** <PERSON>xxxxx\n\n**CC:** Vivexxxx\n\n**Subject:** Re: \\[Daily Insider\\] The future of ads at Google Marketing Live\n\n\n\nI’m not as convinced by this. Yes, we’re pushing Pmax super hard, since that was our previous strategy. It’s not at all clear to me that it’s landing beyond the advertisers who have already bought in though (anecdotally, nobody was that excited about Pmax in my advertiser conversations on the day, at best it was like they were willing to go along). And there was some real frustration that Google isn’t listening and pushing “full auto” solutions they don’t want. I think we could absolutely tweak the messaging to evolve Pmax and have it land better.\n\n\n\nIn any case, I think the UI and branding can be very flexible in our model. SearchMax or Pmax for search, I think it doesn’t matter too much. The decision making structure is key, as you point out.\n\n\n\nOmkar\n\n\n\n\n\n**On Wed, May 22, 2024 at 8:36 AM <PERSON> xxxxxx wrote:**\n\n\n\nRead this whole thing, and <PERSON><PERSON><PERSON>’s summary. Yesterday we doubled down, unambiguously, that *all* our AI goodness is PMax. It was a consistent theme throughout the day. We said Pmax gets you 27% more conversions, and not just non-retail. Sylvanus led the audience in a Power Pair chant. DG was presented wholly separately, as part of the YouTube suite. Our sales force sees this and doesn’t believe DG is going to be a thing. Rion was bummed at the end of the day—“we have a lot to dig out of”.\n\n\n\nPmax is how you buy performance on Google. I just don’t see us walking that back, and anything that’s not Pmax is structurally disadvantaged from a positioning and sales perspective.", "author": "keep-the-momentum", "created_time": "2025-05-14T20:54:27", "url": "https://reddit.com/r/PPC/comments/1kmq91a/leaked_internal_email_at_google_regarding_pmax/", "upvotes": 156, "comments_count": 104, "sentiment": "bullish", "engagement_score": 364.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmqaqg", "title": "Leaked internal email at Google re: PMAX", "content": "Not permitted to add images in this subreddit but im sure if you google this, you;ll find its legit. \n\n\n\n**From:** <PERSON><PERSON><PERSON> \n\n**Sent:** 5/23/2024 4:51:40 PM\n\n**To:** <PERSON> \n\n**CC:** <PERSON><PERSON><PERSON> \n\n**Subject:** Re: \\[Daily Insider\\] The future of ads at Google Marketing Live\n\n\n\nI’m not as convinced by this. Yes, we’re pushing Pmax super hard, since that was our previous strategy. It’s not at all clear to me that it’s landing beyond the advertisers who have already bought in though (anecdotally, nobody was that excited about Pmax in my advertiser conversations on the day, at best it was like they were willing to go along). And there was some real frustration that Google isn’t listening and pushing “full auto” solutions they don’t want. I think we could absolutely tweak the messaging to evolve Pmax and have it land better.\n\n\n\nIn any case, I think the UI and branding can be very flexible in our model. SearchMax or Pmax for search, I think it doesn’t matter too much. The decision making structure is key, as you point out.\n\n\n\nOmkar\n\n\n\n\n\n**On Wed, May 22, 2024 at 8:36 AM <PERSON>  wrote:**\n\n\n\nRead this whole thing, and <PERSON><PERSON><PERSON>’s summary. Yesterday we doubled down, unambiguously, that *all* our AI goodness is PMax. It was a consistent theme throughout the day. We said Pmax gets you 27% more conversions, and not just non-retail. Syl<PERSON>us led the audience in a Power Pair chant. DG was presented wholly separately, as part of the YouTube suite. Our sales force sees this and doesn’t believe DG is going to be a thing. Rion was bummed at the end of the day—“we have a lot to dig out of”.\n\n\n\nPmax is how you buy performance on Google. I just don’t see us walking that back, and anything that’s not Pmax is structurally disadvantaged from a positioning and sales perspective.", "author": "keep-the-momentum", "created_time": "2025-05-14T20:56:22", "url": "https://reddit.com/r/DigitalMarketing/comments/1kmqaqg/leaked_internal_email_at_google_re_pmax/", "upvotes": 66, "comments_count": 12, "sentiment": "bullish", "engagement_score": 90.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmqobf", "title": "Just flipped the switch on our new 31 panel home system with my dad", "content": "My dad has been a solar enthusiast since who knows when... maybe since the <PERSON> administration. He built our off-grid family home in Tennessee in the late 90s, making me a solar enthusiast for all my life, since that is where I was born and raised. We just finished putting on a new roof to our garage and adding 31 new, much more efficient panels and a new battery system. This new system has doubled our energy output, and the new batteries should store enough that we may never need to run a generator again! \n\nYesterday, when we flipped the switch, we were getting between 11-13 kW. It was a long process and required lots of heavy lifting, but it is a proud accomplishment for our family. ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-14T21:11:33", "url": "https://reddit.com/r/solar/comments/1kmqobf/just_flipped_the_switch_on_our_new_31_panel_home/", "upvotes": 651, "comments_count": 35, "sentiment": "bullish", "engagement_score": 721.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kmr6jy", "title": "Leaked internal email at Google discussing heavy sales push for PMAX", "content": "Not listening to customers, lack of transparency, lack of control - despite the heavy sales push for PMAX, it will be interesting to see what we are ‘sold’ at Marketing Live 2025 \n\nSure if you google you will find this and see its legit.\n\nFrom: <PERSON><PERSON>kar Muxxxxxx\n\nSent: 5/23/2024 4:51:40 PM\n\nTo: <PERSON>xx\n\nCC: Vivexxxx\n\nSubject: Re: [Daily Insider] The future of ads at Google Marketing Live\n\nI’m not as convinced by this. Yes, we’re pushing Pmax super hard, since that was our previous strategy. It’s not at all clear to me that it’s landing beyond the advertisers who have already bought in though (anecdotally, nobody was that excited about Pmax in my advertiser conversations on the day, at best it was like they were willing to go along). And there was some real frustration that Google isn’t listening and pushing “full auto” solutions they don’t want. I think we could absolutely tweak the messaging to evolve Pmax and have it land better.\n\nIn any case, I think the UI and branding can be very flexible in our model. SearchMax or Pmax for search, I think it doesn’t matter too much. The decision making structure is key, as you point out.\n\nOmkar\n\nOn Wed, May 22, 2024 at 8:36 AM Michael xxxxxx wrote:\n\nRead this whole thing, and <PERSON><PERSON><PERSON>’s summary. Yesterday we doubled down, unambiguously, that all our AI goodness is PMax. It was a consistent theme throughout the day. We said Pmax gets you 27% more conversions, and not just non-retail. Sylvanus led the audience in a Power Pair chant. DG was presented wholly separately, as part of the YouTube suite. Our sales force sees this and doesn’t believe DG is going to be a thing. Rion was bummed at the end of the day—“we have a lot to dig out of”.\n\nPmax is how you buy performance on Google. I just don’t see us walking that back, and anything that’s not Pmax is structurally disadvantaged from a positioning and sales perspective.", "author": "keep-the-momentum", "created_time": "2025-05-14T21:32:16", "url": "https://reddit.com/r/marketing/comments/1kmr6jy/leaked_internal_email_at_google_discussing_heavy/", "upvotes": 10, "comments_count": 8, "sentiment": "bullish", "engagement_score": 26.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}]