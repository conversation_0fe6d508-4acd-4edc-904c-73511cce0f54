#!/usr/bin/env python3
"""
Reddit API认证测试脚本
用于验证Reddit API配置是否正确
"""

import os
import praw
import prawcore
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_reddit_auth():
    """测试Reddit API认证"""
    print("开始测试Reddit API认证...")
    
    # 获取配置
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'AI-Hedge-Fund-Bot/1.0')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    print(f"Client ID: {client_id[:10]}..." if client_id else "Client ID: 未设置")
    print(f"Client Secret: {client_secret[:10]}..." if client_secret else "Client Secret: 未设置")
    print(f"User Agent: {user_agent}")
    print(f"Username: {username}")
    print(f"Password: {'已设置' if password else '未设置'}")
    print()
    
    if not client_id or not client_secret:
        print("❌ Reddit API配置缺失！")
        return False
    
    try:
        # 创建Reddit实例
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent,
            username=username,
            password=password
        )
        
        # 测试基本认证
        print("测试基本认证...")
        print(f"Reddit实例创建成功: {reddit}")
        
        # 测试只读访问
        print("\n测试只读访问...")
        try:
            # 获取一个公开子版块的基本信息
            subreddit = reddit.subreddit('python')
            print(f"子版块名称: {subreddit.display_name}")
            print(f"子版块描述: {subreddit.public_description[:100]}...")
            print(f"订阅者数量: {subreddit.subscribers}")
            
            # 测试获取帖子
            print("\n测试获取帖子...")
            submissions = list(subreddit.hot(limit=3))
            print(f"成功获取 {len(submissions)} 个热门帖子:")
            for i, submission in enumerate(submissions, 1):
                print(f"  {i}. {submission.title[:50]}...")
            
            print("\n✅ Reddit API认证测试成功！")
            return True
            
        except prawcore.exceptions.ResponseException as e:
            print(f"❌ API响应错误: {e}")
            print("可能的原因:")
            print("1. API凭据无效")
            print("2. 应用程序类型配置错误")
            print("3. 用户名/密码错误")
            return False
            
        except prawcore.exceptions.RequestException as e:
            print(f"❌ 请求错误: {e}")
            print("可能的原因:")
            print("1. 网络连接问题")
            print("2. Reddit服务器问题")
            return False
            
    except Exception as e:
        print(f"❌ 认证失败: {e}")
        print("请检查以下配置:")
        print("1. REDDIT_CLIENT_ID 和 REDDIT_CLIENT_SECRET 是否正确")
        print("2. 在 https://www.reddit.com/prefs/apps 创建的应用类型是否为 'script'")
        print("3. REDDIT_USERNAME 和 REDDIT_PASSWORD 是否正确")
        return False

def test_specific_subreddit_access():
    """测试特定子版块访问"""
    print("\n" + "="*50)
    print("测试目标子版块访问...")
    
    try:
        reddit = praw.Reddit(
            client_id=os.getenv('REDDIT_CLIENT_ID'),
            client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
            user_agent=os.getenv('REDDIT_USER_AGENT', 'AI-Hedge-Fund-Bot/1.0'),
            username=os.getenv('REDDIT_USERNAME'),
            password=os.getenv('REDDIT_PASSWORD')
        )
        
        # 测试目标子版块
        test_subreddits = ['stocks', 'investing', 'wallstreetbets']
        
        for subreddit_name in test_subreddits:
            try:
                subreddit = reddit.subreddit(subreddit_name)
                # 尝试获取一个帖子
                submissions = list(subreddit.new(limit=1))
                if submissions:
                    print(f"✅ r/{subreddit_name}: 可访问，获取到帖子")
                else:
                    print(f"⚠️  r/{subreddit_name}: 可访问，但没有获取到帖子")
            except Exception as e:
                print(f"❌ r/{subreddit_name}: 访问失败 - {e}")
                
    except Exception as e:
        print(f"❌ 子版块测试失败: {e}")

if __name__ == '__main__':
    success = test_reddit_auth()
    if success:
        test_specific_subreddit_access()
    else:
        print("\n请修复认证问题后重新运行测试。")
