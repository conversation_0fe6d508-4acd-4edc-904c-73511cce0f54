[{"platform": "reddit", "post_id": "reddit_1kq3rir", "title": "Big difference between google ad numbers and GA4", "content": "<PERSON><PERSON>,\n\nI’m a product analyst, and also do some marketing analysis for stakeholders.\n\nWe notice there’s a big difference between a certain conversion event in google ads (which has been set up in tag manager) and GA4. \n\nGoogle ads giving like 14 conversions, while ga4 has over 72.\n\nWhat are some places I could start looking to see where this goes wrong?", "author": "xynaxia", "created_time": "2025-05-19T05:12:46", "url": "https://reddit.com/r/marketing/comments/1kq3rir/big_difference_between_google_ad_numbers_and_ga4/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kq3w5a", "title": "Please read if you are thinking about getting Solar 🌞", "content": "I work for a solar company, where most of my day involves communicating with sales reps and customers. I also monitor system performance post-installation—and in my experience, around 80% of systems don’t deliver the results promised. And many clients reach out upset about double billing, often because they were told their electric bill would be $0 and they’d receive monthly credits from the utility company and that they’d only have to pay the bank from then on.\n\nIf you are thinking about getting a system DO YOUR RESEARCH\n\nWhat I recommend: \n\n1. Read the Bank’s Contract, Not just the Installer’s: you are paying interest!\n\nIf you’re financing your solar system—which most customers do—you need to read the bank’s contract, not the installer’s. This is especially important if you’re leasing, as about 95% of our clients are. The financing contract will outline every single payment you’ll make yearly over the life of the lease, adding the interest rate. It will also show a comparison between the system’s advertised cost (what you think you’re paying) and the actual total lifetime cost—which is more than double due to interest. \n\nFor example, one customer expected to pay $19,800 for a 14-panel system, but her total cost over 25 years added up to $41,800. \n\nIf you are able to, find your own financing, don’t use the banks they offer. Read point 8 ⬇️ \n\n2.\tRecognize Sales Reps’ True Motivation:\n\nSales representatives are focused on their commission, not your savings—and some make $30,000 to $50,000 a month from just a few installs. To close deals, many reps actively lie to customers. Three common lies I’ve seen:\n• “This program is only offered to 2-3 homes in the neighborhood.” (Falsee! they’re knocking on every door.)\n• “You’ll pay a fixed amount for the full contract term.” (Also false— there is interest!)\n• “No more paying the utility company” (False! You will most likely be double billed, even if your offset is 100%, you are still going to pay a meter fee to the utility company. Keep in mind, there will be months when your system doesn’t cover your entire consumption and you’ll have to pull from the grid)\n\n\n3. Ask About Maintenance Costs:\nSolar systems aren’t maintenance free, and repairs can be expensive. Issues will come up eventually—even minor ones. The cheapest service we’ve handled was $450, just to tighten a single panel and check performance\n\n4.\tGet Direct Contact Info:\nAlways ask for the project manager’s number or the direct contact for the solar department. Don’t settle for an office or call center number—those agents are usually not trained to handle solar-specific questions or issues.\n\n5. Speak to the Project Manager Before Installation:\nMake sure you talk directly to the project manager—or whoever is overseeing the solar department—before the system is installed. If they dodge your questions or just send you back to your sales rep, that’s a red flag. Often, they won’t give straight answers because the truth could discourage you from moving forward. \n\n6.\tIf Your regular Bill Is Under $200, Think Twice:\nBased on monitoring over 100 clients, if your current electric bill is under $200/month, solar likely won’t save you much. In many cases, you’ll end up paying more or saving as little as $20 a mont\n\n7. Not a recommendation but be aware: you are signing a contract and they’re putting a lien on your house!! \n\n8. As someone mentioned in the comments: most of this doesn’t apply to CASH deals, but what I recommend for cash deals is to go straight to an installer and be involved as much as you can in the process. Most companies use third party installers, FIND THOSE THIRD PARTIES. \n\n\nI’m speaking up because I’m tired of seeing people misled into 20+ year financial commitments based on false promises of savings. What’s worse is how often sales guys target older ppl—about 90% of our clients are over 70 and retired, making them especially vulnerable. In separate cases, our installers arrived only to find the homeowners had no memory of signing up for solar and they realize that the customers have Alzheimer’s disease. The sales guy never followed up or checked in. On 2 of those 3, the sales guy was aware that the customer had memory issues. It was disgusting to me. Maybe I’m just to morally correct or just too stupid to work on this industry but that felt terrible for me. I get happy when people cancel. Really.\n\nI speak out to help people pause, think, and truly research what they’re committing to. I work in the solar industry, but it’s hard to find meaning in what I do when I’m the one answering the phone as customers break down—angry, confused, and overwhelmed—because they were promised things that simply aren’t true. While sales reps walk away with five-figure monthly commissions, I’m the one earning less than 2k a month, left to absorb the insults and consequences. Everyone else just says: “They should’ve known better.” But I know exactly what lies were told to convince them to sign. And honestly, it feels evil.\n\nRemember people: If it sounds too good to be true is because it is.  I hope you take my advice and really look what you’re getting into. \n\n\nEdited on 05/21: I wanted to add a few extra clarification on points 1 and 2 and I also added a point 8.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-19T05:21:24", "url": "https://reddit.com/r/solar/comments/1kq3w5a/please_read_if_you_are_thinking_about_getting/", "upvotes": 1289, "comments_count": 345, "sentiment": "bullish", "engagement_score": 1979.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kq6kq7", "title": "Anyone considering LEAPS puts for TSLA right now?", "content": "Hey all,  \nAfter the recent runup I am considering putting a little bit into LEAPS puts for TSLA.  \nI know the stock is irrational, but I think a small bet that the stock will go down over the next 12 months is reasonable.  \nAnyone else already doing this? At what strike price and duration?  \nThanks", "author": "Oszillationswerkzeug", "created_time": "2025-05-19T08:28:13", "url": "https://reddit.com/r/options/comments/1kq6kq7/anyone_considering_leaps_puts_for_tsla_right_now/", "upvotes": 9, "comments_count": 36, "sentiment": "bearish", "engagement_score": 81.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kq8l7g", "title": "Google Ads - <PERSON><PERSON><PERSON> not showing up?", "content": "Hi, i have been paying for google ads for months now. It works great but i have a problem. My favicon is not showing up!\n\n[Google AD showing](https://tinypic.host/image/Knipsel1.3Kf4NG)\n\nNo favicon or even images like the other company's.\n\n[Attached photo's campaign](https://tinypic.host/image/Knipsel3.3Kf0af)\n\n[Direct URL to favicon](https://clevair.nl/favicon.ico) (site header has been changed)\n\n[The icon is shown in the site map on Google search.](https://tinypic.host/image/Knipsel4.3KfcfO)\n\nHow do i fix the favicon and images to optimize Google Ads?\n\nThank u!", "author": "Aggravating_Pie1913", "created_time": "2025-05-19T10:46:06", "url": "https://reddit.com/r/adwords/comments/1kq8l7g/google_ads_favicon_not_showing_up/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kq9w04", "title": "Investment analyst opportunity (London)", "content": "*Thank you for reading. We have heard of others connecting with candidates via this forum and thought to post the below.*\n\n Ananda Asset Management is a top performing equity investment manager launched in 2018.\n\nWe are a growing firm and are looking for an investment analyst. A great platform to further a career in a best-in-class, collaborative and motivating working environment.  \n  \nThe role:\n\n1. Conduct detailed fundamental research on individual companies and sectors\n2. Monitor existing positions and idea generation\n3. Generalist coverage – the fund typically invests across consumer, industrials, information technology and healthcare in Europe and North America\n4. Integral part of the team evaluating investment opportunities\n\n The candidate:\n\n1. Driven, thoughtful, and passionate about stock picking\n2. 2-5 years of professional experience from a Tier 1 IB, or PE/HF/VC/financial journalism\n3. Independent thinker with demonstrable interest in public market investing\n4. Excellent academic credentials\n\n Please contact us with your CV and a short cover letter (max 200 words, stock picks welcome) at: [**<EMAIL>**](mailto:<EMAIL>)", "author": "AnandaAM", "created_time": "2025-05-19T12:01:00", "url": "https://reddit.com/r/SecurityAnalysis/comments/1kq9w04/investment_analyst_opportunity_london/", "upvotes": 3, "comments_count": 3, "sentiment": "bearish", "engagement_score": 9.0, "source_subreddit": "SecurityAnalysis", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqa0v4", "title": "[N] We benchmarked gender bias across top LLMs (GPT-4.5, <PERSON>, <PERSON><PERSON><PERSON>). Results across 6 stereotype categories are live.", "content": "We just launched a new benchmark and leaderboard called **Leval-S**, designed to evaluate **gender bias in leading LLMs**.\n\nMost existing evaluations are public or reused, that means models may have been optimized for them. Ours is different:\n\n* **Contamination-free** (none of the prompts are public)\n* Focused on **stereotypical associations** across 6 domains\n\nWe test for stereotypical associations across **profession, intelligence, emotion, caregiving, physicality, and justice,**using paired prompts to isolate polarity-based bias.\n\n🔗 [Explore the results here (free)](https://www.levalhub.com/)\n\nSome findings:\n\n* **GPT-4.5** scores highest on fairness (94/100)\n* **GPT-4.1** (released without a safety report) ranks near the bottom\n* **Model size ≠ lower bias,** there's no strong correlation\n\nWe welcome your feedback, questions, or suggestions on what *you* want to see in future benchmarks.", "author": "LatterEquivalent8478", "created_time": "2025-05-19T12:07:49", "url": "https://reddit.com/r/MachineLearning/comments/1kqa0v4/n_we_benchmarked_gender_bias_across_top_llms/", "upvotes": 4, "comments_count": 31, "sentiment": "bullish", "engagement_score": 66.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqc199", "title": "head of cloud got someone to mentor me? what should I be asking?", "content": "I expressed interest in getting a cloud role in my company which provides cloud services and uses azure and terraform but will be expanding into AWS soon since I have my CCP ad solutions architect associate I  was basically told there would be need for someone with my knowledge in the chat I had with the head of cloud. We had like 2 different teams calls just getting to know each other and my interests and it was pretty cool. Now obviously this guy is super busy so he got one of his guys on the cloud specialist team officially mentor me and ask him questions if I had any.\n\n  \nSo i was introduced to the guy, lets just call him bob and hes a cool dude. he got me access to plural sight sandbox which was very cool and offered advice on getting the terraform cert which I did in like a month and told me maybe get kubernetes associate or something. this was pretty much between two teams chat convos and im currently looking at a kubernetes course but apart from all that what else should I be asking him? never had a mentor before and i have no idea what else to ask. I always don't like bothering some people sometimes so we havent really spoken for a few weeks ( he didnt reply to my last question). can anyone help me out lol how should I make the best of this situation.", "author": "Correct_Adeptness_60", "created_time": "2025-05-19T13:43:21", "url": "https://reddit.com/r/cloudcomputing/comments/1kqc199/head_of_cloud_got_someone_to_mentor_me_what/", "upvotes": 3, "comments_count": 1, "sentiment": "bullish", "engagement_score": 5.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqfdxx", "title": "Game Ready & Studio Driver 576.52 FAQ/Discussion", "content": "# GeForce Hotfix Display Driver version 576.66 has been released. [Please visit this thread for discussion](https://www.reddit.com/r/nvidia/comments/1l2o80a/geforce_hotfix_display_driver_version_57666/). This WHQL driver thread will be locked.\n\nGeForce Hotfix Display Driver version 576.66 is based on our latest Game Ready Driver 576.52.  \n   \nThis Hotfix addresses the following:\n\n* Dune: Awakening may crash during gameplay \\[5273568\\]\n* EA Sports FC 25 may crash during gameplay \\[5251937\\]\n* \\[RTX 50 series\\] Dragons Dogma 2 displays shadow flicker \\[5252205\\]\n* \\[RTX 50 series\\] Video playback in a web browser may show brief red/green flash corruption \\[5241341\\]\n* Clair Obscur: Expedition 33 may crash \\[5283401\\]\n\nA GeForce driver is an incredibly complex piece of software, We have an army of software engineers constantly adding features and fixing bugs. These changes are checked into the main driver branches, which are eventually run through a massive QA process and released.\n\nSince we have so many changes being checked in, we usually try to align driver releases with significant game or product releases. This process has served us pretty well over the years but it has one significant weakness. Sometimes a change that is important to many users might end up sitting and waiting until we are able to release the driver.\n\n**The GeForce Hotfix driver is our way to trying to get some of these fixes out to you more quickly. These drivers are basically the same as the previous released version, with a small number of additional targeted fixes. The fixes that make it in are based in part on your feedback in the Driver Feedback threads and partly on how realistic it is for us to quickly address them. These fixes (and many more) will be incorporated into the next official driver release, at which time the Hotfix driver will be taken down.**\n\n**To be sure, these Hotfix drivers are beta, optional and provided as-is. They are run through a much abbreviated QA process. The sole reason they exist is to get fixes out to you more quickly. The safest option is to wait for the next WHQL certified driver. But we know that many of you are willing to try these out. As a result, we only provide NVIDIA Hotfix drivers through our NVIDIA Customer Care support site.**  \n   \n[Click here](https://international.download.nvidia.com/Windows/576.66hf/576.66-desktop-notebook-win10-win11-64bit-international-dch.hf.exe) to download the GeForce Hotfix display driver version 576.66 for Windows 10 x64 / Windows 11 x64\n\n# Reminders:\n\n* Hotfix driver needs to be downloaded via the download link on this post below or from the [NVIDIA Customer Support article here](https://nvidia.custhelp.com/app/answers/detail/a_id/5667/). **This driver will not be available to download via NV App or Driver Search**. The fixes contained within this Hotfix driver will be included in the next full WHQL release. [Click here](https://international.download.nvidia.com/Windows/576.66hf/576.66-desktop-notebook-win10-win11-64bit-international-dch.hf.exe) to download the 576.66 Hotfix Driver\n* The Hotfix driver is a very targeted driver release to fix specific issues and **you should only expect fixes related to the items they listed**. The driver itself is using WHQL 576.52 as a base so if the issue is not specifically listed in the Hotfix lists, then it's not going to be fixed and you'll have to wait for the next full release for more fixes.\n* Reminder that if you have driver related issues, please send a driver report directly to NVIDIA with detailed information. This is the best way to get the issue recognized, replicated, and solved. [Link to driver bug report form here](https://forms.gle/kJ9Bqcaicvjb82SdA)\n\n\\---------------------------\n\n# Game Ready & Studio Driver 576.52 has been released.\n\n# If you cannot find the driver in NVIDIA Website Search or not showing in NVIDIA App, please give it time to propagate.\n\n**Game Ready Driver Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/rtx-5060-dune-awakening-f1-25-geforce-game-ready-driver/)\n\n**Game Ready Driver Direct Download Link**: [Link Here](https://us.download.nvidia.com/Windows/576.52/576.52-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n**Studio Driver Article Here**: [Link Here](https://blogs.nvidia.com/blog/rtx-ai-garage-computex-microsoft-build)\n\n**Studio Driver Direct Download Link**: [Link Here](https://us.download.nvidia.com/Windows/576.52/576.52-desktop-win10-win11-64bit-international-nsd-dch-whql.exe)\n\n# New feature and fixes in driver 576.52:\n\n# Game Ready\n\nThis new Game Ready Driver provides the best gaming experience for the latest new games supporting DLSS 4 technology including F1 25 and Dune: Awakening, as well as the Full Ray Tracing update for NARAKA: BLADEPOINT.\n\n# Applications\n\nThe May NVIDIA Studio Driver provides support for the new GeForce RTX 5060 desktop and laptop GPUs. In addition, this release offers optimal support for the latest new creative applications and updates including Topaz Video AI releasing Starlight Mini, Chaos Vantage introducing support for Shader Execution Reordering (SER), Bilibili adding Maxine Video Effects SDK, and DLSS 4 support coming to Chaos Enscape and Autodesk VRED.\n\n# Gaming Technology\n\nAdds support for GeForce RTX 5060 desktop and laptop GPUs\n\n# Fixed Gaming Bugs\n\n* **FIXED** \\[F1 23/F1 24\\] Game crashes at the end of a race \\[5240429\\]\n* **FIXED** \\[Diablo II Resurrected\\] Game displays black screen corruption when using DLSS \\[5264112\\]\n* **FIXED** \\[SCUM\\] Game may crash after updating to R575 drivers \\[5257319\\]\n* **FIXED** Shader disk cache will not be created with certain games if OS username contains unicode characters \\[5274587\\]\n\n# Fixed General Bugs\n\n* **FIXED** \\[Lumion 12\\] Missing certain UI components \\[5213228\\]\n* **FIXED** \\[Varjo XR3\\] Varjo XR3 HMD is not working on RTX 50 series GPUs \\[5173753\\]\n* **FIXED** \\[Notebook\\] GeForce RTX 50 series TGP limit may be clipped earlier \\[5170771\\]\n   * This is listed as Fixed in the [GeForce Forum 576.52 post](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/565270/geforce-grd-57652-feedback-thread-released-51925/)\n\n# Open Issues\n\n**Includes additional open issues from** [GeForce Forums](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/565270/geforce-grd-57652-feedback-thread-released-51925/)\n\n* Flickering/corruption around light sources in Ghost of Tsushima Directors Cut \\[5138067\\]\n* Cyberpunk 2077 will crash when using Photo Mode to take a screenshot with path tracing enabled \\[5076545\\]\n* **576.66 Hotfix Fixed** \\- ~~EA Sports FC 25 may crash during gameplay \\[5251937\\]~~\n* \\[Forza Horizon 5\\] Game may crash after extended gameplay \\[5131160\\]\n   * Per u/m_w_h comment [here](https://www.reddit.com/r/nvidia/comments/1kqfdxx/comment/mtalvtm/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button)\n      * IIRC the Forza Horizon 5 (FH5) issue is a game bug related to FH5 not tracking evicted resources correctly. If that's the case, Nvidia could implement a workaround in upcoming drivers but the issue is actually game related and needs to be addressed by the game developer.\n      * Potential workaround(s) in the meantime:\n      * Set Forza Horizon 5 profile flag *0x006D6197* to *0xA2B53761* ^(credit Guzz) using Nvidia Profile Inspector. Note that Nvidia Profile Inspector's *'Show unknown settings from NVIDIA predefined profiles'* needs to be ticked to see that flag.\n      * Revert to driver 561.09 or earlier, evicted resources in that driver are handled differently and will result in slowdown/leaks rather than crash\n* **576.66 Hotfix Fixed** ~~\\[RTX 50 series\\] Dragons Dogma 2 displays shadow flicker \\[5252205\\]~~\n* **576.66 Hotfix Fixed** ~~\\[RTX 50 series\\] Video playback in a web browser may show brief red/green flash corruption \\[5241341\\]~~\n* Wuthering Waves may randomly crash during gameplay after updating to R575 drivers \\[5259963\\]\n* \\[RTX 50 series\\] Enshrouded crashes after launching game \\[5279848\\]\n* \\[NVIDIA App\\] Adding an unsupported app to NVIDIA App and enabling Smooth Motion forces it globally to other apps \\[5243686\\]\n* \\[RTX 50 series\\]\\[Battlefeld 2042\\] Random square artifacts may appear around lights during gameplay \\[5284105\\]\n* Changing a setting in the \"NVIDIA Control Panel\" -> \"Manage 3D Settings\" may trigger shader disk cache rebuild \\[5282396\\]\n* \\[Gray Zone Warfare\\] Game may crash on startup \\[5284518\\]\n* \\[RTX 50 series\\] Bugcheck when attempting to launch Twinmotion \\[5282285\\]\n* \\[RTX 50 series\\] Monster Hunter World may crash when playing in DX12 mode \\[5325098\\]\n* \\[DirectX 12\\] Intermittent crash may be observed in certain games when hardware-accelerated GPU scheduling is disabled \\[5327650\\] -> To enable Hardware-Accelerated GPU Scheduling, open the Windows Settings -> System -> Display -> Graphics -> Advanced Graphics Settings and toggle Hardware-Accelerated GPU scheduling to \"On\"\n\nPartial freezing/black screen when alt-tabbing from game/desktop after upgrading to Windows 11 24H2:  \n[https://www.reddit.com/r/Windows11/comments/1kgp7ar/cause\\_and\\_solution\\_to\\_windows\\_24h2\\_related/](https://www.reddit.com/r/Windows11/comments/1kgp7ar/cause_and_solution_to_windows_24h2_related/)\n\n# Driver Downloads and Tools\n\n**Information & Documentation**\n\n* Driver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n* Latest Game Ready Driver: 576.52 WHQL - [Game Ready Driver Release Notes](https://us.download.nvidia.com/Windows/576.52/576.52-win11-win10-release-notes.pdf)\n* Latest Studio Driver: 576.52 WHQL - [Studio Driver Release Notes](https://us.download.nvidia.com/Windows/576.52/576.52-win10-win11-nsd-release-notes.pdf)\n\n**Feedback & Discussion Forums**\n\n* **Submit driver feedback directly to NVIDIA:** [**Link Here**](https://forms.gle/kJ9Bqcaicvjb82SdA)\n* NVIDIA 576.52 Driver Forum: [Link Here](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/565270/geforce-grd-57652-feedback-thread-released-51925/)\n* r/NVIDIA Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\n**Having Issues with your driver and want to fully clean the driver? Use DDU (Display Driver Uninstaller)**\n\n* DDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n* DDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n* DDU/WagnardSoft Patreon: [Link Here](https://www.patreon.com/wagnardsoft)\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue -** [**Link Here**](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/564384/geforce-grd-57628-feedback-thread-released-43025/)\n\n**There is only one real way for any of these problems to get solved**, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n* **Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations between hardware and software. Driver will never be perfect and there will always be issues for some people. Two people with the same hardware configuration might not have the same experience with the same driver versions. Again, I encourage folks who installed the driver to post their experience here good or bad.", "author": "Nestledrink", "created_time": "2025-05-19T16:01:10", "url": "https://reddit.com/r/nvidia/comments/1kqfdxx/game_ready_studio_driver_57652_faqdiscussion/", "upvotes": 471, "comments_count": 1525, "sentiment": "bearish", "engagement_score": 3521.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqftxu", "title": "42 and Retired! I can’t believe it", "content": "Turned everything in today; it’s official! What a wild ride. It still doesn’t make sense how I made it to this point from an initial salary of $35k/yr, 19 years ago, moving up the ladder and saving and investing in S&P 500 index funds.\n\nThe answer is my wife (41). We met in our early 30’s and are extremely similar INTJs, from values to earnings to spending habits to goals. If you’re single and want to compare numbers below, it’s best to divide by two. For context, we have one child (6).\n\n“Liquid” Accounts ($3M)\n\nTaxable: $1.3M - We realized that we would be way too heavily weighted in retirement accounts, so we started going heavy in taxable, while just getting employer match in pre-tax, about 5 yrs ago.\n\nPre-Tax: $1.2M - We started maxing this out again last year after hitting FI, just for tax purposes.\n\nRoth: $0.5M - Mostly from early in our careers, but we just starting doing backdoor last year.\n\nRental Property ($500k, paid off, nets $20k cash profit/yr) - This is a one bedroom condo that I owned before I met my wife. It’s in a great location, not a great ROI, but it’s been easily rented every day since I starting renting it 9 years ago.\n\nCurrent annual expenses: $105k/yr ($85k with net rental income removed) - This will ebb and flow, but there are some reductions that should show up this year. After being a heavy drinker for 25 yrs, I quit drinking alcohol in December. I can do all of my landscaping myself, which I actually really enjoy. It will definitely increase some for travel, but we already travel quite a bit.\n\nHouse ($1.4M, purchased for $850k, $575k left on mortgage, 2.875% fixed with 26 yrs left) - Not planning to pay this off early.\n\nCars (2008, 2011 models years, no payments/purchases since 2014) - Still under 200k miles combined, but we may have to buy a new car in the next few years.\n\nFuture Income: My wife likes her job (and employer-funded health care) and is planning to stay on for at least two more years. She is reducing her responsibilities and will now be off in the summer, so all three of us will be free to take month-long vacations in the summer.\n\nThat’s enough info for now, still can’t believe I made it to this point!! Let me know if you have any questions!\n", "author": "firelurker3", "created_time": "2025-05-19T16:18:48", "url": "https://reddit.com/r/Fire/comments/1kqftxu/42_and_retired_i_cant_believe_it/", "upvotes": 1842, "comments_count": 249, "sentiment": "bullish", "engagement_score": 2340.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqg2i5", "title": "Tesla Looks to Improve LFP Battery Performance and Bring Production to the U.S.", "content": "", "author": "Sohmal3", "created_time": "2025-05-19T16:28:09", "url": "https://reddit.com/r/teslamotors/comments/1kqg2i5/tesla_looks_to_improve_lfp_battery_performance/", "upvotes": 159, "comments_count": 40, "sentiment": "neutral", "engagement_score": 239.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqo3ts", "title": "Anybody ever question why tf we’re still doing this?", "content": "I’ve been a smb for 7.5 years. I’m 30 now. This has been my whole professional life. \n\nMy revenue is 1.5-2M, but i only net like $$50-$100k, maybe $200k on a good year. \n\nThis sounds kinda cool but its not that great. It kinda sucks actually\n\nI risk it all every year to make less than any of my friends who just took white collar jobs who started at $100k and make like $350k+/yr now. \n\nI probably could have been one of those people, but nope i had to fulfill my dream of being a business owner. If i could just go back in time and yell at the stupid ambitious boy i used to be\n\nAnyone else ever feel this way? Just questioning everything that lead you into this life?\n\nEdit: I think i just got a growth idea. It might bankrupt me, but fuck it, could be fun. Will keep you guys posted\n\nEdit 2: I was mostly just venting but i feel like i owe you all answers to these questions i’ve been asked multiple times:\n\n1. What I do: I run an app marketing and development agency. \n\n2. Why are margins so low: I’m in that weird spot where i have enough staff to handle more clients, but if i fire anybody i cant handle the clientele i have now, so i need to grow or scale down. Also in theory i do make 20% net, so i should make $300k this year, but i’m factoring in black swan events that cost like $100k+ and are different every year. Nothings happened in 2025 yet. \n\n3. Can you cut expenses: Believe me i have. Overall expenses are half of what they were at the peak, mostly from outsourcing labor. And client retention is up, so no that isn’t the issue. Revenue from the peak is down because my peak was covid and the world isn’t like that anymore.\n\n4. Why don’t you scale: My clientele have limited runway. If i don’t close 2 new deals a months, my net clients drop on a monthly basis. This is expected from the niche i’m in. It’s not a great niche to be all in on. Its the reason why i grew so fast and then stagnated so hard. \n\n5. How are you going to scale: I’m planning to merge other agencies into mine to expand. Or just sell it once i post a full year without a black swan.  ", "author": "Own-Development7059", "created_time": "2025-05-19T21:42:05", "url": "https://reddit.com/r/smallbusiness/comments/1kqo3ts/anybody_ever_question_why_tf_were_still_doing_this/", "upvotes": 731, "comments_count": 452, "sentiment": "bearish", "engagement_score": 1635.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqosz7", "title": "Check out my primer on IT Services", "content": "Relevant ot mega caps and Indian offshore players like Accenture ($ACN) and Cognizant ($CTSH) as well as boutiques like EPAM ($EPAM), Endava ($DAVA) and Globant ($GLOB) and my personal favourite Reply SpA ($REY $REY.IM)", "author": "thegorillagame", "created_time": "2025-05-19T22:11:28", "url": "https://reddit.com/r/SecurityAnalysis/comments/1kqosz7/check_out_my_primer_on_it_services/", "upvotes": 12, "comments_count": 0, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "SecurityAnalysis", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kqprov", "title": "$7M into LCID across 3 accounts. Betting Tesla stumbles, <PERSON><PERSON> wakes up. Am I early or cooked?", "content": "This is real. $7M into LCID across 3 different accounts (screenshots above— redacted for privacy, LCID only).\n\nI’ve been watching <PERSON><PERSON> tweet like a politician while <PERSON>id quietly builds. Gravity SUV’s coming, Tesla’s brand taking hits, and Lucid’s sitting at sub-$3 like it’s 2020 again.\n\nIf they capture even a slice of Tesla’s market share — 5%, hell, even 3% — the upside’s there.\nYeah, I know the risks. This is either a setup for a massive win or a long-term relationship with my red bags.\n \nLet me have it — \n\nVote in the comments:\nA) Visionary\nB) Bag holder in denial\nC) $20 by next year\nD) Should’ve gone all in on NVIDIA\n", "author": "Psychological_Ask301", "created_time": "2025-05-19T22:54:53", "url": "https://reddit.com/r/wallstreetbets/comments/1kqprov/7m_into_lcid_across_3_accounts_betting_tesla/", "upvotes": 1052, "comments_count": 1836, "sentiment": "bullish", "engagement_score": 4724.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}]