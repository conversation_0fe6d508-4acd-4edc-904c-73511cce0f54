[{"platform": "reddit", "post_id": "reddit_1j3vrqi", "title": "I don’t get why teachers are having a problem with AI. Just use google docs with versioning.", "content": "If you use Google docs with versioning you can go through the history and see the progress that their students made. If there’s no progress and it was done all at once it was done by AI. ", "author": "FoodExisting8405", "created_time": "2025-03-05T05:18:42", "url": "https://reddit.com/r/artificial/comments/1j3vrqi/i_dont_get_why_teachers_are_having_a_problem_with/", "upvotes": 4, "comments_count": 91, "sentiment": "neutral", "engagement_score": 186.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j3wafb", "title": "Small businesses are actually not as small as I thought, they cost a lot to run", "content": "Running a small business is wild. One day, you’re convinced you’re onto something huge. The next, you’re staring at your bank account wondering how a “simple small  business” somehow drained your savings.\n\nThe ups and downs are real, but let’s be honest, walking away isn’t even an option. Because despite the chaos, the long nights, and the unexpected costs, there’s nothing else I’d rather be doing.", "author": "Animeproctor", "created_time": "2025-03-05T05:49:53", "url": "https://reddit.com/r/smallbusiness/comments/1j3wafb/small_businesses_are_actually_not_as_small_as_i/", "upvotes": 869, "comments_count": 130, "sentiment": "bullish", "engagement_score": 1129.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j3yrlr", "title": "Google Is Hobbling Popular Ad Blocker uBlock Origin on Chrome", "content": "", "author": "mWo12", "created_time": "2025-03-05T08:47:33", "url": "https://reddit.com/r/privacy/comments/1j3yrlr/google_is_hobbling_popular_ad_blocker_ublock/", "upvotes": 923, "comments_count": 163, "sentiment": "neutral", "engagement_score": 1249.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j3zlms", "title": "Am I the only one buying MSFT and GOOGL these days?", "content": "I waited for MSFT drop to below 400$ for almost a year. Now it seems a good buying opportunity to me. \nFor Google the news about Waymo/Uber partnership is reassuring.\n\nI don’t see any thread to Google and Microsoft businesses because of Trump tariffs policy. \n\nAm I the only one doing that now?\n\nMy portfolio: https://app.mecompounding.com/reference/53616c7465645f5fd560f91ed4f0abaae504e965e0be044a7a691cb829dc7aaaaf1f936e3e4631a9a6ed90e9e81c0b3e", "author": "Ok-Championship4945", "created_time": "2025-03-05T09:51:59", "url": "https://reddit.com/r/dividends/comments/1j3zlms/am_i_the_only_one_buying_msft_and_googl_these_days/", "upvotes": 95, "comments_count": 84, "sentiment": "bullish", "engagement_score": 263.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j4186q", "title": "How many of your new clients actually find you through Google?", "content": "Hey guys, just curious—how many of your new clients actually find you through Google? Do you invest in **SEO**, or do most of your leads come from referrals, social media, or ads?\n\nI know some businesses that get a ton of traffic just by ranking well locally, while others don’t really see much from their website. If your site is bringing in clients, what’s working for you? And if not, do you think SEO is worth it?\n\n", "author": "Ok-Top943", "created_time": "2025-03-05T11:46:06", "url": "https://reddit.com/r/smallbusiness/comments/1j4186q/how_many_of_your_new_clients_actually_find_you/", "upvotes": 13, "comments_count": 49, "sentiment": "neutral", "engagement_score": 111.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j4284t", "title": "Game Ready Driver 572.70 FAQ/Discussion", "content": "# [GeForce Hotfix Display Driver version 572.75](https://nvidia.custhelp.com/app/answers/detail/a_id/5640/kw/hotfix)\n\nGeForce *Hotfix* Driver Version 572.75 GeForce *Hotfix* Display Driver version 572.75 is based on our latest Game Ready Driver 572.70. \n\n* \\[GeForce RTX 5080/5090\\] Graphics cards may not run at full speeds on system reboot when overclocked \\[5088034\\]\n* \\[GeForce RTX 50 series\\] GeForce RTX 50 series GPUs crashes with black screen  \\[5120886\\]\n\n[Click here](https://international.download.nvidia.com/Windows/572.75hf/572.75-desktop-notebook-win10-win11-64bit-international-dch.hf.exe) to download the GeForce *Hotfix* display driver version 572.75 for Windows 10 x64 / Windows 11 x64. \n\nHotfix driver needs to be downloaded from the article linked above or the direct download link above. It will not show up in driver search or Nvidia App. These fixes in Hotfix driver will also get folded into the next WHQL driver.\n\n\\---------\n\n# Game Ready Driver 572.70 has been released.\n\n# If you cannot find the driver in NVIDIA Website Search or not showing in NVIDIA App, please give it time to propagate.\n\n**Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/geforce-rtx-5070-game-ready-driver/)\n\n**Game Ready Driver Download Link**: [Link Here](https://us.download.nvidia.com/Windows/572.70/572.70-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n**New feature and fixes in driver 572.70:**\n\n**Game Ready** \\- This new Game Ready Driver supports the new GeForce RTX 5070 GPU and provides the best gaming experience for the latest new games supporting DLSS 4 technology including FragPunk. Further support for new titles leveraging DLSS technology includes the free upgrade for Grand Theft Auto V.\n\n**Gaming Technology** \\- Adds support for the GeForce RTX 5070\n\n**Fixed Gaming Bugs**\n\n* N/A\n\n**Fixed General Bugs**\n\n* PC may boot to a black screen when connected via DisplayPort with certain monitors \\[5131002\\]\n\n**Open Issues**\n\n* Changing state of \"Display GPU Activity Icon in Notification Area\" does not take effect until PC is rebooted \\[4995658\\]\n* PC may bugcheck IRQL NOT LESS OR EQUAL 0xa during gameplay with HDR enabled \\[5091576\\]\n\n**Additional Open Issues from** [GeForce Forums](https://www.nvidia.com/en-us/geforce/forums/user/15//559105/geforce-grd-57270-feedback-thread-released-3525/)\n\n* Forza Horizon 5 lights flicker at night time \\[5147170\\]\n* \\[RTX 50 series\\] Red Dead Redemption 2 crashes shortly after starting a game in DX12 mode. No issue in Vulkan mode \\[5137042\\] \n* \\[RTX 50 series\\] Display may show black screen when selecting DLDSR resolution \\[5144768\\] \n* \\[RTX 50 series\\] Starfield may disaplay dithering/banding artifacts while in the menu screen \\[5121715\\]\n* \\[RTX 50 series\\] NVIDIA Control Panel setting \"Perform scaling on\" missing \"GPU\" option when connected to a monitor in DSC mode \\[5156168\\]\n* \\[RTX 50 series\\] Cyberpunk 2077 will crash when using Photo Mode to take a screenshot with path tracing enabled \\[5076545\\]\n* Some NVIDIA Control Panel / NVIDIA App settings changes immediately get reset or give error \\[5160516\\] -> A new profile update has been pushed to correct this issue. Please reinstall the latest Game Ready Driver using the URL below and select \"Express (Recommended)\" installation to preserve your settings. [https://www.nvidia.com/en-us/geforce/game-ready-drivers/](https://www.nvidia.com/en-us/geforce/game-ready-drivers/) Once the driver installation has completed, reboot your PC.\n\n**Driver Downloads and Tools**\n\nDriver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n\nLatest Game Ready Driver: **572.70** WHQL\n\nLatest Studio Driver: **572.60** WHQL\n\nDDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n\nDDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n\n**DDU/WagnardSoft Patreon:** [**Link Here**](https://www.patreon.com/wagnardsoft)\n\nDocumentation: [Game Ready Driver 572.70 Release Notes](https://us.download.nvidia.com/Windows/572.70/572.70-win11-win10-release-notes.pdf) | [Studio Driver 572.60 Release Notes](https://us.download.nvidia.com/Windows/572.60/572.60-win10-win11-nsd-release-notes.pdf)\n\nNVIDIA Driver Forum for Feedback: [Link Here](https://www.nvidia.com/en-us/geforce/forums/user/15//559105/geforce-grd-57270-feedback-thread-released-3525/)\n\n**Submit driver feedback directly to NVIDIA**: [Link Here](https://forms.gle/kJ9Bqcaicvjb82SdA)\n\n[r/NVIDIA](https://new.reddit.com/r/NVIDIA/) Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\nHaving Issues with your driver? Read here!\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue**\n\nThere is only one real way for any of these problems to get solved, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows 10 or 11\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\nIf it still crashes, we have a few other troubleshooting steps but this is fairly involved and you should not do it if you do not feel comfortable. Proceed below at your own risk:\n\n* A lot of driver crashing is caused by Windows TDR issue. There is a huge post on GeForce forum about this [here](https://forums.geforce.com/default/topic/413110/the-nvlddmkm-error-what-is-it-an-fyi-for-those-seeing-this-issue/). This post dated back to 2009 (Thanks Microsoft) and it can affect both Nvidia and AMD cards.\n* Unfortunately this issue can be caused by many different things so it’s difficult to pin down. However, editing the [windows registry](https://www.reddit.com/r/battlefield_4/comments/1xzzn4/tdrdelay_10_fixed_my_crashes_since_last_patch/) might solve the problem.\n* Additionally, there is also a tool made by Wagnard (maker of DDU) that can be used to change this TDR value. [Download here](http://www.wagnardmobile.com/Tdr%20Manipulator/Tdr%20Manipulator%20v1.1.zip). Note that I have not personally tested this tool.\n\nIf you are still having issue at this point, visit [GeForce Forum for support](https://forums.geforce.com/default/board/33/geforce-drivers/) or contact your manufacturer for RMA.\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n\n**Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n* **What does the new Power Management option “Optimal Power” means? How does this differ from Adaptive?** *The new power management mode is related to what was said in the Geforce GTX 1080 keynote video. To further reduce power consumption while the computer is idle and nothing is changing on the screen, the driver will not make the GPU render a new frame; the driver will get the one (already rendered) frame from the framebuffer and output directly to monitor.*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations. The software will not be perfect and there will be issues for some people. For a more comprehensive list of open issues, please take a look at the Release Notes. Again, I encourage folks who installed the driver to post their experience here... good or bad.\n\n*Did you know NVIDIA has a Developer Program with 150+ free SDKs, state-of-the-art Deep Learning courses, certification, and access to expert help. Sound interesting?* [Learn more here](https://nvda.ws/3wCfH6X)*.*", "author": "Nestledrink", "created_time": "2025-03-05T12:44:50", "url": "https://reddit.com/r/nvidia/comments/1j4284t/game_ready_driver_57270_faqdiscussion/", "upvotes": 188, "comments_count": 1287, "sentiment": "bearish", "engagement_score": 2762.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j44i96", "title": "Google urges the US government to avoid company's breakup", "content": "", "author": "Consistent-Age5347", "created_time": "2025-03-05T14:38:09", "url": "https://reddit.com/r/privacy/comments/1j44i96/google_urges_the_us_government_to_avoid_companys/", "upvotes": 538, "comments_count": 90, "sentiment": "neutral", "engagement_score": 718.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j455de", "title": "Elon & Tesla are getting short end of the stick with support for Trump/DOGE - What is the end game?", "content": "Hey there, let’s chat about what’s been going on with <PERSON><PERSON> lately, especially with his ties to Trump and that whole DOGE thing. Honestly, it’s not looking too great these days, and I’ve been mulling it over. Tesla’s stock has taken a real beating—down more than 35% from its peak, which is tough to stomach if you’re an investor or just a fan of the company. And it’s not just numbers on a screen; people are getting upset in the real world too. There’s been vandalism hitting Tesla cars and even the charging stations, which is crazy to think about. It feels like there’s this growing wave of frustration—or even outright anger—directed at <PERSON><PERSON> and the folks who drive them, not just here but all over the globe.\n\nI can’t help but wonder where this is all headed. Some countries might start slapping extra tariffs on Tesla vehicles, maybe as a way to push back against Elon’s moves or just to make a point. It’s like the walls are closing in a bit, you know? And for what? I keep asking myself if <PERSON><PERSON>’s big gamble here is backfiring. Teaming up with <PERSON> and jumping into this DOGE mess—did he accidentally set himself up for a fall? <PERSON>’s the kind of guy who might lean on <PERSON><PERSON> for as long as it suits him, then just walk away when he’s done, leaving <PERSON><PERSON> holding the bag. And that bag could be full of some pretty massive losses.\n\nIt’s hard not to think about the bigger picture too. If the political winds shift—say, the next president’s a Democrat—things could get even dicier for <PERSON>on. People are already tossing around wild ideas, like him facing legal trouble or, worst case, even prison. I don’t know if it’ll go that far, but it’s the kind of thing that keeps you up at night if you’re rooting for him. Right now, it’s tough to spot the silver lining. What’s the upside for <PERSON>on or <PERSON>sla in all this chaos? I’m scratching my head trying to figure it out, because from where I’m sitting, it looks like a rough road ahead with not much to show for it. What do you think—am I missing something here?", "author": "GroveResident", "created_time": "2025-03-05T15:07:04", "url": "https://reddit.com/r/teslainvestorsclub/comments/1j455de/elon_tesla_are_getting_short_end_of_the_stick/", "upvotes": 141, "comments_count": 362, "sentiment": "bearish", "engagement_score": 865.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j498mr", "title": "[Update] Has anyone completed a 20 year lease from SolarCity (Tesla now)? Did they take your panels?", "content": "# ORIGINAL POST:\n\n[https://www.reddit.com/r/solar/comments/x7gjbd/has\\_anyone\\_completed\\_a\\_20\\_year\\_lease\\_from/](https://www.reddit.com/r/solar/comments/x7gjbd/has_anyone_completed_a_20_year_lease_from/)\n\n# Update: 6/1/2025\n\nMy panels are off my roof and everything is fixed!  I am actually quite surprised and happy at how well this entire thing ended for me, considering how awful the entire start was, but I'll give it to <PERSON><PERSON>, they really came through at the end, and I honestly believe a lot of the mess of leaving the panels was actually the local contractors fault... and I believe it was either incompetence or shady business practice, so let's get into it.\n\nWhere I left off was around March 2025, where I had studs sticking out of roof, inverter still on the wall, panels in my yard, wire and brackets just on the side, with no answers or communication from <PERSON><PERSON>.\n\nFinally <PERSON><PERSON> scheduled a panel removal in April.  When they arrived, I was very meticulous about the scope of work they would be doing, and they told me they'd be reomving the panels, taking all the wire away, removing the inverter and basically undoing everything.  I asked about the roof, and they said they need to go source tiles from the local roof tile supplier, and get as close to a color match but they would be replacing all the roof tiles that are cut free of charge, but they weren't able to do it then and there.  I was hesitant because I didn't want to be left with it but they swapped 1 tile to bring wth them to match the color, so I said okay.\n\nThe next week I got a schedule date for May 28th for them to come replace the roof tiles.  When they came on May 28th, I asked about the scope of work and was surprised - they were doing a full repair.  They removed the old cut tiles, removed all the pucks, sealed every hole / puncture, and replaced any paper in large areas that may have torn or been hazardous for a leak.  They said they used something called like Solar Flex / Solar Seal.  It's basically a roof repair type thing that fixes roofs.\n\nSo when all is said and done, they repaired my roof, they removed all my equipment, and they even gave me 10 free tiles to replace any cracked tiles in the future.  A+ customer service right at the end.\n\n# So what happened?  Why was there miscommunication?\n\nWhen they originally came to remove the panels from my yard and the inverter in April, I asked them wtf happened and they essentially said my account got stuck in a kind of limbo state.  Apparently the contractor that came out to remove the panels from my roof marked the job as complete - the issue is, he was originally SUPPOSED to remove everything, the roof, the inverter, the wiring, and take it all away, and he left everything in the yard.  He marked it as complete, but Tesla never got the equipment, so it never went through the channels and had the correct assignments.  He was showing me on his tablet too so I was able to verify it.\n\nIt's weird he would mark it as complete, but the local contractor was kind of shit talking Tesla like 'Yeah these big name companies always do this kinda thing, so you know I'm not saying you should or need to but if you want, if they just leave these panels here, I'll be happy to come by and take them or you know, I can just put them back up and connect them if they just never pick them up.  Up to you man.\"\n\nHe was really stressing the whole idea of coming back to set it up, which would be an easy install for him since all the wiring and everything is there, and I'm willing to bet that would be an easy way for him to make money.  It seems kind of like a nefarious way to make some money doing what I would assume is easy work.  He also stressed that the pucks they used, there's no way for them to be removed.  The local contractor said 'Yeah man those kinds of stands, they're not normal anchors, they're IN your roof so you can't remove them at all.\"\n\nBut when Tesla came out to get thepanels I asked them and they were like 'uhhh no we can remove the studs and put newtile over the pucks.  Yeah I don't know why he'd say that.\"\n\nSo overall it seems the contractor either messed up, or was trying to get some quick easy contract money by hoping I would call him and be like 'Hey, Tesla never came and picked up my shit, fuck them can you come install them and get me free solar?\" and maybe he'd charge like $500 for the labor since all the parts are already there.\n\nAnyways, that's the end of it.  Happy ending, pretty rough going through it and had to be methodical to get it, but I'm glad it worked out in the end.\n\nGood luck to everyone else out there!\n___\n\n# Update: 3/5/2025\n\nHi everyone, I've been getting messages over the last 3 years since I've made the post, asking about what has happened, so I wanted to tell everyone what happened, and include photos, yay!\n\nI'll add the date and anything relevant to updates at the top of this post with the dates.\n\n**Spoiler Alert / tl;dr**: The entire Panel Removal / end of lease experience has been shitty, and I'm still \"working\" with Tesla on it.  Onto the ~~fun~~ annoying story!\n\n# So to catch up:\n\nI bought a house that came with one of the first SolarCity panels installed - it was installed around September 2003.  It was a Lease Contract with my final payment being in August of 2024.  I had asked about what happens at the end of a Lease, especially with Tesla buying the contract out, and no one really had experience so I figure I'd write and document my experience.\n\n___\n\n# July 31st, 2024:\n\nReached out to SolarCity about questions involving the removal of the panels:\n\n[https://pastebin.com/NRgxE35q](https://pastebin.com/NRgxE35q)\n\n___\n\n# August 2024:\n\nAfter I made my final payment in August 2024, our panels and app kept working as expected, we just didn't have to make any payments.  Finally I signed the documents at the end of the Lease, and when I asked what the next steps were, they told me someone from Tesla would reach out to me to schedule the removal.\n\n___\n\n# November 4th, 2024:\n\nI received an email / Text that let me know my panels needed maintenance and servicing, and to reach out in the Tesla App.  When I went into the Tesla App, I had a ! next to appointments, so I went and scheduled the panel removal.  I added the date to schedule and set it for January 29th removal.\n\nI went into the support chat and asked the following questions and received the answers:\n\n* \\*Who is removing the panels?\n   * A local company contracted and insured by Tesla.\n* If there are issues, who do I contact?\n   * I contact Tesla since it's insured.\n* How long will it take?\n   * It will take 1 workday. Roof repairs and everything included.\n* Will they take the panels with them? Will they be hauled same day?\n   * Yes, they will be hauled sameday.\n\n___\n\n# January 29th, 2025:\n\nI had an 11am appointment, and a local contractor came out, introduced himself, dude was super nice, but just contracted through Tesla, not actually working for Tesla.\n\nI asked him what he was going to be doing today and he said he was going to remove the panels from the roof, and store them in our yard and Tesla will come and pick them up.  I asked about the inverter, and he said he wasn't going to do the inverter.\n\nAfter some back and forth he showed me and told me his scope of work, contracted from Tesla, was to just Remove the Panels, and place them on the side of my yard.  He wasn't licensed to do the electrical part, and he was being straight up with me like \"Yeah man, I'll be honest I'm not licensed to touch the electrical, I get what you're saying, but you don't want me to touch that stuff.  I can do roof things all day for sure.\"\n\nSo I told him, okay well, I don't want him to breach his contract and signed off scope of work with Tesla, so I let him do his thing.\n\nWhile he was working on the roof, I jumped in chat on [tesla.com](http://tesla.com), I forgot what options I selected, since most options were 'Use the App!' but I chose some option that allowed for me to chat with someone.\n\nHere's the chat:\n\n[https://pastebin.com/RPvVMYYs](https://pastebin.com/RPvVMYYs)\n\nAnd that was the end of my chat.  So the panels got removed, my roof looks like shit, and I still have the inverter on my wall.\n\nHere's the photos of how my yard and roof currently look:\n\n[https://postimg.cc/gallery/XFt3461/779c1f32](https://postimg.cc/gallery/XFt3461/779c1f32)\n\n**Edit: Side note about the roof tiles**\n\nI have a tile roof, and my contractor said this is the first time he's ever seen solarcity panels on a roof tile like this.\n\nAll the wording from the SolarCity / Tesla seems to acknowledge this is odd - normally under composite or shingle they use Pucks they can remove.  We have roof jacks.\n\nThe roof jack is glued down to the black paper, and drilled in, so in order to remove the roof jacks, we have to replace the black paper, seal up the hole, replace the roof tile.  \n\nAs you can see in my photo, our tiles are all cut so shittily.  If they are to repair my roof, they need to order new tiles, so this is going to become a huge ordeal.\n\n___\n\n# February 5th, 2025:\n\nReached out again to Web Chat to see what is going on.\n\n[https://pastebin.com/EfEbmKQv](https://pastebin.com/EfEbmKQv)\n\nI was told my case manager would call me End of Day.  Never received any calls.\n\n___\n\n# February 6th, 2025:\n\nReached out again to find out what's going on, since it's been a week.\n\n[https://pastebin.com/Lx2A38f2](https://pastebin.com/Lx2A38f2)\n\n___\n\n# February 28th, 2025:\n\nI decided to just wait about a month, it was a busy month and you know what, let's just give em a month and see what's going on.\n\nPrepare to get angry.\n\nInstead of the web browser app, I decided to open a ticket inside my Tesla App, and saw I had a ticket in progress and that I could start a chat.  So I started a chat.  I took screenshots of each conversation.  I'll just let you read it.  I put two links, one a direct and another the website.\n\nChat 1: [https://postimg.cc/Cd2Cqg5Q](https://postimg.cc/Cd2Cqg5Q)\n\n[https://i.postimg.cc/cZYPg6kg/Solar-Chat1.png](https://i.postimg.cc/cZYPg6kg/Solar-Chat1.png)\n\nChat 2: [https://postimg.cc/7fT31WTY](https://postimg.cc/7fT31WTY)\n\n[https://i.postimg.cc/5bVTw1DP/Solar-Chat2.png](https://i.postimg.cc/5bVTw1DP/Solar-Chat2.png)\n\nI'll try to summarize:\n\nChat 1: A guy named Robert, from the **Remove & Reinstallation team**, said they don't handle the end of lease.  He told me he'll transfer me to the Energy Support Team.  He transferred me, I instantly got disconnected.\n\nChat 2: I matched with Robert again, and he instantly disconnected (lmao), and connected with another Remove + Reinstallation Team.  They transferred me.  The person who I got transferred to, simply just said 'Call the number' but the Tesla number he gave me, literally told me to talk to them in the app for support, which was what I was doing.\n\nFinally, while on the chat, I decided to try something, I'm not advocating for this but it did get me some actual help.\n\nI called the Tesla Number, went to the Panel portion, and there's an option for Emergencies.  It told me 'This is ONLY for emergencies.'\n\nThe moment someone answered I just quickly said 'Hey I don't know why I'm here, I was supposed to be transferred to The Energy Support Team for my panel removal questions.\" and the guy quickly said 'Okay let me transfer you.'\n\nI was transferred to Technical Support, which isn't quite what I needed, HOWEVER, this was someone I was actually able to talk to and I explained my situation and they felt so bad.  They gave me great information.\n\nAnd I completely Understood that she was technical support, and not really understanding how end of leases work, but she had access to the system to see notes and clarify things.\n\nWhat I found out is:\n\n* My Case was transferred to a different manager on February 5th.\n* The new Case Manager has been emailing two separate Field Service Managers for Tesla in my area, every 2-3 days.  I asked for dates and was given:\n   * Feb 18\n   * Feb 21\n   * Feb 25\n   * Feb 26\n* The reason panels are in my yard is the Field Service Manager is not responding to my case manager.\n* She recommended I email my original case manager, I did and have not heard back anything.\n* She said she will personally send an email out to the new case manager explaining I'm left in the dark and if I could at least get any kind of update, even something as simple as 'Hey, we are still working on the case and I am waiting for a response from x y z.' rather than radio silence.\n\nAnd that is where I am at as of 3/5/2025.  No communication since.", "author": "hngfff", "created_time": "2025-03-05T17:57:04", "url": "https://reddit.com/r/solar/comments/1j498mr/update_has_anyone_completed_a_20_year_lease_from/", "upvotes": 46, "comments_count": 41, "sentiment": "bullish", "engagement_score": 128.0, "source_subreddit": "solar", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j4f0r3", "title": "Google's new AI Mode is a huge leap away from search as we know it", "content": "", "author": "thisisinsider", "created_time": "2025-03-05T21:48:16", "url": "https://reddit.com/r/artificial/comments/1j4f0r3/googles_new_ai_mode_is_a_huge_leap_away_from/", "upvotes": 63, "comments_count": 55, "sentiment": "neutral", "engagement_score": 173.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j4ftx1", "title": "You knew it was coming: Google begins testing AI-only search results", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-03-05T22:20:35", "url": "https://reddit.com/r/technology/comments/1j4ftx1/you_knew_it_was_coming_google_begins_testing/", "upvotes": 2962, "comments_count": 455, "sentiment": "neutral", "engagement_score": 3872.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1j4g8g4", "title": "Thanks Google for forcing us out of Chrome", "content": "", "author": "nbayat", "created_time": "2025-03-05T22:37:04", "url": "https://reddit.com/r/chrome/comments/1j4g8g4/thanks_google_for_forcing_us_out_of_chrome/", "upvotes": 986, "comments_count": 265, "sentiment": "neutral", "engagement_score": 1516.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}]