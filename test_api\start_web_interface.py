#!/usr/bin/env python3
"""
AI对冲基金准确性分析Web界面启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """检查依赖包"""
    required_packages = ['flask', 'flask-cors', 'pandas']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    webbrowser.open('http://localhost:5000')

def main():
    """主函数"""
    print("🚀 启动AI对冲基金准确性分析Web界面...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查数据目录
    reasoning_logs_dir = "reasoning_logs/accuracy_tracking"
    if not os.path.exists(reasoning_logs_dir):
        print(f"⚠️  准确性跟踪数据目录不存在: {reasoning_logs_dir}")
        print("请先运行带有 --track-accuracy 参数的回测实验:")
        print("python src/backtester.py --tickers AAPL --track-accuracy --save-reasoning")
        
        # 创建目录以避免错误
        os.makedirs(reasoning_logs_dir, exist_ok=True)
    
    # 设置环境变量
    os.environ['FLASK_ENV'] = 'development'
    
    # 延迟打开浏览器
    Timer(2.0, open_browser).start()
    
    print("📊 Web界面功能:")
    print("   - 代理准确率排名和统计")
    print("   - 交互式图表和可视化")
    print("   - 实验数据对比分析")
    print("   - 数据导出功能")
    print()
    print("🌐 访问地址: http://localhost:5000")
    print("📱 支持桌面和移动端访问")
    print()
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        # 启动Flask应用
        from src.web.app import app
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 Web界面已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
