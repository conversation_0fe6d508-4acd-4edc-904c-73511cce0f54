#!/usr/bin/env python3
"""
SSL连接测试脚本
用于诊断和测试 api.financialdatasets.ai 的SSL连接问题
"""

import os
import sys
import time
import ssl
import socket
import requests
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_connection():
    """测试基本的SSL连接"""
    print("=== 测试基本SSL连接 ===")
    
    try:
        # 测试基本的socket连接
        context = ssl.create_default_context()
        with socket.create_connection(('api.financialdatasets.ai', 443), timeout=30) as sock:
            with context.wrap_socket(sock, server_hostname='api.financialdatasets.ai') as ssock:
                print(f"✓ SSL连接成功")
                print(f"  协议版本: {ssock.version()}")
                print(f"  加密套件: {ssock.cipher()}")
                return True
    except Exception as e:
        print(f"✗ SSL连接失败: {e}")
        return False

def test_requests_connection():
    """测试使用requests库的连接"""
    print("\n=== 测试requests库连接 ===")
    
    # 测试不同的SSL配置
    configs = [
        {"name": "默认配置", "verify": True, "timeout": (30, 120)},
        {"name": "禁用SSL验证", "verify": False, "timeout": (30, 120)},
        {"name": "自定义SSL上下文", "verify": True, "timeout": (30, 120), "custom_ssl": True},
    ]
    
    for config in configs:
        print(f"\n--- {config['name']} ---")
        try:
            session = requests.Session()
            
            if config.get("custom_ssl"):
                # 创建自定义SSL上下文
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = True
                ssl_context.verify_mode = ssl.CERT_REQUIRED
                ssl_context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
                
                # 配置重试策略
                retry_strategy = Retry(
                    total=3,
                    backoff_factor=2,
                    status_forcelist=[429, 500, 502, 503, 504],
                    allowed_methods=["GET", "POST"],
                )
                adapter = HTTPAdapter(max_retries=retry_strategy)
                session.mount("https://", adapter)
            
            # 设置请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
            })
            
            # 测试API端点
            url = "https://api.financialdatasets.ai/financial-metrics/?ticker=AAPL&report_period_lte=2025-06-01&limit=10&period=ttm"
            
            # 添加API密钥（如果有的话）
            headers = {}
            if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
                headers["X-API-KEY"] = api_key
                print(f"  使用API密钥: {api_key[:10]}...")
            else:
                print("  未找到API密钥")
            
            response = session.get(
                url, 
                headers=headers, 
                verify=config["verify"], 
                timeout=config["timeout"]
            )
            
            print(f"✓ 请求成功")
            print(f"  状态码: {response.status_code}")
            print(f"  响应大小: {len(response.content)} bytes")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  JSON解析成功，包含 {len(data.get('financial_metrics', []))} 条记录")
                except:
                    print("  JSON解析失败")
            
            return True
            
        except requests.exceptions.SSLError as e:
            print(f"✗ SSL错误: {e}")
        except requests.exceptions.ConnectionError as e:
            print(f"✗ 连接错误: {e}")
        except requests.exceptions.Timeout as e:
            print(f"✗ 超时错误: {e}")
        except Exception as e:
            print(f"✗ 其他错误: {e}")
    
    return False

def test_with_retries():
    """测试带重试机制的连接"""
    print("\n=== 测试重试机制 ===")
    
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            print(f"尝试 {attempt + 1}/{max_attempts}")
            
            session = requests.Session()
            
            # 配置重试策略
            retry_strategy = Retry(
                total=5,
                backoff_factor=2,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["GET", "POST"],
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("https://", adapter)
            
            # 设置请求头
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Connection': 'keep-alive',
            })
            
            url = "https://api.financialdatasets.ai/financial-metrics/?ticker=AAPL&report_period_lte=2025-06-01&limit=5&period=ttm"
            
            headers = {}
            if api_key := os.environ.get("FINANCIAL_DATASETS_API_KEY"):
                headers["X-API-KEY"] = api_key
            
            response = session.get(url, headers=headers, verify=True, timeout=(30, 120))
            
            if response.status_code == 200:
                print(f"✓ 请求成功！状态码: {response.status_code}")
                try:
                    data = response.json()
                    print(f"  获取到 {len(data.get('financial_metrics', []))} 条财务指标数据")
                except:
                    print("  响应不是有效的JSON格式")
                return True
            else:
                print(f"✗ 请求失败，状态码: {response.status_code}")
                print(f"  响应内容: {response.text[:200]}...")
                
        except (requests.exceptions.SSLError, requests.exceptions.ConnectionError) as e:
            if attempt < max_attempts - 1:
                wait_time = 2 ** attempt
                print(f"✗ 连接错误: {str(e)[:100]}...")
                print(f"  等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"✗ 最终失败: {e}")
                return False
        except Exception as e:
            print(f"✗ 其他错误: {e}")
            return False
    
    return False

def test_api_function():
    """测试修改后的API函数"""
    print("\n=== 测试修改后的API函数 ===")
    
    try:
        from src.tools.api import get_financial_metrics
        
        print("调用 get_financial_metrics('AAPL', '2025-06-01')...")
        result = get_financial_metrics('AAPL', '2025-06-01', limit=5)
        
        if result:
            print(f"✓ API函数调用成功！获取到 {len(result)} 条记录")
            if result:
                print(f"  最新记录日期: {result[0].report_period}")
                print(f"  市值: {result[0].market_cap}")
            return True
        else:
            print("✗ API函数返回空结果")
            return False
            
    except Exception as e:
        print(f"✗ API函数调用失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始SSL连接诊断测试...")
    print(f"Python版本: {sys.version}")
    print(f"OpenSSL版本: {ssl.OPENSSL_VERSION}")
    
    # 检查环境变量
    if os.environ.get("FINANCIAL_DATASETS_API_KEY"):
        print("✓ 找到API密钥")
    else:
        print("⚠ 未找到FINANCIAL_DATASETS_API_KEY环境变量")
    
    results = []
    
    # 运行各项测试
    results.append(("基本SSL连接", test_basic_connection()))
    results.append(("requests库连接", test_requests_connection()))
    results.append(("重试机制测试", test_with_retries()))
    results.append(("API函数测试", test_api_function()))
    
    # 输出测试结果总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    for test_name, success in results:
        status = "✓ 通过" if success else "✗ 失败"
        print(f"{test_name:<20} {status}")
    
    success_count = sum(1 for _, success in results if success)
    print(f"\n总计: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！SSL连接问题已解决。")
    elif success_count > 0:
        print("⚠ 部分测试通过，SSL连接问题可能已部分解决。")
    else:
        print("❌ 所有测试都失败了，需要进一步诊断。")

if __name__ == "__main__":
    main()
