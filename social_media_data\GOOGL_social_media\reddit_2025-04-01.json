[{"platform": "reddit", "post_id": "reddit_1jon0e8", "title": "What do Tier1 companies do if Google suspends them?", "content": "We recently got the obscure misrepresentation suspension for our brand which is quite mature (several years old). We run across multiple regions and have separate sub-accounts per region. This was deliberate to minimise risk per account, and give better control of product-set/pricing/currency per region. It just felt cleaner.\n\nWe had 3 subaccounts suspended a few days ago. I've experienced this before, paid a third party for an audit, essentially tried everything they suggested (including improving a lot of content etc). Didn't work. Ended up deleting the sub-account, recreating and didn't have a problem at all. Very inconsistent.\n\nWe've just migrated one of these subaccounts to a new domain (better suited) and have recreated successfully. It meant we had to drop our historical PMax campaign data for this region, but didn't see another option.\n\nI'm yet to request review on the other two just yet as my experience gives me the impression it'll just automatically get rejected again.\n\nI did notice something very strange in our account details though (seems like a Google bug). For context, we started in Australia (we are an Australian based company). The latest sub-account we created was for Canada - and this was about 3 months ago. When I go back to the Australian sub-account (and the parent account), both display \"Canada\" as the country in the \"Business Info\" section. Note, this is not editable. There's no way we originally set it up with \"Canada\" selected - it seems like somehow when creating the sub-account it randomly reset the parent company. With all the UI bugs I've seen recently (and the lack of support) it really wouldn't surprise me that their system could break like this.\n\nAnyhow, I have no idea if that could be a trigger for the misrepresentation - but I just don't see a fix for it. I submitted a couple of tickets with varying information and they all come back with the annoying/useless responses:\n\n>I understand your concern regarding the suspension of your Google Merchant Center Account and I appreciate that you want to fix this issue on a priority basis. I apologize for the inconvenience caused. \n\n>I do understand your concern related to the country name, please be informed that the country name in the account cannot be changed after creating the account. And we may not be able to pinpoint if this could be the possible reason for the suspension of your Merchant Center account.\n\n>Upon checking your Merchant Center account, I found that it has been suspended due to [Misrepresentation](https://support.google.com/merchants/answer/6150127?hl=en&sjid=1238185107008396898-AP). \n\n>This policy suspension (Shopping ads) / limited visibility (Free Listings) means that we have reviewed your Google Merchant Center account and concluded that it does not comply with our Shopping Ads and/or Free Listing policies, and we have therefore disapproved your Google Merchant Center account. \n\n>Google doesn't want users to feel misled by the content promoted in Shopping ads and free listings, and that means being upfront, honest, and providing shoppers with the information that they need to make informed decisions. \n\n>You can resolve this issue by: \n\n>Review your account and online store\n\n>Ensure you meet our Shopping Ads and/or Free Listing policies\n\n>Provide additional information to verify your business\n\n>We recommend you complete the following if prompted to do so, for example:\n\n>For non-EU merchants: complete identity verification if the option is available before requesting a re-review. \n\nI understand the support channel sucks. I also understand there's no way to contact someone higher up.\n\nBut my question is, what do large tier1 entities do if Google take them for ransom? They just take the huge hit to business and tell shareholders the bad news?\n\nI know I'm not the first person to request assistance on this policy, but man this is utterly frustrating. I really don't like companies like 'GetStubGroup' who charge $3500 and then offer only a partial refund if unable to get the approval - doesn't exactly smell of confidence in their expensive work!\n\nAnyone got any other insight/info?", "author": "Floorman1", "created_time": "2025-04-01T04:14:56", "url": "https://reddit.com/r/adwords/comments/1jon0e8/what_do_tier1_companies_do_if_google_suspends_them/", "upvotes": 0, "comments_count": 5, "sentiment": "bearish", "engagement_score": 10.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1joo998", "title": "Alright, gotta ask: anyone else sick of building dashboards no one looks at?", "content": "So, my buddy and I are analytics + ML engineers from FAANG, and we keep seeing the same problem over and over.\n\nAnalytics teams are always understaffed, slammed with requests, and grinding out dashboards that business folks barely use. Meanwhile, stakeholders wanna do their own exploring but don’t wanna get their hands dirty. They just wanna ask questions and get answers. Simple, right?\n\nHere’s the kicker: **Our Data Science team is cranking out TWO new dashboards a day** (we’re talking big, fancy dashboards), and they get like **five views a month on average.** It’s insane. All that effort, basically flushed.\n\nHere’s the loop:\n\n* Business folks: *“Can’t we just ask a question and get the answer already?”*\n* Data teams: *“Sure, here’s your 27th dashboard this month. Enjoy.”*\n* Reality: They don’t. They forget about it, and the cycle starts again.\n\nNow we’re thinking... what if you could literally just **talk to your data?** Like, no setup, no building out new dashboards every five seconds. Just asking questions and getting answers, fast.\n\n**I’m curious, though:**\n\n1. Are you running into this same nightmare of building dashboards that nobody uses?\n2. Would something that just lets people chat with their data actually be useful? Or is it just another shiny object?\n3. If you’ve tried anything like this, what totally sucked about it? (We tried Looker Conversational Analytics early preview, and evaluated ThoughtSpot - kinda blah)\n4. What would make something like this genuinely valuable for you?\n5. Also… what’s the dumbest dashboard request you’ve built that ended up getting zero views? 😂\n\nI’ve got a feeling we’re not alone here. Would love to hear your takes. We’re just spitballing ideas here, so be brutally honest. Appreciate you!", "author": "PeachWithBenefits", "created_time": "2025-04-01T05:34:06", "url": "https://reddit.com/r/analytics/comments/1joo998/alright_gotta_ask_anyone_else_sick_of_building/", "upvotes": 278, "comments_count": 102, "sentiment": "bullish", "engagement_score": 482.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1<PERSON><PERSON><PERSON>", "title": "Are you ready to block your entire website from Google?", "content": "I think you know the answer. No.\n\nGoogle released AI overviews for more countries and search queries with the March Core update.\n\nMore and more popular \"white-hat\" SEO experts change their minds. They don't believe Google speakers and guides anymore. They see Google working hard to eat their traffic.\n\nMany discussions around that, but they have led to almost nothing:\n\n1/ Yes, we've already realized that the world of zero-click marketing is inevitable.   \n2/ Yes, we've already realized that there is no sense in creating content for most banal information search terms.\n\nBut what if:\n\n1/ Google goes even further and starts showing AI overviews for more niches and searches?   \n2/ Google starts creating even more of its sites and online stores?\n\nSite owners cannot resist this, because game theory will not allow it.\n\nTo force Google to change, a huge share of content creators must close their content for scanning and indexing by Google.\n\nBut as soon as some part of the creators block websites from Google, the creators who do NOT do it will greatly benefit from it.\n\nSo, it will never happen on the level we need to change Google's behaviour.\n\nThat's why I don't see a mechanism to make Google pay for the content we create.\n\nDo you see?", "author": "<PERSON><PERSON>", "created_time": "2025-04-01T08:09:25", "url": "https://reddit.com/r/SEO/comments/1joqeep/are_you_ready_to_block_your_entire_website_from/", "upvotes": 75, "comments_count": 68, "sentiment": "neutral", "engagement_score": 211.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jorv51", "title": "Google's DeepMind becomes more secretive, due to business and competitive objectives", "content": "According to FT: \"Former staffers suggested the new processes have stifled the release of commercially sensitive research to avoid the leaking of potential innovations. One said that publishing papers on generative AI was “almost impossible”.\n\nIn one incident, DeepMind stopped the publication of research that showed Google’s Gemini language model is not as capable or is less safe than rivals, especially OpenAI’s GPT-4, according to one current employee.\"\n\nWell Google is a business, and it is got to look out for number one. American businesses are not only competing with each other, but they are increasingly worried about competition from China. I would like the US government to increase public funding for AI, but it is unlikely under this current administration. And because there is a lot of private funding for AI.\n\nIdeally science should be a public good, especially publicly funded research. Chinese have benefited from American research in AI. And now the genie's out of the bottle. And many Chinese companies are following open models, where they share their technology with others. I don't think closed science is the best business model for global business. But the proof is in the pudding. Let's see who gets more traction, companies with closed models like OpenAI and Google, or open models from Meta and DeepSeek.\n\nReference: Financial Times ", "author": "fool49", "created_time": "2025-04-01T09:58:26", "url": "https://reddit.com/r/economy/comments/1jorv51/googles_deepmind_becomes_more_secretive_due_to/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jot5ym", "title": "If Google has a way to quantify content quality, why it indexes lorem ipsum?", "content": "Recently I've launched a new site with zero domain authority and backlinks. Today I checked with GSC and saw that 24 out of 530 pages are indexed. When I look into which URLs got indexed, I saw that half of them from the blog template that I forgot to delete, pure lorem ipsum nonsense. Moreover, blog has no links from the main page and there is no main blog page. A user can't reach to them in anyway unless they type the exact URL.  \n  \nWhy Google decided to index those pages instead of main content? How Google decided that these pages are valuable and useful? ", "author": "satyrcan", "created_time": "2025-04-01T11:23:06", "url": "https://reddit.com/r/SEO/comments/1jot5ym/if_google_has_a_way_to_quantify_content_quality/", "upvotes": 33, "comments_count": 105, "sentiment": "neutral", "engagement_score": 243.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jozgv3", "title": "Trad 401k vs Roth 401k & how much to contribute", "content": "I (23F) finally got the first big boy job and am now faced with choosing benefits. Employer offers 50% match on first 6% with my choice to contribute pre-tax to the 401k or post tax to the Roth 401k. My salary is 65k, I have >10k in a HYSA, and no debt. I've heard if you anticipate being a higher tax bracket by retirement you should consider <PERSON> over trad, and unless I have a major career switch that should be more likely than not. Im also not sure if I should consider contributing more than the default 6%  or if I should invest that $ elsewhere.\n\nI want to min max as much as possible with investing and finances, so any thoughts or feedback is appreciated bc all I have rn is google and Reddit.", "author": "deceptivecrayon", "created_time": "2025-04-01T16:10:30", "url": "https://reddit.com/r/FinancialPlanning/comments/1jozgv3/trad_401k_vs_roth_401k_how_much_to_contribute/", "upvotes": 2, "comments_count": 11, "sentiment": "neutral", "engagement_score": 24.0, "source_subreddit": "FinancialPlanning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jp2j5i", "title": "Should I change my SCHD allocation to 0?", "content": "In my early 20’s. I heard it’s better to focus on market gains as opposed reinvesting dividends, is that generally true? Plus something about a taxable event.\n\nRoth IRA\nFXIAX : 40%\nQQQM : 45%\nSCHD : 15%\n\nBasic account \nTesla: (20%)\nApple: (10%)\nGoogle: (15%)\nNvidia: (20%)\nMicrosoft: 20%)\nAmazon: (15%)", "author": "Popster962", "created_time": "2025-04-01T18:12:51", "url": "https://reddit.com/r/dividends/comments/1jp2j5i/should_i_change_my_schd_allocation_to_0/", "upvotes": 3, "comments_count": 51, "sentiment": "bullish", "engagement_score": 105.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jp40py", "title": "How Does My Desktop PC Know My Exact Location Without GPS?", "content": "I recently noticed something strange and a bit concerning.\n\nI have a **custom-built desktop PC** with **no GPS, no mobile data, and no built-in location services**. My phone's **WiFi and location were turned off**, yet when I opened Google Maps (or any other mapping service) on my **PC**, it somehow **knew my exact location**—down to my street.\n\nBut when I **turned off WiFi on my PC**, suddenly, it couldn’t pinpoint my location anymore. It could only estimate based on my IP, which was much less accurate.\n\nAfter some research, I found that this happens because of **WiFi Positioning System (WPS)**. Even if you’re not connected to a WiFi network, your device can still **scan for nearby networks**, and companies like Google, Apple, and Microsoft have massive databases of WiFi locations. Your PC just sends the list of detected networks to their servers, and they use that data to determine your position.\n\nHas anyone else noticed this?  \nWhat do you think about this from a privacy perspective?  \nAre there any ways to fully prevent it besides turning off WiFi?\n\nI’d like to hear other people’s thoughts on this. How much control do we actually have over this kind of tracking?", "author": "suraj_reddit_", "created_time": "2025-04-01T19:11:46", "url": "https://reddit.com/r/privacy/comments/1jp40py/how_does_my_desktop_pc_know_my_exact_location/", "upvotes": 11, "comments_count": 33, "sentiment": "neutral", "engagement_score": 77.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jp43ku", "title": "Harvard research reveals a surprising solution to climate change", "content": "Humans are storytelling creatures. As the world grapples with coordinating to [solve climate change](https://akhilpuri.substack.com/p/metacrisis-the-root-of-all-our-planetary), new research from Harvard shows that a surprising age-old mechanism might hold the answer. In results that seem like satire, the researchers found that [ancient societies coordinated using gossip](https://www.scientificamerican.com/article/the-surprising-benefits-of-gossip/). But the results make sense once we realize that coordinating with someone requires establishing trustworthiness. And how do we establish someone’s trustworthiness? By asking other people about them, i.e. gossiping!\n\nThe research has profound implications for driving the culture change required to usher in systems change. When asked how we could implement findings from the research in today’s world, the researchers replied, ”We are already doing this at scale today. We just call them Podcasts. A bunch of tech bros talking about what they heard from whom and airing their grievances at being misunderstood when they were just trying to make the world a better place”. <PERSON>, <PERSON>, and <PERSON><PERSON> could not be reached for comments on being classified as the world’s top gossips. But the results did prompt <PERSON> to announce a new podcast in another desperate attempt to fool people into liking him.\n\nIn another finding that has implications for solving the AI alignment problem, the researchers focused on how gossip creates shared reality. It is a well-established fact that our brains do not see the world as it is, but act as prediction engines based on historical information. This means that what we see as reality is just our perception. This means that to solve the AI alignment problem, we just need to believe [<PERSON> Andreessen](https://a16z.com/ai-will-save-the-world/) and [Sam Altman](https://www.cnn.com/2023/10/31/tech/sam-altman-ai-risk-taker/index.html) when they answer questions about the AI-driven apocalypse with “Just trust me bro”. AI maximalist David Shapiro vouches for the efficacy of this method, having amassed, in his words, knowledge (strong belief backed by evidence) on how it is all going to turn out fine. \n\nThe research also showed why Kamala Harris lost the election bigly to Donald Trump. She just could not keep the engines of gossip running as fast as Donald Trump. The President, speaking from the Oval Office with a bag of Cheetos, praised the breakthrough research—”I have always said that I have the best gossip. You just need to look at our leaked chat messages. China can’t beat us. They got no gossip. None. Xi wouldn’t let them have it.”\n\nSo there you have it folks. No need for any fancy solutions- no crypto currencies, no[ network states](https://thenetworkstate.com/), no [new economic models](https://doughnuteconomics.org/about-doughnut-economics), no [new cities](https://www.edgeesmeralda.com/), no spiritual awakening. Just gossip a new world into being. To learn more, listen to this 17-hour podcast between [Daniel Schmachtenberger](https://www.youtube.com/watch?v=uA5GV-XmwtM), [Ian McGilchrist and Nate Hagens](https://www.youtube.com/watch?v=F838KOrQrRg)! They clearly have the right idea!\n\n*It should, of course, be obvious by now that this is an April Fool’s Day post. I hope that reading it gave you a little bit of a laugh and served as a reminder to not take everything around us and ourselves too seriously. The future is not yet written. And we might yet find our way out of this mess that surrounds us. And if not, I for one would prefer to go down laughing. Take it easy folks.* \n\n*If you liked this post, you might want to check out my newsletter on Substack where I write about the Metacrisis and systems change-*  [*akhilpuri.substack.com*](http://akhilpuri.substack.com) *:)*", "author": "zenpenguin19", "created_time": "2025-04-01T19:14:59", "url": "https://reddit.com/r/Renewable/comments/1jp43ku/harvard_research_reveals_a_surprising_solution_to/", "upvotes": 13, "comments_count": 0, "sentiment": "bullish", "engagement_score": 13.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jp4v8n", "title": "Why when I open google maps on the browser it takes me to club pilates? I asked a friend to try and it does the same thing. Weird <PERSON> fools prank?", "content": "", "author": "hawks1312", "created_time": "2025-04-01T19:45:58", "url": "https://reddit.com/r/GoogleMaps/comments/1jp4v8n/why_when_i_open_google_maps_on_the_browser_it/", "upvotes": 17, "comments_count": 20, "sentiment": "neutral", "engagement_score": 57.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}]