[{"platform": "reddit", "post_id": "reddit_1io927p", "title": "Tesla switch", "content": "Anyone can anyone help explain to me what happened today? There was 100s and 1000s of contracts on the call side of the options chain today and even more right at close compared to the past two days when they was all on the put side.  What happened to make this change all of a sudden and how do you know if this a reversal or just a bounce?", "author": "ApplicationLate8154", "created_time": "2025-02-13T02:41:38", "url": "https://reddit.com/r/options/comments/1io927p/tesla_switch/", "upvotes": 4, "comments_count": 18, "sentiment": "neutral", "engagement_score": 40.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1io99bs", "title": "Are EREVs actually a step forward", "content": "This looks like a step back in our electric transition, but could actually be a step forward, especially in the use cases of towing a trailer or in big rigs, like what Edison motors is trying to do. ", "author": "Admirable_North6673", "created_time": "2025-02-13T02:51:59", "url": "https://reddit.com/r/electricvehicles/comments/1io99bs/are_erevs_actually_a_step_forward/", "upvotes": 134, "comments_count": 352, "sentiment": "neutral", "engagement_score": 838.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1io9u3j", "title": "Ever heard of \"allinonemarketing.com\"", "content": "Hello all! \nI'm a business owner with a growing list of customers that we need to be able to reach out to on a regular basis via email. While searching for a reliable cheap email marketing program (hopefully not monthly fee) I received an ad for a website called Allinonemarketing.com. I have done many searches on this company and have found little to no help. \n\nWhen looking at their product it seems like they have most of what we need but it definitely seems to good to be true. But I wanted to know if anyone has used their product and has ifo on what it really is. \n\nWhen looking at reviews I see mostly negative. Actually I see all negative. But we all know that the upset customers are usually the ones that make reviews. The happy customer tend to stay quiet (sadly). \n\nSo I'm curious has anyone else used their services? How is it? \n\nEdit: I didn't do it. I didn't buy it. Too many scammer vibes coming from it. I found one review that said even after you pay full price for it you will wind up spending $400 to get full access to everything. I read the terms and policies for the 7 day refund and basically you will never get the refund. If you use the product at all during the 7 days then you lose eligibility to get the refund. Soo... nah...\n I figured I'm not at a point in my start up to get scammed $400, $200, or even $97. I will stick to what I know works. ", "author": "88savage44", "created_time": "2025-02-13T03:21:55", "url": "https://reddit.com/r/DigitalMarketing/comments/1io9u3j/ever_heard_of_allinonemarketingcom/", "upvotes": 0, "comments_count": 68, "sentiment": "bearish", "engagement_score": 136.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iodb1v", "title": "Google removed all reviews from the past month and disabled reviews on Gulf of Mexico", "content": "Reviewing has been disabled on the Gulf of Mexico, with a 'Details' link pointing to a generic information page. Additionally, it seems that reviews from the past month have been deleted in bulk, the last review being one by one '<PERSON><PERSON>' 1 month ago (cannot look up the specific date, thanks Google).\n\nWhat are reviews even for if they get scraped by Google when they decide they do not like them?\n\n[https://imgur.com/a/qmzVPC1](https://imgur.com/a/qmzVPC1)", "author": "alexionut05", "created_time": "2025-02-13T06:52:18", "url": "https://reddit.com/r/GoogleMaps/comments/1iodb1v/google_removed_all_reviews_from_the_past_month/", "upvotes": 5, "comments_count": 15, "sentiment": "neutral", "engagement_score": 35.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iogtnr", "title": "<PERSON><PERSON> calls for the U.S. government to delete entire agencies: 'Remove the roots of the weed'", "content": "", "author": "Curious_Suchit", "created_time": "2025-02-13T11:15:25", "url": "https://reddit.com/r/elonmusk/comments/1iogtnr/elon_musk_calls_for_the_us_government_to_delete/", "upvotes": 292, "comments_count": 520, "sentiment": "bullish", "engagement_score": 1332.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ioha2m", "title": "My first marketing client as a software dev blood sweat & tears - looking back(11 months ago)", "content": "**TL;DR:**\n\n* I started in software engineering and went into marketing with little experience.\n* Was so hungry for clients I signed up a bunch of idiots until I met one decent client who made me 4.5k in a month.\n* Learned how to sell over the phone.\n* Learned how to do ads (link to them included).\n* Learned how to do database reactivations, nurturing examples included.\n* Made the guy $30k in 30 days and a nice commission out of this.\n* Got an EPIC testimonial.\n* Outsourced the calls & increased the pricing.\n* Client got too cocky and this happened… (read in the end, it's too good of a lesson to put here).\n\nThis is going to be one of the first case studies I had at the time and all the mistakes I can now see looking back at it.\n\nWhen I first started my marketing journey, I had no case studies, nothing to piggyback off results-wise. I came from software engineering.\n\nI had to take any clients I could to make it work, and most of them unfortunately were the legendary \"nightmare clients\" who expected everything for nothing. It’s like this meme:\n\n\"$5000 client - sent you the invoice.\"  \n\"$50 client - So what exactly do I get for this massive investment of mine? Hopefully everything above and beyond, right???\"\n\nBut I finally landed one whose business looked solid, a big studio with fancy cars. First month, I was given not that impressive of a budget, around 1000 pounds.\n\nI was using GoHighLevel to set everything up since I heard from multiple sources that to convert leads from Meta, you need to have a hub.\n\n**First Leads Start Coming In**  \nI, without ANY (I repeat ANY) sales experience and only equipped with basic knowledge of the service, would call those people. Kind of a funny experience imagine an introverted dev cold calling some dudes' leads...\n\n**Phone Call #1**  \n\"Hey mate, you alright?\"  \n\"Who's this? Where you calling from? Are you a scammer?\" *Hangs up*  \nTurns out my little Polish accent did not come off as professional :D\n\n**Phone Call #2**  \n\"Hi Tim, Matt here with {My client's company}. I saw you have a nice Tesla. You wanted the ceramic coating done, right?\"  \n\"Yeah, how much?\"  \n\"600 pounds for basic and...\" *Hangs up on me.*  \nNote to self: Okay, DO NOT give the price over the phone.\n\n**Phone Call #3**  \n\"Hi Theo, it's Matt with {My client's company}, just giving you a quick call about ceramic coating for your Porsche.\"  \n\"Yeah, how much would it cost to do a 3-year coating?\"  \n\"I'm not sure, we haven't seen your car yet and I wouldn't want to overcharge you. When could you pop by the shop and see all the luxury cars we have in here, meet the owner, and see what we are all about?\"  \n(This was a longer call, but this dude did end up booking.)\n\nThere were also a lot of calls where people just wouldn’t trust us. So, I started texting them before the call with our Instagram, which looked absolutely stunning. That helped a lot. Then, I started sending custom messages depending on the car the customer had and THAT worked even better.\n\nThen I built some automations so before they even talked to me on the phone, we would've already known if we:\n\n* Had the right person.\n* They were free for a chat.\n* They saw social proof.\n\nThose three things helped a lot with my silly accent calling strangers.\n\n**Ads & Marketing Strategy**  \nAds seemed to be going pretty good the whole time. NOW, looking back after working with a lot of detailing businesses, I can tell you that advertising is NOT as difficult as it seems, but advertising a bad business or a bad product will make you question yourself over and over.\n\nA lot of customers that I signed because I just needed customers never saw the results that legitimate businesses with big, structured operations did.\n\n**Pay attention to whether the business is worth signing** because I can tell you right now, the amount of stress you will have with those delusional clients is just NOT worth it...\n\n**The Ads**  \nI had a fair idea how it works after watching a good amount of tutorials on eCommerce, which let me tell you was NOT as transferable as I initially thought. Local business marketing has its own strategies, pros, and cons, which I learned later on.\n\n**Here was my link to the ads but the post got auto removed :S**\n\n**Database Reactivation & Nurturing**  \nAt first, I wasn’t doing any nurturing because I was the one calling leads myself. Then one day, I thought why not message all the hundreds of people sitting in our CRM doing nothing?\n\nI created an offer, put those people into an SMS + email + voicemail drop workflow, and pressed the doomsday button.\n\nFirst minute: nothing.  \nSecond minute: nothing.  \nFive minutes later: messages and calls start coming in to the point where we can’t keep up. It felt surreal, like that scene from *Better Call Saul* when Jimmy just aired the ad and people started fully booking with him.\n\nFrom that day on, I wrote down a bunch of text reminders, nurturing sequences, emails, and voicemail drops to keep things consistent.\n\n# My Nurturing Sequence:\n\n**Day 1: Welcome + Personal SMS**\n\n* Confirm inquiry & provide baseline of who you are.\n* 15 mins later, send another message super casual, making sure it looks personal (not automated).\n\n**Day 3: Provide Value**\n\n* Send educational content that you yourself would pay $5 for.\n\n**Day 6: The Process, Behind the Scenes**\n\n* Send a .doc or YT video showing the full process.\n\n**Day 14: Social Proof**\n\n* Testimonial of someone local who used your service.\n\n**Day 21: Be Transparent**\n\n* Show all the work that goes into it & why it’s different from competitors.\n\n**Day 28: Follow-up**\n\n* Ask if they had any luck getting the service done.\n\n**Day 35: Q&A**\n\n* Send common customer questions & follow up after 5 hours.\n\n**Day 42: Offer**\n\n* Reminder of their initial inquiry + a complimentary offer.\n\n**Day 49: Cross-Sell**\n\n* Show other services that complement what they wanted.\n\n**Day 56: Time-Based Offer**\n\n* Message saying there was a reschedule & offer a better price.\n\n**Scaling & The Downfall**  \nWhen I initially signed him up, I was getting 15% commission, so I got £4.5k that month, which was CRAZY to me at the time (avg salary in my country = £1k).\n\nI wanted more clients like him, so I outsourced phone calls. But my commission-based hires were biting into margins hard, so I told the guy we’d increase our fee to £500 flat + 20%.\n\nBAD IDEA.\n\nNext month, results weren’t as great, and my client got cocky. He wanted to cut commissions to 10% and call the leads himself. I stupidly agreed.\n\nLong story short he was calling leads after work instead of instantly, people weren’t picking up, and a week later, he blamed ME for “not getting results.”\n\nLesson learned: **If your client tries to mess with the process, just say NO.**\n\nAlso **get your testimonials ASAP from early clients to avoid this nonsense.**\n\nOverall to people who have no idea where to get started, worry about being in a different industry... just go and do it. I was cold calling left and right, got lucky when someone said yes and the rest is history.\n\nPs. I used some chatgpt to organise it better because I do tend to write in a bit chaotic way but I hope it motivated some of the guys here :D Never give up never what!", "author": "martis941", "created_time": "2025-02-13T11:45:55", "url": "https://reddit.com/r/DigitalMarketing/comments/1ioha2m/my_first_marketing_client_as_a_software_dev_blood/", "upvotes": 25, "comments_count": 11, "sentiment": "neutral", "engagement_score": 47.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ioiqls", "title": "Game Ready Driver 572.42 FAQ/Discussion", "content": "# Game Ready Driver 572.42 has been released.\n\n**Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/avowed-indiana-jones-great-circle-geforce-game-ready-driver/)\n\n**Game Ready Driver Download Link**: [Link Here](https://us.download.nvidia.com/Windows/572.42/572.42-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n**New feature and fixes in driver 572.42:**\n\n**Game Ready** \\- This new Game Ready Driver provides the best gaming experience for the latest new games supporting DLSS 4 technology including Indiana Jones and the Great Circle. Further support for new titles leveraging DLSS technology includes Avowed and Wuthering Waves. In addition, this driver supports the launch of <PERSON>'s Civilization VII.\n\n**Gaming Technology** \\- Adds support for the GeForce RTX 5090 and GeForce RTX 5080 GPUs\n\n**Fixed Gaming Bugs**\n\n* \\[Valorant\\] Game may crash when starting game \\[4951583\\]\n* \\[Final Fantasy XVI\\] PC may freeze when exiting game \\[5083532\\]\n* \\[Delta Force\\] Some PC configurations may experience performance regression when Resizable BAR is enabled \\[5083758\\]\n\n**Fixed General Bugs**\n\n* \\[3DMark DXR Feature Test\\] Unusually low score for Blackwell GPUs \\[5062329\\]\n* Resolved a compatibility issue caused by version mismatches between current and updated dynamic link library files. \\[5081921\\]\n   * **Requires selecting \"Custom (Advanced)\" -> \"Perfor a clean installation\" during the driver installation process** \n*  \n\n**Open Issues**\n\n* Changing state of \"Display GPU Activity Icon in Notification Area\" does not take effect until PC is rebooted \\[4995658\\]\n* \\[VRay 6\\] Unexpected Low Performance on CUDA Vpath Tests for Blackwell GPUs \\[4915763\\]\n\n**Additional Open Issues from** [GeForce Forums](https://www.nvidia.com/en-us/geforce/forums/user/15//557200/geforce-grd-57242-feedback-thread-released-21325/)\n\n* Adobe Substance 3D Painter texture corruption in baking results from GPU raytracing \\[5091781\\] \n* After letting display go to sleep for an extended period, driver may crash when waking up monitor \\[5089560\\] \n* \\[SteamVR\\] Some apps may display stutter on GeForce RTX 50 series \\[5088118\\] \n* \\[Vulkan/DirectX\\] Some apps may display slight image corruption on pixelated 2D patterns \\[5071565\\] \n\n**Driver Downloads and Tools**\n\nDriver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n\nLatest Game Ready Driver: **572.42** WHQL\n\nLatest Studio Driver: **572.16** WHQL\n\nDDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n\nDDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n\n**DDU/WagnardSoft Patreon:** [**Link Here**](https://www.patreon.com/wagnardsoft)\n\nDocumentation: [Game Ready Driver 572.42 Release Notes](https://us.download.nvidia.com/Windows/572.42/572.42-win11-win10-release-notes.pdf) | [Studio Driver 572.16 Release Notes](https://us.download.nvidia.com/Windows/572.16/572.16-win10-win11-nsd-release-notes.pdf)\n\nNVIDIA Driver Forum for Feedback: [Link Here](https://www.nvidia.com/en-us/geforce/forums/user/15//557200/geforce-grd-57242-feedback-thread-released-21325/)\n\n**Submit driver feedback directly to NVIDIA**: [Link Here](https://forms.gle/kJ9Bqcaicvjb82SdA)\n\n[r/NVIDIA](https://new.reddit.com/r/NVIDIA/) Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\nHaving Issues with your driver? Read here!\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue**\n\nThere is only one real way for any of these problems to get solved, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows 10 or 11\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\nIf it still crashes, we have a few other troubleshooting steps but this is fairly involved and you should not do it if you do not feel comfortable. Proceed below at your own risk:\n\n* A lot of driver crashing is caused by Windows TDR issue. There is a huge post on GeForce forum about this [here](https://forums.geforce.com/default/topic/413110/the-nvlddmkm-error-what-is-it-an-fyi-for-those-seeing-this-issue/). This post dated back to 2009 (Thanks Microsoft) and it can affect both Nvidia and AMD cards.\n* Unfortunately this issue can be caused by many different things so it’s difficult to pin down. However, editing the [windows registry](https://www.reddit.com/r/battlefield_4/comments/1xzzn4/tdrdelay_10_fixed_my_crashes_since_last_patch/) might solve the problem.\n* Additionally, there is also a tool made by Wagnard (maker of DDU) that can be used to change this TDR value. [Download here](http://www.wagnardmobile.com/Tdr%20Manipulator/Tdr%20Manipulator%20v1.1.zip). Note that I have not personally tested this tool.\n\nIf you are still having issue at this point, visit [GeForce Forum for support](https://forums.geforce.com/default/board/33/geforce-drivers/) or contact your manufacturer for RMA.\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n\n**Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n* **What does the new Power Management option “Optimal Power” means? How does this differ from Adaptive?** *The new power management mode is related to what was said in the Geforce GTX 1080 keynote video. To further reduce power consumption while the computer is idle and nothing is changing on the screen, the driver will not make the GPU render a new frame; the driver will get the one (already rendered) frame from the framebuffer and output directly to monitor.*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations. The software will not be perfect and there will be issues for some people. For a more comprehensive list of open issues, please take a look at the Release Notes. Again, I encourage folks who installed the driver to post their experience here... good or bad.\n\n*Did you know NVIDIA has a Developer Program with 150+ free SDKs, state-of-the-art Deep Learning courses, certification, and access to expert help. Sound interesting?* [Learn more here](https://nvda.ws/3wCfH6X)*.*", "author": "Nestledrink", "created_time": "2025-02-13T13:12:46", "url": "https://reddit.com/r/nvidia/comments/1ioiqls/game_ready_driver_57242_faqdiscussion/", "upvotes": 219, "comments_count": 967, "sentiment": "bearish", "engagement_score": 2153.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iokolw", "title": "Google Maps blocks Gulf of America reviews after rename criticism", "content": "", "author": "Tracker-man", "created_time": "2025-02-13T14:49:07", "url": "https://reddit.com/r/news/comments/1iokolw/google_maps_blocks_gulf_of_america_reviews_after/", "upvotes": 31498, "comments_count": 1653, "sentiment": "neutral", "engagement_score": 34804.0, "source_subreddit": "news", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ioksnw", "title": "AI could be used for a 'bad biological attack from some evil person,' ex-Google CEO <PERSON> warns", "content": "", "author": "MetaKnowing", "created_time": "2025-02-13T14:54:19", "url": "https://reddit.com/r/artificial/comments/1ioksnw/ai_could_be_used_for_a_bad_biological_attack_from/", "upvotes": 59, "comments_count": 31, "sentiment": "neutral", "engagement_score": 121.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ionq6b", "title": "Nancy Pelosi NEW $GOOGL Position 🗞️", "content": "\n<PERSON>’s financial disclosure report shows she purchased 50 call options on Alphabet Inc. (GOOGL) with a $150 strike price, expiring in January 2026. The transaction, valued between $250,001 and $500,000, suggests a bullish stance on Google’s stock price.\n\nPossible Short-Term Impact on GOOGL:\n\t1.\tIncreased Market Attention: Large trades by high-profile figures, especially politicians, often attract retail and institutional investors. This could lead to short-term buying pressure on GOOGL.\n\t2.\tSpeculation on Future Catalysts: <PERSON><PERSON><PERSON>’s stock trades have historically sparked speculation about potential government policies or insider knowledge. Investors might anticipate upcoming favorable developments for Google (e.g., regulatory decisions, AI advancements).\n\t3.\tMomentum Effect: If traders interpret this move as a signal of confidence in Google’s future performance, the stock price could experience short-term upward momentum.", "author": "nerdy-nate", "created_time": "2025-02-13T16:59:58", "url": "https://reddit.com/r/wallstreetbets/comments/1ionq6b/nancy_pelosi_new_googl_position/", "upvotes": 4374, "comments_count": 484, "sentiment": "neutral", "engagement_score": 5342.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ioq76t", "title": "Subreddit Update", "content": "Hi all! I'm u/Abrownn, this sub's mod, and I have three minor announcements.\n\n---\n\nFirst is Link Flair! A user kindly reached out to inquire about link flair and the possibility of filters for flair. There is no native \"exclude\" flair filter, however I have added a hacky workaround for the most requested filter that uses the site's native \"include\" function: The [\"No AI Filter\"](https://www.reddit.com/r/technews/search?q=-flair%3A%22AI%2FML%22&restrict_sr=on&sort=new&t=all). You can also find it at the bottom of the sidebar from now on.\n\n---\n\nSecond is a reminder of the sub's focus: Tech News. A good heuristic (although a tad reductive) for what's appropriate here is \"If it explicitly goes 'beep-boop', then it's likely a good fit\". This is a HARD tech subreddit. No social media, no politics, no lawsuits, no layoffs, no business news**, no legal news, no crypto stuff. If you aren't sure if a post is a good fit then [please send me a modmail](https://www.reddit.com/message/compose/?to=/r/technews) (NOT a DM) - I don't bite and I usually respond pretty quick.\n\n(Asterisks: \"Investing money in a new semicon fab\" is fine, a company \"being fined for FTC violations\" is not)\n\n---\n\nThird, \"[Redditquette](https://support.reddithelp.com/hc/en-us/articles/*********-Reddiquette)\". Tldr, don't be a dick. \n\n99% of the bans here are for spam and I'm happy to provide a screenshot of the ban log for transparency/proof. I don't ban people for being plain dumb or ignorant, but I do ban people for blatant trolling or disregard of reality (which seems to be getting rapidly worse these days). An engineer said this to musk recently and I think it's a pretty fair take on how I evaluate reported comments:\n\n> \"It’s only really like the tenth percentile of the adult population who’d be gullible enough to fall for this,\" the data scientist told Musk during a face-to-face meeting.\n\nIf you're *maliciously stupid*, then you'll probably catch a ban. Go back to Twitter and do that shit, don't waste everyone else's time here. I need all of your help to police content in the sub, so please do make use of the report feature but do not abuse it because I do report abusive reports to the admins and *they will respond accordingly.*\n\n---\n\nQuestions? Comments? Concerns?", "author": "abrownn", "created_time": "2025-02-13T18:43:28", "url": "https://reddit.com/r/technews/comments/1ioq76t/subreddit_update/", "upvotes": 45, "comments_count": 9, "sentiment": "bearish", "engagement_score": 63.0, "source_subreddit": "technews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ioqb79", "title": "Tesla Takeover: protests planned at Tesla stores globally this weekend", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-13T18:48:10", "url": "https://reddit.com/r/electricvehicles/comments/1ioqb79/tesla_takeover_protests_planned_at_tesla_stores/", "upvotes": 2030, "comments_count": 617, "sentiment": "neutral", "engagement_score": 3264.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ior0do", "title": "Google Maps Won’t Let You Leave Negative Reviews on the Gulf of America", "content": "[https://gizmodo.com/google-maps-wont-let-you-leave-negative-reviews-on-the-gulf-of-america-2000563649](https://gizmodo.com/google-maps-wont-let-you-leave-negative-reviews-on-the-gulf-of-america-2000563649)", "author": "esporx", "created_time": "2025-02-13T19:17:15", "url": "https://reddit.com/r/GoogleMaps/comments/1ior0do/google_maps_wont_let_you_leave_negative_reviews/", "upvotes": 0, "comments_count": 34, "sentiment": "bearish", "engagement_score": 68.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iornbw", "title": "State Dept. Suspends Plan to Buy Armored Teslas", "content": "", "author": "Lantis28", "created_time": "2025-02-13T19:43:52", "url": "https://reddit.com/r/politics/comments/1iornbw/state_dept_suspends_plan_to_buy_armored_teslas/", "upvotes": 1573, "comments_count": 174, "sentiment": "bullish", "engagement_score": 1921.0, "source_subreddit": "politics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ioubvb", "title": "GameStop Considering Investing in Crypto", "content": "CNBC is reporting GameStop is considering investing in Bitcoin and other crypto assets.\n\nApparently when your company has become irrelevant and you no longer have any other ideas to grow your business, the move is to start investing investor assets into vaporware?\n\nWhat a joke.  This is why I can’t buy into this market right now.\n\nAnd the stock is up 15% after hours on the news…", "author": "Coronator", "created_time": "2025-02-13T21:39:04", "url": "https://reddit.com/r/investing/comments/1ioubvb/gamestop_considering_investing_in_crypto/", "upvotes": 0, "comments_count": 33, "sentiment": "bullish", "engagement_score": 66.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1iovsmv", "title": "Google Maps has turned off the ability to review the Gulf of “America”", "content": "", "author": "homemade-fruit-salad", "created_time": "2025-02-13T22:43:16", "url": "https://reddit.com/r/google/comments/1iovsmv/google_maps_has_turned_off_the_ability_to_review/", "upvotes": 1049, "comments_count": 280, "sentiment": "neutral", "engagement_score": 1609.0, "source_subreddit": "google", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ioxatq", "title": "[D] We built GenAI at Google and Apple, then left to build an open source AI lab, to enable the open community to collaborate and build the next DeepSeek. Ask us anything on Friday, Feb 14 from 9am-12pm PT!", "content": "Proof: [https://imgur.com/a/kxiTTXP](https://imgur.com/a/kxiTTXP)\n\nTL;DR: Hi 👋 we’re Oumi, an AI lab that believes in an unconditionally open source approach–code, weights, training data, infrastructure, and collaboration—so the entire community can collectively push AI forward. We built a platform for anyone to contribute research in AI. Ask us anything about open source, scaling large models, DeepSeek, and what it takes to build frontier models, both inside and outside of big tech companies. Tell us what is working well in open source AI or what challenges you are facing. What should we work on together to improve AI in the open?\n\n\\-------------\n\nFor years, we worked at big tech (Google, Apple, Microsoft) leading efforts on GenAI models like Google Cloud PaLM, Gemini, and Apple’s health foundation models. We were working in silos and knew there had to be a better way to develop these models openly and collaboratively. So, we built a truly open source AI platform that makes it possible for tens of thousands of AI researchers, scientists, and developers around the world to collaborate, working together to advance frontier AI in a collective way that leads to more efficient, transparent and responsible development. The Oumi platform (fully open-source, Apache 2.0 license) supports pre-training, tuning, data curation/synthesis, evaluation, and any other common utility, in a fully recordable and reproducible fashion, while being easily customizable to support novel approaches.\n\nDeepSeek showed us what open source can achieve by leveraging open-weight models like LLaMA. But we believe AI should be even more open: not just the weights, but also the training data, and the code–make it ALL open. Then go even further: make it easy for anyone to access and experiment, make it easy for the community to work together and collaborate. \n\nSome resources about Oumi if you’re interested:\n\nOur GitHub repo: [https://github.com/oumi-ai/oumi](https://github.com/oumi-ai/oumi)\n\nOur launch story: [https://venturebeat.com/ai/ex-google-apple-engineers-launch-unconditionally-open-source-oumi-ai-platform-that-could-help-to-build-the-next-deepseek/](https://venturebeat.com/ai/ex-google-apple-engineers-launch-unconditionally-open-source-oumi-ai-platform-that-could-help-to-build-the-next-deepseek/)\n\nOur site: [https://oumi.ai/](https://oumi.ai/) \n\nIf you want to collaborate and contribute to community research projects, regardless of where you get your compute, you can sign up at: [https://oumi.ai/community](https://oumi.ai/community). We will be starting with the post-training of existing open models, next, we will be collaboratively pursuing improvements to pre-training. We intend to publish the research with all contributors included as authors.\n\nWe’re here to answer questions about our open source approach, scaling large models, DeepSeek, what it takes to build frontier models both inside and outside of big tech companies, and anything else you all want to discuss.\n\nWe’ll be here Friday, February 14 from 9am-12pm PT / 12pm-3pm ET. Ask us anything.\n\n**Joining us in the AMA:**\n\n* (u/koukoumidis) **Manos Koukoumidis** \\- CEO and Co-founder, ex-Google (Cloud GenAI Lead)\n* (u/oelachqar) **Oussama Elachqar** \\- Co-founder, Engineering, ex-Apple (Health foundation models)\n* (u/MatthewPersons) **Matthew Persons** \\- Co-founder, Engineering, ex-Google (Cloud PaLM & NL Lead)\n* (u/jeremy\\_oumi) **Jeremy Greer** \\- Co-founder, Research, ex-Google (Gemini Alignment)", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-13T23:53:27", "url": "https://reddit.com/r/MachineLearning/comments/1ioxatq/d_we_built_genai_at_google_and_apple_then_left_to/", "upvotes": 162, "comments_count": 69, "sentiment": "neutral", "engagement_score": 300.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}]