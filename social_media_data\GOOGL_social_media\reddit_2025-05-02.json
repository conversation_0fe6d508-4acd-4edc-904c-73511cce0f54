[{"platform": "reddit", "post_id": "reddit_1kcow8t", "title": "Google Ads Help", "content": "Hey, marketing for a PI law firm. Google ads account campaign is only serving for competitor searches. The keyword that is being triggered is “personal injury lawyer”.\n\nI tried to add tons of negative keywords for all of the other law firms but that is impossible to get all of them. \n\nNow I am only running a ton of exact match keywords and getting almost no impressions. \n\nAny ideas would be welcomed!", "author": "filleniummalcon90", "created_time": "2025-05-02T01:12:38", "url": "https://reddit.com/r/marketing/comments/1kcow8t/google_ads_help/", "upvotes": 2, "comments_count": 3, "sentiment": "bearish", "engagement_score": 8.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kcvtak", "title": "Does running ads on Amazon and Google at the same time really help?", "content": "Hi Folks! \n\nI runa bootstapped business where we craft premium yet guilt free iced and hot chocolate mixes. Currently we are selling on Amazon and our own Website. So this time I have designed an ad campaign for google and amazon both at the same time and was wondering if it really makes any difference. \n\n  \nWhat do you think? Is it better to advertise everywhere at once or is this not something you would suggest? I am eager to hear from you guys.\n\n  \nr/marketing r/advertising r/AmazonSeller r/amazonindia r/google r/GoogleAdsDiscussion r/googleads r/delhi r/india r/startups ", "author": "DesignSignificant900", "created_time": "2025-05-02T08:19:49", "url": "https://reddit.com/r/business/comments/1kcvtak/does_running_ads_on_amazon_and_google_at_the_same/", "upvotes": 1, "comments_count": 9, "sentiment": "bearish", "engagement_score": 19.0, "source_subreddit": "business", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kd1ur1", "title": "You need yt premium to download now???", "content": "I went into youtube cz I was looking  for something funny, then when I finally found one, clicked then it said something like premium o just clicked no thanks, after a while I checked if it was finished. What is this", "author": "Core_x3", "created_time": "2025-05-02T14:07:58", "url": "https://reddit.com/r/youtube/comments/1kd1ur1/you_need_yt_premium_to_download_now/", "upvotes": 0, "comments_count": 38, "sentiment": "neutral", "engagement_score": 76.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kd3a7b", "title": "I’m really sorry for this question but I’m an overwhelmed old man that wants a basic website but I feel I can’t trust any info on google", "content": "Wow! Thank you all sooooo much!!! I love it when reddit comes through sans outlandish ego and sincerely appreciate all the legit and pertinent tips and offers I've received. I hope everyone has a great weekend!\n\nEvery time I search I get 3 year old posts about netlify but I don't even know where to begin on that site, I don't see a \"dumbass\" section lol. I know nothing about coding etc, I just need a few pictures and a paragraph describing my small business that will rarely be visited. The website address I'd like is available but I don't know how I could get it, afforably. I guess that's how people confirm if its a legit business now a-days so I feel like I'm missing out on some business. I made the mistake of godady a few years ago so I am just totally at a loss of what's a scam of $5 now but turns to $5000 later. Thanks for any advice you have, I may be in a pipe dream here. ", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-02T15:07:16", "url": "https://reddit.com/r/webdev/comments/1kd3a7b/im_really_sorry_for_this_question_but_im_an/", "upvotes": 118, "comments_count": 142, "sentiment": "bearish", "engagement_score": 402.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kd5i47", "title": "10 startup lessons I’d tattoo on every founder’s arm (in comic sans) - i will not promote", "content": "10 startup lessons I’d tattoo on every founder’s arm (in comic sans)\n\n1.  no one cares about your idea. not even your mom. show traction.\n\n1. 2. build fast. talk to users faster. and by “talk,” I mean listen instead of pitching your 7 layer roadmap.\n2. 3. fundraising is just sales in patagonia vests. channel your inner wolf of zoom Street.\n3. 4. co-founder > idea. if your cofounder makes you want to throw a stapler, rethink everything.\n4. 5. distribution eats product for breakfast. and probably your runway too.downloads are cute. retention pays rent.\n5. 6. talk to customers weekly. yes, actual humans. Not just google analytics.\n6. 7. don’t scale like you’re Elon unless your bank balance also says “SpaceX.”\n7. 8. going viral is great until you realize no one stuck around.\n8. 9. pivoting is fine. but if you’ve pivoted 5 times this month, maybe you’re just spinning.\n9. 10. startups are hard.but if you’re laughing, crying and googling “what is product-market fit” at 2am… you’re doing it right.", "author": "v<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-02T16:38:22", "url": "https://reddit.com/r/startups/comments/1kd5i47/10_startup_lessons_id_tattoo_on_every_founders/", "upvotes": 547, "comments_count": 143, "sentiment": "neutral", "engagement_score": 833.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kd5p7x", "title": "Google is quietly testing ads in AI chatbots", "content": "“Google I/O later this month will probably help clarify how Google plans to monetize Gemini, but the company appears to be getting all the pieces in place. Before long, free chatbots could have interstitial AdSense ads unless you pay for premium access, and Google could be upselling us on a more expensive version of Gemini services. The free ride may be coming to an end.”", "author": "thatguyisme87", "created_time": "2025-05-02T16:46:45", "url": "https://reddit.com/r/singularity/comments/1kd5p7x/google_is_quietly_testing_ads_in_ai_chatbots/", "upvotes": 416, "comments_count": 141, "sentiment": "neutral", "engagement_score": 698.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kd7bgz", "title": "I disabled Google Lens in Chrome and reloaded google, but Google Lens is still active", "content": "I absolutely hate google lens, it's literally worthless. Is there any way to fix this?", "author": "Deep_Ad_6433", "created_time": "2025-05-02T17:53:33", "url": "https://reddit.com/r/chrome/comments/1kd7bgz/i_disabled_google_lens_in_chrome_and_reloaded/", "upvotes": 23, "comments_count": 36, "sentiment": "bullish", "engagement_score": 95.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kdc3iq", "title": "Firefox could be doomed without Google search deal, says executive", "content": "", "author": "Nehemoth", "created_time": "2025-05-02T21:16:15", "url": "https://reddit.com/r/technology/comments/1kdc3iq/firefox_could_be_doomed_without_google_search/", "upvotes": 3262, "comments_count": 375, "sentiment": "neutral", "engagement_score": 4012.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kdetk9", "title": "[D] Papers/ tips for creating an activation-atlas like this google/open-ai one?", "content": "I want to create an activation atlas like the one made by Google and OpenAI in 2019 (https://distill.pub/2019/activation-atlas/ ). However the \"lucid\" package they used is not up-to-date.\n\nI've found some more recent feature vis packages like [https://arxiv.org/abs/2503.22399](https://arxiv.org/abs/2503.22399)  [https://adagorgun.github.io/VITAL-Project/](https://adagorgun.github.io/VITAL-Project/) but I have not found anything that could create an \"atlas\" of many classes.\n\nAnyone have any packages/ tips for creating a activation atlas? I could use an older version of tensorflow to use lucid, but I was wondering if there were any other up-to-date alternatives. Any help would be appreciated!\n\n", "author": "AGenocidalPacifist", "created_time": "2025-05-02T23:19:05", "url": "https://reddit.com/r/MachineLearning/comments/1kdetk9/d_papers_tips_for_creating_an_activationatlas/", "upvotes": 7, "comments_count": 3, "sentiment": "neutral", "engagement_score": 13.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}]