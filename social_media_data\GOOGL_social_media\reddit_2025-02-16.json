[{"platform": "reddit", "post_id": "reddit_1iqinm4", "title": "Draining the swamp - Washington DC Google Trends", "content": "#shady", "author": "Away-Thanks4374", "created_time": "2025-02-16T02:59:02", "url": "https://reddit.com/r/elonmusk/comments/1iqinm4/draining_the_swamp_washington_dc_google_trends/", "upvotes": 0, "comments_count": 10, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iqjmro", "title": "🚨 Google deleted years of Location History without warning – let’s take action! 🚨", "content": "I’m organizing a **legal action** to demand that Google **restore deleted Timeline data** for those affected. If this happened to you too, **read below**—I’ll explain what happened to me.\n\nI’ve used Google Maps Timeline for **over 10 years**, regularly checking it to revisit old trips and important moments. A few months ago, I reactivated it without realizing that **Google had silently turned off my Timeline and, upon reactivation, automatically set auto-delete to 3 months—almost imperceptibly.**\n\n**No warning, no confirmation that it would erase more than 10 years of memories—just gone.**\n\nI lost **memories I can’t replace**—the day I met my wife, places I visited in my youth, my travels. After searching, I found **hundreds of people** reporting the same issue, yet **Google has ignored everyone**. There’s no direct way to contact Google Maps support, just an unanswered forum.\n\nI’m **not a lawyer**, but I’m already **talking to law firms** willing to take this case. If you were affected, let’s take action together.\n\n📌 **Join here**: [https://forms.gle/gAtbh4Hy2ci18USq8](https://forms.gle/gAtbh4Hy2ci18USq8)\n\nThe more of us, the stronger our case.", "author": "da<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-16T03:55:26", "url": "https://reddit.com/r/GoogleMaps/comments/1iqjmro/google_deleted_years_of_location_history_without/", "upvotes": 0, "comments_count": 19, "sentiment": "bullish", "engagement_score": 38.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iqt97z", "title": "In layman's terms, what do data analysts really do on a day to day basis.", "content": "I'm considering data analysis as a career, largely because a) I'm pretty good with spreadsheets. b) I hear it pays well. c) I hear the job market is pretty good.\n\nThat said, I know nothing about SQL, Python (or any other programming language). I'm considering going back to school for this. I have a Bachelor's in Operations Management, which has some, but not many, parallel skills. My Bachelor's is also 15 years old and I don't honestly remember a ton of the information.\n\nI'd like to know more about what data analysts actually do, without all the industry jargon. Any insight would be much appreciated.", "author": "Livid-Passion9672", "created_time": "2025-02-16T14:24:39", "url": "https://reddit.com/r/analytics/comments/1iqt97z/in_laymans_terms_what_do_data_analysts_really_do/", "upvotes": 181, "comments_count": 83, "sentiment": "neutral", "engagement_score": 347.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iqzios", "title": "Google doesn’t use the shortest walking route if 50 feet of a road doesn’t have sidewalk.", "content": "They really expect you to walk an extra 15 minutes or so just because the most convenient route is missing sidewalk for a short distance. I guess it’s a legal thing. A little inconvenient though.", "author": "Mission_Grapefruit92", "created_time": "2025-02-16T18:57:24", "url": "https://reddit.com/r/GoogleMaps/comments/1iqzios/google_doesnt_use_the_shortest_walking_route_if/", "upvotes": 11, "comments_count": 45, "sentiment": "bearish", "engagement_score": 101.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ir2pjo", "title": "Lost 40k in 2 Months – Looking for Guidance to Rebuild", "content": "\nI started trading with $80k and lost $40k in just two months. Here’s how it went down:\n\t•\tIn December, I got caught up in the Tesla bull run hype and assumed January would take it to the moon. Ended up losing almost $25k on Tesla options.\n\t•\tAfter that, I tried to be more cautious, but still had some small miscellaneous losses.\n\t•\tThen I discovered SPY 0DTE options and made the dumb assumption that if I buy 100 contracts, a $0.10 increase in premium would net me $1k. Sounded simple, right?\n\t•\tEvery time I entered a trade, it felt like the market instantly moved against me. I’d put in $10k to try and make $1k, only to lose $5k-$6k instead. Clearly, my risk management was terrible.\n\nNow, I’m down $40k, which was about 75% of my savings over the past three years. I’m devastated, but not completely broke. I transferred $10k back to my checking account to remove the temptation of revenge trading and left myself with $3k in Robinhood, hoping to build it up slowly and properly this time.\n\nThe truth is, I never had a real strategy—just got influenced by tons of YouTube videos and jumped in blindly. I was basically gambling.\n\nI want to change that. I want to develop the right mindset, risk management, and a solid trading strategy. If anyone here has gone through something similar and turned things around, I’d really appreciate your insights, tips, or even just some motivation.\n\nThanks in advance!", "author": "Friendly_Walrus3526", "created_time": "2025-02-16T21:11:06", "url": "https://reddit.com/r/options/comments/1ir2pjo/lost_40k_in_2_months_looking_for_guidance_to/", "upvotes": 351, "comments_count": 403, "sentiment": "bullish", "engagement_score": 1157.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}]