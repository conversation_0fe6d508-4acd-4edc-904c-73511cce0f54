[{"platform": "reddit", "post_id": "reddit_1joih4a", "title": "<PERSON><PERSON> says his DOGE role is hurting Tesla's stock price, calling it \"a very expensive job\"", "content": "", "author": "ItzWarty", "created_time": "2025-04-01T00:17:12", "url": "https://reddit.com/r/teslainvestorsclub/comments/1joih4a/elon_musk_says_his_doge_role_is_hurting_teslas/", "upvotes": 172, "comments_count": 190, "sentiment": "neutral", "engagement_score": 552.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1joihb0", "title": "Teslas are now Proud Boy cars", "content": "https://www.wired.com/story/proud-boys-and-militias-come-to-teslas-defense/", "author": "maxplanar", "created_time": "2025-04-01T00:17:29", "url": "https://reddit.com/r/electriccars/comments/1joihb0/teslas_are_now_proud_boy_cars/", "upvotes": 1233, "comments_count": 480, "sentiment": "neutral", "engagement_score": 2193.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jon0e8", "title": "What do Tier1 companies do if Google suspends them?", "content": "We recently got the obscure misrepresentation suspension for our brand which is quite mature (several years old). We run across multiple regions and have separate sub-accounts per region. This was deliberate to minimise risk per account, and give better control of product-set/pricing/currency per region. It just felt cleaner.\n\nWe had 3 subaccounts suspended a few days ago. I've experienced this before, paid a third party for an audit, essentially tried everything they suggested (including improving a lot of content etc). Didn't work. Ended up deleting the sub-account, recreating and didn't have a problem at all. Very inconsistent.\n\nWe've just migrated one of these subaccounts to a new domain (better suited) and have recreated successfully. It meant we had to drop our historical PMax campaign data for this region, but didn't see another option.\n\nI'm yet to request review on the other two just yet as my experience gives me the impression it'll just automatically get rejected again.\n\nI did notice something very strange in our account details though (seems like a Google bug). For context, we started in Australia (we are an Australian based company). The latest sub-account we created was for Canada - and this was about 3 months ago. When I go back to the Australian sub-account (and the parent account), both display \"Canada\" as the country in the \"Business Info\" section. Note, this is not editable. There's no way we originally set it up with \"Canada\" selected - it seems like somehow when creating the sub-account it randomly reset the parent company. With all the UI bugs I've seen recently (and the lack of support) it really wouldn't surprise me that their system could break like this.\n\nAnyhow, I have no idea if that could be a trigger for the misrepresentation - but I just don't see a fix for it. I submitted a couple of tickets with varying information and they all come back with the annoying/useless responses:\n\n>I understand your concern regarding the suspension of your Google Merchant Center Account and I appreciate that you want to fix this issue on a priority basis. I apologize for the inconvenience caused. \n\n>I do understand your concern related to the country name, please be informed that the country name in the account cannot be changed after creating the account. And we may not be able to pinpoint if this could be the possible reason for the suspension of your Merchant Center account.\n\n>Upon checking your Merchant Center account, I found that it has been suspended due to [Misrepresentation](https://support.google.com/merchants/answer/6150127?hl=en&sjid=1238185107008396898-AP). \n\n>This policy suspension (Shopping ads) / limited visibility (Free Listings) means that we have reviewed your Google Merchant Center account and concluded that it does not comply with our Shopping Ads and/or Free Listing policies, and we have therefore disapproved your Google Merchant Center account. \n\n>Google doesn't want users to feel misled by the content promoted in Shopping ads and free listings, and that means being upfront, honest, and providing shoppers with the information that they need to make informed decisions. \n\n>You can resolve this issue by: \n\n>Review your account and online store\n\n>Ensure you meet our Shopping Ads and/or Free Listing policies\n\n>Provide additional information to verify your business\n\n>We recommend you complete the following if prompted to do so, for example:\n\n>For non-EU merchants: complete identity verification if the option is available before requesting a re-review. \n\nI understand the support channel sucks. I also understand there's no way to contact someone higher up.\n\nBut my question is, what do large tier1 entities do if Google take them for ransom? They just take the huge hit to business and tell shareholders the bad news?\n\nI know I'm not the first person to request assistance on this policy, but man this is utterly frustrating. I really don't like companies like 'GetStubGroup' who charge $3500 and then offer only a partial refund if unable to get the approval - doesn't exactly smell of confidence in their expensive work!\n\nAnyone got any other insight/info?", "author": "Floorman1", "created_time": "2025-04-01T04:14:56", "url": "https://reddit.com/r/adwords/comments/1jon0e8/what_do_tier1_companies_do_if_google_suspends_them/", "upvotes": 0, "comments_count": 5, "sentiment": "bearish", "engagement_score": 10.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1joo998", "title": "Alright, gotta ask: anyone else sick of building dashboards no one looks at?", "content": "So, my buddy and I are analytics + ML engineers from FAANG, and we keep seeing the same problem over and over.\n\nAnalytics teams are always understaffed, slammed with requests, and grinding out dashboards that business folks barely use. Meanwhile, stakeholders wanna do their own exploring but don’t wanna get their hands dirty. They just wanna ask questions and get answers. Simple, right?\n\nHere’s the kicker: **Our Data Science team is cranking out TWO new dashboards a day** (we’re talking big, fancy dashboards), and they get like **five views a month on average.** It’s insane. All that effort, basically flushed.\n\nHere’s the loop:\n\n* Business folks: *“Can’t we just ask a question and get the answer already?”*\n* Data teams: *“Sure, here’s your 27th dashboard this month. Enjoy.”*\n* Reality: They don’t. They forget about it, and the cycle starts again.\n\nNow we’re thinking... what if you could literally just **talk to your data?** Like, no setup, no building out new dashboards every five seconds. Just asking questions and getting answers, fast.\n\n**I’m curious, though:**\n\n1. Are you running into this same nightmare of building dashboards that nobody uses?\n2. Would something that just lets people chat with their data actually be useful? Or is it just another shiny object?\n3. If you’ve tried anything like this, what totally sucked about it? (We tried Looker Conversational Analytics early preview, and evaluated ThoughtSpot - kinda blah)\n4. What would make something like this genuinely valuable for you?\n5. Also… what’s the dumbest dashboard request you’ve built that ended up getting zero views? 😂\n\nI’ve got a feeling we’re not alone here. Would love to hear your takes. We’re just spitballing ideas here, so be brutally honest. Appreciate you!", "author": "PeachWithBenefits", "created_time": "2025-04-01T05:34:06", "url": "https://reddit.com/r/analytics/comments/1joo998/alright_gotta_ask_anyone_else_sick_of_building/", "upvotes": 278, "comments_count": 102, "sentiment": "bullish", "engagement_score": 482.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1<PERSON><PERSON><PERSON>", "title": "Are you ready to block your entire website from Google?", "content": "I think you know the answer. No.\n\nGoogle released AI overviews for more countries and search queries with the March Core update.\n\nMore and more popular \"white-hat\" SEO experts change their minds. They don't believe Google speakers and guides anymore. They see Google working hard to eat their traffic.\n\nMany discussions around that, but they have led to almost nothing:\n\n1/ Yes, we've already realized that the world of zero-click marketing is inevitable.   \n2/ Yes, we've already realized that there is no sense in creating content for most banal information search terms.\n\nBut what if:\n\n1/ Google goes even further and starts showing AI overviews for more niches and searches?   \n2/ Google starts creating even more of its sites and online stores?\n\nSite owners cannot resist this, because game theory will not allow it.\n\nTo force Google to change, a huge share of content creators must close their content for scanning and indexing by Google.\n\nBut as soon as some part of the creators block websites from Google, the creators who do NOT do it will greatly benefit from it.\n\nSo, it will never happen on the level we need to change Google's behaviour.\n\nThat's why I don't see a mechanism to make Google pay for the content we create.\n\nDo you see?", "author": "<PERSON><PERSON>", "created_time": "2025-04-01T08:09:25", "url": "https://reddit.com/r/SEO/comments/1joqeep/are_you_ready_to_block_your_entire_website_from/", "upvotes": 75, "comments_count": 68, "sentiment": "neutral", "engagement_score": 211.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1joricd", "title": "Switzerland: Tesla Q1 -65%", "content": "https://www.srf.ch/news/wirtschaft/einbruch-an-der-boerse-tesla-verkaeufe-sinken-ungebremst-beeinflusst-durch-social-media\n\nSwitzerland was always been a strong Tesla market. For those of you who speak german and/or can click an online translation function the above article is helpful. For the others: Sales in Q1/25 are down 65% compared to Q1/24. That's it, that's the message. \n\n(I drove a Model 3 from 2019 to february 2025)", "author": "tbol<PERSON>_swiss", "created_time": "2025-04-01T09:32:40", "url": "https://reddit.com/r/electriccars/comments/1joricd/switzerland_tesla_q1_65/", "upvotes": 982, "comments_count": 97, "sentiment": "bullish", "engagement_score": 1176.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jou505", "title": "Wells Fargo Names Tesla New  ‘Tactical Short Idea,’ Expects Stock to Sink 50%", "content": "", "author": "afonso_investor", "created_time": "2025-04-01T12:17:14", "url": "https://reddit.com/r/electriccars/comments/1jou505/wells_fargo_names_tesla_new_tactical_short_idea/", "upvotes": 1707, "comments_count": 70, "sentiment": "bearish", "engagement_score": 1847.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jowoic", "title": "The world in the 1800s: \"cameras have been developed? They create images of real life instead of someone having to draw it? That's so lazy!\"", "content": "The world in the early 20th century: \"drawings can now be turned into moving pictures with cameras instead of letting people imagine them moving? That's ruining storytelling!\"\n\nThe world in the late 20th century: \"computers can now make animation and movie effects? That's so lazy!\"\n\nThe world in the 21st century: \"snapchat filters, photoshop and other technology can alter images dramatically? That's so lazy!\"\n\nThe world now: \"Ai can make images? That's so lazy!\"", "author": "Atalkingpizzabox", "created_time": "2025-04-01T14:16:58", "url": "https://reddit.com/r/artificial/comments/1jowoic/the_world_in_the_1800s_cameras_have_been/", "upvotes": 3, "comments_count": 20, "sentiment": "neutral", "engagement_score": 43.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jp1tfr", "title": "Tesla is sitting on $200 million worth of Cybertruck inventory", "content": "", "author": "SpriteZeroY2k", "created_time": "2025-04-01T17:44:59", "url": "https://reddit.com/r/electricvehicles/comments/1jp1tfr/tesla_is_sitting_on_200_million_worth_of/", "upvotes": 2578, "comments_count": 541, "sentiment": "neutral", "engagement_score": 3660.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jp2bpf", "title": "I'm really tired of people trying to sell me ChatGPT wrappers.", "content": "I run a small law practice. The amount of marketing by charlatans trying to convince me to incorporate their shitty LLM program into my business is nauseating. The courts have been very consistent about sanctioning attorneys who file LLM-written briefs that hallucinate case citations. I will never use an LLM in my business. Period.\n\nI know this must apply to other industries. What's the most ridiculous business case you've been pitched by the AI-scammers?", "author": "NotThePopeProbably", "created_time": "2025-04-01T18:04:47", "url": "https://reddit.com/r/smallbusiness/comments/1jp2bpf/im_really_tired_of_people_trying_to_sell_me/", "upvotes": 732, "comments_count": 151, "sentiment": "bearish", "engagement_score": 1034.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jp2j5i", "title": "Should I change my SCHD allocation to 0?", "content": "In my early 20’s. I heard it’s better to focus on market gains as opposed reinvesting dividends, is that generally true? Plus something about a taxable event.\n\nRoth IRA\nFXIAX : 40%\nQQQM : 45%\nSCHD : 15%\n\nBasic account \nTesla: (20%)\nApple: (10%)\nGoogle: (15%)\nNvidia: (20%)\nMicrosoft: 20%)\nAmazon: (15%)", "author": "Popster962", "created_time": "2025-04-01T18:12:51", "url": "https://reddit.com/r/dividends/comments/1jp2j5i/should_i_change_my_schd_allocation_to_0/", "upvotes": 3, "comments_count": 51, "sentiment": "bullish", "engagement_score": 105.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jp40py", "title": "How Does My Desktop PC Know My Exact Location Without GPS?", "content": "I recently noticed something strange and a bit concerning.\n\nI have a **custom-built desktop PC** with **no GPS, no mobile data, and no built-in location services**. My phone's **WiFi and location were turned off**, yet when I opened Google Maps (or any other mapping service) on my **PC**, it somehow **knew my exact location**—down to my street.\n\nBut when I **turned off WiFi on my PC**, suddenly, it couldn’t pinpoint my location anymore. It could only estimate based on my IP, which was much less accurate.\n\nAfter some research, I found that this happens because of **WiFi Positioning System (WPS)**. Even if you’re not connected to a WiFi network, your device can still **scan for nearby networks**, and companies like Google, Apple, and Microsoft have massive databases of WiFi locations. Your PC just sends the list of detected networks to their servers, and they use that data to determine your position.\n\nHas anyone else noticed this?  \nWhat do you think about this from a privacy perspective?  \nAre there any ways to fully prevent it besides turning off WiFi?\n\nI’d like to hear other people’s thoughts on this. How much control do we actually have over this kind of tracking?", "author": "suraj_reddit_", "created_time": "2025-04-01T19:11:46", "url": "https://reddit.com/r/privacy/comments/1jp40py/how_does_my_desktop_pc_know_my_exact_location/", "upvotes": 11, "comments_count": 33, "sentiment": "neutral", "engagement_score": 77.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jp43ku", "title": "Harvard research reveals a surprising solution to climate change", "content": "Humans are storytelling creatures. As the world grapples with coordinating to [solve climate change](https://akhilpuri.substack.com/p/metacrisis-the-root-of-all-our-planetary), new research from Harvard shows that a surprising age-old mechanism might hold the answer. In results that seem like satire, the researchers found that [ancient societies coordinated using gossip](https://www.scientificamerican.com/article/the-surprising-benefits-of-gossip/). But the results make sense once we realize that coordinating with someone requires establishing trustworthiness. And how do we establish someone’s trustworthiness? By asking other people about them, i.e. gossiping!\n\nThe research has profound implications for driving the culture change required to usher in systems change. When asked how we could implement findings from the research in today’s world, the researchers replied, ”We are already doing this at scale today. We just call them Podcasts. A bunch of tech bros talking about what they heard from whom and airing their grievances at being misunderstood when they were just trying to make the world a better place”. <PERSON>, <PERSON>, and <PERSON><PERSON> could not be reached for comments on being classified as the world’s top gossips. But the results did prompt <PERSON> to announce a new podcast in another desperate attempt to fool people into liking him.\n\nIn another finding that has implications for solving the AI alignment problem, the researchers focused on how gossip creates shared reality. It is a well-established fact that our brains do not see the world as it is, but act as prediction engines based on historical information. This means that what we see as reality is just our perception. This means that to solve the AI alignment problem, we just need to believe [<PERSON> Andreessen](https://a16z.com/ai-will-save-the-world/) and [Sam Altman](https://www.cnn.com/2023/10/31/tech/sam-altman-ai-risk-taker/index.html) when they answer questions about the AI-driven apocalypse with “Just trust me bro”. AI maximalist David Shapiro vouches for the efficacy of this method, having amassed, in his words, knowledge (strong belief backed by evidence) on how it is all going to turn out fine. \n\nThe research also showed why Kamala Harris lost the election bigly to Donald Trump. She just could not keep the engines of gossip running as fast as Donald Trump. The President, speaking from the Oval Office with a bag of Cheetos, praised the breakthrough research—”I have always said that I have the best gossip. You just need to look at our leaked chat messages. China can’t beat us. They got no gossip. None. Xi wouldn’t let them have it.”\n\nSo there you have it folks. No need for any fancy solutions- no crypto currencies, no[ network states](https://thenetworkstate.com/), no [new economic models](https://doughnuteconomics.org/about-doughnut-economics), no [new cities](https://www.edgeesmeralda.com/), no spiritual awakening. Just gossip a new world into being. To learn more, listen to this 17-hour podcast between [Daniel Schmachtenberger](https://www.youtube.com/watch?v=uA5GV-XmwtM), [Ian McGilchrist and Nate Hagens](https://www.youtube.com/watch?v=F838KOrQrRg)! They clearly have the right idea!\n\n*It should, of course, be obvious by now that this is an April Fool’s Day post. I hope that reading it gave you a little bit of a laugh and served as a reminder to not take everything around us and ourselves too seriously. The future is not yet written. And we might yet find our way out of this mess that surrounds us. And if not, I for one would prefer to go down laughing. Take it easy folks.* \n\n*If you liked this post, you might want to check out my newsletter on Substack where I write about the Metacrisis and systems change-*  [*akhilpuri.substack.com*](http://akhilpuri.substack.com) *:)*", "author": "zenpenguin19", "created_time": "2025-04-01T19:14:59", "url": "https://reddit.com/r/Renewable/comments/1jp43ku/harvard_research_reveals_a_surprising_solution_to/", "upvotes": 13, "comments_count": 0, "sentiment": "bullish", "engagement_score": 13.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jp5lmt", "title": "I'm confused on who is buying Tesla Stock", "content": "Just wondering who is actually buying Tesla stock today. It's up 3.59% over yesterday. 2weeks ago it was 222.00, today it's 268.46.\nRevenue is down 71% over last year, net profit is down, it's P/E ratio is 132.20.  Compared to Toyota's P/E is 6.86 and they make money and it's stock is 17.58 US. Is it just day traders and hedge funds? To me the stock seems pretty toxic and should drop to 30-50$ range before anyone should consider buying. Am I missing something?", "author": "No-Bee6369", "created_time": "2025-04-01T20:14:30", "url": "https://reddit.com/r/investing_discussion/comments/1jp5lmt/im_confused_on_who_is_buying_tesla_stock/", "upvotes": 2075, "comments_count": 1654, "sentiment": "bullish", "engagement_score": 5383.0, "source_subreddit": "investing_discussion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jp6cr2", "title": "How do you calculate inflation with compounded interest", "content": "So if I suppose that inflation will be 3.5% in the future and I would like to have 5% return to live off of does that mean I actually need to get  8.5 % to achieve my goal? How does compounding figure into it?  FYI, I am not fire as I am to old (62) but ready to retire now i can (I am in semi retirement mode now)", "author": "Over-Kaleidoscope482", "created_time": "2025-04-01T20:44:51", "url": "https://reddit.com/r/Fire/comments/1jp6cr2/how_do_you_calculate_inflation_with_compounded/", "upvotes": 11, "comments_count": 34, "sentiment": "neutral", "engagement_score": 79.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1jpa3mh", "title": "FU money led to …. more money", "content": "I hit my FU money number recently—net worth of $1.8M at the age of 43. I realized I wasn’t going to get much farther ahead at my current company so I sort of chilled out on my work—taking on fewer projects, etc. \n\nMeanwhile I was casually looking for a new job that had fewer hours to consider barista FIRE. I got an offer from a new company which is paying me $40k more annually and I will only work a 36 hour work week. Plus I can retain benefits even if I reduce my hours to 20 a week.\n\nI’m so excited!! I don’t think this would have transpired if I cared more about my current job. So many of my coworkers live paycheck to paycheck and it’s nice to have the ability to just walk away from a stressful job, start a new job working fewer hours for more money. I don’t have a mortgage that I’m tied to, I don’t have car payments, and I have enough liquid savings to cover any big emergency expense. FI is such a critical part of this lifestyle. I almost don’t care if I can RE because I have a low stress job that I can stay at for the rest of my career.", "author": "Extra-Blueberry-4320", "created_time": "2025-04-01T23:24:47", "url": "https://reddit.com/r/Fire/comments/1jpa3mh/fu_money_led_to_more_money/", "upvotes": 2945, "comments_count": 194, "sentiment": "neutral", "engagement_score": 3333.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}]