2025-07-07 22:02:22,762 - INFO - 开始收集Reddit历史数据
2025-07-07 22:02:22,762 - INFO - 日期范围: 2025-01-01 到 2025-06-01
2025-07-07 22:02:22,762 - INFO - 目标股票: ['GOOGL', 'TSLA']
2025-07-07 22:02:22,762 - INFO - 目标子版块: 22 个
2025-07-07 22:02:22,762 - INFO - 进度: 1/22 - r/investing
2025-07-07 22:02:22,762 - INFO - 开始收集 r/investing 数据...
2025-07-07 22:02:26,725 - WARNING - Retrying due to SSLError(MaxRetryError("HTTPSConnectionPool(host='oauth.reddit.com', port=443): Max retries exceeded with url: /r/investing/hot?limit=30&raw_json=1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))")) status: GET https://oauth.reddit.com/r/investing/hot
2025-07-07 22:02:27,902 - INFO - r/investing: 获取到 30 个热门帖子
2025-07-07 22:02:35,867 - INFO - r/investing: 获取到 6 个新帖子
2025-07-07 22:02:42,860 - INFO - r/investing: 获取到 29 个month热门帖子
2025-07-07 22:02:50,925 - INFO - r/investing: 获取到 30 个year热门帖子
2025-07-07 22:02:50,926 - INFO - r/investing 获取到 95 个帖子，开始筛选...
2025-07-07 22:02:50,937 - INFO - r/investing 收集到 0 个相关帖子
2025-07-07 22:02:50,937 - INFO - 等待 27.1 秒后处理下一个子版块...
2025-07-07 22:03:18,047 - INFO - 进度: 2/22 - r/stocks
2025-07-07 22:03:18,048 - INFO - 开始收集 r/stocks 数据...
2025-07-07 22:03:25,823 - INFO - r/stocks: 获取到 30 个热门帖子
2025-07-07 22:03:31,220 - INFO - r/stocks: 获取到 4 个新帖子
2025-07-07 22:03:36,468 - INFO - r/stocks: 获取到 29 个month热门帖子
2025-07-07 22:03:43,810 - INFO - r/stocks: 获取到 30 个year热门帖子
2025-07-07 22:03:43,810 - INFO - r/stocks 获取到 93 个帖子，开始筛选...
2025-07-07 22:03:43,812 - INFO - r/stocks 收集到 0 个相关帖子
2025-07-07 22:03:43,813 - INFO - 等待 27.1 秒后处理下一个子版块...
2025-07-07 22:04:10,934 - INFO - 进度: 3/22 - r/SecurityAnalysis
2025-07-07 22:04:10,935 - INFO - 开始收集 r/SecurityAnalysis 数据...
2025-07-07 22:04:19,265 - INFO - r/SecurityAnalysis: 获取到 30 个热门帖子
2025-07-07 22:04:23,785 - INFO - r/SecurityAnalysis: 获取到 2 个新帖子
2025-07-07 22:04:28,797 - INFO - r/SecurityAnalysis: 获取到 0 个month热门帖子
2025-07-07 22:04:37,335 - INFO - r/SecurityAnalysis: 获取到 30 个year热门帖子
2025-07-07 22:04:37,336 - INFO - r/SecurityAnalysis 获取到 62 个帖子，开始筛选...
2025-07-07 22:04:37,337 - INFO - 找到相关帖子: Q1 2025 Letters & Reports... (日期: 2025-04-09)
2025-07-07 22:04:37,338 - INFO - 找到相关帖子: Societe Bic... (日期: 2025-05-27)
2025-07-07 22:04:37,340 - INFO - r/SecurityAnalysis 收集到 2 个相关帖子
2025-07-07 22:04:37,340 - INFO - 等待 27.6 秒后处理下一个子版块...
2025-07-07 22:05:04,959 - INFO - 进度: 4/22 - r/ValueInvesting
2025-07-07 22:05:04,960 - INFO - 开始收集 r/ValueInvesting 数据...
2025-07-07 22:05:10,769 - INFO - r/ValueInvesting: 获取到 30 个热门帖子
2025-07-07 22:05:16,768 - INFO - r/ValueInvesting: 获取到 2 个新帖子
2025-07-07 22:05:22,618 - INFO - r/ValueInvesting: 获取到 29 个month热门帖子
2025-07-07 22:05:28,450 - INFO - r/ValueInvesting: 获取到 30 个year热门帖子
2025-07-07 22:05:28,451 - INFO - r/ValueInvesting 获取到 91 个帖子，开始筛选...
2025-07-07 22:05:28,453 - INFO - r/ValueInvesting 收集到 0 个相关帖子
2025-07-07 22:05:28,453 - INFO - 等待 18.4 秒后处理下一个子版块...
2025-07-07 22:05:46,811 - INFO - 进度: 5/22 - r/financialindependence
2025-07-07 22:05:46,812 - INFO - 开始收集 r/financialindependence 数据...
2025-07-07 22:05:50,750 - INFO - r/financialindependence: 获取到 30 个热门帖子
2025-07-07 22:05:54,828 - INFO - r/financialindependence: 获取到 3 个新帖子
2025-07-07 22:06:03,129 - INFO - r/financialindependence: 获取到 23 个month热门帖子
2025-07-07 22:06:11,511 - INFO - r/financialindependence: 获取到 30 个year热门帖子
2025-07-07 22:06:11,512 - INFO - r/financialindependence 获取到 86 个帖子，开始筛选...
2025-07-07 22:06:11,514 - INFO - r/financialindependence 收集到 0 个相关帖子
2025-07-07 22:06:11,515 - INFO - 等待 26.3 秒后处理下一个子版块...
2025-07-07 22:06:37,859 - INFO - 进度: 6/22 - r/StockMarket
2025-07-07 22:06:37,859 - INFO - 开始收集 r/StockMarket 数据...
2025-07-07 22:06:43,221 - INFO - r/StockMarket: 获取到 30 个热门帖子
2025-07-07 22:06:50,199 - INFO - r/StockMarket: 获取到 3 个新帖子
2025-07-07 22:06:56,121 - INFO - r/StockMarket: 获取到 26 个month热门帖子
2025-07-07 22:07:02,019 - INFO - r/StockMarket: 获取到 30 个year热门帖子
2025-07-07 22:07:02,020 - INFO - r/StockMarket 获取到 89 个帖子，开始筛选...
2025-07-07 22:07:02,021 - INFO - r/StockMarket 收集到 0 个相关帖子
2025-07-07 22:07:02,022 - INFO - 等待 20.3 秒后处理下一个子版块...
2025-07-07 22:07:22,297 - INFO - 进度: 7/22 - r/investing_discussion
2025-07-07 22:07:22,297 - INFO - 开始收集 r/investing_discussion 数据...
2025-07-07 22:07:30,471 - INFO - r/investing_discussion: 获取到 30 个热门帖子
2025-07-07 22:07:38,718 - INFO - r/investing_discussion: 获取到 2 个新帖子
2025-07-07 22:07:43,147 - INFO - r/investing_discussion: 获取到 27 个month热门帖子
2025-07-07 22:07:51,625 - INFO - r/investing_discussion: 获取到 30 个year热门帖子
2025-07-07 22:07:51,625 - INFO - r/investing_discussion 获取到 89 个帖子，开始筛选...
2025-07-07 22:07:51,626 - INFO - r/investing_discussion 收集到 0 个相关帖子
2025-07-07 22:07:51,627 - INFO - 等待 19.0 秒后处理下一个子版块...
2025-07-07 22:08:10,647 - INFO - 进度: 8/22 - r/technology
2025-07-07 22:08:10,648 - INFO - 开始收集 r/technology 数据...
2025-07-07 22:08:16,443 - INFO - r/technology: 获取到 30 个热门帖子
2025-07-07 22:08:24,844 - INFO - r/technology: 获取到 11 个新帖子
2025-07-07 22:08:31,471 - INFO - r/technology: 获取到 28 个month热门帖子
2025-07-07 22:08:37,560 - INFO - r/technology: 获取到 30 个year热门帖子
2025-07-07 22:08:37,561 - INFO - r/technology 获取到 99 个帖子，开始筛选...
2025-07-07 22:08:37,563 - INFO - r/technology 收集到 0 个相关帖子
2025-07-07 22:08:37,564 - INFO - 等待 19.9 秒后处理下一个子版块...
2025-07-07 22:08:57,447 - INFO - 进度: 9/22 - r/tech
2025-07-07 22:08:57,447 - INFO - 开始收集 r/tech 数据...
2025-07-07 22:09:04,272 - INFO - r/tech: 获取到 30 个热门帖子
2025-07-07 22:09:12,300 - INFO - r/tech: 获取到 0 个新帖子
2025-07-07 22:09:19,079 - INFO - r/tech: 获取到 23 个month热门帖子
2025-07-07 22:09:25,830 - INFO - r/tech: 获取到 30 个year热门帖子
2025-07-07 22:09:25,831 - INFO - r/tech 获取到 83 个帖子，开始筛选...
2025-07-07 22:09:25,832 - INFO - r/tech 收集到 0 个相关帖子
2025-07-07 22:09:25,832 - INFO - 等待 16.5 秒后处理下一个子版块...
2025-07-07 22:09:42,336 - INFO - 进度: 10/22 - r/gadgets
2025-07-07 22:09:42,336 - INFO - 开始收集 r/gadgets 数据...
2025-07-07 22:09:49,161 - INFO - r/gadgets: 获取到 30 个热门帖子
2025-07-07 22:09:54,960 - INFO - r/gadgets: 获取到 1 个新帖子
2025-07-07 22:10:00,635 - INFO - r/gadgets: 获取到 20 个month热门帖子
2025-07-07 22:10:08,547 - INFO - r/gadgets: 获取到 30 个year热门帖子
2025-07-07 22:10:08,547 - INFO - r/gadgets 获取到 81 个帖子，开始筛选...
2025-07-07 22:10:08,548 - INFO - r/gadgets 收集到 0 个相关帖子
2025-07-07 22:10:08,548 - INFO - 等待 25.1 秒后处理下一个子版块...
2025-07-07 22:10:33,606 - INFO - 进度: 11/22 - r/apple
2025-07-07 22:10:33,606 - INFO - 开始收集 r/apple 数据...
2025-07-07 22:10:41,654 - INFO - r/apple: 获取到 30 个热门帖子
2025-07-07 22:10:49,322 - INFO - r/apple: 获取到 4 个新帖子
2025-07-07 22:10:57,957 - INFO - r/apple: 获取到 29 个month热门帖子
2025-07-07 22:11:05,424 - INFO - r/apple: 获取到 30 个year热门帖子
2025-07-07 22:11:05,424 - INFO - r/apple 获取到 93 个帖子，开始筛选...
2025-07-07 22:11:05,427 - INFO - r/apple 收集到 0 个相关帖子
2025-07-07 22:11:05,427 - INFO - 等待 19.2 秒后处理下一个子版块...
2025-07-07 22:11:24,580 - INFO - 进度: 12/22 - r/android
2025-07-07 22:11:24,581 - INFO - 开始收集 r/android 数据...
2025-07-07 22:11:45,717 - WARNING - Retrying due to ReadTimeout(ReadTimeoutError("HTTPSConnectionPool(host='oauth.reddit.com', port=443): Read timed out. (read timeout=16.0)")) status: GET https://oauth.reddit.com/r/android/hot
2025-07-07 22:11:51,703 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/hot
2025-07-07 22:11:58,765 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:12:18,784 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/hot
2025-07-07 22:12:25,507 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/hot
2025-07-07 22:12:34,084 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:13:09,092 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/hot
2025-07-07 22:13:14,242 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/hot
2025-07-07 22:13:22,280 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:13:33,562 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/new
2025-07-07 22:13:40,236 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/new
2025-07-07 22:13:48,005 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:14:08,017 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/new
2025-07-07 22:14:13,119 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/new
2025-07-07 22:14:20,790 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:14:55,810 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/new
2025-07-07 22:15:02,099 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/new
2025-07-07 22:15:10,040 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:15:19,103 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:15:24,422 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:15:32,779 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:15:52,809 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:15:58,414 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:16:06,722 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:16:41,755 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:16:47,987 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:16:56,238 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:17:05,450 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:17:11,379 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:17:20,283 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:17:40,314 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:17:46,215 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:17:54,955 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:18:29,987 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:18:35,217 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/android/top
2025-07-07 22:18:42,297 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:18:42,298 - WARNING - r/android 未获取到任何帖子
2025-07-07 22:18:42,299 - INFO - 等待 18.3 秒后处理下一个子版块...
2025-07-07 22:19:00,590 - INFO - 进度: 13/22 - r/cars
2025-07-07 22:19:00,590 - INFO - 开始收集 r/cars 数据...
2025-07-07 22:19:12,354 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/hot
2025-07-07 22:19:17,695 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/hot
2025-07-07 22:19:24,942 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:19:44,980 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/hot
2025-07-07 22:19:51,983 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/hot
2025-07-07 22:20:00,448 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:20:35,465 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/hot
2025-07-07 22:20:40,978 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/hot
2025-07-07 22:20:49,146 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:20:59,685 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/new
2025-07-07 22:21:05,197 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/new
2025-07-07 22:21:12,880 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:21:32,897 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/new
2025-07-07 22:21:39,205 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/new
2025-07-07 22:21:47,360 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:22:22,392 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/new
2025-07-07 22:22:27,996 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/new
2025-07-07 22:22:36,491 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:22:44,668 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:22:49,993 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:22:57,277 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:23:17,316 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:23:22,732 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:23:30,112 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:24:05,143 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:24:12,051 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:24:20,125 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:24:30,726 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:24:37,483 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:24:45,528 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:25:05,535 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:25:12,371 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:25:19,993 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:25:55,002 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:26:01,331 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/cars/top
2025-07-07 22:26:10,153 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:26:10,154 - WARNING - r/cars 未获取到任何帖子
2025-07-07 22:26:10,155 - INFO - 等待 25.4 秒后处理下一个子版块...
2025-07-07 22:26:35,576 - INFO - 进度: 14/22 - r/electricvehicles
2025-07-07 22:26:35,577 - INFO - 开始收集 r/electricvehicles 数据...
2025-07-07 22:26:47,501 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/hot
2025-07-07 22:26:53,930 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/hot
2025-07-07 22:27:02,021 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:27:22,045 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/hot
2025-07-07 22:27:27,215 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/hot
2025-07-07 22:27:34,513 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:28:09,519 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/hot
2025-07-07 22:28:16,236 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/hot
2025-07-07 22:28:24,462 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:28:35,307 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/new
2025-07-07 22:28:40,641 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/new
2025-07-07 22:28:48,879 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:29:08,917 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/new
2025-07-07 22:29:15,151 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/new
2025-07-07 22:29:22,358 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:29:57,375 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/new
2025-07-07 22:30:02,524 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/new
2025-07-07 22:30:10,998 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:30:20,154 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:30:26,229 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:30:34,575 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:30:54,615 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:31:00,509 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:31:09,146 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:31:44,176 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:31:51,025 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:31:59,252 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:32:08,244 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:32:14,486 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:32:23,440 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:32:43,464 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:32:49,119 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:32:58,092 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:33:33,099 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:33:39,158 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/electricvehicles/top
2025-07-07 22:33:47,356 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:33:47,356 - WARNING - r/electricvehicles 未获取到任何帖子
2025-07-07 22:33:47,357 - INFO - 等待 24.9 秒后处理下一个子版块...
2025-07-07 22:34:12,243 - INFO - 进度: 15/22 - r/teslamotors
2025-07-07 22:34:12,244 - INFO - 开始收集 r/teslamotors 数据...
2025-07-07 22:34:24,156 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/hot
2025-07-07 22:34:29,969 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/hot
2025-07-07 22:34:38,082 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:34:58,090 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/hot
2025-07-07 22:35:04,567 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/hot
2025-07-07 22:35:13,306 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:35:48,346 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/hot
2025-07-07 22:35:54,061 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/hot
2025-07-07 22:36:02,857 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:36:14,935 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/new
2025-07-07 22:36:20,095 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/new
2025-07-07 22:36:28,081 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:36:48,095 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/new
2025-07-07 22:36:54,451 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/new
2025-07-07 22:37:03,387 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:37:38,415 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/new
2025-07-07 22:37:43,647 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/new
2025-07-07 22:37:52,335 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:38:04,724 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:38:11,243 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:38:19,181 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:38:39,214 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:38:44,341 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:38:53,006 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:39:28,049 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:39:33,867 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:39:42,467 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:39:51,356 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:39:57,966 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:40:06,915 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:40:26,935 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:40:32,767 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:40:40,303 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:41:15,312 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:41:20,579 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/teslamotors/top
2025-07-07 22:41:29,112 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 22:41:29,113 - WARNING - r/teslamotors 未获取到任何帖子
2025-07-07 22:41:29,113 - INFO - 等待 21.7 秒后处理下一个子版块...
2025-07-07 22:41:50,794 - INFO - 进度: 16/22 - r/SelfDrivingCars
2025-07-07 22:41:50,794 - INFO - 开始收集 r/SelfDrivingCars 数据...
2025-07-07 22:41:50,796 - INFO - 达到请求限制，等待 1232 秒...
2025-07-07 23:02:31,998 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/hot
2025-07-07 23:02:38,334 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/hot
2025-07-07 23:02:45,956 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:03:05,974 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/hot
2025-07-07 23:03:12,943 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/hot
2025-07-07 23:03:20,402 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:03:55,437 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/hot
2025-07-07 23:04:00,922 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/hot
2025-07-07 23:04:08,508 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:04:19,453 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/new
2025-07-07 23:04:24,674 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/new
2025-07-07 23:04:33,322 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:04:53,351 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/new
2025-07-07 23:04:59,675 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/new
2025-07-07 23:05:08,397 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:05:43,437 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/new
2025-07-07 23:05:48,846 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/new
2025-07-07 23:05:57,559 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:06:05,832 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:06:12,029 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:06:20,343 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:06:40,375 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:06:46,601 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:06:54,727 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:07:29,757 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:07:34,898 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:07:42,112 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:07:51,566 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:07:57,011 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:08:04,668 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:08:24,686 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:08:31,541 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:08:40,248 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:09:15,263 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:09:21,306 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/SelfDrivingCars/top
2025-07-07 23:09:29,557 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:09:29,557 - WARNING - r/SelfDrivingCars 未获取到任何帖子
2025-07-07 23:09:29,558 - INFO - 等待 27.3 秒后处理下一个子版块...
2025-07-07 23:09:56,905 - INFO - 进度: 17/22 - r/business
2025-07-07 23:09:56,906 - INFO - 开始收集 r/business 数据...
2025-07-07 23:10:04,980 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/hot
2025-07-07 23:10:11,757 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/hot
2025-07-07 23:10:19,511 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:10:39,530 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/hot
2025-07-07 23:10:45,039 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/hot
2025-07-07 23:10:53,206 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:11:28,244 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/hot
2025-07-07 23:11:33,536 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/hot
2025-07-07 23:11:41,014 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:11:50,470 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/new
2025-07-07 23:11:56,994 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/new
2025-07-07 23:12:04,197 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:12:24,205 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/new
2025-07-07 23:12:30,076 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/new
2025-07-07 23:12:38,219 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:13:13,251 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/new
2025-07-07 23:13:20,274 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/new
2025-07-07 23:13:28,032 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:13:38,120 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:13:44,131 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:13:52,908 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:14:12,918 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:14:18,646 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:14:25,733 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:15:00,770 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:15:06,742 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:15:14,339 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:15:24,538 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:15:30,045 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:15:38,412 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:15:58,455 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:16:04,571 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:16:12,954 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:16:47,964 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:16:54,807 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/business/top
2025-07-07 23:17:02,449 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:17:02,450 - WARNING - r/business 未获取到任何帖子
2025-07-07 23:17:02,450 - INFO - 等待 26.2 秒后处理下一个子版块...
2025-07-07 23:17:28,678 - INFO - 进度: 18/22 - r/entrepreneur
2025-07-07 23:17:28,679 - INFO - 开始收集 r/entrepreneur 数据...
2025-07-07 23:17:36,721 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/hot
2025-07-07 23:17:43,029 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/hot
2025-07-07 23:17:50,268 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:18:10,299 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/hot
2025-07-07 23:18:17,311 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/hot
2025-07-07 23:18:26,269 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:19:01,300 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/hot
2025-07-07 23:19:07,977 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/hot
2025-07-07 23:19:15,696 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:19:26,780 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/new
2025-07-07 23:19:33,083 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/new
2025-07-07 23:19:41,679 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:20:01,699 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/new
2025-07-07 23:20:06,777 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/new
2025-07-07 23:20:15,742 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:20:50,764 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/new
2025-07-07 23:20:56,665 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/new
2025-07-07 23:21:04,810 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:21:14,397 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:21:19,737 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:21:27,649 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:21:47,666 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:21:53,950 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:22:02,773 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:22:37,788 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:22:44,312 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:22:52,988 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:23:01,698 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:23:08,419 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:23:15,996 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:23:36,033 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:23:42,658 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:23:51,255 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:24:26,286 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:24:32,508 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/entrepreneur/top
2025-07-07 23:24:40,367 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:24:40,368 - WARNING - r/entrepreneur 未获取到任何帖子
2025-07-07 23:24:40,368 - INFO - 等待 26.9 秒后处理下一个子版块...
2025-07-07 23:25:07,256 - INFO - 进度: 19/22 - r/startups
2025-07-07 23:25:07,257 - INFO - 开始收集 r/startups 数据...
2025-07-07 23:25:17,462 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/hot
2025-07-07 23:25:22,700 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/hot
2025-07-07 23:25:30,933 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:25:50,951 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/hot
2025-07-07 23:25:57,178 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/hot
2025-07-07 23:26:05,193 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:26:40,233 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/hot
2025-07-07 23:26:45,599 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/hot
2025-07-07 23:26:53,594 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:27:05,825 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/new
2025-07-07 23:27:11,088 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/new
2025-07-07 23:27:19,579 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:27:39,602 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/new
2025-07-07 23:27:46,215 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/new
2025-07-07 23:27:53,641 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:28:28,671 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/new
2025-07-07 23:28:34,230 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/new
2025-07-07 23:28:41,611 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:28:51,987 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:28:58,714 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:29:06,709 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:29:26,723 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:29:32,892 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:29:41,009 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:30:16,046 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:30:22,358 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:30:30,374 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:30:40,339 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:30:45,791 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:30:54,383 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:31:14,425 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:31:19,968 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:31:27,689 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:32:02,698 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:32:07,912 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/startups/top
2025-07-07 23:32:15,587 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:32:15,588 - WARNING - r/startups 未获取到任何帖子
2025-07-07 23:32:15,589 - INFO - 等待 27.2 秒后处理下一个子版块...
2025-07-07 23:32:42,752 - INFO - 进度: 20/22 - r/news
2025-07-07 23:32:42,752 - INFO - 开始收集 r/news 数据...
2025-07-07 23:32:55,098 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/hot
2025-07-07 23:33:02,061 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/hot
2025-07-07 23:33:09,216 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:33:29,223 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/hot
2025-07-07 23:33:35,351 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/hot
2025-07-07 23:33:43,191 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:34:18,204 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/hot
2025-07-07 23:34:25,125 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/hot
2025-07-07 23:34:33,818 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:34:44,056 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/new
2025-07-07 23:34:49,781 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/new
2025-07-07 23:34:56,876 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:35:16,898 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/new
2025-07-07 23:35:23,605 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/new
2025-07-07 23:35:30,966 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:36:06,011 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/new
2025-07-07 23:36:11,938 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/new
2025-07-07 23:36:19,163 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:36:29,608 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:36:34,962 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:36:43,207 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:37:03,228 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:37:10,084 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:37:17,958 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:37:52,988 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:37:58,263 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:38:05,649 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:38:14,679 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:38:21,457 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:38:30,159 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:38:50,195 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:38:56,375 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:39:04,579 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:39:39,618 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:39:46,504 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/news/top
2025-07-07 23:39:55,006 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:39:55,006 - WARNING - r/news 未获取到任何帖子
2025-07-07 23:39:55,007 - INFO - 等待 23.7 秒后处理下一个子版块...
2025-07-07 23:40:18,751 - INFO - 进度: 21/22 - r/worldnews
2025-07-07 23:40:18,752 - INFO - 开始收集 r/worldnews 数据...
2025-07-07 23:40:27,205 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/hot
2025-07-07 23:40:32,681 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/hot
2025-07-07 23:40:40,518 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:41:00,546 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/hot
2025-07-07 23:41:07,402 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/hot
2025-07-07 23:41:15,700 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:41:50,733 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/hot
2025-07-07 23:41:56,722 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/hot
2025-07-07 23:42:05,437 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:42:15,087 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/new
2025-07-07 23:42:21,538 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/new
2025-07-07 23:42:30,205 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:42:50,235 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/new
2025-07-07 23:42:55,586 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/new
2025-07-07 23:43:03,335 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:43:38,339 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/new
2025-07-07 23:43:43,737 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/new
2025-07-07 23:43:51,154 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:44:01,245 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:44:06,360 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:44:14,046 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:44:34,077 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:44:39,746 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:44:47,429 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:45:22,467 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:45:27,861 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:45:36,518 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:45:48,710 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:45:55,124 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:46:02,459 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:46:22,484 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:46:27,862 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:46:35,190 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:47:10,233 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:47:15,719 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/worldnews/top
2025-07-07 23:47:24,122 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:47:24,123 - WARNING - r/worldnews 未获取到任何帖子
2025-07-07 23:47:24,123 - INFO - 等待 19.4 秒后处理下一个子版块...
2025-07-07 23:47:43,483 - INFO - 进度: 22/22 - r/technews
2025-07-07 23:47:43,483 - INFO - 开始收集 r/technews 数据...
2025-07-07 23:47:53,570 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/hot
2025-07-07 23:48:00,006 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/hot
2025-07-07 23:48:08,171 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:48:28,205 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/hot
2025-07-07 23:48:34,478 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/hot
2025-07-07 23:48:42,630 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:49:17,658 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/hot
2025-07-07 23:49:22,773 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/hot
2025-07-07 23:49:29,924 - WARNING - 获取热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:49:41,296 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/new
2025-07-07 23:49:46,994 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/new
2025-07-07 23:49:54,307 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:50:14,344 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/new
2025-07-07 23:50:20,592 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/new
2025-07-07 23:50:27,977 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:51:02,992 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/new
2025-07-07 23:51:08,875 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/new
2025-07-07 23:51:16,387 - WARNING - 获取新帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:51:26,530 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:51:32,402 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:51:41,344 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:52:01,362 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:52:07,750 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:52:16,128 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:52:51,161 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:52:56,952 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:53:05,731 - WARNING - 获取month热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:53:17,727 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:53:23,059 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:53:30,435 - WARNING - 请求失败，等待 15 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:53:50,474 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:53:55,957 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:54:03,190 - WARNING - 请求失败，等待 30 秒后重试: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:54:38,227 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:54:44,548 - WARNING - Retrying due to ConnectionError(ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))) status: GET https://oauth.reddit.com/r/technews/top
2025-07-07 23:54:52,571 - WARNING - 获取year热门帖子失败: error with request ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-07 23:54:52,572 - WARNING - r/technews 未获取到任何帖子
2025-07-07 23:54:52,575 - INFO - 保存 GOOGL 2025-04-09: 1 个新帖子
2025-07-07 23:54:52,578 - INFO - 保存 GOOGL 2025-05-27: 1 个新帖子
