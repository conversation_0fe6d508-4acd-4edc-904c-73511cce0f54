[{"platform": "reddit", "post_id": "reddit_1jrxrzw", "title": "Tell me why Tariffs should be bearish for Google", "content": "I hold a small long Google position via stock and options with most positions in UST, BRK/B and SPY. My BRK/B and SPY positions are fully hedged with puts. I get it that market is in panic mode and thus selling everything, but I’m curious to know your opinion/analysis about Google. Is there any fundamental reason that we should believe its growth would be affected by the Tariffs ? \n\nI can’t think of any except that “recession “ Wall Street has been screaming about. However from eco data , we can’t see how a recession is imminent?\nThanks a lot.", "author": "LittleHanaSister", "created_time": "2025-04-05T07:09:03", "url": "https://reddit.com/r/ValueInvesting/comments/1jrxrzw/tell_me_why_tariffs_should_be_bearish_for_google/", "upvotes": 76, "comments_count": 69, "sentiment": "bearish", "engagement_score": 214.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1js29lw", "title": "HEY GOOGLE! UN BREAK DO NOT DISTURB- recent update modes suck", "content": "Do not disturb worked great. PAST TENSE.\n\nI used to get up on weekends and tap DND and poof- I got notifications. It was great! I could tap it again and turn it on.\n\nAfter this recent update I can't figure out how to turn off my weekend setting when I get up without turning off the entire preset schedule?\n\nUN BREAK IT! PLEASE! I HOPE YOU ARE LISTENING!", "author": "pdots5", "created_time": "2025-04-05T12:24:33", "url": "https://reddit.com/r/GooglePixel/comments/1js29lw/hey_google_un_break_do_not_disturb_recent_update/", "upvotes": 587, "comments_count": 116, "sentiment": "neutral", "engagement_score": 819.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1js3a54", "title": "Chrome not signing out Google account when closing browser", "content": "It seems after the last update that my Google account doesn't automatically sign out of chrome when I close chrome   I prefer my Google account to be signed out when closing the browser for security reasons. Is there a new setting I'm missing?  While tedious at times, I do prefer having to re sign in after booting up chrome. \n\nThanks for any and all thoughts! ", "author": "jwelsh044", "created_time": "2025-04-05T13:19:25", "url": "https://reddit.com/r/chrome/comments/1js3a54/chrome_not_signing_out_google_account_when/", "upvotes": 0, "comments_count": 28, "sentiment": "neutral", "engagement_score": 56.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1js4vjo", "title": "Google is preparing to launch veo 2 soon", "content": "", "author": "Unknown<PERSON><PERSON>ce", "created_time": "2025-04-05T14:37:42", "url": "https://reddit.com/r/singularity/comments/1js4vjo/google_is_preparing_to_launch_veo_2_soon/", "upvotes": 650, "comments_count": 99, "sentiment": "neutral", "engagement_score": 848.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1js7c4i", "title": "Google Ads users — what sucks the most about managing negative keywords?", "content": "Hey everyone — I’m a software developer exploring the PPC space. I’ve been studying Google Ads (mostly theory so far), and I’m looking to build a small tool that *actually* solves annoying problems for people managing campaigns every day.\n\nRight now, I’m super curious about negative keyword management. It seems like something that’s important but also kind of tedious and easy to mess up.\n\nIf you had a magic wand, what’s one thing you’d *automate*, *improve*, or *simplify* when it comes to negative keywords?\n\nNot here to sell anything — just want to listen and build something useful. Your insights would help me a ton 🙏", "author": "Flat-Zombie5443", "created_time": "2025-04-05T16:29:02", "url": "https://reddit.com/r/PPC/comments/1js7c4i/google_ads_users_what_sucks_the_most_about/", "upvotes": 1, "comments_count": 29, "sentiment": "bearish", "engagement_score": 59.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1js8kru", "title": "IBM Data Analyst Professional Certificate OR Google Data Analytics Professional Certificate", "content": "Hello, I am a Informatics and Telecommunications student and I am interested in learning more about Data Analytics. I already have knowledge on Informatics through University so I am not a complete beginner. I saw those 2 certificates and they both seemed very interesting for a beggining in this field. But I am having trouble in choosing. I want to gain as much knowledge as possible in this field in order to slowly start working. Which of these would you recommend? Do you maybe have any other recommandations on how to start? Thank you", "author": "RecommendationDry605", "created_time": "2025-04-05T17:22:58", "url": "https://reddit.com/r/analytics/comments/1js8kru/ibm_data_analyst_professional_certificate_or/", "upvotes": 53, "comments_count": 28, "sentiment": "neutral", "engagement_score": 109.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jsghlf", "title": "MMW: EU will not impose services tariffs on US exports because there is no viable substitute to Google and Meta for EU businesses to sell and market goods at scale.", "content": "This would just penalize their own economy. For other items I do believe viable alternatives exist and thus they’re willing to impose them. Note: yes I know for some reason the US executive branch doesn’t seem to be considering this.\n\nEdit: I’m not going to try to debate with people who don’t understand the trillions in capex (even before AI) put towards data centers by FAAMG that would make it a decade long project for the EU to create a substitute. Also recreate, Google, YouTube, LinkedIn, Facebook, instagram etc for marketing dollars.", "author": "Socks797", "created_time": "2025-04-05T23:19:13", "url": "https://reddit.com/r/ValueInvesting/comments/1jsghlf/mmw_eu_will_not_impose_services_tariffs_on_us/", "upvotes": 0, "comments_count": 17, "sentiment": "neutral", "engagement_score": 34.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "GOOGL"}]