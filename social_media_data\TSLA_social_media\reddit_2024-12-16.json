[{"platform": "reddit", "post_id": "reddit_1hf8jkf", "title": "Most of the accusations of AI stealing intellectual property are a form of Dunn<PERSON><PERSON><PERSON><PERSON><PERSON> effect", "content": "People vastly overestimate originality/uniqueness of their \"creative\" output. They ignore that human cognitive and creative ability are bounded and over the years as our culture has become more and more homegenized, they have been constrained to be within a narrow limit. So most of them are shocked to learn that whatever they create and think is original, probably a million other people came up with very similar things independently. And when you put them together they all line up within a very predictable statistical distribution that is trivial for any predictive algorithm to learn given enough compute. And then it can pump out infinite amount of content from that same distribution. In fact most of the mid content creators make stuff exactly in this way, our brain is good in inferring this distribution with enough training. You see this type of mid, predictable content in every creative field - like music where every single song sounds exactly similar to something you have heard before or movies/shows whose plots are so predictable you can see all the \"twists\" coming from a mile away. Occasionally there will be a content of high originality and creativity, but soon others will imitate that and create many similar things making the distribution wide again. I think more than anyone else the creatives should be using AI the most to understand exactly where their output falls in this distribution and thus enabling them to go outside the distribution to create something actually new.", "author": "obvithrowaway34434", "created_time": "2024-12-16T02:04:37", "url": "https://reddit.com/r/singularity/comments/1hf8jkf/most_of_the_accusations_of_ai_stealing/", "upvotes": 40, "comments_count": 45, "sentiment": "neutral", "engagement_score": 130.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hf9n4e", "title": "How Google turned Jaguars into self-driving taxis, but General Motors gave up", "content": "", "author": "nick7566", "created_time": "2024-12-16T03:02:30", "url": "https://reddit.com/r/SelfDrivingCars/comments/1hf9n4e/how_google_turned_jaguars_into_selfdriving_taxis/", "upvotes": 143, "comments_count": 32, "sentiment": "neutral", "engagement_score": 207.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hf9vxt", "title": "Boomer Uncle in Law ", "content": "So I met this uncle in law who is like 60 this summer for the first time and wasted no time to tell me how great of a trader he is and that he manages other peoples money which is fucking scary. He proceeded to tell me to buy ALT and CLF both are way lower than in when he told me to buy. Ever since then he keeps texting me and all he wants me to buy is XOM and NUE which he took a huge loss on both. Meanwhile I told him to buy Tesla at 220 , Meta at 540 , MSTR at 160. What is wrong with these boomers who keep calling for a crash every day ", "author": "Moist-Ad2764", "created_time": "2024-12-16T03:16:02", "url": "https://reddit.com/r/wallstreetbets/comments/1hf9vxt/boomer_uncle_in_law/", "upvotes": 5, "comments_count": 56, "sentiment": "bullish", "engagement_score": 117.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hf9zlt", "title": "Don't like <PERSON><PERSON>, Love trading TSLA", "content": "https://preview.redd.it/vgtrwuh1n47e1.png?width=2944&format=png&auto=webp&s=d3252053bd96fe6b731cab2b4455fa847155d889\n\nYes, I’m a hypocrite.\n\nTSLA has been absolute madness since I started trading it in October. Mostly been selling puts, but even selling calls turned out profitable.\n\nHere are the results per 10 contracts (I usually trade 2, max 3 )", "author": "No_Supermarket_8647", "created_time": "2024-12-16T03:21:42", "url": "https://reddit.com/r/options/comments/1hf9zlt/dont_like_elon_musk_love_trading_tsla/", "upvotes": 0, "comments_count": 39, "sentiment": "bearish", "engagement_score": 78.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfaitq", "title": "27 need advice for future", "content": "HYSA: $124,000\n\nIndividual brokerage: $103,000\n\nROTH 401K: $60,000\n\nStudent loan: 22,000 ($300 a month) interest rate 4.50%\n\n\nInvested in VOO/MSFT/SMH/TSLA\nMajority in voo ,smh,msft \n\n27M, single , Living at home for 5 years after college so was able to save….but plan on moving out in 2025 but will be renting.\n\nShould I open a Roth IRA? \nShould I just pay off all my student loans 300 a month about 70$ in interest payments\n\n60k in HYSA for emergencies. Rest saved up for a down payment but market for condos in NY/NJ is tough. \n\n\nWant to know if there is any suggestions on what I should change/ reallocate / do for the future for continued growth ????\n\nKind of feel like taking foot off gas for 1 year and just contributing enough to get employer match and enjoy life by experiences etc ", "author": "SnooPets6005", "created_time": "2024-12-16T03:52:28", "url": "https://reddit.com/r/personalfinance/comments/1hfaitq/27_need_advice_for_future/", "upvotes": 2, "comments_count": 8, "sentiment": "bullish", "engagement_score": 18.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfcn1n", "title": "Palo Alto Networks (PANW) can it 10x in next 5 years ", "content": "I’m evaluating an investment in Palo Alto Networks and would value your perspectives. Here are three compelling reasons to consider this stock:\n\t•\tStrong Financial Performance: In Q1 of fiscal 2025, Palo Alto Networks reported a 14% year-over-year revenue increase to $2.14 billion, with net profit soaring by more than 80% to $350.7 million, indicating effective operational strategies. ￼\n\t•\tStrategic Platformization: The company’s shift towards a comprehensive cybersecurity platform is gaining traction, with approximately 70 new platform customers added in the recent quarter, aligning with industry trends towards consolidated security infrastructures. ￼\n\t•\tExpanding Cloud Security Market: With the Cloud-Native Application Protection Platform (CNAPP) market projected to reach $6 billion by 2028, Palo Alto Networks’ Prisma Cloud offering is well-positioned to capitalize on this growth, supported by regular updates and competitive bundling efforts. ￼\n\nWhat are your thoughts on investing in PANW? Do you see potential challenges or additional opportunities that should be considered?", "author": "sidsaladi", "created_time": "2024-12-16T05:58:31", "url": "https://reddit.com/r/ValueInvesting/comments/1hfcn1n/palo_alto_networks_panw_can_it_10x_in_next_5_years/", "upvotes": 2, "comments_count": 6, "sentiment": "bullish", "engagement_score": 14.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hffbp9", "title": "Why passkeys are dangerous ", "content": "1. **Passkeys as the Future** Passkeys are widely promoted as the replacement for passwords, but this claim deserves scrutiny. While they are touted as more secure than passwords, the reality is more nuanced.\n\n2. **Private Keys and Security** Assumptions Passkeys use a pair of private and public keys, with the private key intended to remain secret. On the surface, this appears secure since private keys cannot be exposed under normal circumstances. However, the practical implementation raises concerns.\n\n3. **Platform Lock-In** Some platforms, like Apple, lock users into their ecosystem. If you create a passkey on Apple’s platform, you often cannot transfer it. You’re forced to either change the passkey (if allowed) or continue using your Apple device, limiting flexibility and user control.\n\n4. **Data Breaches** Passkeys are claimed to be immune to data breaches. However, platforms like iCloud Keystore store passkeys. Even if encrypted, they are not hashed, which makes them potentially vulnerable to hacking. Encryption can be broken, adding significant risk.\n\n5. **Zero-Day Exploits** Zero-day vulnerabilities are a major risk. If an exploit enables hackers to extract private keys from devices, it could compromise all passkeys for every user. Such an event could lead to catastrophic consequences, making passkeys a massive target.\n\n6. **Physical Theft** If your device is stolen, all accounts protected by passkeys could be compromised. This risk is mitigated with traditional passwords combined with two-factor authentication (2FA), as a thief would still need to know your password.\n\n7. **Comparison to Passwords** with 2FA Passkeys are better than passwords without 2FA or weak passwords with 2FA. However, a strong password combined with 2FA is likely more secure. Password managers can generate and store strong passwords, reducing the burden of remembering them.\n\n8. **Façade of Security** Passkeys provide a sense of security due to their modern technology. This can lead to complacency, as users may believe they no longer need to prioritize security practices. Overconfidence in a system can be dangerous.\n\n9. **Increased Attack Vector** Passkeys introduce a new attack vector. A single vulnerability could compromise a massive number of accounts simultaneously. This concentrated risk makes passkeys a potentially attractive target for hackers.\n\n10. **Coexistence with Passwords** Passkeys are often implemented alongside passwords, rather than replacing them. This adds complexity and additional attack surfaces without eliminating existing password vulnerabilities.\n\n**Conclusion** Passkeys are not inherently a perfect solution. They may improve security in some cases but fall short in others. A more secure approach involves combining strong passwords with 2FA and maintaining a vigilant mindset toward security.\n\nWhat are your thoughts on this? Am I wrong?\n\nEdit: Changed to better formatting and rephrased with chatgpt", "author": "CountyMiserable9917", "created_time": "2024-12-16T09:18:32", "url": "https://reddit.com/r/privacy/comments/1hffbp9/why_passkeys_are_dangerous/", "upvotes": 0, "comments_count": 78, "sentiment": "bullish", "engagement_score": 156.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfg16i", "title": "What should I do with 600k", "content": "I came into a sum of money. I bought a house and paid off half of it. The rest is neutrally geared and just ticks away in the background. I barely even think about it. I still have around 600k cash though and would love to turn that into as much as possible of course. How much do you think I can potentially make from this? I'm 40 years old and earn around 60k a year\n\nBefore some of you say, be happy with what you have,  I definitely am. I have never had money in my entire life. Quite the opposite. I just would like to set myself, and my kids up when they come along.", "author": "Beautiful-Bit-19", "created_time": "2024-12-16T10:13:32", "url": "https://reddit.com/r/StockMarket/comments/1hfg16i/what_should_i_do_with_600k/", "upvotes": 59, "comments_count": 365, "sentiment": "bullish", "engagement_score": 789.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfg752", "title": "What are some examples of extremely financially wealthy people/celebrities who don't like to flaunt their wealth? I", "content": "I want to achieve true financial independence one day, but it seems like every wealthy person on social media who has money just acts like a pretentious asshole, for example I've seen reels of celebrities wearing ridiculous jewel-studded designer clothing just to go buy coffee, or those viral videos of rich international students driving their Lamborghinis to class. It honestly really puts me off the idea of having wealth, because I would never want to be in the company of this type of person - they honestly make me feel a bit sick lol. These aren't really the kind of role models that I want to have, even though I aspire to attain their level of wealth so that I no longer have to work for a living. I feel like most rich people buy and wear extremely expensive things just to show that they can afford it, and they also act like douchebags to people who aren't as well off. Are there any noteworthy examples of famous people who've achieved high levels of wealth but prefer to live a more down-to-earth lifestyle, and more importantly are actually good, decent human beings?", "author": "cs342", "created_time": "2024-12-16T10:26:11", "url": "https://reddit.com/r/Fire/comments/1hfg752/what_are_some_examples_of_extremely_financially/", "upvotes": 0, "comments_count": 50, "sentiment": "bullish", "engagement_score": 100.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfhpxv", "title": "Tesla has started lithium refining outside Corpus Christi Texas ", "content": "1", "author": "dcahill78", "created_time": "2024-12-16T12:10:26", "url": "https://reddit.com/r/teslainvestorsclub/comments/1hfhpxv/tesla_has_started_lithium_refining_outside_corpus/", "upvotes": 175, "comments_count": 29, "sentiment": "neutral", "engagement_score": 233.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfi4nk", "title": "Many people talk about LLMs but a lot of people sleeping on real world AI ", "content": "Having been interested in this field for many years, I’m still in awe of the progress Google and Tesla have made this year. It’s not just about self-driving cars like Waymo or Tesla’s FSD v13.2, but also advancements in robotics. The stock market seems to be catching on, with GOOGL and TSLA performing very well. I’m really looking forward to what 2025 will bring! Do you disagree, pls tell me your opinion :) ", "author": "AlbatrossHummingbird", "created_time": "2024-12-16T12:35:53", "url": "https://reddit.com/r/singularity/comments/1hfi4nk/many_people_talk_about_llms_but_a_lot_of_people/", "upvotes": 78, "comments_count": 48, "sentiment": "neutral", "engagement_score": 174.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfipzt", "title": "Can I click on my own ads that I am paying for in Google Ads to test a conversion and does this violate Google Ads policy (rather than violating Adsense policy)?", "content": "Hi. A redditor had mentioned to me that clicking my own ads to test a conversion could be a violation of Google Ads policy.  Now clicking your own ad is definitely a violation of Google Adsense policy as per their documentation for Adsense:\n\n> [Although publishers are not permitted to click on their own ads for any reason ...](https://support.google.com/adsense/answer/1348754?hl=en)\n\nand\n\n> [Please note that clicks on Google ads must result from genuine user interest, and any method that artificially generates clicks or impressions is strictly prohibited by our program policies. If we observe high levels of invalid traffic on your account, we may suspend or disable the account to protect our advertisers and users](https://support.google.com/admob/answer/3342054?hl=en) \n\nBut is clicking on your own ad that you are paying for a violation of Google Ads policy?  Of course, I am referring to Google Ads policy here and not Google Adsense policy.  I want to test a conversion and ensure the conversion is counted in Google Ads (I know that I can use Tag Assistant to debug a conversion, but testing in Tag Assistant is ultimately not counted in Google Ads). I searched for this on Google and I can't find any official documentation on whether clicking on your own ad that you are paying for in Google Ads, is a violation of Google Ads policy.\n\nIn the Google Community forums, a [Platinum Product Expert suggests clicking on your ad to test a conversion](https://support.google.com/google-ads/thread/*********/manually-clicking-on-google-ads-to-test-conversion-tracking-is-a-standard-way-to-test?hl=en) - surely, the Platinum Product Expert wouldn't be giving advice that is violating Google Ads policy that could get your account suspended/banned?\n\nSo can I click on my own Google Ads that I am paying for, to test a conversion and does this violate Google Ads policy (not Adsense policy)?", "author": "trucker-123", "created_time": "2024-12-16T13:09:40", "url": "https://reddit.com/r/adwords/comments/1hfipzt/can_i_click_on_my_own_ads_that_i_am_paying_for_in/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfjvh8", "title": "2.5 year follow-up on buying the dip on pandemic stocks", "content": "I bought the dip on several 'pandemic stocks' that had significant declines (70%+) in 2022 and started sharing public updates 1-2 times a year.\n\nIn Q3 2023 I began reallocating into AI-related stocks when I developed strong conviction. I'm also working on a self-funded AI startup, which keeps me in the loop on AI.\n\nReturns have been strong and continue to give me the runway to work on my startup and support my family after leaving my corporate tech job. There's more context in my previous updates linked below.\n\n**Previous updates:**\n\n* [1 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/147v53k/1_year_followup_on_buying_the_dip_on_pandemic/)\n* [1.5 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/17mzc07/15_year_followup_on_buying_the_dip_on_pandemic/)\n* [2 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/1d758m7/2_year_followup_on_buying_the_dip_on_pandemic/)\n\n# Progress updates\n\nBelow are the returns for this portfolio (opened in 2022) as of December 2024.\n\n# Realized (sold) in 2023\n\n* **Affirm (AFRM):** \\+18.09%\n* **Allbirds (BIRD):** \\+6.89%\n* **Coinbase (COIN):** \\+6.42%\n* **Carvana (CVNA):** \\+845.57%\n* **Meta (META):** \\+256.36%\n* **Cloudflare (NET):** \\+50.67%\n* **Netflix (NFLX):** \\+122.39%\n* **Peloton (PTON):** \\-71.51%\n* **Roblox (RBLX):** \\+25.57%\n* **Shopify (SHOP):** \\+51.27%\n* **Snapchat (SNAP):** \\-1.99%\n* **Unity (U):** \\+27.57%\n\nSee the [previous update](https://www.reddit.com/r/investing/comments/1d758m7/2_year_followup_on_buying_the_dip_on_pandemic/) for comments.\n\n# Realized (sold) in 2024\n\n* **Advanced Micro Devices (AMD):** \\-18.69% (\\*)\n* **Amazon (AMZN):** \\+97.41%\n* **Alphabet/Google (GOOGL):** \\+75.05%\n* **Apple (AAPL):** \\*****% (\\*)\n* **ARM (ARM):** \\+10.52% (\\*)\n* **ASML (ASML):** \\-7.40% (\\*)\n* **Intel (INTC):** \\-20.12% (\\*)\n* **Microsoft (MSFT):** \\*****% (\\*)\n* **Netflix (NFLX):** \\+361.74%\n* **Palantir (PLTR):** \\+65.72% (\\*)\n* **Roblox (RBLX):** \\+42.66%\n* **Shopify (SHOP):** \\+86.96%\n* **Snowflake (SNOW):** \\+51.57%\n* **Super Micro Computer (SMCI):** \\+14.48% (\\*)\n* **Taiwan Semiconductor (TSM):** \\+103.19%\n* **Tesla (TSLA):** \\+92.16% (\\*)\n* **Unity (U):** \\-8.73%\n\n(*\\*) Short term capital gain/loss held for <1yr*\n\nI held several companies (\\*) for a short time to spread out my AI bets, but some of the increasingly high P/E ratios concerned me (PLTR, TSLA, etc.).\n\nSo, I sold them and purchased more NVIDIA. This approach is considered risky, but I have strong conviction in their relative valuation, defensibility, and long-term prospects.\n\nIn this case I believe that 'diversification' among AI stocks would do more to dilute my returns than mitigate my risks. Particularly because they're all highly correlated and despite its market cap, Nvidia has the most reasonable valuation all things considered. We'll see if the bet pays off.\n\nTo clarify, I am confident in the long-term prospects of Palantir, Tesla, and others, but I'm just not comfortable with the valuations. I may reassess in the future when I have new information or valuations change.\n\nMeta is the only other stock I held onto, given my bullish view on their AI and hardware strategy combined with their attractive valuation.\n\n# Unrealized (current investments)\n\n* **Meta (META):** \\+367.97%\n* **Nvidia (NVDA):** \\+117.05%\n\nNvidia's performance here is understated since I recently increased my holdings, but it's up \\~200% since I initially purchased it last year after dithering for months.\n\nThese returns are less impactful without the values or relative weights, but I’d like to maintain some anonymity around it.\n\n# Rate of return (IRR)\n\nMy annual return (IRR) for this portfolio (Jun 2022 to Dec 2024) is **73.6%**.\n\n# Investment thesis\n\nSee a summary of my investment thesis in a [previous update](https://www.reddit.com/r/investing/comments/17mzc07/15_year_followup_on_buying_the_dip_on_pandemic/). **TL;DR:**\n\n>“…AI is another secular trend like PCs (Windows, Mac), the internet (browsers, search, social) and mobile (iOS, Android, wearables). The difference is that new technology like AI can now spread faster than ever before and get used in new ways. Every new epoch uniquely benefits from the past, potentially bending the growth curve in new ways.\n\n>The other difference is that Nvidia has a monopoly position on the core technology driving this innovation. Therefore, the \\~350% run up over the last 12 months doesn’t make NVIDIA the stock of the last year, but rather it’s the stock of the next decade. The recent 3X gain will be a blip compared to what’s coming thanks to NVIDIA’s CUDA (moat), among [other things](https://stratechery.com/2023/nvidia-on-the-mountaintop/).”\n\n>\\- [1.5 year follow-up on buying the dip on pandemic stocks](https://www.reddit.com/r/investing/comments/17mzc07/15_year_followup_on_buying_the_dip_on_pandemic/), Nov. 2023\n\n# Nvidia's dominance\n\nI'm still working through my thoughts here, but my strong conviction around Nvidia comes in part from these observations:\n\n* Nvidia is [founder-led](https://www.amazon.com/Nvidia-Way-Jensen-Huang-Making/dp/1324086718/) and focuses on accelerated computing while remaining broad enough to allow for innovation and insight across industries where it can apply its core competencies.\n* It's unprecedented to have such dominance in such a fast-growing, valuable industry while maintaining such a long-term sustainable advantage.\n* This advantage is due to unreasonable investments (research, hardware, software, ecosystem, relationships) over decades, making it hard to copy.\n* AI will consistently make software easier to create, thus reducing the moats of software companies and make them less attractive than hardware companies (for now).\n* Despite their impressive growth, Nvidia is still supply constrained, which is fundamentally easier to predict than demand constraints.\n\n# We still underestimate the AI opportunity\n\nMost importantly, we’ve barely scratched the surface of AI’s opportunities and benefits. Even the most ambitious targets underestimate it because the better and cheaper AI gets, the more use cases we'll find.\n\nWe have a habit of confusing the limits of our imagination with the limits of reality. Our imaginations are trained on what happened before, but there has never been anything like this before.\n\n# Final thoughts\n\nI'm still well within the '*maybe I'm just lucky*' phase since it's only been a couple years. Towards the end of [my last update](https://www.reddit.com/r/investing/comments/1d758m7/2_year_followup_on_buying_the_dip_on_pandemic/) I also shared a few ways my investment approach has changed, which I’m still benefiting from.\n\nI expect my next update to be the 3-year update in mid 2025.\n\n**UPDATE:** Thanks for mentioning I should have included indexes for benchmark context. Indexes like QQQ and S&P 500 also had good returns during the same period (June 2022 - Dec 2024). For an apples to apples comparison, the IRR of QQQ and S&P 500 respectively during this period were \\~24% and \\~16%.", "author": "gabe736", "created_time": "2024-12-16T14:08:16", "url": "https://reddit.com/r/investing/comments/1hfjvh8/25_year_followup_on_buying_the_dip_on_pandemic/", "upvotes": 839, "comments_count": 184, "sentiment": "bullish", "engagement_score": 1207.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfkxaf", "title": "Mismatching numbers in different dashboards - how much time do you lose on this?  ", "content": "In my company there's far too many dashboards, and one of the problems is that KPIs never match. I am wasting so much time every week on this, so just wondering if this is a common problem in analytics. How is it for you guys? ", "author": "NoSeatGaram", "created_time": "2024-12-16T14:58:06", "url": "https://reddit.com/r/analytics/comments/1hfkxaf/mismatching_numbers_in_different_dashboards_how/", "upvotes": 44, "comments_count": 35, "sentiment": "neutral", "engagement_score": 114.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfli5p", "title": "[Giveaway] Unihertz Jelly Max × r/gadgets – Win the World’s Smallest 5G Smartphone!", "content": "Unihertz is a smartphone manufacturer dedicated to creating smartphones with unique\nfeatures, such as ultra-compact sizes, QWERTY keyboards, rugged builds, massive\nbattery capacities, and even projector functions.\n\nIn a market dominated by larger smartphones, Unihertz's Jelly series has stood out for its\ncompact size. Now, Unihertz reimagines the series with the Jelly Max, perfectly\nbalancing portability and performance. As the world’s smallest 5G smartphone, Jelly Max\noffers:\n\n* 5-inch display designed to balance portability and power.\n* Dimensity 7300 5G chipset with 12GB RAM and 256GB ROM for smooth\nmultitasking and powerful performance.\n* 4000mAh battery with 66W fast charging, reaching 90% in just 20 minutes.\n* Android 14 OS for enhanced security, seamless connectivity, and user-friendly\nfeatures.\n* 100MP main camera with 3.4X optical zoom and a 32MP front camera for\nincredible photos and selfies.\n* Transparent curved design for a sleek, comfortable, and pocket-friendly grip.\n\nLearn more about the Jelly Max on the official Unihertz website:\n\nhttps://www.unihertz.com/products/jelly-max.\n\nTo celebrate the launch of this incredible smartphone, we've partnered with Unihertz to\ngive away a Jelly Max to one lucky gadget lover on Reddit!\n\n#How To Enter:\n\n#Contest 1:\n\n* Leave a top-level comment about how a small smartphone fits seamlessly\ninto your lifestyle.\n\n#Contest 2:\n\n* Navigate to https://bit.ly/UMaxGiveaway and use any of the entry methods for a\nchance to win a Jelly Max.\nYou can win either contest, so we recommend participating in both!\nRules:\n* One winner will be randomly selected from top-level comments that meet the\nentry requirement (Contest 1)\n* One top-level comment/entry per person. Duplicate entries will be removed.\n* Accounts must be 90 days old by December 15, 2024\n* Contest restricted to residents in regions where Unihertz’s official website can\nship. For more details, visit https://www.unihertz.com.\n* Entries are open until January 15th.\n* Moderators are not eligible to win.\n* Winners of the two contests must be different individuals.\n* If the winner does not meet the eligibility requirements, a new winner will be\nselected from the comments.", "author": "noeatnosleep", "created_time": "2024-12-16T15:24:26", "url": "https://reddit.com/r/gadgets/comments/1hfli5p/giveaway_unihertz_jelly_max_rgadgets_win_the/", "upvotes": 3, "comments_count": 147, "sentiment": "neutral", "engagement_score": 297.0, "source_subreddit": "gadgets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfnitv", "title": "SCHD alternative?", "content": "I believe SCHD is the best etf out there with a good dividend! But do you guys know any other etf like SCHD that you consider better? ", "author": "These_Care1095", "created_time": "2024-12-16T16:53:21", "url": "https://reddit.com/r/dividends/comments/1hfnitv/schd_alternative/", "upvotes": 0, "comments_count": 31, "sentiment": "neutral", "engagement_score": 62.0, "source_subreddit": "dividends", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfnomz", "title": "This video has over 1.5M likes. You can just make things up and people will believe you…", "content": "“Programmed with 100 trillion GPUs” was my favorite. Yes there’s over 10,000 of these GPUs for each human on earth.", "author": "Glittering-Neck-2505", "created_time": "2024-12-16T17:00:12", "url": "https://reddit.com/r/singularity/comments/1hfnomz/this_video_has_over_15m_likes_you_can_just_make/", "upvotes": 5, "comments_count": 42, "sentiment": "neutral", "engagement_score": 89.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfp2qj", "title": "Google Surprises Everyone With Veo 2: <PERSON><PERSON> Videos Outshine OpenAI's Sora", "content": "", "author": "Ethan<PERSON>ill<PERSON>s_TG", "created_time": "2024-12-16T17:58:19", "url": "https://reddit.com/r/artificial/comments/1hfp2qj/google_surprises_everyone_with_veo_2_sample/", "upvotes": 90, "comments_count": 25, "sentiment": "neutral", "engagement_score": 140.0, "source_subreddit": "artificial", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfpozu", "title": "Democrats eye Harris 2028 presidential run as they devise political comeback", "content": "", "author": "const_iterator", "created_time": "2024-12-16T18:23:46", "url": "https://reddit.com/r/nottheonion/comments/1hfpozu/democrats_eye_harris_2028_presidential_run_as/", "upvotes": 0, "comments_count": 124, "sentiment": "neutral", "engagement_score": 248.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfqezj", "title": "Are you getting noticed?", "content": "After almost a year of coding my platform, I started promoting it online. I tried different social media and platforms to get feedback, but I barely got any engagement—like, **almost none** (so yeah, you can imagine how frustrated I was).\n\nAt first, I thought my product was *trash*, but then I realized… I’d never actually done any marketing before, and it turned out to be way less straightforward than it seemed.\n\n*What’s the point of building the best platform/app/whatever if no one even knows it exists?*\n\nYeah, that question haunted me for months until...I discovered **Reddit.**\n\nAlmost by chance, I started posting in various Reddit communities, sharing my story, my journey, and what I was working on. This time, the posts were doing well—I managed to get some users to check out my platform and gather useful feedback. But something was still missing, and I couldn’t figure out what…Then, while watching Spider<PERSON><PERSON> with a hot margherita pizza next to me, it hit me: “right place, right time.” That’s when the idea for postonreddit clicked.\n\nOn Friday night, I bought the domain and between Saturday and Sunday, *fueled by a good amount of caffeine*, I decided to build an MVP and a landing page (little sleep, lots of coffee—I’m the perfect stereotype). The idea is simple: postonreddit is a platform that lets you schedule your posts on Reddit and suggests (based on the community you select) the best day and time to post to reach as many people as possible.\n\nSo now I can promote my platform with posts on Reddit, writing and publishing them directly from my other platform that I built just for this (the metaverse is pretty awesome haha).\n\nI’ve still got a ton of code to write, so I’d better leave here and open up VS Code, or else I’ll end up not sleeping again tonight… If the idea seems interesting, let me know what you think and if you hate it… still let me know what you think!", "author": "WerewolfCapital4616", "created_time": "2024-12-16T18:54:07", "url": "https://reddit.com/r/Entrepreneur/comments/1hfqezj/are_you_getting_noticed/", "upvotes": 0, "comments_count": 19, "sentiment": "neutral", "engagement_score": 38.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfrsrx", "title": "Looking for partner [My strongest skill; sales] needed [experienced paid ads manager]. ", "content": "Hello.\n\nI am looking for a potential partner to start a SMMA.\n\nWhat I can offer is my experience selling high-ticket products in B2B and B2C. In my B2B experience, I've closed contracts after 1.5 years of negotiation and retained them for more than 3 years. In my B2C experience, I've closed $120,000 deals from cold calling and hyping customers who weren't actively looking in the market.\n\nWhat I'd do in the business is manage the sales cycle and bring in business. Part of the offer I am building requires someone to be in person with the customer, so I'll do that part as well.\n\nThe partner I need; proven experience in Google ads, Meta ads, Linkedin Ads, and Pinterest ads. Experience in sales funnels, and video editing. (I can also help with video editing, but a partner with this skill would add up too).\n\nDM me if you have;\n\nSpent +$50,000 in paid ads.\n\nProven ROI 5/10:1. \\[you've generated from $5 to $10 for every spent dollar in ads\\]\n\nOur goal is to close, deliver and maintain 200 retainers at $2,000 each. 51-49% profit sharing.", "author": "wanna_become", "created_time": "2024-12-16T19:51:42", "url": "https://reddit.com/r/adwords/comments/1hfrsrx/looking_for_partner_my_strongest_skill_sales/", "upvotes": 0, "comments_count": 1, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfslm8", "title": "Democrats eye Harris 2028 presidential run as they devise political comeback", "content": "", "author": "anonymous-69", "created_time": "2024-12-16T20:26:13", "url": "https://reddit.com/r/nottheonion/comments/1hfslm8/democrats_eye_harris_2028_presidential_run_as/", "upvotes": 0, "comments_count": 242, "sentiment": "neutral", "engagement_score": 484.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hft37m", "title": "Neighbors reported my business. Help? ", "content": "Hey so I run a detailing business on the side and usually my operations are mobile but in the PNW our weather gets bad this season so I recently started accepting clients at my home garage. Everything was fine until a neighbor confronted me saying that he'd report me if i didn't stop because he claimed i was being too loud and \"disrupting the neighborhood\". I didn't actually expect him to do anything and I kind of just laughed it off. Well this morning 2 cops showed up saying they'd received a formal noise complaint and I was basically ordered to stop or get fined. WTF do I do?! I can't run my business without this garage. \n\nEdit- I read my counties code laws beforehand and saw nothing about noise or running operations out of my garage. Basically the police told me I'd get fined every time they were called out. I just really don't understand how this is considered \"disturbing the peace\". \n\nEdit 2-  A lot of people in the comments are asking how I'm making so much noise and it's honestly because my air compressor and vacuum are being used pretty much constantly throughout the day. I'll also add that I live in a town home type complex so the houses are close together so as the garages. ", "author": "Big_bag_chaser", "created_time": "2024-12-16T20:46:46", "url": "https://reddit.com/r/smallbusiness/comments/1hft37m/neighbors_reported_my_business_help/", "upvotes": 122, "comments_count": 412, "sentiment": "neutral", "engagement_score": 946.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfupqf", "title": "Covered Tesla Calls", "content": "Anyone holding CCs on Tesla? Sold some calls cause the premiums are so high right now and i dont mind my shares getting called away (my average cost price is around 160). Still feeling kinda bad cause the stock is pumping. DTE is in Mar25", "author": "F<PERSON>ffyPeng<PERSON>", "created_time": "2024-12-16T21:55:07", "url": "https://reddit.com/r/options/comments/1hfupqf/covered_tesla_calls/", "upvotes": 9, "comments_count": 11, "sentiment": "bullish", "engagement_score": 31.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfw1t2", "title": "Six EV SUVs Were Driven Until They Died. The Winner Was Clear", "content": "", "author": "Tymofiy2", "created_time": "2024-12-16T22:53:16", "url": "https://reddit.com/r/electriccars/comments/1hfw1t2/six_ev_suvs_were_driven_until_they_died_the/", "upvotes": 62, "comments_count": 72, "sentiment": "neutral", "engagement_score": 206.0, "source_subreddit": "electriccars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hfx0r9", "title": "Does a peak power of 4.8kw seem low for 30 panels totaling 24k?", "content": "I just bought a house that has 30 solar panels on the roof totaling 24,000 W combined. But when I monitor it through the app, the highest output I ever see is 4.8 kw.  My previous house had far fewer panels and a peak rate of 6 kW.  All panels appear to be functioning according to the app. ", "author": "bigdi<PERSON><PERSON>", "created_time": "2024-12-16T23:37:37", "url": "https://reddit.com/r/solar/comments/1hfx0r9/does_a_peak_power_of_48kw_seem_low_for_30_panels/", "upvotes": 0, "comments_count": 44, "sentiment": "neutral", "engagement_score": 88.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}]