[{"platform": "reddit", "post_id": "reddit_1ibwr6u", "title": "Has China FSD caught up?", "content": "If BYD has FSD \"V13+\" already in China, what's Tesla's MOAT?  \n  \nWatching this video of BYD's [FSD ](https://youtu.be/Qjl3r3D2tG4?si=D6MxhMtUHGWmdKzl&t=340)in action, I'm shook. Never imagined FSD in China has caught up or surpassed Tesla FSD.  \nJust one intervention at [05:40](https://youtu.be/Qjl3r3D2tG4?si=D6MxhMtUHGWmdKzl&t=340) mark in 30 minute drive with hundreds of scooters and jaywalkers rampant at every turn.\n\nDo I start selling my TSLA shares and looking into Chinese stocks?\n\n\\-\n\nHere's a brief synopsis of the video (ChatGPT)\n\n* **Introduction and Setup**:\n   * The challenge involves testing BYD’s autonomous driving capabilities under extreme conditions in a crowded, rural Chinese city at night, with a mix of people and scooters on the roads.\n   * The test vehicle is the Denza G9 GT, capable of urban autonomous driving but not yet fully updated for parking features.\n* **Initial Observations**:\n   * The car adjusts smoothly to dynamic situations like people walking onto the road, scooters changing lanes unexpectedly, and non-standard traffic patterns.\n   * It handles missing lane markings and unusual left-turn signals well, demonstrating reliable lane-changing and speed adjustments.\n* **Complex Traffic Scenarios**:\n   * Encounters included scooters suddenly appearing, pedestrians jaywalking, and erratically parked vehicles.\n   * The AI adjusts speed, yields to pedestrians, and navigates intersections effectively, though it struggles with areas lacking traffic signals or clear road markings.\n* **Challenges with Local Traffic Norms**:\n   * In some areas, straight and left-turn signals work simultaneously, leading to chaos.\n   * The car successfully handles these situations, adhering to traffic rules while ensuring safety for nearby scooters and pedestrians.\n* **Specific Difficulties**:\n   * In a school zone, the car yielded to crossing students, causing a delay that led to a violation notification for obstructing traffic.\n   * This highlighted differences in local driving expectations and challenges faced by autonomous systems in adhering to nuanced human behaviors.\n* **Performance in Crowded Areas**:\n   * The car safely navigated through congested areas like shopping districts with heavy foot and scooter traffic.\n   * Despite tight spaces and unpredictable movements, the AI avoided collisions and maintained a smooth ride.\n* **Critiques and Reflections**:\n   * Observations on China’s traffic system pointed out inefficiencies like conflicting signals and reckless driving behaviors.\n   * The narrator expressed frustration over receiving a traffic violation for prioritizing pedestrian safety.\n* **Conclusion**:\n   * The test showcased the potential and limitations of the BYD vehicle’s autonomous driving in extreme real-world conditions.\n   * The system’s reliance on LIDAR and its ability to handle chaotic traffic were impressive, but legal and cultural challenges remain significant barriers.\n   * Questions were raised about whether similar autonomous features would be released in other markets like Korea.", "author": "Upset-Apartment1959", "created_time": "2025-01-28T08:03:11", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ibwr6u/has_china_fsd_caught_up/", "upvotes": 19, "comments_count": 84, "sentiment": "bearish", "engagement_score": 187.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibxbfz", "title": "Desperately need help with lead generation", "content": "My business is experiencing a pretty rough time. We're a B2B SaaS provider of automation solutions for small enterprises. Over the last few months, we've been losing MRR quickly due to high monthly churn.\n\nThere are two issues:\n\n1. **Churn:** We're losing our current clients due to (what I believe to be) a significant discrepancy between how our leads understand our service and what we deliver.\n2. **Lead generation:** Our lead gen tactics, like Google Ads and outbound email campaigns, no longer yield the same ROI. We're paying a lot for ads, and our organic channels have plateaued. Basically, our lead quality has plummeted.\n\nOur team is small but competent and operates on a minimal monthly budget. Does anyone know how to improve our situation, or has anyone been in a similar spot?\n\nPlease let me know what you found effective in finding amazing leads.\n\n**Edit:** Thanks for all the help, folks! I tried <PERSON><PERSON>'s free trial, and it's decent if you just need a handful of emails to test out. It pulls verified emails from LinkedIn Sales Navigator but the free credits run out fast. ", "author": "Forsaken-Spell8853", "created_time": "2025-01-28T08:47:58", "url": "https://reddit.com/r/DigitalMarketing/comments/1ibxbfz/desperately_need_help_with_lead_generation/", "upvotes": 82, "comments_count": 49, "sentiment": "bullish", "engagement_score": 180.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibz1wv", "title": "Poland urges Tesla boycott after <PERSON><PERSON>’s call to ‘move past’ Nazi guilt", "content": "", "author": "doopityWoop22", "created_time": "2025-01-28T11:00:25", "url": "https://reddit.com/r/worldnews/comments/1ibz1wv/poland_urges_tesla_boycott_after_musks_call_to/", "upvotes": 83677, "comments_count": 1547, "sentiment": "neutral", "engagement_score": 86771.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ibzsxa", "title": "[D] DeepSeek’s $5.6M Training Cost: A Misleading Benchmark for AI Development?", "content": "Fellow ML enthusiasts,\n\nDeepSeek’s recent announcement of a $5.6 million training cost for their DeepSeek-V3 model has sparked significant interest in the AI community. While this figure represents an impressive engineering feat and a potential step towards more accessible AI development, I believe we need to critically examine this number and its implications.\n\nThe $5.6M Figure: What It Represents\n\n* Final training run cost for DeepSeek-V3\n* Based on 2,048 H800 GPUs over two months\n* Processed 14.8 trillion tokens\n* Assumed GPU rental price of $2 per hour\n\n\nWhat’s Missing from This Cost?\n\n1. R&D Expenses: Previous research, failed experiments, and precursor models\n2. Data Costs: Acquisition and preparation of the training dataset\n3. Personnel: Salaries for the research and engineering team\n4. Infrastructure: Electricity, cooling, and maintenance\n5. Hardware: Actual cost of GPUs (potentially hundreds of millions)\n\n\nThe Bigger Picture\n\nSome analysts estimate the total R&D budget for DeepSeek-V3 could be around $100 million, with more conservative estimates ranging from $500 million to $1 billion per year for DeepSeek’s operations.\n\n\nQuestions for discussion \n\n1. How should we benchmark AI development costs to provide a more accurate representation of the resources required?\n2. What are the implications of focusing solely on the final training run cost?\n3. How does this $5.6M figure compare to the total investment needed to reach this point in AI development?\n4. What are the potential risks of underestimating the true cost of AI research and development?\n\n\nWhile we should celebrate the engineering and scientific breakthroughs that DeepSeek has achieved, as well as their contributions to the open-source community, is the focus on this $5.6M figure the right way to benchmark progress in AI development?\n\n\nI’m eager to hear your thoughts and insights on this matter. Let’s have a constructive discussion about how we can better understand and communicate the true costs of pushing the boundaries of AI technology.", "author": "BubblyOption7980", "created_time": "2025-01-28T11:49:54", "url": "https://reddit.com/r/MachineLearning/comments/1ibzsxa/d_deepseeks_56m_training_cost_a_misleading/", "upvotes": 0, "comments_count": 59, "sentiment": "neutral", "engagement_score": 118.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ic0kp2", "title": "Trump announces chip tariffs up to 100%", "content": "https://en.rti.org.tw/news/view/id/2012378\n\n> U.S. President <PERSON> has announced his intent to impose import tariffs as high as 100% on computer chips and semiconductors. In a speech at the House GOP Issues Conference in Miami on Monday, he also suggested he would remove <PERSON>’s program of paying subsidies to chip makers like Intel or TSMC to build fabrication plants in the U.S.\n\nDoes this mean puts on SPY?", "author": "Genevieves_bitch", "created_time": "2025-01-28T12:36:13", "url": "https://reddit.com/r/investing/comments/1ic0kp2/trump_announces_chip_tariffs_up_to_100/", "upvotes": 4591, "comments_count": 259, "sentiment": "bearish", "engagement_score": 5109.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ic1jer", "title": "Norway is set to become the first country to fully transition to electric vehicles", "content": "", "author": "cnbc_official", "created_time": "2025-01-28T13:28:08", "url": "https://reddit.com/r/environment/comments/1ic1jer/norway_is_set_to_become_the_first_country_to/", "upvotes": 2029, "comments_count": 54, "sentiment": "neutral", "engagement_score": 2137.0, "source_subreddit": "environment", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ic2lgt", "title": "<PERSON><PERSON> Musk Tried to Pressure Norway’s Wealth Fund—A Major Tesla Shareholder—to Approve His Compensation Package", "content": "[https://e24.no/boers-og-finans/i/jQM4bL/elon-musk-to-nicolai-tangen-did-you-send-my-text-messages-to-the-press](https://e24.no/boers-og-finans/i/jQM4bL/elon-musk-to-nicolai-tangen-did-you-send-my-text-messages-to-the-press)\n\n  \nRecently disclosed text messages reveal that <PERSON><PERSON> tried to sway Norway’s massive sovereign wealth fund—commonly known as the Oil Fund—to support his controversial Tesla pay package. The fund, which manages assets of around USD 1.8 trillion and holds a significant stake in Tesla, voted against <PERSON><PERSON>’s compensation proposal, prompting him to admonish CEO <PERSON><PERSON> not to request any “favors” until making amends.\n\nIn their exchange, <PERSON><PERSON> also accused <PERSON><PERSON> of leaking the texts to the media, after Norwegian outlets reported on his canceled dinner appearance at the Oil Fund’s investment conference. <PERSON><PERSON> countered that Norway’s strict transparency laws, akin to the U.S. Freedom of Information Act, required partial disclosure, including confirmation that <PERSON><PERSON> would not attend.\n\nInitially, the Oil Fund classified much of the conversation as private and withheld it. However, in the spirit of transparency, they eventually released all texts. The incident highlights the tension between <PERSON><PERSON>’s calls for discretion and the Norwegian Oil Fund’s legal obligations to remain open. While emphasizing a positive relationship with Tesla as a critical investment, the fund has reiterated its opposition to Musk’s pay plan.", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-28T14:20:45", "url": "https://reddit.com/r/stocks/comments/1ic2lgt/elon_musk_tried_to_pressure_norways_wealth_funda/", "upvotes": 3384, "comments_count": 186, "sentiment": "bullish", "engagement_score": 3756.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ic66i4", "title": "I make around 100k/month barely working, not sure of next steps", "content": "I don't want to get into details but one of my businesses brings in about 100k/month in service. I work about 10 hours a week and have a FT employee that works about the same.  90% of our work is remote but the customers are local as 10% is onsite.   One issue is it's 24/7/365 on-call so I feel burned out.  Another issue is customer acquisition is very hard and likely would cost over a year of revenue.\n\nI used to have multiple employees and everything but worked on automating many tasks so there's not much workload.   I also have a fancy office and all the setup to handle over a dozen more employees, with a ton of remote ones.  \n\nRight now I make as much as I can spend and don't really need more money so been coasting.  I hate the work but it keeps me busy enough.   I could easily work remotely and have the employee do the local stuff. I'm not really sure if it makes sense to grow or start another project or what.  I run a few dozen other businesses but they don't make the free money and are more work", "author": "88captain88", "created_time": "2025-01-28T16:57:47", "url": "https://reddit.com/r/Entrepreneur/comments/1ic66i4/i_make_around_100kmonth_barely_working_not_sure/", "upvotes": 0, "comments_count": 182, "sentiment": "neutral", "engagement_score": 364.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ic8rn8", "title": "As someone who hires digital marketing roles...", "content": "The quality of your resume matters. I am the director of digital marketing, marketing analytics, and marketing operations for a mid-size company. I hire a hand-full of people every year and go through literally thousands of resumes per position. Our positions are fully remote and potential candidates can be anywhere in the US or Canada so we received a lot of applicants. The current digital marketing manager role I am hiring pays up to $155K and I have received 2172 resumes for the position. Of those, I have moved 13 candidates through to my hiring manager for an initial phone interview.\n\nFor context, for those familiar with it, we use Greenhouse as our HR platform. I open and look at every single resume that comes through. I can tell in about 10 seconds if someone is a hard pass for me. It doesn't mean that they might not be qualified, it just means the resume is so underwhelming that I am moving on to the next one. \n\nI understand this is my personal perspective and others will vary. That said, here is what I am looking for:\n\n* Your resume needs to stand out! I am hiring for marketing positions. If you cannot market yourself, how can I trust you managing a $5m budget?\n* If you are not good at building a resume, go to Etsy and pay $20 for a well designed resume that is aesthetically pleasing and is formatted in a way that you can highlight your experience.\n* I know not everyone agrees but use (some) color in your resume. When I am going through 30 resumes and I am getting hit with all black text only brick of text resumes one after another, they rarely catch my eye. Even better, match the color scheme (or color) to include the company's color pallet. It's a subconscious trick that will resonate with people who review a lot of resumes.\n* Keep it under 2 pages. I don't care how much experience you have, I am only looking at your last couple of positions as my focus.\n* Do not highlight your freelance experience as the focus of your resume. Since I am hiring a fully remote role, I will be concerned that you are going to be working two gigs if your resume focus is freelance work. You can include it, but don't make that a focus of your work history.\n* Absolutely list all of the platforms and tools that you have experience with. I always look at those when they are listed. If you list Google Ads, Meta Ads, Bing Ads, Marketo, Salesforce, Tableau, SEMRush, and other platforms that we use, I am going to give your resume more attention.\n* Do the small things. If I am hiring for a digital marketing manager position, indicate that you are looking for a digital marketing manager role. Don't say you are a \"digital expert\" or that you are seeking a \"senior digital role\". I want someone who identifies as seeking the role for which I am hiring.\n* If you include a cover letter, make sure it is personalized for the company and written specifically to communicate why this particular role is interesting to you and why our company seems like a good fit for you. If you are sending generic cover letters, you might as well not send it.\n* Imbed a link to your LinkedIn profile. Imbed a link to your portfolio if you have one. It's a small thing but I am more likely to look at them if I don't have to copy and paste links into my browser.\n* Lastly, for the love of all that is holy, do not write your resume or cover letter in third person. I will immediately think you are a narcissistic lunatic and hit the reject button without reading another word.\n\nHopefully this is helpful for someone. I go through a lot of resumes and most of of them are bad. If you are sending out dozens (or hundreds) of resumes and not getting any hits, change your resume. It can be as simple as downloading a resume from Etsy and sending something out with a little character. Market yourself. Happy hunting!", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-01-28T18:42:13", "url": "https://reddit.com/r/DigitalMarketing/comments/1ic8rn8/as_someone_who_hires_digital_marketing_roles/", "upvotes": 152, "comments_count": 73, "sentiment": "bullish", "engagement_score": 298.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1icczqe", "title": "Marketing guy claims he needs Primary Ownership of Google Business listing. Why?", "content": "We've already given an ownership role to him, and he now claims he needs Primary Ownership to do his \"SEO magic\". I've only heard this second hand so I don't know the exact explanation. The quote is not his, just my snarky summary. \n\nIs there any possible reason he would need this? It seems very sketch and everything I've read says even the Manager role has all the same permissions other than user management. He has ownership already, why would he need more? And if this is malicious, what would be his angle?", "author": "DuckenHahnchen", "created_time": "2025-01-28T21:33:31", "url": "https://reddit.com/r/marketing/comments/1icczqe/marketing_guy_claims_he_needs_primary_ownership/", "upvotes": 19, "comments_count": 33, "sentiment": "neutral", "engagement_score": 85.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1icd3tr", "title": "Google Ruined My Wife's Pixel 4a, Offered Compensation, and Then Made It Useless… Here's the Story", "content": "Hey r/GooglePixel,\n\nI’m not super Reddit-savvy, but I don’t know where else to go. I need to share my wife's frustrating experience with Google's recent Pixel 4a \"Battery Performance\" update. You know, the one where Google forced an update that bricked her perfectly good phone and then gave her $100 credit as \"compensation\"? Well, buckle up, because it gets worse.\n\nHere’s what happened:\n\n1. The Issue: Like many others, my wife's Pixel 4a was essentially ruined after Google's mandatory forced update. Google’s handling of this update has created waves of frustration across its user base. Battery life? Toast. Usability? Gone. Her once-reliable phone was rendered useless. \n2. The “Fix”: Google offered $100 store credit to use toward a new Pixel device. Fine, we thought—not ideal, but at least it's something, right?\n3. The Catch: We went to the Google Store to buy a Pixel 8a that was already on sale. But when we tried to redeem the $100 credit, the system wouldn’t let us… because the phone was \"already discounted.\"\n\nLet me say that again: Google gave my wife $100 credit because they broke her phone… but won’t let her use it to buy the replacement device because it’s on sale. The sale price has nothing to do with the situation—this is supposed to be compensation, not a coupon!\n\n1. Google’s Support Response: We reached out to customer support (full transcript here: [https://imgur.com/a/w3uBueZ](https://imgur.com/a/w3uBueZ) ). We explained that this isn’t a typical promo code—it’s a voucher to make up for their forced update ruining her phone. After being bounced around and asked to \"try incognito mode\" (seriously?), they ultimately said they couldn’t help us. When we attempted to further explain the situation, they abruptly disconnected the chat without resolving anything.\n\nThe excuse? \"Promo codes cannot be combined with other promotions.\" But nowhere in the terms does it say that this credit can’t be used on a sale item. Even worse, others have apparently been able to use their vouchers on discounted phones. Why the inconsistency?\n\n1. Adding Insult to Injury: If she had chosen the $50 cash option instead of the $100 store credit, she wouldn’t have been restricted like this. The $100 store credit is essentially Google saying, \"We’re sorry for ruining your phone, but please consider buying a Pixel again.\" Yet when we try to, they make it as difficult as possible.\n2. Even Worse: To add fuel to the fire, we’re now seeing reports from others who, after being refused the ability to use their store credit, are also being denied the option to go back and choose the $50 cash alternative. So not only is the credit worthless in many cases, but once you’ve picked it, you’re stuck with it.\n\nMy Takeaway: This \"compensation\" is essentially worthless if my wife can’t use it to buy the replacement phone it’s meant for. Google’s update broke her phone, and now they’re nickel-and-diming us on their so-called solution. It’s insulting.\n\nHere’s the simple solution Google should offer:\n\n* Option 1: Let her uninstall the update and return her phone to its previous, fully functional state.\n* Option 2: If that’s not possible, honor your commitment and compensate her properly for her broken phone—whether that means unrestricted store credit, cash, or a direct replacement.\n\nI wanted to share this here because:\n\n* Awareness: If you’re dealing with the same issue, you’re not alone.\n* Advice: If anyone’s successfully navigated this nonsense, I’d love to hear how you did it.\n* Accountability: Google needs to know this isn’t okay. Their lack of support is unacceptable, and they need to do better.\n\nLet’s make some noise. If you’ve dealt with this or have thoughts, drop them in the comments. Google, if you’re reading this: Fix this mess.\n\nWe’ve fallen in love with Pixel devices over the years because they’ve been so reliable. We’ve purchased several Pixels, trusting Google to deliver great products. But now, Google has destroyed that trust with how they’re handling this issue. It’s heartbreaking to see a brand we believed in let its loyal customers down.", "author": "Kunzite_", "created_time": "2025-01-28T21:38:14", "url": "https://reddit.com/r/GooglePixel/comments/1icd3tr/google_ruined_my_wifes_pixel_4a_offered/", "upvotes": 719, "comments_count": 183, "sentiment": "bullish", "engagement_score": 1085.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "TSLA"}]