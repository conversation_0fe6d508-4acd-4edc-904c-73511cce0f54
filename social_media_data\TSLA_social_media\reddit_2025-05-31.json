[{"platform": "reddit", "post_id": "reddit_1kzp0w5", "title": "Tesla faces collapsing sales in Canada's Québec province, with new registrations tumbling 85%", "content": "", "author": "Adventurous_Row3305", "created_time": "2025-05-31T05:00:54", "url": "https://reddit.com/r/technology/comments/1kzp0w5/tesla_faces_collapsing_sales_in_canadas_québec/", "upvotes": 16972, "comments_count": 569, "sentiment": "neutral", "engagement_score": 18110.0, "source_subreddit": "technology", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kzuzqs", "title": "Tesla Or Not?", "content": "There is a lot of negativity about string inverters. Trinity solar also said I could get Enphase for the same $2.90 PPW in NJ. There’s mix reviews about Trinity. Any reason why I shouldn’t use them?\n\n\n\n(25) Enphase Energy REC 460w pure Rx\n(25)Enphase Energy IQ8X-80-M-US\n1 X Enphase IQ Combiner 5x-IQ-AM-1-240V-5\nEcofasten Mount Systems \n\n\n24 QCell 410’s \n1 X 11.4kW Tesla Powerwall 3\n\n(You’re gonna love the functionality of your Tesla automobile and your Tesla battery.  The battery works as an inverter and backup storage)\n\n$28,540 solar (2.9PPW)\n$15,740 battery (minimum price)\n\n$44,280 total\n-$13,284 FTC\n\n$30,996\n-$15,300 SRECS \n\n$15,696 after all incentives.", "author": "Inner-Chemistry2576", "created_time": "2025-05-31T11:36:30", "url": "https://reddit.com/r/solar/comments/1kzuzqs/tesla_or_not/", "upvotes": 1, "comments_count": 28, "sentiment": "neutral", "engagement_score": 57.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kzzkr8", "title": "It’s <PERSON><PERSON>’s World. We’re All Just Riding in It: WSJ", "content": "https://www.wsj.com/tech/waymo-cars-self-driving-robotaxi-tesla-uber-0777f570?\n\nAnd then the archived link for paywall: https://archive.md/8hcLS\n\nUnless you live in one of the few cities where you can hail a ride from Waymo, which is owned by Google’s parent company, Alphabet, it’s almost impossible to appreciate just how quickly their streets have been invaded by autonomous vehicles. \n\nWaymo was doing 10,000 paid rides a week in August 2023. By May 2024, that number of trips in cars without a driver was up to 50,000. In August, it hit 100,000. Now it’s already more than 250,000. \nAfter pulling ahead in the race for robotaxi supremacy, Waymo has started pulling away. \n\nIf you study the Waymo data, you can see that curve taking shape. \nIt cracked a million total paid rides in late 2023. By the end of 2024, it reached five million. We’re not even halfway through 2025 and it has already crossed a cumulative 10 million. At this rate, Waymo is on track to double again and blow past 20 million fully autonomous trips by the end of the year.\n“This is what exponential scaling looks like,” said <PERSON><PERSON><PERSON>, Waymo’s co-chief executive, at Google’s recent developer conference. \n", "author": "FarrisAT", "created_time": "2025-05-31T15:20:09", "url": "https://reddit.com/r/singularity/comments/1kzzkr8/its_waymos_world_were_all_just_riding_in_it_wsj/", "upvotes": 317, "comments_count": 177, "sentiment": "neutral", "engagement_score": 671.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1l00sma", "title": "What's the technical argument that Tesla will face fewer barriers to scaling than Argo, Cruise, Motional, and early-stage Waymo did?", "content": "I'm happy to see Tesla switching their engineers to the passenger seat in advance of the June 12th launch.  But I'm still confused about the optimism about Tesla's trajectory. Specifically, today on the Road to Autonomy Podcast, the hosts seemed to predict that Tesla would have a bigger ODD in Austin than Waymo by the end of the year.\n\nI'm very much struggling to see <PERSON><PERSON>'s path here. When you're starting off with 1:1 remote backup operations, avoiding busier intersections, and a previously untried method of going no-driver (i.e. camera-only), that doesn't infuse confidence that you can scale past the market leader in terms of roads covered or number of cars, quickly.\n\nThe typical counter-argument I hear is that the large amount of data from FSD supervised, combined with AI tech, will, in essence, slingshot reliability. As a matter of first principles, I see how that could be a legitimate technical prediction.  However, there are three big problems. First, this argument has been made in one form or another since at least 2019, and just now/next month we have reached a driverless launch. (Some slingshot--took 6+ years to even start.) Second, Waymo has largely closed the data gap-- 300K driverless miles a day is a lot of data to use to improve the model. Finally, and most importantly, I don't see evidence that large data combined with AI will solve all  the of specific problems other companies have had in switching to driverless.\n\nAI and data doesn't stop lag time and 5G dead zones, perception problems common in early driverless tests, vehicles getting stuck, or the other issues we have seen. Indeed, we know there are unsolved issues, otherwise Tesla wouldn't need to have almost a Chandler, AZ-like initial launch.  Plus Tesla is trying this without LiDAR, which may create other issues, such as insufficient redundancy or problems akin to what prompts interventions with FSD every few hundred miles.\n\nIn fact, if anyone is primed to expand in Austin, it is Waymo-- their Austin geofence is the smallest of their five and Uber is anxious to show autonomy growth, so it is surely asking for that geofence to expand. And I see no technical challenges to doing that, given what Waymo has already done in other markets.\n\nWhat am I missing?", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-31T16:11:37", "url": "https://reddit.com/r/SelfDrivingCars/comments/1l00sma/whats_the_technical_argument_that_tesla_will_face/", "upvotes": 69, "comments_count": 307, "sentiment": "bullish", "engagement_score": 683.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1l054yl", "title": "Remember when brand CPCs were cheap?", "content": "Rant Incoming: Remember when there was less automation and brand clicks could be bought for 0.30$ Having everyone conqesting each over by default was the biggest downside to fully automated strategies. What are you doing to control brand spend? What strategies worked for you?", "author": "s_hecking", "created_time": "2025-05-31T19:17:05", "url": "https://reddit.com/r/adwords/comments/1l054yl/remember_when_brand_cpcs_were_cheap/", "upvotes": 4, "comments_count": 12, "sentiment": "neutral", "engagement_score": 28.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1l06trc", "title": "Overlayed crash data from the Tesla Model 3 accident.", "content": "When this was first posted it was a witch hunt against FSD and everyone seemed to assume it was the FSDs fault. \n\nLooking at the crash report it’s clear that the driver disengaged FSD and caused the crash. Just curious what everyone here thinks. ", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-31T20:32:11", "url": "https://reddit.com/r/SelfDrivingCars/comments/1l06trc/overlayed_crash_data_from_the_tesla_model_3/", "upvotes": 1326, "comments_count": 631, "sentiment": "bearish", "engagement_score": 2588.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1l07p9p", "title": "FSD likely saves a life", "content": "", "author": "FriendFun7876", "created_time": "2025-05-31T21:10:58", "url": "https://reddit.com/r/SelfDrivingCars/comments/1l07p9p/fsd_likely_saves_a_life/", "upvotes": 0, "comments_count": 54, "sentiment": "neutral", "engagement_score": 108.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1l07rk1", "title": "Tesla powerwall 3. Timeline?", "content": "If I go with <PERSON><PERSON> to do the install \n\nWhat’s everyone’s best guesstimate If I start the process now. And <PERSON><PERSON> sends a person to come to my house in two weeks to do a site survey. Realistically how soon until they complete the install of the following equipment. \n\n(1) Tesla powerwall 3 \n\n\n(3) Tesla powerwall 3 expansion packs. \n\nAnd all necessary hardware for the complete install ", "author": "reborn56", "created_time": "2025-05-31T21:13:55", "url": "https://reddit.com/r/solar/comments/1l07rk1/tesla_powerwall_3_timeline/", "upvotes": 0, "comments_count": 10, "sentiment": "neutral", "engagement_score": 20.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}]