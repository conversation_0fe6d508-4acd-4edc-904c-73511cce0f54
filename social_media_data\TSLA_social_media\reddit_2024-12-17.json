[{"platform": "reddit", "post_id": "reddit_1hfyym1", "title": "Timeline gone", "content": "I got the notification last week on my secondary phone just as I was about to use maps and I was in a hurry. No confirmation, no warning, now I'm left with the default 3 months instead of over ten years. \nI used it to track holidays, trips, unmarked locations to return to...\nI've only just realised the extent of how gone everything is, I was still hoping to be able to restore it...\nI should be asleep right now, work starts in four hours and I'm tossing and turning in bed. I'm absolutely devastated that this had been taken from me by shitty communication and an absolute no-can-do attitude by support staff. \nIn fact I'm not devastated, I'm fucking livid.", "author": "<PERSON><PERSON><PERSON>", "created_time": "2024-12-17T01:09:12", "url": "https://reddit.com/r/GoogleMaps/comments/1hfyym1/timeline_gone/", "upvotes": 29, "comments_count": 10, "sentiment": "neutral", "engagement_score": 49.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg0rh6", "title": "Reminder: Don't leave an HSA to your kids", "content": "Link to an article summarizing a reminder from tax experts <PERSON> and <PERSON> about how HSAs are treated when you pass.\n\nhttps://www.thinkadvisor.com/2024/12/13/slott-levine-heres-what-happens-to-the-hsa-when-a-client-dies-/\n\nBasically, if it's your spouse, that's great. They can keep it or roll it over into their own HSA.\n\nEverybody else, the entire balance is gross income that year. $200,000 left? That's income. $3,000 left? That's income.\n\nI wouldn't just plan to leave it to the surviving spouse without additional thought. Doing so assumes they can both change the beneficiary to the kids and then spend it down before it becomes a problem. If you're the \"accountant\" in the marriage, is your spouse really going to fix this issue?\n\nInstead, I suggest you have a plan for how the HSA will be mostly depleted--maybe down to 50,000 or less in 2024 dollars--by the time you're 70.\n\nThe tax treatment of HSAs contrasts sharply with IRAs and other traditional retirement plans, which allow the income to be spread out over 10 years (previously life expectancy, and before that 5 years). It also contrasts with taxable brokerage accounts, which benefit from a step-up in basis so that heirs can sell for very little taxable gain.\n\nThis issue is especially relevant for FIRE folks who are going to build a sizable HSA balance, especially those using the decades-of-receipts method.", "author": "financeking90", "created_time": "2024-12-17T02:42:45", "url": "https://reddit.com/r/financialindependence/comments/1hg0rh6/reminder_dont_leave_an_hsa_to_your_kids/", "upvotes": 907, "comments_count": 177, "sentiment": "bearish", "engagement_score": 1261.0, "source_subreddit": "financialindependence", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg179q", "title": "Everything here is 100% generated w/ Google Veo 2", "content": "", "author": "SharpCartographer831", "created_time": "2024-12-17T03:05:33", "url": "https://reddit.com/r/singularity/comments/1hg179q/everything_here_is_100_generated_w_google_veo_2/", "upvotes": 1393, "comments_count": 242, "sentiment": "neutral", "engagement_score": 1877.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg1upa", "title": "why are my WMT outperforming my MSFT shares", "content": "I went into my brokerage app today and saw, much to my surprise, that my WMT shares are actually outperforming my MSFT shares by about 0.6% (small IK but it got me thinking). I bought them around the same time, across 2020-2024 (mostly in 2020 for both). \n\nLooking at the stocks outside of the context of when I bought them, MSFT is outperforming 103% vs 97% but this still begs the question of why is MSFT only outperforming by that little when MSFT is 🚀 and WMT is 💩right? \n\nSo I dug into the numbers a bit (super simple stuff)\n\n\\*\\*\\*Don't know why my pictures can't be added to the post but here's an Imgur link [https://imgur.com/a/4PbHxI7](https://imgur.com/a/4PbHxI7) \n\nTop Line Growth\n\nMSFT: 49%\n\nWMT: 16%\n\n\n\nBottom Line Growth\n\nMSFT: 63%\n\nWMT: 12%\n\n\n\nDividend Growth\n\nMSFT: 33%\n\nWMT: 5.5%\n\n  \nPE\n\nMSFT: 37\n\nWMT:  39 (wtf)\n\n  \nConclusion: WMT price growth seems crazy. Either they're overvalued or MSFT is undervalued. I'm thinking WMT is overvalued and I should sell some. \n\n What's everyone else's thoughts?\n\nPositions:\n\nWMT: 6 shares (+IDK in ETFs because I'm only seeing top 25 parsed results)\n\nMSFT: 5 shares (+20k in ETFs)\n\n", "author": "PotatoTrader1", "created_time": "2024-12-17T03:41:04", "url": "https://reddit.com/r/stocks/comments/1hg1upa/why_are_my_wmt_outperforming_my_msft_shares/", "upvotes": 0, "comments_count": 27, "sentiment": "bullish", "engagement_score": 54.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg34fu", "title": "Data analytics", "content": "Hey! I want to develop skills essential for data analytics, what skills I should start working on? Let me know best platform for that", "author": "Alive-Rip5404", "created_time": "2024-12-17T04:54:16", "url": "https://reddit.com/r/analytics/comments/1hg34fu/data_analytics/", "upvotes": 0, "comments_count": 13, "sentiment": "neutral", "engagement_score": 26.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg4mfx", "title": "TIFU by Quitting My Job to Go Full Indie", "content": "Not a typical TIFU, but here we go.\n\nI decided to quit my full-time job, to go full into my projects. My side projects, Directify and Larafast, have been my secret obsession for months. Late nights, weekends, every spare moment I could find—I've been pouring my heart into these products.\n\nToday, I made the call. No more full-time jobs.\n\nSix months of runway. That's my safety net. Six months to turn these passion projects into something real. No more juggling a day job and my dreams. This is it.\n\nSome might call it risky. I call it finally listening to that voice inside that's been screaming \"GO FOR IT\" for way too long.\n\nDirectify. Larafast. They're more than just side projects now. They're my entire world.\n\nWish me luck, Reddit. 🚀\n\nEdit: Holy crap, this blew up. Thanks for the support, kind strangers!", "author": "ka<PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-17T06:27:56", "url": "https://reddit.com/r/Entrepreneur/comments/1hg4mfx/tifu_by_quitting_my_job_to_go_full_indie/", "upvotes": 0, "comments_count": 47, "sentiment": "bullish", "engagement_score": 94.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg553u", "title": "Can I do web development on a low-spec work PC using a USB setup?", "content": "\nSo, I have a lot of free time at my current job (receptionist). Honestly, the pay is great, and since I’m still in college, I feel really lucky to have a job that’s both chill and well-paid. This is pretty rare in my country (Argentina).\n\nI do web development, but I haven’t been able to find anything in the field yet. I was thinking of using my free time at work to code, but the work PC has really low specs, and it’s even worse with the job’s programs running in the background.\n\nThe specs are:\n\t•\tIntel Core i3 7020U (2.3GHz)\n\t•\t4GB RAM (usually at 90% usage due to work programs).\n\nI was wondering if it’s possible to set up everything on a USB drive using portable versions of the tools I need. Do you think I could work on some MERN stack projects or any kind of web development with this setup?", "author": "<PERSON><PERSON>", "created_time": "2024-12-17T07:04:25", "url": "https://reddit.com/r/webdev/comments/1hg553u/can_i_do_web_development_on_a_lowspec_work_pc/", "upvotes": 0, "comments_count": 45, "sentiment": "neutral", "engagement_score": 90.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg5wmw", "title": "How Do You Keep Cloud Migration Costs Under Control and Avoid Unexpected Expenses?", "content": "One of the biggest concerns when migrating to the cloud is managing costs. For those who’ve gone through the process, what strategies did you use to keep costs under control? It’s common for businesses to face unexpected expenses, so I’ve seen companies focus on choosing the right mix of services, optimizing resource usage, and leveraging cost management tools during migration. How do you ensure your cloud migration stays within budget and doesn’t result in surprise costs later?", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2024-12-17T08:01:10", "url": "https://reddit.com/r/cloudcomputing/comments/1hg5wmw/how_do_you_keep_cloud_migration_costs_under/", "upvotes": 1, "comments_count": 4, "sentiment": "neutral", "engagement_score": 9.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg5yop", "title": "Advice for learning hands-on Meta/Google Ads management while managing a £10K/month budget", "content": "Hi Everyone! I’m a freelance consultant with 10+ years in eCommerce and digital marketing based in the UK, primarily focused on strategy, project management, and KPIs. I recently finished a contract for a brand I love as Ecommerce manager. Now, they need me to directly manage and execute their Meta and Google Ads campaigns on a tight £10K/month budget so doable, considering we operate in the Luxury fashion space.\n\nWhile I have a strong understanding of Meta and Google Ads from a strategic perspective (working alongside agencies and freelancers), I lack deep operational experience. I need to quickly learn the specialist skills:\n\n* Launching and optimizing campaigns.\n* Building/editing audiences.\n* Testing new creatives and strategies.\n\nMy goal is to deliver results as both a hands-on specialist and strategic advisor.\n\nWhat’s the best way to upskill quickly on execution tasks? Are there tools, resources, or workflows you’d recommend to get operationally strong in Meta and Google Ads?\n\nAny advice would be hugely appreciated, thanks!", "author": "Previous_Delivery_41", "created_time": "2024-12-17T08:05:19", "url": "https://reddit.com/r/DigitalMarketing/comments/1hg5yop/advice_for_learning_handson_metagoogle_ads/", "upvotes": 23, "comments_count": 26, "sentiment": "bullish", "engagement_score": 75.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hg6kb0", "title": "Question for Data Analytics and Industry Professionals RE: \"The User Experience\" (From: a user)", "content": "So, serious question - do any of you work, or know anyone who works on UIs/Databases/User Experience designs specifically for one of the various streaming giants: Netflix, MAX, Prime, Disney+, Hulu?  \n\n\nI apologize first-hand if this sounds overly-angry, but this problem has gotten EXPONENTIALLY WORSE over the past 7 years or so, in particular and will only be exacerbated by B<PERSON>LLSHIT AI TRENDS.   \n\n\nBecause I HAVE A NITPICK.   \n\n\nWhy do you make your service impossible to index properly to the average user? Occasionally, I may be able to sort a genre or sub-genre alphabetically, such as with MAX; however, the default sort is ALWAYS a smattered, mish-mash of \"here's shit you're likely to click on\" or \"more recent/promoted\" results...JUST LIKE THAT HORSE-SHIT THAT GOOGLE'S been pulling for the past five years.   \n  \nLook, I just want to know exactly WHAT IS THERE, and I DO NOT want it appearing in multiple categories thereby increasing the time it takes to scroll through each category, NOR do I want a NEVER ENDING category based on some bullshit, algorithm that tries to generate results on the fly via applied \\[TAGS\\], popularity, or god KNOWS however other \"hidden metrics,\" that we as users cannot see, nor control. (as is the current trend)  \n  \nFrom a design/user-experience standpoint, IT'S A GODDAMNED NIGHTMARE. IT'S FUCKING STUPID.    \n  \nI realize this may save the labor of having to curate and sanitize your records, BUT IT IS MASSIVELY UNFRIENDLY TO YOUR ENTIRE USERBASE.   \n  \nLook, here's what I'm talking about. I walk into a Library Today, or a Blockbuster Video a decade ago, titles aren't spontaneously being shuffled from one shelf to the other, aside from NEW RELEASE to OLD SHIT. The STATIC LOCATION of said item helps ME, the USER, keep track of what's available and what's no longer available. It makes BROWSINGS a MILLION TIMES EASIER, not to mention more satisfying.   \n  \nThis is WHY people LOVED GOING TO THE STORE. The sort MADE SENSE, instead of whatever logarithmic HORSE SHIT you software developers are fucking with today.   \n  \nAnd it's not just limited to the Streaming services. This is what Amazon does, this is what STEAM does, hell - this is what FACEBOOK has been doing with your posts, instead of just giving a \"TRULY\" chronological timeline of your contacts.   \n  \nI   \nDO  \nNOT  \nNEED/WANT    \nYOU   \nMAKING   \nDECISIONS   \nFOR   \nME   \n  \nFUCKING FIX IT. The amount of autonomy that's been STOLEN from users by the modern data informatics and analytics paradigm because of current data trends is FUCKING CRIMINAL.   \n  \nIt intentionally obfuscates \"what is actually going on\" to ANYONE who doesn't have direct access to the individual data-records, and is ethically OPENLY HOSTILE TO THE END USER.   \n  \nIn other words, YOU NEED TO MAKE IT POSSIBLE FOR ME (THE USER and YOUR CUSTOMER) TO DO A \"REAL\" not \"LOGARITHMIC\" search query of your records.   ", "author": "divine_shadow", "created_time": "2024-12-17T08:53:09", "url": "https://reddit.com/r/analytics/comments/1hg6kb0/question_for_data_analytics_and_industry/", "upvotes": 2, "comments_count": 11, "sentiment": "bullish", "engagement_score": 24.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hga9md", "title": "UK’s Bold Strategy to Revolutionize Grid Connections for Data Centers – Powering Clean Energy Growth by 2030 ", "content": "", "author": "viral_pinktastic", "created_time": "2024-12-17T13:09:51", "url": "https://reddit.com/r/CleanEnergy/comments/1hga9md/uks_bold_strategy_to_revolutionize_grid/", "upvotes": 1, "comments_count": 0, "sentiment": "bullish", "engagement_score": 1.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgcoit", "title": "As an experienced data analyst, what are some of your best practices? ", "content": "Over the years of working in this field, what are some of the best practices (1) you think every data analyst should observe, and (2) you would have done in the beginning of your career in your first work (if you could go back in time)? ", "author": "Arethereason26", "created_time": "2024-12-17T15:10:14", "url": "https://reddit.com/r/analytics/comments/1hgcoit/as_an_experienced_data_analyst_what_are_some_of/", "upvotes": 112, "comments_count": 43, "sentiment": "neutral", "engagement_score": 198.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgev47", "title": "Debunking arguments against biofuels ", "content": "The transport sector is a major contributor to climate change. We need to replace fossil fuels with carbon neutral energy sources in the transport sector to achieve net zero. Biofuels are the ideal way to decarbonize heavy vehicles. Drop-in biofuels are the kind of biofuels which should be used to power heavy vehicles because they are chemically identical and are comparable with existing infrastructure. \n\nAll the arguments against biofuels are proven invalid by science \n\n\\- Using residual biomass as the production feedstock for drop-in biofuels and biochar will eliminate the need for new farmland or repurposing existing farmland \n\n\\- Hundreds of millions of tons of residual biomass are produced every year by agriculture and  forestry \n\n\\- Residual biomass is produced on existing farmland and by existing forestry operations \n\n\\- The thermochemical conversion processes that can co-convert residual biomass into drop-in biofuels and biochar can (and should) be self powered \n\n\\- A fraction of the products produced by the thermochemical production process can be combusted to produce the energy for the process \n\n\\- A fraction of the feedstock biomass can be combusted in the reaction chamber to produce the energy for the process \n\n\\- Transportation of residual biomass is not an issue \n\n\\- Pyrolysis and hydrothermal liquefaction (HTL) plants are modular so that they can be located in close proximity to sources of residual biomass \n\n\\- Residual biomass can be torrified (heated at low temperates without O2) to remove low or no chemical potential energy substances from biomass  \n\n\\- Torrefaction produces a combustable hydrocarbon gas which can (and should) be combusted to produce the energy needed for the process\n\n\\- Regenerative agriculture will eliminate the need to use residual biomass as fertilizer \n\n\\- Crop rotation with nitrogen fixing plants will replenish soil nitrogen \n\n\\- Cover crops will prevent topsoil erosion \n\n\\- No-till will preserve soil organic matter \n\n \\- Leaving residual biomass in-situ will cause the carbon that makes up the biomass to be decomposed into CO2 without utilization of the biomasses chemical potential energy  \n\nAs of now biofuels are still not a \"false solution\". \n\n\\- Used cooking oil and animal fat are being used to produce drop-in biofuels \n\n\\- Used cooking oil can cause clogs if it is dumped into specific systems \n\n\\- The fast food industry produces large quantities of used cooking oil \n\n\\- The meat industry produces large quantities of animal fat \n\n\\- Oil from oilseed crops is also being used as a feedstock to produce drop-in biofuels \n\n   \\- The intended product from oilseed crops is meal for feeding animals, this means that oil from these crops is a type of residual biomass \n\n\\- Existing animal feedlots use far less land than traditional or regenerative grazing\n\n\\- Feeding cows clay will reduce methane production in their digestive systems - [https://newatlas.com/environment/cow-burps-methane-clay/](https://newatlas.com/environment/cow-burps-methane-clay/) \n\n\\- The manure produced by livestock can (and should) be used to produce renewable natural gas via anaerobic digestion \n\n \\- The expansion of oilseed crops is displacing corn which is intended for animal feed production but does not co-produce carbohydrate oil alongside animal feed like oilseed crops \n\nOpposition to biofuels is based in emotion not logic. The majority of people seem to be too emotionally minded to understand that the sustainability of biofuels can be optimized under the right circumstances. Biofuels should be optimized for sustainability rather than written off as a \"false solution\". Our mindset towards biofuels needs to be based in logic not emotion. ", "author": "Live_Alarm3041", "created_time": "2024-12-17T16:47:51", "url": "https://reddit.com/r/CleanEnergy/comments/1hgev47/debunking_arguments_against_biofuels/", "upvotes": 0, "comments_count": 10, "sentiment": "bearish", "engagement_score": 20.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgevoe", "title": "$3 million net worth", "content": "I’m 45 and it has been a crazy 6 years. Last year I wrote an update 5 years post divorce. Well this past year has been one of the best years of my life. \n\nI got married again, to a great woman who I’ve been seeing for several years now. \n\nOn the Fire front, when we first started dating she asked me for investment help as she always worked and saved, but always just kept a large savings account at the bank. I introduced her to VTI and Target date funds. Also, her dream was to buy a house which she a few years back. \n\nWe both have been lucky with good careers that have accelerated in our 40s. We both get bonuses, I was investing almost all of mine and I really didn’t ask her what she was doing with hers. She just shared that she also likes saving. \n\nWe sat down and looked at all our accounts, and she has been saving and investing $4k-5k per month. When we added everything together I have just shy of $2 million and she has a bit over $1 million. We were both a bit shocked. \n\nAbout $850k is home equity. We own a nice house together and her old home she now rents out. The rest of the $2 million + is invested. \n\nWriting all this just seems so crazy. From age 35-39 I was miserable. Trapped in a toxic marriage with a woman who refused to work and tried her best to spend every penny I made, while telling me I didn’t provide enough for her. Today I have an amazing partner. We travel and have loads of fun together but are also a real team building a solid life together. \n\nWe both like our jobs, at least for the moment, but keep discussing when we might pull the trigger and get a nice house up the mountains one day. We don’t have an exact date, but when we both just don’t feel like working anymore. \n\nI guess my final thought is to those in there 30s, approaching 40 who feel down or are in a bad spot, it’s never too late to get your life together. For me, life really turned around at 40. ", "author": "NoShelter5922", "created_time": "2024-12-17T16:48:34", "url": "https://reddit.com/r/Fire/comments/1hgevoe/3_million_net_worth/", "upvotes": 1583, "comments_count": 207, "sentiment": "bullish", "engagement_score": 1997.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hggma5", "title": "FTC Bans Hidden Fees, Making Hotels and Event Tickets Cheaper", "content": "", "author": "gammapsi05", "created_time": "2024-12-17T18:02:05", "url": "https://reddit.com/r/UpliftingNews/comments/1hggma5/ftc_bans_hidden_fees_making_hotels_and_event/", "upvotes": 39656, "comments_count": 957, "sentiment": "neutral", "engagement_score": 41570.0, "source_subreddit": "UpliftingNews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hghbhn", "title": "<PERSON>: \"The Biden Admin paid Reuters over $300 million in government contracts. 11 different Biden government agencies targeted Elon's businesses. All 11 agencies paid millions to Reuters. Reuters then won the Pulitzer Prize for “their work on Elon Musk and misconduct at his businesses”\"", "content": "", "author": "twinbee", "created_time": "2024-12-17T18:32:03", "url": "https://reddit.com/r/elonmusk/comments/1hghbhn/mike_benz_the_biden_admin_paid_reuters_over_300/", "upvotes": 187, "comments_count": 621, "sentiment": "neutral", "engagement_score": 1429.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgi2ei", "title": "Masters in renewable energy and sustainable development ", "content": "Hello everyone. I completely my UG in Mechanical Engineering and I'm interested in doing masters in either renewable energy or sustainable development in Germany. Suggest me good colleges and courses related to the same. Thanks in advance. ", "author": "Itz_me_kratos", "created_time": "2024-12-17T19:04:05", "url": "https://reddit.com/r/Renewable/comments/1hgi2ei/masters_in_renewable_energy_and_sustainable/", "upvotes": 2, "comments_count": 2, "sentiment": "neutral", "engagement_score": 6.0, "source_subreddit": "Renewable", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgikor", "title": "If Bitcoin falls below $23,000, MicroStrategy will be forced to liquidate all of its BTC holdings and file for bankruptcy lol", "content": "The price was below that just a year ago, so this scenario isn’t far-fetched. In fact, I believe it will happen. MicroStrategy is a massive fraud that will collapse alongside Bitcoin.\n\nThere is some absolute f*ckery that is happening with these companies money printing against loans on crypto. Whenever his happens, the market catches up and people get annihilated.\n\nThere will be some kind of catalyst that plummets crypto, maybe some kind of quantum computer attack from a rogue nation or independent group of hackers, and crypto will crash extra hard this time because <PERSON><PERSON> and these other delusional morons will have over leveraged so comically hard.", "author": "WhoreMasterFalco", "created_time": "2024-12-17T19:26:16", "url": "https://reddit.com/r/wallstreetbets/comments/1hgikor/if_bitcoin_falls_below_23000_microstrategy_will/", "upvotes": 13303, "comments_count": 2409, "sentiment": "bearish", "engagement_score": 18121.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgknvl", "title": "Walmart is experimenting with body cameras for employees—like the ones used by cops—to ward off 'unprecedented levels' of shopper violence", "content": "", "author": "Forward-Answer-4407", "created_time": "2024-12-17T20:58:59", "url": "https://reddit.com/r/business/comments/1hgknvl/walmart_is_experimenting_with_body_cameras_for/", "upvotes": 2523, "comments_count": 100, "sentiment": "neutral", "engagement_score": 2723.0, "source_subreddit": "business", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgmo68", "title": "What are undervalued growth stocks that have high potential 2025,2030?", "content": "Hey I am new investor looking for undervalued stocks that have high growth potential and am trying to find the next APPL, AMZN, NVDA.If anyone knows of any stocks they believe have high growth potential in the future I will greatly appreciate any advice. Have a great day.", "author": "CakeImpressive2206", "created_time": "2024-12-17T22:27:53", "url": "https://reddit.com/r/ValueInvesting/comments/1hgmo68/what_are_undervalued_growth_stocks_that_have_high/", "upvotes": 2, "comments_count": 89, "sentiment": "bullish", "engagement_score": 180.0, "source_subreddit": "ValueInvesting", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgnhlq", "title": "Tesla should come out with a feature that lets you set the height of the hatch if your home and then any height when in public.", "content": "", "author": "ginger_VS_pie", "created_time": "2024-12-17T23:04:40", "url": "https://reddit.com/r/teslamotors/comments/1hgnhlq/tesla_should_come_out_with_a_feature_that_lets/", "upvotes": 936, "comments_count": 177, "sentiment": "neutral", "engagement_score": 1290.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1hgo6zy", "title": "Should I cancel my whole life insurance?", "content": "My husband (27M) and I (29F) were sold whole life insurance policies from a friend. We knew that they would be a slow up front investment, so the idea when we got the policies was that it would be a way to kind of diversify our tax exempt investments for retirement and our future children's inheritance. I recently started to wonder/realize if I have been low key tricked and if I would have been better off just investing the money in the market and utilizing trusts. (I think this is probably the case but I already did the dang thing). This lead me to wonder if I should consider canceling the policies.\n\nA little bit about our specific situation. \n\n* We live in the United States if that matters for tax purposes. \n* My income $0 (I'm currently getting my CRNA degree, so in 2 years I will be making about 250K)\n* My husband right now makes about 80K, so we're living just off his income. \n* When I graduate we plan to max out both of our 403Bs/401Ks and IRAs every year. \n* For debt we have about 15K in student loans total. (My current tuition is paid by my employer, and we saved a good chunk of money before I started, so we're planning not to take out any more student loans.)\n* We also have about 390K left on a 30 year mortgage at 2.8% we bought the house about 3 years ago. Our mortgage is about 2200 a month. \n* For retirement we have a combined 125K in our 403B/401Ks & we are offered a pension.\n* Our employer offers a benefit to employees of free term life insurance equal to 3 years pay. \n\nA little bit about the policies. (My husband and I both have the same ones). We've had them for 3 years.\n\n* Whole Life Policy: \n   * 250,000 death benefit. \n   * Net Accumulated Value of 2,000 \n   * Annual Premium 3000\n   * Total Paid in 9,000\n* Variable Universal Live: \n   * 250,000 death benefit. \n   * Net Accumulated Value of 3,500\n   * Annual Premium 2,000 \n   * Total Paid in 6,000\n\nMy question is being as we plan to max other tax exempt investing vehicles is this life insurance a reasonable thing to keep in this situation? Or should I consider cutting my losses canceling the policy, and planning to invest in more non-tax exempt vehicles in the future? Or would I even be better off paying extra towards my mortgage instead of the life insurance despite the low interest rate? From what I understand if we were to cancel the policies now we'd loose about 10,000 dollars each. If we did cancel what would be the best way if any to go about that so to take the least amount of loss possible? ", "author": "ElegantSquash157", "created_time": "2024-12-17T23:38:14", "url": "https://reddit.com/r/personalfinance/comments/1hgo6zy/should_i_cancel_my_whole_life_insurance/", "upvotes": 0, "comments_count": 62, "sentiment": "bearish", "engagement_score": 124.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}]