#!/usr/bin/env python3
"""
Reddit实时数据收集器

从Reddit API获取股票相关讨论数据，支持增量更新和情感分析
与AI对冲基金回测系统完全兼容
"""

import os
import json
import time
import logging
import re
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
from difflib import SequenceMatcher
import argparse
from dataclasses import dataclass
import re
import ssl
import urllib3
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

import praw
import prawcore
from dotenv import load_dotenv
from tqdm import tqdm

# 加载环境变量
load_dotenv()

@dataclass
class RedditConfig:
    """Reddit API配置"""
    client_id: str
    client_secret: str
    user_agent: str
    username: Optional[str] = None
    password: Optional[str] = None

class RedditLiveCollector:
    """Reddit实时数据收集器"""
    
    def __init__(self, config: RedditConfig, output_dir: str = "social_media_data"):
        """
        初始化Reddit收集器

        Args:
            config: Reddit API配置
            output_dir: 输出目录
        """
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 配置日志
        self.setup_logging()

        # 初始化Reddit API客户端 (只读模式，更稳定)
        self.reddit = self.create_reddit_client(config)

        # 验证API认证
        self.verify_authentication()
        
        # 股票配置 - 扩展关键词以提高匹配率
        self.ticker_keywords = {
            'AAPL': ['Apple', 'AAPL', 'Apple Inc', 'iPhone', 'iPad', 'Mac', 'iOS', 'Tim Cook', 'APPL', 'apple'],
            'MSFT': ['Microsoft', 'MSFT', 'Windows', 'Azure', 'Office', 'Xbox', 'Satya Nadella', 'MSFT', 'microsoft'],
            'NVDA': ['NVIDIA', 'NVDA', 'GeForce', 'RTX', 'GPU', 'AI chip', 'Jensen Huang', 'nvidia', 'graphics card'],
            'GOOGL': [
                # 公司名称和股票代码
                'Google', 'GOOGL', 'GOOG', 'Alphabet', 'alphabet', 'google',
                # 产品和服务
                'Gmail', 'YouTube', 'Android', 'Chrome', 'Google Search', 'Google Maps',
                'Google Drive', 'Google Cloud', 'GCP', 'Google Ads', 'AdWords', 'AdSense',
                'Google Play', 'Pixel', 'Chromebook', 'Google Assistant', 'Google Home',
                'Waymo', 'DeepMind', 'Google Fiber', 'Nest', 'Stadia',
                # 高管
                'Sundar Pichai', 'Larry Page', 'Sergey Brin',
                # 行业术语
                'search engine', 'cloud computing', 'digital advertising', 'autonomous driving',
                'artificial intelligence', 'machine learning', 'quantum computing',
                # 常见拼写错误和变体
                'googl', 'gooogle', 'google inc', 'alphabet inc', 'youtube'
            ],
            'AMZN': ['Amazon', 'AMZN', 'AWS', 'Prime', 'Alexa', 'Jeff Bezos', 'Andy Jassy', 'amazon', 'e-commerce'],
            'TSLA': [
                # 公司名称和股票代码
                'Tesla', 'TSLA', 'tesla', 'Tesla Inc', 'Tesla Motors',
                # 产品线
                'Model S', 'Model 3', 'Model Y', 'Model X', 'Cybertruck', 'Semi', 'Roadster',
                'Powerwall', 'Solar Roof', 'Supercharger', 'Tesla Bot', 'Optimus',
                # 技术和服务
                'Autopilot', 'Full Self Driving', 'FSD', 'Tesla Energy', 'Tesla Network',
                'Tesla Insurance', 'Tesla Solar', 'Gigafactory', 'Megapack',
                # 高管
                'Elon Musk', 'Drew Baglino', 'Zachary Kirkhorn',
                # 行业术语
                'electric vehicle', 'EV', 'electric car', 'autonomous driving', 'self-driving',
                'battery technology', 'lithium-ion', 'sustainable energy', 'renewable energy',
                'electric mobility', 'zero emission', 'clean energy',
                # 相关公司/项目
                'SpaceX', 'Neuralink', 'Boring Company',
                # 常见拼写错误和变体
                'tsla', 'tesla motors', 'teslaa', 'teslas'
            ],
            'META': ['Meta', 'META', 'Facebook', 'Instagram', 'WhatsApp', 'Mark Zuckerberg', 'FB', 'facebook', 'instagram'],
            'NFLX': ['Netflix', 'NFLX', 'streaming', 'Reed Hastings', 'netflix'],
            'AMD': ['AMD', 'Ryzen', 'Radeon', 'Lisa Su', 'amd'],
            'INTC': ['Intel', 'INTC', 'Core', 'Xeon', 'Pat Gelsinger', 'intel']
        }
        
        # 目标子版块 - 大幅扩展以提高数据覆盖度
        self.target_subreddits = [
            # 主要投资和股票相关
            'stocks', 'investing', 'wallstreetbets', 'SecurityAnalysis',
            'ValueInvesting', 'financialindependence', 'StockMarket',
            'options', 'pennystocks', 'dividends', 'investing_discussion',
            'finance', 'business', 'economy', 'Economics', 'FinancialPlanning',
            'SecurityAnalysis', 'ValueInvesting', 'investing_discussion',
            'StockMarket', 'options', 'pennystocks', 'dividends',

            # 科技和行业相关
            'technology', 'tech', 'TechNews', 'gadgets', 'startups',
            'artificial', 'MachineLearning', 'singularity', 'Futurology',
            'CloudComputing', 'programming', 'webdev',

            # 公司特定子版块
            'apple', 'microsoft', 'nvidia', 'tesla', 'amazon', 'google',
            'TeslaMotors', 'teslamotors', 'TeslaInvestorsClub', 'elonmusk',
            'GooglePixel', 'Android', 'youtube', 'chrome', 'googlemaps',

            # 电动车和能源相关 (针对TSLA)
            'electricvehicles', 'EVs', 'electriccars', 'SelfDrivingCars',
            'AutonomousVehicles', 'CleanEnergy', 'solar', 'batteries',
            'renewable', 'sustainability', 'environment',

            # 搜索引擎和广告相关 (针对GOOGL)
            'SEO', 'PPC', 'adwords', 'marketing', 'DigitalMarketing',
            'webmaster', 'analytics', 'privacy',

            # 新闻和讨论
            'news', 'worldnews', 'business', 'Economics', 'politics',
            'UpliftingNews', 'nottheonion', 'todayilearned',

            # 其他相关
            'entrepreneur', 'smallbusiness', 'personalfinance',
            'financialindependence', 'FIRE', 'investing_discussion'
        ]
        
        # 情感分析关键词
        self.sentiment_keywords = {
            'bullish': [
                'buy', 'bull', 'bullish', 'long', 'calls', 'moon', 'rocket',
                'pump', 'surge', 'rally', 'breakout', 'uptrend', 'strong',
                'positive', 'optimistic', 'growth', 'profit', 'earnings beat'
            ],
            'bearish': [
                'sell', 'bear', 'bearish', 'short', 'puts', 'crash', 'dump',
                'drop', 'fall', 'decline', 'downtrend', 'weak', 'negative',
                'pessimistic', 'loss', 'earnings miss', 'overvalued'
            ]
        }
        
        # 已处理帖子ID缓存
        self.processed_posts: Set[str] = set()
        self.load_processed_posts()

    def create_reddit_client(self, config: RedditConfig) -> praw.Reddit:
        """创建配置了网络重试和SSL处理的Reddit客户端"""
        try:
            # 禁用SSL警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

            # 创建自定义的requests会话，配置重试策略
            session = requests.Session()

            # 配置重试策略 - 添加401到重试状态码
            retry_strategy = Retry(
                total=5,  # 总重试次数
                backoff_factor=2,  # 退避因子
                status_forcelist=[401, 403, 429, 500, 502, 503, 504],  # 需要重试的状态码
                allowed_methods=["GET", "POST"],  # 允许重试的方法
                raise_on_status=False
            )

            # 创建HTTP适配器
            adapter = HTTPAdapter(
                max_retries=retry_strategy,
                pool_connections=10,
                pool_maxsize=20
            )

            # 挂载适配器
            session.mount("http://", adapter)
            session.mount("https://", adapter)

            # 设置SSL配置
            session.verify = True  # 启用SSL验证

            # 优先使用用户认证模式（如果有用户名密码）
            if config.username and config.password:
                self.logger.info("使用用户认证模式创建Reddit客户端")
                reddit = praw.Reddit(
                    client_id=config.client_id,
                    client_secret=config.client_secret,
                    user_agent=config.user_agent,
                    username=config.username,
                    password=config.password,
                    requestor_kwargs={
                        'session': session,
                        'timeout': (30, 60)  # 连接超时30秒，读取超时60秒
                    }
                )
            else:
                # 使用应用认证模式（只读）
                self.logger.info("使用应用认证模式创建Reddit客户端")
                reddit = praw.Reddit(
                    client_id=config.client_id,
                    client_secret=config.client_secret,
                    user_agent=config.user_agent,
                    requestor_kwargs={
                        'session': session,
                        'timeout': (30, 60)  # 连接超时30秒，读取超时60秒
                    }
                )

            self.logger.info("Reddit客户端创建成功，已配置网络重试和SSL处理")
            return reddit

        except Exception as e:
            self.logger.error(f"创建Reddit客户端失败: {e}")
            # 回退到基本配置
            self.logger.info("回退到基本Reddit客户端配置")
            if config.username and config.password:
                return praw.Reddit(
                    client_id=config.client_id,
                    client_secret=config.client_secret,
                    user_agent=config.user_agent,
                    username=config.username,
                    password=config.password
                )
            else:
                return praw.Reddit(
                    client_id=config.client_id,
                    client_secret=config.client_secret,
                    user_agent=config.user_agent
                )

    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "reddit_collector.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def test_network_connectivity(self) -> bool:
        """测试网络连接和Reddit API状态"""
        try:
            # 测试基本网络连接
            import socket
            socket.create_connection(("www.reddit.com", 443), timeout=10)
            self.logger.info("网络连接正常")

            # 测试Reddit API连接
            response = requests.get("https://www.reddit.com/api/v1/me", timeout=10)
            self.logger.info(f"Reddit API状态: {response.status_code}")
            return True

        except Exception as e:
            self.logger.warning(f"网络连接测试失败: {e}")
            return False

    def verify_authentication(self):
        """验证Reddit API认证"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 先测试网络连接
                if not self.test_network_connectivity():
                    self.logger.warning("网络连接不稳定，但继续尝试认证...")

                # 测试基本认证 - 尝试访问一个简单的公开子版块
                test_subreddit = self.reddit.subreddit('test')
                _ = test_subreddit.display_name
                self.logger.info("Reddit API认证验证成功 (只读模式)")
                return

            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 401:
                    self.logger.error("Reddit API认证失败 - 401 Unauthorized")
                    self.logger.error("请检查以下配置:")
                    self.logger.error("1. REDDIT_CLIENT_ID 和 REDDIT_CLIENT_SECRET 是否正确")
                    self.logger.error("2. 在 https://www.reddit.com/prefs/apps 创建的应用类型是否为 'script'")
                    self.logger.error("3. 确认User-Agent格式正确")
                    raise ValueError("Reddit API认证失败")
                elif e.response.status_code == 403:
                    if attempt < max_retries - 1:
                        wait_time = 30 * (attempt + 1)
                        self.logger.warning(f"Reddit API访问被禁止 - 403 Forbidden (尝试 {attempt + 1}/{max_retries})")
                        self.logger.warning(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        self.logger.error("Reddit API访问被禁止 - 403 Forbidden")
                        self.logger.error("可能原因:")
                        self.logger.error("1. Reddit应用类型设置错误 (应该是'script')")
                        self.logger.error("2. User-Agent格式不正确")
                        self.logger.error("3. IP地址被Reddit限制")
                        self.logger.error("4. Reddit服务器临时限制")
                        self.logger.warning("继续运行，但可能遇到访问限制...")
                        return  # 不抛出异常，允许继续运行
                else:
                    self.logger.error(f"Reddit API验证失败: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(10)
                        continue
                    raise
            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Reddit API验证失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(10)
                    continue
                else:
                    self.logger.error(f"Reddit API验证失败: {e}")
                    raise

    def load_processed_posts(self):
        """加载已处理的帖子ID"""
        cache_file = self.output_dir / "processed_posts_cache.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                    self.processed_posts = set(data.get('processed_posts', []))
                self.logger.info(f"加载了 {len(self.processed_posts)} 个已处理帖子ID")
            except Exception as e:
                self.logger.warning(f"加载缓存失败: {e}")
    
    def save_processed_posts(self):
        """保存已处理的帖子ID"""
        cache_file = self.output_dir / "processed_posts_cache.json"
        try:
            with open(cache_file, 'w') as f:
                json.dump({
                    'processed_posts': list(self.processed_posts),
                    'last_updated': datetime.now().isoformat()
                }, f, indent=2)
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def fuzzy_match(self, text: str, keyword: str, threshold: float = 0.8) -> bool:
        """模糊匹配函数，用于处理拼写错误和变体"""
        if not text or not keyword:
            return False

        # 直接匹配
        if keyword.lower() in text.lower():
            return True

        # 模糊匹配 - 对于较短的关键词使用更严格的阈值
        if len(keyword) <= 4:
            threshold = 0.9

        similarity = SequenceMatcher(None, text.lower(), keyword.lower()).ratio()
        return similarity >= threshold

    def extract_tickers_from_text(self, text: str, title: str = "") -> List[str]:
        """从文本中提取股票代码 - 增强版本支持模糊匹配和标题+内容检查"""
        if not text and not title:
            return []

        # 合并标题和内容进行检查
        combined_text = f"{title} {text}".strip()
        text_lower = combined_text.lower()
        found_tickers = []

        for ticker, keywords in self.ticker_keywords.items():
            ticker_found = False

            for keyword in keywords:
                # 精确匹配
                if keyword.lower() in text_lower:
                    found_tickers.append(ticker)
                    ticker_found = True
                    break

                # 模糊匹配 - 对于重要关键词
                if len(keyword) > 3 and self.fuzzy_match(combined_text, keyword, 0.85):
                    found_tickers.append(ticker)
                    ticker_found = True
                    break

            if ticker_found:
                continue

        # 直接匹配股票代码格式 $TICKER, TICKER:, (TICKER)
        ticker_patterns = [
            r'\$([A-Z]{1,5})\b',  # $TSLA
            r'\b([A-Z]{1,5}):\s',  # TSLA:
            r'\(([A-Z]{1,5})\)',   # (TSLA)
            r'\b([A-Z]{1,5})\s+stock\b',  # TSLA stock
            r'\b([A-Z]{1,5})\s+shares?\b'  # TSLA shares
        ]

        for pattern in ticker_patterns:
            matches = re.findall(pattern, combined_text.upper())
            for match in matches:
                if match in self.ticker_keywords:
                    found_tickers.append(match)

        return list(set(found_tickers))

    def search_subreddit_for_tickers(self, subreddit_name: str,
                                   start_date: datetime, end_date: datetime,
                                   tickers: List[str], limit: int = 100) -> List[Dict[str, Any]]:
        """使用Reddit搜索API在子版块中搜索特定股票相关内容"""
        posts = []

        try:
            subreddit = self.reddit.subreddit(subreddit_name)

            # 为每个股票代码构建搜索查询
            for ticker in tickers:
                if ticker not in self.ticker_keywords:
                    continue

                # 构建搜索关键词 - 使用最重要的关键词
                keywords = self.ticker_keywords[ticker][:5]  # 取前5个最重要的关键词
                search_queries = [
                    ticker,  # 股票代码本身
                    f"${ticker}",  # 带$符号的股票代码
                    keywords[0] if keywords else ticker  # 主要公司名称
                ]

                for query in search_queries:
                    try:
                        # 搜索帖子
                        search_results = subreddit.search(
                            query,
                            sort='relevance',
                            time_filter='all',
                            limit=limit // len(search_queries)
                        )

                        for submission in search_results:
                            try:
                                created_time = datetime.fromtimestamp(
                                    submission.created_utc, tz=timezone.utc
                                ).replace(tzinfo=None)

                                # 检查时间范围
                                if not (start_date <= created_time <= end_date):
                                    continue

                                # 处理帖子内容
                                processed_post = self.process_submission(submission, debug_mode=False)
                                if processed_post:
                                    posts.append(processed_post)

                            except Exception as e:
                                self.logger.debug(f"处理搜索结果失败: {e}")
                                continue

                        # 添加延迟避免过快请求
                        time.sleep(1)

                    except Exception as e:
                        self.logger.warning(f"搜索 '{query}' 在 r/{subreddit_name} 失败: {e}")
                        continue

        except Exception as e:
            self.logger.error(f"搜索 r/{subreddit_name} 失败: {e}")

        return posts

    def determine_sentiment(self, text: str) -> str:
        """确定文本情感倾向"""
        if not text:
            return 'neutral'
        
        text_lower = text.lower()
        bullish_score = 0
        bearish_score = 0
        
        for keyword in self.sentiment_keywords['bullish']:
            bullish_score += text_lower.count(keyword)
        
        for keyword in self.sentiment_keywords['bearish']:
            bearish_score += text_lower.count(keyword)
        
        if bullish_score > bearish_score:
            return 'bullish'
        elif bearish_score > bullish_score:
            return 'bearish'
        else:
            return 'neutral'
    
    def calculate_engagement_score(self, upvotes: int, comments_count: int) -> float:
        """计算参与度分数"""
        return upvotes * 1.0 + comments_count * 2.0
    
    def process_submission(self, submission, debug_mode=False) -> Optional[Dict[str, Any]]:
        """处理Reddit提交"""
        try:
            # 检查是否已处理
            if submission.id in self.processed_posts:
                if debug_mode:
                    self.logger.debug(f"跳过已处理帖子: {submission.id}")
                return None

            # 获取文本内容
            title = submission.title or ""
            content = submission.selftext or ""
            full_text = f"{title} {content}"

            if debug_mode:
                self.logger.info(f"处理帖子: '{title[:50]}{'...' if len(title) > 50 else ''}'")
                self.logger.info(f"  作者: {submission.author}, 评分: {submission.score}, 评论: {submission.num_comments}")

            # 提取相关股票代码 - 同时检查标题和内容
            tickers = self.extract_tickers_from_text(full_text, title)

            if debug_mode:
                if tickers:
                    self.logger.info(f"  [MATCH] 找到匹配股票: {tickers}")
                else:
                    self.logger.info(f"  [SKIP] 无匹配股票")
                    # 显示前100个字符用于调试
                    preview = full_text[:100].replace('\n', ' ')
                    self.logger.debug(f"  文本预览: '{preview}{'...' if len(full_text) > 100 else ''}'")

            if not tickers:
                return None
            
            # 创建帖子数据
            created_time = datetime.fromtimestamp(
                submission.created_utc, tz=timezone.utc
            ).replace(tzinfo=None)
            
            post_data = {
                'platform': 'reddit',
                'post_id': f"reddit_{submission.id}",
                'title': title,
                'content': content,
                'author': str(submission.author) if submission.author else 'deleted',
                'created_time': created_time.isoformat(),
                'url': f"https://reddit.com{submission.permalink}",
                'upvotes': submission.score,
                'comments_count': submission.num_comments,
                'tickers': tickers,
                'sentiment': self.determine_sentiment(full_text),
                'engagement_score': self.calculate_engagement_score(
                    submission.score, submission.num_comments
                ),
                'source_subreddit': submission.subreddit.display_name,
                'hashtags': None
            }
            
            # 标记为已处理
            self.processed_posts.add(submission.id)
            
            return post_data
            
        except Exception as e:
            self.logger.error(f"处理提交失败 {submission.id}: {e}")
            return None
    
    def save_posts_by_date_and_ticker(self, posts: List[Dict[str, Any]]):
        """按日期和股票代码保存帖子"""
        posts_by_ticker_date = {}
        
        for post in posts:
            created_time = datetime.fromisoformat(post['created_time'])
            date_str = created_time.strftime('%Y-%m-%d')
            
            for ticker in post['tickers']:
                key = (ticker, date_str)
                if key not in posts_by_ticker_date:
                    posts_by_ticker_date[key] = []
                
                # 为每个股票创建单独的帖子记录
                ticker_post = post.copy()
                ticker_post['ticker'] = ticker
                del ticker_post['tickers']  # 移除tickers列表
                
                posts_by_ticker_date[key].append(ticker_post)
        
        # 保存到文件
        for (ticker, date_str), ticker_posts in posts_by_ticker_date.items():
            ticker_dir = self.output_dir / f"{ticker}_social_media"
            ticker_dir.mkdir(parents=True, exist_ok=True)
            
            file_path = ticker_dir / f"reddit_{date_str}.json"
            
            # 如果文件已存在，合并数据
            existing_posts = []
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        existing_posts = json.load(f)
                except Exception as e:
                    self.logger.warning(f"读取现有文件失败 {file_path}: {e}")
            
            # 合并并去重
            existing_ids = {post.get('post_id') for post in existing_posts}
            new_posts = [post for post in ticker_posts 
                        if post.get('post_id') not in existing_ids]
            
            if new_posts:
                all_posts = existing_posts + new_posts
                
                # 按时间排序
                all_posts.sort(key=lambda x: x.get('created_time', ''))
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(all_posts, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"保存 {ticker} {date_str}: {len(new_posts)} 个新帖子")
    
    def collect_from_subreddit(self, subreddit_name: str,
                              start_date: datetime, end_date: datetime,
                              limit: int = 1000) -> List[Dict[str, Any]]:
        """从指定子版块收集数据 - 带重试机制处理403错误和SSL错误"""
        posts = []
        max_retries = 5  # 增加重试次数
        base_delay = 30  # 减少基础延迟到30秒

        for attempt in range(max_retries):
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                self.logger.info(f"开始收集 r/{subreddit_name} 数据... (尝试 {attempt + 1}/{max_retries})")

                # 测试子版块访问权限
                try:
                    # 先测试能否访问子版块基本信息
                    _ = subreddit.display_name
                    self.logger.debug(f"成功访问 r/{subreddit_name}")
                except prawcore.exceptions.NotFound:
                    self.logger.error(f"r/{subreddit_name} 不存在")
                    return posts
                except prawcore.exceptions.Forbidden:
                    if attempt < max_retries - 1:
                        delay = base_delay * (1.5 ** attempt)
                        self.logger.warning(f"r/{subreddit_name} 访问被禁止，等待 {delay:.1f} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"r/{subreddit_name} 访问被禁止 (多次重试失败)")
                        return posts
                except prawcore.exceptions.ResponseException as e:
                    if e.response.status_code == 401:
                        self.logger.error(f"r/{subreddit_name} 401认证错误 - 请检查Reddit API凭据")
                        return posts  # 401错误不重试
                    elif e.response.status_code == 403:
                        if attempt < max_retries - 1:
                            delay = base_delay * (1.5 ** attempt)  # 使用更温和的退避策略
                            self.logger.warning(f"r/{subreddit_name} 403错误，等待 {delay:.1f} 秒后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            self.logger.error(f"r/{subreddit_name} 403错误 (多次重试失败) - 可能是临时限制")
                            return posts
                    else:
                        raise
                except (prawcore.exceptions.ServerError, prawcore.exceptions.RequestException,
                        ConnectionError, TimeoutError, Exception) as e:
                    if attempt < max_retries - 1:
                        delay = base_delay * (1.5 ** attempt)
                        self.logger.warning(f"r/{subreddit_name} 网络错误 ({type(e).__name__})，等待 {delay:.1f} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"r/{subreddit_name} 网络错误 (多次重试失败): {e}")
                        return posts

                # 获取历史帖子 - 使用多种排序方式获取更全面的数据
                actual_limit = min(limit, 500)  # 大幅增加限制以获取更多历史数据
                all_submissions = []

                # 定义多种获取方式和时间过滤器
                collection_methods = [
                    ('top', 'all'),
                    ('top', 'year'),
                    ('top', 'month'),
                    ('hot', None),
                    ('controversial', 'all'),
                    ('controversial', 'year')
                ]

                # 尝试多种方法收集数据
                for method, time_filter in collection_methods:
                    try:
                        method_limit = actual_limit // len(collection_methods)  # 分配限制
                        if method == 'hot':
                            submissions = subreddit.hot(limit=method_limit)
                        elif method == 'top':
                            submissions = subreddit.top(time_filter=time_filter, limit=method_limit)
                        elif method == 'controversial':
                            submissions = subreddit.controversial(time_filter=time_filter, limit=method_limit)

                        # 收集提交
                        method_submissions = list(submissions)
                        all_submissions.extend(method_submissions)
                        self.logger.debug(f"r/{subreddit_name} {method}({time_filter}): 获取 {len(method_submissions)} 个帖子")

                        # 添加小延迟避免过快请求
                        time.sleep(0.5)

                    except Exception as e:
                        self.logger.warning(f"r/{subreddit_name} {method}({time_filter}) 失败: {e}")
                        continue

                # 去重 - 基于帖子ID
                seen_ids = set()
                unique_submissions = []
                for sub in all_submissions:
                    if sub.id not in seen_ids:
                        seen_ids.add(sub.id)
                        unique_submissions.append(sub)

                submissions = unique_submissions
                self.logger.info(f"r/{subreddit_name} 总共获取 {len(submissions)} 个唯一帖子")

                # 处理帖子
                submission_count = 0
                consecutive_errors = 0
                max_consecutive_errors = 5

                for submission in tqdm(submissions, desc=f"r/{subreddit_name}"):
                    try:
                        submission_count += 1
                        created_time = datetime.fromtimestamp(
                            submission.created_utc, tz=timezone.utc
                        ).replace(tzinfo=None)

                        # 检查时间范围
                        if not (start_date <= created_time <= end_date):
                            continue

                        post_data = self.process_submission(submission, debug_mode=True)
                        if post_data:
                            posts.append(post_data)
                            consecutive_errors = 0  # 重置错误计数

                        # API限制控制 - 动态调整延迟
                        if consecutive_errors > 0:
                            time.sleep(min(2.0, 0.5 * (consecutive_errors + 1)))
                        else:
                            time.sleep(0.3)  # 减少基础延迟

                    except prawcore.exceptions.TooManyRequests:
                        consecutive_errors += 1
                        wait_time = min(120, 30 * consecutive_errors)  # 动态增加等待时间
                        self.logger.warning(f"API限制，等待{wait_time}秒...")
                        time.sleep(wait_time)
                        continue
                    except prawcore.exceptions.ResponseException as e:
                        consecutive_errors += 1
                        if e.response.status_code == 401:
                            self.logger.error(f"处理提交时401认证错误: {e}")
                            self.logger.error("Reddit API认证失败，停止处理此子版块")
                            break  # 401错误不重试
                        elif e.response.status_code == 403:
                            self.logger.warning(f"处理提交时403错误: {e}")
                            if consecutive_errors >= max_consecutive_errors:
                                self.logger.error("连续403错误过多，停止处理此子版块")
                                break
                            time.sleep(10)  # 短暂等待后继续
                            continue
                        else:
                            self.logger.error(f"处理提交失败: {e}")
                            if consecutive_errors >= max_consecutive_errors:
                                self.logger.error("连续错误过多，停止处理此子版块")
                                break
                            continue
                    except (ConnectionError, TimeoutError, Exception) as e:
                        consecutive_errors += 1
                        self.logger.error(f"处理提交失败 ({type(e).__name__}): {e}")
                        if consecutive_errors >= max_consecutive_errors:
                            self.logger.error("连续错误过多，停止处理此子版块")
                            break
                        time.sleep(2)  # 短暂等待后继续
                        continue

                self.logger.info(f"r/{subreddit_name} 成功处理了 {submission_count} 个提交，获得 {len(posts)} 个相关帖子")
                return posts  # 成功获取数据，退出重试循环

            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 401:
                    self.logger.error(f"收集 r/{subreddit_name} 失败: 401 认证错误")
                    self.logger.error("可能原因:")
                    self.logger.error("1. Reddit API凭据已过期或无效")
                    self.logger.error("2. 应用类型设置错误（应该是'script'）")
                    self.logger.error("3. Client ID或Secret错误")
                    self.logger.error("请检查 https://www.reddit.com/prefs/apps 中的应用设置")
                    break  # 401错误通常不会通过重试解决
                elif e.response.status_code == 403:
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        self.logger.warning(f"收集 r/{subreddit_name} 403错误，等待 {delay} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"收集 r/{subreddit_name} 403错误 (多次重试失败)")
                else:
                    self.logger.error(f"收集 r/{subreddit_name} 失败: {e}")
                    break
            except Exception as e:
                self.logger.error(f"收集 r/{subreddit_name} 失败: {e}")
                break

        return posts
    
    def collect_data(self, start_date: datetime, end_date: datetime,
                    tickers: Optional[List[str]] = None,
                    subreddits: Optional[List[str]] = None,
                    limit_per_subreddit: int = 1000) -> Dict[str, int]:
        """
        收集Reddit数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            tickers: 目标股票代码列表
            subreddits: 目标子版块列表
            limit_per_subreddit: 每个子版块的帖子限制
        
        Returns:
            收集统计信息
        """
        if tickers:
            # 过滤股票关键词
            filtered_keywords = {k: v for k, v in self.ticker_keywords.items() 
                               if k in tickers}
            self.ticker_keywords = filtered_keywords
        
        target_subreddits = subreddits or self.target_subreddits
        
        self.logger.info(f"开始收集Reddit数据")
        self.logger.info(f"时间范围: {start_date} 到 {end_date}")
        self.logger.info(f"目标股票: {list(self.ticker_keywords.keys())}")
        self.logger.info(f"目标子版块: {target_subreddits}")
        
        all_posts = []
        stats = {'total_posts': 0, 'relevant_posts': 0, 'subreddits_processed': 0}
        
        for subreddit_name in target_subreddits:
            try:
                # 方法1: 常规收集 (浏览帖子)
                posts = self.collect_from_subreddit(
                    subreddit_name, start_date, end_date, limit_per_subreddit
                )

                # 方法2: 搜索收集 (针对特定股票搜索)
                if tickers:
                    search_posts = self.search_subreddit_for_tickers(
                        subreddit_name, start_date, end_date, tickers,
                        limit=limit_per_subreddit // 4  # 搜索使用较少的限制
                    )
                    posts.extend(search_posts)

                # 去重 - 基于帖子ID
                seen_ids = set()
                unique_posts = []
                for post in posts:
                    post_id = post.get('post_id', '')
                    if post_id and post_id not in seen_ids:
                        seen_ids.add(post_id)
                        unique_posts.append(post)

                all_posts.extend(unique_posts)
                stats['subreddits_processed'] += 1
                stats['relevant_posts'] += len(unique_posts)

                self.logger.info(f"r/{subreddit_name}: 收集到 {len(unique_posts)} 个相关帖子 (去重后)")
                
                # 定期保存数据
                if len(all_posts) >= 100:
                    self.save_posts_by_date_and_ticker(all_posts)
                    all_posts = []
                    self.save_processed_posts()
                
            except Exception as e:
                self.logger.error(f"处理 r/{subreddit_name} 失败: {e}")
                continue
        
        # 保存剩余数据
        if all_posts:
            self.save_posts_by_date_and_ticker(all_posts)
        
        # 保存缓存
        self.save_processed_posts()
        
        stats['total_posts'] = len(self.processed_posts)
        
        return stats

def clear_system_reddit_env_vars():
    """清除可能冲突的系统环境变量"""
    reddit_vars = [
        'REDDIT_CLIENT_ID',
        'REDDIT_CLIENT_SECRET',
        'REDDIT_USER_AGENT',
        'REDDIT_USERNAME',
        'REDDIT_PASSWORD'
    ]

    for var in reddit_vars:
        if var in os.environ:
            del os.environ[var]

def load_reddit_config() -> RedditConfig:
    """加载Reddit API配置"""
    # 清除系统环境变量，确保使用.env文件中的配置
    clear_system_reddit_env_vars()

    # 强制重新加载.env文件
    load_dotenv(override=True)

    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')

    if not client_id or not client_secret:
        raise ValueError(
            "Reddit API配置缺失！请在.env文件中设置:\n"
            "REDDIT_CLIENT_ID=your_client_id\n"
            "REDDIT_CLIENT_SECRET=your_client_secret\n"
            "REDDIT_USER_AGENT=your_app_name/1.0\n"
            "REDDIT_USERNAME=your_username (可选)\n"
            "REDDIT_PASSWORD=your_password (可选)\n\n"
            "获取Reddit API凭据: https://www.reddit.com/prefs/apps"
        )

    return RedditConfig(
        client_id=client_id,
        client_secret=client_secret,
        user_agent=user_agent,
        username=username,
        password=password
    )

def main():
    parser = argparse.ArgumentParser(description='Reddit实时数据收集器')
    parser.add_argument('--start-date', type=str, default='2024-12-01',
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2025-06-01',
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--tickers', nargs='+',
                       default=['GOOGL', 'TSLA', ],
                       help='目标股票代码')
    parser.add_argument('--subreddits', nargs='+',
                       help='目标子版块 (默认使用预设列表)')
    parser.add_argument('--output-dir', default='social_media_data',
                       help='输出目录')
    parser.add_argument('--limit-per-subreddit', type=int, default=2000,
                       help='每个子版块的帖子限制 (默认2000以获得更好覆盖)')
    parser.add_argument('--incremental', action='store_true',
                       help='增量更新模式')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    parser.add_argument('--test-connection', action='store_true',
                       help='仅测试连接，不收集数据')
    
    args = parser.parse_args()

    try:
        # 设置日志级别
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            print("调试模式已启用")

        # 加载配置
        config = load_reddit_config()

        # 创建收集器
        collector = RedditLiveCollector(config, args.output_dir)

        # 仅测试连接
        if args.test_connection:
            print("正在测试Reddit API连接...")
            if collector.test_network_connectivity():
                print("✓ 网络连接正常")
            else:
                print("✗ 网络连接异常")

            # 测试几个子版块的访问
            test_subreddits = ['test', 'stocks', 'investing']
            for sub_name in test_subreddits:
                try:
                    subreddit = collector.reddit.subreddit(sub_name)
                    _ = subreddit.display_name
                    print(f"✓ r/{sub_name} 访问正常")
                except Exception as e:
                    print(f"✗ r/{sub_name} 访问失败: {e}")
            return 0

        # 解析日期
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')

        # 增量更新：从昨天开始
        if args.incremental:
            start_date = datetime.now() - timedelta(days=1)
            end_date = datetime.now()

        # 开始收集
        stats = collector.collect_data(
            start_date=start_date,
            end_date=end_date,
            tickers=args.tickers,
            subreddits=args.subreddits,
            limit_per_subreddit=args.limit_per_subreddit
        )
        
        # 显示统计
        print("\n" + "="*60)
        print("Reddit数据收集完成")
        print("="*60)
        print(f"处理子版块数: {stats['subreddits_processed']}")
        print(f"相关帖子数: {stats['relevant_posts']}")
        print(f"总处理帖子数: {stats['total_posts']}")
        print(f"数据保存在: {args.output_dir}")
        
    except Exception as e:
        print(f"收集失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
