[{"platform": "reddit", "post_id": "reddit_1il2us6", "title": "\"Search image with google\" returns error 413 request entity too large", "content": "Hello, I often use the \"Search image with google\" option when right clicking on an image to find higher quality versions or slightly different versions of an image. I've had trouble with it switching to google lens in the past but was able to restore the \"Search image with google\" option thanks to you lovely redditors. However today the option is still there but when i click it, it takes me to this error 413 page. Is there a work around or is this truly the end of this incredibly useful feature?\n\nhttps://preview.redd.it/v6pt6lajj0ie1.png?width=1386&format=png&auto=webp&s=5d274b06ab6ec6827b7b55c1fbd16840418d9ad1\n\n", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-02-09T01:05:55", "url": "https://reddit.com/r/chrome/comments/1il2us6/search_image_with_google_returns_error_413/", "upvotes": 116, "comments_count": 94, "sentiment": "neutral", "engagement_score": 304.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1il3mho", "title": "Google reviews disappearing", "content": "95 disappeared in the last 2 days, all organic and all 5 stars, what is going on ", "author": "Expensive-Can9669", "created_time": "2025-02-09T01:45:21", "url": "https://reddit.com/r/smallbusiness/comments/1il3mho/google_reviews_disappearing/", "upvotes": 6, "comments_count": 40, "sentiment": "neutral", "engagement_score": 86.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1il8ggf", "title": "Great Magazine Reads: Robocars continue towards a path of safer U.S. roads", "content": "[https://popculturelunchbox.substack.com/p/great-magazine-reads-robocars-continue](https://popculturelunchbox.substack.com/p/great-magazine-reads-robocars-continue)\n\nIt’s been a few years now since I was regularly quoted in the media and spoke a lot about autonomous vehicles. But with my time away from the spotlight, it’s good to see in the January/February issue of WIRED magazine that AVs are still in the pipeline and have begun taking on a much cooler name: robocars.\n\nWhile not many places are getting to experience the wonders of the safer world that these kinds of devices could bring, the article mentions that places like Los Angeles, Phoenix, San Francisco, and Wuhan, China are well along the way with the vehicles, and the people in those cities barely blink an eyeball to their existence anymore.\n\nThe authors’ goal for the article was to follow a single Waymo robotaxi throughout a whole workday in San Francisco to see how it operated and to also interview as many of its passengers as they could.\n\nThose who rode in the robocars reported the same kinds of experiences I had back at a future-transportation conference in Los Angeles about a decade ago. It starts out feeling like a cool amusement park ride and quickly shifts to being the opposite. No thrills. No lurches. Just smoothly and slowly moving along.\n\nOne of the first observations of the authors is that the Waymos spend a good bit of time going to their recharging lots to power back up, with no passengers in tow. The logical question about all this so-called deadheading? “Is Waymo going to make congestion worse by filling the streets with 5,000-pound contraptions that are completely empty?”\n\nAn urban-planning professor interviewed in the article says that the use cases of the past 15 years of Uber and Lyft are starting to offer a pretty good idea of what robocars might do for congestion.\n\n“Research suggests that, in fact, Uber and Lyft brought more private cars onto city streets, partly because drivers acquired new ones to gig for the platforms. All that led to—you guessed it—more congestion. No one will be buying a new car to gig for Waymo, of course. But there could be more gridlock mainly because of the way cities fail to price roads. In busy downtown, driving is free. It’s the price of parking that typically pressures car owners to take some other mode of transit. Trouble is, robots don’t need to park downtown. It’s a recipe for endless traffic.”\n\nBut back to the task at hand. A couple seemingly excited to be taking their robo Waymo, after they reached their destination near City Lights bookstore, said they loved that there was no stranger in the car and how smooth the ride had been. Others said it was not “slow and stupid,” like they thought it would be. The cars don’t seem to charge out into the intersections like cabbies tend to do, which is a good idea in terms of reducing your chance of being hit by someone else running a red light.\n\nSuch benefits could truly spell the eventual end of old-school taxis. But the driverless vehicles are too few in number for now, which in turn means fares are still higher than Uber and Lyft.\n\nThe biggest benefit of all, which Waymo aggressively and unusually shares data about, is that robocar adoption would reduce deaths by some 72 percent. But, for whatever reasons, any major injuries caused by robo companies—including by Uber and Cruise—have led to essentially the closing down of those ventures. There is still a zero tolerance for incidents.\n\nBut Waymo is looking pretty good as the story ends, when the authors complain about their butts hurting from driving around following the robocar. Of course, that’s not a problem for the robocar driver. That driver doesn’t exist.", "author": "IcyVehicle8158", "created_time": "2025-02-09T06:20:10", "url": "https://reddit.com/r/AutonomousVehicles/comments/1il8ggf/great_magazine_reads_robocars_continue_towards_a/", "upvotes": 6, "comments_count": 0, "sentiment": "bullish", "engagement_score": 6.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ilaglv", "title": "Google is deleting the reviews of my business?", "content": "Hi! I don't know a lot about this things but I think this is the correct subreddit. Sorry if it's not. \n\nSo we have a paintball business, and last week we had around 150 reviews. Today we have 127... \nI don't think people are deleting the reviews, it doesn't make sense. \n\nWe encourage the costumers to leave a review after the game, and we mostly works on Saturday and Sunday. So between monday-friday we have 0 reviews but when the weekend comes we have around 5/8 per day. It's that messing around with Google algorithm or something to spot fake reviews? \nBtw, all the reviews we had are 5 stars, if that matters.\n\nWhat can we do to fix that? We are from a town in Spain where there is only 3 other paintball arenas so people put their eyes on the one with more reviews :( \n\n", "author": "LuisArrobaja", "created_time": "2025-02-09T08:42:08", "url": "https://reddit.com/r/SEO/comments/1ilaglv/google_is_deleting_the_reviews_of_my_business/", "upvotes": 24, "comments_count": 75, "sentiment": "neutral", "engagement_score": 174.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ilgx0c", "title": "A Super Bowl ad featuring Google’s Gemini AI contained a whopper of a mistake about cheese", "content": "🧀 ", "author": "Chilango615", "created_time": "2025-02-09T15:17:35", "url": "https://reddit.com/r/nottheonion/comments/1ilgx0c/a_super_bowl_ad_featuring_googles_gemini_ai/", "upvotes": 11205, "comments_count": 279, "sentiment": "neutral", "engagement_score": 11763.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iljj1o", "title": "As a Student: AWS or AZURE?", "content": "Hi,\n\nI want to learn Cloud Computing with a focus on Cloud Security. I do not have very good idea about cloud computing. Where to start? Which one is easier to learn? I tried AWS but got confused so much, that I left it in first few hours. Is Azure easy to learn?\n\nThanks!", "author": "souravpadhi89", "created_time": "2025-02-09T17:09:32", "url": "https://reddit.com/r/cloudcomputing/comments/1iljj1o/as_a_student_aws_or_azure/", "upvotes": 18, "comments_count": 30, "sentiment": "neutral", "engagement_score": 78.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ill855", "title": "Deepseek’s AI model is ‘the best work’ out of China but the hype is ‘exaggerated,’ Google Deepmind CEO says", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-02-09T18:20:35", "url": "https://reddit.com/r/wallstreetbets/comments/1ill855/deepseeks_ai_model_is_the_best_work_out_of_china/", "upvotes": 1372, "comments_count": 270, "sentiment": "neutral", "engagement_score": 1912.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ill8nh", "title": "Deepseek’s AI model is ‘the best work’ out of China but the hype is 'exaggerated,' Google Deepmind CEO says", "content": "", "author": "dtrannn666", "created_time": "2025-02-09T18:21:07", "url": "https://reddit.com/r/singularity/comments/1ill8nh/deepseeks_ai_model_is_the_best_work_out_of_china/", "upvotes": 384, "comments_count": 187, "sentiment": "neutral", "engagement_score": 758.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}]