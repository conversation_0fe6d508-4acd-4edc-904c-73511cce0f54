#!/usr/bin/env python3
"""
Reddit账户访问测试
快速测试当前Reddit账户是否能正常访问
"""

import os
import praw
import prawcore
from dotenv import load_dotenv
from datetime import datetime

# 加载环境变量
load_dotenv()

def test_reddit_account():
    """测试Reddit账户访问"""
    
    print("=== Reddit账户访问测试 ===")
    print(f"测试时间: {datetime.now()}")
    
    # 获取配置
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    print(f"\n配置检查:")
    print(f"  Client ID: {client_id[:10]}..." if client_id else "  Client ID: 未设置")
    print(f"  User Agent: {user_agent}")
    print(f"  Username: {username}")
    print(f"  Password: {'已设置' if password else '未设置'}")
    
    if not all([client_id, client_secret, username, password]):
        print("❌ 配置不完整")
        return False
    
    try:
        # 创建Reddit实例
        print(f"\n步骤1: 创建Reddit连接...")
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent,
            username=username,
            password=password
        )
        print("✓ Reddit实例创建成功")
        
        # 测试用户认证
        print(f"\n步骤2: 测试用户认证...")
        try:
            user = reddit.user.me()
            print(f"✓ 用户认证成功")
            print(f"  用户名: {user.name}")
            print(f"  账户创建时间: {datetime.fromtimestamp(user.created_utc)}")
            print(f"  Link Karma: {user.link_karma}")
            print(f"  Comment Karma: {user.comment_karma}")
            print(f"  账户状态: {'正常' if not user.is_suspended else '已暂停'}")
            
            if user.is_suspended:
                print("❌ 账户已被暂停，无法继续测试")
                return False
                
        except prawcore.exceptions.ResponseException as e:
            if e.response.status_code == 401:
                print("❌ 认证失败 - 用户名或密码错误")
            elif e.response.status_code == 403:
                print("❌ 访问被禁止 - 账户可能被限制")
            else:
                print(f"❌ 认证失败: HTTP {e.response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 认证失败: {e}")
            return False
        
        # 测试基本API访问
        print(f"\n步骤3: 测试基本API访问...")
        try:
            # 测试访问一个简单的子版块
            subreddit = reddit.subreddit('test')
            display_name = subreddit.display_name
            print(f"✓ 子版块访问成功: r/{display_name}")
            
        except Exception as e:
            print(f"❌ 子版块访问失败: {e}")
            return False
        
        # 测试获取帖子 (这里可能出现403)
        print(f"\n步骤4: 测试获取帖子...")
        try:
            posts = list(subreddit.new(limit=1))
            if posts:
                post = posts[0]
                print(f"✓ 帖子获取成功")
                print(f"  标题: {post.title[:50]}...")
                print(f"  作者: {post.author}")
                print(f"  分数: {post.score}")
                return True
            else:
                print("⚠ 没有找到帖子，但API访问正常")
                return True
                
        except prawcore.exceptions.ResponseException as e:
            if e.response.status_code == 403:
                print("❌ 获取帖子被禁止 (403错误)")
                print("  这是当前遇到的主要问题")
                return False
            elif e.response.status_code == 429:
                print("❌ 请求过于频繁 (429错误)")
                print("  建议等待后重试")
                return False
            else:
                print(f"❌ 获取帖子失败: HTTP {e.response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取帖子失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_multiple_subreddits():
    """测试多个子版块的访问"""
    
    print(f"\n=== 多子版块访问测试 ===")
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    try:
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent,
            username=username,
            password=password
        )
        
        # 测试目标子版块
        test_subreddits = ['stocks', 'investing', 'wallstreetbets', 'test', 'python']
        success_count = 0
        
        for sub_name in test_subreddits:
            print(f"\n测试 r/{sub_name}:")
            try:
                subreddit = reddit.subreddit(sub_name)
                
                # 测试基本信息
                display_name = subreddit.display_name
                print(f"  ✓ 基本信息: {display_name}")
                
                # 测试获取帖子
                posts = list(subreddit.new(limit=1))
                if posts:
                    post = posts[0]
                    print(f"  ✓ 帖子获取成功: {post.title[:30]}...")
                    success_count += 1
                else:
                    print(f"  ⚠ 没有帖子")
                    
            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 403:
                    print(f"  ❌ 403错误 - 访问被禁止")
                elif e.response.status_code == 429:
                    print(f"  ❌ 429错误 - 请求过于频繁")
                else:
                    print(f"  ❌ HTTP {e.response.status_code}错误")
            except Exception as e:
                print(f"  ❌ 错误: {e}")
        
        print(f"\n测试结果: {success_count}/{len(test_subreddits)} 个子版块可访问")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 多子版块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    
    # 基本账户测试
    basic_success = test_reddit_account()
    
    if basic_success:
        print(f"\n✅ 基本测试通过 - Reddit账户可正常访问")
        
        # 进行多子版块测试
        multi_success = test_multiple_subreddits()
        
        if multi_success:
            print(f"\n✅ 多子版块测试通过 - 可以开始数据收集")
        else:
            print(f"\n⚠ 多子版块测试失败 - 可能存在临时限制")
            
    else:
        print(f"\n❌ 基本测试失败")
        print(f"\n可能的原因:")
        print(f"1. 临时的Reddit API限制 (最常见)")
        print(f"2. IP地址被临时限制")
        print(f"3. 账户达到API配额限制")
        print(f"4. Reddit服务器端问题")
        
        print(f"\n建议的解决方案:")
        print(f"1. 等待1-2小时后重试")
        print(f"2. 尝试更换网络环境")
        print(f"3. 检查Reddit官方状态页面")
        print(f"4. 如果问题持续，考虑联系Reddit支持")
    
    print(f"\n测试完成时间: {datetime.now()}")

if __name__ == '__main__':
    main()
