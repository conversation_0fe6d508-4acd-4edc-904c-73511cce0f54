[{"platform": "reddit", "post_id": "reddit_1i65k4j", "title": "Ads on youtube have reached a new level of bad", "content": "I was just scrolling on YouTube shorts and saw this", "author": "Successful_Koala3674", "created_time": "2025-01-21T00:17:32", "url": "https://reddit.com/r/youtube/comments/1i65k4j/ads_on_youtube_have_reached_a_new_level_of_bad/", "upvotes": 0, "comments_count": 14, "sentiment": "bearish", "engagement_score": 28.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i65pxt", "title": "Ready for FIRE if needed", "content": "As I work in one of the big tech companies where stability is now practically nonexistent, I want to be sure I am prepared in case of layoffs (or burnouts from my side).\n\nCurrently I'm 50 and planning to work for at least 5 more years. I love my job and the team I work with and if it was for me, I would not retire anytime soon but.. That's not fully under my control.\n\nCurrent asset is distributed as following:\n\n* 3.25M invested between:\n   * 2.5M in stock market (NVIDIA, Google, Tesla and 35% on VOO\n   * 102K in a deferred annuity allocated 78% domestic and 22 foreign\n      * Note that if I start withdrawing from this account before the age of 59 and ½ I incur in a 10% penalty\n   * 570K in 401K\n   * 26K in HSA (VOO)\n\nMy current strategy is basically not to touch:\n\n* the deferred annuity\n* the 401K\n* the HSA.\n\nKeep diversify the 2.5M in stocks until I want to (or I have to) stop working and, at that point, use the investments as the primary source of income by, at that point, starting with an re-investment strategy (being sure I minimize taxes) which means: selling everything (hoping a that point it reach a value higher than 2.5) and buy Tresure bonds.\n\nAs I'm planning to retire in a country in Europe (wife has dual citizenship and I can get the same) the current 4.625% 20 year bond are plenty for us to live if we are able to buy at least 2.5M of bonds.\n\nIs this a decent strategy? Are there better options to take to minimize taxes and be ready to switch from stock index market to a source of more reliable yearly income?\n\nThanks for all the help!", "author": "knows_notting", "created_time": "2025-01-21T00:24:42", "url": "https://reddit.com/r/Fire/comments/1i65pxt/ready_for_fire_if_needed/", "upvotes": 0, "comments_count": 15, "sentiment": "bullish", "engagement_score": 30.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i6d48f", "title": "How can you build insights across Google Analytics, HubSpot, Salesforce, Stripe, and Snowflake - without hopping multiple tools?", "content": "I work in SaaS and consistently need to connect data points from various stages of the sales funnel, from site visitors and lead generation to sales opportunities, revenue, and product usage.   \n  \nTypically, I have used SQL querying tools, Spreadsheets, BI Tools for Dashboards, Notebooks for exploration and a lot more. Is there a way I can sync data from these tools, explore when I need to and present dashboards and work on all this collaboratively with my team (a lot of ad-hoc Qs keep coming up from different stakeholders).   \n  \nThis process is static and cumbersome to replicate regularly.   \n  \nI'm also building something myself to solve for this and would like to know what methods are effective for integrating and analyzing these diverse data streams more dynamically. ", "author": "Better-Department662", "created_time": "2025-01-21T07:12:13", "url": "https://reddit.com/r/analytics/comments/1i6d48f/how_can_you_build_insights_across_google/", "upvotes": 5, "comments_count": 23, "sentiment": "neutral", "engagement_score": 51.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i6h4nj", "title": "What to do if nothing works after Google’s anti-scrapers update? How to do SEO now?", "content": "Hi, guys. After Google’s anti-scrapers update, I can’t properly track my site’s rankings. Even the tools aren’t working. What should I do? How are your SEO teams adapting to this?\n\nHelp!", "author": "JosephineAllard_SEO", "created_time": "2025-01-21T12:07:13", "url": "https://reddit.com/r/marketing/comments/1i6h4nj/what_to_do_if_nothing_works_after_googles/", "upvotes": 10, "comments_count": 14, "sentiment": "neutral", "engagement_score": 38.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i6ha6p", "title": "Best solution for getting options data into Google Sheets?", "content": "What app/function do folks use/recommend to integrate options data (current bid, mid, ask, Greeks) real time into google sheets? Or have folks tried and decided programmatic approach with python and such is the better option? Which service/app/api works reliably please? Paid services/subscriptions totally fine. ", "author": "Responsible-Skirt-98", "created_time": "2025-01-21T12:16:22", "url": "https://reddit.com/r/options/comments/1i6ha6p/best_solution_for_getting_options_data_into/", "upvotes": 3, "comments_count": 15, "sentiment": "neutral", "engagement_score": 33.0, "source_subreddit": "options", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i6ujg3", "title": "Can Waymo Pivot to a Camera-only approach?", "content": "I am trying to understand the autonomous driving space better to inform some investment strategy. I understand that the use of radar systems and LIDAR adds some safety to overcome certain shortcomings of a camera only approach. However I am also concerned that if a camera-only approach proves safe \"enough\", it may be accepted legally and in that case may have an overwhelming advantage in terms of cost per mile and scalability. So the big question is this: Lets say TSLA does indeed get approval for fully autonomous camera-only based driving, would a company like Waymo be able to pivot to a similar approach? They already have the data from both Camera footage as well as radar/ lidar. Can the datasets be retrained to attempt to produce the same accuracy from camera-only data? If so it would seem that Waymo would be a good bet because its much easier to peel down the sensors needed ( since you already have the data with more sensors) than to create datasets of sensors you never installed ( If Camera only doesn't work then TSLA will never have the Radar/ Lidar data it needs?). ", "author": "oikk01", "created_time": "2025-01-21T21:57:09", "url": "https://reddit.com/r/SelfDrivingCars/comments/1i6ujg3/can_waymo_pivot_to_a_cameraonly_approach/", "upvotes": 0, "comments_count": 21, "sentiment": "bearish", "engagement_score": 42.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}]