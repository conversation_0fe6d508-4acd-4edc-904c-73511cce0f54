[{"platform": "reddit", "post_id": "reddit_1il2us6", "title": "\"Search image with google\" returns error 413 request entity too large", "content": "Hello, I often use the \"Search image with google\" option when right clicking on an image to find higher quality versions or slightly different versions of an image. I've had trouble with it switching to google lens in the past but was able to restore the \"Search image with google\" option thanks to you lovely redditors. However today the option is still there but when i click it, it takes me to this error 413 page. Is there a work around or is this truly the end of this incredibly useful feature?\n\nhttps://preview.redd.it/v6pt6lajj0ie1.png?width=1386&format=png&auto=webp&s=5d274b06ab6ec6827b7b55c1fbd16840418d9ad1\n\n", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-02-09T01:05:55", "url": "https://reddit.com/r/chrome/comments/1il2us6/search_image_with_google_returns_error_413/", "upvotes": 116, "comments_count": 94, "sentiment": "neutral", "engagement_score": 304.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1il3mho", "title": "Google reviews disappearing", "content": "95 disappeared in the last 2 days, all organic and all 5 stars, what is going on ", "author": "Expensive-Can9669", "created_time": "2025-02-09T01:45:21", "url": "https://reddit.com/r/smallbusiness/comments/1il3mho/google_reviews_disappearing/", "upvotes": 6, "comments_count": 40, "sentiment": "neutral", "engagement_score": 86.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1il8ggf", "title": "Great Magazine Reads: Robocars continue towards a path of safer U.S. roads", "content": "[https://popculturelunchbox.substack.com/p/great-magazine-reads-robocars-continue](https://popculturelunchbox.substack.com/p/great-magazine-reads-robocars-continue)\n\nIt’s been a few years now since I was regularly quoted in the media and spoke a lot about autonomous vehicles. But with my time away from the spotlight, it’s good to see in the January/February issue of WIRED magazine that AVs are still in the pipeline and have begun taking on a much cooler name: robocars.\n\nWhile not many places are getting to experience the wonders of the safer world that these kinds of devices could bring, the article mentions that places like Los Angeles, Phoenix, San Francisco, and Wuhan, China are well along the way with the vehicles, and the people in those cities barely blink an eyeball to their existence anymore.\n\nThe authors’ goal for the article was to follow a single Waymo robotaxi throughout a whole workday in San Francisco to see how it operated and to also interview as many of its passengers as they could.\n\nThose who rode in the robocars reported the same kinds of experiences I had back at a future-transportation conference in Los Angeles about a decade ago. It starts out feeling like a cool amusement park ride and quickly shifts to being the opposite. No thrills. No lurches. Just smoothly and slowly moving along.\n\nOne of the first observations of the authors is that the Waymos spend a good bit of time going to their recharging lots to power back up, with no passengers in tow. The logical question about all this so-called deadheading? “Is Waymo going to make congestion worse by filling the streets with 5,000-pound contraptions that are completely empty?”\n\nAn urban-planning professor interviewed in the article says that the use cases of the past 15 years of Uber and Lyft are starting to offer a pretty good idea of what robocars might do for congestion.\n\n“Research suggests that, in fact, Uber and Lyft brought more private cars onto city streets, partly because drivers acquired new ones to gig for the platforms. All that led to—you guessed it—more congestion. No one will be buying a new car to gig for Waymo, of course. But there could be more gridlock mainly because of the way cities fail to price roads. In busy downtown, driving is free. It’s the price of parking that typically pressures car owners to take some other mode of transit. Trouble is, robots don’t need to park downtown. It’s a recipe for endless traffic.”\n\nBut back to the task at hand. A couple seemingly excited to be taking their robo Waymo, after they reached their destination near City Lights bookstore, said they loved that there was no stranger in the car and how smooth the ride had been. Others said it was not “slow and stupid,” like they thought it would be. The cars don’t seem to charge out into the intersections like cabbies tend to do, which is a good idea in terms of reducing your chance of being hit by someone else running a red light.\n\nSuch benefits could truly spell the eventual end of old-school taxis. But the driverless vehicles are too few in number for now, which in turn means fares are still higher than Uber and Lyft.\n\nThe biggest benefit of all, which Waymo aggressively and unusually shares data about, is that robocar adoption would reduce deaths by some 72 percent. But, for whatever reasons, any major injuries caused by robo companies—including by Uber and Cruise—have led to essentially the closing down of those ventures. There is still a zero tolerance for incidents.\n\nBut Waymo is looking pretty good as the story ends, when the authors complain about their butts hurting from driving around following the robocar. Of course, that’s not a problem for the robocar driver. That driver doesn’t exist.", "author": "IcyVehicle8158", "created_time": "2025-02-09T06:20:10", "url": "https://reddit.com/r/AutonomousVehicles/comments/1il8ggf/great_magazine_reads_robocars_continue_towards_a/", "upvotes": 6, "comments_count": 0, "sentiment": "bullish", "engagement_score": 6.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ilaglv", "title": "Google is deleting the reviews of my business?", "content": "Hi! I don't know a lot about this things but I think this is the correct subreddit. Sorry if it's not. \n\nSo we have a paintball business, and last week we had around 150 reviews. Today we have 127... \nI don't think people are deleting the reviews, it doesn't make sense. \n\nWe encourage the costumers to leave a review after the game, and we mostly works on Saturday and Sunday. So between monday-friday we have 0 reviews but when the weekend comes we have around 5/8 per day. It's that messing around with Google algorithm or something to spot fake reviews? \nBtw, all the reviews we had are 5 stars, if that matters.\n\nWhat can we do to fix that? We are from a town in Spain where there is only 3 other paintball arenas so people put their eyes on the one with more reviews :( \n\n", "author": "LuisArrobaja", "created_time": "2025-02-09T08:42:08", "url": "https://reddit.com/r/SEO/comments/1ilaglv/google_is_deleting_the_reviews_of_my_business/", "upvotes": 24, "comments_count": 75, "sentiment": "neutral", "engagement_score": 174.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ild93q", "title": "News: Sales nosedive in German and Netherlands due to Tesla shame", "content": "Sources:\n\nFortune: Tesla demand is nosediving in EV-friendly Europe amid Elon Musk's endorsement of the far right.\n\nYahoo: Nearly a third of Elon Musk's EV-loving Dutch customers may sell their Teslas\n\nNL times: Video: Tesla showroom in The Hague vandalized with swastikas, anti-fascist texts\n\nSeeing more Tesla previous owners ending leases, selling and switching to other EVs from Reddit.", "author": "Giant-Panda-atNL", "created_time": "2025-02-09T12:01:44", "url": "https://reddit.com/r/electricvehicles/comments/1ild93q/news_sales_nosedive_in_german_and_netherlands_due/", "upvotes": 3710, "comments_count": 621, "sentiment": "bearish", "engagement_score": 4952.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ildou8", "title": "I almost got stranded with my Tesla in the middle of nowhere... and it led to building kwbuddy 🚗⚡️", "content": "Last month, I learned this lesson the hard way during a cross-country drive in Turkey. My electric vehicle's battery was running low, and despite multiple apps showing nearby charging stations, I discovered they didn't actually exist. Talk about range anxiety!\n\nThis experience revealed a critical gap in EV infrastructure information that affects thousands of drivers daily. Instead of just complaining, I decided to solve this problem.\n\nI built https://kwbuddy.com - a real-time data for EV charging stations.", "author": "onurgenes", "created_time": "2025-02-09T12:29:42", "url": "https://reddit.com/r/webdev/comments/1ildou8/i_almost_got_stranded_with_my_tesla_in_the_middle/", "upvotes": 0, "comments_count": 1, "sentiment": "neutral", "engagement_score": 2.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ildrt1", "title": "In your opinion, how would a Tesla market crash play out?", "content": "<PERSON><PERSON> is doing worse than expected, there's enough posts about this. I'm curious to know how things could play out. Like, Tesla price crashes 50% in a year, for example. People realise it's a terrible option, they bounce. Tesla heavy funds (which ones?) start dropping. \n\n<PERSON><PERSON>, the richest man on earth, borrows money against his valuable stock. If the stock isn't as valuable will he be forced to make his other companies (X, SpaceX etc) public? Obviously he'll never be poor but he would likely do anything and everything to protect his wealth, so what do you think that could entail?", "author": "patchy<PERSON>", "created_time": "2025-02-09T12:34:51", "url": "https://reddit.com/r/investing/comments/1ildrt1/in_your_opinion_how_would_a_tesla_market_crash/", "upvotes": 455, "comments_count": 479, "sentiment": "bearish", "engagement_score": 1413.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ilhfk0", "title": "RTX 5090FE Molten 12VHPWR", "content": "I guess it was a matter of time. I lucked out on 5090FE - and my luck has just run out. \n\nI have just upgraded from 4090FE to 5090FE. My PSU is Asus Loki SFX-L. The cable used was this one: [https://www.moddiy.com/products/ATX-3.0-PCIe-5.0-600W-12VHPWR-16-Pin-to-16-Pin-PCIE-Gen-5-Power-Cable.html](https://www.moddiy.com/products/ATX-3.0-PCIe-5.0-600W-12VHPWR-16-Pin-to-16-Pin-PCIE-Gen-5-Power-Cable.html)\n\nI am not distant from the PC-building world and know what I'm doing. The cable was securely fastened and clicked on both sides (GPU and PSU).\n\nI noticed the burning smell playing Battlefield 5. The power draw was 500-520W. Instantly turned off my PC - and see for yourself...\n\n1. The cable was securely fastened and clicked.\n2. The PSU and cable haven't changed from 4090FE (which was used for 2 years). Here is the previous build: [https://pcpartpicker.com/b/RdMv6h](https://pcpartpicker.com/b/RdMv6h)\n3. Noticed a melting smell, turned off the PC - and just see the photos. The problem seems to have originated from the PSU side.\n4. Loki's 12VHPWR pins are MUCH thinner than in the 12VHPWR slot on 5090FE.\n5. Current build: [https://pcpartpicker.com/b/VRfPxr](https://pcpartpicker.com/b/VRfPxr)\n\nI dunno what to do really. I will  try to submit warranty claims to Nvidia and Asus. But I'm afraid I will simply be shut down on the \"3rd party cable\" part. Fuck, man", "author": "ivan6953", "created_time": "2025-02-09T15:40:24", "url": "https://reddit.com/r/nvidia/comments/1ilhfk0/rtx_5090fe_molten_12vhpwr/", "upvotes": 14365, "comments_count": 3952, "sentiment": "neutral", "engagement_score": 22269.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ilk0fz", "title": "Is anyone else scared or concerned?", "content": "Over the past decade we've seen a remarkable and rapid increase in the price of the S&P 500 index. What are some key factors that you believe have contributed to this growth? How sustainable do you think it is moving forward?  Do you believe the fundamentals support this rise? I'd love to hear your thoughts on this! ", "author": "Jigawattts", "created_time": "2025-02-09T17:29:56", "url": "https://reddit.com/r/StockMarket/comments/1ilk0fz/is_anyone_else_scared_or_concerned/", "upvotes": 0, "comments_count": 120, "sentiment": "bullish", "engagement_score": 240.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ilkg43", "title": "AI will obsolete most young vertical SAAS startups, I will not promote", "content": "This is an unpopular opinion, but living in New York City and working with a ton of vertical SaaS startups, meaning basically database wrapper startups that engineer workflows for specific industries and specific users, what they built was at one point in time kind of innovative, or their edge was the fact that they built these like very specific workflows. And so a lot of venture capital and seed funding has gone into these types of startups. But with AI, those database wrapper startups are basically obsolete. I personally feel like all of these companies are going to have to shift like quickly to AI or watch all of their edge and what value they bring to the table absolutely evaporate. It's something that I feel like it's not currently being priced in and no one really knows how to price, but it's going to be really interesting to watch as more software becomes generated and workflows get generated.\n\nI’m not saying these companies are worth nothing, but their products need to be completely redone\n\nEDIT: for people not understanding: \n\nThe UX is completely different from traditional vertical saas. Also in real world scenarios, AI does not call the same APIs as the front end. The data handling and validation is different. It’s 50% rebuild. Then add in the technical debt, the fact that they might need a different tech stack to build agents correctly, different experience in their engineers. \n\nthe power struggles that occur inside companies that need a huge change like this could tank the whole thing alone.  \n\nIt can be done, but these companies are vulnerable. The edge they have is working with existing customers to get it right. But they basically blew millions on a tech implementation that’s not as relevant going forwards. \n\nInvestors maybe better served putting money into a fresh cap table ", "author": "Few_Incident4781", "created_time": "2025-02-09T17:48:12", "url": "https://reddit.com/r/startups/comments/1ilkg43/ai_will_obsolete_most_young_vertical_saas/", "upvotes": 100, "comments_count": 237, "sentiment": "neutral", "engagement_score": 574.0, "source_subreddit": "startups", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ilr95a", "title": "<PERSON><PERSON> crash on Full Self-Driving v13 goes viral", "content": "", "author": "dtrannn666", "created_time": "2025-02-09T22:34:31", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ilr95a/tesla_cybertruck_crash_on_full_selfdriving_v13/", "upvotes": 289, "comments_count": 311, "sentiment": "bearish", "engagement_score": 911.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}]