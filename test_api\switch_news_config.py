#!/usr/bin/env python3
"""
新闻配置切换脚本
用于在不同股票的新闻数据配置之间切换
"""

import json
import shutil
import argparse
from pathlib import Path


def switch_config(ticker: str):
    """
    切换到指定股票的新闻配置
    
    Args:
        ticker: 股票代码 (AAPL, MSFT等)
    """
    config_file = f"news_config_{ticker.lower()}.json"
    target_file = "news_config.json"
    
    if not Path(config_file).exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        # 备份当前配置
        if Path(target_file).exists():
            backup_file = f"{target_file}.backup"
            shutil.copy2(target_file, backup_file)
            print(f"✅ 当前配置已备份到: {backup_file}")
        
        # 复制新配置
        shutil.copy2(config_file, target_file)
        print(f"✅ 已切换到 {ticker.upper()} 配置")
        
        # 显示配置内容
        with open(target_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"\n📰 当前新闻配置:")
        print(f"  数据源: {', '.join(config['default_settings']['selected_sources'])}")
        print(f"  Alpha Vantage目录: {config['local_data_directories']['alpha_vantage']}")
        print(f"  时间偏移: {config['default_settings']['time_offset_days']} 天")
        
        return True
        
    except Exception as e:
        print(f"❌ 切换配置失败: {e}")
        return False


def list_available_configs():
    """列出可用的配置文件"""
    print("📋 可用的新闻配置:")
    
    config_files = list(Path(".").glob("news_config_*.json"))
    if not config_files:
        print("  未找到任何配置文件")
        return
    
    for config_file in config_files:
        ticker = config_file.stem.replace("news_config_", "").upper()
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            alpha_dir = config['local_data_directories']['alpha_vantage']
            sources = config['default_settings']['selected_sources']
            print(f"  {ticker}: {alpha_dir} (源: {', '.join(sources)})")
        except Exception as e:
            print(f"  {ticker}: 配置文件损坏 - {e}")


def test_current_config():
    """测试当前配置"""
    try:
        from src.tools.local_news_reader import get_local_multi_source_news
        
        print("🧪 测试当前配置...")
        
        # 测试MSFT数据 - 使用一个确实存在的日期
        news = get_local_multi_source_news(ticker='MSFT', date='2024-01-02', limit=3)
        
        if news:
            print(f"✅ 成功读取到 {len(news)} 条新闻")
            print("📰 示例新闻:")
            for i, n in enumerate(news[:2], 1):
                print(f"  {i}. {n.title[:60]}...")
                print(f"     来源: {n.source}, 股票: {n.ticker}")
        else:
            print("⚠️  未读取到新闻数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="新闻配置切换工具")
    parser.add_argument('ticker', nargs='?', help='股票代码 (AAPL, MSFT等)')
    parser.add_argument('--list', '-l', action='store_true', help='列出可用配置')
    parser.add_argument('--test', '-t', action='store_true', help='测试当前配置')
    
    args = parser.parse_args()
    
    if args.list:
        list_available_configs()
    elif args.test:
        test_current_config()
    elif args.ticker:
        if switch_config(args.ticker):
            print("\n🔄 建议重启应用程序以使配置生效")
    else:
        print("用法:")
        print("  python switch_news_config.py MSFT    # 切换到MSFT配置")
        print("  python switch_news_config.py AAPL    # 切换到AAPL配置")
        print("  python switch_news_config.py --list  # 列出可用配置")
        print("  python switch_news_config.py --test  # 测试当前配置")


if __name__ == "__main__":
    main()
