[{"platform": "reddit", "post_id": "reddit_1kq3rir", "title": "Big difference between google ad numbers and GA4", "content": "<PERSON><PERSON>,\n\nI’m a product analyst, and also do some marketing analysis for stakeholders.\n\nWe notice there’s a big difference between a certain conversion event in google ads (which has been set up in tag manager) and GA4. \n\nGoogle ads giving like 14 conversions, while ga4 has over 72.\n\nWhat are some places I could start looking to see where this goes wrong?", "author": "xynaxia", "created_time": "2025-05-19T05:12:46", "url": "https://reddit.com/r/marketing/comments/1kq3rir/big_difference_between_google_ad_numbers_and_ga4/", "upvotes": 1, "comments_count": 2, "sentiment": "neutral", "engagement_score": 5.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kq3w5a", "title": "Please read if you are thinking about getting Solar 🌞", "content": "I work for a solar company, where most of my day involves communicating with sales reps and customers. I also monitor system performance post-installation—and in my experience, around 80% of systems don’t deliver the results promised. And many clients reach out upset about double billing, often because they were told their electric bill would be $0 and they’d receive monthly credits from the utility company and that they’d only have to pay the bank from then on.\n\nIf you are thinking about getting a system DO YOUR RESEARCH\n\nWhat I recommend: \n\n1. Read the Bank’s Contract, Not just the Installer’s: you are paying interest!\n\nIf you’re financing your solar system—which most customers do—you need to read the bank’s contract, not the installer’s. This is especially important if you’re leasing, as about 95% of our clients are. The financing contract will outline every single payment you’ll make yearly over the life of the lease, adding the interest rate. It will also show a comparison between the system’s advertised cost (what you think you’re paying) and the actual total lifetime cost—which is more than double due to interest. \n\nFor example, one customer expected to pay $19,800 for a 14-panel system, but her total cost over 25 years added up to $41,800. \n\nIf you are able to, find your own financing, don’t use the banks they offer. Read point 8 ⬇️ \n\n2.\tRecognize Sales Reps’ True Motivation:\n\nSales representatives are focused on their commission, not your savings—and some make $30,000 to $50,000 a month from just a few installs. To close deals, many reps actively lie to customers. Three common lies I’ve seen:\n• “This program is only offered to 2-3 homes in the neighborhood.” (Falsee! they’re knocking on every door.)\n• “You’ll pay a fixed amount for the full contract term.” (Also false— there is interest!)\n• “No more paying the utility company” (False! You will most likely be double billed, even if your offset is 100%, you are still going to pay a meter fee to the utility company. Keep in mind, there will be months when your system doesn’t cover your entire consumption and you’ll have to pull from the grid)\n\n\n3. Ask About Maintenance Costs:\nSolar systems aren’t maintenance free, and repairs can be expensive. Issues will come up eventually—even minor ones. The cheapest service we’ve handled was $450, just to tighten a single panel and check performance\n\n4.\tGet Direct Contact Info:\nAlways ask for the project manager’s number or the direct contact for the solar department. Don’t settle for an office or call center number—those agents are usually not trained to handle solar-specific questions or issues.\n\n5. Speak to the Project Manager Before Installation:\nMake sure you talk directly to the project manager—or whoever is overseeing the solar department—before the system is installed. If they dodge your questions or just send you back to your sales rep, that’s a red flag. Often, they won’t give straight answers because the truth could discourage you from moving forward. \n\n6.\tIf Your regular Bill Is Under $200, Think Twice:\nBased on monitoring over 100 clients, if your current electric bill is under $200/month, solar likely won’t save you much. In many cases, you’ll end up paying more or saving as little as $20 a mont\n\n7. Not a recommendation but be aware: you are signing a contract and they’re putting a lien on your house!! \n\n8. As someone mentioned in the comments: most of this doesn’t apply to CASH deals, but what I recommend for cash deals is to go straight to an installer and be involved as much as you can in the process. Most companies use third party installers, FIND THOSE THIRD PARTIES. \n\n\nI’m speaking up because I’m tired of seeing people misled into 20+ year financial commitments based on false promises of savings. What’s worse is how often sales guys target older ppl—about 90% of our clients are over 70 and retired, making them especially vulnerable. In separate cases, our installers arrived only to find the homeowners had no memory of signing up for solar and they realize that the customers have Alzheimer’s disease. The sales guy never followed up or checked in. On 2 of those 3, the sales guy was aware that the customer had memory issues. It was disgusting to me. Maybe I’m just to morally correct or just too stupid to work on this industry but that felt terrible for me. I get happy when people cancel. Really.\n\nI speak out to help people pause, think, and truly research what they’re committing to. I work in the solar industry, but it’s hard to find meaning in what I do when I’m the one answering the phone as customers break down—angry, confused, and overwhelmed—because they were promised things that simply aren’t true. While sales reps walk away with five-figure monthly commissions, I’m the one earning less than 2k a month, left to absorb the insults and consequences. Everyone else just says: “They should’ve known better.” But I know exactly what lies were told to convince them to sign. And honestly, it feels evil.\n\nRemember people: If it sounds too good to be true is because it is.  I hope you take my advice and really look what you’re getting into. \n\n\nEdited on 05/21: I wanted to add a few extra clarification on points 1 and 2 and I also added a point 8.", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-19T05:21:24", "url": "https://reddit.com/r/solar/comments/1kq3w5a/please_read_if_you_are_thinking_about_getting/", "upvotes": 1289, "comments_count": 345, "sentiment": "bullish", "engagement_score": 1979.0, "source_subreddit": "solar", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kq87o9", "title": "In 2019, I saw a hotel being constructed and submitted it to Google Maps. This morning, I got this email.", "content": "", "author": "biebrforro", "created_time": "2025-05-19T10:22:23", "url": "https://reddit.com/r/google/comments/1kq87o9/in_2019_i_saw_a_hotel_being_constructed_and/", "upvotes": 3805, "comments_count": 77, "sentiment": "neutral", "engagement_score": 3959.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kq8l7g", "title": "Google Ads - <PERSON><PERSON><PERSON> not showing up?", "content": "Hi, i have been paying for google ads for months now. It works great but i have a problem. My favicon is not showing up!\n\n[Google AD showing](https://tinypic.host/image/Knipsel1.3Kf4NG)\n\nNo favicon or even images like the other company's.\n\n[Attached photo's campaign](https://tinypic.host/image/Knipsel3.3Kf0af)\n\n[Direct URL to favicon](https://clevair.nl/favicon.ico) (site header has been changed)\n\n[The icon is shown in the site map on Google search.](https://tinypic.host/image/Knipsel4.3KfcfO)\n\nHow do i fix the favicon and images to optimize Google Ads?\n\nThank u!", "author": "Aggravating_Pie1913", "created_time": "2025-05-19T10:46:06", "url": "https://reddit.com/r/adwords/comments/1kq8l7g/google_ads_favicon_not_showing_up/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kq8wgd", "title": "who has access to data stored on cloud compute", "content": "I'm curious—if you store data on Google Cloud or other cloud providers, do internal engineers have direct access to that data? Additionally, how challenging is it to modify the data once it's stored?", "author": "OkOne7613", "created_time": "2025-05-19T11:04:50", "url": "https://reddit.com/r/cloudcomputing/comments/1kq8wgd/who_has_access_to_data_stored_on_cloud_compute/", "upvotes": 6, "comments_count": 13, "sentiment": "neutral", "engagement_score": 32.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kq9zng", "title": "google maps gets dumber daily", "content": "It's pathetic. It is unsafe. The world is due to recognize that google is making them dumber. ", "author": "Chi_CoffeeDogLover", "created_time": "2025-05-19T12:06:03", "url": "https://reddit.com/r/GoogleMaps/comments/1kq9zng/google_maps_gets_dumber_daily/", "upvotes": 93, "comments_count": 27, "sentiment": "neutral", "engagement_score": 147.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kqdbo4", "title": "Is building a Blog worth it going forward? Chat GPT = New Google or Blog = smart", "content": "Is it worth it to create a blog anymore? \n\nI wanted to generate organic traffic so I can affiliate link, drop my own products and help local businesses backlink/brand identification. \n\nCurious what you see going forward in regard to internet usage and function? ", "author": "Smart_Examination146", "created_time": "2025-05-19T14:37:48", "url": "https://reddit.com/r/Entrepreneur/comments/1kqdbo4/is_building_a_blog_worth_it_going_forward_chat/", "upvotes": 11, "comments_count": 15, "sentiment": "bearish", "engagement_score": 41.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kqe3sd", "title": "Dedicated Google Ad Specialist Shit the Bed", "content": "Like the title said, my Dedicated Google Ad Specialist not only shit the bed, but is not MIA. How can I get in touch with someone at Google PPC to get a new \"specialist\"?", "author": "johnmaggio420", "created_time": "2025-05-19T15:09:37", "url": "https://reddit.com/r/PPC/comments/1kqe3sd/dedicated_google_ad_specialist_shit_the_bed/", "upvotes": 27, "comments_count": 40, "sentiment": "neutral", "engagement_score": 107.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kqfdxx", "title": "Game Ready & Studio Driver 576.52 FAQ/Discussion", "content": "# GeForce Hotfix Display Driver version 576.66 has been released. [Please visit this thread for discussion](https://www.reddit.com/r/nvidia/comments/1l2o80a/geforce_hotfix_display_driver_version_57666/). This WHQL driver thread will be locked.\n\nGeForce Hotfix Display Driver version 576.66 is based on our latest Game Ready Driver 576.52.  \n   \nThis Hotfix addresses the following:\n\n* Dune: Awakening may crash during gameplay \\[5273568\\]\n* EA Sports FC 25 may crash during gameplay \\[5251937\\]\n* \\[RTX 50 series\\] Dragons Dogma 2 displays shadow flicker \\[5252205\\]\n* \\[RTX 50 series\\] Video playback in a web browser may show brief red/green flash corruption \\[5241341\\]\n* Clair Obscur: Expedition 33 may crash \\[5283401\\]\n\nA GeForce driver is an incredibly complex piece of software, We have an army of software engineers constantly adding features and fixing bugs. These changes are checked into the main driver branches, which are eventually run through a massive QA process and released.\n\nSince we have so many changes being checked in, we usually try to align driver releases with significant game or product releases. This process has served us pretty well over the years but it has one significant weakness. Sometimes a change that is important to many users might end up sitting and waiting until we are able to release the driver.\n\n**The GeForce Hotfix driver is our way to trying to get some of these fixes out to you more quickly. These drivers are basically the same as the previous released version, with a small number of additional targeted fixes. The fixes that make it in are based in part on your feedback in the Driver Feedback threads and partly on how realistic it is for us to quickly address them. These fixes (and many more) will be incorporated into the next official driver release, at which time the Hotfix driver will be taken down.**\n\n**To be sure, these Hotfix drivers are beta, optional and provided as-is. They are run through a much abbreviated QA process. The sole reason they exist is to get fixes out to you more quickly. The safest option is to wait for the next WHQL certified driver. But we know that many of you are willing to try these out. As a result, we only provide NVIDIA Hotfix drivers through our NVIDIA Customer Care support site.**  \n   \n[Click here](https://international.download.nvidia.com/Windows/576.66hf/576.66-desktop-notebook-win10-win11-64bit-international-dch.hf.exe) to download the GeForce Hotfix display driver version 576.66 for Windows 10 x64 / Windows 11 x64\n\n# Reminders:\n\n* Hotfix driver needs to be downloaded via the download link on this post below or from the [NVIDIA Customer Support article here](https://nvidia.custhelp.com/app/answers/detail/a_id/5667/). **This driver will not be available to download via NV App or Driver Search**. The fixes contained within this Hotfix driver will be included in the next full WHQL release. [Click here](https://international.download.nvidia.com/Windows/576.66hf/576.66-desktop-notebook-win10-win11-64bit-international-dch.hf.exe) to download the 576.66 Hotfix Driver\n* The Hotfix driver is a very targeted driver release to fix specific issues and **you should only expect fixes related to the items they listed**. The driver itself is using WHQL 576.52 as a base so if the issue is not specifically listed in the Hotfix lists, then it's not going to be fixed and you'll have to wait for the next full release for more fixes.\n* Reminder that if you have driver related issues, please send a driver report directly to NVIDIA with detailed information. This is the best way to get the issue recognized, replicated, and solved. [Link to driver bug report form here](https://forms.gle/kJ9Bqcaicvjb82SdA)\n\n\\---------------------------\n\n# Game Ready & Studio Driver 576.52 has been released.\n\n# If you cannot find the driver in NVIDIA Website Search or not showing in NVIDIA App, please give it time to propagate.\n\n**Game Ready Driver Article Here**: [Link Here](https://www.nvidia.com/en-us/geforce/news/rtx-5060-dune-awakening-f1-25-geforce-game-ready-driver/)\n\n**Game Ready Driver Direct Download Link**: [Link Here](https://us.download.nvidia.com/Windows/576.52/576.52-desktop-win10-win11-64bit-international-dch-whql.exe)\n\n**Studio Driver Article Here**: [Link Here](https://blogs.nvidia.com/blog/rtx-ai-garage-computex-microsoft-build)\n\n**Studio Driver Direct Download Link**: [Link Here](https://us.download.nvidia.com/Windows/576.52/576.52-desktop-win10-win11-64bit-international-nsd-dch-whql.exe)\n\n# New feature and fixes in driver 576.52:\n\n# Game Ready\n\nThis new Game Ready Driver provides the best gaming experience for the latest new games supporting DLSS 4 technology including F1 25 and Dune: Awakening, as well as the Full Ray Tracing update for NARAKA: BLADEPOINT.\n\n# Applications\n\nThe May NVIDIA Studio Driver provides support for the new GeForce RTX 5060 desktop and laptop GPUs. In addition, this release offers optimal support for the latest new creative applications and updates including Topaz Video AI releasing Starlight Mini, Chaos Vantage introducing support for Shader Execution Reordering (SER), Bilibili adding Maxine Video Effects SDK, and DLSS 4 support coming to Chaos Enscape and Autodesk VRED.\n\n# Gaming Technology\n\nAdds support for GeForce RTX 5060 desktop and laptop GPUs\n\n# Fixed Gaming Bugs\n\n* **FIXED** \\[F1 23/F1 24\\] Game crashes at the end of a race \\[5240429\\]\n* **FIXED** \\[Diablo II Resurrected\\] Game displays black screen corruption when using DLSS \\[5264112\\]\n* **FIXED** \\[SCUM\\] Game may crash after updating to R575 drivers \\[5257319\\]\n* **FIXED** Shader disk cache will not be created with certain games if OS username contains unicode characters \\[5274587\\]\n\n# Fixed General Bugs\n\n* **FIXED** \\[Lumion 12\\] Missing certain UI components \\[5213228\\]\n* **FIXED** \\[Varjo XR3\\] Varjo XR3 HMD is not working on RTX 50 series GPUs \\[5173753\\]\n* **FIXED** \\[Notebook\\] GeForce RTX 50 series TGP limit may be clipped earlier \\[5170771\\]\n   * This is listed as Fixed in the [GeForce Forum 576.52 post](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/565270/geforce-grd-57652-feedback-thread-released-51925/)\n\n# Open Issues\n\n**Includes additional open issues from** [GeForce Forums](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/565270/geforce-grd-57652-feedback-thread-released-51925/)\n\n* Flickering/corruption around light sources in Ghost of Tsushima Directors Cut \\[5138067\\]\n* Cyberpunk 2077 will crash when using Photo Mode to take a screenshot with path tracing enabled \\[5076545\\]\n* **576.66 Hotfix Fixed** \\- ~~EA Sports FC 25 may crash during gameplay \\[5251937\\]~~\n* \\[Forza Horizon 5\\] Game may crash after extended gameplay \\[5131160\\]\n   * Per u/m_w_h comment [here](https://www.reddit.com/r/nvidia/comments/1kqfdxx/comment/mtalvtm/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button)\n      * IIRC the Forza Horizon 5 (FH5) issue is a game bug related to FH5 not tracking evicted resources correctly. If that's the case, Nvidia could implement a workaround in upcoming drivers but the issue is actually game related and needs to be addressed by the game developer.\n      * Potential workaround(s) in the meantime:\n      * Set Forza Horizon 5 profile flag *0x006D6197* to *0xA2B53761* ^(credit Guzz) using Nvidia Profile Inspector. Note that Nvidia Profile Inspector's *'Show unknown settings from NVIDIA predefined profiles'* needs to be ticked to see that flag.\n      * Revert to driver 561.09 or earlier, evicted resources in that driver are handled differently and will result in slowdown/leaks rather than crash\n* **576.66 Hotfix Fixed** ~~\\[RTX 50 series\\] Dragons Dogma 2 displays shadow flicker \\[5252205\\]~~\n* **576.66 Hotfix Fixed** ~~\\[RTX 50 series\\] Video playback in a web browser may show brief red/green flash corruption \\[5241341\\]~~\n* Wuthering Waves may randomly crash during gameplay after updating to R575 drivers \\[5259963\\]\n* \\[RTX 50 series\\] Enshrouded crashes after launching game \\[5279848\\]\n* \\[NVIDIA App\\] Adding an unsupported app to NVIDIA App and enabling Smooth Motion forces it globally to other apps \\[5243686\\]\n* \\[RTX 50 series\\]\\[Battlefeld 2042\\] Random square artifacts may appear around lights during gameplay \\[5284105\\]\n* Changing a setting in the \"NVIDIA Control Panel\" -> \"Manage 3D Settings\" may trigger shader disk cache rebuild \\[5282396\\]\n* \\[Gray Zone Warfare\\] Game may crash on startup \\[5284518\\]\n* \\[RTX 50 series\\] Bugcheck when attempting to launch Twinmotion \\[5282285\\]\n* \\[RTX 50 series\\] Monster Hunter World may crash when playing in DX12 mode \\[5325098\\]\n* \\[DirectX 12\\] Intermittent crash may be observed in certain games when hardware-accelerated GPU scheduling is disabled \\[5327650\\] -> To enable Hardware-Accelerated GPU Scheduling, open the Windows Settings -> System -> Display -> Graphics -> Advanced Graphics Settings and toggle Hardware-Accelerated GPU scheduling to \"On\"\n\nPartial freezing/black screen when alt-tabbing from game/desktop after upgrading to Windows 11 24H2:  \n[https://www.reddit.com/r/Windows11/comments/1kgp7ar/cause\\_and\\_solution\\_to\\_windows\\_24h2\\_related/](https://www.reddit.com/r/Windows11/comments/1kgp7ar/cause_and_solution_to_windows_24h2_related/)\n\n# Driver Downloads and Tools\n\n**Information & Documentation**\n\n* Driver Download Page: [Nvidia Download Page](https://www.nvidia.com/Download/Find.aspx?lang=en-us)\n* Latest Game Ready Driver: 576.52 WHQL - [Game Ready Driver Release Notes](https://us.download.nvidia.com/Windows/576.52/576.52-win11-win10-release-notes.pdf)\n* Latest Studio Driver: 576.52 WHQL - [Studio Driver Release Notes](https://us.download.nvidia.com/Windows/576.52/576.52-win10-win11-nsd-release-notes.pdf)\n\n**Feedback & Discussion Forums**\n\n* **Submit driver feedback directly to NVIDIA:** [**Link Here**](https://forms.gle/kJ9Bqcaicvjb82SdA)\n* NVIDIA 576.52 Driver Forum: [Link Here](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/565270/geforce-grd-57652-feedback-thread-released-51925/)\n* r/NVIDIA Discord Driver Feedback: [Invite Link Here](https://discord.gg/y3TERmG)\n\n**Having Issues with your driver and want to fully clean the driver? Use DDU (Display Driver Uninstaller)**\n\n* DDU Download: [Source 1](https://www.wagnardsoft.com/) or [Source 2](http://www.guru3d.com/files-details/display-driver-uninstaller-download.html)\n* DDU Guide: [Guide Here](https://docs.google.com/document/d/1xRRx_3r8GgCpBAMuhT9n5kK6Zse_DYKWvjsW0rLcYQ0/edit)\n* DDU/WagnardSoft Patreon: [Link Here](https://www.patreon.com/wagnardsoft)\n\n**Before you start - Make sure you Submit Feedback for your Nvidia Driver Issue -** [**Link Here**](https://www.nvidia.com/en-us/geforce/forums/game-ready-drivers/13/564384/geforce-grd-57628-feedback-thread-released-43025/)\n\n**There is only one real way for any of these problems to get solved**, and that’s if the Driver Team at Nvidia knows what those problems are. So in order for them to know what’s going on it would be good for any users who are having problems with the drivers to [Submit Feedback](https://forms.gle/kJ9Bqcaicvjb82SdA) to Nvidia. A guide to the information that is needed to submit feedback can be found [here](http://nvidia.custhelp.com/app/answers/detail/a_id/3141).\n\n**Additionally, if you see someone having the same issue you are having in this thread, reply and mention you are having the same issue. The more people that are affected by a particular bug, the higher the priority that bug will receive from NVIDIA!!**\n\n**Common Troubleshooting Steps**\n\n* Be sure you are on the latest build of Windows\n* Please visit the following link for [DDU guide](https://goo.gl/JChbVf) which contains full detailed information on how to do Fresh Driver Install.\n* If your driver still crashes after DDU reinstall, try going to Go to Nvidia Control Panel -> Managed 3D Settings -> Power Management Mode: Prefer Maximum Performance\n\n**Common Questions**\n\n* **Is it safe to upgrade to <insert driver version here>?** *Fact of the matter is that the result will differ person by person due to different configurations. The only way to know is to try it yourself. My rule of thumb is to wait a few days. If there’s no confirmed widespread issue, I would try the new driver.*\n* **Bear in mind that people who have no issues tend to not post on Reddit or forums. Unless there is significant coverage about specific driver issue, chances are they are fine. Try it yourself and you can always DDU and reinstall old driver if needed.**\n* **My color is washed out after upgrading/installing driver. Help!** *Try going to the Nvidia Control Panel -> Change Resolution -> Scroll all the way down -> Output Dynamic Range = FULL.*\n* **My game is stuttering when processing physics calculation** *Try going to the Nvidia Control Panel and to the Surround and PhysX settings and ensure the PhysX processor is set to your GPU*\n\nRemember, driver codes are extremely complex and there are billions of different possible configurations between hardware and software. Driver will never be perfect and there will always be issues for some people. Two people with the same hardware configuration might not have the same experience with the same driver versions. Again, I encourage folks who installed the driver to post their experience here good or bad.", "author": "Nestledrink", "created_time": "2025-05-19T16:01:10", "url": "https://reddit.com/r/nvidia/comments/1kqfdxx/game_ready_studio_driver_57652_faqdiscussion/", "upvotes": 471, "comments_count": 1525, "sentiment": "bearish", "engagement_score": 3521.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kqg1as", "title": "Google \"Loves Freshness\" is debunked by AHrefs", "content": "You've read it a million times be wannabe SEO \"influencers\" that Google \"loves freshness\" or updates = ranking. Again, Ahrefs with their copy of the Google web have debunked what most of us already knew:\n\nSource: [https://ahrefs.com/blog/how-long-does-it-take-to-rank-in-google-and-how-old-are-top-ranking-pages/](https://ahrefs.com/blog/how-long-does-it-take-to-rank-in-google-and-how-old-are-top-ranking-pages/)\n\nVia u/patrickstox on [X](https://x.com/patrickstox/status/1924498763831205992)\n\n>\n\nBack in 2017, we conducted a [study](https://ahrefs.com/blog/how-long-does-it-take-to-rank/) to answer a simple yet important question: How old are the top-ranking pages in Google? The results were eye-opening and became one of our most-referenced data studies.\n\n>Clients and stakeholders often ask, “How long till my website (page) ranks [on top of Google](https://ahrefs.com/blog/how-to-get-on-top-of-google-search/)?” You could say “it depends” and give a lecture on all the variables like quality of the content, website strength, resources, competition… or you can use the data below.\n\n>I want to give a huge thanks to our data scientist [<PERSON><PERSON><PERSON><PERSON>](https://sg.linkedin.com/in/xibeijia-guan) for doing all the hard data parts of this study, and to our CMO [Tim Soulo](https://ahrefs.com/blog/author/tim-soulo/) for his input. Let’s dig in.\n\n>Key takeaways\n\n>Only 1.74% of newly published pages rank in the top 10 within a year (down from [5.7% in 2017](https://ahrefs.com/blog/how-long-does-it-take-to-rank/)).\n\n>40.82% of pages that ranked in the top 10 did so within 1 month.\n\n>It can take less time to rank for high-volume keywords now compared to 2017.\n\n>It takes longer to rank for high-volume keywords than low-volume ones.\n\n>72.9% of pages in Google’s top 10 are more than 3 years old (up from 59% in 2017).\n\n>The average #1 ranking page is 5 years old (up from 2 years old in 2017).\n\nEdit: Adding Google's past and consistent comments on Fresh\n\nThis is Google on Fresh:\n\n# Google: We Do Not Favor Fresh Content\n\n[https://www.seroundtable.com/google-does-not-favor-fresh-content-26243.html](https://www.seroundtable.com/google-does-not-favor-fresh-content-26243.html)\n\n  \n", "author": "WebLinkr", "created_time": "2025-05-19T16:26:49", "url": "https://reddit.com/r/SEO/comments/1kqg1as/google_loves_freshness_is_debunked_by_ahrefs/", "upvotes": 70, "comments_count": 76, "sentiment": "bullish", "engagement_score": 222.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}]