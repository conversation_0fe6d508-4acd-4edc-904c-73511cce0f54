{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-01-30", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Customer Success Internship: MBA", "content": "Hey everyone,\n\nA week ago, a recruiter from Microsoft reached out to me to apply for their Customer Success Internship since I had the relevant experience. I havent heard anything from the team since. I do realise it would take time but im getting a little worried now. Does anyone know how long it usually takes for them to get back to you? The post for the internship is still active so i havent lost hope.", "created_time": "2025-01-30T00:21:21", "platform": "reddit", "sentiment": "bullish", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Away_Entertainer1963", "url": "https://reddit.com/r/microsoft/comments/1id94nd/customer_success_internship_mba/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Microsoft and TicTok?", "content": "If Microsoft buys TicTok, do you see Microsoft’s stock jump? ", "created_time": "2025-01-30T00:54:02", "platform": "reddit", "sentiment": "bullish", "engagement_score": 22.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "TheTriplet1976", "url": "https://reddit.com/r/microsoft/comments/1id9ui3/microsoft_and_tictok/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Someone recommend a good Mail App alternative", "content": "Currently, the (NEW) outlook is just a piece of junk. it's never syncing it doesn't load on startup takes 3 business day for email to enter mailbox. Sent e-mail don't send and finish in queued box for eternity.  \ni'm tire of that bullshit. I just want the old one back use to work perfectly fine.\n\nI've found a way to open the old version, but it had to be done via the new version every single time. because it won't open the old one by default. so you had to go in settings and you find an option there in option---general----about outlook----return to windows mail and calendar. And if you send negative feedback to microsoft they'll just block that feature it'll be gone like it never existed. So yeah. I want an alternative i just can't find any right now. That's why i'm here. **(WARNING AFTER I RETURN TO OLD VERSION, ALL OF THE ATTACHED FILE SUCH AS LINKED PDF THAT I RECEIVED FROM SCAMMER AND SPAM EMAIL WERE FOUND ON THE PC BY MY ANTIVIRUS, THIS IS HIGH RISK AND I STRONGLY SUGGEST NOT TO RETURN TO OLD MAIL BECAUSE OF THAT, COULD POTENTIALLY ARM YOUR PC AND PUT YOUR FILES AT RISK)**\n\nI guess the most important parts is FREE and sync well and do what it's suppose to do. also allow to have multiple e-mail. Because outlook (new) just isn't working. Now i use my phone or just link phone to pc to write large email. it's soo annoying. i just can't rely on outlook (new) anymore. so many time it failed to do what it's suppose to do.", "created_time": "2025-01-30T00:57:17", "platform": "reddit", "sentiment": "bullish", "engagement_score": 18.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Secret_Fisherman_292", "url": "https://reddit.com/r/microsoft/comments/1id9xd9/someone_recommend_a_good_mail_app_alternative/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Microsoft's launches new Surface Laptop and Surface Pro laptops with 22 hours of battery life", "content": "", "created_time": "2025-01-30T15:16:28", "platform": "reddit", "sentiment": "neutral", "engagement_score": 176.0, "upvotes": 154, "num_comments": 0, "subreddit": "unknown", "author": "nick314", "url": "https://reddit.com/r/microsoft/comments/1idp76m/microsofts_launches_new_surface_laptop_and/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Fix the lock screen", "content": "A screen timeout has to let you be able to revert to lockscreen. It's the oldest reason for a lockscreen. In Windows 11, you need to have Bluetooth enabled, and have your phone tracked so the computer monitors you proximity and locks the screen that way.\n\nPlease add the option to just lock it after a time. You can make it sleep but the option isn't there for screen locking.", "created_time": "2025-01-30T17:42:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Zealousideal_Meat297", "url": "https://reddit.com/r/microsoft/comments/1idsnd4/fix_the_lock_screen/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Microsoft interested in buying TikTok", "content": "I guess its Name would Change into Microsoft TikTok365. Or do you have you have any Suggestion TikTok new Name? ", "created_time": "2025-01-30T22:18:39", "platform": "reddit", "sentiment": "bullish", "engagement_score": 44.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "smokinggunss", "url": "https://reddit.com/r/microsoft/comments/1idz8my/microsoft_interested_in_buying_tiktok/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Getting into Microsoft as a contractor", "content": "I may be offered a 4-month (possibly 6-month with extension) contract as a Program Manager at Microsoft through a staffing agency. What are the odds of me getting direct hire after contract lapses? Or this may be a game where the carrot is full-time employment but the contract keeps renewing or worse still canceled? ", "created_time": "2025-01-30T23:12:28", "platform": "reddit", "sentiment": "neutral", "engagement_score": 23.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Kool99123", "url": "https://reddit.com/r/microsoft/comments/1ie0hl6/getting_into_microsoft_as_a_contractor/", "ticker": "MSFT", "date": "2025-01-30"}, {"title": "Is there like a Lite Version of Office?", "content": "Like i just download LibreOffice and the whole package is 300MB\n\nCompared to my Licensed Office ProPlus LTSC 2019 which is +4GB\n\nLike i litterally use ot like 3 times a month to edit a word document or make a simple presentation.\n\n\n\nEdit: if the post is not clear\n\nI am asking sbout a specific version of office or modified office, not an alternative to office suite\n\nLike is there a version called Office 2021 Basic that has like only word, powerpoint, excel, and access for under 1GB in file size, removing other functionality like OneDrive and onenote integration and offloading the smartArt database to cloud(as office 365)", "created_time": "2025-01-29T03:49:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 29.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Bebo991_Gaming", "url": "https://reddit.com/r/microsoft/comments/1icl0xg/is_there_like_a_lite_version_of_office/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Microsoft CEO <PERSON><PERSON><PERSON> touts DeepSeek's open-source AI as \"super impressive\": \"We should take the developments out of China very, very seriously\"", "content": "Microsoft's CEO says AI developments from China should be taken very seriously amid the DeepSeek AI frenzy.", "created_time": "2025-01-29T04:03:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 775.0, "upvotes": 693, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1icla6d/microsoft_ceo_satya_nadella_touts_deepseeks/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Disconnecting Microsoft Services", "content": "Hey guys, I work a lot with Microsoft Software for my Job. Outlook, OneDrive, 365 and so on and my girlfriend does too. My Computer broke some days ago and so I logged into my Microsoft Accounts on her Laptop and now since my Computer Works again I wanted to disconnect all Thors Services of mine from her Laptop. I logged off all Services but she still has all my data on her one Drive and also gets all my contacts as recommendations on her 365 which annoys her a lot. Any idea how to fully disconnect again?", "created_time": "2025-01-29T14:09:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "_Naydra_", "url": "https://reddit.com/r/microsoft/comments/1icukn5/disconnecting_microsoft_services/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Is the Segoe UI (Variable) fonts free to use?", "content": "Hey everyone\n\nI’ve been wondering if the fonts have a license or rights similar to Roboto- where they can be used without extra stairs? I’ve been using the font at my organization for the past 4 years.\n\nI <PERSON><PERSON> thought Microsoft kinda allows that unlike Apple.", "created_time": "2025-01-29T14:13:30", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "new-romantics89", "url": "https://reddit.com/r/microsoft/comments/1icunui/is_the_segoe_ui_variable_fonts_free_to_use/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "iPhone and Android integration can be done with the start menu of Windows 11.", "content": "", "created_time": "2025-01-29T17:52:50", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Novel_Negotiation224", "url": "https://reddit.com/r/microsoft/comments/1iczvii/iphone_and_android_integration_can_be_done_with/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Microsoft and OpenAI investigate whether DeepSeek illicitly obtained data from ChatGPT", "content": "", "created_time": "2025-01-29T18:42:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 180.0, "upvotes": 90, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1id14ac/microsoft_and_openai_investigate_whether_deepseek/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Imagine having unparalleled brand capital with the sinplest of words \"Office\" and ditching it entirely for the catchy \"Microsoft 365 copilot\"", "content": "Honestly, what are Microsoft doing?", "created_time": "2025-01-29T20:13:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 279.0, "upvotes": 193, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1id3ctn/imagine_having_unparalleled_brand_capital_with/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "Microsoft Tops Global Game Sales Charts in December 2024", "content": "", "created_time": "2025-01-29T21:01:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "johanas25", "url": "https://reddit.com/r/microsoft/comments/1id4hz9/microsoft_tops_global_game_sales_charts_in/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "The Current State of Software Engineering Interview is Deeply Flawed", "content": "Someone posted this on LinkedIn:\n\n*The current state of software engineering interviews is deeply flawed. A friend of mine is considering leaving their job just to focus on studying full-time for interviews. Think about that—interviews have become so demanding and disconnected from day-to-day work that candidates feel the need to dedicate months solely to preparation.*\n\n*This isn’t just about solving complex algorithms or mastering system design; it’s about creating a process that values practical skills, creativity, and the ability to collaborate—qualities that truly define great engineers.*\n\n*We need to ask ourselves: are we testing for the right things? Or are we unintentionally gatekeeping talent by prioritizing who can memorize LeetCode problems over who can build scalable, impactful software?*\n\n[Post | Feed | LinkedIn](https://www.linkedin.com/feed/update/urn:li:activity:7288556194070200321/)\n\n  \nHaving interviewed for a SWE role and worked for other big non-tech companies. I would say the interview is deeply flawed at Microsoft. I've never seen a place that is more focused on algorithm and design pattern knowledge. Solving LeetCode problems, You can be passionate about the work, hard-working, eager to learn and growth, have a breath of knowledge, creative, able to collaborate and work with others but if you can't code a link list in C# (which is something rarely done or used) then no hire. I would like to see the SWE in Test roles brought back but it may be too late. ", "created_time": "2025-01-29T21:45:51", "platform": "reddit", "sentiment": "bullish", "engagement_score": 20.0, "upvotes": 10, "num_comments": 0, "subreddit": "unknown", "author": "Zestyclose_Depth_196", "url": "https://reddit.com/r/microsoft/comments/1id5l09/the_current_state_of_software_engineering/", "ticker": "MSFT", "date": "2025-01-29"}, {"title": "is it possible to have microsoft family but only with the time limits?", "content": "Awhile back my father put microsoft family on my computer, only for the time limit though. With microsoft family you’re going to have restrictions too, is there a way to have only the time limits and no restrictions?", "created_time": "2025-01-28T00:05:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 37.0, "upvotes": 31, "num_comments": 0, "subreddit": "unknown", "author": "Kind_Lie6930", "url": "https://reddit.com/r/microsoft/comments/1ibobty/is_it_possible_to_have_microsoft_family_but_only/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Amazon Takes Office Space at New Tower in Miami's Wynwood Area - Bloomberg.com", "content": "", "created_time": "2025-01-28T02:00:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1ibqpe5/amazon_takes_office_space_at_new_tower_in_miamis/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Microsoft CEO <PERSON><PERSON><PERSON> calls himself ‘product’ of bond between India-US", "content": "", "created_time": "2025-01-28T09:15:47", "platform": "reddit", "sentiment": "bullish", "engagement_score": 367.0, "upvotes": 297, "num_comments": 0, "subreddit": "unknown", "author": "HindustanTimes", "url": "https://reddit.com/r/microsoft/comments/1ibxnzp/microsoft_ceo_satya_na<PERSON><PERSON>_calls_himself_product/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Seeking Insights on New Grad Remote Role Relocating", "content": "I’m a recent graduate starting a fully remote role at Microsoft. To boost my early career, I'm considering relocating from the East Coast (DC) to Seattle or Chicago. \n\nI loved my internship in Redmond. I was drawn to Seattle’s vibrant city life, excellent public transit, and stunning nature. The PNW spring/summer climate fits my preference for weather, and being near Microsoft HQ could enhance my learning and networking opportunities.\n\nOn the other hand, Chicago also offers a dynamic tech scene and a large Microsoft office. As a sports fan, I appreciate that both cities are major sports hubs.\n\nI’d love to hear from anyone with experience as a new grad remote worker in Seattle, Chicago, or similar cities. How have your experiences been with learning opportunities, networking, and work-life balance? How did living in your city influence your ability to make friends and build a professional network? Thanks for your insights!", "created_time": "2025-01-28T14:12:27", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "NickoHand", "url": "https://reddit.com/r/microsoft/comments/1ic2f8k/seeking_insights_on_new_grad_remote_role/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Client questionnaire and dependent outcome", "content": "Hi Guys. \n\nAfter some advice, please. Which programs or platforms within the Microsoft architecture (or 3rd party) should I use for the following use case.\n\nI would like to send a questionnaire out to customers who then answer a series of questions which, dependent on their answers, result in them being linked to a particular set of services unique to their answers.\n\nIs it a case of using any of these maybe?\n\n-  ‘Forms (with branching )?\n- Power automation? \n\nIf I’m honest, I don’t really understand how to use Forms even yet , so go easy on the technical side! \n\nThanks\n\n<PERSON>.", "created_time": "2025-01-28T19:47:20", "platform": "reddit", "sentiment": "neutral", "engagement_score": 0.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "DannykGolf1979", "url": "https://reddit.com/r/microsoft/comments/1icaefj/client_questionnaire_and_dependent_outcome/", "ticker": "MSFT", "date": "2025-01-28"}, {"title": "Previous Microsoft interns, where are the most popular spots to live?", "content": "I decided to take the lump sum and I want to live near other interns. Which areas should I look at?", "created_time": "2025-01-27T04:40:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 5.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "thoughtfulgoose", "url": "https://reddit.com/r/microsoft/comments/1ib00ar/previous_microsoft_interns_where_are_the_most/", "ticker": "MSFT", "date": "2025-01-27"}, {"title": "My Word and Excel 2019 Suddenly Say They Are 365", "content": "and I want to change them back, and do not know how.\n\nUnder \"Account\", they still say that they are \"Microsoft Office Home and Student 2019\".  But they say \"Microsoft 365\" as I open them, and they have a new look-and-feel.  I don't want 365, because I don't want anyone snooping on my files.  I don't need OneDrive, Copilot, or any other connected services.  I would have gotten 365 when I installed Office had I wanted it - it seemed pushed on me then, it took seeking for me to find a legacy non-networked Office version.\n\nI am curious if anyone has any suggestions.", "created_time": "2025-01-27T08:03:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 173.0, "upvotes": 47, "num_comments": 0, "subreddit": "unknown", "author": "Shunya<PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1ib3575/my_word_and_excel_2019_suddenly_say_they_are_365/", "ticker": "MSFT", "date": "2025-01-27"}, {"title": "Microsoft email is totally disaster", "content": "Just create an email for work, hasn't touch it in 3 day since it is weekend and then boom. [The account is gone](https://imgur.com/a/nANiY9h) along with all work related platform. My email is only for work so \"violating agreement\" is out of question. ", "created_time": "2025-01-27T14:19:31", "platform": "reddit", "sentiment": "bullish", "engagement_score": 24.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Homeboy15999", "url": "https://reddit.com/r/microsoft/comments/1iba4sh/microsoft_email_is_totally_disaster/", "ticker": "MSFT", "date": "2025-01-27"}, {"title": "Microsoft 365 price increase (UK)", "content": "I got an email yesterday saying my subscription to 365 will go up next month from £79.99 to £104.99. That’s an increase of 31%. Microsoft haven’t given me any justification for such a hike. One of the richest companies in the world and I only use Word, Excel and Outlook. Does this seem unfair to people? I am a single user. ", "created_time": "2025-01-27T19:17:19", "platform": "reddit", "sentiment": "neutral", "engagement_score": 48.0, "upvotes": 4, "num_comments": 0, "subreddit": "unknown", "author": "Just_Eye2956", "url": "https://reddit.com/r/microsoft/comments/1ibhf7j/microsoft_365_price_increase_uk/", "ticker": "MSFT", "date": "2025-01-27"}, {"title": "Hiring process for cloud network engineer", "content": "Hi everyone! I recently received an invite for an online assessment for a new grad role as a Cloud Network Engineer at Microsoft. Could anyone share insights about the assessment? Should I focus on DSA problems or brush up on networking/cloud concepts? Any tips would be greatly appreciated!", "created_time": "2025-01-26T17:54:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "lilac_moon_1989", "url": "https://reddit.com/r/microsoft/comments/1ial6mi/hiring_process_for_cloud_network_engineer/", "ticker": "MSFT", "date": "2025-01-26"}, {"title": "Looking for Roommates in Seattle – Starting Full Time at Microsoft in September", "content": "Looking for Roommates in Seattle – Starting Full Time at Microsoft in September\n\nHey everyone, I’m moving to Seattle this September to start full-time at Microsoft and I’m looking to find roommates.\n\nI’m hoping to connect with others who are also moving to the area and are looking for housing. Ideally, I’d like to live in areas like Downtown, Bellevue, or Redmond, but I’m open to other suggestions as well.\n\nIf you’re also moving to Seattle around the same time and looking for roommates, I’d greatly appreciate connecting to explore options together. Feel free to DM me!\n", "created_time": "2025-01-25T03:36:17", "platform": "reddit", "sentiment": "neutral", "engagement_score": 18.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Think_Ad_9627", "url": "https://reddit.com/r/microsoft/comments/1i9e4wk/looking_for_roommates_in_seattle_starting_full/", "ticker": "MSFT", "date": "2025-01-25"}, {"title": "Does anyone have any experience working in the EPC part of Microsoft ?", "content": "I have a job offer for a construction role with Microsoft in Sweden, as part of the data center projects. \n\nDoes anyone have any experience in this sort of area of Microsoft? How is the culture? Is it similar to the rest of Microsoft or is it distinct? How is work life balance? ", "created_time": "2025-01-25T14:22:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 6.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "<PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i9nyfv/does_anyone_have_any_experience_working_in_the/", "ticker": "MSFT", "date": "2025-01-25"}, {"title": "Office 365 Classic Subscription - NEW CUSTOMERS", "content": "Hello interwebs,\n\nWanted to share how to subscribe as a new customer to the 365 Classic packages. I've been doing loads of research, none of which seemed to indicate how to work around the new pricing model as a new customer.\n\nThankfully, Microsoft offers prorating ( **limited to specific countries** \\- [https://support.microsoft.com/en-us/account-billing/countries-with-prorated-refunds-for-microsoft-subscriptions-38c81df4-10c1-f3eb-8d2f-b04f980c435f](https://support.microsoft.com/en-us/account-billing/countries-with-prorated-refunds-for-microsoft-subscriptions-38c81df4-10c1-f3eb-8d2f-b04f980c435f) ), so I thought it wouldn't hurt to experiment. After a few full refunds, this is what I found to be the best course of action:\n\n***Subscribe to your desired Office 365 plan (Personal/Family/etc.) for 1 month > Manage > Cancel > Select \"Or buy at CAD $XXXX/year\" (under the Switch plan button).***\n\nIn my particular case, I went with the 1 month of 365 Personal, immediately followed the above steps and was provided an option to switch to 365 Personal Classic, + convert it to a yearly sub instead.\n\nAlthough you have to essentially pay an added cost ($3.50 CAD for me), the subscription will automatically switch to the original 365 Classic the following month and remain recurring.\n\n  \nHappy savings!!! <3", "created_time": "2025-01-25T15:29:24", "platform": "reddit", "sentiment": "bullish", "engagement_score": 85.0, "upvotes": 55, "num_comments": 0, "subreddit": "unknown", "author": "7<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://reddit.com/r/microsoft/comments/1i9pc55/office_365_classic_subscription_new_customers/", "ticker": "MSFT", "date": "2025-01-25"}, {"title": "Microsoft 365 Personal (UK): 41.7% Price increase. Email notification feedback.", "content": "The manner of which the price increase is being communicated via email is **awful** and needs to be urgently updated.\n\nThe fact that the cost is increasing by a whopping **41.7%** from **£5.99** to **£8.49** and no standalone email has been sent advising of the massive price change, due to the addition of AI, is mind blowing!\n\nCurrently, the only reference to the price increase is 'hidden' within the most recent Subscription Renewal email. As the email title is the same as every other renewal email, this can be easily missed and therefore comes across as intentionally deceptive.\n\nThere **NEEDS** to be a standalone email providing the following:\n\n* Notification of the price increase (Cost change)\n* Reasons for the price increase (AI addition)\n* Options and instructions for alternative packages\n* Information on the new Classic option which removes the AI functionality\n\nPlease reconsider your current marketing on this. It does not hurt to be clear and forthcoming.", "created_time": "2025-01-25T22:45:14", "platform": "reddit", "sentiment": "neutral", "engagement_score": 124.0, "upvotes": 42, "num_comments": 0, "subreddit": "unknown", "author": "ContinuumOnPC", "url": "https://reddit.com/r/microsoft/comments/1i9z8jm/microsoft_365_personal_uk_417_price_increase/", "ticker": "MSFT", "date": "2025-01-25"}, {"title": "How us one of the largest companies in the US so awful at logistics?", "content": "I ordered an xbox series x from microsoft on January 18. My order status is “pending” What gives? I have not been able to find a way to inquire about my order, or get an estimated date of arrival. \n\nMiddling Etsy sellers do a better job of fulfilling orders. This is just disgraceful!", "created_time": "2025-01-24T00:39:22", "platform": "reddit", "sentiment": "bearish", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "constant--questions", "url": "https://reddit.com/r/microsoft/comments/1i8iulh/how_us_one_of_the_largest_companies_in_the_us_so/", "ticker": "MSFT", "date": "2025-01-24"}, {"title": "Question about M6 job level", "content": "Looking at applying for a role that lists the level as \"M6\", it's a director role. For those that are familiar with both Microsoft and the world of MBB consulting, what level of consulting would this be equivalent to?\n\nWhat is the leveling system and what is M6 specifically?\n\nTIA!", "created_time": "2025-01-24T00:56:51", "platform": "reddit", "sentiment": "neutral", "engagement_score": 14.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Psychological_Owl545", "url": "https://reddit.com/r/microsoft/comments/1i8j7nq/question_about_m6_job_level/", "ticker": "MSFT", "date": "2025-01-24"}, {"title": "Surface 3 OS Choices", "content": "I have a Surface 3 running Windows 10 22H2. After Windows 10 has sunset, What OS should I run? Linux Mint? Will that work with a touch-based machine? Or should I get something like BitDefender to keep Windows running longer? It's not my daily driver. So, I would only use it to watch Plex, Netflix, Hulu, and Disney+.", "created_time": "2025-01-24T12:58:37", "platform": "reddit", "sentiment": "bullish", "engagement_score": 94.0, "upvotes": 54, "num_comments": 0, "subreddit": "unknown", "author": "the_mhousman", "url": "https://reddit.com/r/microsoft/comments/1i8usfg/surface_3_os_choices/", "ticker": "MSFT", "date": "2025-01-24"}, {"title": "Looking to join technical support for Xbox.", "content": "I’m interested in working remotely and doing technical support for Xbox and pc gamers through Microsoft. How would I go about applying for that? ", "created_time": "2025-01-24T21:21:58", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "djdavis4156", "url": "https://reddit.com/r/microsoft/comments/1i96d69/looking_to_join_technical_support_for_xbox/", "ticker": "MSFT", "date": "2025-01-24"}, {"title": "Microsoft's business development chief <PERSON> resigns", "content": "", "created_time": "2025-01-23T03:18:45", "platform": "reddit", "sentiment": "neutral", "engagement_score": 445.0, "upvotes": 387, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1i7u3c0/microsofts_business_development_chief_ch<PERSON>_young/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Remote Positions - 50 Mile Hub Radius?", "content": "Straight to the point question I have here. I live in southern FL and was looking at quite a few Microsoft positions that state up to 100% remote, but sometimes in the job descriptions I'm seeing you have to be within a 50 mile radius of a hub or it just doesn't say it at all. The jobs I'm gearing towards (Procurement/Supply Chain) are stating relocation will be supported to be within 50 mile radius. I have a family and pretty much have no intention of wanting to move anywhere, is it even worth me applying to these positions if this is actually true? I'm seeing on Reddit some people aren't even actually within that radius and have negotiated with the hiring manager? ", "created_time": "2025-01-23T04:43:41", "platform": "reddit", "sentiment": "neutral", "engagement_score": 7.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "TrumpBrahs", "url": "https://reddit.com/r/microsoft/comments/1i7vmsn/remote_positions_50_mile_hub_radius/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Copilot integration with Microsoft word is now free?", "content": "I have notice when Im using microsoft word, the copilot icon is already popping up. But i havent used it yet. Does anyone is using this feature in microsoft word?", "created_time": "2025-01-23T05:30:16", "platform": "reddit", "sentiment": "neutral", "engagement_score": 13.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "SilentAdvocate2023", "url": "https://reddit.com/r/microsoft/comments/1i7webz/copilot_integration_with_microsoft_word_is_now/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Remote employee question", "content": "Hi! I recently was offered a job at Microsoft and my background check just completed, so I haven’t started yet. I’m not located in WA and my job is 100% remote. Im just curious—if there is a Microsoft office near me, can I go in and use it on random days if I want? Or is it only for those assigned to teams that work out of those buildings?\n\nI’ve been remote since 2018 and it can get a little isolating/stir crazy, so would love the option to go somewhere, even for a few hours.\n\nCheers! ", "created_time": "2025-01-23T07:02:59", "platform": "reddit", "sentiment": "neutral", "engagement_score": 56.0, "upvotes": 8, "num_comments": 0, "subreddit": "unknown", "author": "Katinthehat02", "url": "https://reddit.com/r/microsoft/comments/1i7xrxw/remote_employee_question/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Cheaper hidden Office offer without Copilot", "content": "I am (was, actually) a Microsoft 365 Family subscriber. A few days ago Copilot started showing up integrated into my Office apps. I am not a big fan of Microsoft shoving and trying to upsell Copilot everywhere, but I have grown accostumed to it and I thought no more of the subject. However, a few days after that, when browsing through my Microsoft account settings, I accidentally discovered that for the \"privilege\" of having Copilot unwillingly shoved into my face, my subscription is now 30% (!!!) more expensive. Fortunately I was on an annual subscription and thus rebilling was still a few months away!\n\nAfter pondering for a while, I decided to eradicate the problem at the source and canceled my subscription. I discovered, and want to share with potential users facing the same dilemma, that when you are canceling your subscription, you are presented with a hidden, 30% cheaper M365 subscription without Copilot. That offer is not accessible anywhere outside the cancelation screen. \n\n This \"hidden offer when canceling\" seems right out of the playbook of unreputable adult content merchants rather than reputable software vendors. It was a bit too shady for me and so I've moved on to a non-Microsoft office product, but for those interested in getting the non-Copilot version of Office at the previous price, take note of the existence of this hidden offer.  \n\n In my opinion, Microsoft COULD have been user friendly when selling these features to users. For example: \n\n* \"We have integrated Copilot into Office. Do you want to try it out? Click here to activate a 15-day trial. If you like it, you can enable the Copilot add-on for your subscription for a 30% price increase. You can also disable this add-on in the future at any time and it will take effect at the next time your subscription renews.\" \n* New subscribers of M365 can choose to subscribe with or without the Copilot add-on. \n\nWhat  Microsoft actually chose to do: \n\n* Everyone gets a silent 30% price increase. Users do not get notified of the price increase. There is no explanation of the reason behind the price increase (Copilot integration). \n* Copilot gets forcefully shoved in everyone's face inside Office. (The OneNote integration is specially disgusting, the Copilot icon literally follows your cursor around everywhere.) There is no way to disable it except for using a global setting for \"connected experiences\" - which disables a lot more than just Copilot. \n* They implemented a hidden offer without Copilot integration and the associated price increase that is kept secret and only presented to users when canceling their subscription as a last resort customer retention tactic - because they KNEW perfectly well that some subscribers would cancel after such a gigantic price increase. ", "created_time": "2025-01-23T08:54:19", "platform": "reddit", "sentiment": "bearish", "engagement_score": 119.0, "upvotes": 35, "num_comments": 0, "subreddit": "unknown", "author": "forger-eight", "url": "https://reddit.com/r/microsoft/comments/1i7z7wu/cheaper_hidden_office_offer_without_copilot/", "ticker": "MSFT", "date": "2025-01-23"}, {"title": "Amazon UK Boss Says Mr <PERSON> Vs The Post Office Wouldn't Work On Prime - Deadline", "content": "", "created_time": "2025-01-23T14:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1i83ysc/amazon_uk_boss_says_mr_bates_vs_the_post_office/", "ticker": "MSFT", "date": "2025-01-23"}], "metadata": {"timestamp": "2025-07-06T22:18:14.854133", "end_date": "2025-01-30", "days_back": 7, "successful_dates": ["2025-01-30", "2025-01-29", "2025-01-28", "2025-01-27", "2025-01-26", "2025-01-25", "2025-01-24", "2025-01-23"], "failed_dates": [], "source": "local"}}}}