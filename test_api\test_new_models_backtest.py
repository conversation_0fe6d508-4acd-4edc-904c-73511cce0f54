#!/usr/bin/env python3
"""
测试新添加的QingYun模型在回测系统中的集成
选择表现最好的2个模型进行简单回测测试
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class BacktestTester:
    """回测测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.api_key = os.getenv("QINGYUN_API_KEY")
        if not self.api_key:
            raise ValueError("❌ QINGYUN_API_KEY环境变量未设置")
        
        # 选择表现最好的2个模型进行测试
        self.test_models = [
            "qwen3-1.7b",      # 响应时间最快
            "ERNIE-Speed-128K"  # 响应质量好且速度快
        ]
        
        print(f"🚀 新QingYun模型回测集成测试")
        print(f"✅ API密钥已配置")
        print(f"📋 将测试 {len(self.test_models)} 个表现最好的新模型")
        print("-" * 60)
    
    def run_backtest_with_model(self, model_name: str) -> dict:
        """使用指定模型运行回测"""
        print(f"\n🧪 测试模型: {model_name}")
        print("-" * 40)
        
        try:
            # 构建回测命令
            cmd = [
                "python", "src/backtester.py",
                "--tickers", "AAPL",
                "--start-date", "2024-01-01", 
                "--end-date", "2024-01-02",
                "--deterministic",
                "--seed", "42",
                "--save-reasoning"
            ]
            
            print(f"🔄 执行命令: {' '.join(cmd)}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 运行回测（使用非交互模式）
            env = os.environ.copy()
            env["PYTHONUNBUFFERED"] = "1"
            
            # 创建一个临时输入文件来模拟用户选择
            # 我们需要找到模型在列表中的位置
            model_selection_input = self._get_model_selection_input(model_name)
            
            process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=Path(__file__).parent
            )
            
            # 发送模型选择输入
            stdout, stderr = process.communicate(input=model_selection_input, timeout=300)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 检查执行结果
            success = process.returncode == 0
            
            result = {
                "model_name": model_name,
                "success": success,
                "execution_time": execution_time,
                "return_code": process.returncode,
                "stdout": stdout[:500] + "..." if len(stdout) > 500 else stdout,
                "stderr": stderr[:500] + "..." if len(stderr) > 500 else stderr,
                "error": None
            }
            
            if success:
                print(f"  ✅ 回测成功 | 耗时: {execution_time:.2f}s")
                print(f"  📊 返回码: {process.returncode}")
            else:
                print(f"  ❌ 回测失败 | 返回码: {process.returncode}")
                print(f"  📝 错误信息: {stderr[:200]}...")
            
            return result
            
        except subprocess.TimeoutExpired:
            print(f"  ⏰ 回测超时（5分钟）")
            return {
                "model_name": model_name,
                "success": False,
                "execution_time": 300,
                "return_code": -1,
                "stdout": "",
                "stderr": "Timeout after 5 minutes",
                "error": "Timeout"
            }
        except Exception as e:
            print(f"  ❌ 执行失败: {str(e)}")
            return {
                "model_name": model_name,
                "success": False,
                "execution_time": 0,
                "return_code": -1,
                "stdout": "",
                "stderr": str(e),
                "error": str(e)
            }
    
    def _get_model_selection_input(self, model_name: str) -> str:
        """生成模型选择输入"""
        # 这里我们需要模拟用户在交互界面中选择模型
        # 由于我们知道模型的显示名称，我们可以尝试找到对应的选项
        
        # 简化处理：直接返回一些常见的选择
        # 实际使用中，可能需要根据具体的交互界面调整
        return f"[qingyun] {model_name}\n\n\n\n\n"
    
    def test_all_models(self):
        """测试所有选定的模型"""
        print(f"🔄 开始回测集成测试...")
        
        results = []
        success_count = 0
        
        for model_name in self.test_models:
            result = self.run_backtest_with_model(model_name)
            results.append(result)
            if result["success"]:
                success_count += 1
            
            # 添加延迟避免资源冲突
            time.sleep(5)
        
        # 生成报告
        print(f"\n" + "=" * 60)
        print(f"📊 回测集成测试报告")
        print(f"=" * 60)
        print(f"总测试数: {len(results)}")
        print(f"成功数: {success_count}")
        print(f"失败数: {len(results) - success_count}")
        print(f"成功率: {success_count/len(results)*100:.1f}%")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for result in results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['model_name']} - 耗时: {result['execution_time']:.1f}s")
            if result["error"]:
                print(f"   错误: {result['error']}")
        
        return results

def main():
    """主函数"""
    try:
        tester = BacktestTester()
        results = tester.test_all_models()
        
        # 检查是否有成功的回测
        success_count = sum(1 for r in results if r["success"])
        if success_count > 0:
            print(f"\n🎉 {success_count} 个模型回测集成测试成功！")
            print(f"✅ 新模型已成功集成到回测系统中")
        else:
            print(f"\n⚠️  所有回测测试都失败了，可能需要检查系统配置")
            
    except Exception as e:
        print(f"❌ 测试器初始化失败: {e}")

if __name__ == "__main__":
    main()
