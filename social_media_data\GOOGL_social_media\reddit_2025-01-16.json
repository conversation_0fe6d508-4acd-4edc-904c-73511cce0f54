[{"platform": "reddit", "post_id": "reddit_1i2nwbu", "title": "Is google search dead?", "content": "I am hearing from a lot of folks these days that google search is dead and all searches are going to AI chats. We do not need to optimise or write content for google searchranking.. Is that correct?", "author": "ansulsg", "created_time": "2025-01-16T12:34:00", "url": "https://reddit.com/r/marketing/comments/1i2nwbu/is_google_search_dead/", "upvotes": 0, "comments_count": 23, "sentiment": "neutral", "engagement_score": 46.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i2wbgh", "title": "Google Data Analytics worth it?", "content": "Hi, is the above really worth it? I'm currently studying L4 Data Analytics via work but the material is much better I think on Coursera (trialling the 7 day free version). \n\nIs the cert still worth it? YouTube tells me one thing but I wanted thoughts from real people in the field.\n\nThanks ", "author": "Informal-Fly4609", "created_time": "2025-01-16T18:56:34", "url": "https://reddit.com/r/analytics/comments/1i2wbgh/google_data_analytics_worth_it/", "upvotes": 35, "comments_count": 51, "sentiment": "neutral", "engagement_score": 137.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1i2y2j0", "title": "Aws vs Google data certificates ", "content": "Most posts I see are saying that the Google cert is pretty worthless. What about an AWS cert for an entry level candidate in business or data analytics? Maybe the data engineer cert or solutions architect? Or are the days of certs behind us in this job market? ", "author": "anxiouskitty25", "created_time": "2025-01-16T20:10:23", "url": "https://reddit.com/r/analytics/comments/1i2y2j0/aws_vs_google_data_certificates/", "upvotes": 2, "comments_count": 3, "sentiment": "neutral", "engagement_score": 8.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}]