{"agent_name": "social_media_analyst_agent", "ticker": "MSFT", "trading_date": "2025-02-10", "api_calls": {"load_local_social_media_data": {"data": [{"title": "Microsoft needs to pick up the pace", "content": "Microsoft's services are starting to get worse and worse. There's a reason why people are switching to Mac and Linux. Just the other day I sent an important email, and Outlook decides to put their response in my junk folder. Teachers have been getting frustrated with Teams because of the downgrades Microsoft is making to it to add in AI (which is great, but don't take away functionally please). I'm starting to consider switching as well. Started dual booting Linux to see if I can get my things to work on it.", "created_time": "2025-02-10T04:56:21", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "tddk25", "url": "https://reddit.com/r/microsoft/comments/1ilyc1x/microsoft_needs_to_pick_up_the_pace/", "ticker": "MSFT", "date": "2025-02-10"}, {"title": "Microsoft Study Finds AI Makes Human Cognition “Atrophied and Unprepared” | Researchers find that the more people use AI at their job, the less critical thinking they use.", "content": "", "created_time": "2025-02-10T17:44:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 376.0, "upvotes": 310, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1imc1ce/microsoft_study_finds_ai_makes_human_cognition/", "ticker": "MSFT", "date": "2025-02-10"}, {"title": "Got microsoft offer, background verification started", "content": "I am so happy to share that I got a offer from microsoft after months of grinding.  However I have doubt when should I put my resignation in the current company before or after background verification.  I have asked the recruiter she told me to put the resignation. But have seen lot of post in internet stating the delay in background verification. ", "created_time": "2025-02-10T20:37:42", "platform": "reddit", "sentiment": "neutral", "engagement_score": 178.0, "upvotes": 36, "num_comments": 0, "subreddit": "unknown", "author": "the_great_danton1", "url": "https://reddit.com/r/microsoft/comments/1imge97/got_microsoft_offer_background_verification/", "ticker": "MSFT", "date": "2025-02-10"}, {"title": "Microsoft is doing a lot of stuff right, but still need to better itself", "content": "For the past two decades, I've seen a lot of change from Microsoft. Mostly good change.\n\nBut obviously there's still some ironing to do.\n\n[https://www.reddit.com/r/browsers/comments/1ikj3xt/microsoft\\_support\\_page\\_lies\\_about\\_how\\_to/](https://www.reddit.com/r/browsers/comments/1ikj3xt/microsoft_support_page_lies_about_how_to/)\n\nFound this hard to believe, so I tried it myself.\n\nWent to Google and searched:\n\n\\- how to uninstall google chome  \n\\- how to uninstall firefox  \n\\- how to uninstall brave  \n\\- how to uninstall microsoft edge\n\nThe result from the official brand took me to:  \n[https://support.google.com/chrome/answer/95319?hl=en&co=GENIE.Platform%3DDesktop](https://support.google.com/chrome/answer/95319?hl=en&co=GENIE.Platform%3DDesktop)  \n[https://support.mozilla.org/en-US/kb/uninstall-firefox-from-your-computer#](https://support.mozilla.org/en-US/kb/uninstall-firefox-from-your-computer#)  \n[https://support.brave.com/hc/en-us/articles/4404876135565-How-do-I-uninstall-Brave](https://support.brave.com/hc/en-us/articles/4404876135565-How-do-I-uninstall-Brave)  \n[https://www.microsoft.com/en-us/edge/?form=MT00OR&cs=*********&ch=1](https://www.microsoft.com/en-us/edge/?form=MT00OR&cs=*********&ch=1)\n\nThis is a very sad move from Microsoft and something I thought the company was finally being able to turn away from.\n\n**EDIT:**\n\nI didn't explain myself properly on the first attempt.\n\nIf you Google for it, you'll get an official result from Microsoft:\n\nThe page is Titled: Uninstall Microsoft Edge\n\n[https://www.microsoft.com/en-us/edge/uninstall-edge](https://www.microsoft.com/en-us/edge/uninstall-edge)\n\nBut, if you visit such page as a client, you're forwarded to another:\n\n[https://www.microsoft.com/en-us/edge/?ch=1&cs=4112006293&form=MA13FJ](https://www.microsoft.com/en-us/edge/?ch=1&cs=4112006293&form=MA13FJ)\n\n**My issue is with Microsoft blatantly messing with the search results!**\n\nI've added an image with the result from Google search:\n\n[https://imgur.com/a/16Xkxcc](https://imgur.com/a/16Xkxcc)", "created_time": "2025-02-09T01:29:06", "platform": "reddit", "sentiment": "neutral", "engagement_score": 54.0, "upvotes": 6, "num_comments": 0, "subreddit": "unknown", "author": "frankielc", "url": "https://reddit.com/r/microsoft/comments/1il3b6i/microsoft_is_doing_a_lot_of_stuff_right_but_still/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "Microsoft Identity and Access Administrator SC-300 Exam", "content": "Can anyone recommend good study material I'm thinking of buying\nhttps://a.co/d/16gi1Md\n\n<PERSON> and 2 more\nMicrosoft Identity and Access Administrator SC-300 Exam Guide: Gain the confidence to pass the SC-300 exam using exam-focused study resources\nISBN-13: 978-1836200390, ISBN-10: 1836200390", "created_time": "2025-02-09T03:38:53", "platform": "reddit", "sentiment": "bullish", "engagement_score": 14.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "Bulky_Novel_4224", "url": "https://reddit.com/r/microsoft/comments/1il5rcg/microsoft_identity_and_access_administrator_sc300/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "Migrating imap to exchange", "content": "Hello, \n\nCould someone explain to me how exchange works? \n\nWe have a rented email server with @domain.com(imap and web access). We want to migrate to exchange on office 365 bussines subscription. If we migrate to exchange via imap migration (providing email and password) will emails stay on imap server? \n\n", "created_time": "2025-02-09T12:48:32", "platform": "reddit", "sentiment": "neutral", "engagement_score": 11.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "blindpd", "url": "https://reddit.com/r/microsoft/comments/1ildztw/migrating_imap_to_exchange/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "What is your tried and true implementation of Microsoft app?", "content": "I'm and looking for ways to automate productivity and make life simpler.", "created_time": "2025-02-09T23:14:47", "platform": "reddit", "sentiment": "neutral", "engagement_score": 20.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "LivinJH", "url": "https://reddit.com/r/microsoft/comments/1ils4r0/what_is_your_tried_and_true_implementation_of/", "ticker": "MSFT", "date": "2025-02-09"}, {"title": "Why Working at Microsoft May No Longer Be Worth It (2025)", "content": "In 2023, Microsoft’s Chief Marketing Officer <PERSON> made a notable statement on the company’s internal Yammer platform: “The most important lever for almost all our employees’ compensation upside is the stock price.” Below article looks into it.\n\n[**Why Working at Microsoft May No Longer Be Worth It (2025)**](https://deepseeks.medium.com/7069e2914ec1)\n\nThis philosophy tied employees’ financial futures directly to Microsoft’s stock performance.  \nWith negative one year returns, layoffs without severance is it worth to stay at microsoft.", "created_time": "2025-02-08T14:34:44", "platform": "reddit", "sentiment": "bullish", "engagement_score": 541.0, "upvotes": 365, "num_comments": 0, "subreddit": "unknown", "author": "LowerButterscotch556", "url": "https://reddit.com/r/microsoft/comments/1ikok6h/why_working_at_microsoft_may_no_longer_be_worth/", "ticker": "MSFT", "date": "2025-02-08"}, {"title": "Just got waitlisted for a CSA role at Microsoft any chances?", "content": "I just heard back from my final round interview at Microsoft for the CSA MBA role full time position and they just waitlisted me, saying they cannot offer me a role at this time but I am still under consideration as full time hiring is not yet finished. Basically, if someone turns down the offer for the role, they will take me. They said that they will provide me with an update in a few weeks. Does that actually happen? ", "created_time": "2025-02-08T16:18:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 38.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Particular_Price4153", "url": "https://reddit.com/r/microsoft/comments/1ikqvpf/just_got_waitlisted_for_a_csa_role_at_microsoft/", "ticker": "MSFT", "date": "2025-02-08"}, {"title": "Anyone has an update on the msft software engineer ai/ml role in redmond.", "content": "So im just wondering if Anyone has heard back from Microsoft for the software engineer ai/ml role in redmond. I applied on the 2nd of January as a new grad international based in the uk and was then transferred the next day to another role called FTE SWE AI ML January.\n\nI also managed to get a refferal for the role about a week ago but still havent recieved any type of communication at all not even a phone screen. Is this normal and the wait time could be a up to a month or should I just forget about it.", "created_time": "2025-02-07T00:56:40", "platform": "reddit", "sentiment": "neutral", "engagement_score": 19.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Reason-Plenty", "url": "https://reddit.com/r/microsoft/comments/1ijio7v/anyone_has_an_update_on_the_msft_software/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "Microsoft Software Engineer Screening Round Interview Experience", "content": "Hi\n\n\n\nI completed a screening(first) round for IC2 Software Engineer at Microsoft with Hiring Manager. My primary experience is with enterprise software development with Java, but the role is more inclined with Low level programming. \n\n\n\nSo tasked with a couple of coding tasks in C. First one is simple, and I did it pretty quick. But the second one, When I'm implementing the brute force approach by having three loops. The interview mentioned It's better to do it in an optimized way by using a data structure.\n\n\n\nI could not think of this because I was coding with C (When asked about it, I rated myself a 7 in C programming) in the interview when my primary language is Java. Also, my recruiter mentioned me that there would be no coding, it will focus primarily on my resume. So, I prepared only my experience and some concepts. This was also a reason for me not doing well.\n\n\n\nThe interview is scheduled for 45 mins, but it lasted for 30 mins. The interview mentioned the role would be comprising extremely low-level programming. \n\n\n\nSo, how long should I wait for the result. Do you think I would clear this round? I'm not sure about the standards of Microsoft. I have a feeling that I won't clear this tbh.\n\n\n\nThanks in advance.", "created_time": "2025-02-07T01:40:23", "platform": "reddit", "sentiment": "bullish", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "sutbborn_face", "url": "https://reddit.com/r/microsoft/comments/1ijjk6i/microsoft_software_engineer_screening_round/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "Amazon, Echoing Microsoft, Says It Can't Keep Up With AI Demand - Bloomberg", "content": "", "created_time": "2025-02-07T14:00:04", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "AmazonNewsBot", "url": "https://reddit.com/r/amazon/comments/1ijvlty/amazon_echoing_microsoft_says_it_cant_keep_up/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "What are the upgraded features of purchased office programs?", "content": "This may be a stupid question but honestly, I don’t know what “purchased” word, powerpoint, or excel do that is better than the free apps. Can someone give me a breakdown or a link to somewhere where this is explained?", "created_time": "2025-02-07T14:34:46", "platform": "reddit", "sentiment": "neutral", "engagement_score": 3.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "chickenfeeder41", "url": "https://reddit.com/r/microsoft/comments/1ijwcmm/what_are_the_upgraded_features_of_purchased/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "What to expect in my first connect as an L63 Sr SDE?", "content": "Hello All,\n\nI joined Microsoft as an L63 Sr SDE in September in the C+E org. So far things have ben stressful, microsoft doesn't really have an onboarding bootcamp, so I feel like I am kind of expected to ramp up very fast and deliver on my sprint tasks.\n\nI feelI am doing okay. I was also a full stack developer role, but I got reorged into a data engineering role. For example, I was building typescript/react based apps and maintaining backends also in same language. My new team mostly runs jobs on Azure Data FActory, and uses Scala, and Spark, big mindset shift for me. The tasks are coming top down, and i find myself working on weekeends as the estimates are assigned by the manager.\n\nThe Nov connect was really looking forward, and was more of a mock connect if you will. My real connect will be in May/June I think, what are the odds that they give me a LITE or 80% in the first connect?\n\nI don't have a lot of faith in my manager, as she has a poker face. I am aslo struggling because my team  and manager included have a very strong accent that I cannot understand even after watching the recorded videos.\n\nThanks", "created_time": "2025-02-07T22:20:28", "platform": "reddit", "sentiment": "bullish", "engagement_score": 49.0, "upvotes": 13, "num_comments": 0, "subreddit": "unknown", "author": "PartySuccotash5011", "url": "https://reddit.com/r/microsoft/comments/1ik7gik/what_to_expect_in_my_first_connect_as_an_l63_sr/", "ticker": "MSFT", "date": "2025-02-07"}, {"title": "Microsoft interview 29th Jan intern results?", "content": "It has been exactly week since my final round at Microsoft for Redmond. From what I heard and seen on other subs if accepted a change in portal would appear to completed. Has anyone that interviewed on 1/29 received that portal status changed yet? For explore or normal swe intern? Mine is still on scheduling, I will wait until the end of the week for better confirmation. Thx", "created_time": "2025-02-06T02:32:01", "platform": "reddit", "sentiment": "neutral", "engagement_score": 15.0, "upvotes": 5, "num_comments": 0, "subreddit": "unknown", "author": "Creative-Hunter8009", "url": "https://reddit.com/r/microsoft/comments/1iis2li/microsoft_interview_29th_jan_intern_results/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Updated Privacy Policy should be illegal", "content": "**TLDR:  If you opt out of Microsoft's new connected services you can't sync with OneDrive but if you opt in to connected services, Microsoft will scrape all of your content for AI use.**\n\nSo - I saw a few posts online recently about the updated privacy policy and this new \"connected services\" malarkey.  And of course I went down a rabbit hole - that I wish I hadn't.  \n\nHere's the gist of it.  If you have all of the 3 connected services options selected   \n  \n1) Turn on experiences that analyze your content   \n2) Turn on experiences that download online content   \n3) Turn on all connected experiences.  \n  \n\\- you have zero privacy, and Microsoft WILL use AI to scrape your content.  Even though they say they don't - who are we actually kidding?  The lay language in the privacy policy says certain features like **Microsoft 365 Copilot** do access and process your data to provide ***\"assistance\"***. Copilot connects large language models to your organizational data, including documents, emails, and meetings, to generate contextually relevant responses. (uh-huh).  This processing is done to ***assist*** you and is not used to train Microsoft's AI models.  \n\nYou can choose to disconnect them but here's what will happen if you do.  For example, I want to ensure that my OneNote syncs across my devices.  If I opt out of #3 above (turn on all connected experiences) this is what Microsoft says will happen:\n\n*Cloud-based features like* ***OneNote syncing, real-time collaboration in Word/Excel, and automatic saving to OneDrive*** *will* ***not work****.*\n\nMicrosoft explicitly states that disabling these services means you lose access to any feature that requires cloud connectivity, including:\n\n* ***OneNote cloud sync*** *(your notes will only be stored locally)*\n* ***Auto-save & real-time collaboration*** *in Word, Excel, and PowerPoint*\n* ***Online templates, stock images, and AI-powered tools*** *like Editor or Designer*\n\n*If you want* ***OneNote to sync across devices****, you must enable at least some level of connected services. You can still use* ***OneNote locally*** *without syncing if you keep cloud features disabled.*\n\n  \nNow - you would think that ok - I'll just use option #2 because it allows you to sync OneNote with the cloud while keeping things as private as possible. But no - you can't just select option #2.  If you want to select either #1 or #2, you MUST opt in to #3 - turning on ALL connected experiences.  \n\nSo, If your **main goal is privacy**, you'd have to disable everything and manually back up.  \n\nHOW HAS MICROSOFT BEEN ALLOWED TO GET AWAY WITH THIS???  How is this legal?  WTAF is going on???\n\n\n\n", "created_time": "2025-02-06T15:53:07", "platform": "reddit", "sentiment": "neutral", "engagement_score": 58.0, "upvotes": 12, "num_comments": 0, "subreddit": "unknown", "author": "voubar", "url": "https://reddit.com/r/microsoft/comments/1ij5pj4/updated_privacy_policy_should_be_illegal/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Any tips or insight on Business Development Manager roles?", "content": "Hi all, I recently scheduled an intro phone call with a hiring manager at Microsoft to discuss this role. Could anyone with experience provide me with some insight on it? I come from 4 years of sales, is this just another BDR role? \n\nTIA", "created_time": "2025-02-06T17:39:42", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "pbandit11", "url": "https://reddit.com/r/microsoft/comments/1ij8cgh/any_tips_or_insight_on_business_development/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Microsoft’s AI boss just raided Google. He poached two scientists who built a tool that can transform ho-hum text into a riveting podcast", "content": "", "created_time": "2025-02-06T19:29:03", "platform": "reddit", "sentiment": "neutral", "engagement_score": 111.0, "upvotes": 95, "num_comments": 0, "subreddit": "unknown", "author": "ControlCAD", "url": "https://reddit.com/r/microsoft/comments/1ijb1m0/microsofts_ai_boss_just_raided_google_he_poached/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Investors Who Shunned the U.S. Office Market Are Coming Back", "content": "The volume of office building sales increased to $63.6 billion in 2024, up 20% from 2023, according to data firm MSCI. That activity still pales compared with 2015 to 2019, when volume averaged $142.9 billion a year. But it marked the first increase since 2021.", "created_time": "2025-02-06T22:23:29", "platform": "reddit", "sentiment": "neutral", "engagement_score": 102.0, "upvotes": 60, "num_comments": 0, "subreddit": "unknown", "author": "PrestigiousCat969", "url": "https://reddit.com/r/finance/comments/1ijfbj7/investors_who_shunned_the_us_office_market_are/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "MicrosoftDocs GitHub Issues Hidden", "content": "", "created_time": "2025-02-06T23:02:23", "platform": "reddit", "sentiment": "neutral", "engagement_score": 4.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "myroon5", "url": "https://reddit.com/r/microsoft/comments/1ijg7xa/microsoftdocs_github_issues_hidden/", "ticker": "MSFT", "date": "2025-02-06"}, {"title": "Microsoft Market Consultant Interview", "content": "I have an interview coming up for a Microsoft Market Consultant role. Anyone know any questions that might be asked?\n", "created_time": "2025-02-05T03:48:57", "platform": "reddit", "sentiment": "neutral", "engagement_score": 48.0, "upvotes": 34, "num_comments": 0, "subreddit": "unknown", "author": "Ok-Nefariousness-352", "url": "https://reddit.com/r/microsoft/comments/1ii17zb/microsoft_market_consultant_interview/", "ticker": "MSFT", "date": "2025-02-05"}, {"title": "Microsoft Product Design Internship Interview", "content": "\n\nHi! \n\nSo, I applied to Microsoft’s PD Internship in November. Yesterday, I received a follow-up email that I’ve been invited to the final round of interviews! \n\nThe email said I have a 45-minute portfolio presentation and 3 1:1 interviews. Less than week from in now- has anyone gone through the interview process for this internship before? I just had a couple of questions. \n\n- in the 45 minutes for your presentation, is any of that time left for the interviewers to ask questions? I have 2 slide decks on 2 projects, presenting them takes 30 min. Should that be enough?\n- does anyone remember the behavioral questions they ask on 1:1\n- If I were lucky enough to receive an offer (unlikely, LOL), would they let me choose a start date? I’ve commited to another summer internship at a FAANG and would hate to renege. Was hoping I could take this for fall. \n\nThank you so much- any advice or insight is beyond helpful ❤️", "created_time": "2025-02-05T14:19:14", "platform": "reddit", "sentiment": "bearish", "engagement_score": 15.0, "upvotes": 3, "num_comments": 0, "subreddit": "unknown", "author": "Creative_Pin_3175", "url": "https://reddit.com/r/microsoft/comments/1iiaywe/microsoft_product_design_internship_interview/", "ticker": "MSFT", "date": "2025-02-05"}, {"title": "Microsoft Build dates confirmed", "content": "", "created_time": "2025-02-05T17:32:42", "platform": "reddit", "sentiment": "neutral", "engagement_score": 61.0, "upvotes": 61, "num_comments": 0, "subreddit": "unknown", "author": "BippityBoppityWhoops", "url": "https://reddit.com/r/microsoft/comments/1iiflcf/microsoft_build_dates_confirmed/", "ticker": "MSFT", "date": "2025-02-05"}, {"title": "Windows 10 doesn't have the middle-finger emoji", "content": "I was digging around the emoji shortcut thingy, and I couldn't find the middle-finger emoji. I guess they don't want people to be offensive or smt.   \n  \nP.S: I only posted this here bc Windows 10 subreddit probably has enough people complaining about things like ads, telemetry, etc.", "created_time": "2025-02-04T04:04:10", "platform": "reddit", "sentiment": "bearish", "engagement_score": 9.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "Great_Leg_4836", "url": "https://reddit.com/r/microsoft/comments/1ih93nt/windows_10_doesnt_have_the_middlefinger_emoji/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Interview with OG Microsoft DevDiv lead <PERSON><PERSON>", "content": "", "created_time": "2025-02-04T11:51:22", "platform": "reddit", "sentiment": "neutral", "engagement_score": 2.0, "upvotes": 2, "num_comments": 0, "subreddit": "unknown", "author": "itsemdee", "url": "https://reddit.com/r/microsoft/comments/1ihfwvj/interview_with_og_microsoft_devdiv_lead_yuval/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Preferred Work Location for New Grad Role", "content": "I’m a recent graduate starting a fully remote role at Microsoft. I'm considering relocating from the East Coast (DC) to **Seattle** or **Chicago** to boost my early career development.\n\nI loved my internship in Redmond. I was drawn to Seattle’s vibrant city energy, excellent public transit, and stunning nature. The PNW spring/summer climate fits my preference for weather, and being near Microsoft HQ could enhance my learning and networking opportunities.\n\nOn the other hand, Chicago also offers a dynamic tech scene and a large Microsoft office. As a sports fan, I appreciate that both cities are major sports hubs.\n\nI’d love to hear from anyone with experience as a new grad remote worker in Seattle, Chicago, or similar cities. How have your experiences been with learning opportunities, networking, and work-life balance? How did living in your city influence your ability to make friends and build a professional network? Thanks for your insights!", "created_time": "2025-02-04T12:19:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 8.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "NickoHand", "url": "https://reddit.com/r/microsoft/comments/1ihgd9d/preferred_work_location_for_new_grad_role/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Microsoft Data Engineer Interview", "content": "I had my first interview round on January 28th. The recruiter asked me to be available for the entire day as they planned to conduct all interviews on the same day. \nHowever, it's been almost a week with no updates, despite my follow-ups.\n\nThe interview went well—I was able to answer 90% of the questions confidently.\n\n", "created_time": "2025-02-04T14:33:43", "platform": "reddit", "sentiment": "neutral", "engagement_score": 16.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Business_Art173", "url": "https://reddit.com/r/microsoft/comments/1ihizm9/microsoft_data_engineer_interview/", "ticker": "MSFT", "date": "2025-02-04"}, {"title": "Windows on ARM is actually good!", "content": "Hey, \nI recently got a new Copilot+ PC because it was the only good laptop in the price range (Go figure). It has the snapdragon X plus 42-100 in it, 32GB of RAM and 1TB of storage. I got it for $1248 AUD. Anyway, multiple apps I went to install said no arm compatibility and it won't work. I click install and it works flawlessly! Honestly, I was using Linux before getting my laptop and now this is pulling me back to Windows! The battery life is awesome and the fans haven't kicked in once.\nThank you Microsoft and thank you Len<PERSON>!\n\nThe only issue is <PERSON><PERSON><PERSON> doesn't work :(", "created_time": "2025-02-03T07:15:18", "platform": "reddit", "sentiment": "neutral", "engagement_score": 182.0, "upvotes": 98, "num_comments": 0, "subreddit": "unknown", "author": "deleted", "url": "https://reddit.com/r/microsoft/comments/1igjk2g/windows_on_arm_is_actually_good/", "ticker": "MSFT", "date": "2025-02-03"}, {"title": "Starting with Engage -  what are your best tips ?", "content": "Hi redittors, just like many other companies, we're switching from Facebook Workplace to Microsoft Engage. I'm rather happy about it because we'll have a single tool for everything.   \nI had specific questions and would also like to have your opinion and best tips about using Engage ?\n\n\\- image size to post on communities: are there ideal size to use? I could'nt find information online yet\n\n\\- discussion VS praise: how do you use praise? how do your communities react to them?\n\nThanks, i'll be happy to discuss it with you !", "created_time": "2025-02-03T11:45:15", "platform": "reddit", "sentiment": "neutral", "engagement_score": 1.0, "upvotes": 1, "num_comments": 0, "subreddit": "unknown", "author": "helag<PERSON>", "url": "https://reddit.com/r/microsoft/comments/1ign8ou/starting_with_engage_what_are_your_best_tips/", "ticker": "MSFT", "date": "2025-02-03"}, {"title": "Question about Company Store", "content": "My big brother works for MS, so I'm able to access the company store via the Friends ane Family system, right?\nI was wondering if the company store has a \"funds\" system similar to that of Steam, where you can add money into your account to get games with instead of directly using your credit card. Is this system also in the normal MS store included in Windows?", "created_time": "2025-02-03T17:24:36", "platform": "reddit", "sentiment": "neutral", "engagement_score": 12.0, "upvotes": 0, "num_comments": 0, "subreddit": "unknown", "author": "Puzzleheaded_Hat2452", "url": "https://reddit.com/r/microsoft/comments/1igughm/question_about_company_store/", "ticker": "MSFT", "date": "2025-02-03"}, {"title": "If I Had Only Knew.. Before Joining MSFT", "content": "Let me first say Microsoft is a great company. But before accepting the offer there are some things I wish I did a better job of before saying I accept.\n\n* Researched the role a lot more on sites like Reddit and Glassdoor - Titles can be misleading.\n* Knew the level I was coming in at - Not just the number and salary but where you are on the ladder and compare it to where you are now. Be comfortable if you do decide to jump because it might be a while before you move up. Just to note, there are \"firewalls\" between certain levels like 62-63. The criteria to get through the firewalls are not just your average \"I do a good job so promote me.\" So you might want to fight that battle before joining to make sure you come in where you should be based on your experience. A lot of people come in under where they were previously. I've seen VPs come in at Sr. level. Some people just say if the pay is equivalent or better then I'm good, like I did. Microsoft pays more than average and I wouldn't do that again.\n* Knew that what org you come into matters - Don't just think because you are in the building it's easy to change rooms. There are internal biases between rooms and even teams.\n* (As you probably can tell by the recent layoffs) Knew performance matters - I knew this. Duh. Like everywhere performance matters. BUT to others, if you are not a self motivated individual then don't waste your time. Just to note, it's stressful working here. So if you don't do stress well then stay where you are at.\n\nAgain, great company, benefits, learning opportunities and you work with tons of smart people. I'm good.", "created_time": "2025-02-03T17:38:12", "platform": "reddit", "sentiment": "neutral", "engagement_score": 342.0, "upvotes": 248, "num_comments": 0, "subreddit": "unknown", "author": "Zestyclose_Depth_196", "url": "https://reddit.com/r/microsoft/comments/1igustt/if_i_had_only_knew_before_joining_msft/", "ticker": "MSFT", "date": "2025-02-03"}], "metadata": {"timestamp": "2025-07-06T20:03:07.932006", "end_date": "2025-02-10", "days_back": 7, "successful_dates": ["2025-02-10", "2025-02-09", "2025-02-08", "2025-02-07", "2025-02-06", "2025-02-05", "2025-02-04", "2025-02-03"], "failed_dates": [], "source": "local"}}}}