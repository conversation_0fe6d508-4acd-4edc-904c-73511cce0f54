[{"platform": "reddit", "post_id": "reddit_1ksdn9b", "title": "[D] Google already out with a Text- Diffusion Model", "content": "Not sure if anyone was able to give it a test but Google released Gemeni Diffusion, I wonder how different it is from traditional (can't believe we're calling them that now) transformer based LLMs, especially when it comes to reasoning. Here's the announcement:\n\nhttps://blog.google/technology/google-deepmind/gemini-diffusion/\n", "author": "<PERSON><PERSON>u", "created_time": "2025-05-22T00:30:38", "url": "https://reddit.com/r/MachineLearning/comments/1ksdn9b/d_google_already_out_with_a_text_diffusion_model/", "upvotes": 269, "comments_count": 66, "sentiment": "neutral", "engagement_score": 401.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksdycm", "title": "How will Google replace the “shop” buttons on D2C platforms? “I will not promote”", "content": "\nHow will this end the most that D2Cs have?\n\n“I will not promote”.\n\nIs it true that Google hit a big thing on D2C side? \n\nJust curious to know about the thoughts on the recent Google I/o especially on the agent that buys things for you. \n\nHow is it going to affect the D2C players? ", "author": "Extreme-Bird-9768", "created_time": "2025-05-22T00:46:27", "url": "https://reddit.com/r/startups/comments/1ksdycm/how_will_google_replace_the_shop_buttons_on_d2c/", "upvotes": 2, "comments_count": 6, "sentiment": "bullish", "engagement_score": 14.0, "source_subreddit": "startups", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksg34h", "title": "Double check: are we (41M, 35F) ready to retire?", "content": "I would love a second set (or many!) of eyes as I consider going down the long-dreamed-of path of early retirement.\n\nMy wife (35F) and I (41M) are looking to be conservative in our decision to FIRE. As such, we're targeting a 3% withdrawal rate in hopes that we'll be able to leave a sizeable inheritance for our 2 kids (3 and 5yrs).\n\nWe live in a Moderate Cost of Living area. I make approx $120k/yr and my wife makes approx $90k/yr. We both max our 410k.\n\nI'm at my wits end with my job/career and have been dreaming of this moment for years. I can barely stand another day with total burnout. However, she loves her job and may continue until that's no longer the case. However, let's just assume she'll retire in a few years (so we'll have decent insurance through her employer).\n\nCombined assets:\n\nTaxable brokerage: $3.1m\n\nInherited IRA: $100k\n\nRoth IRA: $300k\n\n401k: $600k ttl (200 Roth, 400 Trad'l)\n\n=TOTAL in brokerage: $4.1m\n\n*Allocation: 70% Dom stk, 15% Int'l stk, 15% bonds/short term. Vast majority of stocks are low fee index funds with a bit of AMZN, TSLA, GOOG and other individual stocks held in taxable since ~2008 (so super low cost basis).\n\nAdd'l $100k across HYSAs which we'll draw from first.\n529s for kids: $100k ttl ($50k ea.)\nHome paid off ($600k)\n3 vehicles paid off (approx $50k ttl value)\n\nTotal NW: approx $5m\n\nYearly expenses are currently about $120k, but I figure we could adjust up to $150k or down to $100k without much difficulty if needed during 'up/down' years in the market.\n\nDoes anything look 'off' with the above? Using conservative 3% withdrawals, it appears we're safe to withdraw approx. $120k/yr, but I'm hoping we can justify the $150k annually instead given my wife plans to work a few more years and the paid off home, cars and head start on college funds. Am I missing any obvious concerns or red flags?\n\nThanks for any thoughts/notes. The thought of retiring early is simultaneously thrilling and terrifying. \n\n\n", "author": "NotGreatB0B", "created_time": "2025-05-22T02:37:44", "url": "https://reddit.com/r/Fire/comments/1ksg34h/double_check_are_we_41m_35f_ready_to_retire/", "upvotes": 1, "comments_count": 39, "sentiment": "bullish", "engagement_score": 79.0, "source_subreddit": "Fire", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksgd7j", "title": "The future of Google ads", "content": "I just watched Google I/O 2025 and saw the changes and future of search. My question is: what will be the future of Google ads? \n\nI wonder if Google ads will disappear from search with zero click results, but will Google advertising then shift much more towards YouTube and will Google prioritize video?\n\nVery curious about your thoughts! ", "author": "Turbulent_Invite_286", "created_time": "2025-05-22T02:53:04", "url": "https://reddit.com/r/PPC/comments/1ksgd7j/the_future_of_google_ads/", "upvotes": 52, "comments_count": 81, "sentiment": "neutral", "engagement_score": 214.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksic8k", "title": "Gobsmacked at Google’s Ridiculous Suggestion", "content": "A rant, but possibly also amusing or enraging. You decide.\n\nSo this happened to me a couple of days ago on a call with google on my ads account. I typically decline their “help” but had taken the chance about 3 months ago and struck it lucky with a woman who really seemed to “get it” and offered some minor but useful tweaks to my campaign. This week I had another call but sadly a new “more senior” person.\n\nBackground: I’m a comedy hypnotist entertainer - my campaign targets people looking for corporate entertainment ideas. I don’t need to target people looking for stage hypnotists because there are only a handful in my market and searchers will find me anyway. My target is people who are looking for something different but dont yet know what that might be. \n\nThose of you with any experience at all will immediately see that p-max is NOT a good fit for me. I have to explain this every time to the googletrons by pointing out that their machine learning will examine my site, decide that I am a hypnotist (true) and then make ads for hypnotists. Not useful. I explained that to my “consultant<PERSON> du jour on a call last Tuesday.\n\nHere it comes: she did some “investigation” and decided to suggest to me that I remove the word “hypnotist” from my website almost entirely so that their “AI” would them make ads more related to corporate entertainment.\n\nYes folks, I should change my site so their dodgy AI gets the right result for this campaign. Moreover I should remove from my site the description of the service I provide so as not to confuse that AI. Presumably my clients having clicked the p-max ad would simply book “entertainment” without actually knowing what it was?\n\nI explained to her that that was like telling melbourne zoo to eliminate all talk of zoos and animals from their site to run a campaign targeting “things to do with the family on the weekend”. I’m not entirely sure she got it.\n\nHowever I remain aghast and sometimes amused at the sheer chutzpah of that suggestion - only possible for large monopolies unmoored from the realities of their customer needs.\n\nThanks for letting me share this rant.\n", "author": "gerardv-anz", "created_time": "2025-05-22T04:44:37", "url": "https://reddit.com/r/PPC/comments/1ksic8k/gobsmacked_at_googles_ridiculous_suggestion/", "upvotes": 93, "comments_count": 27, "sentiment": "bearish", "engagement_score": 147.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kskqqq", "title": "Can you advertise Paintball Experiences on Google Ads?", "content": "I've got a partner who owns a paintball company and from Google's literature on the topic, it isn't clear if you can or cannot advertise paintball experiences as per this link to their dangerous goods policy. [https://support.google.com/adspolicy/answer/9683742?hl=en&sjid=8659124719095600084-NC](https://support.google.com/adspolicy/answer/9683742?hl=en&sjid=8659124719095600084-NC) so, can you advertise paintball experiences on Google ads?", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-05-22T07:24:57", "url": "https://reddit.com/r/adwords/comments/1kskqqq/can_you_advertise_paintball_experiences_on_google/", "upvotes": 1, "comments_count": 3, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksm81p", "title": "3 Teens Almost Got Away With Murder. Then Police Found Their Google Searches", "content": "", "author": "<PERSON><PERSON><PERSON>man", "created_time": "2025-05-22T09:13:08", "url": "https://reddit.com/r/privacy/comments/1ksm81p/3_teens_almost_got_away_with_murder_then_police/", "upvotes": 684, "comments_count": 109, "sentiment": "neutral", "engagement_score": 902.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1<PERSON><PERSON><PERSON>", "title": "Career shifting to Autonomous Vehicle Controls Software Engineer", "content": "Hello everyone!\n\nI'm a Mechanical Engineering graduate, currently working remotely as an ICE Controls Engineer in a Automotive Software Company. My thesis was focused on System Dynamics and Full State Feedback Controller design. I also have a background in vehicle structure and mathematical modeling, thanks to my time leading an autonomous vehicle (AV) team during university.\n\nAt the moment, I’d say I’m around 6/10 in Control Theory — strong on the theoretical side but lacking hands-on experience with embedded systems and microcontrollers. I’m currently pursuing a second master's degree in Intelligent Systems Engineering, where my thesis is focused on **State of Health (SOH) estimation for Li-ion battery packs**.\n\nHere’s the catch:  \nWhile I have experience with powertrain modeling, system modeling, and some exposure to ROS through AV testing internships, **I don’t have practical embedded systems knowledge**. I don't know how to code microcontrollers, simulate low-level systems, or assess ECU-compatibility from a coding perspective.\n\nThat’s what I’m aiming to change.\n\n**My current roadmap:**\n\n* **Learn Python** via *\"Python for Everybody – Full University Course\" (YouTube)* – currently ongoing\n* **Follow up with CS50 (Harvard’s Intro to CS)** for foundational understanding\n* **Move into microcontroller applications** (Ard<PERSON><PERSON>, Raspberry Pi)\n* **Eventually combine with embedded systems + control applications**\n* **Buy a 3D printer** to start rapid prototyping at home\n\nI’d love your feedback:\n\n* Am I on a reasonable path?\n* Should I add or skip something?\n* Any resources or tips for combining embedded + control systems in a practical way?\n\nPlease feel free to throw any advice, book recommendations, or opinions in the comments — I’m all ears!", "author": "justfiltered", "created_time": "2025-05-22T13:59:12", "url": "https://reddit.com/r/AutonomousVehicles/comments/1ksreyn/career_shifting_to_autonomous_vehicle_controls/", "upvotes": 5, "comments_count": 6, "sentiment": "bullish", "engagement_score": 17.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksur4x", "title": "<PERSON><PERSON><PERSON><PERSON> at Google I/O", "content": "", "author": "diplomat33", "created_time": "2025-05-22T16:15:08", "url": "https://reddit.com/r/SelfDrivingCars/comments/1ksur4x/dolgov_at_google_io/", "upvotes": 55, "comments_count": 48, "sentiment": "neutral", "engagement_score": 151.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksutta", "title": "Google faces antitrust investigation over deal for AI-fueled chatbots", "content": "No paywall: [https://finance.yahoo.com/news/google-faces-antitrust-investigation-over-*********.html](https://finance.yahoo.com/news/google-faces-antitrust-investigation-over-*********.html)\n\nPaywall: [https://finance.yahoo.com/news/google-faces-antitrust-investigation-over-*********.html](https://finance.yahoo.com/news/google-faces-antitrust-investigation-over-*********.html)\n\n(Bloomberg) — The Justice Department is probing whether Alphabet Inc.’s Google violated antitrust law with an agreement to use the artificial intelligence technology of a popular chatbot maker, according to people with knowledge of the matter.\n\nAntitrust enforcers have recently told Google they’re examining whether it structured an agreement with the company known as [Character.AI](http://Character.AI) to avoid formal government merger scrutiny, said the people, who asked not to be identified discussing the confidential probe. In a deal with Google last year, the founders of the chatbot maker joined the search firm, which also got a non-exclusive license to use their venture’s technology.\n\nDeals like the one Google struck have been hailed in Silicon Valley as an efficient way for companies to bring in expertise for new projects. However, they’ve also caught the attention of regulators wary of mature technology companies using their clout to head off competition from new innovators.\n\nGoogle is “always happy to answer any questions from regulators,” <PERSON>, a company spokesperson, said in an e-mailed statement. “We’re excited that talent from [Character.Ai](http://Character.Ai) has joined the company but we have no ownership stake and they remain a separate company.”\n\nThe Justice Department can scrutinize whether the transaction itself is anticompetitive even if didn’t require a formal review. Google hasn’t been accused of wrongdoing as part of the antitrust probe, which is in early stages and may not lead to an enforcement action.\n\nA spokesperson for the Justice Department declined to comment. A representative for [Character.AI](http://Character.AI) didn’t respond to requests for comment.\n\nStarting under the Biden administration, enforcers began scrutinizing competition throughout the rapidly evolving AI ecosystem, including specialized chips and the supply of computing power. As part of that focus, the government is looking at whether partnerships with AI startups give the largest tech companies an unfair advantage as the technology develops.\n\n[Character.AI](http://Character.AI) is known for chatbots that can virtually mimic anyone or anything. Its founders previously worked at Google before leaving several years ago to start the new company. Following the deal, they rejoined Google last year, along with some members of its research team.\n\nBloomberg reported in August that under its deal with Google, existing Character.AI investors were to see shares bought out at a price that would translate to a $2.5 billion valuation for the company. As part of the deal, the startup entered into a non-exclusive licensing deal with Google for its large language model technology. Character.AI meanwhile continues to exist.\n\nThe Justice Department civil investigation could also ratchets up antitrust scrutiny on Google following federal court rulings that the company had illegal monopolies in the online search and advertising technology markets.\n\nIn the online search case, the Justice Department has proposed forcing Google to spin off its Chrome browser as a way to restore competition in search market.\n\nAs part of the case, the government has also urged a judge to ban Google from paying for search engine defaults, including with AI products, and allow enforcers to examine any AI-related acquisition by the company, regardless of whether it triggers the threshold for a formal review. A ruling is expected in the summer.", "author": "callsonreddit", "created_time": "2025-05-22T16:18:03", "url": "https://reddit.com/r/StockMarket/comments/1ksutta/google_faces_antitrust_investigation_over_deal/", "upvotes": 18, "comments_count": 2, "sentiment": "neutral", "engagement_score": 22.0, "source_subreddit": "StockMarket", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksuxd8", "title": "Google LSA’s not allowing you to dispute leads, automation/feedback option does not credit back clearly unqualified leads", "content": "I run these LSA’s in addition to search ads for a lawyer client of mine, we recently turned on LSA’s for the qualified lead aspect although they’re much higher. Many of the leads clarify that they were not injured or at fault (she’s a personal injury lawyer and we only bid on PI - auto) i tried calling and they said there’s nothing i can do with the thousands spent and whenever i give the feedback they do not credit the amount. Anyone have any advice? Thinking i should just stick to search ads. TIA! Kayce @ Abode Marketing ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-22T16:21:55", "url": "https://reddit.com/r/marketing/comments/1ksuxd8/google_lsas_not_allowing_you_to_dispute_leads/", "upvotes": 2, "comments_count": 1, "sentiment": "neutral", "engagement_score": 4.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksv6dz", "title": "How do get my company to come up in Google Search results when potential clients search for me?", "content": "I just started a new business a few months ago. Business is legally incorporated, has a website, address, phone number, domain etc. \n\nWhen I google my company name my website doesn't even come up. My business name is unique, but the words are common and there are other businesses with similar names.\n\nHow can I get my business into the search results when someone googles the name? I am doing a lot of cold outreach and so want to ensure that potential clients can find my business if they want to google me to make sure I am legitimate with a third party source", "author": "moonshine_estate", "created_time": "2025-05-22T16:31:55", "url": "https://reddit.com/r/smallbusiness/comments/1ksv6dz/how_do_get_my_company_to_come_up_in_google_search/", "upvotes": 2, "comments_count": 20, "sentiment": "neutral", "engagement_score": 42.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ksvalf", "title": "250 - 83k on Google", "content": "Started with $250 in puts the day it dropped 8%. Hit 6k and flipped calls. Cashing all but 25 k out.  Going to pay off debt and buy stock instead of options. \n\nLife changing shit right there.", "author": "Rough_Resource_2900", "created_time": "2025-05-22T16:36:45", "url": "https://reddit.com/r/wallstreetbets/comments/1ksvalf/250_83k_on_google/", "upvotes": 434, "comments_count": 73, "sentiment": "neutral", "engagement_score": 580.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kt04dz", "title": "New head of Social Security, hired from Wall Street, tells staff he had to Google the job when he was offered it", "content": "", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-22T19:50:55", "url": "https://reddit.com/r/nottheonion/comments/1kt04dz/new_head_of_social_security_hired_from_wall/", "upvotes": 3315, "comments_count": 76, "sentiment": "neutral", "engagement_score": 3467.0, "source_subreddit": "nottheonion", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kt36hj", "title": "Google's Veo 3 Is Already Deepfaking All of YouTube's Most Smooth-Brained Content", "content": "", "author": "9th_username", "created_time": "2025-05-22T21:56:30", "url": "https://reddit.com/r/technology/comments/1kt36hj/googles_veo_3_is_already_deepfaking_all_of/", "upvotes": 12387, "comments_count": 1203, "sentiment": "neutral", "engagement_score": 14793.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kt47pn", "title": "Google pays Stackoverflow to use its data...that we created?", "content": "Interesting story on Wired, \"Google’s Deal With Stack Overflow Is the Latest Proof That AI Giants Will Pay for Data\"\n\n[https://www.wired.com/story/google-deal-stackoverflow-ai-giants-pay-for-data/](https://www.wired.com/story/google-deal-stackoverflow-ai-giants-pay-for-data/)\n\nTOS checkboxes and all, I get it...but we created all of the knowledge on SO and now Google is paying them to train AI based on our actual knowledge.\n\nKind of like Facebook makes a trillion on us writing their content. ", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-05-22T22:43:02", "url": "https://reddit.com/r/webdev/comments/1kt47pn/google_pays_stackoverflow_to_use_its_datathat_we/", "upvotes": 357, "comments_count": 159, "sentiment": "neutral", "engagement_score": 675.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}]