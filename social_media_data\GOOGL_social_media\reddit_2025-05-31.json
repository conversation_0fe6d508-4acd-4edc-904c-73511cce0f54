[{"platform": "reddit", "post_id": "reddit_1kzld43", "title": "Google sees a 'significant rise' in Pixel market share for Canada", "content": "", "author": "wickedplayer494", "created_time": "2025-05-31T01:37:24", "url": "https://reddit.com/r/GooglePixel/comments/1kzld43/google_sees_a_significant_rise_in_pixel_market/", "upvotes": 704, "comments_count": 123, "sentiment": "neutral", "engagement_score": 950.0, "source_subreddit": "GooglePixel", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kzrtpo", "title": "Today i learned that currency exchange doesn't scale linearly, thanks Google", "content": "", "author": "666y4nn1ck", "created_time": "2025-05-31T08:03:32", "url": "https://reddit.com/r/google/comments/1kzrtpo/today_i_learned_that_currency_exchange_doesnt/", "upvotes": 912, "comments_count": 43, "sentiment": "neutral", "engagement_score": 998.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kzxspr", "title": "Google Gemini integration in Siri might be a bigger deal than we initially thought", "content": "", "author": "Fer65432_Plays", "created_time": "2025-05-31T14:01:04", "url": "https://reddit.com/r/apple/comments/1kzxspr/google_gemini_integration_in_siri_might_be_a/", "upvotes": 971, "comments_count": 183, "sentiment": "neutral", "engagement_score": 1337.0, "source_subreddit": "apple", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kzyrv1", "title": "Is anyone rethinking their SEO strategy after Google’s new AI Mode + Query Fan-Out?", "content": "With the rollout of AI Mode, Google’s now using a “Query Fan-Out” approach- basically breaking one search into multiple sub-questions, pulling content from all over the web to build a richer AI-generated answer.\nIt’s no longer about ranking for a single keyword- now it’s about being the most useful answer to one of those many sub-queries.\nThis got me thinking…\n\nAre content clusters and topical authority the new baseline?How are you adjusting your site structure or content strategy for this?Anyone seeing traffic shifts already from AI Overviews?Would love to hear how others are approaching this shift. Are we looking at the future of SEO… or another hype cycle?", "author": "One_Title_6837", "created_time": "2025-05-31T14:45:22", "url": "https://reddit.com/r/DigitalMarketing/comments/1kzyrv1/is_anyone_rethinking_their_seo_strategy_after/", "upvotes": 6, "comments_count": 16, "sentiment": "bullish", "engagement_score": 38.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kzzg2b", "title": "[Showoff Satuday] I built an open source Google Analytics alternative", "content": "I've been building [Rybbit ](https://rybbit.io)since the start of this year because I felt that web analytics could be a lot more fun. \n\nI'd been using Google Analytics for years, and the it kept getting harder to use for no reason as it became obvious that they were not building a tool designed for people like me. \n\nSo far I've gotten ⭐6000 GitHub stars since launch earlier this month!", "author": "FantasticTraining731", "created_time": "2025-05-31T15:14:40", "url": "https://reddit.com/r/webdev/comments/1kzzg2b/showoff_satuday_i_built_an_open_source_google/", "upvotes": 281, "comments_count": 61, "sentiment": "neutral", "engagement_score": 403.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kzzkr8", "title": "It’s <PERSON><PERSON>’s World. We’re All Just Riding in It: WSJ", "content": "https://www.wsj.com/tech/waymo-cars-self-driving-robotaxi-tesla-uber-0777f570?\n\nAnd then the archived link for paywall: https://archive.md/8hcLS\n\nUnless you live in one of the few cities where you can hail a ride from Waymo, which is owned by Google’s parent company, Alphabet, it’s almost impossible to appreciate just how quickly their streets have been invaded by autonomous vehicles. \n\nWaymo was doing 10,000 paid rides a week in August 2023. By May 2024, that number of trips in cars without a driver was up to 50,000. In August, it hit 100,000. Now it’s already more than 250,000. \nAfter pulling ahead in the race for robotaxi supremacy, Waymo has started pulling away. \n\nIf you study the Waymo data, you can see that curve taking shape. \nIt cracked a million total paid rides in late 2023. By the end of 2024, it reached five million. We’re not even halfway through 2025 and it has already crossed a cumulative 10 million. At this rate, Waymo is on track to double again and blow past 20 million fully autonomous trips by the end of the year.\n“This is what exponential scaling looks like,” said <PERSON><PERSON><PERSON>, Waymo’s co-chief executive, at Google’s recent developer conference. \n", "author": "FarrisAT", "created_time": "2025-05-31T15:20:09", "url": "https://reddit.com/r/singularity/comments/1kzzkr8/its_waymos_world_were_all_just_riding_in_it_wsj/", "upvotes": 317, "comments_count": 177, "sentiment": "neutral", "engagement_score": 671.0, "source_subreddit": "singularity", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1l00sma", "title": "What's the technical argument that Tesla will face fewer barriers to scaling than Argo, Cruise, Motional, and early-stage Waymo did?", "content": "I'm happy to see Tesla switching their engineers to the passenger seat in advance of the June 12th launch.  But I'm still confused about the optimism about Tesla's trajectory. Specifically, today on the Road to Autonomy Podcast, the hosts seemed to predict that Tesla would have a bigger ODD in Austin than Waymo by the end of the year.\n\nI'm very much struggling to see <PERSON><PERSON>'s path here. When you're starting off with 1:1 remote backup operations, avoiding busier intersections, and a previously untried method of going no-driver (i.e. camera-only), that doesn't infuse confidence that you can scale past the market leader in terms of roads covered or number of cars, quickly.\n\nThe typical counter-argument I hear is that the large amount of data from FSD supervised, combined with AI tech, will, in essence, slingshot reliability. As a matter of first principles, I see how that could be a legitimate technical prediction.  However, there are three big problems. First, this argument has been made in one form or another since at least 2019, and just now/next month we have reached a driverless launch. (Some slingshot--took 6+ years to even start.) Second, Waymo has largely closed the data gap-- 300K driverless miles a day is a lot of data to use to improve the model. Finally, and most importantly, I don't see evidence that large data combined with AI will solve all  the of specific problems other companies have had in switching to driverless.\n\nAI and data doesn't stop lag time and 5G dead zones, perception problems common in early driverless tests, vehicles getting stuck, or the other issues we have seen. Indeed, we know there are unsolved issues, otherwise Tesla wouldn't need to have almost a Chandler, AZ-like initial launch.  Plus Tesla is trying this without LiDAR, which may create other issues, such as insufficient redundancy or problems akin to what prompts interventions with FSD every few hundred miles.\n\nIn fact, if anyone is primed to expand in Austin, it is Waymo-- their Austin geofence is the smallest of their five and Uber is anxious to show autonomy growth, so it is surely asking for that geofence to expand. And I see no technical challenges to doing that, given what Waymo has already done in other markets.\n\nWhat am I missing?", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-05-31T16:11:37", "url": "https://reddit.com/r/SelfDrivingCars/comments/1l00sma/whats_the_technical_argument_that_tesla_will_face/", "upvotes": 69, "comments_count": 307, "sentiment": "bullish", "engagement_score": 683.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1l08ufz", "title": "How can I possibly rank at the top of Google search results when the space is dominated by highly capitalized companies with massive resources and strong media presence?", "content": "If the top results on Google are dominated by companies with massive capitalization and numerous backlinks from major news outlets, how can I compete in such a market? I run a file transfer and sharing service.", "author": "HilbertSpac3", "created_time": "2025-05-31T22:02:24", "url": "https://reddit.com/r/SEO/comments/1l08ufz/how_can_i_possibly_rank_at_the_top_of_google/", "upvotes": 40, "comments_count": 45, "sentiment": "bullish", "engagement_score": 130.0, "source_subreddit": "SEO", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1l090p5", "title": "[D] Internal transfers to Google Research / DeepMind", "content": "Quick question about research engineer/scientist roles at DeepMind (or Google Research).\n\nWould joining as a SWE and transferring internally be easier than joining externally?\n\nI have two machine learning publications currently, and a couple others that I'm submitting soon. It seems that the bar is quite high for external hires at Google Research, whereas potentially joining internally as a SWE, doing 20% projects, seems like it might be easier. Google wanted to hire me as a SWE a few years back (though I ended up going to another company), but did not get an interview when I applied for research scientist. My PhD is in theoretical math from a well-known university, and a few of my classmates are in Google Research now.", "author": "random_sydneysider", "created_time": "2025-05-31T22:10:31", "url": "https://reddit.com/r/MachineLearning/comments/1l090p5/d_internal_transfers_to_google_research_deepmind/", "upvotes": 105, "comments_count": 54, "sentiment": "neutral", "engagement_score": 213.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "GOOGL"}]