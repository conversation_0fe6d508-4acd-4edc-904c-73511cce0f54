[{"platform": "reddit", "post_id": "reddit_1kp97ke", "title": "Tesla pulls all the demand levers with discounts and incentives as sales crash", "content": "", "author": "SpriteZeroY2k", "created_time": "2025-05-18T02:05:14", "url": "https://reddit.com/r/electricvehicles/comments/1kp97ke/tesla_pulls_all_the_demand_levers_with_discounts/", "upvotes": 1085, "comments_count": 460, "sentiment": "bearish", "engagement_score": 2005.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kp9s0v", "title": "Locate Correct Replacement Battery Help", "content": "I have an HP Roar Bluetooth Speaker which was perfectly functional and great sounding until it refused to recharge. It is about 10 years old, so I can't complain too much. I don't want to generate e-waste for no reason, and I would like to replace the battery to keep using it. The battery has three leads, red, white and black, but all the batteries I have found so far on Amazon are shown with only two leads - red and black. If anyone could offer assistance/information I would be very grateful. The last thing I want to do is incorrectly wire up a lithium battery. \n\nhttps://preview.redd.it/dxbuelf2dg1f1.jpg?width=662&format=pjpg&auto=webp&s=e020719401274fb97fa8cc305b007b953c4cdf88\n\n", "author": "DukeOfUkes", "created_time": "2025-05-18T02:37:29", "url": "https://reddit.com/r/batteries/comments/1kp9s0v/locate_correct_replacement_battery_help/", "upvotes": 0, "comments_count": 6, "sentiment": "neutral", "engagement_score": 12.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpbozu", "title": "Tesla Robotaxi Will Have ‘Lots Of Tele-Ops’—Which Means Supervised FSD", "content": "", "author": "RepresentativeCap571", "created_time": "2025-05-18T04:30:27", "url": "https://reddit.com/r/SelfDrivingCars/comments/1kpbozu/tesla_robotaxi_will_have_lots_of_teleopswhich/", "upvotes": 172, "comments_count": 316, "sentiment": "neutral", "engagement_score": 804.0, "source_subreddit": "SelfDrivingCars", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpib8z", "title": "Reddit generates a new link every time you click share", "content": "They are 100% tracking which users share and which users open shared posts.\n\nThey know everyone who live or work together and are sharing posts.\n\nThey know all your friends you share your posts with.", "author": "SlovenianTherapist", "created_time": "2025-05-18T12:00:21", "url": "https://reddit.com/r/privacy/comments/1kpib8z/reddit_generates_a_new_link_every_time_you_click/", "upvotes": 3539, "comments_count": 289, "sentiment": "neutral", "engagement_score": 4117.0, "source_subreddit": "privacy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpkvxw", "title": "Google ads to grow your business!", "content": "Hi everyone, I've recently completed a Google Ads course and am eager to gain real-world experience. I'm offering my services free of charge to a few businesses, providing expert help with setting up new campaigns, auditing and optimizing existing ones, keyword research, ad copy creation, and performance analysis. In return for positive results or actionable insights, I would be grateful for a video testimonial about your experience working with me. This is a great opportunity for you to receive dedicated Google Ads support at no cost, while I build my portfolio. If interested, please send me a direct message with details about your business and Google Ads needs. I look forward to potentially collaborating!\n", "author": "No-File-718", "created_time": "2025-05-18T14:11:39", "url": "https://reddit.com/r/adwords/comments/1kpkvxw/google_ads_to_grow_your_business/", "upvotes": 3, "comments_count": 3, "sentiment": "bullish", "engagement_score": 9.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kplbk9", "title": "On site nuclear reprocessing", "content": "  \nNuclear reprocessing will be needed to maximize the efficiency of nuclear energy. The remaining uranium and plutonium in spent nuclear fuel is still fissile and thus should be used to make new fuel (mixed oxide fuel). **What if we could reprocess SNF on site rather than at a centralized reprocessing facility**.  \n\nOn-site reprocessing **would require small scale (likely modular) reprocessing technologies**.  This means either downscaling existing reprocessing technologies like PUREX or incorporating small scale into next generation reprocessing technologies like pyroprocessing. The footprint of NPPs will be increased by the addition of on-site reprocessing so it is important to minimize the increase in order to reduce cost, public opposition and regulatory hurdles.  \n\nHere is how on site reprocessing would work \n\n1. SNF is transferred from the NPPs spent fuel pools into a radiation shielded self propelled platform with omni directional tires. \n\n2. The self propelled platform is driven across a short paved pathway to the on site reprocessing facility  \n\n3. The SNF is unloaded from the self propelled platform \n\n4. The SNF is reprocessed using either an downscaled existing or next generation reprocessing technology \n\n5. MOX fuel is manufactured from the separated uranium and plutonium   \n\n6. The fission products are packaged in radiation shielded containers and sold to nuclear battery manufacturers. \n\n7. The MOX fuel is transported to the reactor buildings using the same self propelled platform from earlier \n\n8. The MOX fuel is unloaded from the self propelled platform and either loaded into the reactor immediately or stored for later. \n\nThere might be some variations but this is the basic idea \n\n**Curio's modular reprocessing technology could act as a basis for developing an on-site nuclear reprocessing technology**. Modularity will reduce construction cost and time. The same logic behind making power reactors modular also applies to reprocessing technologies. \n\nThe main benefit of this idea are \n\n\\- **No SNF transport required**, eliminating the logistical, safety, and security challenges of moving radioactive materials over long distances.\n\n\\- Lower **public and regulatory resistance** due to reduced perceived risk \n\n\\- Potential to scale with new reactor builds, especially advanced reactors that may be **sited in remote locations** \n\nWhat challenges do you think would arise in licensing or safeguarding such systems? Could reprocessing technologies really be made compact enough? Share your thoughts. ", "author": "Live_Alarm3041", "created_time": "2025-05-18T14:31:26", "url": "https://reddit.com/r/CleanEnergy/comments/1kplbk9/on_site_nuclear_reprocessing/", "upvotes": 3, "comments_count": 2, "sentiment": "neutral", "engagement_score": 7.0, "source_subreddit": "CleanEnergy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kplovq", "title": "Hello, could you please help me filling out this form?", "content": "Hi! I'm working on a project for a class in university exploring a new transportation service for people going on a cruise. Among other things, it's investigating the possibility of using an autonomous vehicle for such service. Would you be so kind helping me filling out this form?  Thank you so much to everyone that will answer! \n\n[Form](https://forms.cloud.microsoft/Pages/ResponsePage.aspx?id=qjD8eCGPfUqfGqOZPiKTSZ8pRHqKludDk1EWYViUW1tUNFM4WjdZR0ozNFNBTzYyVjBaUUFCSEpXUC4u&r33e10332bcca42bc9910b417af4b90ab=r%2FAutonomousVehicles)\n\nPS: I know in some questions answers will be biased and this is not the only place where I'm gathering responses. In this regard, please do not change the text in the last question", "author": "No-Worldliness-8824", "created_time": "2025-05-18T14:47:55", "url": "https://reddit.com/r/AutonomousVehicles/comments/1kplovq/hello_could_you_please_help_me_filling_out_this/", "upvotes": 0, "comments_count": 0, "sentiment": "neutral", "engagement_score": 0.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpn715", "title": "Our Google Sheets spend tracking system is driving me mad - help!", "content": "My partner and I have been using a Google Sheet to track our shared expenses for the past year. Every two weeks we manually update who paid what, calculate splits, and transfer money to settle up.\n\nIt takes a lot of time and I’m looking for an alternative. Anyone found a better system for tracking shared expenses while maintaining individual budgets? We tried Splitwise but we usually add a couple transactions at once so we need to work around the paywall, which is inconvenient. However, I don't see myself getting the paid version.", "author": "plateg9", "created_time": "2025-05-18T15:54:42", "url": "https://reddit.com/r/personalfinance/comments/1kpn715/our_google_sheets_spend_tracking_system_is/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpnsk3", "title": "Can someone guide me about Cloud/VPS pricing for beginners?", "content": "So basically, I'm building an app and I need to find the cheapest best possible cloud or VPS Hosting for UK region. It'll be a medium complexity app with a low number of users (<1000) but it has some heavy background tasks and infrastructure tools.\nSo basically something like:\nFrontend + API Backend + Long running scripts + PostgreSQL + Redis + some vectorDB.\nI'm planning to outsource some of this to managed services but still need to make sure I choose the cheapest option possible but highly scalable and available in the future.\nBut I have no idea how to choose Providers since I don't really understand the not-so-simple pricing they have.\nI have some experience working on AWS, but I'm also looking into GCP, Hetzner+Coolify mix, Digital Ocean, and Oracle cloud free-tier(really bad reviews for this one so probably not).\nPlease guide how to make the right choice.", "author": "jobsearcher_throwacc", "created_time": "2025-05-18T16:20:27", "url": "https://reddit.com/r/cloudcomputing/comments/1kpnsk3/can_someone_guide_me_about_cloudvps_pricing_for/", "upvotes": 7, "comments_count": 14, "sentiment": "bullish", "engagement_score": 35.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kps3lk", "title": "🚀 Wall Street Radar: Stocks to Watch Next Week - 18 May", "content": "**Updated Portfolio:**\n\nCOIN: Coinbase Global Inc  \nTSLA: Tesla Inc  \nSEZL: Sezzle Inc\n\nComplete article and charts [HERE](https://www.gb.capital/p/wall-street-radar-stocks-to-watch-253)\n\n**In-depth analysis of the following stocks:**\n\n* CMP: Compass Minerals International  \n* RUN: Sunrun Inc\n* TTD: The Trade Desk Inc\n* BULL: Webull Corporation\n* OS: OneStream Inc \n* ECVT: Ecovyst Inc", "author": "Market_Moves_by_GBC", "created_time": "2025-05-18T19:23:34", "url": "https://reddit.com/r/economy/comments/1kps3lk/wall_street_radar_stocks_to_watch_next_week_18_may/", "upvotes": 2, "comments_count": 0, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "economy", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpsnio", "title": "Website not showing up on Google", "content": "I need some advice as I dont know anything about tech and SEO etc. I have a website called https//www.balancednuttitionsolutions.ca that I started a month ago. It is not showing up on Google search. I have submitted the website to Google console and done everything I need to for SEO like add meta tags and description for all the pages and images. Google console says that my website is not showing up because it is a ‘page with redirect’. I used to have a similar website www.balancednutritionsolutions.com years ago. Could that be a problem for my new website? I have no idea what to do to get it to show up on google. ", "author": "Canadiansnow1982", "created_time": "2025-05-18T19:47:15", "url": "https://reddit.com/r/webdev/comments/1kpsnio/website_not_showing_up_on_google/", "upvotes": 1, "comments_count": 43, "sentiment": "neutral", "engagement_score": 87.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpvbx3", "title": "How will Robotaxi work with only L2 as the current level", "content": "Forgive me for sounding like a naysayer here. I'm just trying to understand how this will work out, and how <PERSON><PERSON>'s vision of having self driving Robotaxis in June in Austin Texas is supposed to come true. Because as the CEO put it in one of the videos of him visiting a TESLA factory, this is make  or break for <PERSON><PERSON>. \"If they can't solve fsd, the company is dead\" was more or less the quote.   \n   \nAs of early 2025, Tesla's FSD remains in beta testing, requiring active driver supervision. It's classified as a Level 2 autonomous system, meaning the driver must maintain control and attention at all times.  \n\n\nBut with the design that has been showcased when they unveiled the Robotaxis/Cybercab, there were no steering wheel, so human intervention is not possible in that scenario.  \n  \n So how would it work ? \n\nAlso what do you think about the latest news that the Robotaxi event will be invite only ? I feel like it's a bad sign when they are invite only, as that means that they have an unfinished product and thus need to invite a selected few that they know will talk positively about Robotaxi to sell the experience. So basically just a big marketing campaign.   \n\nAlso it has been confirmed that there will be operators that will be supervising the rides and providing assistance if needed in tough situations that the Tesla \"FSD\" can't handle.\n\n", "author": "PleasantAnomaly", "created_time": "2025-05-18T21:44:25", "url": "https://reddit.com/r/teslainvestorsclub/comments/1kpvbx3/how_will_robotaxi_work_with_only_l2_as_the/", "upvotes": 4, "comments_count": 137, "sentiment": "neutral", "engagement_score": 278.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpwcvv", "title": "Is there a way to get the \"Search image with Google Lens\" functionality back?", "content": "I found the \"Search image with Google Lens\" function in the context menu to be a FANTASTIC function and I have been more and more disappointed with every replacement they make for it.\n\nIs there a way to get \"Search image with Google Lens\", the one with the Search/Text/Translate options on the side panel, available in my version of Chrome again? Like with the flags or something?\n\n\nChrome Version: 136.0.7103.114\n\nOS: Windows 10 Home 19045.5854", "author": "Confident-House-1566", "created_time": "2025-05-18T22:30:21", "url": "https://reddit.com/r/chrome/comments/1kpwcvv/is_there_a_way_to_get_the_search_image_with/", "upvotes": 5, "comments_count": 11, "sentiment": "neutral", "engagement_score": 27.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kpwzoo", "title": "Well played.", "content": "I thought this was a clever ad I spotted in the wild.\n\nAny other words out there that could fit the technique?", "author": "Virago_XV", "created_time": "2025-05-18T23:01:08", "url": "https://reddit.com/r/marketing/comments/1kpwzoo/well_played/", "upvotes": 664, "comments_count": 94, "sentiment": "neutral", "engagement_score": 852.0, "source_subreddit": "marketing", "hashtags": null, "ticker": "TSLA"}]