[{"platform": "reddit", "post_id": "reddit_1ktz6po", "title": "[25] Looking for Feedback on My Financial Plan – Loans, Investing, Emergency Fund, and Big Life Changes Ahead", "content": "Hi r/personalfinance! I’d really appreciate feedback on my current financial plan and whether I’m on the right track. I’ve been trying to stay intentional with my money while juggling student loans, investing, and preparing for some big life milestones.\n\nInvestments & Accounts\n\t•\tRoth IRA – $32.1k (opened 3 years ago, maxed this year). Asset mix: ~67% domestic stocks, 19% international, 12% bonds, 2% short-term. I have less than 20% in individual stocks (I got lucky and purchased some while everything was recovering after COVID, and they exploded upwards since). The holdings are spread across various funds I picked early on when I was still learning. I’ve since become more educated and am working to consolidate into a cleaner, Boglehead-esque portfolio with 4 core funds (can’t mentally commit to ONLY 3). I have no plans to sell the stocks anytime soon but will see what happens long-term. \nCurrent holdings: AAPL, AMZN, BRKB, DIS, FBGRX, FFRHX, FSELX, FSKAX, FTBFX, FTIHX, FXAIX, FXNAX, FZILX, FZROX, GOOGL, SCHD, SCHG, TSLA\n\t•\t403(b) – $4.3k (ROTH contributions at 7% = ~$220/paycheck). My employer matches 50% of the first 4%, and it’s vested in 3 years. \n\t•\tBrokerage (Fidelity) – $5.6k in SPAXX. Currently being used as a quasi HYSA but I like that it’s liquid, can be used for investments without additional movement later on (don’t need to open another account), and I can throw it into short term CDs if higher rates are available. It came with a debit card\n\t•\tI have a TreasuryDirect account that my family has given me bonds/bills as gifts in. I’ve made no contributions as of yet, but it contains $900 in T-bills, $800 in I-bonds face value, and $2,450 face value in EE bonds (maturing around 2035)\n\t•\tI also have a $600 CD that will be cashed and moved into another account come November. It was a gift with a bank and I want to consolidate\n\nDebts\n\t•\tCredit cards – Always paid in full each month\n\t•\tStudent loans – All undergrad loans paid off. Grad loans currently in forbearance until 2026 (no required payments), but I’m still paying them down aggressively using the avalanche method:\n\t•\t$19,692 @ 6.54%\n\t•\t$19,448 @ 5.28%\n\t•\tPaying at least $500/month, often more (some months $1k+). I’m anticipating this will be near my monthly payment amount once I’m required to start paying, so I want to adequately budget for this as is. \n\nIncome\n\t•\tMain job (healthcare) – ~$2,050 biweekly take-home\n\t•\tStarting in July: plan to pick up an extra 10 hours/month of bonus pay (~$450 post-tax)\n\t•\tSide gigs: I referee rugby, making ~$3750/year, and earn $600/year as a student supervisor in a pro bono PT clinic\n\t•\tOne time “gifts” expected this year still include $1,600 post-tax from a sign-on bonus (used the other half to max out the IRA) and $3,000 expected from a family gift\n\nExpenses\n\t•\tRent + utilities (incl. electric/internet): ~$2k/month\n\t•\tTotal average monthly spending (Including loan payments): ~$3,100–$3,300\n\t•\tHealth insurance and other benefits deducted pre-tax\n\nPartner\n\t•\tLives with me, pays me back for  ~$500/month as I use my account to pay all the bills and rent\n\t•\tRecently started a $44k salary job\n\t•\tHas a strong emergency fund (~10 months) but is in the preparation stages to start contributing to a Roth IRA/401K\n\nGoals (in order of personal priority):\n\t1.\tRebuild emergency fund to $10k (aiming for July/August)\n\t2.\tSave ~$2.5k for an engagement ring (late summer/fall proposal)\n\t3.\tSave $5k+ for a car down payment by August (likely need to replace current car)\n\t4.\tAggressively pay down student loans (goal: payoff in 3–5 years)\n\t5.\tIncrease 403(b) contributions when able (as goals are met vs as I tighten up the budget, hope to at least increase to 10%)\n\nFuture Planning\n\t•\tLikely life events: marriage → house → kids (3+ years out)\n\t•\tPlan to stay in current area until 403(b) match vests and I try for an internal promotion/job transition (~$10–15k salary bump, likely into home health)\n\t•\tGirlfriend and I are aligned on priorities and flexible if budget cuts are needed, but I think we are in okay shape. Just want to be mindful of our lifestyle creep as she just went from part-time work to this full-time job. \n\nWould love your thoughts on:\n\t•\tConsolidating my Roth IRA into fewer funds\n\t•\tWhether I’m allocating wisely between debt payoff vs. investments\n\t•\tAnything I might be overlooking or underestimating\n\nThanks so much in advance!\n", "author": "Historical_Sugar_270", "created_time": "2025-05-24T00:44:45", "url": "https://reddit.com/r/personalfinance/comments/1ktz6po/25_looking_for_feedback_on_my_financial_plan/", "upvotes": 1, "comments_count": 1, "sentiment": "bearish", "engagement_score": 3.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ku63jx", "title": "Is <PERSON><PERSON> the best choice moving forward?", "content": "Looking to do 2 batteries + 5 ish kW to my existing 5 ish kw system. \n\nWith all the craziness of the solar tax credit going away etc, my confidence in local solar companies is at an all time low. \n\nMy og system was also sunpower, so that’s been fun. Even enphase, if you look at their stock is just a straight decline over the last 5 years straight. Down almost 40%\n\nTesla is a massive company of course. And up 500% over the last 5 years no matter how you look at it. So at the very least, I’ll feel like they’ll still be here. \n\nAt the same time all I hear is horror stories about tesla. Never responding, failed installs, problem after problem. But do i have to just roll with that because, “at least they’ll still be here next year” \n\nI have no clue what to think lol. \n\nAre they just the clear cut choice to go with right now amidst all the chaos in the industry? ", "author": "rkelez", "created_time": "2025-05-24T07:40:30", "url": "https://reddit.com/r/solar/comments/1ku63jx/is_tesla_the_best_choice_moving_forward/", "upvotes": 0, "comments_count": 13, "sentiment": "bearish", "engagement_score": 26.0, "source_subreddit": "solar", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ku6akv", "title": "BYD overtakes Tesla in Europe for the first time as battery EV sales jump 169% in April", "content": "", "author": "kuyaparapo", "created_time": "2025-05-24T07:54:29", "url": "https://reddit.com/r/worldnews/comments/1ku6akv/byd_overtakes_tesla_in_europe_for_the_first_time/", "upvotes": 1557, "comments_count": 146, "sentiment": "neutral", "engagement_score": 1849.0, "source_subreddit": "worldnews", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ku8slj", "title": "Tesla's robotaxis will be limited to certain parts of Austin and avoid intersections the company deems unsafe", "content": "", "author": "ItzWarty", "created_time": "2025-05-24T10:49:24", "url": "https://reddit.com/r/teslainvestorsclub/comments/1ku8slj/teslas_robotaxis_will_be_limited_to_certain_parts/", "upvotes": 138, "comments_count": 124, "sentiment": "neutral", "engagement_score": 386.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1ku99px", "title": "About leaking: best AA cells I ever saw", "content": "I know this is a low power device, it still impresses me. This is a Casio FX-82 calculator from 1989, still running on its original cells (expiration date 1997) with voltage close to nominal. I don't even know if it Zinc or alcaline. We had the technology, we had everything.", "author": "<PERSON><PERSON><PERSON>", "created_time": "2025-05-24T11:19:18", "url": "https://reddit.com/r/batteries/comments/1ku99px/about_leaking_best_aa_cells_i_ever_saw/", "upvotes": 309, "comments_count": 37, "sentiment": "neutral", "engagement_score": 383.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kucnah", "title": "My Google Ads Search Campaign Tanked After Years of Success – Any Insight?", "content": "Hey everyone,\n\nI run a service-based business in NYC focused on indoor air quality testing (mold, VOCs, etc.), and my entire business has been built on Google Ads search campaigns. I don’t have a storefront – just a couple of employees, a solid service, and a phone number.\n\nHere’s the rundown:\n\n**The Backstory**\n\nWhen I started a few years ago, I knew very little – learned from YouTube, tried things out. Somehow I created a search campaign that worked.\n\n* 6–7 clicks a day\n* $6–$8 CPC\n* 1–3 phone calls a day\n* Booked 2–4 jobs a week\n\nThis kept my business running smoothly for two years. I hired people. Life was good.\n\n**The Problem**\n\nIn 2025, everything fell apart. Without any major changes, impressions vanished, CPC shot up to **$42 per click**, and conversions died.\n\nI paused that campaign, created a brand new one from scratch – same targeting, new ads – and the exact same thing happened:\n\n* Barely any impressions\n* CPC still sky-high\n* Leads dried up\n\nI rely almost entirely on inbound search traffic. Referrals help, but they’re not reliable – this is a one-and-done type service. You don’t need a mold test every week. My market is NYC, so demand should always exist.\n\n# What I’ve Tried\n\n* Rebuilt campaign from scratch\n* Tested new keywords and ad copy\n* Adjusted bidding strategies (Maximize Conversions)\n* Monitored quality scores and ad relevance (everything looked fine)\n\n# What I Don’t Understand\n\n* Why would a historically consistent campaign suddenly stop delivering?\n* Why would a new campaign, in a massive market like NYC, get almost no traffic?\n* Has something changed in Google’s system recently that favors big-budget or lead form campaigns?\n\nI’m honestly at a loss. This is how I feed my employees and pay rent. If anyone’s experienced this drop-off recently or has thoughts, I’d really appreciate some guidance or just to know I’m not going crazy.\n\nThanks in advance.\n\n\n\n**Update:** \n\n\n\nFirst of all—massive thanks to everyone who commented on my original post. The advice, sympathy, and even just the “yeah dude, Google Ads is a black box now” validation helped more than you know.\n\nSo after a week of *nothing*—no calls, no leads, just CPCs spiking to $54 and me paying my crew out of pure delusion—I was cooked. Burnt. Done. Sitting at my desk like a monkey staring at a glowing rectangle wondering why my life is now entirely dependent on an algorithm I don’t understand.\n\nThen I remembered I have ChatGPT Pro. And this thing called *Operator*. I was like, “You know what? I’m already getting zero calls so before i pay an agency let’s see what happens if I just let the AI do it.  This campaign is already wrecked anyway.\n\nSo I copy-pasted this prompt I built using GPT-4.5 and Reddit threads and deep reaserchj based on this, logged in through Operator, guide it to log in gave it my Google Ads credentials (yes, I know, probably insane), and told it:\n\n“Do whatever you want. Break shit. Edit anything. I literally do not care anymore.”\n\nAnd this thing *went to town*.\n\nFor 27 straight minutes it was like watching a hacker movie in real-time. It removed 47 negative keywords, added new keywords, changed some to phrase some to broad match, adjusted targeting, restructured some ad groups, and scrolled through settings I forgot even existed. Every 30 seconds it would ask something like “Do you want me to change this?” and I finally just said:\n\n“STOP ASKING. YOU ARE GOD NOW.”\n\nThen it stopped. Said “all done.”\n\nI figured it was about to get my account banned or implode my credit card.\n\n**Next day, I get 4 phone calls.**\n\nThree scheduled jobs. One from a luxury retail store in SoHo. Another from a hotel needing 12 rooms tested. A few solid residentials. CPC dropped from $42 to **$7.96**. And it’s stayed there all week.\n\n\n\nThe week before? **$0**.\n\nThis week? **Booked $17K.**\n\nWhat even is reality anymore?\n\nAnyway, I’m working on diversifying channels now because I’m not trying to let one algorithm decide whether I eat next month. But for now—holy shit. We’re back. ", "author": "Reasonable_Mark_747", "created_time": "2025-05-24T14:16:14", "url": "https://reddit.com/r/PPC/comments/1kucnah/my_google_ads_search_campaign_tanked_after_years/", "upvotes": 135, "comments_count": 33, "sentiment": "bullish", "engagement_score": 201.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kucv55", "title": "[D] Is Google Colab Pro worth for my project?", "content": "Hey guys, I'm currently dealing with my bachelor degree's final project. My title is “Grayscale Image Colorization Using Deep Learning”. I have datasets of 10000 images i guess. And it took quite a long time to train it.\n\nSo my question is, does purchasing colab pro makes the training faster or not? And does it worth the money if i just want to focus on developing my project using colab pro? \n\nThanks for you guys input, I’ll be waiting for it.", "author": "Few-Criticism9249", "created_time": "2025-05-24T14:26:22", "url": "https://reddit.com/r/MachineLearning/comments/1kucv55/d_is_google_colab_pro_worth_for_my_project/", "upvotes": 5, "comments_count": 36, "sentiment": "bullish", "engagement_score": 77.0, "source_subreddit": "MachineLearning", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kuczh9", "title": "Amazon sold me a 5090, turned out it was a 4090 😂", "content": "Dang, this is the nastiest scam ever. I bought a 5090 Aorus Master ICE on amazon, a return deal. Upon opening the 5090 Aorus box, it turned out it was just a 4090 Aero with a 5090 serial sticker slapped on the card. (there is no 5090 Aero afaik) \n\nSomeone knew damn well what they were doing, the 5090 serial sticker had peel marks, it was lifted off the Aorus 5090. (pic 2, check the peel marks) \n\nSo, beware when buying Amazon open box deals. Ive never had anything like this happen. This absolutely sucks. Ofcourse, sent it back and will get a refund, but this is just so nasty. 🤮\n\nI hope the person that did this gets a BSOD every minute of his or her life 😂", "author": "jaspers<PERSON><PERSON>", "created_time": "2025-05-24T14:31:43", "url": "https://reddit.com/r/nvidia/comments/1kuczh9/amazon_sold_me_a_5090_turned_out_it_was_a_4090/", "upvotes": 8124, "comments_count": 813, "sentiment": "bullish", "engagement_score": 9750.0, "source_subreddit": "nvidia", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kugab8", "title": "Elon is back to spending 24/7 at Tesla. 👍", "content": "", "author": "Traditional_War_8229", "created_time": "2025-05-24T16:56:59", "url": "https://reddit.com/r/teslainvestorsclub/comments/1kugab8/elon_is_back_to_spending_247_at_tesla/", "upvotes": 206, "comments_count": 303, "sentiment": "neutral", "engagement_score": 812.0, "source_subreddit": "teslainvestorsclub", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kugymb", "title": "Realistically what is going to happen with TSLA robotaxi launch", "content": "Basically the headline.\n\nHonestly i haven’t seen a lot of quality discussion about the planned robotaxi launch in June. Otherthan bulls and bears yelling on each other and random meme shit. \nLet’s collect all the facts and try to find what’s gonna happen in few weeks. \n\nHere are some facts we know so far and i will try to be impartial as i can be and just state facts and not my opinion. \n- Elon announced robotaxi planned launch in Austin in June. \n- TSLA stock has rallied 40+ % since the announcement. \n- TSLA sales are being disastrous worldwide and on going sales news are negative. Hence making the stock rally purely based on hopium on robotaxi launch. \n- TSLA FSD is currently at level-2 autonomy. \n- Full autonomy with driver less operation is level-5. \n- Tsla uses a complete vision based system. vs competitors multiple sensors including LIDAR and Radars. \n- We haven’t seen any test vehicles or any tests happening around robotaxi despite launch date being just few weeks away. (Waymo did years of testing before making their service publicly available)\n- On the otherside, tsla fans seems to be seeing all the current fsd being driven by consumers are actual test and that is good enough for fsd launch. \n\nThat’s all for now. Let’s discuss and as we go we can add more points and if possible we can keep this as a long thread to keep track the planned event. \n", "author": "ldmonko", "created_time": "2025-05-24T17:25:41", "url": "https://reddit.com/r/wallstreetbets/comments/1kugymb/realistically_what_is_going_to_happen_with_tsla/", "upvotes": 432, "comments_count": 810, "sentiment": "bullish", "engagement_score": 2052.0, "source_subreddit": "wallstreetbets", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kuhcwl", "title": "What cloud GPU providers do you guys actually use (and trust)?", "content": "Hey everyone! I'm looking for some real talk here - what cloud GPU platforms are you actually using for training/inference these days? I've tested a bunch of them with pretty mixed results, so I'm curious what's been working for others.\n\nI'm not obsessing over finding the absolute cheapest option, but more like decent performance for reasonable money, and hopefully something that doesn't make me want to pull my hair out during setup. Would be awesome if it has Jupy<PERSON> support or lets me jump into a ready-made environment without much hassle.", "author": "Dull_Wishbone2294", "created_time": "2025-05-24T17:43:07", "url": "https://reddit.com/r/cloudcomputing/comments/1kuhcwl/what_cloud_gpu_providers_do_you_guys_actually_use/", "upvotes": 7, "comments_count": 13, "sentiment": "neutral", "engagement_score": 33.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kuikv1", "title": "<PERSON><PERSON> responds with \"Yeah (sigh)\" to the following: \"I think the appetite to cut spending in Congress was so much lower than even he ever anticipated. DOGE has done incredible work, but the GOP has failed to actually implement any of the cuts. I, for one, am incredibly disappointed.\"", "content": "", "author": "twinbee", "created_time": "2025-05-24T18:36:51", "url": "https://reddit.com/r/elonmusk/comments/1kuikv1/elon_responds_with_yeah_sigh_to_the_following_i/", "upvotes": 16, "comments_count": 75, "sentiment": "neutral", "engagement_score": 166.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kuioms", "title": "Tesla Launches Alcantara Dash Trim Upgrade for New Model 3 and Model Y", "content": "", "author": "chrisdh79", "created_time": "2025-05-24T18:41:35", "url": "https://reddit.com/r/teslamotors/comments/1kuioms/tesla_launches_alcantara_dash_trim_upgrade_for/", "upvotes": 253, "comments_count": 122, "sentiment": "neutral", "engagement_score": 497.0, "source_subreddit": "teslamotors", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kukvba", "title": "Upskilling as a Data Analyst?", "content": "I am a Senior Data Analyst, and have been an analyst for around 5 years now. When I started out, I was always taking different courses in SQL, Python, etc. However for the past 2 years I’ve not been as motivated to up-skill further.\n\nI mainly use SQL and Tableau in my current role, and our team doesn’t use Python (we are the “Reporting” team) - the data engineering team handle any DBT requests, etc. My degree is in business, though I am quite competent in SQL and Tableau now, and can design complex Tableau reports and SQL scripts for those reports. Despite not up-skilling in my own time anymore, I’m hard-working on my projects and have built some of the company’s most used reports.\n\nDoes anyone have any recommendations to continue advancing? I feel the next step is to dive into Data Engineering, though I’m quite happy building reports and not sure if I’d enjoy <PERSON> as much. I’d like to stay working on projects at least for a few more years, rather than moving into leadership roles, as I enjoy the coding and report-building more than just being stuck in meetings all-day.\n\nThanks", "author": "12fitness", "created_time": "2025-05-24T20:21:12", "url": "https://reddit.com/r/analytics/comments/1kukvba/upskilling_as_a_data_analyst/", "upvotes": 141, "comments_count": 36, "sentiment": "neutral", "engagement_score": 213.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kum63j", "title": "How bad can it get when crypto goes POOF?", "content": "Right now there is about 3 trillion market cap in crypto, which is making a lot of people feel significantly richer than they are. supposing crypto market cap goes to near zero, I do not think there has ever been a wealth destruction event like that ever in the history of markets. I think it could have significant impacts on the global economy, causing a very real financial crisis.\n\nAI is probably in a bubble as well, there are very obvious stocks at the very least which are beyond reason, such as tesla and Palantir.\n\nThese seem to be a staple in the modern portfolio, and could contribute to an even greater loss of the wealth effect.\n\nthe wealth effect would further deteriorate as multiple compression occurs across the board in a bear market.\n\nA lot of people will probably pile on here saying that crypto wont go to zero, but factually there is only one use for blockchain technology right now and that would be a CBDC. CBDC would not have anything to do with the current coins which are being treated as ponzi schemes, and would not be related to the likes of tether or USDC. As matter of fact tether and USDC would likely stop existing as liquidity for them would dry up in an event where there is no coins to gamble on outside of them.\n\nIs the next great financial crisis going to play out something like this? and when will it occur? Historically in great bubbles they were nearing an end when political figures were stoking the fire for personal gain. It is not hard to see this playing out right now. A lot of people find comfort that there is greater acceptance of crypto, but in fact it is a sign that the party is nearing its end. History rhymes.\n\nEDIT: I have responded a lot in here, I think that I have clarified a few things with people and would like to change my wording above. Bitcoin is not a ponzi scheme, it is closer to a pyramid scheme or pump and dump. Not much more to add by responding to new comments, they all seem to be along the same lines. Either you understand or you dont, and I dont have more time to explain ;)", "author": "MeasurementSecure566", "created_time": "2025-05-24T21:20:59", "url": "https://reddit.com/r/investing/comments/1kum63j/how_bad_can_it_get_when_crypto_goes_poof/", "upvotes": 0, "comments_count": 284, "sentiment": "bearish", "engagement_score": 568.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}]