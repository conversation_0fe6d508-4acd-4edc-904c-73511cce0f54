#!/usr/bin/env python3
"""
Reddit关键词匹配调试脚本
分析为什么处理了帖子但没有找到相关内容
"""

import os
import re
from datetime import datetime, timezone
from dotenv import load_dotenv
import praw
import prawcore

# 清除系统环境变量
reddit_vars = ['REDDIT_CLIENT_ID', 'REDDIT_CLIENT_SECRET', 'REDDIT_USER_AGENT', 'REDDIT_USERNAME', 'REDDIT_PASSWORD']
for var in reddit_vars:
    if var in os.environ:
        del os.environ[var]

# 强制从.env文件加载
load_dotenv(override=True)

class RedditMatchingDebugger:
    """Reddit关键词匹配调试器"""
    
    def __init__(self):
        self.ticker_keywords = {
            'AAPL': ['Apple', 'AAPL', 'Apple Inc', 'iPhone', 'iPad', 'Mac', 'iOS', 'Tim Cook'],
            'MSFT': ['Microsoft', 'MSFT', 'Windows', 'Azure', 'Office', 'Xbox', '<PERSON><PERSON><PERSON>'],
            'NVDA': ['NVIDIA', 'NVDA', 'GeForce', 'RTX', 'GPU', 'AI chip', 'Jensen Huang'],
            'GOOGL': ['Google', 'GOOGL', 'Alphabet', 'YouTube', 'Android', 'Chrome', 'Sundar Pichai'],
            'AMZN': ['Amazon', 'AMZN', 'AWS', 'Prime', 'Alexa', 'Jeff Bezos', 'Andy Jassy'],
            'TSLA': ['Tesla', 'TSLA', 'Elon Musk', 'Model S', 'Model 3', 'Model Y', 'Cybertruck'],
            'META': ['Meta', 'META', 'Facebook', 'Instagram', 'WhatsApp', 'Mark Zuckerberg'],
            'NFLX': ['Netflix', 'NFLX', 'streaming', 'Reed Hastings'],
            'AMD': ['AMD', 'Ryzen', 'Radeon', 'Lisa Su'],
            'INTC': ['Intel', 'INTC', 'Core', 'Xeon', 'Pat Gelsinger']
        }
        
        # 创建Reddit客户端
        self.reddit = praw.Reddit(
            client_id=os.getenv('REDDIT_CLIENT_ID'),
            client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
            user_agent=os.getenv('REDDIT_USER_AGENT'),
            username=os.getenv('REDDIT_USERNAME'),
            password=os.getenv('REDDIT_PASSWORD')
        )
    
    def extract_tickers_from_text(self, text: str, debug=False) -> list:
        """从文本中提取股票代码（带调试信息）"""
        if not text:
            return []
        
        text_lower = text.lower()
        found_tickers = []
        
        if debug:
            print(f"  📝 分析文本: '{text[:100]}{'...' if len(text) > 100 else ''}'")
            print(f"  🔍 文本长度: {len(text)} 字符")
        
        # 关键词匹配
        for ticker, keywords in self.ticker_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    found_tickers.append(ticker)
                    if debug:
                        print(f"  ✅ 找到匹配: {ticker} <- '{keyword}'")
                    break
            else:
                if debug and ticker in ['GOOGL', 'TSLA']:  # 只显示目标股票的详细信息
                    print(f"  ❌ {ticker} 无匹配:")
                    for kw in keywords:
                        if kw.lower() in text_lower:
                            print(f"     - '{kw}' ✅")
                        else:
                            print(f"     - '{kw}' ❌")
        
        # 直接匹配股票代码格式 $TICKER
        ticker_pattern = r'\$([A-Z]{1,5})\b'
        matches = re.findall(ticker_pattern, text.upper())
        for match in matches:
            if match in self.ticker_keywords:
                found_tickers.append(match)
                if debug:
                    print(f"  ✅ 找到股票代码: ${match}")
        
        result = list(set(found_tickers))
        if debug:
            print(f"  🎯 最终结果: {result}")
        
        return result
    
    def debug_subreddit_posts(self, subreddit_name: str, limit: int = 10, target_tickers: list = None):
        """调试子版块帖子的匹配情况"""
        print(f"\n{'='*60}")
        print(f"调试 r/{subreddit_name} 的帖子匹配")
        print(f"{'='*60}")
        
        if target_tickers:
            print(f"🎯 目标股票: {target_tickers}")
            # 过滤关键词
            filtered_keywords = {k: v for k, v in self.ticker_keywords.items() 
                               if k in target_tickers}
            print(f"🔑 使用的关键词:")
            for ticker, keywords in filtered_keywords.items():
                print(f"   {ticker}: {keywords}")
            self.ticker_keywords = filtered_keywords
        
        print(f"📊 分析前 {limit} 个帖子...\n")
        
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            posts = list(subreddit.new(limit=limit))
            
            total_posts = 0
            relevant_posts = 0
            
            for i, submission in enumerate(posts, 1):
                total_posts += 1
                
                title = submission.title or ""
                content = submission.selftext or ""
                full_text = f"{title} {content}"
                
                print(f"\n📄 帖子 #{i}")
                print(f"   标题: {title[:80]}{'...' if len(title) > 80 else ''}")
                print(f"   内容: {content[:80] if content else '(无内容)'}{'...' if len(content) > 80 else ''}")
                print(f"   作者: {submission.author}")
                print(f"   评分: {submission.score}, 评论: {submission.num_comments}")
                
                # 分析匹配
                tickers = self.extract_tickers_from_text(full_text, debug=True)
                
                if tickers:
                    relevant_posts += 1
                    print(f"   🎉 相关帖子! 匹配股票: {tickers}")
                else:
                    print(f"   ⚪ 无相关股票")
                
                print(f"   🔗 链接: https://reddit.com{submission.permalink}")
            
            print(f"\n{'='*60}")
            print(f"📊 统计结果")
            print(f"{'='*60}")
            print(f"总处理帖子: {total_posts}")
            print(f"相关帖子数: {relevant_posts}")
            print(f"匹配率: {relevant_posts/total_posts*100:.1f}%")
            
            if relevant_posts == 0:
                print(f"\n🔍 建议:")
                print(f"1. 检查关键词是否过于严格")
                print(f"2. 考虑添加更多相关术语")
                print(f"3. 检查帖子内容是否真的包含目标股票信息")
            
        except Exception as e:
            print(f"❌ 调试失败: {e}")
    
    def test_keyword_matching(self):
        """测试关键词匹配功能"""
        print(f"\n{'='*60}")
        print(f"测试关键词匹配功能")
        print(f"{'='*60}")
        
        test_texts = [
            "Google stock is going up today",
            "GOOGL earnings beat expectations",
            "Alphabet announces new AI features",
            "Tesla Model 3 sales are strong",
            "TSLA to the moon! 🚀",
            "Elon Musk tweets about Cybertruck",
            "I bought some $GOOGL calls",
            "What do you think about $TSLA?",
            "Apple vs Google competition",
            "Microsoft Azure vs Google Cloud",
            "Random post about weather"
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n🧪 测试 #{i}: '{text}'")
            tickers = self.extract_tickers_from_text(text, debug=True)
            print(f"   结果: {tickers if tickers else '无匹配'}")

def main():
    """主函数"""
    print("Reddit关键词匹配调试工具")
    
    try:
        debugger = RedditMatchingDebugger()
        
        # 1. 测试关键词匹配功能
        debugger.test_keyword_matching()
        
        # 2. 调试r/stocks子版块
        debugger.debug_subreddit_posts('stocks', limit=10, target_tickers=['GOOGL', 'TSLA'])
        
        # 3. 调试r/test子版块（对比）
        debugger.debug_subreddit_posts('test', limit=5, target_tickers=['GOOGL', 'TSLA'])
        
    except Exception as e:
        print(f"❌ 调试工具运行失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
