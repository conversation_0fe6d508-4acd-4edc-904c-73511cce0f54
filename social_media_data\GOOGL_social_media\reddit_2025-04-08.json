[{"platform": "reddit", "post_id": "reddit_1ju1wz1", "title": "Anyone else planning on adding a “Tariff charge” line on their invoices and receipts?", "content": "I’m going to add “Trump Tariff Surcharge (37%)” on mine. I fear this will turn people away but I also need to be honest and transparent. How are you all going to handle this?", "author": "barsonbity", "created_time": "2025-04-08T01:28:04", "url": "https://reddit.com/r/smallbusiness/comments/1ju1wz1/anyone_else_planning_on_adding_a_tariff_charge/", "upvotes": 1285, "comments_count": 105, "sentiment": "neutral", "engagement_score": 1495.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ju2fbv", "title": "What are these called? Google lens doesn't know, either", "content": "What is this part called, and where could they be ordered from? Google lens is giving me parts with a completely different shape. The first 2 pics are of the parts and the 3rd pic is of the Google lens results. ", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_time": "2025-04-08T01:54:23", "url": "https://reddit.com/r/batteries/comments/1ju2fbv/what_are_these_called_google_lens_doesnt_know/", "upvotes": 34, "comments_count": 49, "sentiment": "neutral", "engagement_score": 132.0, "source_subreddit": "batteries", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ju4lfu", "title": "Google is allegedly paying some AI staff to do nothing for a year rather than join rivals", "content": "", "author": "lurker_bee", "created_time": "2025-04-08T03:51:09", "url": "https://reddit.com/r/technology/comments/1ju4lfu/google_is_allegedly_paying_some_ai_staff_to_do/", "upvotes": 5566, "comments_count": 201, "sentiment": "neutral", "engagement_score": 5968.0, "source_subreddit": "technology", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ju9oxu", "title": "Looking for high ROI lead gen channels beyond paid ads and lists", "content": "I've hit a wall with traditional paidd acquisition. My CAC is way too high and it's not worth the few leads we're getting. We tried Google and Meta, but optimized funnels and retargeting aren't hitting.\n\nBuying leads has been even worse. Emails keep bouncing, info is outdated and irrelevant contacts. Just wasting my time here and starting to get nervous.\n\nWhat I'm trying to do now is build a clean and reliable pipeline. Best would be something based on first-party/real-time data. I can do outreach but it has to be personalized and scalable.\n\nPlease let me know if you had a similar experience and what worked for you.\n\nEdit: I work at a boutique marketing agency. We provide B2B marketing services for our clients like blog creation, off-page SEO, and email marketing campaigns. ", "author": "Forsaken-Spell8853", "created_time": "2025-04-08T09:47:23", "url": "https://reddit.com/r/DigitalMarketing/comments/1ju9oxu/looking_for_high_roi_lead_gen_channels_beyond/", "upvotes": 108, "comments_count": 30, "sentiment": "bullish", "engagement_score": 168.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jua32k", "title": "How are you scaling Google Ads campaigns in 2025 while keeping CPA stable ?", "content": "I’ve noticed that post-initial optimization, scaling tends to inflate CPA — especially with Performance Max or broad match + CPA strategies.\n\nCurious to know how others are handling scale without letting CPA increase.", "author": "scalemarketer", "created_time": "2025-04-08T10:14:21", "url": "https://reddit.com/r/PPC/comments/1jua32k/how_are_you_scaling_google_ads_campaigns_in_2025/", "upvotes": 107, "comments_count": 18, "sentiment": "neutral", "engagement_score": 143.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jud5jd", "title": "Rich is getting richer, poor is getting poorer - small businesses cry out loud. Story of the latest Google update March 2025", "content": "A few observations about the latest Google Core update\n\n* Brands with high search volume get multiple listings.\n* Small businesses are again being targeted. -\n* AI overview eats up the business of affiliate websites -\n* Showing generic results of brands with better brand presence even for the long-tailed queries. \n* Independent websites suffer, Google admits -\n*  AI Overview sources are not that relevant. ", "author": "Mission_Purpose_6815", "created_time": "2025-04-08T13:08:56", "url": "https://reddit.com/r/Entrepreneur/comments/1jud5jd/rich_is_getting_richer_poor_is_getting_poorer/", "upvotes": 4, "comments_count": 2, "sentiment": "bullish", "engagement_score": 8.0, "source_subreddit": "Entrepreneur", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1jufn0k", "title": "Google Quietly Removing Features (Again)", "content": "New Maps update seems to have dropped recently with some nice new larger font in route preview. *Great*, I thought to myself initially. \nWell today I realized the **post-trip summary has been 'updated'** by which understand: it now fits the newer design aesthetic while **removing 90% of the useful features it had**. No time taken, no average speed, no total distance.\nIt was one of the things that stood out as an advantage of GMaps on Android over GMaps on iOS so you'd think they would eventually add it to iOS too, right? But Google being Google just removed it altogether.\nAre they ever going to stop or just keep going until every little useful or cute easter egg is removed?", "author": "Tzankotz", "created_time": "2025-04-08T14:59:24", "url": "https://reddit.com/r/GoogleMaps/comments/1jufn0k/google_quietly_removing_features_again/", "upvotes": 55, "comments_count": 21, "sentiment": "bearish", "engagement_score": 97.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1juj5dx", "title": "Survey on Public Perception", "content": "https://docs.google.com/forms/d/e/1FAIpQLSftBLlxX_a5wNL3qtIlBqZvJfzZYPdQSM6UZ3pnscdHgCgyyA/viewform?usp=sharing\nIf I could get some responses to this survey I have to do for a research project that would be great. Is is on safety and trusting autonomous vehicles to achieve certain goals", "author": "Educational_Arm9386", "created_time": "2025-04-08T17:23:41", "url": "https://reddit.com/r/AutonomousVehicles/comments/1juj5dx/survey_on_public_perception/", "upvotes": 1, "comments_count": 0, "sentiment": "neutral", "engagement_score": 1.0, "source_subreddit": "AutonomousVehicles", "hashtags": null, "ticker": "GOOGL"}]