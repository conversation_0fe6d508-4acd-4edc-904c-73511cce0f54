[{"platform": "reddit", "post_id": "reddit_1kpc18w", "title": "Google ads for the first time", "content": "I've been advertising on Facebook and now I want to try it on Google Apps. Is there any advice or strategy that has worked for you and that is like the master key to having a good ad on Google Ads?", "author": "Illustrious-Egg6644", "created_time": "2025-05-18T04:52:17", "url": "https://reddit.com/r/PPC/comments/1kpc18w/google_ads_for_the_first_time/", "upvotes": 6, "comments_count": 30, "sentiment": "neutral", "engagement_score": 66.0, "source_subreddit": "PPC", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kpkvxw", "title": "Google ads to grow your business!", "content": "Hi everyone, I've recently completed a Google Ads course and am eager to gain real-world experience. I'm offering my services free of charge to a few businesses, providing expert help with setting up new campaigns, auditing and optimizing existing ones, keyword research, ad copy creation, and performance analysis. In return for positive results or actionable insights, I would be grateful for a video testimonial about your experience working with me. This is a great opportunity for you to receive dedicated Google Ads support at no cost, while I build my portfolio. If interested, please send me a direct message with details about your business and Google Ads needs. I look forward to potentially collaborating!\n", "author": "No-File-718", "created_time": "2025-05-18T14:11:39", "url": "https://reddit.com/r/adwords/comments/1kpkvxw/google_ads_to_grow_your_business/", "upvotes": 3, "comments_count": 3, "sentiment": "bullish", "engagement_score": 9.0, "source_subreddit": "adwords", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kpn715", "title": "Our Google Sheets spend tracking system is driving me mad - help!", "content": "My partner and I have been using a Google Sheet to track our shared expenses for the past year. Every two weeks we manually update who paid what, calculate splits, and transfer money to settle up.\n\nIt takes a lot of time and I’m looking for an alternative. Anyone found a better system for tracking shared expenses while maintaining individual budgets? We tried Splitwise but we usually add a couple transactions at once so we need to work around the paywall, which is inconvenient. However, I don't see myself getting the paid version.", "author": "plateg9", "created_time": "2025-05-18T15:54:42", "url": "https://reddit.com/r/personalfinance/comments/1kpn715/our_google_sheets_spend_tracking_system_is/", "upvotes": 0, "comments_count": 8, "sentiment": "neutral", "engagement_score": 16.0, "source_subreddit": "personalfinance", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kpnsk3", "title": "Can someone guide me about Cloud/VPS pricing for beginners?", "content": "So basically, I'm building an app and I need to find the cheapest best possible cloud or VPS Hosting for UK region. It'll be a medium complexity app with a low number of users (<1000) but it has some heavy background tasks and infrastructure tools.\nSo basically something like:\nFrontend + API Backend + Long running scripts + PostgreSQL + Redis + some vectorDB.\nI'm planning to outsource some of this to managed services but still need to make sure I choose the cheapest option possible but highly scalable and available in the future.\nBut I have no idea how to choose Providers since I don't really understand the not-so-simple pricing they have.\nI have some experience working on AWS, but I'm also looking into GCP, Hetzner+Coolify mix, Digital Ocean, and Oracle cloud free-tier(really bad reviews for this one so probably not).\nPlease guide how to make the right choice.", "author": "jobsearcher_throwacc", "created_time": "2025-05-18T16:20:27", "url": "https://reddit.com/r/cloudcomputing/comments/1kpnsk3/can_someone_guide_me_about_cloudvps_pricing_for/", "upvotes": 7, "comments_count": 14, "sentiment": "bullish", "engagement_score": 35.0, "source_subreddit": "cloudcomputing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kppvku", "title": "Google CEO - <PERSON><PERSON>: \"I spent time with Elon maybe two weeks ago. <...> His ability to will future technologies into existence... just I mean unparalleled. <...> These are phenomenal people\"", "content": "", "author": "twinbee", "created_time": "2025-05-18T17:49:06", "url": "https://reddit.com/r/elonmusk/comments/1kppvku/google_ceo_sundar_pich<PERSON>_i_spent_time_with_elon/", "upvotes": 0, "comments_count": 58, "sentiment": "neutral", "engagement_score": 116.0, "source_subreddit": "elonmusk", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kpret8", "title": "LOOKING FOR <PERSON><PERSON><PERSON>LE SEO EXPERT", "content": "I am looking for a google seo expert who can rank my website please comment below ", "author": "ExtensionKitchen3988", "created_time": "2025-05-18T18:54:02", "url": "https://reddit.com/r/DigitalMarketing/comments/1kpret8/looking_for_google_seo_expert/", "upvotes": 0, "comments_count": 22, "sentiment": "neutral", "engagement_score": 44.0, "source_subreddit": "DigitalMarketing", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kps3lk", "title": "🚀 Wall Street Radar: Stocks to Watch Next Week - 18 May", "content": "**Updated Portfolio:**\n\nCOIN: Coinbase Global Inc  \nTSLA: Tesla Inc  \nSEZL: Sezzle Inc\n\nComplete article and charts [HERE](https://www.gb.capital/p/wall-street-radar-stocks-to-watch-253)\n\n**In-depth analysis of the following stocks:**\n\n* CMP: Compass Minerals International  \n* RUN: Sunrun Inc\n* TTD: The Trade Desk Inc\n* BULL: Webull Corporation\n* OS: OneStream Inc \n* ECVT: Ecovyst Inc", "author": "Market_Moves_by_GBC", "created_time": "2025-05-18T19:23:34", "url": "https://reddit.com/r/economy/comments/1kps3lk/wall_street_radar_stocks_to_watch_next_week_18_may/", "upvotes": 2, "comments_count": 0, "sentiment": "bullish", "engagement_score": 2.0, "source_subreddit": "economy", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kpsnio", "title": "Website not showing up on Google", "content": "I need some advice as I dont know anything about tech and SEO etc. I have a website called https//www.balancednuttitionsolutions.ca that I started a month ago. It is not showing up on Google search. I have submitted the website to Google console and done everything I need to for SEO like add meta tags and description for all the pages and images. Google console says that my website is not showing up because it is a ‘page with redirect’. I used to have a similar website www.balancednutritionsolutions.com years ago. Could that be a problem for my new website? I have no idea what to do to get it to show up on google. ", "author": "Canadiansnow1982", "created_time": "2025-05-18T19:47:15", "url": "https://reddit.com/r/webdev/comments/1kpsnio/website_not_showing_up_on_google/", "upvotes": 1, "comments_count": 43, "sentiment": "neutral", "engagement_score": 87.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1kpwcvv", "title": "Is there a way to get the \"Search image with Google Lens\" functionality back?", "content": "I found the \"Search image with Google Lens\" function in the context menu to be a FANTASTIC function and I have been more and more disappointed with every replacement they make for it.\n\nIs there a way to get \"Search image with Google Lens\", the one with the Search/Text/Translate options on the side panel, available in my version of Chrome again? Like with the flags or something?\n\n\nChrome Version: 136.0.7103.114\n\nOS: Windows 10 Home 19045.5854", "author": "Confident-House-1566", "created_time": "2025-05-18T22:30:21", "url": "https://reddit.com/r/chrome/comments/1kpwcvv/is_there_a_way_to_get_the_search_image_with/", "upvotes": 5, "comments_count": 11, "sentiment": "neutral", "engagement_score": 27.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}]