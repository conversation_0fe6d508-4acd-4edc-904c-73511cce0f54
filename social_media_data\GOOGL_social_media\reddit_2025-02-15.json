[{"platform": "reddit", "post_id": "reddit_1ippbqg", "title": "Hey <PERSON>, seriously, can we please stop calling it the Gulf of America and just stick with the Gulf of Mexico?", "content": "As an American, It's so stupid. I refuse to call the Gulf of Mexico that just because we have a president who makes dumb decisions.\n\n\n\n\n\n  \nEdit: Sorry I didn't know there were so many posts about this topic. ", "author": "Madd200", "created_time": "2025-02-15T00:28:31", "url": "https://reddit.com/r/GoogleMaps/comments/1ippbqg/hey_google_seriously_can_we_please_stop_calling/", "upvotes": 229, "comments_count": 111, "sentiment": "neutral", "engagement_score": 451.0, "source_subreddit": "GoogleMaps", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iprkfl", "title": "I was an early reservation holder for the Cybertruck but I bought the Lighting and traded my Model3 for a ZDX for the wife.", "content": "Truly a love hate relationship.\n\nIn 2017 I was traveling 134 + up to 20 miles additionally round trip everyday for work.  I had scaled down from my 2013 Tundra to a used 04 Prius with  140k miles on it in the effort to save money.  \nI dove deep into hypermiling and without being crazy and doing over the top aerodynamic mods, I was averaging about $200 in gas every month and a $50 oil change monthly for every 5k miles.\nI started looking into PHV’s and the best I could find at the time was 40 miles of range before turning to ICE.  While driving around one day,  I passed the Tesla Dealership in Springfield NJ.  I had only just learned of the Tesla Model S and thought they were basically a novelty much like the fisher Ocean.  Didn’t know much but thought let me stop.  The Salesperson introduced me to the model 3 and I fell in LOVE.  The technology and sportiness and it was unusual.   To boot,  no oil changes,  no brakes in 115k miles and my electricity bill only went up $100.  Totally worth it! \nI saw so much potential in the technology and became a quick fan boy.  Started following <PERSON><PERSON> on Twitter and would watch Tesla time news.  I even convinced my wife to let me invest $35k in TSLA.  And that’s where the journey changed.\nNot knowing much about markets, and missing out on opportunities like google and Apple, I jumped on <PERSON><PERSON><PERSON> and hit buy.\nThis was fitting the time <PERSON><PERSON> was dropping hints about the coming truck and boy that’s what I really wanted. An Electric pick up. So I hit buy and like the true fan boy I was, I checked twitter for Elmo updates.  I shit you not,  2 minutes after hitting buy,  he tweets, “I think my stock is overpriced”.   And I watched the value of the stock plummet $200.  I was mortified at my decision.  I felt foolish, but I held. And it all worked out. I held through both Stock Splits and made quite a bit of money.\nUnfortunately, as I held on to that stock and watched it grow, it blinded me from what was really going on.  The tweets got uglier and uglier. But I still bought my second Model3. \n\nBut enough became enough.  When Elmo endorsed Trump, I sold all my shares as a start.  And today, we traded my girl in for an Acura ZDX for my wife.  \n\nWe feel lighter not being saddled with a symbol of hate.  The car was amazing for being the pioneer it was.  But the quality of other EV’s that are out here now is worlds better.  Still behind on the software, but I have realized we are still in the novelty stage with the software anyway.\n\n“I bought before I knew he was crazy”\n", "author": "4TheOutdoors", "created_time": "2025-02-15T02:29:24", "url": "https://reddit.com/r/electricvehicles/comments/1iprkfl/i_was_an_early_reservation_holder_for_the/", "upvotes": 590, "comments_count": 208, "sentiment": "bullish", "engagement_score": 1006.0, "source_subreddit": "electricvehicles", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1ipu756", "title": "What does Google use to make their UIs?", "content": "Was wondering what they use to make the UI in the screenshot.", "author": "These-Inevitable-146", "created_time": "2025-02-15T05:04:18", "url": "https://reddit.com/r/webdev/comments/1ipu756/what_does_google_use_to_make_their_uis/", "upvotes": 268, "comments_count": 77, "sentiment": "neutral", "engagement_score": 422.0, "source_subreddit": "webdev", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iq0xsn", "title": "I just want to sue Google LLC for ending Android 6/7 support for YouTube. Some people still use such devices, and it no longer supports the latest version.", "content": "", "author": "CaterpillarCurrent50", "created_time": "2025-02-15T13:06:13", "url": "https://reddit.com/r/youtube/comments/1iq0xsn/i_just_want_to_sue_google_llc_for_ending_android/", "upvotes": 8, "comments_count": 80, "sentiment": "bullish", "engagement_score": 168.0, "source_subreddit": "youtube", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iq24hm", "title": "Mexico threatens Google with lawsuit over Gulf of America renaming in its maps", "content": "", "author": "Some-Technology4413", "created_time": "2025-02-15T14:10:16", "url": "https://reddit.com/r/google/comments/1iq24hm/mexico_threatens_google_with_lawsuit_over_gulf_of/", "upvotes": 1156, "comments_count": 83, "sentiment": "neutral", "engagement_score": 1322.0, "source_subreddit": "google", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iq39vb", "title": "how to revert back to the old google lens ui?", "content": "i don't like the new one at all, this one was much better, does anybody know how to get it back?\n\n[old one](https://preview.redd.it/0qmsn0onibje1.png?width=1553&format=png&auto=webp&s=50c5a58ab49408b155832f4808a05f15a2d9a6b2)\n\n[new one](https://preview.redd.it/snhs3biuibje1.png?width=1879&format=png&auto=webp&s=d190bebea27fe1ec1f1b32589442db78ba755504)\n\n", "author": "Primary_Act_9259", "created_time": "2025-02-15T15:06:36", "url": "https://reddit.com/r/chrome/comments/1iq39vb/how_to_revert_back_to_the_old_google_lens_ui/", "upvotes": 9, "comments_count": 24, "sentiment": "neutral", "engagement_score": 57.0, "source_subreddit": "chrome", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iq97r0", "title": "Google Reviews Exchange", "content": "Hello Guys,\nLet's exchange 5star google reviews \nI'm planning to upgrade our  business through Google reviews.\nSo I'm planning to Exchange 5 star google reviews. \nI'm having more google accounts, so we can exchange Google reviews and Build our business in online together and get better clients opportunities. \nSo let's help together🤝\nPlease Feel Free to DM me and exchange reviews.\nMessage me for more info♥️\n\n", "author": "hihihihi95", "created_time": "2025-02-15T19:31:38", "url": "https://reddit.com/r/smallbusiness/comments/1iq97r0/google_reviews_exchange/", "upvotes": 0, "comments_count": 32, "sentiment": "neutral", "engagement_score": 64.0, "source_subreddit": "smallbusiness", "hashtags": null, "ticker": "GOOGL"}, {"platform": "reddit", "post_id": "reddit_1iqa9ap", "title": "Google analytics vs Vercel web analytics", "content": "Has anyone gotten conflicting analytics results between Google analytics and vercel’s analytics?\n\nThe data from my Google analytics shows the device breakdown of my users like this:\n\nDesktop: 59.3%\nMobile: 39.6%\nTablet: 1.1%\n\nWhereas from vercel I get:\n\nDesktop: 39%\nMobile: 59%\nTablet: 2%\n\nThey are recorded on the same time frame. The percentages seem to match but I’m not sure why the categories are switched. Which analytics breakdown is more accurate?", "author": "Some_Difficulty8105", "created_time": "2025-02-15T20:16:46", "url": "https://reddit.com/r/analytics/comments/1iqa9ap/google_analytics_vs_vercel_web_analytics/", "upvotes": 1, "comments_count": 1, "sentiment": "neutral", "engagement_score": 3.0, "source_subreddit": "analytics", "hashtags": null, "ticker": "GOOGL"}]