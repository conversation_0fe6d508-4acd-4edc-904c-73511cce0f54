#!/usr/bin/env python3
"""
Reddit API调试测试
逐步测试Reddit API的各个功能
"""

import os
import praw
import prawcore
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_step_by_step():
    """逐步测试Reddit API功能"""
    
    print("=== Reddit API 逐步调试测试 ===")
    
    # 配置
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
    print(f"从环境变量读取的User-Agent: {user_agent}")
    
    print(f"User-Agent: {user_agent}")
    
    try:
        # 步骤1: 创建Reddit实例
        print("\n步骤1: 创建Reddit实例...")
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        print("✓ Reddit实例创建成功")
        
        # 步骤2: 测试简单子版块访问
        print("\n步骤2: 测试简单子版块访问...")
        test_subreddit = reddit.subreddit('test')
        print(f"✓ 子版块对象创建成功: {test_subreddit}")
        
        # 步骤3: 获取子版块基本信息
        print("\n步骤3: 获取子版块基本信息...")
        display_name = test_subreddit.display_name
        print(f"✓ 子版块名称: {display_name}")
        
        # 步骤4: 测试获取帖子 (这里可能出现403)
        print("\n步骤4: 测试获取帖子...")
        try:
            submissions = list(test_subreddit.new(limit=1))
            if submissions:
                post = submissions[0]
                print(f"✓ 成功获取帖子: {post.title[:50]}...")
            else:
                print("⚠ 没有找到帖子")
        except Exception as e:
            print(f"✗ 获取帖子失败: {e}")
            print("这可能是403错误的来源")
        
        # 步骤5: 测试目标子版块
        print("\n步骤5: 测试目标子版块...")
        target_subreddits = ['stocks', 'investing', 'wallstreetbets']
        
        for sub_name in target_subreddits:
            print(f"\n  测试 r/{sub_name}:")
            try:
                subreddit = reddit.subreddit(sub_name)
                
                # 测试基本信息
                try:
                    display_name = subreddit.display_name
                    print(f"    ✓ 基本信息访问成功: {display_name}")
                except Exception as e:
                    print(f"    ✗ 基本信息访问失败: {e}")
                    continue
                
                # 测试获取帖子
                try:
                    submissions = list(subreddit.new(limit=1))
                    if submissions:
                        post = submissions[0]
                        print(f"    ✓ 帖子访问成功: {post.title[:30]}...")
                    else:
                        print(f"    ⚠ 没有找到帖子")
                except Exception as e:
                    print(f"    ✗ 帖子访问失败: {e}")
                    
            except Exception as e:
                print(f"    ✗ 子版块访问失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_alternative_subreddits():
    """测试替代的子版块"""
    
    print("\n=== 测试替代子版块 ===")
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
    
    reddit = praw.Reddit(
        client_id=client_id,
        client_secret=client_secret,
        user_agent=user_agent
    )
    
    # 测试一些更开放的子版块
    alternative_subreddits = [
        'python',
        'technology', 
        'business',
        'finance',
        'economics',
        'news'
    ]
    
    working_subreddits = []
    
    for sub_name in alternative_subreddits:
        print(f"\n测试 r/{sub_name}:")
        try:
            subreddit = reddit.subreddit(sub_name)
            
            # 测试获取帖子
            submissions = list(subreddit.new(limit=1))
            if submissions:
                post = submissions[0]
                print(f"  ✓ 成功: {post.title[:40]}...")
                working_subreddits.append(sub_name)
            else:
                print(f"  ⚠ 没有帖子")
                
        except Exception as e:
            print(f"  ✗ 失败: {e}")
    
    print(f"\n可用的子版块: {working_subreddits}")
    return working_subreddits

if __name__ == '__main__':
    success = test_step_by_step()
    
    if success:
        working_subs = test_alternative_subreddits()
        
        if working_subs:
            print(f"\n=== 建议 ===")
            print(f"可以使用这些子版块进行数据收集: {working_subs}")
            print(f"修改reddit_live_collector.py中的target_subreddits列表")
        else:
            print(f"\n=== 问题分析 ===")
            print(f"所有子版块都无法访问，可能的原因:")
            print(f"1. Reddit API应用设置问题")
            print(f"2. 网络或IP限制")
            print(f"3. Reddit API政策变更")
